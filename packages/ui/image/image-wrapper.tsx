import { ClassName, createComponent, mergeClass, SlotFn } from '@skynet/shared'
import imageLoading from './source/image-loading.png'

type ImageWrapperOptions = {
  props: {
    class?: ClassName
  }
  slots: {
    default?: SlotFn
  }
}

export const ImageWrapper = createComponent<ImageWrapperOptions>({
  props: {
    class: '',
  },
  inheritAttrs: false,
}, (props, { slots }) => {
  return () => (
    <x-image-wrapper class={mergeClass('inline-block relative [&>img]:opacity-0 [&>img]:relative [&>img]:size-full [&>img]:z-up-up', props.class)}>
      <div class="absolute w-full h-full animate-pulse z-up">
        <div class={mergeClass('bg-gray-200', props.class)}>
          <img src={imageLoading} class="size-full rounded-[inherit]" />
        </div>
      </div>
      {
        slots?.default?.()
      }
    </x-image-wrapper>
  )
})

export default ImageWrapper
