/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Pager, transformInteger, Button, Checkbox, openDialog } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { apiGetSeriesList } from './spend_board_material-api'
import dayjs from 'dayjs'
import TaskForm from './task-form'

type InfoModifyPageOptions = {
  props: {}
}
export const SpendBoardSeriesList = createComponent<InfoModifyPageOptions>({
  props: {},
}, props => {
  const loading = ref(false)
  const list = ref<M.SpendBoardMaterial.SeriesItem[]>([])
  const defaultParams: M.SpendBoardMaterial.SeriesRequest = {
    series_resource_title: '', // 资源id或者titles
    source_type: 0, // 1首发剧， 2 爬坡合格二轮
  }
  const form = ref<M.SpendBoardMaterial.SeriesRequest>(defaultParams)
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const Table = CreateTableOld<M.SpendBoardMaterial.SeriesItem>()
  const QueryForm = CreateForm<M.SpendBoardMaterial.SeriesRequest>()
  const checkedItem = ref<M.SpendBoardMaterial.SeriesItem[]>([])

  const getList = async () => {
    const params = {
      ...form.value,
      page_info: pageInfo.value,
    }
    loading.value = true
    list.value = []
    try {
      const res = await apiGetSeriesList(params)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const columns: TableColumnOld<M.SpendBoardMaterial.SeriesItem>[] = [
    [
      '',
      row => {
        const id = row.series_resource_id
        return (
          <Checkbox
            label=""
            modelValue={checkedItem.value.map(i => i.series_resource_id).includes(id)}
            onUpdate:modelValue={(value: unknown) => {
              if (value) {
                checkedItem.value.push(row)
              } else {
                checkedItem.value = checkedItem.value.filter(i => i.series_resource_id !== id)
              }
            }}
          />
        )
      },
      { class: 'w-[30px]' },
    ],
    ['更新日期', row => {
      return dayjs(row.updated * 1000).format('YYYY-MM-DD')
    }, { class: 'w-[190px] !bg-transparent ' }],
    ['资源ID', 'series_resource_id', { class: 'w-[80px]' }],
    ['资源名称', 'series_resource_title', { class: 'w-[190px]' }],
    ['语种', 'language_version'],
    ['入库来源备注', row => {
      return (
        <div class="text-center">
          <span>{row.source_type === 1 ? '首发剧' : '爬坡合格二轮'}</span>
        </div>
      )
    }],
    ['上架时间', row => {
      if (!row.listing_time) return '-'
      return dayjs(row.listing_time * 1000).format('YYYY-MM-DD')
    }, { class: 'w-[190px]' }],
  ]

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = defaultParams
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }

  onQuery()

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>剧单库</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              ['资源名称/资源ID：', 'series_resource_title', { type: 'text', placeholder: '请输入资源名称' }],
              ['入库来源备注', 'source_type', { type: 'select', options: [{
                value: 1,
                label: '首发剧',
              }, {
                value: 2,
                label: '爬坡合格二轮',
              }], autoInsertEmptyOption: true }, { transform: transformInteger }],
            ]}
          />
        ),
        table: () => (
          <Table
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex justify-end items-center">
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (checkedItem.value.length === 0) {
                return
              }
              const model = ref()
              model.value = openDialog({
                body: (
                  <TaskForm
                    checkedSeries={checkedItem.value}
                    onCancel={() => model.value && model.value()}
                    onOk={() => {
                      model.value && model.value()
                    }}
                  />
                ),
                customClass: '!pb-0 !w-[1000px]',
              })
            }}
            >上传素材
            </Button>
          </x-table-actions>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default SpendBoardSeriesList
