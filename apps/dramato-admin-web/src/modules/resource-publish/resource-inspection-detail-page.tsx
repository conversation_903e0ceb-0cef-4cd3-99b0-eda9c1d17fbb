/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { onMounted, ref, onUnmounted, computed } from 'vue'
import { CreateForm, transformNumber, DateTime, Button, transformTimestamp, Icon, showFailToast, showSuccessToast, openDialog, Input, SvgIcon } from '@skynet/ui'
import { z } from 'zod'
import { set, get, cloneDeep } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { langKey, langValue } from 'src/modules/resource/constant'
import { Wrapper } from 'src/layouts/wrapper'
import { resourceTypeOptions, authPlatforms, authLanguages, authAreas, sharingModels, contentRates, releaseRounds, maritalStatusOptions, fertilityStatusOptions, ageOptions, vipTypes, freeStatusOptions, deployAdvanceOptions } from './constant'
import { useRouter, useRoute } from 'vue-router'
import { apiGetResourcePublishDetail, apiChangeAudit, apiSaveCheckInfo, apiGetCheckInfo, apiPublishPreview, apiTransTerms } from './resource-publish-api'
import { ResourceList } from './components/resource-list'
import { CheckInfoForm } from './components/check-info-form'
import { useResourceStore } from './use-resource-publish-store'
import { ShowStatus } from 'src/modules/resource-publish/components/show-status'
import { FileUploader } from './components/file-uploader'
import { EvaluateTable } from './components/evaluate-table'
import { defaultTemplate } from './constant'

type ResourceEntryDetailPageOptions = {
  props: {}
}

export const ResourceEntryDetailPage = createComponent<ResourceEntryDetailPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const route = useRoute()
  const detailLoading = ref(true)
  const { passFormData, validateInfo, getLabelList, subtitleLogs, getSubtitleRecordList } = useResourceStore()
  passFormData.value = {}
  const fileUploaderRef = ref<M.FileUploaderInstance>()
  const currentResource = ref<M.IResourcePublishDetail>({
    auth_platform: [],
    language_code: [],
    subtitle_language_code: [],
    upload_subtitle_language_code: [],
    list: [],
    audit_all_subtitle_process: false,
  })

  const currentStatus = computed(() => {
    return currentResource.value.audit_process?.find(item => item.page_type === 1) as M.IResourceAuditProcessItem
  })

  const isExtractTerms = computed(() => {
    return currentResource.value.term_extract_status === 1
  })

  // 1 资源验收 2 多语言信息 3 多语言字幕 4 终审发布 5 资源录入
  const isRejectStatus = computed(() => {
    const isReject = currentResource.value.audit_process?.some(item => item.status === 1) || false
    return isReject
  })

  const hasBanned = computed(() => {
    return [4, 5].includes(currentResource.value.recycle_status || 0)
  })

  // 非审核通过的有通过按钮
  const hasPassButton = computed(() => {
    const obj = currentResource.value.audit_process?.find(item => item.page_type === 1)
    return obj?.status !== 2 && !hasBanned.value
  })

  // 审核已通过
  const hasPassed = computed(() => {
    const obj = currentResource.value.audit_process?.find(item => item.page_type === 1)
    return obj?.status === 2 && !hasBanned.value
  })

  const saveLoading = ref(false)
  const Form = CreateForm<M.IResourcePublishDetail>()

  const cancel = () => {
    void router.replace('/resource-publish/inspection')
  }

  const onPass = () => {
    if (!validateInfo.validateAll()) {
      return
    }
    // 提取翻译才可以审核通过
    if (currentResource.value.term_extract_status !== 2) {
      showFailToast('术语提取中,暂不允许通过')
      return
    }
    const scoreData = ref<M.IRatingDetail[]>([])
    const rating = ref<'S' | 'A' | 'B' | 'C' | string>('')
    const list = passFormData.value.rating_detail?.detail || []
    const data = list && list.length > 0
      ? list.map((item, index) => ({
        id: String(index + 1),
        category: item.category || '',
        criteria: item.criteria || '',
        desc: item.desc || '',
        score: (item.score !== undefined && item.score !== null) ? item.score : undefined,
        weight: item?.weight || 0,
      }))
      : cloneDeep(defaultTemplate as unknown as M.IRatingDetail[])

    scoreData.value = data.map(item => ({
      id: item.id,
      category: item.category,
      criteria: item.criteria,
      score: item.score || 0,
      desc: item.desc,
      weight: item.weight,
    }))
    const tableData = ref<M.IRatingDetail[]>(data as M.IRatingDetail[])
    const hideDialog = openDialog({
      title: '审核通过',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[600px]',
      body: () => (
        <x-inspection-confirm-dialog class="flex flex-col space-y-4">
          <div>
            <div class="py-4">{requiredLabel('视频质量评级：')}</div>
            <EvaluateTable
              data={tableData.value}
              onScoreChange={(newData, total, rate) => {
                scoreData.value = newData.map(item => ({
                  id: item.id,
                  category: item.category,
                  criteria: item.criteria,
                  score: item.score || 0,
                  desc: item.desc,
                  weight: item.weight,
                }))
                rating.value = rate
              }} />
          </div>
          <x-inspection-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
              if (scoreData.value.some(item => item.score === undefined || item.score === 0)) {
                showFailToast('请完成评分')
                return
              }
              try {
                saveLoading.value = true
                await apiSaveCheckInfo({
                  ...passFormData.value,
                  id: currentResource.value.id as number,
                  content_rating: rating.value,
                  rating_detail: {
                    detail: scoreData.value,
                  },
                })
                await apiChangeAudit({
                  id: currentResource.value.id as number,
                  page_type: 1,
                  ope_type: 2,
                })

                showSuccessToast('操作成功')
                cancel()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                saveLoading.value = false
                hideDialog()
              }
            }}
            >
              {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确认通过
            </button>
          </x-inspection-footer>
        </x-inspection-confirm-dialog>
      ),
    })
  }

  const onPublishPreview = () => {
    if (!validateInfo.validateAll()) {
      return
    }
    const hideDialog = openDialog({
      title: '发布预告片',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[400px]',
      body: () => (
        <x-inspection-confirm-dialog class="flex flex-col space-y-4">
          <x-inspection-content>
            确认发布预告片
          </x-inspection-content>
          <x-inspection-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
              try {
                saveLoading.value = true
                await apiSaveCheckInfo({
                  ...passFormData.value,
                  id: currentResource.value.id as number,
                })

                await apiPublishPreview({
                  id: +currentResource.value.id!,
                })

                showSuccessToast('操作成功')
                cancel()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                saveLoading.value = false
                hideDialog()
              }
            }}
            >
              {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              提交
            </button>
          </x-inspection-footer>
        </x-inspection-confirm-dialog>
      ),
    })
  }

  const handleParse = (e: ClipboardEvent) => {
    const data = e.clipboardData
    if (!data) return
    const blob = data.items[0]?.getAsFile()
    if (!blob) return
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = event => {
      const base64Str = event.target?.result
      if (typeof base64Str !== 'string') return
      const bytes = window.atob(base64Str.split(',')[1])
      const array = []
      for (let i = 0; i < bytes.length; i++) {
        array.push(bytes.charCodeAt(i))
      }
      const newBlob = new Blob([new Uint8Array(array)], { type: 'image/png' })
      const file = new File([newBlob], `paste_${Date.now()}.png`, { type: 'image/png' })
      fileUploaderRef.value?.handleStart(file, newBlob)
    }
  }

  const onReject = () => {
    const RejectForm = CreateForm<M.IRejectForm>()
    const files = ref<M.UploadUserFileWithPath[]>([])
    const rejectForm = ref<M.IRejectForm>({
      audit_failed_reason: '',
      reject_image_list: [],
    })
    const formRules = z.object({
      audit_failed_reason: z.string().min(1, {
        message: '请输入审核不通过原因',
      }),
    })
    const { error, validateAll } = useValidator(rejectForm, formRules)

    const hideDialog = openDialog({
      title: '审核不通过',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-inspection-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-text-area>
            <RejectForm
              class="m-auto grid w-full grid-cols-3"
              hasAction={false}
              error={error.value}
              data={rejectForm.value}
              onChange={(path, value: any) => {
                set(rejectForm.value, path, value)
              }}
              items={[
                [
                  requiredLabel('备注：'),
                  'audit_failed_reason',
                  {
                    type: 'custom',
                    render: () => (
                      <textarea
                        class="textarea textarea-bordered textarea-sm w-full"
                        placeholder="请输入审核不通过原因"
                        onPaste={(e: ClipboardEvent) => {
                          const data = e.clipboardData
                          if (!data) return
                          // 检查是否包含图片
                          const hasImage = Array.from(data.items).some(item => item.type.startsWith('image/'))
                          if (hasImage) {
                            // 如果是图片则阻止默认粘贴并上传
                            e.preventDefault()
                            handleParse(e)
                          }
                          // 如果是文本则使用默认粘贴行为
                        }}
                        onInput={(e: Event) => {
                          rejectForm.value.audit_failed_reason = (e.target as HTMLTextAreaElement).value
                        }}
                      />
                    ),
                  },
                  {
                    class: 'col-span-3',
                  },
                ],
                [
                  '图片：',
                  'reject_image_list',
                  {
                    type: 'custom',
                    render: () => (
                      <FileUploader
                        ref={fileUploaderRef}
                        onSuccess={({ file }) => {
                          files.value.push(file)
                        }}
                        onRemove={({ file }) => {
                          const fileIndex = files.value.findIndex((item: M.UploadUserFileWithPath) => item?.uid === file?.uid)
                          files.value.splice(fileIndex, 1)
                        }} />
                    ),
                  },
                  {
                    class: 'col-span-3',
                  },
                ],
              ]}
            />
          </x-text-area>
          <x-inspection-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
              if (!validateAll()) return
              saveLoading.value = true
              try {
                await apiChangeAudit({
                  id: +currentResource.value.id!,
                  page_type: 1,
                  reject_to_page: 5,
                  ope_type: 1,
                  reject_reason: rejectForm.value.audit_failed_reason,
                  reject_image_list: files.value.map((item: M.UploadUserFileWithPath) => item.raw.path),
                })
                showSuccessToast('操作成功')
                cancel()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                saveLoading.value = false
                hideDialog()
              }
            }}
            >
              {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-inspection-footer>
        </x-inspection-confirm-dialog>
      ),
    })
  }

  const getDetail = async () => {
    passFormData.value = {}
    try {
      const id = +route.params.id
      const res = await apiGetResourcePublishDetail({
        id,
      })
      currentResource.value = res.data || {}
      const checkRes = await apiGetCheckInfo({
        id,
      })
      passFormData.value = checkRes.data || {}
      await getSubtitleRecordList(id)
    } catch (error: any) {
      showFailToast(error.response.data.err_msg || '获取详情失败')
    } finally {
      detailLoading.value = false
    }
  }

  const showText = (text: string) => {
    return <div class="break-words text-base text-[var(--text-1)]">{get(currentResource.value, text, '')}</div>
  }

  const showSelectedText = (text: string, options: { value: string | number, label: string }[]) => {
    const result = options.filter(o => o.value === get(currentResource.value, text, ''))
    return <div class="text-base text-[var(--text-1)]">{result[0]?.label || '- 空 -'}</div>
  }

  const showMulSelectedText = (text: string, options: { value: string | number, label: string }[]) => {
    const arr: any[] = get(currentResource.value, text, '') || []
    const result = options.filter(o => arr.includes(o.value as number))
    return <div class="text-base text-[var(--text-1)]">{result.map(o => o.label).join(',') || '- 空 -'}</div>
  }

  const renderLanguageShow = (languages: string[]) => {
    const showLanguage = languages.map((langCode: string) => {
      return langValue[langKey.findIndex(code => code === langCode)]
    }).join(',')
    return (
      <div>
        {showLanguage}
      </div>
    )
  }

  let interval = null
  interval = setInterval(async () => {
    const res = await apiGetResourcePublishDetail({
      id: +route.params.id,
    })
    currentResource.value = res.data || {}
  }, 30 * 1000)

  onMounted(() => {
    if (route.params.id) {
      void getLabelList()
      void getDetail()
    }
  })

  onUnmounted(() => {
    clearInterval(interval)
  })

  return () => (
    <>
      <Wrapper>
        <section class="breadcrumbs text-sm">
          <ul>
            <li class="cursor-pointer" onClick={() => cancel()}>资源验收</li>
            <li>资源信息</li>
          </ul>
        </section>
        <Form
          class="m-auto grid w-full grid-cols-3"
          hasAction={false}
          labelClass="text-[var(--text-2)]"
          data={currentResource.value}
          onChange={(path, value: any) => {
            set(currentResource.value, path, value)
          }}
          items={[
            [
              '',
              'audit_status',
              {
                type: 'custom',
                render: () => <ShowStatus data={currentStatus.value} recycleStatus={currentResource.value.recycle_status} />,
              },
              {
                class: `col-span-3 ${currentResource.value.check_status! > 0 ? 'block' : 'hidden'}`,
                hint: () => currentResource.value.check_reason ? <div class="ml-4 mt-4 !text-base !text-[var(--text-1)]">驳回原因：{currentResource.value.check_reason}</div> : '',
              },
            ],
            [
              requiredLabel('短剧名称：'),
              'title',
              {
                type: 'custom',
                render: () => {
                  return showText('title')
                },
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '短剧名称2：',
              'title2',
              {
                type: 'custom',
                render: () => {
                  return showText('title2')
                },
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '短剧名称3：',
              'title3',
              {
                type: 'custom',
                render: () => {
                  return showText('title3')
                },
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '商务评级：',
              'external_content_rating',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('external_content_rating', contentRates)
                },
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('商务负责人：'),
              'business_principal',
              {
                type: 'custom',
                render: () => {
                  return showText('business_principal')
                },
              },
            ],
            [
              requiredLabel('视频质量评级：'),
              'content_rating',
              {
                type: 'custom',
                render: () => {
                  const scoreData = ref<M.IRatingDetail[]>([])
                  const list = passFormData.value.rating_detail?.detail || []
                  const data = list && list.length > 0
                    ? list.map((item, index) => ({
                      id: String(index + 1),
                      category: item.category || '',
                      criteria: item.criteria || '',
                      desc: item.desc || '',
                      score: (item.score !== undefined && item.score !== null) ? item.score : undefined,
                      weight: item?.weight || 0,
                    }))
                    : cloneDeep(defaultTemplate as unknown as M.IRatingDetail[])

                  scoreData.value = data.map(item => ({
                    id: item.id,
                    category: item.category,
                    criteria: item.criteria,
                    score: item.score || 0,
                    desc: item.desc,
                    weight: item.weight,
                  }))
                  const tableData = ref<M.IRatingDetail[]>(data as M.IRatingDetail[])
                  return (
                    <div class="text-lg">
                      { passFormData.value.content_rating }
                      {passFormData.value.rating_detail?.detail ? (
                        <Button class="btn btn-link btn-sm" onClick={() => {
                          openDialog({
                            title: '评分详情',
                            body: () => (
                              <EvaluateTable
                                disabled={true}
                                data={tableData.value}
                                onScoreChange={(newData, total, rate) => {
                                  scoreData.value = newData.map(item => ({
                                    id: item.id,
                                    category: item.category,
                                    criteria: item.criteria,
                                    score: item.score || 0,
                                    desc: item.desc,
                                    weight: item.weight,
                                  }))
                                }} />
                            ),
                          })
                        }}>
                          评分详情
                        </Button>
                      ) : null}
                    </div>
                  )
                },
              },
              {
                class: `col-span-1 ${(passFormData.value.content_rating) ? 'block' : 'hidden'}`,
              },
            ],
            [
              requiredLabel('短剧简介：'),
              'description',
              {
                type: 'custom',
                render: () => {
                  return showText('description')
                },
              },
              {
                class: 'col-span-3',
              },
            ],
            [
              '合作方名称：',
              'partner_name',
              {
                type: 'custom',
                render: () => {
                  return showText('partner_name')
                },
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('网盘链接：'),
              'netdisk_url',
              {
                type: 'custom',
                render: () => showText('netdisk_url'),
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '',
              'x',
              {
                type: 'custom',
                render: () => <></>,
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('视频内嵌人声语言：'),
              'language_code',
              {
                type: 'custom',
                render: () => renderLanguageShow(currentResource.value?.language_code as string[]),
              },
            ],
            [
              '视频内嵌字幕语言：',
              'subtitle_language_code',
              {
                type: 'custom',
                render: () => renderLanguageShow(currentResource.value.subtitle_language_code as string[]),
              },
            ],
            [
              '上传字幕语言(多语言翻译输入)：',
              'upload_subtitle_language_code',
              {
                type: 'custom',
                render: () => renderLanguageShow(currentResource.value.upload_subtitle_language_code as string[]),
              },
            ],
            [
              requiredLabel('资源类型：'),
              'resource_type',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('resource_type', [{
                    value: 1,
                    label: '本土剧',
                  }, {
                    value: 2,
                    label: '翻译剧',
                  }])
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '短剧类型：',
              'resource_type_v2',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('resource_type_v2', resourceTypeOptions)
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('承制方：'),
              'contractor',
              {
                type: 'custom',
                render: () => showText('contractor'),
              },
              {
                class: `col-span-1`,
              },
            ],
            [
              requiredLabel('发行轮次：'),
              'release_round',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('release_round', releaseRounds)
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('VIP类型：'),
              'vip_type',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('vip_type', vipTypes)
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              'N天后开放其他解锁方式：',
              'unlocked_other_days',
              {
                type: 'custom',
                render: () => currentResource.value.unlocked_other_days
              },
              {
                class: `col-span-1 ${currentResource.value.vip_type !== 0 ? 'block' : 'hidden'}`,
              },
            ],
            [
              currentResource.value.vip_type === 2 ? requiredLabel('每天更新集数：') : '每天更新集数：',
              'vip_everyday_update_sn',
              {
                type: 'custom',
                render: () => <div>{currentResource.value.vip_everyday_update_sn && currentResource.value.vip_everyday_update_sn > 0 ? currentResource.value.vip_everyday_update_sn + '集' : '-'}</div>,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '授权平台：',
              'auth_platform',
              {
                type: 'custom',
                render: () => {
                  const auth_platform = currentResource.value.auth_platform as number[]
                  return (
                    <div class="break-words">
                      {auth_platform?.map(v => authPlatforms.find(p => p.value === v)?.label).join(',')}
                    </div>
                  )
                },
              },
            ],
            [
              '授权语言：',
              'auth_language',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('auth_language', authLanguages)
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '授权区域：',
              'auth_area',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('auth_area', authAreas)
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '分成模式：',
              'sharing_model',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('sharing_model', sharingModels)
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              <div class="flex items-center gap-2">
                {requiredLabel('授权价格')}
                <div class="font-bold">(美元)：</div>
              </div>,
              'auth_price',
              {
                type: 'custom',
                render: () => {
                  return showText('auth_price')
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '投放金额：',
              'put_money',
              {
                type: 'custom',
                render: () => {
                  return showText('put_money')
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '收入流水：',
              'revenue_flow',
              {
                type: 'custom',
                render: () => {
                  return showText('revenue_flow')
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '预估分成：',
              'estimate_sharing',
              {
                type: 'custom',
                render: () => {
                  return showText('estimate_sharing')
                },
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('总集数：'),
              'count',
              {
                type: 'custom',
                render: () => <div>{currentResource.value.count}集</div>,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('付费第1集：'),
              'unlocked_episodes',
              {
                type: 'custom',
                render: () => <div>{currentResource.value.unlocked_episodes}集</div>,
              },
              {
                transform: transformNumber,
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('是否可预告：'),
              'preview_status',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="text-lg">
                      { currentResource.value?.preview_status === 1 ? '不可预告' : currentResource.value?.preview_status === 2 ? '可预告' : '-' }
                    </div>
                  )
                },
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '海报可预告时间：',
              'poster_preview_time',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="text-lg">
                      <DateTime value={(currentResource.value?.poster_preview_time as number * 1000 || 0)} />
                    </div>
                  )
                },
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              '预告片可预告时间：',
              'preview_time',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="text-lg">
                      <DateTime value={(currentResource.value?.preview_time as number * 1000 || 0)} />
                    </div>
                  )
                },
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              requiredLabel('是否可免费'),
              'free_status',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('free_status', freeStatusOptions)
                },
              },
            ],
            [
              '最早可免费时间：',
              'free_start_time',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="text-lg">
                      <DateTime value={(currentResource.value?.free_start_time as number * 1000 || 0)} />
                    </div>
                  )
                },
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              () => currentResource.value.release_round === 1 ? requiredLabel('可投放时间：') : '可投放时间：',
              'deploy_time',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="text-lg">
                      <DateTime value={(currentResource.value?.deploy_time as number * 1000 || 0)} />
                    </div>
                  )
                },
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              '上线时间：',
              'online_time',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="text-lg">
                      <DateTime value={(currentResource.value?.online_time as number * 1000 || 0)} />
                    </div>
                  )
                },
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              requiredLabel('授权开始时间：'),
              'auth_start_time',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="text-lg">
                      <DateTime value={(currentResource.value?.auth_start_time as number * 1000 || 0)} />
                    </div>
                  )
                },
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],

            [
              requiredLabel('授权结束时间：'),
              'auth_end_time',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="text-lg">
                      <DateTime value={(currentResource.value?.auth_end_time as number * 1000 || 0)} />
                    </div>
                  )
                },
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],

            [
              '是否可提前投放：',
              'deploy_advance',
              {
                type: 'custom',
                render: () => {
                  return showSelectedText('deploy_advance', deployAdvanceOptions)
                },
              },
            ],

            [
              '演员信息',
              'actors',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="flex flex-col">
                      {
                        !currentResource.value?.actors || currentResource.value.actors?.length === 0
                          ? '- 暂无 -'
                          : currentResource.value.actors?.map(actor => {
                            return (
                              <div class="flex space-x-2">
                                <div title={actor.name} class="line-clamp-1 overflow-hidden max-w-[200px] space-x-1 break-all break-words">
                                  演员：{ actor.name }
                                </div>
                                {typeof actor.gender === 'number' ? <span>性别： { ['未知', '男', '女'][actor.gender || 0] }</span> : null }
                                <div title={actor.home_page} class="line-clamp-1 flex-1 break-words">链接： <a class={actor.home_page ? 'link' : ''} target="_blank" href={actor.home_page ? actor.home_page : 'javascript:void(0);'}>{actor.home_page}</a>
                                </div>
                              </div>
                            )
                          })
                      }
                    </div>
                  )
                },
              },
              {
                class: 'col-span-3',
              },
            ],
            [
              '婚姻状态：',
              'marital_status',
              {
                type: 'custom',
                render: () => {
                  return showMulSelectedText('marital_status', maritalStatusOptions)
                },
              },
            ],
            [
              '生育状态：',
              'fertility_status',
              {
                type: 'custom',
                render: () => {
                  return showMulSelectedText('fertility_status', fertilityStatusOptions)
                },
              },
            ],
            [
              '年龄：',
              'age',
              {
                type: 'custom',
                render: () => {
                  return showMulSelectedText('age', ageOptions)
                },
              },
            ],
          ]}
        />
        {
          !detailLoading.value
            ? (
                <ResourceList
                  list={currentResource.value?.list}
                  subtitleLogs={subtitleLogs.value}
                  canEdit={true}
                  language={currentResource.value.upload_subtitle_language_code as string[]}
                  isExtractTerms={isExtractTerms.value}
                  onRefreshList={async () => {
                    if (route.params.id) {
                      const res = await apiGetResourcePublishDetail({
                        id: +route.params.id,
                      })
                      currentResource.value = res.data || {}
                      void getSubtitleRecordList(+route.params.id)
                    }
                  }}
                />
              )
            : null
        }

        {/* 可审核状态 */}
        { hasPassButton.value ? <CheckInfoForm /> : <CheckInfoForm editable={false} /> }

        <div class="flex justify-start gap-x-2">
          <Button class="btn-default btn btn-sm" onClick={() => {
            cancel()
          }}
          >返回
          </Button>
          {
            hasPassButton.value
              ? (
                  <>
                    <Button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={onPass}>
                      {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                      审核通过
                    </Button>
                  </>
                )
              : null
          }
          {
            [3, 1].includes(currentResource.value.check_status || -1) && currentResource.value?.preview_status === 2 && !hasBanned.value
              ? (
                  <Button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={onPublishPreview}>
                    {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    {currentResource.value.publish_status_preview === 1 ? '重新发布预告片' : '预告片发布'}
                  </Button>
                )
              : null
          }
          <Button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={onReject}>
            {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
            审核驳回
          </Button>

          {hasPassed.value ? (
            <Button class="btn btn-primary btn-sm" disabled={currentResource.value.term_trans_status === 1} onClick={() => {
              const transLoading = ref(false)
              const hideDeleteDialog = openDialog({
                title: '提示',
                mainClass: 'pb-0 px-5',
                customClass: '!w-[400px]',
                body: () => (
                  <x-trans-terms-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <x-trans-terms-body>确认翻译术语</x-trans-terms-body>
                    <x-trans-terms-footer class="flex w-full justify-end gap-x-[10px]">
                      <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                      <Button class="btn btn-primary btn-sm" disabled={transLoading.value} onClick={async () => {
                        try {
                          transLoading.value = true
                          const res = await apiTransTerms({
                            series_resource_id: +route.params.id,
                          })
                          if (res.code === 200) {
                            showSuccessToast('操作成功')
                          }
                        } catch (error: any) {
                          showFailToast(error.response.data.err_msg || error.response.data.msg || '操作失败')
                        } finally {
                          transLoading.value = false
                        }
                      }}
                      >
                        {transLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                        确定
                      </Button>
                    </x-trans-terms-footer>
                  </x-trans-terms-confirm-dialog>
                ),
              })
            }}>{currentResource.value.term_trans_status === 1 ? '翻译中' : '术语翻译' }</Button>
          ) : null}
          {/* {
            !isRejectStatus.value && !hasBanned.value
              ? (
                  <Button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={onReject}>
                    {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    审核驳回
                  </Button>
                )
              : null
          } */}

        </div>
      </Wrapper>
    </>
  )
})

export default ResourceEntryDetailPage
