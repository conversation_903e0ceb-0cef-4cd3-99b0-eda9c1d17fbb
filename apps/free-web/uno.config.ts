import { defineConfig } from 'unocss'
import presetWind, { Theme } from '@unocss/preset-wind'
import { skynetUnoPreset } from '@skynet/preset/uno'
import { breakpoints } from './src/lib/constants'

export default defineConfig({
  presets: [
    presetWind({
      dark: {
        light: '',
        dark: '.dark',
      },
    }), // 兼容 tailwind
    skynetUnoPreset,
  ],

  content: {
    filesystem: [
      'src/**/*.{html,js,ts,jsx,tsx,vue}',
      '../../packages/ui/**/*.{html,js,ts,jsx,tsx,vue}',
    ],
  },
  extendTheme: (theme: Theme) => {
    return {
      ...theme,
      maxWidth: {
        ...theme.maxWidth,
        pad: 'var(--pad-page-max-width)',
        pc: 'var(--pc-page-max-width)',
      },
      height: {
        ...theme.height,
        'top-bar': 'var(--top-bar-height)',
      },
      backgroundColor: {
        ...theme.backgroundColor,
        'fill-1': 'var(--fill-1)',
        'fill-2': 'var(--fill-2)',
        'fill-3': 'var(--fill-3)',
        'fill-4': 'var(--fill-4)',
        'fill-5': 'var(--fill-5)',
        'fill-6': 'var(--fill-6)',
        'fill-7': 'var(--fill-7)',
        'fill-8': 'var(--fill-8)',

      },
    }
  },
  theme: {
    breakpoints: {
      // phone: breakpoints.phone.min + 'px', // phone 没有意义，因为所有样式默认都是优先适配手机
      pad: breakpoints.pad.min + 'px',
      pc: breakpoints.pc.min + 'px',
    },
    colors: {
      primary: '#0a66c2',
      // 其他颜色变量的自定义...
      // secondary - 次色调
      // accent - 强调色
      // success - 成功状态色
      // info - 信息状态色
      // warning - 警告状态色
      // error - 错误状态色
    },
    zIndex: {
      // 局部
      up: '1',
      'up-up': '2',
      // 全局
      footer: '64',
      dialog: '128',
      popover: '1024',
      shareButton: '2048',
      mask: '2560',
      toast: '5120',
      'global-loading': '9999',
    },
  },
  rules: [
  ],
})
