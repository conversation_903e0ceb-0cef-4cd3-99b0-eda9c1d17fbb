declare namespace M {
  interface SaveItem {
    series_resource_id?: number
    status?: number
    role_modify?: string
    face_role_modify?: string
  }

  interface relationItem {
    name?: string
    relation?: string
  }
  interface RoleListItem {
    id?: number
    name?: string
    nickname?: string
    introduction?: string
    episode_list?: string[]
    faces?: string[]
    relation_list?: relationItem[]
  }

  interface SplitActingItem {
    id?: number
    name?: string
    desc?: string
    relation?: string
  }

  interface SplitActingListItem {
    id?: number
    name?: string
    desc?: string
    relation?: string
  }

  interface GetSplitActingListItem {
    resource: number
  }

  interface MaterialSearchOptionNew {
    progress?: number
    role_mark?: number
    material_status?: number
    page_index?: number
    page_size?: number// 选填参数
  }

  interface MultiSplitItem {
    uid: string
    list_type: number
  }

  interface GetRoleListItem {
    resource: number
  }
}
