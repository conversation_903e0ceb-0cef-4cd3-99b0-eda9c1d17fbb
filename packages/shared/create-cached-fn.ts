export type Key = string | number

/**
 * 创建一个缓存函数，缓存函数的返回值，
 * 注意不要在 fn 中使用 computed 和 watch，用了也会失效
 * 当 key 是第一次遇到的字符串时，缓存 fn(key) 的返回值
 * 当 key 是之前遇到过的字符串时，直接返回缓存的值，
 * @param fn
 * @returns
 */
function createCachedFn<T extends object>(fn: (key: string) => T): (key?: string) => T
function createCachedFn<T extends object>(fn: (key: number) => T): (key?: number) => T
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function createCachedFn<T extends object>(fn: (key: any) => T) {
  const cache = new Map<Key, T>()
  function getFromCache(f2: typeof fn, key: Key) {
    if (cache.has(key)) {
      return cache.get(key)!
    } else {
      const r = f2(key)
      cache.set(key, r)
      return r
    }
  }
  return function (key: Key = '') {
    return getFromCache(fn, key)
  }
}

export { createCachedFn }
