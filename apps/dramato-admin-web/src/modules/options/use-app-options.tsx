import { ref, onMounted, watch } from 'vue'
import { bindLoading } from '@skynet/shared'
import { keepError } from 'src/lib/http-client'
import { lang } from 'src/lib/constant'
import { apiGetAppOptions } from '../application/application.api'

const appOptions = ref<Array<{ label: string, value: number, platform: number, language: string[] }>>([])
const loadingAppOptions = ref<boolean>(false)
type Config = {
  onSuccess?: () => void
  onError?: () => void
  transform?: Record<string, string>
  isFree?: boolean
}
export const useAppOptions = (config?: Config) => {
  const fetchAppOptions = async () => {
    const response = await bindLoading(apiGetAppOptions({ app_name: '' }), loadingAppOptions).catch(keepError(config?.onError))
    if (!response.data) return
    appOptions.value = response.data.list?.filter(item => config?.isFree ? item.app_name.includes('Free') : item).map(item => ({
      label: item.app_name,
      value: item.id,
      language: item.supported_language,
      platform: item.platform,
    })) ?? []
    setTimeout(() => {
      config?.onSuccess?.()
    })
  }
  onMounted(() => {
    if (loadingAppOptions.value) return
    void fetchAppOptions()
  })
  return {
    appOptions,
    loadingAppOptions,
  }
}

export const useLanguageOptions = (appId: () => string | number | undefined, config?: Config) => {
  const languageOptions = ref<Array<{ label: string, value: string }>>([])
  const { appOptions } = useAppOptions(config)
  watch(appId, id => {
    const options = appOptions.value.find(item => item.value === id)?.language || [
      'English',
      'Filipino',
      'French',
      'German',
      'Hindi',
      'Indonesian',
      'Italian',
      'Japanese',
      'Korean',
      'Malaysia',
      'Portuguese',
      'Russian',
      'Spanish',
      'Thai',
      'Turkish',
      'Vietnamese',
      '简体中文',
      '繁体中文',
    ]

    languageOptions.value = options.map((item: string) => ({
      label: lang.find(i => i.value === item)?.label || '- 空 -',
      value: item,
    }))
  }, {
    immediate: true,
  })
  return {
    languageOptions,
  }
}

export const useAppAndLangOptions = (appId: () => string | number | undefined, config?: Config) => {
  const { appOptions, loadingAppOptions } = useAppOptions(config)
  const { languageOptions } = useLanguageOptions(appId, config)

  return {
    appOptions,
    loadingAppOptions,
    languageOptions,
  }
}
