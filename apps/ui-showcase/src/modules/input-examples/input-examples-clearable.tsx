import { createComponent } from '@skynet/shared'
import { Input } from '@skynet/ui'
import { ref } from 'vue'

export const InputExamplesClearable = createComponent(null, () => {
  const name = ref<string>('')
  return () => (
    <div class="space-y-2 p-2 markdown-body">
      <Input
        clearable
        placeholder="请输入姓名"
        class="w-80 h-10 inline-block"
        v-model={name.value}
      />
    </div>
  )
})

export default InputExamplesClearable
