import { createComponent } from '@skynet/shared'
import { computed, type VNodeChild } from 'vue'
import { useRoute } from 'vue-router'
import { SvgIcon } from '@skynet/ui'
import { Back } from '~/modules/common/back/back'
import { useSeriesItem } from '~/modules/series/use-series-item'
import { useImmersive } from '~/modules/series/use-immersive'

type TransitionLayoutOptions = {
  slots: {
    default: () => VNodeChild
  }
}
export const TransitionLayout = createComponent<TransitionLayoutOptions>(null, (props, { slots }) => {
  // const route = useRoute()
  // const return_to = computed(() => route.query.return_to?.toString?.() ?? '/')
  const return_to = computed(() => {
    const params = new URLSearchParams(location.search)
    return params.get('return_to')?.toString() ?? '/'
  })
  // const seriesId = computed(() => route.params.series_id?.toString())
  const seriesId = computed(() => {
    const pathSegments = location.pathname.split('/');
    if (pathSegments[1] === 'series') {
      return pathSegments[2];
    }
    return '';
  });
  const { immersive } = useImmersive(seriesId.value)
  const { seriesItem, getCurrentEpisodeNumber, faqPanelVisible } = useSeriesItem(seriesId.value)
  function moreClick() {
    faqPanelVisible.value = true
  }
  return () => (
    <x-transition-layout class="relative">
      <Back
        class={`fixed top-0 z-up w-full shrink-0 items-center justify-start bg-transparent font-normal text-[#fdfbfc] ${immersive.value && 'hidden'}`}
        isWhite={true} backTo={return_to.value} titleLeft={true}
        title={() => (
          <>
            <div class="flex w-full items-center">
              <span class="grow-0 shrink truncate">{seriesItem.value?.name}</span>
              {getCurrentEpisodeNumber(n => <span class="ml-2 mr-auto shrink-0 grow-0"> Ep. {n} </span>)}
              <SvgIcon
                name="ic-ellipsis-vertical"
                class="size-6 shrink-0 ml-auto"
                onClick={moreClick}
              />
            </div>
          </>
        )}
      />
      <x-transition-content class="scrollbar-hide max-h-safe-screen relative block overflow-hidden ">
        {/* <RouterView>{({ Component, route }: any) => {
          return (
            <div key={seriesId.value}>
              {Component}
            </div>
          )
        }}</RouterView> */}
        {slots.default ? slots.default() : null}
      </x-transition-content>
    </x-transition-layout>
  )
})

export default TransitionLayout
