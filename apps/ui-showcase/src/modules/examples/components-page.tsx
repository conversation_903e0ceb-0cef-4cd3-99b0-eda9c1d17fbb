import { createComponent } from '@skynet/shared'
import { ButtonExample } from 'src/modules/examples/button-example'
import { RouterLink } from 'vue-router'
type ComponentsPageOptions = {
  props: {}
}
export const ComponentsPage = createComponent<ComponentsPageOptions>({
  props: {},
}, () => {
  return () => (
    <div class="bg-white mt-4 rounded-lg py-8 px-6">
      <article>
        <h1 class="text-3xl font-bold">组件列表</h1>
        <dl class="grid grid-cols-auto gap-4 my-4 [&_dt]:text-lg [&_dd]:text-sm [&_dd]:text-gray-500 [&_dt]:border-l-4
          [&_dt]:border-primary-500 [&_dt]:pl-2 [&_x-group]:space-y-2 space-y-2 [&_dd_a]:border-b [&_dd_a]:border-black
          [&_dd_a]:after:content-['🔗']"
        >
          <x-group>
            <dt>
              <RouterLink to="/components/form">Form 组件</RouterLink>
            </dt>
            <dd>
              整合了表单的大部分功能，可搭配校验器一起使用。
              <RouterLink to="/components/form">开始使用</RouterLink>
            </dd>
          </x-group>
          <x-group>
            <dt>
              <RouterLink to="/components/table">Table 组件</RouterLink>
            </dt>
            <dd>
              整合了表格的大部分功能
            </dd>
          </x-group>
        </dl>
      </article>
    </div>
  )
})
