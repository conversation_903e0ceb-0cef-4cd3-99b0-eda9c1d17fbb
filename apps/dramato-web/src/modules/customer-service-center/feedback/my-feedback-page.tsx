/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, getQueries, mc } from '@skynet/shared'
import { useMyFeedbackStore } from './my-feedback-store'
import InfinityScroll from 'src/h5_modules/common/infinity-scroll/infinity-scroll'
import { storeToRefs } from 'pinia'
import { onActivated, onMounted, onUnmounted, ref } from 'vue'
import { useScrollPositionStore } from 'src/h5_modules/home/<USER>'
import dayjs from 'dayjs'
import { arrow } from '../images/images'
import { jsBridge } from 'src/lib/jsbridge'
import { Back } from 'src/h5_modules/common/back/back'
import { useLocale } from 'src/lib/use-locale'
import { Button, Empty } from '@skynet/ui'
import EmptyPage from 'src/h5_modules/common/empty-page/empty-page.vue'
import { MyFeedbackApi } from './feedback-api'
import { useRouter } from 'vue-router'
import { track } from 'src/lib/track'
import { isIos } from 'src/lib/ua'

type MyFeedbackPageOptions = {
  props: {}
}
export const MyFeedbackPage = createComponent<MyFeedbackPageOptions>({
  name: 'MyFeedbackPage',
  props: {},
}, props => {
  const router = useRouter()
  const { showBack } = getQueries({ showBack: 1 })
  const language = router.currentRoute.value.query.language as string
  const { t } = useLocale(language)

  const homePageRef = ref()
  const scrollPositionStore = useScrollPositionStore()

  const {
    getFeedback,
  } = useMyFeedbackStore()

  const {
    feedback,
    loadingFeedback,
    hasMoreFeedback,
    feedbackNext,
  } = storeToRefs(useMyFeedbackStore())

  const onWorkOrderClick = (d: M.MyFeedback.List.Feedback) => {
    // 埋点上报
    track('feedback', 'myfeedback_content', 'click', {
      work_order: d.work_order_id,
    })
    void router.push({ path: 'feedback-detail', query: { ...router.currentRoute.value.query, from: 'csc', id: d.work_order_id } })
  }

  onMounted(() => {
    track('feedback', 'myfeedback', 'show')
    console.log('homePageRef', 'offset=0&page_size=20')

    document.title = t('customer-service-center.myFeedback')
    hasMoreFeedback.value = true
    feedback.value = []
    void MyFeedbackApi.clickRedDot({ type: 1 })
    void getFeedback('offset=0&page_size=20')
  })

  onActivated(() => {
    console.log('onActivated')

    // 恢复滚动位置，nexttick 和 requestAnimationFrame 都不能解决, 只能用 setTimeout 了
    setTimeout(() => {
      if (homePageRef.value) {
        document.documentElement.scrollTop = scrollPositionStore.scrollTop
      }
    }, 20)
  })

  onUnmounted(() => {
    console.log('onUnmounted')
    feedbackNext.value = 'offset=0&page_size=20'
    document.documentElement.scrollTop = scrollPositionStore.scrollTop
  })

  return () => (
    <x-my-feedback-page class="overflow-hide flex h-full flex-col bg-[var(--black)]">
      {showBack === 1 && <Back title={t('customer-service-center.myFeedback')} isWhite={true} class="shrink-0 bg-transparent" /> }
      <x-my-feedback-box
        ref={homePageRef}
        onScroll={(e: any) => {
          scrollPositionStore.setScrollTop(e.target.scrollTop)
        }}
        class={mc(' w-full flex-1 overflow-auto', jsBridge.inDramawaveApp ? 'mb-23' : 'mb-16')}
      >
        {
          loadingFeedback.value || (feedback.value && feedback.value.length > 0) ? (
            <InfinityScroll
              hasMore={hasMoreFeedback.value}
              next={feedbackNext.value}
              loading={loadingFeedback.value}
              onLoad={getFeedback}
              loadingText={t('infinityScroll.loading')}
              endText={t('infinityScroll.end')}
            >
              <x-my-feedback-list class="flex w-full flex-col gap-y-2 px-3 pt-4">
                {
                  feedback.value && feedback.value.length > 0 && feedback.value.map(i => (
                    <x-feedback-item key={i.work_order_id} class="flex items-start justify-between overflow-hidden py-3" onClick={() => onWorkOrderClick(i)}>
                      <x-feedback-item-content class="flex flex-1 flex-col gap-y-2 overflow-hidden pr-2">
                        <x-feedback-item-title class="text-3.5 leading-4.25 max-h-8.5 line-clamp-2 w-full break-all text-[#FDFBFC]">{i.description}</x-feedback-item-title>
                        <x-feedback-item-desc class="text-3 flex flex-row items-center gap-x-2 text-[#797B7D]">
                          <x-feedback-item-create>{dayjs(i.op_time * 1000).format('YYYY-MM-DD HH:mm:ss')}</x-feedback-item-create>
                          {i.feedback_status === 2 && <x-feedback-item-status class="h-4.5 rounded-1 text-3 leading-4.5 bg-[#2E2F30] px-1 text-[#797B7D]">{i.feedback_status_text}</x-feedback-item-status>}
                        </x-feedback-item-desc>
                      </x-feedback-item-content>
                      <x-feedback-item-aside class="flex h-5 flex-row items-center gap-x-1">
                        {i.has_red_dot && <x-feedback-item-reply class="h-3.75 leading-3.75 text-2.5 rounded-50 bg-[#FC2763] px-1 text-white">{i.un_read_msg_num}</x-feedback-item-reply>}
                        <img src={arrow} class={mc('size-5 rotate-270')} />
                      </x-feedback-item-aside>
                    </x-feedback-item>
                  ),
                  )
                }
              </x-my-feedback-list>
            </InfinityScroll>
          ) : (
            <x-empty class="mt-48%  relative flex w-full flex-col overflow-hidden">
              <EmptyPage image-size="160" description={t('myList.noData')} />
            </x-empty>
          )
        }
      </x-my-feedback-box>

      <div class={`pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] z-shareButton fixed bottom-0 left-1/2 w-full -translate-x-1/2 bg-[var(--black)] ${jsBridge.inDramawaveApp ? 'h-23' : 'share-bottom-2 flex h-16 items-center pb-2 pt-3'}`}>
        <Button
          class="no-tap-color dramato-button pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] z-shareButton  fixed left-1/2 box-border h-11 w-[calc(100%-1.5rem)] -translate-x-1/2 cursor-pointer text-nowrap rounded-lg border-none bg-[var(--brand-6)] text-base font-medium leading-tight text-[var(--text-5)] outline-none"
          onClick={() => {
            // track('csc', 'feedback', 'click')
            void router.push({ path: 'feedback', query: { ...router.currentRoute.value.query, from: 'csc' } })
          }}
        >
          {t('customer-service-center.i-want-to-give-feedback')}
        </Button>
      </div>
    </x-my-feedback-page>
  )
})

export default MyFeedbackPage
