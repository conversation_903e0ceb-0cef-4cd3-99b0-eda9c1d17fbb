/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, required } from '@skynet/shared'
import { DialogFooter, transformTimestamp } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { isOurApp, isThirdPartyApp } from 'src/lib/app-helper'
import { dramaStatusOptionsForPush } from 'src/modules/options/options'
import { useAppOptions } from 'src/modules/options/use-app-options'
import { ref, watch } from 'vue'
import { Countries } from './config'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
type Data = M.AvailableMaterial
type PushFormOptions = {
  props: {
    data: Data
    languageList: M.EpisodeLanguage[]
  }
  emits: {
    ok: (data: Data) => void
    cancel: Fn
  }
}

const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()

export const PushForm = createComponent<PushFormOptions>({
  props: {
    data: required,
    languageList: [],
  },
  emits: {
    ok: fn,
    cancel: fn,
  },
}, (props, { emit }) => {
  const { appOptions } = useAppOptions()
  const thirdKey = ref(props.data.third_key)
  const country = ref<string[]>([])
  const dramaStatus = ref(props.data.drama_status)
  const listingTime = ref(props.data.listing_time)

  watch(() => [props.languageList, props.data.country], () => {
    const config = props.languageList.find(item => item.code === props.data.language)
    const l = config?.name || ''
    if (['英语', '日语', '泰语', '韩语', '西语', '葡语', '法语', '印尼语', '中文繁体', '俄语', '德语', '阿语'].includes(l)) {
      let c = ''
      if (l === '英语') {
        c = 'US'
      }
      if (l === '日语') {
        c = 'JP'
      }
      if (l === '泰语') {
        c = 'TH'
      }
      if (l === '韩语') {
        c = 'KR'
      }
      if (l === '西语') {
        c = 'ES'
      }
      if (l === '葡语') {
        c = 'PT'
      }
      if (l === '法语') {
        c = 'FR'
      }
      if (l === '印尼语') {
        c = 'ID'
      }
      if (l === '中文繁体') {
        c = 'TW'
      }
      if (l === '俄语') {
        c = 'RU'
      }
      if (l === '德语') {
        c = 'DE'
      }
      if (l === '阿语') {
        c = 'SA'
      }

      country.value = [c]
    } else {
      country.value = props.data.country ? [props.data.country] as string[] : config?.area ? [Countries.find(i => i.label === config?.area)?.value || ''] : []
    }
  }, { deep: true, immediate: true })

  return () => (
    <>
      <section class="grid grid-cols-[max-content_auto] gap-2 pb-10">
        <div>剧名</div><div> {props.data.title}</div>
        <div>剧集ID</div><div> {props.data.series_key}</div>
        <div>应用名</div><div> {appOptions.value.find(item => item.value === props.data.app_id)?.label}</div>
        <div>上架状态</div>
        <div>
          {
            isOurApp(props.data.app_name)
              ? <span class="badge badge-outline whitespace-nowrap">{dramaStatusOptionsForPush.find(item => item.value === props.data.drama_status)?.label} </span>
              : (
                  <select class="select select-bordered select-xs" v-model={dramaStatus.value}>
                    {dramaStatusOptionsForPush.map(item => (
                      <option value={item.value}>{item.label}</option>
                    ))}
                  </select>
                )
          }
        </div>
        <div>上架时间</div>
        <div>
          <input readonly={isOurApp(props.data.app_name)} disabled={isOurApp(props.data.app_name)} type="datetime-local" class="input input-bordered input-xs" value={transformTimestamp[0](listingTime.value ?? 0)}
            onChange={e => listingTime.value = transformTimestamp[1]((e.target as HTMLInputElement).value)}
          />
        </div>
        <div>语言</div><div> {props.languageList.find(item => item.code === props.data.language)?.name}</div>
        <div>国家</div>
        <div>
          <FormMultiSelect
            search
            class="w-full"
            popoverWrapperClass="z-popover-in-dialog"
            modelValue={country.value}
            onUpdate:modelValue={e => country.value = e as string[]}
            maxlength={1}
            options={Countries}
          />
        </div>
        {
          isThirdPartyApp(props.data.app_name)
          && (
            <>
              <div>三方包剧集ID</div><div><input type="text" class="input input-xs input-bordered max-w-full" v-model={thirdKey.value} /></div>
            </>
          )
        }
      </section>
      <DialogFooter cancelText="取消" okText="确定" onCancel={() => emit('cancel')}
        onOk={() => {
          emit('ok', {
            ...props.data,
            third_key: thirdKey.value, country: country.value[0], drama_status: dramaStatus.value, listing_time: listingTime.value,
          })
        }}
      />
    </>
  )
})
