import { computed, nextTick, onMounted, ref, VNode, watch } from 'vue'
import { Empty, MergeClass, usePopover } from '../ui'
import { InputOption, SimpleValue } from './form-types'
import { ClassName, createComponent, createStringId, fn, mc, SlotFn, tryCall } from '@skynet/shared'
import { onClickOutside, useDebounceFn } from '@vueuse/core'
import { withStopPropagation } from '../utils'
import { useFormMultiSelectStore } from './use-form-multi-select-store'
import { Tooltip } from '../tooltip/tooltip'

export type FormMultiSelectOptions<D = unknown> = {
  props: {
    modelValue?: D[]
    options?: Array<InputOption<D>> | (() => VNode | string | null)
    clickBehavior?: 'select' | 'toggle'
    maxlength?: number | null
    search?: boolean | {
      placeholder?: string
      debounce?: number
    } | null
    popoverWrapperClass?: ClassName
    itemClass?: ClassName
    disabledItemClass?: ClassName
    onClear?: null | (() => void)
    disabled?: boolean
  }
  emits: {
    'update:modelValue': (value: D[]) => void
    'update:keyword': (keyword: string) => void
    maxlengthExceeded: () => void
  }
  slots: {
    loadingOptions?: SlotFn
    emptyOptions?: SlotFn
  }
}
export const CreateFormMultiSelect = <D extends SimpleValue>() => createComponent<FormMultiSelectOptions<D>>({
  name: 'FormMultiSelect',
  props: {
    modelValue: [],
    options: [],
    clickBehavior: 'toggle',
    maxlength: null,
    search: null,
    popoverWrapperClass: '',
    disabledItemClass: '',
    onClear: null,
    disabled: false,
    itemClass: '',
  },
  emits: {
    'update:modelValue': fn,
    'update:keyword': fn,
    maxlengthExceeded: fn,
  },
}, (props, { emit, attrs }) => {
  const keyword = ref('')
  const uniqueId = createStringId()
  const labelWrapper = ref<HTMLElement | null>(null)
  const availableOptions = computed(() => {
    if (typeof props.options === 'function') {
      return props.options()
    }
    return props.options.filter(option =>
      keyword.value ? option.label.toString().toLowerCase().includes(keyword.value.toLowerCase()) : true)
  })
  const hoveredIndex = ref(-1)
  const debounce = typeof props.search === 'object' ? props.search?.debounce : 300
  const onUpdateKeyword = useDebounceFn(value => {
    keyword.value = value
    emit('update:keyword', value)
  }, debounce)
  const addModelValue = (value: D) => {
    if (props.maxlength === 1) {
      updateModelValue([value])
      return
    }
    updateModelValue([...props.modelValue, value])
  }
  const clear = () => {
    if (props.onClear) {
      props.onClear()
    } else {
      updateModelValue([])
    }
  }
  const updateModelValue = (value: D[]) => {
    if (props.maxlength && value.length > props.maxlength) {
      emit('maxlengthExceeded')
      return
    }
    emit('update:modelValue', value)
  }
  const onKeyDown = (e: KeyboardEvent) => {
    if (!availableOptions.value || !Array.isArray(availableOptions.value)) { return }
    if (e.key === 'ArrowDown') {
      hoveredIndex.value = Math.min(hoveredIndex.value + 1, availableOptions.value.length - 1)
      e.preventDefault()
    } else if (e.key === 'ArrowUp') {
      hoveredIndex.value = Math.max(hoveredIndex.value - 1, 0)
      e.preventDefault()
    } else if (e.key === 'Enter') {
      const value = availableOptions.value[hoveredIndex.value]?.value
      if (value) {
        addModelValue(value)
      }
    }
  }
  const onClickOption = (option: InputOption<D>) => {
    // 删除已选项
    if (props.clickBehavior === 'toggle' && props.modelValue.includes(option.value)) {
      updateModelValue(props.modelValue.filter(value => value !== option.value))
      return
    }
    // 添加选项
    addModelValue(option.value)
  }
  const width = ref(0)
  onMounted(() => {
    width.value = labelWrapper.value?.getBoundingClientRect().width ?? 0
  })
  const { visible: open } = usePopover({
    triggerElement: labelWrapper,
    triggerType: 'click',
    useFlip: false,
    placement: 'bottom-start',
    offset: 0,
    arrowVisible: false,
    class: 'rounded-md',
    wrapperClass: ['drop-shadow-center', props.popoverWrapperClass].join(' '),
    beforeOpen: () => !props.disabled,
    content: () => (
      <x-popover class={['border border-t-0 block']} style={{ width: width.value + 'px' }}>
        <div class="bg-white">
          {props.search && (
            <>
              <input autocomplete="off" id={uniqueId} class="w-full py-2 px-2 outline-none" value={keyword.value}
                onInput={e => onUpdateKeyword((e.target as HTMLInputElement).value)}
                onKeydown={onKeyDown}
                placeholder={(typeof props.search === 'object' && props.search.placeholder) || '请输入关键字'}
              />
              <hr />
            </>
          )}
          <ul class="max-h-[22em] overflow-auto">
            {Array.isArray(availableOptions.value)
              ? availableOptions.value.length === 0
                ? <div class="p-4 text-center">暂无数据</div>
                : availableOptions.value.map((option, index) => {
                  const selected = props.modelValue.includes(option.value)
                  return (
                    <Tooltip key={index} popContent={() => tryCall(option.tooltip)}>
                      <li
                        class={mc(
                          'py-2 px-2 flex items-center gap-x-2',
                          hoveredIndex.value === index ? 'bg-primary/10' : '',
                          selected ? 'bg-primary/25' : '',
                          props.itemClass,
                          option.disabled ? props.disabledItemClass : '',
                        )}
                        onMouseenter={() => { hoveredIndex.value = index }}
                        onClick={option.disabled ? undefined : () => onClickOption(option)}
                      >
                        <span class="size-4 inline-flex items-center justify-center shrink-0 grow-0 text-primary">
                          {selected ? icons.value.checked : icons.value.unchecked}
                        </span>
                        <span class="flex-1 truncate">{option.label}</span>
                      </li>
                    </Tooltip>

                  )
                })
              : availableOptions.value}
          </ul>
        </div>
      </x-popover>
    ),
  })
  const labelInner = ref<HTMLElement | null>(null)
  // 新增选项时滚动到底部
  watch(() => props.modelValue.length, (newLength, oldLength) => {
    if (newLength > oldLength) {
      void nextTick(() => {
        labelInner.value?.scrollTo({ top: labelInner.value.scrollHeight })
      })
    }
  })
  const onRemoveItem = (value: D) => {
    const found = Array.isArray(props.options) && props.options.find(item => item.value === value)
    if (found && found.disabled) return
    updateModelValue(props.modelValue.filter(item => item !== value))
  }
  const { icons } = useFormMultiSelectStore()
  return () => {
    return (
      <MergeClass tag="x-multi-select" baseClass="block h-8 min-w-[200px] relative text-sm">
        <label ref={labelWrapper}
          class={['flex flex-nowrap justify-end items-center input input-bordered !px-0 rounded-t-md rounded-b-md bg-white relative z-up ',
            open.value ? 'max-h-[80px] h-auto min-h-full' : 'h-full overflow-hidden',
            props.disabled ? 'input-disabled' : '',
          ]}
          for={uniqueId}
        >
          <x-tag-list ref={labelInner} class={['flex gap-x-1 min-h-full self-start gap-y-1 leading-none px-1 overflow-auto w-full relative shadow-[inset_-4px_0_4px_-4px_rgba(0,0,0,0.25)]',
            open.value ? 'flex-wrap items-start py-[3px]' : 'flex-wrap hide-scrollbar items-center',
          ]}
          >
            {props.modelValue.length === 0
              ? <x-tag class="invisible border border-line-1 px-1 py-[4px] rounded-md truncate shrink-0 grow-0 inline-flex items-center gap-x-1 tooltip tooltip-right"><Empty /></x-tag>
              : props.modelValue.map((value, index) => (
                findLabelFromOptions(value, props.options, ({ label, disabled, tooltip }) => (
                  <Tooltip key={index} popContent={() => tooltip}>
                    <x-tag class="border border-line-1 px-1 py-[4px] rounded-md truncate shrink-0 grow-0 inline-flex items-center gap-x-1 tooltip tooltip-right">
                      <x-name>{label}</x-name>
                      {!disabled && (
                        <x-remove class="size-[14px] relative" onClick={(e: Event) => { e.stopPropagation(); onRemoveItem(value) }}>
                          <span class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"> {icons.value.removeItem} </span>
                        </x-remove>
                      )}
                    </x-tag>
                  </Tooltip>
                ))
              ))}
          </x-tag-list>
          <x-icons class="relative flex flex-nowrap h-full shrink-0 grow-0">
            {open.value || props.modelValue.length === 0
              ? null
              : (
                  <span class="px-2 shrink-0 flex cursor-pointer justify-center items-center border-r">
                    已选{props.modelValue.length}项
                  </span>
                )}
            <x-clear class="w-[30px] h-full shrink-0 flex cursor-pointer justify-center items-center"
              title="清空"
              onClick={withStopPropagation(() => clear())}
            >
              {icons.value.clear}
            </x-clear>
            <x-down class="w-[30px] h-full shrink-0 flex cursor-pointer justify-center items-center">
              {open.value ? icons.value.up : icons.value.down}
            </x-down>
          </x-icons>
        </label>

      </MergeClass>
    )
  }
})

const findLabelFromOptions = (value: string | number | boolean, options: InputOption[] | (() => VNode | string | null),
  callback: (p: { label: unknown, disabled?: boolean, tooltip?: string }) => unknown) => {
  if (typeof options === 'function') {
    return null
  }
  const option = options.find(option => option.value === value)
  return option ? callback(option) : callback({ label: value, disabled: false })
}
