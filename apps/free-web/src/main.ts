import { createApp } from 'vue'
import './assets/style/global.css'
import router from './router.tsx'
import { App } from './app.tsx'
import i18n from './i18n/init-i18n.ts'
import 'src/init/init.ts'
import { inApp } from './lib/jsbridge.ts'
import { report, reportNow, reportNowViaBeacon } from '@skynet/client-track'
import { deviceInfo } from './lib/get-device-info.ts'
/* @ts-expect-error never mind */
import('virtual:svg-icons-register')
/* @ts-expect-error never mind */
import('virtual:svg-no-colored-icons-register')

if (inApp) {
  report({
    event: `H5_page_load`,
    // @ts-expect-error never mind
    device_manufacturer: deviceInfo?.device_manufacturer ?? deviceInfo?.manufacturer ?? 'unknown',
    event_info: JSON.stringify({
      origin: 'H5',
      entity: 'page',
      event: 'load',
      time: Date.now(),
      url: window.location.href,
    }),
  })
}

window.addEventListener('beforeunload', reportNowViaBeacon)

const app = createApp(App)
app.use(router)
app.use(i18n)

app.mount('#app')
