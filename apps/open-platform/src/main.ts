/* eslint-disable vue/multi-word-component-names */
import { createApp } from 'vue'
import './assets/style/global.css'
import 'github-markdown-css'
import router from './router.tsx'
import { App } from './app'
import 'src/init/init.ts'
import { Icon } from '@iconify/vue'
import { createPinia } from 'pinia'
/* @ts-expect-error never mind */
import('virtual:svg-icons-register')
/* @ts-expect-error never mind */
import('virtual:svg-no-colored-icons-register')

import Multiselect from 'vue-multiselect' // https://vue-multiselect.js.org/#sub-custom-configuration
import 'vue-multiselect/dist/vue-multiselect.css'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'
import 'element-plus/dist/index.css'
import { i18n, getLocale } from './locales'

const app = createApp(App)
const pinia = createPinia()
app.use(router)
app.use(pinia)
app.use(i18n)
app.use(ElementPlus, {
  locale: getLocale() === 'en-US' ? en : zhCn,
})
console.log(getLocale())
app.mount('#app')
app.component('Multiselect', Multiselect)
app.component('Icon', Icon)
