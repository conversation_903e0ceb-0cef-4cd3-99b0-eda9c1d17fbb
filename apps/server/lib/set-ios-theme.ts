export const setIOSTheme = (color: string = '#F3F0F3') => {
  // 设置 theme-color
  let metaTheme = document.querySelector('meta[name="theme-color"]')
  if (!metaTheme) {
    metaTheme = document.createElement('meta')
    metaTheme.setAttribute('name', 'theme-color')
    document.head.appendChild(metaTheme)
  }
  metaTheme.setAttribute('content', color)

  // 设置 apple-mobile-web-app-capable
  let metaCapable = document.querySelector('meta[name="apple-mobile-web-app-capable"]')
  if (!metaCapable) {
    metaCapable = document.createElement('meta')
    metaCapable.setAttribute('name', 'apple-mobile-web-app-capable')
    metaCapable.setAttribute('content', 'yes')
    document.head.appendChild(metaCapable)
  }

  // 设置 apple-mobile-web-app-status-bar-style
  let metaStatusBar = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]')
  if (!metaStatusBar) {
    metaStatusBar = document.createElement('meta')
    metaStatusBar.setAttribute('name', 'apple-mobile-web-app-status-bar-style')
    metaStatusBar.setAttribute('content', 'default')
    document.head.appendChild(metaStatusBar)
  }
}
