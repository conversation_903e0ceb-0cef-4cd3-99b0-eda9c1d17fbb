
import { httpClient } from 'src/lib/http-client'

export const apiGetRankList = (data: M.Rank.params) =>
  httpClient.post<ApiResponse<M.Rank.ListResponse>>('/rank/sub/list', data)

export const apiCreateRank = (data: M.Rank.CreateRank) =>
  httpClient.post<ApiResponse<boolean>>('/rank/sub/save', data)

export const apiEditRank = (data: M.Rank.CreateRank) =>
  httpClient.post<ApiResponse<boolean>>('/rank/sub/save', data)

export const apiGetRankInfo = (data: { type: number, app_id: number, language_version: string }) =>
  httpClient.post<ApiResponse<{ info: M.Rank.RankInfo }>>('/rank/info', data)

export const apiSaveRankInfo = (data: M.Rank.RankInfo & { app_id: number, language_version: string }) =>
  httpClient.post<ApiResponse<{ info: M.Rank.RankInfo }>>('/rank/save', data)
