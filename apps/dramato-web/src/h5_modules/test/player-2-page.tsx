import { createComponent } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import TCPlayer from 'tcplayer.js'
import 'tcplayer.js/dist/tcplayer.min.css'
type PlayerPageOptions = {
  props: {}
}
export const PlayerPage = createComponent<PlayerPageOptions>({
  props: {},
}, props => {
  let player: any = null
  const videoRef = ref<HTMLVideoElement | null>(null)
  const route = useRoute()
  onMounted(() => {
    void initPlayer()
  })
  return () => (
    <x-player-page class="block">
      <div class="flex justify-center items-center">
        <button class="p-2 m-2 bg-red-500" onClick={onClick}>Play</button>
      </div>
      <video id="myVideo" width="400" height="600" preload="auto" playsinline webkit-playsinline />
    </x-player-page>
  )

  function onClick() {
    if (!player) return
    player.play()
  }

  function initPlayer() {
    // player-container-id 为播放器容器 ID，必须与 html 中一致
    player = TCPlayer('myVideo', {
      resolution: '480p',
      sources: [{
        src: route.query.url?.toString() || '', // 播放地址，必传
      }],
      licenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1308445808_1/v_cube.license', // license 地址，必传。参考准备工作部分，在视立方控制台申请 license 后可获得 licenseUrl
    })
    window.p = player
    // player.src(url); // url 播放地址
  }
})

export default PlayerPage
