import { createComponent, mc } from '@skynet/shared'
import { check } from '../../images/images'

type ActionDialogOptions = {
  props: {
    title: string
    actions: {
      value: number | string
      label: string
    }[]
    visible: boolean
    selected?: number
    safeBottom: boolean
  }
  emits: {
    close: () => void
    select: (index: number | string) => void
  }
}
export const ActionDialog = createComponent<ActionDialogOptions>({
  props: {
    title: '',
    actions: [],
    visible: false,
    selected: 0,
    safeBottom: false,
  },
  emits: {
    close: () => {},
    select: () => {},
  },
}, (props, { emit }) => {
  function hideActionSheet() {
    emit('close')
  }

  const selectAction = (id: number | string) => {
    emit('select', id)
  }

  const getActions = () => {
    return props.actions.map(action => {
      return (
        <li class={`flex items-center justify-between px-3 py-4 pr-2 ${action.value === props.selected && 'selected bg-[var(--grey-10)]'}`} onClick={() => selectAction(action.value)}>
          <span class="line-clamp-1 h-6 overflow-hidden break-words text-base leading-6">{action.label}</span>
          { action.value === props.selected ? <img src={check} class="mr-2 size-6 text-[var(--brand-6)]" /> : null }
        </li>
      )
    })
  }

  return () => (
    <x-action-dialog class={mc('relative z-toast duration-300', !props.visible && 'opacity-0')} tabindex="0">
      <x-action-dialog-shadow class={mc('fixed inset-0 left-0 right-0 top-0 max-w-[var(--phone-page-max-width)] pad:max-w-[var(--phone-page-max-width)] pc:max-w-[var(--phone-page-max-width)] mx-auto bg-[var(--mask-1)] transition-opacity duration-300 bg-opacity-40', !props.visible && 'opacity-0 pointer-events-none')} onClick={hideActionSheet} />
      <x-action-dialog-main-wrapper class={mc('max-w-[var(--phone-page-max-width)] pad:max-w-[var(--phone-page-max-width)] pc:max-w-[var(--phone-page-max-width)] mx-auto fixed flex flex-col bottom-0 left-0 right-0 bg-[#1D1D1E] pt-3 pb-2 rounded-t-xl transition-transform duration-300',
        !props.visible && 'opacity-0 pointer-events-none translate-y-full',
        props.safeBottom ? 'pb-safe-bottom' : 'pb-0',
      )}
      >
        <div class="p-3">
          <h3 class="text-base">{props.title}</h3>
        </div>
        <main class="max-h-[80vh] flex-1 overflow-y-auto py-2">
          <ul>
            {getActions()}
          </ul>
        </main>
      </x-action-dialog-main-wrapper>
    </x-action-dialog>
  )
})

export default ActionDialog
