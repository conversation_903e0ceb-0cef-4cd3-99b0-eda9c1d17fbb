declare namespace M {
  export interface User {
    uid: string
    phone: string
    avatar: string
    nickname: string
    company: string // 公司名称
    usci: string // 统一社会信用代码
    licence_pic: string // 营业执照图片
    auth_status: number // 认证状态 0:全部 1: 未认证 2: 已认证 3：认证失败
    email?: string
  }

  export interface UserUpdateParams {
    nickname: string
    avatar: string
    company: string // 公司名称
    usci: string // 统一社会信用代码
    licence_pic: string // 营业执照图片
  }
}
