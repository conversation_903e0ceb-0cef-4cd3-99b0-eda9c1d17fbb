<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="referrer" content="always">
  <meta name="viewport"
    content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
  <title>DramaWave</title>
  <script>window.t0_for_report = Date.now()</script>
  <!-- reset css -->
  <style>
    * {
      padding: 0;
      margin: 0;
      box-sizing: border-box;
    }

    *::before {
      box-sizing: border-box;
    }

    *::after {
      box-sizing: border-box;
    }

    ol,
    ul {
      list-style: none;
    }

    button,
    input {
      font-size: inherit;
      font-family: inherit;
      color: inherit;
    }

    img {
      max-width: 100%;
    }

    body {
      font-synthesis: none;
      text-rendering: optimizeLegibility;
    }
  </style>
  <!-- app css -->
  <style>
    body {
      background-color: #0b0b0b;
      color: #fff;
    }

    @media (min-width: 1000px) {
      #app {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }

      #app>article {
        flex: 1;
        display: flex;
      }
    }


    x-wrapper {
      flex: 1;
      display: block;
      background-size: cover;
      background-position: center;
      position: relative;
      padding-top: 28px;
      display: block;
      padding-bottom: 28px;
      overflow: hidden;
    }

    @media (min-width: 1000px) {
      x-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    main {
      display: block;
      margin-left: 12px;
      margin-right: 12px;
    }

    @media (min-width: 1000px) {
      main {
        display: flex;
        max-width: 1000px;
        column-gap: 50px;
      }
    }

    x-cover {
      display: flex;
      justify-content: center;
      align-items: center;
      /* 宽高比 3:4 */
      aspect-ratio: 3 / 4;
      margin: 0 28px;
      position: relative;
      overflow: hidden;
    }



    @media (min-width: 1000px) {
      x-cover {
        width: 400px;
        flex-shrink: 0;
        margin: 0;
      }
    }

    x-cover>.cover {
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.20);
      background-color: lightgray;
    }

    x-cover>.play {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    x-summary {
      display: block;
    }

    @media (min-width: 1000px) {
      x-summary {
        display: flex;
        flex-direction: column;
        row-gap: 12px;
        justify-content: center;
        align-items: center;
      }
    }

    x-summary x-desc {
      overflow: hidden;
      line-height: 1.4em;
      color: #AAA;
      font-size: 14px;
    }

    x-summary x-desc.folded {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      text-overflow: ellipsis;
      max-height: 7em;
    }

    x-summary x-desc.unfolded {
      max-height: none;
      display: block;
    }

    x-summary x-more,
    x-summary x-pick-up {
      display: block;
      text-align: right;
      font-size: 12px;
    }

    x-summary x-pick-up {
      display: none;
    }

    x-summary h1 {
      margin-top: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: var(--text-Icon-text-1, #FDFBFC);
      font-size: 20px;
      font-weight: 510;
    }

    @media (min-width: 1000px) {
      x-summary h1 {
        font-size: 24px;
        font-size: 38px;

        white-space: normal;
      }
    }

    x-summary ul {
      display: flex;
      align-items: center;
      gap: 6px;
      margin: 0 auto;
    }

    x-summary ul li {
      line-height: 1.25rem;
      border-radius: 0.675rem;
      background: #313131;
      padding: 0 1rem;
      font-size: 12px;
    }

    x-summary button {
      background-color: blue;
      color: white;
      min-height: 48px;
      min-width: 10em;
      border: none;
      border-radius: 24px;
      /* 渐变背景，从左往右，FE8C0E 到 F700D8 */
      background: linear-gradient(to right, #FE8C0E, #F700D8);
      margin-top: 32px;
      font-size: 20px;
      font-weight: 700;
      position: fixed;
      bottom: 80px;
      left: 12px;
      right: 12px;
    }

    @media (min-width: 1000px) {
      x-summary button {
        position: relative;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
      }
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.2);
      }

      100% {
        transform: scale(1);
      }
    }

    x-summary button img {
      position: absolute;
      top: 15px;
      right: 18px;
      color: white;
      animation: pulse 1s infinite;
    }



    x-mask {
      display: block;
      position: absolute;
      inset: 0;
      height: 100%;
      z-index: 2;
      background: linear-gradient(to bottom, #0b0b0b1a, #0b0b0bff);
    }

    @media (min-width: 1000px) {
      x-mask {
        background: linear-gradient(to bottom, #0b0b0b6a, #0b0b0bff);
      }
    }

    x-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 1;
      overflow: hidden;
    }

    x-bg img {
      /* 模糊效果 */
      filter: blur(10px);
      margin: -10px;
      display: block;
      width: calc(100% + 20px);
      max-width: none;
    }

    x-top-nav {
      position: sticky;
      top: 0;
      z-index: 10;
      color: #fff;
      background-color: #0b0b0b;
      display: grid;
      grid-template-areas:
        'logo title button'
        'logo subtitle button'
      ;
      grid-template-columns: max-content 1fr max-content;
      grid-template-rows: 1fr max-content;
      justify-content: left;
      align-items: center;
      justify-items: start;
      padding: 12px 18px;
      row-gap: 0px;
      column-gap: 8px;
    }

    @media (min-width: 1000px) {
      x-top-nav {
        position: fixed;
        top: 10px;
        left: 0;
        right: 0;
        z-index: 10;
        max-width: 1000px;
        margin: 0 auto;
        padding-left: 0;
        padding-right: 0;
        background-color: transparent;
      }
    }

    x-top-nav .logo {
      grid-area: logo;
    }

    x-top-nav .title {
      grid-area: title;
    }

    @media (min-width: 1000px) {
      x-top-nav .title {
        width: 164px;
        height: 21px;
      }
    }

    x-top-nav .subtitle {
      grid-area: subtitle;
      color: #666;
      font-size: 12px;
    }

    @media (min-width: 1000px) {
      x-top-nav .subtitle {
        display: none;
      }
    }

    x-top-nav button {
      grid-area: button;
      display: flex;
      height: 38px;
      padding: 0px var(--number-16, 18px);
      justify-content: center;
      align-items: center;
      gap: 10px;
      border: none;
      border-radius: 19px;
      background: var(--brand-brand-6, #FC2763);
    }

    @media (min-width: 1000px) {
      x-top-nav button {
        display: none;
      }
    }
  </style>
</head>

<body>
  <!-- 自定义标签不能自闭合，自闭合会出 bug -->
  <div id="app" style="position: relative; visibility: hidden;">

    <x-top-nav>
      <img class="logo" src="https://static-v1.mydramawave.com/frontend_static/assets/logo-ZkLn7rqY.webp" width="37"
        height="37">
      <img class="title" src="https://static-v1.mydramawave.com/frontend_static/assets/dramawave-JYta-J2M.webp"
        width="92" height="12">
      <span class="subtitle" data-i18n="continueToNextEpisode"></span>
      <button data-i18n="open">&nbsp;</button>
    </x-top-nav>
    <article class="art">
      <x-wrapper>
        <x-bg>
          <img class="bg ui-cover" alt="background" />
          <x-mask> <!-- 不能自闭合 --> </x-mask>
        </x-bg>
        <main style="position: relative; z-index: 3;">
          <x-cover>
            <img class="ui-cover cover" src="" alt="cover" />
            <img class="play" width="90" height="90"
              src="https://static-v1.mydramawave.com/frontend_static/dramato-web/art/btn.svg" alt="play" />
          </x-cover>
          <x-summary style="display: flex; flex-direction: column; row-gap: 12px; text-align: center;">
            <h1 class="ui-name">&nbsp;</h1>
            <!-- tags -->
            <ul class="ui-content_tags">
            </ul>
            <div style="text-align: left;">
              <x-desc class="ui-desc unfolded"></x-desc>
              <!-- <x-more data-i18n="more"></x-more> -->
              <!-- <x-pick-up data-i18n="pickUp"></x-pick-up> -->
            </div>
            <button>
              <img src="https://static-v1.mydramawave.com/frontend_static/dramato-web/art/click.webp" width="60"
                height="51" alt="click" />
              <span data-i18n="continueWatching"></span>
            </button>
          </x-summary>
        </main>
      </x-wrapper>
    </article>

  </div>
  <div style="width: 0; height: 0; margin-left: -10px;">
    <iframe id="iframe" src="" width="0" height="0" name="iframe"></iframe>
  </div>
  <!-- i18n starts-->
  <script>
    window.i18n = {
      'en-US': {
        'continueWatching': 'Continue Watching',
        'pickUp': 'Pick up',
        'more': 'More',
        'continueToNextEpisode': 'Next Episode',
        'open': 'Open'
      },
      'zh-CN': {
        'continueWatching': '继续观看',
        'pickUp': '收起',
        'more': '更多',
        'continueToNextEpisode': '继续下一集',
        'open': '打开'
      },
      'zh-TW': {
        'continueWatching': '繼續觀看',
        'pickUp': '收合',
        'more': '更多',
        'continueToNextEpisode': '繼續下一集',
        'open': '開啟'
      },
      'ja-JP': {
        'continueWatching': '続きを見る',
        'pickUp': '折りたたむ',
        'more': 'その他',
        'continueToNextEpisode': '次話を見る',
        'open': '開く'
      },
      'ko-KR': {
        'continueWatching': '이어 보기',
        'pickUp': '접기',
        'more': '더보기',
        'continueToNextEpisode': '다음 회차',
        'open': '열기'
      },
      'es-ES': {
        'continueWatching': 'Continuar viendo',
        'pickUp': 'Contraer',
        'more': 'Más',
        'continueToNextEpisode': 'Siguiente episodio',
        'open': 'Abrir'
      },
      'pt-PT': {
        'continueWatching': 'Continuar a ver',
        'pickUp': 'Recolher',
        'more': 'Mais',
        'continueToNextEpisode': 'Próximo Episódio',
        'open': 'Abrir'
      },
      'vi-VN': {
        'continueWatching': 'Tiếp tục xem',
        'pickUp': 'Thu gọn',
        'more': 'Thêm',
        'continueToNextEpisode': 'Tập tiếp theo',
        'open': 'Mở'
      },
      'th-TH': {
        'continueWatching': 'ดูต่อ',
        'pickUp': 'พับเก็บ',
        'more': 'เพิ่มเติม',
        'continueToNextEpisode': 'ตอนต่อไป',
        'open': 'เปิด'
      },
      'id-ID': {
        'continueWatching': 'Lanjutkan menonton',
        'pickUp': 'Sembunyikan',
        'more': 'Lainnya',
        'continueToNextEpisode': 'Episode Selanjutnya',
        'open': 'Buka'
      },
      'tl-PH': {
        'continueWatching': 'Magpatuloy sa Panonood',
        'pickUp': 'Tiklop',
        'more': 'Marami pa',
        'continueToNextEpisode': 'Susunod na Episode',
        'open': 'Buksan'
      },
      'fr-FR': {
        'continueWatching': 'Continuer à regarder',
        'pickUp': 'Réduire',
        'more': 'Plus',
        'continueToNextEpisode': 'Épisode suivant',
        'open': 'Ouvrir'
      },
      'de-DE': {
        'continueWatching': 'Weiter schauen',
        'pickUp': 'Einklappen',
        'more': 'Mehr',
        'continueToNextEpisode': 'Nächste Folge',
        'open': 'Öffnen'
      },
      'it-IT': {
        'continueWatching': 'Continua a guardare',
        'pickUp': 'Riduci',
        'more': 'Altro',
        'continueToNextEpisode': 'Prossimo Episodio',
        'open': 'Apri'
      },
      'ru-RU': {
        'continueWatching': 'Продолжить просмотр',
        'pickUp': 'Свернуть',
        'more': 'Ещё',
        'continueToNextEpisode': 'Следующий эпизод',
        'open': 'Открыть'
      },
      'tr-TR': {
        'continueWatching': 'İzlemeye Devam Et',
        'pickUp': 'Daralt',
        'more': 'Daha Fazla',
        'continueToNextEpisode': 'Sonraki Bölüm',
        'open': 'Aç'
      },
      'ms-MY': {
        'continueWatching': 'Teruskan menonton',
        'pickUp': 'Tutup',
        'more': 'Lagi',
        'continueToNextEpisode': 'Episod Seterusnya',
        'open': 'Buka'
      }
    }


  </script>
  <!-- i18n ends -->

  <!-- main starts -->
  <script>
    const eventReported = {
      pageView: false,
      viewContent: false,
      startTrial: false,
    }
    window.closeClipboard = true
    let timer
    let clipboardData = ''
    let buffer = []
    let episodeId = ''
    let w2a_key = ''
    let needReportViewContent = false
    let reportNowTimer = null
    let createScriptTimer = null
    const environment = window.location.hostname.indexOf('test') >= 0 ? 'development' : 'prod'
    const backendUrl = 'https://trace.mydramawave.com/client_track'
    const backendUrlTest = 'https://trace-test.mydramawave.com/client_track'
    const search = window.location.search.replace(/^\?/, '')
    const language = navigator.language || 'en-US'
    const t = getTranslation(language)
    const data = [encodeURIComponent(search + `&click_time=${Date.now()}`)]
    const _query = new URLSearchParams(search)
    const query = Object.fromEntries(_query.entries())
    const pixelId = query['pid'] || '2246766269032711'
    const contentId = query['content_id']
    const tasks = { hmReady: false, seriesReady: false }
    function checkTasks() {
      if (tasks.hmReady && tasks.seriesReady) {
        // 这里可以做一些需要等两个任务都完成后再执行的操作
        const app = document.querySelector('#app')
        app.style.visibility = 'visible'
        addReport({
          event: 'landing_page_visible',
          event_info: JSON.stringify(getReportData()),
          load_time: window.t0_for_report ? Date.now() - window.t0_for_report : undefined
        })
        reportNow()
      }
    }
    fetchSeries(contentId).then(renderUi, () => {
      addReport({
        event: 'landing_page_load_series_failed',
        event_info: JSON.stringify(getReportData())
      })
      reportNow()
    })
      .finally(() => {
        tasks.seriesReady = true
        checkTasks()
      })
    // https://www.web2app.com.cn/developer?id=73
    const web2appDomain = 'luckanalysisdata.com'
    const onError = () => {
      const app = document.querySelector('#app')
      app.style.visibility = 'visible'
      addReport({
        event: 'landing_page_load_sdk_failed',
        event_info: JSON.stringify(getReportData())
      })
      reportNow()
    }
    const t3_for_report = Date.now()
    createScript('https://' + web2appDomain + '/jssdk/v10/dramawave?pid=' + pixelId + '&adt=' + data.join(','), () => {
      addReport({
        event: 'landing_page_load_sdk_time',
        event_info: JSON.stringify(getReportData({ load_time: Date.now() - t3_for_report })),
        load_time: Date.now() - t3_for_report
      })
      reportNow()
      tasks.hmReady = true
      checkTasks()
      window._clipboard(function (value) {
        clipboardData = value.replace(/^w2a_data:/g, 'w2a_data://')
        w2a_key = value.replace(/^w2a_data:/g, '')
        // 要等 w2a_key 获取到之后再上报
        addReport({
          event: 'landing_page_view',
          event_info: JSON.stringify(getReportData())
        })
        reportNow()
        if (needReportViewContent) {
          addReport({
            event: 'landing_view_content',
            event_info: JSON.stringify(getReportData())
          })
          reportNow()
        }
      })
    }, onError)
    window.addEventListener('load', function () {
      if (w2a_key) {
        addReport({
          event: 'landing_view_content',
          event_info: JSON.stringify(getReportData())
        })
        try {
          addReport({
            event: 'landing_performance',
            event_info: JSON.stringify(window.performance.getEntriesByType("navigation")[0])
          })
        } catch (e) { }
        reportNow()
      } else {
        needReportViewContent = true
      }
    })
    const button = document.getElementById('app');
    button.addEventListener('click', function () {
      try {
        writeToClipboard()
        sendEvent('BI_StartTrial')
        addReport({
          event: 'landing_start_trial',
          event_info: JSON.stringify(getReportData())
        })
        reportNow(true)
      } catch (err) { }
      goToApp()
    });
    window.onbeforeunload = function () {
      reportNow()
    }
    const more = document.querySelector('x-more')
    more && more.addEventListener('click', function () {
      const desc = document.querySelector('x-desc')
      desc.classList.remove('folded')
      desc.classList.add('unfolded')
      const more = document.querySelector('x-more')
      more.style.display = 'none'
      const pickUp = document.querySelector('x-pick-up')
      pickUp.style.display = 'block'
    })
    const pickUp = document.querySelector('x-pick-up')
    pickUp && pickUp.addEventListener('click', function () {
      const desc = document.querySelector('x-desc')
      desc.classList.add('folded')
      desc.classList.remove('unfolded')
      const more = document.querySelector('x-more')
      more.style.display = 'block'
      const pickUp = document.querySelector('x-pick-up')
      pickUp.style.display = 'none'
    })
    // 找到页面中所有 class 含有 i18n- 的元素，并设置其 textContent 为 t 的值
    const i18nElements = document.querySelectorAll('[data-i18n]')
    i18nElements.forEach(element => {
      element.textContent = t[element.dataset.i18n]
    })

    /************************************************************
     * Helpers
     ************************************************************/

    function writeToClipboard() {
      try {
        ClipboardJS.copy(clipboardData)
      } catch (e) {
        console.error('Error copying to clipboard:', e)
      }
    }

    function isIos() {
      return (
        window.navigator.userAgent
        && (/iP(ad|hone|od)/i.test(window.navigator.userAgent)
          // The new iPad Pro Gen3 does not identify itself as iPad, but as Macintosh.
          // https://github.com/vueuse/vueuse/issues/3577
          || (window.navigator.maxTouchPoints > 2 && /iPad|Macintosh/.test(window.navigator.userAgent)))
      )
    }
    /**
     * 上报事件
     * @return {Promise}
     */
    function sendEvent(eventName) {
      try {
        hm && hm.event(eventName)
      } catch (e) {
        addReport({
          event: 'landing_page_send_event_failed',
          event_info: JSON.stringify(getReportData())
        })
        reportNow()
      }
    }
    function goToApp() {
      const links = generateLinks()
      if (isIos()) {
        // iOS使用Universal Link
        window.location.href = links.universalLink
      } else {
        // document.addEventListener('visibilitychange', handleVisibilityChange)
        // timer = window.setTimeout(() => {
        //   window.open(links.googlePlayLink)
        // }, 2000)
        try {
          checkAndroidAppInstalled();
        } catch (e) {
          window.location.href = links.googlePlayLink
        }
      }
    }
    function handleVisibilityChange() {
      window.clearTimeout(timer) // 如果页面变为可见，清除定时器
    }
    function createScript(src, successCallback, errorCallback) {
      var script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = src;
      script.onload = () => {
        if (createScriptTimer) {
          window.clearTimeout(createScriptTimer)
          createScriptTimer = null
        }
        successCallback()
      };
      script.onerror = errorCallback
      createScriptTimer = window.setTimeout(() => {
        addReport({
          event: 'landing_page_load_sdk_timeout',
          event_info: JSON.stringify(getReportData())
        })
        reportNow()
        errorCallback()
      }, 5000)
      document.head.appendChild(script);
    }
    // https://api-test.mydramawave.com/dm-api/h5/series/info?series_id=QJHm97fn1X
    function fetchSeries(seriesId) {
      const baseUrl = environment === 'development' ? 'https://api-test.mydramawave.com' : 'https://api.mydramawave.com'
      const temp_t = Date.now()
      return fetch(`${baseUrl}/dm-api/drama/share/series_info?series_id=${seriesId}`)
        .then(res => {
          addReport({
            event: 'landing_page_load_series_time',
            event_info: JSON.stringify(getReportData({ load_time: Date.now() - temp_t })),
            load_time: Date.now() - temp_t
          })
          reportNow()
          return res.json()
        })
        .then(res => res.data.series_info)
    }

    function getOsName() {
      const ua = navigator.userAgent && navigator.userAgent.toLowerCase()
      if (ua.indexOf('windows') >= 0) return 'windows'
      if (ua.indexOf('macintosh') >= 0) return 'mac'
      if (ua.indexOf('iphone') >= 0) return 'ios'
      if (ua.indexOf('ipad') >= 0) return 'ios'
      if (ua.indexOf('ipod') >= 0) return 'ios'
      if (ua.indexOf('android') >= 0) return 'android'
      if (ua.indexOf('linux') >= 0) return 'linux'
      return 'unknown'
    }

    function addReport(data) {
      if (eventReported[data.event]) return
      eventReported[data.event] = true
      buffer.push({
        channel: environment === 'development' ? 'devtest' : 'prod',
        environment,
        href: window.location.href,
        page_title: document.title,
        referrer: document.referrer,
        screen_height: window.screen.height.toString(),
        screen_width: window.screen.width.toString(),
        user_agent: window.navigator.userAgent,
        time: Date.now().toString(),
        client_height: window.innerHeight.toString(),
        client_width: window.innerWidth.toString(),
        os_name: getOsName(),
        user_source: 'web',
        ...data,
      })
    }

    function reportNow(force) {
      if (reportNowTimer) {
        window.clearTimeout(reportNowTimer);
      }
      const report = () => {
        reportNowTimer = null
        const isDev = environment === 'development'
        if (buffer.length === 0) return
        const dataToSend = [...buffer]
        buffer = []
        const blob = new Blob([JSON.stringify(dataToSend)], { type: 'application/json' })
        navigator.sendBeacon(isDev ? backendUrlTest : backendUrl, blob)
      }
      if (force) {
        report()
      } else {
        reportNowTimer = window.setTimeout(report, 300)
      }

    }
    function renderUi(data) {
      episodeId = data.episode_list && data.episode_list[0] && data.episode_list[0].id || ''
      const { name, desc, cover, content_tags } = data
      const keys = [
        { key: 'name', kind: 'text' },
        { key: 'desc', kind: 'text' },
        { key: 'cover', kind: 'image' },
        { key: 'content_tags', kind: 'textArray' }
      ]
      keys.forEach(({ key, kind }) => {
        const elements = document.querySelectorAll('.ui-' + key)
        if (!elements.length) return
        const value = data[key]
        elements.forEach(element => {
          if (kind === 'image') {
            const t = Date.now()
            element.src = value ?? ''
            element.onload = function () {
              addReport({
                event: 'landing_page_load_image_time',
                event_info: JSON.stringify(getReportData({
                  load_time: Date.now() - t,
                  total_time: Date.now() - (window.t0_for_report || 0),
                  load_src: element.src
                })),
                load_time: Date.now() - t,
                total_time: Date.now() - (window.t0_for_report || 0),
                load_src: element.src
              })
              reportNow()
            }
          } else if (kind === 'text') {
            element.textContent = value ?? ""
          } else if (kind === 'textArray') {
            if (value) {
              value.forEach(item => {
                const li = document.createElement('li')
                li.textContent = item
                element.appendChild(li)
              })
            } else {
              element.style.display = 'none'
            }
          }
        })
      })
    }
    // 支持 en-US, ja-JP, ko-KR, es-ES, pt-PT, vi-VN, th-TH, id-ID, tl-PH, fr-FR, de-DE, it-IT, ru-RU, tr-TR, ms-MY, zh-TW, zh-Cn
    function getTranslation(language) {
      return window.i18n[language] || i18n['en-US']
    }
    function getReportData(more) {
      more = more || {}
      return {
        ...query,
        pid: pixelId,
        pid_from: query['pid'] ? 'url' : 'default',
        fbc: window.__fbc__,
        fbp: window.__fbp__,
        w2a_key,
        __ip__: window.__ip__,
        __apn__: window.__apn__,
        __pid__: window.__pid__,
        __adv__: window.__adv__,
        __adt__: window.__adt__,
        __dad__: window.__dad__,
        __exid__: window.__exid__,
        ...more,
      }
    }
    /**
     * 通过对象生成查询参数字符串，所有 value 自动转义
     * @param queries 要设置的查询参数对象
     */
    function createQueryString(queries) {
      const p = new URLSearchParams()
      Object.entries(queries).forEach(([key, value]) => {
        value && p.set(key, value.toString())
      })
      return p.toString()
    }

    function checkAndroidAppInstalled() {
      const iframe = document.getElementById("iframe");
      const { deepLink, googlePlayLink } = generateLinks()
      // 再执行谷歌商店跳转
      iframe.src = googlePlayLink;
      // 先自启用
      iframe.src = deepLink;
    }

    function generateLinks() {
      // dramawave://dramawave.app?redirect=/datail?id=MhC8rlRsV&timestamp=1678901234567&campaign id=120216175334570464&
      // adset id=120218590781410617&ad id=120217087272680464&fbp=fb.1.1596403881668.1116446470&fbc=fb.1.1554763741205.AbCdEfGhIiKlMnOpOrStUvWxYz&channel=facebook
      const qs = createQueryString({
        id: contentId,
        w2a_key: 'w2a_data:' + w2a_key,
        timestamp: Date.now(),
        campaign_id: query['hm_cp_id'],
        adset_id: query['hm_adset_id'],
        ad_id: query['hm_ad_id'],
        fbp: window.__fbp__,
        fbc: window.__fbc__,
        channel: query['channel'],
        dpsource: 'W2A',
        pixel_id: pixelId,
      })
      // const deepLink = `intent://push?type=1&playletId=3283&fragmentId=0&callback=dramawave://dramawave.app?redirect=${encodeURIComponent('/detail?' + qs)}#Intent;scheme=dramawave;package=com.dramawave.app;S.browser_fallback_url=market://details?id=com.dramawave.app;end`;
      const deepLink = `intent://dramawave.app?redirect=${encodeURIComponent('/detail?' + qs)}&w2a_key=${encodeURIComponent('w2a_data:' + w2a_key)}#Intent;scheme=dramawave;package=com.dramawave.app;end;`
      // const deepLink = `dramawave://dramawave.app?redirect=${encodeURIComponent('/detail?' + qs)}`
      const universalLink = `https://mydramawave.com?redirect=${encodeURIComponent('/detail?' + qs)}&w2a_key=${encodeURIComponent('w2a_data:' + w2a_key)}`
      // const googlePlayLink = 'https://play.google.com/store/apps/details?id=com.dramawave.app'
      const googlePlayLink = 'market://details?id=com.dramawave.app'
      return { deepLink, universalLink, googlePlayLink }
    }


  </script>
  <!-- main ends -->
  <script>
    window.t1_for_report = Date.now()
    const load_time = window.t1_for_report - window.t0_for_report
    addReport({
      event: 'landing_page_load_time',
      event_info: JSON.stringify(getReportData({ load_time: load_time })),
      load_time: load_time
    })
    reportNow()
  </script>

</body>



</html>