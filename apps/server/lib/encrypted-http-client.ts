import { getQuery, tryGetLocalString, tryParseJson } from '@skynet/shared'
// import { omit } from 'lodash-es'
// import { useLocale } from 'src/h5_modules/i18n/i18n'
// import { computed } from 'vue'
import { get_authorization_for_browser } from './authorization'
import { decrypt, encrypt } from './crypt'
import { get_k_device_hash_from_cookie } from './device-id'
import { commonParams, type ConstructParams, HttpClient } from './http-client'

const k = '2r36789f45q01ae5'
const config = useRuntimeConfig()

// const { currentLocale } = useLocale()
// 这里不 omit name 会导致 axios 报错
// const localeObject = computed(() => omit(currentLocale.value, 'name'))
const skipEncrypt = tryGetLocalString('skipEncrypt', '') || getQuery('skipEncrypt', '')
if (skipEncrypt) {
  localStorage.setItem('skipEncrypt', skipEncrypt)
}
const encryptedCommonParams: ConstructParams = {
  ...commonParams,
  isError: response => {
    if (response.data && !skipEncrypt) {
      const decryptedData = decrypt(response.data, k)
      response.data = tryParseJson(decryptedData, decryptedData)
    }
    return ![200, 0].includes(response.data.code)
  },
  baseURL: config.public.apiUrl,
  transformRequest: !skipEncrypt ? data => {
    // 此处需要禁用 axios 默认将 data 进行 JSON.stringify 的行为
    return data
  } : undefined,
  requestInterceptor: [
    config => {
      if (config.data && !skipEncrypt) {
        config.data = encrypt(JSON.stringify(config.data), k)
        config.headers['Content-Type'] = 'application/json'
      }
      Object.assign(config.headers, {
        'app-name': 'com.dramawave.h5', // 'com.dramawave.app',
        'app-version': '1.2.20',
        'device-hash': get_k_device_hash_from_cookie(),
        'device-id': get_k_device_hash_from_cookie(),
        authorization: get_authorization_for_browser(),
        device: 'h5',
        'Skip-Encrypt': skipEncrypt ? 1 : undefined,
        // ...localeObject.value,
      })
      return config
    },
    null,
  ],
}

export const encryptedHttpClient = new HttpClient(encryptedCommonParams)
