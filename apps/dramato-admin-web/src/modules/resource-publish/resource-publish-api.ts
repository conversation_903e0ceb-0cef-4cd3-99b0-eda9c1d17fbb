import { httpClient } from 'src/lib/http-client'
import { trim } from 'lodash-es'
import { filterIt, splitIt } from '@skynet/shared'
import { checkResourceTitleOrId, checkEpisodeTitleOrId } from './util'
import dayjs from 'dayjs'
const toNumberArr = (value: string) => {
  if (value) {
    return value.split(',').map(i => +i)
  } else {
    return []
  }
}

const toStringArr = (value: string) => {
  if (value) {
    return value.split(',').map(text => trim(text)).filter(text => text)
  } else {
    return []
  }
}

const toNumArr = (value: string[]) => {
  return value.map(v => +v)
}

const arrToString = (value: number[] | string[]) => {
  if (!value || ('' + value.length).length === 0) {
    return ''
  } else {
    return value.join(',')
  }
}

const formatLabelIds = (value: number[] | string[] | number[][]) => {
  if (!value || ('' + value.length).length === 0) {
    return ''
  } else if (Array.isArray(value[0])) {
    return (value as number[][]).map(v => v[1]).filter(Boolean).join(',')
  } else {
    return value.join(',').trim()
  }
}

const dateCheck = (value?: number) => {
  if (!value) return 0
  return value
}

const formatDate = (value?: string) => {
  return value ? dayjs(value).unix() : undefined
}

// 资源录入
export const apiGetResourcePublishList = (data: M.IResourcePublishQueryParams) =>
  httpClient.post<ApiResponse<{
    list: M.IResourcePublishListItem[]
    total: number
  }>>('/series_resource_v2/entering_list', data, {
    transformRequestData: {
      create_start_time: [dateCheck],
      create_end_time: [dateCheck],
      series_key_or_title_list: [checkEpisodeTitleOrId],
      resource_id_or_title_list: [checkResourceTitleOrId],
    },
  })

export const apiCreateOrUpdateResourcePublish = (data: M.IResourcePublishDetail) => {
  return httpClient.post<ApiResponse<M.IResourcePublishDetail>>('/series_resource_v2/create_or_update', data, {
    transformRequestData: {
      auth_platform: [arrToString],
      language_code: [arrToString],
      subtitle_language_code: [arrToString],
      upload_subtitle_language_code: [arrToString],
      auth_start_time: [dateCheck],
      auth_end_time: [dateCheck],
      free_start_time: [dateCheck],
      online_time: [dateCheck],
      preview_time: [dateCheck],
      deploy_time: [dateCheck],
      poster_preview_time: [dateCheck],
      root_label_ids: [arrToString],
      first_label_ids: [arrToString],
      second_label_ids: [arrToString],
      marital_status: [arrToString],
      fertility_status: [arrToString],
      age: [arrToString],
    },
    transformResponseData: {
      'data.auth_platform': [toNumberArr],
      'data.language_code': [toStringArr],
      'data.subtitle_language_code': [toStringArr],
      'data.upload_subtitle_language_code': [toStringArr],
      'data.marital_status': [toNumberArr],
      'data.fertility_status': [toNumberArr],
      'data.age': [toNumberArr],
      'data.root_label_ids': [toStringArr],
      'data.first_label_ids': [toStringArr],
      'data.second_label_ids': [toStringArr],
    },
  })
}

export const apiSaveDraft = (data: { entering_info_req: M.IResourcePublishDetail, check_info_req: M.IPassForm }) => {
  return httpClient.post<ApiResponse<M.IResourcePublishDetail>>('/resource_stat/resource_update', data, {
    transformRequestData: {
      'entering_info_req.auth_platform': [arrToString],
      'entering_info_req.language_code': [arrToString],
      'entering_info_req.subtitle_language_code': [arrToString],
      'entering_info_req.upload_subtitle_language_code': [arrToString],
      'entering_info_req.auth_start_time': [dateCheck],
      'entering_info_req.auth_end_time': [dateCheck],
      'entering_info_req.free_start_time': [dateCheck],
      'entering_info_req.online_time': [dateCheck],
      'entering_info_req.preview_time': [dateCheck],
      'entering_info_req.deploy_time': [dateCheck],
      'entering_info_req.poster_preview_time': [dateCheck],
      'entering_info_req.marital_status': [arrToString],
      'entering_info_req.fertility_status': [arrToString],
      'entering_info_req.age': [arrToString],
      'entering_info_req.root_label_ids': [arrToString],
      'entering_info_req.first_label_ids': [arrToString],
      'entering_info_req.second_label_ids': [formatLabelIds],
      'check_info_req.root_label_ids': [arrToString],
      'check_info_req.first_label_ids': [arrToString],
      'check_info_req.second_label_ids': [formatLabelIds],
    },
    transformResponseData: {
      'data.auth_platform': [toNumberArr],
      'data.language_code': [toStringArr],
      'data.subtitle_language_code': [toStringArr],
      'data.upload_subtitle_language_code': [toStringArr],
    },
  })
}

export const apiGetResourcePublishDetail = (data: {
  id: number
}) => httpClient.post<ApiResponse<M.IResourcePublishDetail>>('/series_resource_v2/detail', data, {
  transformResponseData: {
    'data.auth_platform': [toNumberArr],
    'data.language_code': [toStringArr],
    'data.subtitle_language_code': [toStringArr],
    'data.upload_subtitle_language_code': [toStringArr],
    'data.first_label_ids': [splitIt([',']), filterIt(Boolean), toNumArr],
    'data.root_label_ids': [splitIt([',']), filterIt(Boolean), toNumArr],
    'data.second_label_ids': [splitIt([',']), filterIt(Boolean), toNumArr],
    'data.marital_status': [toNumberArr],
    'data.fertility_status': [toNumberArr],
    'data.age': [toNumberArr],
  },
})

export const apiDeleteResource = (data: {
  id: number
  operate_status: 1 | 2 // 1 归档 2 删除
}) => httpClient.post<ApiResponse<null>>('/series_resource_v2/edit_status', data)

export const apiCommitResourcePublishAudit = (data: {
  ids: number[]
  audit_operate: 1 | 2 // 审核操作 1 提交审核 2 撤销审核
}) => httpClient.post<ApiResponse<null>>('/series_resource_v2/audit', data)

// 批量导入
export const apiUploadResourcePublishExcel = (data: {
  file: File
  req_time?: 1 | 2
  deal_list?: M.ImportErrorItem[]
  req_data?: string
}) =>
  httpClient.post<ApiResponse<{
    repeat_list?: M.ImportErrorItem[]
  }>>('/series_resource_v2/business_batch_import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })

// 验收页面
export const apiGetResourceCheckList = (data: M.IResourcePublishQueryParams) =>
  httpClient.post<ApiResponse<{
    list: M.IResourceCheckItem[]
    total: number
  }>>('/series_resource_v2/check_list', data, {
    transformRequestData: {
      series_key_or_title_list: [checkEpisodeTitleOrId],
      resource_id_or_title_list: [checkResourceTitleOrId],
    },
  })

// 审核
export const apiAuditResource = (data: {
  id: number
  check_status: 2 | 3 // 状态 1 待审核 2 审核不通过 3 审核通过 4 字幕校验通过
  reason?: string
}) => httpClient.post<ApiResponse<null>>('/series_resource_v2/edit_check_status', data)

// 字幕校验列表
export const apiGetSubtitleCheckList = (data: M.IResourcePublishQueryParams) =>
  httpClient.post<ApiResponse<{
    list: M.ICheckSubtitle[]
    total: number
  }>>('/series_resource_v2/subtitle_check_list', data)

// 字幕翻译进度列表
export const apiGetTranslateList = (data: {
  id: number
}) =>
  httpClient.post<ApiResponse<{
    list: M.ISubtitleTranslate[]
    total: number
  }>>('/series_resource_v2/subtitle_trans_list', data)

// 上传封面
export const apiUploadCoverUrl = (data: {
  id: number
  path: string
  type?: 0 | 1 | 2 | 3 | 4 | 5 //  0 竖排封面 1 横排封面 2 补充封面 3. psd资源 4. 竖排背景 5. 纯净封面
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/cover_upload', data)

// 终审页面
export const apiGetPublishList = (data: M.IResourcePublishQueryParams) => httpClient.post<ApiResponse<{
  list: M.IResourcePublishItem[]
  total: number
}>>('/series_resource_v2/publish_list', data, {
  transformRequestData: {
    series_key_or_title_list: [checkEpisodeTitleOrId],
    resource_id_or_title_list: [checkResourceTitleOrId],
    create_start_time: [dateCheck],
    create_end_time: [dateCheck],
  },
})

// 终审发布
export const apiPublishResource = (data: {
  ids: number[]
  type: 1 | 2 | number // 1 发布 2 原剧集发布
  language_codes?: string[]
  re_transcode?: 1 | 0
  re_inject?: 1 | 0
  online_time?: number
}) => httpClient.post<ApiResponse<null>>('/series_resource_v2/publish', data, {
  transformRequestData: {
    online_time: [formatDate],
  },
})

export const apiDelNetDisk = (data: {
  ids: number[]
  operate_type: 1 | number // 1 清理
}) => httpClient.post<ApiResponse<null>>('/series_resource_v2/netdisk_deal', data)

export const apiTranslateSeriesInfo = (data: {
  title?: string
  description?: string
  series_resource_id: number
  target_lang_list: string[]
}) =>
  httpClient.post<ApiResponse<{
    list: M.IEpisodeSeriesInfo[]
  }>>('/series_resource_v2/series_info_trans', data, {
    timeout: 60 * 1000,
  })

export const apiFixSubtitle = (data: {
  series_resource_id: number
  serial_numbers: number[]
  lang_list: string[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/subtitle_fix', data)

export const apiSaveLabel = (data: {
  id: number
  root_label_ids: number[]
  first_label_ids: number[]
  second_label_ids: number[]
}) => {
  return httpClient.post<ApiResponse<null>>('/series_resource_v2/label_save', data, {
    transformRequestData: {
      root_label_ids: [arrToString],
      first_label_ids: [arrToString],
      second_label_ids: [formatLabelIds],
    },
  })
}

export const apiSaveCheckInfo = (data: M.IPassForm) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/check_info_save', data, {
    transformRequestData: {
      root_label_ids: [arrToString],
      first_label_ids: [arrToString],
      second_label_ids: [formatLabelIds],
    },
  })

export const apiGetCheckInfo = (data: {
  id: number
}) =>
  httpClient.post<ApiResponse<M.IPassForm>>('/series_resource_v2/check_info_detail', data, {
    transformResponseData: {
      'data.first_label_ids': [splitIt([',']), filterIt(Boolean), toNumArr],
      'data.root_label_ids': [splitIt([',']), filterIt(Boolean), toNumArr],
      'data.second_label_ids': [splitIt([',']), filterIt(Boolean), toNumArr],
    },
  })

export const apiGetSeriesInfoV2 = (data: { series_resource_id: number }) =>
  httpClient.post<ApiResponse<{
    list: M.IEpisodeSeriesInfo[]
  }>>('/series_resource_v2/series_info', data, {
    transformResponseData: {
      'data.list': [(list: M.IEpisodeSeriesInfo[]) => {
        return list?.map(item => {
          item.color_style = item.color_style === 0 ? undefined : item.color_style
          return item
        })
      }],
    },
  })

export const apiSubtitleSync = (data: {
  id: number
  serial_number: number
  language_code: string
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/subtitle_sync', data)

export const apiUploadInfoExcel = (data: {
  file: File
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/series_info_batch_import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })

export const apiGetSubtitleErrors = (data: {
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<{
    list: M.IResourceSubtitleError[]
  }>>('/series_resource_v2/trans_err_list', data)

export const apiGetSubtitleErrorsDetail = (data: {
  series_resource_id: number // 资源id
  serial_number: number // 集号
  language_code: string // 语种
}) =>
  httpClient.post<ApiResponse<{
    errs: M.IResourceSubtitleDetailError[]
  }>>('/series_resource_v2/srt_errs', data)

export const apiGetLabelList = (data: {
  language_code: string
  series_resource_id?: number
  label_type?: 1 | 2 | 3
}) =>
  httpClient.post<ApiResponse<{
    list: M.ITag[]
  }>>('/series_resource_v2/label_list', data)

export const apiUpdateSeriesInfo = (data: { series_resource_id: number, list: M.IEpisodeSeriesInfo[] }) =>
  httpClient.post<ApiResponse<{
    list: M.IEpisodeSeriesInfo[]
  }>>('/series_resource_v2/series_update', data)

export const apiAnalysisNetUrl = (data: {
  netdisk_url: string
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<{
    list: M.ITag[]
  }>>('/series_resource_v2/baidu_netdisk_reload', data)

export const apiPublishPreview = (data: {
  id: number
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/series_preview_publish', data)

// 获取任务角色
export const apiGetTaskRole = (data: {
  type: M.ITaskType
}) =>
  httpClient.post<ApiResponse<{
    role: 0 | 1// 0: 组员 1：组长
    leaders: {
      name: string
    }[]
    members: {
      name: string
    }[]
  }>>('/series_resource_v2/role', data)

// 领取任务
export const apiGetTask = (data: {
  type: M.ITaskType
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/task_claim', data)

// 审核新增
export const apiChangeAudit = (data: M.IAuditParams) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/check', data)

// 字幕审核
export const apiCommitSubtitleCheckLog = (data: M.ISubtitleCheck) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/resource_ope_record', data)

// 回收操作
export const apiUpdateResourceRecycleStatus = (data: {
  series_resource_id_list: number[]
  recycle_status: 4 | 5 | 1 // 0219新增 回收状态 4：暂缓 5：退剧 1: 取消暂缓
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_exec/recycle', data)

// 获取字幕修改建议
export const apiGetSubtitleFixes = (data: {
  series_resource_id: number // 资源id
  serial_number: number // 集号
  language_code: string // 语种
  srt_content: string // 字幕内容
}) =>
  httpClient.post<ApiResponse<{
    errs: M.IResourceSubtitleDetailError[]
  }>>('/series_resource_v2/ai_srt_fixes', data)

// 5. 演员关联
export const apiUpdateActorAssociate = (data: {
  actors: M.INewActor[]
  series_resource_id: number
}) => httpClient.post<ApiResponse<null>>('/actor/associate', data)

export const apiInjectSubtitle = (data: M.IInjectSubtitleParams) => httpClient.post<ApiResponse<{
  processing_code: number
  success: boolean
}>>('/series_resource/subtitle_inject_manual', data)

// 提取术语
export const apiExtractTerminology = (data: {
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/term_extract', data)

// 获取术语
export const apiGetTerminologyList = (data: {
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<{
    term_list: M.ITerminology[]
    src_lang: string
  }>>('/series_resource_v2/term_base_list', data)

// 获翻译取术语
export const apiGetTransTerminologyList = (data: {
  series_resource_id: number
  language_code: string
  serial_number?: number
}) =>
  httpClient.post<ApiResponse<{
    src_lang: string // 语种
    tar_lang: string
    term_list: M.ITerminology[]
    origin_syllable_list: Api.VoiceoverResource.ISyllable[]
    shorten_syllable_list: Api.VoiceoverResource.ISyllable[]
    replacing: 0 | 1 // 0 不替换 1 替换
  }>>('/series_resource_v2/term_trans_list', data, {
    transformResponseData: {
      'data.origin_syllable_list': [(value: unknown) => {
        if (!value) return []
        return value
      }],
      'data.shorten_syllable_list': [(value: unknown) => {
        if (!value) return []
        return value
      }],
    },
  })

// 修改源字幕术语
export const apiUpdateOriginalTerminology = (data: {
  series_resource_id: number
  terms: M.ITerminology[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/term_base_edit', data)
// src_type =1
export const apiUpdateRoleTerminology = (data: {
  series_resource_id: number
  terms: M.ITerminology[]
}) =>
  httpClient.post<ApiResponse<null>>('/aivoice/character_edit', data)

// 修改其他语言术语
export const apiUpdateOtherLanguageTerminology = (data: {
  series_resource_id: number
  terms: M.ITerminology[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/term_trans_edit', data)
export const apiUpdateMark = (data: {
  series_resource_id: number
  serial_number: number
  content: string
}) => httpClient.post<ApiResponse<null>>('/series_resource_v2/remark/add', data)

// 多语言字幕校验页通过语言查询组员名字
export const apiGetNameByLanguage = (data: {
  lang_code: string
}) =>
  httpClient.post<ApiResponse<{
    names: string[]
  }>>('/series_resource_v2/get_name_by_lang', data)

export const apiDistributeTask = (data: {
  distribution_infos: M.IDistributeTask[]
  type: 1 | 2 | 3 | 4 | number // 任务类型，1：资源验收 2：多语言信息校验 3：多语言字幕校验 4：终审发布
}) =>
  httpClient.post<ApiResponse<{
    names: string[]
  }>>('/series_resource_v2/task_distribute', data)

// 如果为空，则所有语言的字幕都可以改，如果不为空，只能改对应语言的字幕
export const apiGetSubtitleEditable = () =>
  httpClient.post<ApiResponse<{
    lang_code: string
  }>>('/series_resource_v2/can_edit_subtitle_lang')

export const apiDelSingleResource = (data: {
  series_resource_id: number
  serial_number: number
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/episode_del', data)

export const apiSubtitleSyncVoice = (data: {
  series_resource_id: number
  serial_number_list: number[]
  language_code_list: string[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/subtitle_sync_voice', data)

export const apiTransTerms = (data: {
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/term_trans_all', data)

export const apiTransLanguageTerms = (data: {
  series_resource_id: number
  langs: string[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/term_trans', data)

// 获取资源术语各语言翻译状态
export const apiGetTermsStatusList = (data: {
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<{
    list: M.ITermStatus[]
  }>>('/series_resource_v2/term_status', data)
