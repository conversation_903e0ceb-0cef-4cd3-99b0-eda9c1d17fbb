import { mergeI18n } from 'src/h5_modules/i18n/i18n'

export const T = mergeI18n({
  'en-US': {
    seriesItemPage: {
      dontMissOut: 'The stories in between are also very interesting. Don\'t miss out!',
      follow: 'Follow',
      following: 'Following',
      removeFromList: 'Remove from ‘My List’',
      addedToList: 'Added to ‘My List’',
      allEpisodes: 'All {N} Episodes',
      vipStr: 'VIP',
    },
  },
  'zh-CN': {
    seriesItemPage: {
      dontMissOut: '中间的故事也很有趣。不要错过！',
      follow: '关注',
      following: '关注',
      removeFromList: '从收藏中删除',
      addedToList: '添加到我的收藏',
      allEpisodes: '全 {N} 集',
      vipStr: '会员',
    },
  },
  'ja-JP': {
    seriesItemPage: {
      dontMissOut: '途中のストーリーもとても面白いので、是非お見逃しなく！',
      follow: 'フォロー',
      following: 'フォロー中',
      removeFromList: '「マイリスト」から削除',
      addedToList: '「マイリスト」に追加されました',
      allEpisodes: '全 {N} エピソード',
      vipStr: 'VIP',
    },
  },
  'ko-KR': {
    seriesItemPage: {
      dontMissOut: '그 중간의 이야기도 매우 흥미롭습니다. 놓치지 마세요!',
      follow: '팔로우하기',
      following: '팔로잉',
      removeFromList: '\'내 목록\'에서 삭제',
      addedToList: '\'내 목록\'에 추가됨',
      allEpisodes: '모든 {N} 에피소드',
      vipStr: 'VIP',
    },
  },
  'es-ES': {
    seriesItemPage: {
      dontMissOut: 'Las historias de en medio son muy interesantes también ¡No te las pierdas!',
      follow: 'Seguir',
      following: 'Siguiendo',
      removeFromList: 'Eliminar de "Mi Lista',
      addedToList: 'Añadido a "Mi Lista',
      allEpisodes: 'Todos {N} los Episodios',
      vipStr: 'VIP',
    },
  },
  'pt-PT': {
    seriesItemPage: {
      dontMissOut: 'As histórias intermediárias também son muito \ninteressantes. Não perca!',
      follow: 'Seguir',
      following: 'Seguindo',
      removeFromList: 'Excluir de "Minha Lista',
      addedToList: 'Adicionado a "Minha Lista',
      allEpisodes: 'Todos {N} os Episódios',
      vipStr: 'VIP',
    },
  },
  'vi-VN': {
    seriesItemPage: {
      dontMissOut: 'Đoạn giữa câu chuyện cũng rất thú vị. Đừng bỏ lỡ!',
      follow: 'Theo dõi',
      following: 'Đang theo dõi',
      removeFromList: 'Xóa khỏi ‘Danh sách của tôi’',
      addedToList: 'Đã thêm vào ‘Danh sách của tôi’',
      allEpisodes: 'Tất cả {N} Tập',
      vipStr: 'VIP',
    },
  },
  'th-TH': {
    seriesItemPage: {
      dontMissOut: 'เรื่องราวในระหว่างนั้นก็น่าสนใจมาก อย่าพลาด!',
      follow: 'ติดตาม',
      following: 'กำลังติดตาม',
      removeFromList: 'ลบออกจาก ‘รายการของฉัน’',
      addedToList: 'เพิ่มไปยัง ‘รายการของฉัน’',
      allEpisodes: 'ทั้งหมด {N} ตอน',
      vipStr: 'วีไอพี',
    },
  },
  'id-ID': {
    seriesItemPage: {
      dontMissOut: 'Cerita - cerita diantaranya juga sangat menarik. jangan sampai ketinggalan!',
      follow: 'Ikuti',
      following: 'Mengikuti',
      removeFromList: 'Menghapus dari \'Daftar Saya\'',
      addedToList: 'Menambahkan ke \'Daftar Saya\'',
      allEpisodes: 'Semua {N} episode',
      vipStr: 'VIP',
    },
  },
  'tl-PH': {
    seriesItemPage: {
      dontMissOut: 'Ang mga kwento sa pagitan ay napaka-interesante din. Huwag palampasin!',
      follow: 'Sumunod',
      following: 'Sumusunod',
      removeFromList: 'Alisin sa \'Aking Listahan\'',
      addedToList: 'Idinagdag sa \'Aking Listahan\'',
      allEpisodes: 'Lahat ng {N} Episode',
      vipStr: 'VIP',
    },
  },
  'fr-FR': {
    seriesItemPage: {
      dontMissOut: 'Les histoires intermédiaires sont également très intéressantes. Ne les manquez pas!',
      follow: 'Suivre',
      following: 'Suivi',
      removeFromList: 'Retirer de \'Ma Liste\'',
      addedToList: 'Ajouté à \'Ma Liste\'',
      allEpisodes: 'Tous les Épisodes {N}',
      vipStr: 'VIP',
    },
  },
  'de-DE': {
    seriesItemPage: {
      dontMissOut: 'Auch die Geschichten dazwischen sind sehr interessant. Nicht verpassen!',
      follow: 'Folgen',
      following: 'Gefolgt',
      removeFromList: 'Aus \'Meine Liste\' entfernen',
      addedToList: 'Zu \'Meine Liste\' hinzugefügt',
      allEpisodes: 'Alle {N} Folgen',
      vipStr: 'VIP',
    },
  },
  'it-IT': {
    seriesItemPage: {
      dontMissOut: 'Le storie intermedie sono anche molto interessanti. Non perdertele!',
      follow: 'Segui',
      following: 'Seguiti',
      removeFromList: 'Rimuovi da "La Mia Lista',
      addedToList: 'Aggiunto a "La Mia Lista',
      allEpisodes: 'Tutti gli Episodi {N}',
      vipStr: 'VIP',
    },
  },
  'ru-RU': {
    seriesItemPage: {
      dontMissOut: 'Истории между ними тоже очень интересны. Не пропустите!',
      follow: 'Подписаться',
      following: 'Подписки',
      removeFromList: 'Удалить из "Моего Списка',
      addedToList: 'Добавить в "Мой Список',
      allEpisodes: 'Все {N} Эпизоды',
      vipStr: 'VIP',
    },
  },
  'tr-TR': {
    seriesItemPage: {
      dontMissOut: 'Aradaki hikayeler de çok ilginç. Sakın kaçırmayın!',
      follow: 'Takip et',
      following: 'Takip ediyor',
      removeFromList: '\'Listem\'den Kaldır',
      addedToList: '\'Listem\'e eklendi',
      allEpisodes: 'Tümü {N} Bölümler',
      vipStr: 'VIP',
    },
  },
  'ms-MY': {
    seriesItemPage: {
      dontMissOut: 'Cerita di antaranya juga sangat menarik. Jangan lepaskan peluang!',
      follow: 'Ikut',
      following: 'Mengikuti',
      removeFromList: 'Alih keluar daripada \'Senarai Saya\'',
      addedToList: 'Ditambah pada Senarai Saya',
      allEpisodes: 'Semua {N} Episod',
      vipStr: 'VIP',
    },
  },
  'zh-TW': {
    seriesItemPage: {
      dontMissOut: '中間的故事也很有趣。不要錯過！',
      follow: '關注',
      following: '關注',
      removeFromList: '從收藏中刪除',
      addedToList: '添加到我的收藏',
      allEpisodes: '全集{N}',
      vipStr: '會員',
    },
  },
})
