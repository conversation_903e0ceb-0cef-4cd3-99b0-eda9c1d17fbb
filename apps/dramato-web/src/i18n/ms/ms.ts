export const ms = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: 'Popular',
    },
  },
  myList: {
    watchHistory: `<PERSON><PERSON><PERSON>`,
    myList: '<PERSON><PERSON><PERSON>',
    seeAll: '<PERSON><PERSON>',
    noData: 'Tiada hasil yang berkaitan',
  },
  // 分享页
  share: {
    watchNow: 'Tonton Sekarang',
    episodeDialog: {
      title: 'Sertai saya dan dapatkan syiling tambahan!',
      description: 'Saya terpikat oleh drama ini!',
      tips: 'Syor',
      button: 'Tonton Drama Penuh',
      limit24: 'masa terhad: 24j',
    },
    toast: {
      takeOffShelf: 'Tiada Kandungan Tersedia',
    },
  },
  'customer-service-center': {
    title: '<PERSON><PERSON>t Khidmat <PERSON>',
    description: 'Penerangan',
    'description-placeholder': 'Berikan butiran untuk membantu kami memahami masalah tersebut',
    'email-placeholder': 'Masukkan e-mel anda untuk menerima maklum balas terkini',
    'submit-feedback': 'Hantar',
    'enter-feedback': 'Maklum balas isu lain',
    'upload-pictures': 'Muat naik gambar',
    feedback: 'Maklum balas',
    'problem-type': 'Jenis masalah',
    'problem-type-placeholder': 'Sila pilih',
    'payment-issue-placeholder': 'Sila berikan ID dan resit pesanan anda:Google: https://pay.google.com/ (nombor pesanan bermula dengan GPA...) iOS: App store - Apple ID - sejarah pembelian (Order ID)',
    'content-issue-placeholder': 'Sila berikan tajuk drama, nombor episod dan bahasa sari kata yang tiada',
    'ads-issue-placeholder': 'Sila berikan tetapan model peranti, negara dan bahasa anda',
    'technical-issue-placeholder': 'Sila berikan tetapan model peranti, negara dan bahasa anda',
    'other-issue-placeholder': 'Sila berikan butiran lanjut, termasuk model dan negara peranti anda',
    'i-want-to-give-feedback': 'Balas maklum balas',
    'are-you-satisfied-with-this-reply': 'Adakah anda berpuas hati dengan balasan ini?',
    unsatisfied: 'Tidak berpuas hati',
    satisfied: 'Berpuas hati',
    myFeedback: 'Maklum balas saya',
    'reply-to-feedback': 'Balas maklum balas',
    'description-min-length': 'Masukkan huraian masalah (sekurang-kurangnya 6 aksara).',
    'problem-type-not-choose': 'Sila pilih jenis maklum balas.',
    'description-empty': 'Input tidak boleh kosong.',
  },
  terminate: {
    question: 'Bagaimanakah saya mengurus langganan saya?',
    desc: 'Klik butang di bawah untuk masuk ke halaman pengurusan kontrak. Anda boleh mengurus langganan anda dengan mengklik suis kontrak:',
    title: 'Pengurusan Kontrak',
    expiration: 'Masa Tamat Tempoh',
    manageDesc: 'Hubungan Kontrak',
  },
  regulations: {
    howUseData: 'Cara Kami Menggunakan Data Anda',
    content: `Jika anda memilih "Benarkan" pada permintaan kebenaran Ketelusan Penjejakan Apl, kami akan menggunakan maklumat dan data anda untuk mengoptimumkan perkhidmatan kami dan memberikan anda pengalaman terbaik, jika tidak, kami hanya akan mengumpul data dan tidak akan menjalankan sebarang aktiviti penjejakan .

Jika anda bersetuju untuk membenarkan DramaWave dan rakan kongsi serta pembekal kami menggunakan data daripada peranti anda untuk menganalisis cara anda menggunakan aplikasi, memperibadikan pengesyoran kandungan dan menyediakan lebih banyak perkhidmatan berkaitan maklumat, sila klik "Setuju dan Teruskan" untuk meneruskan.
  `,
    acceptAll: 'Terima Semua',
    dataSettings: 'Tetapan Data',
    next: 'Seterusnya',
    cancel: 'Batal',
    aboutYourPrivacy: 'Mengenai Privasi Anda',
    personalDataName: 'Data Diperibadikan',
    personalDataDescription: 'Kami menggunakan maklumat ini untuk mengingati pilihan anda dan untuk memberikan pengiklanan yang diperibadikan dan pengesyoran kandungan, pemberitahuan dan peringatan, dsb.',
    advertisementDataName: 'Data Iklan',
    advertisementDataDescription: 'Kuki, pengecam peranti anda atau maklumat lain mungkin disimpan atau diakses pada peranti anda untuk tujuan pengiklanan dalam talian. Data pengguna juga boleh dikongsi dengan rakan kongsi pengiklanan kami.',
    behaviorsDataName: 'Data Tingkah Laku',
    behaviorsDataDescription: 'Data yang berkaitan dengan analitis, seperti kuki atau pengecam peranti, boleh disimpan untuk tujuan mengukur dan menganalisis penggunaan, tingkah laku dan prestasi pengiklanan.',
    privacyAndTerms: 'Dasar Privasi dan Syarat Perkhidmatan',
    referToPrivacy: 'Sila rujuk kepada Dasar Privasi',
    referToTerms: 'Sila rujuk kepada Syarat Perkhidmatan',
    agreeAndSave: 'Setuju & Simpan',
  },
  coins: {
    earnings: {
      title: 'Pendapatan Saya',
      total: 'Jumlah Berlian',
      exchange: 'Pertukaran',
      earningsHistory: 'Sejarah Pendapatan',
      exchangeHistory: 'Sejarah Pertukaran',
      noRecords: 'Tiada rekod',
      last50earnings: 'Hanya 50 pendapatan terakhir dipaparkan',
      last50exchanges: 'Hanya 50 rekod pertukaran terakhir dipaparkan',
      benefits: 'Faedah',
      gotIt: 'faham',
    },
    exchange: {
      title: 'Zon Pertukaran',
      notOpen: 'Belum dibuka, sila tunggu',
      receiveBtn: 'Terima dengan Gembira',
      earnCoins: 'Dapatkan Berlian',
      redeemRequiredXCoins: 'Penebusan memerlukan {X} berlian.',
      exchangeSuccess: 'Ditebus Berjaya',
      cardId: 'ID Kad',
      exchangeFailed: 'Penebusan Gagal',
      transferFailedInsufficientStock: 'Kehabisan stok. Sila datang awal esok.',
      gotItBtn: 'Faham',
      transferFailedRiskControl: 'Akaun tidak biasa, penebusan tidak dapat dilakukan buat sementara waktu.',
      tryAgainBtn: 'Cuba Lagi Nanti',
      transferConfirm: 'Pengesahan Pemindahan',
      paypalAccountEmail: 'E-mel Akaun PayPal',
      confirmPaypalAccountEmail: 'Sahkan e-mel Akaun PayPal',
      transferX: 'Pemindahan {X}',
      transferXConfirm: 'Sahkan Pemindahan {X}',
      transferToYourAccountX: 'Dipindahkan ke akaun anda {X}',
      confirmBtn: 'Sahkan',
      redeemFailed: 'Penebusan gagal',
      redeemFailedContent: 'Terdapat ralat. Sila cuba lagi nanti.',
      transferSuccessTitle: 'Kami sedang menghantar wang! ',
      transferSuccessContent: 'Wang anda akan tiba dalam masa 5 minit hingga 72 jam.',
      copySuccess: 'Salinan Berjaya',
      enterEmailError: 'Sila masukkan alamat emel yang sah',
      enterEmailValidError: 'Ralat input, sila masukkan semula',
      emailNotMatchError: 'Alamat emel tidak sepadan, sila masukkan semula',
    },
  },
  meal_check_in: {
    sleeping_link: 'Dapatkan Berlian Semasa Tidur',
    rules_title: 'Peraturan Aktiviti',
    benefits_title: 'Benefits',
    rules_1: '1. Setiap hari, terdapat empat waktu untuk anda mendaftar makanan anda: Sarapan: 07:00-10:00; Makan Tengah Hari: 11:00-14:00; Makan Malam: 17:00-20:00; Snek Tengah Malam: 21:00-24:00.',
    rules_2: '2. Jika anda terlepas masa pendaftaran, anda boleh menonton video untuk menuntut ganjaran.',
    rules_3: '3. Ganjaran direset setiap hari pada tengah malam, jadi jangan lupa untuk menuntut ganjaran anda.',
  },
  sleep_check_in: {
    earn_link: 'Catat Makanan Untuk Dapatkan',
    benefits_title: 'Benefits',
    rules_1: '1. Setiap malam dari pukul 7 malam hingga 12 malam, anda boleh mengaktifkan mod \'Tidur untuk Menjana\'.',
    rules_2: '2. Selepas mengaktifkan, pada keesokan harinya, selepas tidur selama 8 jam, anda boleh menuntut ganjaran tidur antara pukul 8 pagi hingga 12 tengah hari.',
    rules_3: '3. Jika anda terlepas masa untuk menuntut ganjaran, anda boleh menonton video antara pukul 12 tengah hari hingga 7 malam pada keesokan harinya untuk menuntut ganjaran.',
  },
  bind_email: {
    title: 'Masukkan alamat emel anda',
    list_1: 'Mendapatkan Syiling Ganjaran Eksklusif dengan Mendaftar Alamat E-mel',
    list_2: 'Dapatkan Notifikasi Mengenai Acara dan Tawaran Istimewa Akan Datang',
    list_3: 'Ketahui episod baru terlebih dahulu',
    email_placeholder: 'Sila masukkan alamat e-mel anda',
    verify: 'Sahkan',
    email_error: 'Sila masukkan alamat emel yang sah',
    verify_progress: 'Pengesahan emel sedang dijalankan',
    verify_success: 'Pengambilan berjaya!',
    re_enter: 'Sila masukkan semula',
    email_repeat: 'Emel ini telah menyertai aktiviti tersebut!',
  },
  toast: {
    'upload-failed': 'Muat naik gagal, sila muat naik semula',
    'submit-success': 'Hantar berjaya',
    'email-incorrect': 'Format e-mel tidak betul',
  },
  not_found: {
    title: 'Halaman yang anda cari tidak wujud',
  },
  infinityScroll: {
    end: 'Akhirnya',
    loading: 'Memuatkan...',
  },
}
