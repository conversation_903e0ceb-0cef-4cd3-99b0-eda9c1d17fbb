import { between, createComponent, fn, required, round } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { useSwipe } from '@vueuse/core'
import { computed, onMounted, ref, Ref } from 'vue'

type PlayerProgressOptions = {
  props: {
    currentTime: number
    totalTime: number
    video: Ref<HTMLVideoElement | undefined | null>
    loading: boolean
  }
  emits: {
    seek: (from: number, to: number) => void
    seekStart: (from: number) => void
  }
}

export const PlayerProgress = createComponent<PlayerProgressOptions>({
  props: {
    currentTime: 0,
    totalTime: 0,
    video: required,
    loading: false,
  },
  emits: {
    seek: fn,
    seekStart: fn,
  },
}, (props, { emit }) => {
  const progressBarRef = ref<HTMLDivElement>()
  const outer = ref<HTMLDivElement>()
  const outerRect = ref<{ width: number, height: number }>({
    width: 0,
    height: 0,
  })

  onMounted(() => {
    if (outer.value) {
      outerRect.value = outer.value.getBoundingClientRect()
    }
  })
  const isDraggingTime = ref(false)
  // 计算进度百分比
  const progressPercentage = computed(() => {
    if (props.totalTime <= 0) return 0
    // 为什么要用 isDraggingTime.value 而不用 isSwiping.value 来判断？
    // 因为 isSwiping.value 变成 false 变得太快了，导致进度条闪烁
    const time = isDraggingTime.value ? targetTime.value : props.currentTime
    return (between(time / props.totalTime, 0, 1)) * 100
  })

  // 格式化时间为 mm:ss 格式
  const formatTime = (seconds: number) => {
    if (isNaN(seconds) || !isFinite(seconds)) return '00:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const targetTime = ref(0)
  const onTouchStart = (event: TouchEvent) => {
    if (!progressBarRef.value) return
    const touch = event.touches[0]
    const rect = progressBarRef.value.getBoundingClientRect()
    const clickX = touch.clientX - rect.left
    const percentage = between(clickX / rect.width, 0, 1)
    targetTime.value = percentage * props.totalTime
  }
  const { isSwiping, direction } = useSwipe(progressBarRef, {
    threshold: 1,
    onSwipeStart: () => {
      isDraggingTime.value = true
      targetTime.value = props.currentTime
      emit('seekStart', targetTime.value)
    },
    onSwipe: onTouchStart,
    onSwipeEnd: () => {
      setTimeout(() => {
        isDraggingTime.value = false
      }, 100)
    },
  })
  const onTouchEnd = (event: TouchEvent) => {
    if (!props.video.value) return
    if (!progressBarRef.value) return
    const touch = event.touches[0] || event.changedTouches[0]
    const rect = progressBarRef.value.getBoundingClientRect()
    const clickX = touch.clientX - rect.left
    const percentage = between(clickX / rect.width, 0, 1)
    targetTime.value = percentage * props.totalTime
    const currentTime = props.currentTime
    props.video.value.currentTime = targetTime.value
    emit('seek', currentTime, targetTime.value)
    setTimeout(() => {
      isDraggingTime.value = false
    }, 100)
  }

  return () => (
    <MergeClass tag="x-player-progress" baseClass="translate-y-1/2">
      <div ref={progressBarRef} class="absolute left-0 top-1/2 flex size-full -translate-y-1/2 items-center justify-center px-0" onTouchend={onTouchEnd}>
        {isDraggingTime.value && (
          <x-status class="absolute bottom-full left-1/2 flex -translate-x-1/2 items-center justify-center gap-2">
            <time>{formatTime(targetTime.value)}</time>
            <span>/</span>
            <time class="opacity-50">{formatTime(props.totalTime)}</time>
          </x-status>
        )}
        <x-outer ref={outer} class={[
          'relative h-0.5 flex-1 rounded-xl bg-[#FDFBFC33]',
          props.loading && 'loading-animation',
          isDraggingTime.value && 'h-2',
        ]}>
          <x-inner class="bg-fill-4 absolute left-0 top-0 h-full rounded-full " style={{ width: `${progressPercentage.value}%` }} />
          <x-inner-circle
            class={['bg-fill-4 absolute left-0 top-1/2 -ml-1 -mt-1 size-2 rounded-full ', props.loading && 'hidden',
              isDraggingTime.value && '-ml-2 -mt-2 h-4 w-3',
            ]}
            style={{ transform: `translateX(${progressPercentage.value / 100 * (outerRect.value.width || 0)}px)` }}
          />
        </x-outer>
      </div>
    </MergeClass>
  )
})

export default PlayerProgress
