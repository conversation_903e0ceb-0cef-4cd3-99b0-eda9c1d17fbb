import { createComponent } from '@skynet/shared'
import rank1 from '../image/rank1.png'
import rank2 from '../image/rank2.png'
import rank3 from '../image/rank3.png'
import normal from '../image/normal.png'
import free from '../image/free.png'
type BillboardOptions = {
  props: {
    title: string
    list: M.DramaItem[]
    show_title?: boolean
  }
}

export const BillboardLayout = createComponent<BillboardOptions>({
  props: {
    title: '',
    list: [],
    show_title: true,
  },
}, props => {
  const ranks = [rank1, rank2, rank3, normal, normal, normal, normal, normal, normal]
  const dramaItem = (drama: M.DramaItem, index: number) => {
    return (
      <div class="flex flex-row gap-x-3 h-[200px]">
        <div class="rounded-md w-[140px] relative shrink-0">
          {drama.free && <img src={free} class="absolute right-1 top-1 w-40px h-auto object-cover shadow-sm" />}
          <img src={ranks[index]} class="absolute top-[-1px] left-[-1px] rounded-tl-md" />
          <img src={drama.cover} class="object-cover rounded-md w-full h-full" />
          {index > 2 && <div class="absolute top-0 left-1 text-white text-sm font-bold">{index + 1}</div>}
        </div>
        <div class="flex flex-col gap-y-1 overflow-hidden">
          <div class="text-[#cccacb] text-md font-['SF Pro'] my-2 line-clamp-2">{drama.title}</div>
          <div class="text-[#797b7d] text-sm font-normal font-['SF Pro'] line-clamp-3">{drama.desc}</div>
          <div class="flex flex-wrap gap-1 flex-row mt-2">
            {
              drama.series_tag.map(tag => <div class="h-[18px] px-2 py-0.5 bg-[#242526] rounded justify-center items-center gap-2.5 inline-flex text-[#6e7071] text-xs font-normal">{tag}</div>)
            }
          </div>
        </div>
      </div>
    )
  }
  return () => (
    props.list && (
      <x-billboard class="w-full flex flex-col gap-2">
        {props?.show_title && <x-billboard-title class="w-full h-[50px] pt-4 pb-3 text-[#fdfbfc] text-lg font-bold font-['SF Pro'] leading-snug">{props.title}</x-billboard-title>}
        <x-billboard-content class="w-full overflow-x-auto">
          <x-billboard-content-scroll class={`${props.list.length === 6 ? 'w-[180%]' : 'w-[250%]'} h-[630px] flex flex-col flex-wrap gap-2`}>
            {
              props.list.map((i, index) => {
                return (
                  <x-billboard-item class={`shrink-0 ${props.list.length === 6 ? 'w-[40%]' : 'w-[30%]'}`}>
                    {dramaItem(i, index)}
                  </x-billboard-item>
                )
              })
            }
          </x-billboard-content-scroll>
        </x-billboard-content>
      </x-billboard>
    )
  )
})
