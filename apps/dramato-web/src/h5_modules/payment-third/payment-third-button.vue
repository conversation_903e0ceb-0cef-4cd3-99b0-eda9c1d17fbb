<template>
  <div class="flex h-full flex-col items-center px-4">
    <div v-if="aliwallexPayInfo" class="mt-7">
      <div class="mb-2 text-center text-sm text-text-3 ">
        Coins
      </div>
      <div class="text-center text-2xl font-bold text-[#434546]">
        {{ aliwallexPayInfo?.currency }} {{
          aliwallexPayInfo?.amount
        }}
      </div>
      <div v-if="isError" class="mt-5 flex rounded-lg bg-[#FFF0F3] p-3">
        <SvgIcon name="payment/ic-fail-fill" class="mr-1 size-4" />
        <span class="text-xs font-medium text-brand-6">{{ T.paymentPage.payNotSupported.ref }}</span>
      </div>
      <div id="applePayButton" class="mt-6" />
      <div id="googlePayButton" class="mt-6" />
      <div class="mt-3 text-center text-xs font-medium text-text-3">
        {{ T.paymentPage.continueToUse.ref }}
        <a class="text-blue-6" @click="$emit('clickRule', 'terms')">{{ T.paymentPage.termsOfUse.ref }}</a> &
        <a class="text-blue-6" @click="$emit('clickRule', 'privacy')">{{ T.paymentPage.privacyPolicy.ref }}</a>
      </div>
    </div>
    <SvgIcon v-else name="payment/loading" class="mt-16 size-8 animate-spin " />
  </div>
</template>

<script setup lang="ts">
import { SvgIcon } from '@skynet/ui';
import { init, createElement, Payment } from '@airwallex/components-sdk';
import { useRoute, useRouter } from 'vue-router';
import { onMounted, ref } from 'vue';
import { showToast } from 'vant';
import { usePayment } from './use-payment';
import { T } from './payment-page.i18n';

const props = defineProps<{
  type: string, // Apple Store or Google Play or recurring
  productId: number,
  payChannel: string,
  paySubChannel: string,
  from: string,
  videoType?: string,
  currency?: string,
}>()

const emits = defineEmits<{
  (e: 'clickRule', type: 'terms' | 'privacy'): 'terms' | 'privacy';
}>();

const { aliwallexPayInfo, getAliwallexPayInfo, getAliwallexOrderStatus } = usePayment()
const route = useRoute();
const router = useRouter();
const isError = ref(false)

onMounted(async () => {
  await getAliwallexPayInfo(props.productId, {
    from: props.from,
    payChannel: props.payChannel,
    paySubChannel: props.paySubChannel,
    series_key: route.params.series_id as string,
    episode_key: route.params.episode_id as string,
    video_type: props.videoType,
    currency: props.currency,
  });
  if (props.type === 'Google Play') {
    initGooglePay();
  } else if (props.type === 'AirWallex') {
    initAirWallex();
  } else {
    // 判断是safari浏览器
    // const isSafari = /^((?!chrome|android).)*version\/.*safari/i.test(navigator.userAgent.toLowerCase());
    const isIOS = /iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase());

    if (!isIOS) {
      isError.value = true;
      return;
    }
    initApplePay();
  }
})

const checkOrderStatus = async (id: string, url: string) => {
  try {
    const res = await getAliwallexOrderStatus(id);
    if (res && res.success) return location.href = url;
    // else if (res && res.success === false) {
    //   showToast('支付失败')
    //   router.push('/payment' + route.query.from)
    // }
    setTimeout(() => {
      checkOrderStatus(id, url);
    }, 1000);
  } catch (error) {
    // TODO
    showToast(T.paymentPage.purchaseFailed.string)
  }

}

const initAirWallex = async () => {
  // STEP #1: Initialize Airwallex SDK
  const { payments } = await init({
    env: aliwallexPayInfo.value?.env || 'demo', // Choose the Airwallex environment ( 'demo', or 'prod')
    enabledElements: ['payments'],
  });

  payments?.redirectToCheckout({
    env: (aliwallexPayInfo.value?.env || 'demo') as Payment.AirwallexEnv, // Which env( 'demo' | 'prod') you would like to integrate with
    mode: aliwallexPayInfo.value?.mode as Payment.Mode,
    intent_id: aliwallexPayInfo.value?.intent_id,
    client_secret: aliwallexPayInfo.value?.client_secret as string,
    currency: aliwallexPayInfo.value?.currency || 'USD',
    country_code: aliwallexPayInfo.value?.country_code || '',
    customer_id: aliwallexPayInfo.value?.customer_id,
    successUrl: aliwallexPayInfo.value?.success_url,
    failUrl: aliwallexPayInfo.value?.fail_url,
    autoCapture: true,
    components: ['card'],
    recurringOptions: {
      next_triggered_by: 'merchant',
      merchant_trigger_reason: 'scheduled',
      currency: aliwallexPayInfo.value?.currency || 'USD',
    }
  });
}

const initApplePay = async () => {
  // STEP #1: Initialize Airwallex SDK
  init({
    env: aliwallexPayInfo.value?.env || 'demo', // Choose the Airwallex environment ( 'demo', or 'prod')
    enabledElements: ['payments'],
  });
  // STEP #2: Create Apple Pay element
  const element = await createElement('applePayButton', {
    // Required, fullFeaturedCard use intent Id and client_secret to prepare checkout
    mode: aliwallexPayInfo.value?.mode as Payment.Mode,
    customer_id: aliwallexPayInfo.value?.customer_id,
    intent_id: aliwallexPayInfo.value?.intent_id, // Replace with your intent ID
    client_secret: aliwallexPayInfo.value?.client_secret, // Replace with your client secret
    amount: {
      value: Number(aliwallexPayInfo.value?.amount) || 0,// 'replace-with-your-intent-amount',
      currency: aliwallexPayInfo.value?.currency || 'CNY',//'replace-with-your-intent-currency',
    },
    countryCode: aliwallexPayInfo.value?.country_code || '', // merchant country code
  });
  const domElement = element.mount('applePayButton');

  // STEP #6: Handle ready event
  // @ts-expect-error never mind
  element.on('ready', (event) => {
    console.log(event.detail);
  });

  // STEP #7: Handle success event
  // @ts-expect-error never mind
  element.on('success', (event) => {
    console.log(JSON.stringify(event.detail));
    checkOrderStatus(aliwallexPayInfo.value?.order_id || '', event.detail.intent.return_url + `&order_id=${aliwallexPayInfo.value?.order_id}`)
  });

  // STEP #8: Handle error event
  // @ts-expect-error never mind
  element.on('error', (event) => {
    console.log(event.detail);
    isError.value = true
  });
}

const initGooglePay = async () => {
  console.log(aliwallexPayInfo.value)
  // STEP #1: Initialize Airwallex SDK
  await init({
    env: aliwallexPayInfo.value?.env || 'demo', // Choose the Airwallex environment ( 'demo', or 'prod')
    enabledElements: ['payments'],
  });
  // STEP #2: Create Google Pay element
  const element = await createElement('googlePayButton', {
    intent_id: aliwallexPayInfo.value?.intent_id, // Replace with your intent ID
    client_secret: aliwallexPayInfo.value?.client_secret,
    amount: {
      value: Number(aliwallexPayInfo.value?.amount) || 0, //'replace-with-your-intent-amount',
      currency: aliwallexPayInfo.value?.currency || 'CNY',
    },
    autoCapture: true,
    merchantInfo: {
      merchantName: aliwallexPayInfo.value?.merchant_name,
    },
    countryCode: aliwallexPayInfo.value?.country_code || "", // merchant country code
    mode: aliwallexPayInfo.value?.mode as Payment.Mode,
    customer_id: aliwallexPayInfo.value?.customer_id,
  });
  // STEP #5: Mount 'googlePayButton' element
  const domElement = element.mount('googlePayButton');

  // STEP #6: Add an event listener to handle events when the element is mounted
  // @ts-expect-error never mind
  element.on('ready', (event) => {
    /*
      ... Handle event
    */
    console.log(event.detail);
  });

  // STEP #7: Add an event listener to handle events when the payment is successful.
  // @ts-expect-error never mind
  element.on('success', async (event) => {
    /*
      ... Handle event on success
    */
    console.log(JSON.stringify(event.detail));
    checkOrderStatus(aliwallexPayInfo.value?.order_id || '', event.detail.intent.return_url + `&order_id=${aliwallexPayInfo.value?.order_id}`)
  });

  // STEP #8: Add an event listener to handle events when the payment has failed.
  // @ts-expect-error never mind
  element.on('error', (event) => {
    /*
      ... Handle event on error
    */
    console.log(event.detail);
    isError.value = true
  });
}
</script>

<style scoped></style>