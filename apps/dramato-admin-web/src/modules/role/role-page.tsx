import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Button, openDialog } from '@skynet/ui'
import { ref, onMounted } from 'vue'
import { set } from 'lodash-es'
import { apiGetRoleList } from './role-api'
import { useRoleStore } from './use-role-store'
import { RoleEditDialog } from './role-edit-dialog'
type RolePageOptions = {
  props: {}
}

export const RolePage = createComponent<RolePageOptions>({
  props: {},
}, props => {
  const { setCurrentRole } = useRoleStore()
  const loading = ref(false)
  const list = ref<M.IRole[]>()
  const form = ref<M.IRoleQueryParams>({
    role_name: '',
  })
  const Table = CreateTableOld<M.IRole>()
  const QueryForm = CreateForm<M.IRoleQueryParams>()
  const columns: TableColumnOld<M.IRole>[] = [
    ['角色ID', 'role', { class: 'w-[100px]' }],
    ['角色名称', 'role_name', { class: 'w-[200px]' }],
    ['状态', row => {
      const statusStyle = row.status === 1 ? 'badge badge-primary' : 'badge default'
      return <div class={`${statusStyle}`}>{ row.status === 1 ? '正常' : '已注销'}</div>
    }, { class: 'w-[130px]' }],
    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap">
            <Button class="btn btn-sm btn-link btn-primary" onClick={() => {
              const createDialog = openDialog({
                title: '编辑角色',
                mainClass: 'pb-0 px-5',
                customClass: '!w-[800px]',
                body: () => <RoleEditDialog afterSave={afterSave} closeDialog={closeDialog} />,
              })

              function afterSave() {
                createDialog()
                void onQuery()
              }

              function closeDialog() {
                createDialog()
              }
              setCurrentRole(row)
            }}
            >
              编辑角色
            </Button>
          </div>
        )
      },
      { class: 'w-[100px]' },
    ],
  ]

  const getList = async () => {
    loading.value = true
    try {
      const { data } = await apiGetRoleList({
        ...form.value,
      })
      list.value = data?.list
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const onQuery = async () => {
    await getList()
  }

  const onReset = async () => {
    form.value = {
      role_name: '',
    }
    await onQuery()
  }

  onMounted(async () => {
    await getList()
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>角色管理</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              { label: '角色名称：', path: 'role_name', input: { type: 'text' } },
            ]}
          />
        ),
        // tableActions: () => (
        //   <x-table-actions class="w-full flex justify-end items-center">
        //     <x-table-actions-right>
        //       <Button class="btn btn-primary btn-sm" onClick={() => {
        //         const createDialog = openDialog({
        //           title: '新增角色',
        //           mainClass: 'pb-0 px-5',
        //           customClass: '!w-[800px]',
        //           body: () => <RoleEditDialog afterSave={afterSave} closeDialog={closeDialog} />,
        //         })
        //         function afterSave() {
        //           createDialog()
        //           void onQuery()
        //         }

        //         function closeDialog() {
        //           createDialog()
        //         }
        //         setCurrentRole({})
        //       }}
        //       >新增角色
        //       </Button>
        //     </x-table-actions-right>
        //   </x-table-actions>
        // ),
        table: () => (
          <Table
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default RolePage
