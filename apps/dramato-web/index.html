<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/dramabuzz.ico" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover" />
  <meta name="google-adsense-account" content="ca-pub-****************">
  <meta property="description" content="" />
  <!-- #region og -->
  <meta property="og:title" content="" />
  <meta property="og:description" content="" />
  <meta property="og:image" content="" />
  <meta property="og:url" content="" />
  <!-- #endregion -->
  <link rel="apple-touch-icon" href="/Logo.png">
  <!-- #region CSS -->
  <style>
    #global-loading {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #0B080B;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .global-loader {
      width: 30px;
      height: 30px;
      border: 3px solid #ff007a;
      border-top-color: transparent;
      border-radius: 50%;
      animation: spin 0.8s linear infinite;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  </style>
  <!-- #endregion -->

  <script>
    // 用于打点上报计算相对时间
    window.startReportTime = Date.now();
  </script>
  <script>
    // 兼容旧页面
    if (['/'].includes(window.location.pathname) && screen.width >= 768) {
      document.write(`<meta name="color-scheme" content="dark only">`)
    }
    if (window.location.pathname.startsWith('/share') ||
      window.location.pathname.startsWith('/account-deletion') ||
      window.location.pathname.startsWith('/customer-service-center') ||
      window.location.pathname.startsWith('/recharge') ||
      window.location.pathname.startsWith('/activity') ||
      window.location.pathname.startsWith('/regulations') ||
      window.location.pathname.startsWith('/sleep-check-in') ||
      window.location.pathname.startsWith('/meal-check-in') ||
      window.location.pathname.startsWith('/ttkol') ||
      window.location.pathname.startsWith('/coins') ||
      window.location.pathname.startsWith('/bind-email') ||
      window.location.pathname.startsWith('/bind-email-result') ||
      window.location.pathname.startsWith('/payment')
    ) {
      document.write(`<meta name="color-scheme" content="dark only">`)
    } else {
      // https://business.yingliangads.com/track_v3.js?pid=135&u1=81&u2=&u3=&u4=&pt=fb&id=***************
      const script = document.createElement('script')
      script.src = 'https://business.yingliangads.com/track_v3.js?pid=135&u1=81&u2=&u3=&u4=&pt=fb&id=***************'
      script.async = true
      document.head.appendChild(script)
    }
  </script>
  <meta name="google-site-verification" content="xEslqGi7E9hhFnZg71xZbY-CkgUp1eE0Xvsloh-i9yY" />
  <title>DramaWave</title>
</head>

<body>
  <h1 id="og-description" style="display: none;"></h1>
  <div id="app"></div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>