import { createComponent } from '@skynet/shared'
import { track } from 'src/lib/track'
import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { BackButton } from '../common/button/back-button'
import { ActivityBlock } from './activity-block'
import { activityImages } from './activity-images/activity-images'
import { useActivityStore } from './use-activity-store'
type ActivityPageOptions = {
  props: {}
}

const imageToButtonMap: Record<number, number | null> = {
  0: null,
  1: 0,
  2: 1,
  3: null,
}
export const ActivityPage = createComponent<ActivityPageOptions>({
  props: {},
}, () => {
  const { config, fetchConfig } = useActivityStore()
  const route = useRoute()
  const language = computed(() => ['en', ''].includes(route.query.language as string) ? 'en' : null)
  onMounted(() => {
    track('activity_H5', 'page', 'show', {
      activity_id: route.query.event_id,
      user_id: route.query.user_id,
      from: route.query.from,
    })
  })

  watch(() => route.query.event_id as string, _eventId => {
    const eventId = Number(_eventId)
    if (eventId) {
      void fetchConfig(eventId)
    }
  }, { immediate: true })

  const images = computed(() => language.value === 'en' ? activityImages.en : activityImages.other)
  return () => (
    <x-activity-page class="block relative bg-[#0c0a0d]">
      <BackButton class="absolute left-0 z-up top-[max(env(safe-area-inset-top),_44px)]" />
      <x-block-list class="flex flex-col gap-0">
        {images.value.map((i, index) => (
          <ActivityBlock key={index} type="image" src={i} button={config.value[imageToButtonMap[index] ?? -1]} />
        ))}
      </x-block-list>
    </x-activity-page>
  )
})

export default ActivityPage
