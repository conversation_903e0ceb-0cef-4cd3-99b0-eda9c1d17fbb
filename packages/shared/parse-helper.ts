export const tryParseJson = <T = object>(json: string | null | undefined, fallback: T) => {
  if (json === null || json === undefined) {
    return fallback
  }
  try {
    return JSON.parse(json) as T
  } catch {
    return fallback
  }
}

/**
 * 尝试将字符串解析为固定小数位的数字
 * @param fixed 小数位数
 * @param str 字符串
 * @param fallback 解析失败时的默认值
 * @returns 解析后的数字
 */
export const tryParseFloat = (fixed: number | undefined | null, str: string | null | undefined, fallback: number | null | undefined = 0) => {
  if (fixed === null || fixed === undefined || str === null || str === undefined) {
    return fallback
  }
  const num = Number(str)
  if (Number.isNaN(num)) {
    return fallback
  }
  // 避免 toFixed 四舍五入的问题，使用 Math.round + 10的n次方 来处理
  const pow = Math.pow(10, fixed)
  return Math.round(num * pow) / pow
}
