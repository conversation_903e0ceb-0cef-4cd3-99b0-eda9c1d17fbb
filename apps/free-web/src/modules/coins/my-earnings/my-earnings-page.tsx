import { createComponent, getQuery, mc } from '@skynet/shared'
import { Back } from 'src/modules/common/back/back'
// import exclamation from '../images/exclamation.svg'
import { coins as coinsIcon, noData, horn, background } from '../images/images'
import { Button } from '@skynet/ui'
import { computed, onMounted, ref, watch, watchEffect } from 'vue'
import { useSwipe } from '@vueuse/core'
// import { openDialog } from 'src/modules/common/dialog'
import { useCoinsStore } from '../coins-store'
import dayjs from 'dayjs'
import router from 'src/router'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { useStretchScroll } from 'src/lib/use-stretch-scroll'
import { preloadImages } from 'src/lib/preload-images'

export const MyEarningsPage = createComponent(null, props => {
  const tab = getQuery('tab', 'earnings')
  const activeTab = ref<string>(tab)
  const wrapper = ref<HTMLDivElement>()
  const tabWrapper = ref<HTMLDivElement>()
  const loadMore = ref<HTMLDivElement>()
  const isFromMyWallet = getQuery('from', '') === 'myWallet'
  const { t } = useLocale()
  const { myEarnings, myExchange, coins, getCoins, getMyEarnings, gettingMyEarnings, gettingMyExchange, getMyExchange, resetMyEarnings, resetMyExchange } = useCoinsStore()

  const { isSwiping, direction, lengthY } = useSwipe(wrapper)

  preloadImages([horn])

  const delta = computed(() => {
    if (isSwiping.value === false || direction.value !== 'up' || lengthY.value === 0 || gettingMyEarnings.value || gettingMyExchange.value) return
    if (!loadMore.value) return Infinity
    // 如果 loadMore 在 viewport 下方，距离 viewport 还有 200px，就加载更多
    const rect = loadMore.value.getBoundingClientRect()
    const clientHeight = document.documentElement.clientHeight
    return rect.top - clientHeight
  })

  // const showExclamation = () => {
  //   track('myearnings_page', 'rules', 'click')
  //   const closeDialog = openDialog({
  //     title: t('coins.earnings.benefits'),
  //     closeVisible: false,
  //     mainClass: '',
  //     body:
  //       <>
  //         <x-exclamation class="block mt-1 px-[26px] pb-7 max-h-[200px] font-normal text-[var(--text-1)] text-sm break-words overflow-y-auto scroll-smooth" id="exclamation-wrapper">
  //           <p>1.Exchanged tickets can be used to unlock premium content in the app.</p>
  //           <p>2.You can adjust the number of tickets to exchange, and any remaining coins will stay in your coin balance.</p>
  //           <p>1.Exchanged tickets can be used to unlock premium content in the app.</p>
  //           <p>2.You can adjust the number of tickets to exchange, and any remaining coins will stay in your coin balance.</p>
  //         </x-exclamation>
  //         <x-footer class="relative flex flex-col bg-white">
  //           <x-mask class="-top-[38px] absolute bg-gradient-to-b from-[rgba(255,_255,_255,_0)] to-[rgba(255,_255,_255,_1)] w-full h-10" />
  //           <x-got-it>
  //             <Button class="bg-[var(--brand-7)] mx-4 mt-4 mb-6 no-tap-color border-none rounded-lg w-[calc(100%-2rem)] h-11 text-[var(--text-5)] text-base outline-none" onClick={() => closeDialog()}>{t('coins.earnings.gotIt')}</Button>
  //           </x-got-it>
  //         </x-footer>
  //       </>,
  //   })
  //   setTimeout(() => {
  //     useStretchScroll(document.getElementById('exclamation-wrapper') as HTMLElement)
  //   }, 200)
  // }

  watch(() => wrapper.value, () => {
    if (wrapper.value) {
      useStretchScroll(wrapper.value)
    }
  })

  watch(() => tabWrapper.value, () => {
    if (tabWrapper.value) {
      useStretchScroll(tabWrapper.value, 'horizontal')
    }
  })

  onMounted(() => {
    document.title = t('coins.earnings.title')
    track('myearnings', 'page', 'show')
    void getCoins()
  })

  watch(() => activeTab.value, () => {
    if (activeTab.value === 'earnings') {
      resetMyEarnings()
      void getMyEarnings(1, 10)
    } else {
      resetMyExchange()
      void getMyExchange(1, 10)
    }
  }, {
    immediate: true,
  })

  watchEffect(() => {
    if (typeof delta.value === 'undefined') return
    if (activeTab.value === 'earnings') {
      if (delta.value < 200 && myEarnings.value.has_more && myEarnings.value.last_id !== undefined) {
        void getMyEarnings(myEarnings.value.page + 1, 10, myEarnings.value.last_id)
      }
    }
    if (activeTab.value === 'exchange') {
      if (delta.value < 200 && myExchange.value.has_more && myExchange.value.last_id !== undefined) {
        void getMyExchange(myExchange.value.page + 1, 10, myExchange.value.last_id)
      }
    }
  })

  return () => (
    <x-my-earning-page class="relative flex flex-col gap-y-4 bg-[var(--surface-4)] text-[var(--text-5)] overflow-hidden size-full">
      <img src={background} class="top-0 left-0 z-0 absolute w-full h-auto" />
      <Back title={t('coins.earnings.title')} isCloseWebview={isFromMyWallet ? false : true} isWhite={true} class="bg-transparent shrink-0">
        {/* <img src={exclamation} class="z-up-up flex justify-start items-center w-6 h-6" onClick={showExclamation} /> */}
      </Back>
      <div class="relative z-up flex justify-between items-center px-5 w-full">
        <x-total-coins class="flex flex-col flex-1 items-start gap-y-2 truncate">
          <div class="font-bold truncate">{t('coins.earnings.total')}</div>
          <div class="flex items-center gap-x-1">
            <img src={coinsIcon} class="w-[18px] h-[18px] object-cover" />
            <span class="font-bold text-3xl">{coins.value ?? 0}</span>
          </div>
        </x-total-coins>
        <Button class="flex justify-center items-center bg-[var(--fill-2)] px-4 border-none rounded-lg h-8 text-[var(--brand-6)] text-base"
          onClick={() => {
            track('myearnings_page', 'exchangebtn', 'click')
            void router.push({ path: 'exchange', query: { ...router.currentRoute.value.query, from: 'myearnings' } })
          }}
        >{t('coins.earnings.exchange')}
        </Button>
      </div>
      <x-history class="relative z-up flex flex-col flex-1 bg-[var(--fill-4)] mx-3 rounded-t-lg w-[calc(100%-1.5rem)] overflow-hidden">
        <x-history-tab class="flex gap-x-4 px-3 pt-5 pb-3 w-full overflow-x-auto" ref={tabWrapper}>
          <x-history-tab-item onClick={() => activeTab.value = 'earnings'} class={
            mc('block w-1/2 text-left text-[var(--text-3)] whitespace-nowrap', activeTab.value === 'earnings' ? 'text-[var(--brand-6)]' : '')
          }
          >{t('coins.earnings.earningsHistory')}
          </x-history-tab-item>
          <x-history-tab-item onClick={() => {
            track('myearnings_page', 'exchangetab', 'click')
            activeTab.value = 'exchange'
          }} class={mc('block w-1/2 text-left text-[var(--text-3)] whitespace-nowrap', activeTab.value === 'exchange' ? 'text-[var(--brand-6)]' : '')}
          >
            {t('coins.earnings.exchangeHistory')}
          </x-history-tab-item>
        </x-history-tab>
        <x-history-area class="flex flex-col flex-1 w-full overflow-hidden">
          {
            activeTab.value === 'earnings'
              ? (
                  myEarnings.value?.list?.length > 0
                    ? (
                        <x-history-list class="flex flex-col text-[var(--text-1)] overflow-hidden size-full">
                          <x-wrapper ref={wrapper} class="share-bottom-5" style="-webkit-overflow-scrolling: touch;">
                            {
                            // 取前50条
                              myEarnings.value.list.slice(0, 50).map(earning => (
                                <x-history-item class="flex justify-between items-center gap-x-3 p-3">
                                  <div class="flex flex-col flex-1 gap-y-1 text-sm truncate">
                                    <div class="truncate">{earning.desc}</div>
                                    <div class="text-[var(--text-3)] truncate">{dayjs.unix(earning.created).format('YYYY-MM-DD')}</div>
                                  </div>
                                  <div class="flex items-center gap-1 shrink-0">
                                    <img src={coinsIcon} class="w-4 h-4" />
                                    <div class="max-w-[12ex] text-[var(--brand-6)] truncate">{earning.amount_str}</div>
                                  </div>
                                </x-history-item>
                              ))
                            }
                            <x-load-more ref={loadMore} class="block w-full h-[1px] invisible" />
                            {
                              (myEarnings.value.list.length === 50 || !myEarnings.value.has_more)
                              && (
                                <x-limit-explain class="block pt-5 pb-3 w-full text-[var(--text-3)] text-center text-sm">
                                  {t('coins.earnings.last50earnings')}
                                </x-limit-explain>
                              )
                            }
                          </x-wrapper>
                        </x-history-list>
                      )
                    : (
                        <x-empty class="relative flex-1 w-full">
                          <div class="top-20 left-1/2 absolute flex flex-col justify-center -translate-x-1/2">
                            <img src={noData} class="w-40 h-40" />
                            <div class="text-[var(--text-4)] text-center">{t('coins.earnings.noRecords')}</div>
                          </div>
                        </x-empty>
                      )
                )
              : (
                  myExchange.value?.list?.length > 0
                    ? (
                        <x-history-list class="flex flex-col text-[var(--text-1)] overflow-hidden size-full">
                          <x-wrapper ref={wrapper} class="share-bottom-5" style="-webkit-overflow-scrolling: touch;">
                            {
                            // 取前50条
                              myExchange.value.list.slice(0, 50).map(exchange => (
                                <x-history-item class="flex justify-between items-center p-3">
                                  <div class="flex flex-col flex-1 gap-y-1 text-sm truncate">
                                    <div class="truncate">{exchange.desc}</div>
                                    {exchange.type === 'amazon' && <div class="text-[var(--text-3)] truncate text-xs">{t('coins.exchange.cardId')}: {exchange?.gift_card_secret}</div>}
                                    {exchange.type === 'ovo' && <div class="text-[var(--text-3)] truncate text-xs">{exchange?.account}</div>}
                                    <div class="text-[var(--text-3)] truncate text-xs">{dayjs.unix(exchange.created).format('YYYY-MM-DD')}</div>
                                  </div>
                                  <div class="flex items-center gap-1">
                                    <img src={coinsIcon} class="w-4 h-4" />
                                    <div class="max-w-[10ex] text-[var(--text-2)] text-base truncate">{exchange.amount_str}</div>
                                  </div>
                                </x-history-item>
                              ))
                            }
                            <x-load-more ref={loadMore} class="block w-full h-[1px] invisible" />
                            {
                              (myExchange.value.list.length === 50 || !myExchange.value.has_more)
                              && (
                                <x-limit-explain class="block pt-5 pb-3 w-full text-[var(--text-3)] text-center text-sm">
                                  {t('coins.earnings.last50exchanges')}
                                </x-limit-explain>
                              )
                            }
                          </x-wrapper>
                        </x-history-list>
                      )
                    : (
                        <x-empty class="relative flex-1 w-full">
                          <div class="top-20 left-1/2 absolute flex flex-col justify-center -translate-x-1/2">
                            <img src={noData} class="w-40 h-40" />
                            <div class="text-[var(--text-4)] text-center">{t('coins.earnings.noRecords')}</div>
                          </div>
                        </x-empty>
                      )
                )
          }
        </x-history-area>
      </x-history>
    </x-my-earning-page>
  )
})

export default MyEarningsPage
