import { createComponent } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { apiSeriesInfo, SeriesInfo } from '../share-api'
import { ref, onMounted } from 'vue'
import router from 'src/router'
import { click, freeLogo } from '../image/image'
import { useI18n } from 'vue-i18n'
import { goToApp } from 'src/lib/go-to-app'
import { isIos } from 'src/lib/ua'
import { track } from 'src/lib/track'

export const SeriesPage = createComponent(null, () => {
  const series = ref<SeriesInfo>()
  const seriesId = router.currentRoute.value.query.series_id as string
  const language = router.currentRoute.value.query.language as string
  const shareUserId = router.currentRoute.value.query.share_user_id as string
  const pageFrom = router.currentRoute.value.query.from as string
  const loading = ref<boolean>(true)
  const width = ref<number>()
  const height = ref<number>()
  const { t, locale } = useI18n()
  const languageMap: Record<string, string> = {
    ja: 'ja',
    en: 'en',
    ko: 'ko',
    es: 'es',
    th: 'th',
    id: 'id',
    vi: 'vi',
    pt: 'pt',
    tl: 'tl',
    it: 'it',
    fr: 'fr',
    de: 'de',
    tr: 'tr',
    ru: 'ru',
  }

  locale.value = language ? languageMap[language.toLowerCase()] || 'en' : 'en'

  void apiSeriesInfo(seriesId, language).then(res => {
    if (!res.data) return
    if (res.data.series_info && res.data.series_info.cover) {
      const image = new Image()
      image.src = res.data.series_info.cover
      image.onload = () => {
        width.value = image.width
        height.value = image.height
        loading.value = false
        series.value = res.data!.series_info
      }
    }
  })

  const deepLink = 'freereels://freereels.app/detail?id=' + seriesId + '&from=share'
  const universalLink = 'https://' + (location.host.includes('m.') ? '' : 'm.') + 'free-reels.com/detail?id=' + seriesId + '&from=share'
  const iosAppStoreLink = `https://apps.apple.com/us/app/dramareels-dramas-and-series/id6738081517?from=share`

  const handleClick = () => {
    track('H5_series', 'watchnow', 'click', {
      series_id: seriesId,
      share_user_id: shareUserId
    })
    // 使用 JavaScript 超时机制，检测应用是否已安装，已安装跳转到app，未安装跳转到appStoreLink
    // 实现H5唤端功能，当IOS时，使用universalLink,当Android时使用 deepLink 唤端
    goToApp(isIos() ? universalLink : deepLink)
  }


  onMounted(() => {
    track('H5_series', 'page', 'show', {
      series_id: seriesId,
      share_user_id: shareUserId
    })
  })


  return () => (
    !loading.value && (
      <div class="relative size-full bg-[#0b080b]">
        <div class="z-0 left-0 top-0 absolute w-full h-auto">
          <img src={series.value?.cover} class="z-0 object-cover h-auto w-full relative" />
          <div class="absolute z-1 size-full left-0 top-0 backdrop-blur-[5px] bg-gradient-to-b from-[#0B080B1A] to-[#0b080b]" />
        </div>
        <div class="px-3 w-full z-up flex flex-col items-center relative py-26">
          <div class="absolute top-0 h-11 left-0 w-full flex items-center justify-center bg-[rgba(9,6,9,0.5)]">
            <img src={freeLogo} class="h-7 w-auto object-cover" />
            <span class="ml-2 text-[rgba(255,255,255,0.8)]">|</span>
            <span class="pl-2 text-[rgba(255,255,255,0.8)] text-sm translate-y-[2px]">Branded as DramaReels on iOS</span>
          </div>
          <div class="flex-1 w-full flex justify-center items-center">
            <img src={series.value?.cover} class="w-[60%] object-cover h-auto rounded-lg border-solid border border-[rgba(253,_251,_252,_0.2)]" width={width.value} height={height.value} />
          </div>
          <div class="w-full flex flex-col items-center gap-3">
            <div class="text-center text-[#fdfbfc] text-lg leading-snug mt-6">{series.value?.name}</div>
            <div class="text-[#cccacb] text-sm font-normal line-clamp-5 break-words">{series.value?.desc}</div>
          </div>
          <Button class="fixed bottom-8 pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] w-[calc(100%-1.5rem)] left-1/2 -translate-x-1/2 h-11 bg-[var(--brand-6)] rounded-lg border-none text-[rgba(253,_251,_252,_0.9)] text-base leading-tight" onClick={handleClick}>
            {t('share.watchNow')}
            <img src={click} class="animate-[blink_.8s_infinite] absolute w-[3.125rem] h-11 right-4 -bottom-[10px] object-cover" />
          </Button>
        </div>
      </div>
    )
  )
})

export default SeriesPage
