/**
 * 获取 language.
 * 支持 en-US, ja-JP, ko-KR, es-ES, pt-PT, vi-VN, th-TH, id-ID, tl-PH, fr-FR, de-DE, it-IT, ru-RU, tr-TR, ms-MY, zh-TW, zh-CN
 */
export default function useLanguage() {
  // 获取请求头
  const headers = useRequestHeaders(['accept-language'])

  // 从请求头获取语言信息
  const acceptLanguage = headers['accept-language'] || ''

  // 默认语言为英语
  let languageAndLocale = 'en-US'

  if (acceptLanguage) {
    // 从请求头中获取首选语言
    const preferredLanguage = acceptLanguage.split(',')[0]
    if (preferredLanguage) {
      languageAndLocale = preferredLanguage
    }
  }

  // 提取主要语言部分（如 zh-CN 中的 zh）
  const language = languageAndLocale.split('-')[0]

  // 确保 languageAndLocale 包含连字符"-"
  if (!languageAndLocale.includes('-')) {
    // 如果不包含连字符，则添加默认区域设置
    // 为常见语言设置默认区域
    const defaultRegions: Record<string, string> = {
      zh: 'CN',
      en: 'US',
      ja: 'JP',
      ko: 'KR',
      fr: 'FR',
      de: 'DE',
      es: 'ES',
      ru: 'RU',
      pt: 'PT',
      vi: 'VN',
      th: 'TH',
      id: 'ID',
      tl: 'PH',
      it: 'IT',
      tr: 'TR',
      ms: 'MY'
    }

    const region = defaultRegions[language] || 'US'
    languageAndLocale = `${language}-${region}`
  }

  return {
    /**
     * 示例 zh, en, ja, ko, fr, de, es, ru
     */
    language,
    /**
     * 示例 zh-CN, en-US, ja-JP, ko-KR, fr-FR, de-DE, es-ES, ru-RU
     */
    languageAndLocale
  }
}
