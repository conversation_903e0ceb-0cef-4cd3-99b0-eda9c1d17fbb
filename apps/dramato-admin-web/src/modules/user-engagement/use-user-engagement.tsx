/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { omit } from 'lodash-es'
import { apiNewUserStrategyList, apiCreateNewUserStrategy, apiNewUserStrategyDetail, apiUpdateNewUserStrategy, apiNewUserStrategyOption } from './user-engagement-api'
// import { EpisodeTagsForm } from './episode-theatre-form'

const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'

export const useUserEngagement = () => {
  return {
    Form, listParams, Table, total, list, loading, search, onPageChange, onPageSizeChange, copyBtn,
  }
}

const Form = CreateForm<M.UserEngagement.List.Request>()

const Table = CreateTableOld<M.UserEngagement.List.Item>()
const total = ref<number>(0)
const list = ref<M.UserEngagement.List.Item[]>([])
const loading = ref<boolean>(false)

const voidTab = {
  id: 0,
  name: '', // 名称
  status: 2, // 状态 0 不限制 1 草稿 2 上架 3 下架
  place_status: 2,
}
const tab = ref<Partial<M.UserEngagement.List.Item>>({
  ...voidTab,
})
const listParams = ref<Partial<M.UserEngagement.List.Request>>({
  next: 'page=1&page_size=10',
  page_info: {
    page_index: 1,
    page_size: 10,
  },
})
const checkedItems = ref<M.UserEngagement.List.Item[]>([])

const search = async () => {
  loading.value = true
  const res = await apiNewUserStrategyList({
    ...listParams.value,
  })
    .finally(() => {
      loading.value = false
      checkedItems.value = []
    })
  list.value = res.data?.items || []
  total.value = res.data?.page_info?.total || 0
  return list.value
}
const onPageChange = async (page_index: number) => {
  listParams.value.page_info = {
    ...(listParams.value.page_info || {}),
    page_index,
  }
  listParams.value.next = 'page=' + page_index + '&page_size=' + listParams.value.page_info.page_size
  await search()
}

const onPageSizeChange = async (page_size: number) => {
  listParams.value.page_info = {
    page_index: 1,
    page_size,
  }
  listParams.value.next = 'page=1&page_size=' + page_size
  await search()
}

const copyBtn = async (row: M.UserEngagement.List.Item) => {
  try {
    const rs = await apiNewUserStrategyDetail({ id: row.id! })
    if (!rs.data) {
      return
    }
    let d: M.UserEngagement.Save.Request = { ...rs.data }
    d = {
      ...omit(d, ['id']),
      name: d.name + '副本',
    }
    await apiCreateNewUserStrategy(d)
    showAlert('复制成功')
    void search()
  } catch (error: any) {
    showAlert(error.response.data.message || error.response.data.err_msg || '复制失败')
  }
}
