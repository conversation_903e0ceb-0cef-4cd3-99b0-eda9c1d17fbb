import { bindLoading, createComponent, createNumberId, mc, openedInProd, useValidator } from '@skynet/shared'
import { Button, Checkbox, TableColumnOld, CreateForm, CreateTableOld, Empty, Icon, Input, openDialog, Pager, showToast, transformDatetime, transformNumber } from '@skynet/ui'
import { cloneDeep, set, throttle } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { computed, onMounted, ref, watch } from 'vue'
import { RouterLink } from 'vue-router'
import { uploadStatusOptions } from './const'
import { apiDeleteMaterial, apiGetMaterialUploadList, apiGetRoleList, apiQueryByKeywords, apiUploadFile, apiUploadMaterial } from './material-upload-api'
import { useUploader } from '../common/uploader/use-uploader'
import { Uploader } from '../common/uploader/uploader'
import { useUploadMaterialStore } from './use-upload-material-store'
import dayjs from 'dayjs'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
export const MaterialUploadPage = createComponent(null, () => {
  let closeUploadDialog: () => void
  let closeDeleteDialog: () => void
  const Form = CreateForm<M.QueryMaterialUploadListParams>()
  const UploadForm = CreateForm<M.UploadMaterialParams>()
  const Table = CreateTableOld<M.MaterialUploadItem>()
  const loading = ref(false)
  const { } = useUploader()
  const { uploadMap } = useUploadMaterialStore()
  const defaultFormData = {
    title: '',
    series_key: '',
    material_name: '',
    material_artist_id: undefined,
    upload_time: undefined, // 用户选中的上传时间范围
    upload_done_time: undefined, // 用户选中的上传时间范围
    upload_status: undefined,
    page_info: {
      page_index: 1,
      page_size: 10,
    },
  }
  const defaultUploadFormData = {
    series_key: '',
    title: '',
    material_name: '',
  }
  const formData = ref<M.QueryMaterialUploadListParams>(cloneDeep(defaultFormData))
  const uploadFormData = ref<M.UploadMaterialParams>(cloneDeep(defaultUploadFormData))
  const tableData = ref<M.MaterialUploadItem[]>([])
  const total = ref(0)
  const dataReady = ref(false)
  const searching = ref(false)
  const checkedItem = ref<M.MaterialUploadItem[]>([])
  const roleList = ref<{ label: string, value: string }[]>([])
  const columns: TableColumnOld<M.MaterialUploadItem>[] = [
    [
      '',
      row => {
        const id = row.material_id
        return (
          <Checkbox
            label=""
            modelValue={checkedItem.value.map(i => i.material_id).includes(id)}
            onUpdate:modelValue={(value: unknown) => {
              if (value) {
                checkedItem.value.push(row)
              } else {
                checkedItem.value = checkedItem.value.filter(i => i.material_id !== id)
              }
            }}
          />
        )
      },
      { class: 'w-[30px]' },
    ],
    [
      '素材ID', 'material_id', { class: 'w-[100px]' },
    ],
    [
      '短剧ID', 'series_key', { class: 'w-[100px]' },
    ],
    [
      '短剧名称', 'title', { class: 'w-[200px]' },
    ],
    [
      '素材名称', 'material_name', { class: 'w-[200px]' },
    ],
    [
      '素材师', 'material_artist_name', { class: 'w-[100px]' },
    ],
    [
      '上传时间', row => dayjs.unix(row.upload_time).format('YYYY-MM-DD HH:mm:ss'), { class: 'w-[150px]' },
    ],
    [
      '上传状态', (row: M.MaterialUploadItem) => (
        <x-upload-status class={mc('text-center', row.upload_status === 2 ? 'text-green-500' : row.upload_status === 3 ? 'text-red-500' : 'text-orange-500')}>{['', '上传中', '上传成功', '上传失败'][row.upload_status]}</x-upload-status>
      ), { class: 'w-[100px]' },
    ],
    [<span class="px-3">操作</span>, (row, index) => (
      <div class="flex gap-x-2 justify-around px-3">
        <Button class="btn btn-outline btn-xs" disabled={row.compression_status !== 2 || !row.material_url} onClick={() => {
          // 下载
          const link = document.createElement('a')
          link.href = row.material_url
          link.download = row.material_name
          link.click()
        }}
        >
          素材下载
        </Button>
        <Button class="btn btn-outline btn-xs" disabled={row.upload_status !== 2} onClick={() => openPlayDialog(row)}>
          查看素材
        </Button>
        <Button class="btn btn-outline btn-xs" onClick={() => openUploadMaterialDialog(row)}>
          重新上传
        </Button>
        <Button class="btn btn-outline btn-xs" onClick={() => deleteMaterial(row)}>
          删除素材
        </Button>
      </div>
    ), {
      class: 'w-[250px]',
    },
    ],
  ]
  const currentUploadFileResult = ref()
  const currentUploadFileKey = ref<string>()
  const promise = ref<Promise<unknown>>()
  const search = async (_page?: number) => {
    formData.value.page_info.page_index = _page ?? formData.value.page_info.page_index
    const params = {
      ...cloneDeep(formData.value),
      material_artist_id: formData.value.display_my_assets === 1 ? undefined : formData.value.material_artist_id,
    }
    const res = await apiGetMaterialUploadList(params)
    if (!res.data) return
    tableData.value = res.data.list
    total.value = res.data.total
  }

  const rules = z.object({
    title: z.string().min(1, '请输入短剧名称'),
    series_key: z.string().min(1, '请输入短剧ID'),
    material_name: z.string().min(1, '请输入素材名称'),
  })

  const { error, validateAll } = useValidator(uploadFormData, rules)

  const upload = async () => {
    if (!validateAll()) {
      console.log(error.value)
      return
    }
    if (!currentUploadFileKey.value) return
    if (currentUploadFileResult.value) {
      const res1 = await apiUploadMaterial(uploadFormData.value)
      if (res1.code === 200 && res1.data?.material_id) {
        const res2 = await bindLoading(apiUploadFile({
          data: [{
            ...currentUploadFileResult.value,
            material_id: res1.data?.material_id,
          }],
        }), loading)
        if (res2.code === 200) {
          showToast('上传成功')
          closeUploadDialog()
          void search()
        }
      }
    } else {
      const res1 = await bindLoading(apiUploadMaterial(uploadFormData.value), loading)
      if (res1.code === 200 && res1.data?.material_id) {
        // 生成记录成功且此时文件还没有上传到oss，需要存储一个状态，等文件上传成功后，再更新状态
        uploadMap.value.set(currentUploadFileKey.value, {
          materialId: res1.data.material_id,
          uploadStatus: 1,
        })
        showToast('上传成功')
        closeUploadDialog()
        void search()
      }
    }
  }

  const confirmDelete = (data: M.MaterialUploadItem) => {
    void apiDeleteMaterial({
      material_id: data.material_id,
    }).then(res => {
      if (res.code === 200) {
        showToast('删除成功')
        closeDeleteDialog()
        if (tableData.value.length === 1) {
          formData.value.page_info.page_index = formData.value.page_info.page_index - 1 > 0 ? formData.value.page_info.page_index - 1 : 1
        }
        void search()
      }
    })
  }

  const deleteMaterial = (data: M.MaterialUploadItem) => {
    closeDeleteDialog = openDialog({
      title: '删除',
      mainClass: 'pb-0 px-5',
      body:
      <>
        是否确认要删除该素材？
        <div class="flex justify-end gap-x-2 px-[20px]">
          <Button class="btn btn-sm btn-default" onClick={() => closeDeleteDialog()}>取消</Button>
          <Button class="btn btn-sm btn-primary" disabled={loading.value} onClick={() => confirmDelete(data)}>确认</Button>
        </div>
      </>,
    })
  }

  const searchResults = ref<{ title: string, series_key: string }[]>([])
  const searchResults2 = ref<{ title: string, series_key: string }[]>([])

  watch(() => uploadFormData.value.title, throttle(() => {
    if (uploadFormData.value.title === '') return
    // 这里只取付费版应用下的短剧资源，不分IOS和安卓，所以线上app_id为2，对应Dramawave_Android，测试app_id为44，对应Dramawave_Android
    void apiQueryByKeywords({ search_keyword: uploadFormData.value.title, app_id: openedInProd ? 2 : 44 }).then(res => {
      searchResults.value = res.data?.list ?? []
    })
  }, 1000))

  watch(() => uploadFormData.value.series_key, throttle(() => {
    // 这里只取付费版应用下的短剧资源，不分IOS和安卓，所以线上app_id为2，对应Dramawave_Android，测试app_id为44，对应Dramawave_Android
    void apiQueryByKeywords({ search_keyword: '', app_id: openedInProd ? 2 : 44 }).then(res => {
      searchResults2.value = res.data?.list.filter(item => item.series_key.includes(uploadFormData.value.series_key)) ?? []
    })
  }, 1000))

  const renderTitleSearchResult = () => {
    return () => searchResults.value.length > 0
      ? (
          <div class="flex flex-col p-3 gap-y-2 justify-start items-start pt-2 max-h-[400px] overflow-y-auto w-[500px] bg-white rounded-md shadow">
            {
              searchResults.value.map(item => (
                <div class="flex items-center cursor-pointer w-full" onClick={() => {
                  uploadFormData.value.title = item.title
                  uploadFormData.value.series_key = item.series_key
                }}
                >
                  <span class="truncate">{item.title}</span>
                </div>
              ))
            }
          </div>
        )
      : (
          <div class="flex justify-center items-center h-32 w-[300px] bg-white rounded-md shadow">
            <p>暂无搜索结果</p>
          </div>
        )
  }

  const renderSeriesKeySearchResult = () => {
    return () => searchResults2.value.length > 0
      ? (
          <div class="flex flex-col p-3 gap-y-2 justify-start items-start pt-2 max-h-[400px] overflow-y-auto w-[500px] bg-white rounded-md shadow">
            {
              searchResults2.value.map(item => (
                <div class="flex flex-col items-start gap-y-1 cursor-pointer w-full" onClick={() => {
                  uploadFormData.value.title = item.title
                  uploadFormData.value.series_key = item.series_key
                }}
                >
                  <div class="truncate">{item.title}（series_key: {item.series_key}）</div>
                </div>
              ))
            }
          </div>
        )
      : (
          <div class="flex justify-center items-center h-32 w-[300px] bg-white rounded-md shadow">
            <p>暂无搜索结果</p>
          </div>
        )
  }

  const openUploadMaterialDialog = (data?: M.MaterialUploadItem) => {
    currentUploadFileResult.value = null
    if (data) {
      uploadFormData.value = {
        series_key: data.series_key,
        title: data.title,
        material_id: data.material_id,
        material_name: data.material_name,
      }
    }
    closeUploadDialog = openDialog({
      title: '上传素材',
      mainClass: '!p-4 !pb-0',
      body: computed(() => (
        <>
          <UploadForm
            class="w-full flex flex-nowrap flex-col"
            hasAction={false}
            error={error.value}
            data={uploadFormData.value}
            onChange={(path, value) => {
              set(uploadFormData.value, path, value)
            }}
            items={[
              [requiredLabel('短剧名称'), 'title', { type: 'custom', render: () => (
                <Input
                  class="input input-bordered input-sm w-full overflow-hidden"
                  inputClass="w-full grow shrink basis-0 p-0 border-none pr-6"
                  popoverWrapperClass="z-popover-in-dialog"
                  v-model={uploadFormData.value.title}
                  results={renderTitleSearchResult()}
                />
              ) }],
              [requiredLabel('短剧ID'), 'series_key', { type: 'custom', render: () => (
                <Input
                  class="input input-bordered input-sm w-full overflow-hidden"
                  inputClass="w-full grow shrink basis-0 p-0 border-none pr-6"
                  popoverWrapperClass="z-popover-in-dialog"
                  v-model={uploadFormData.value.series_key}
                  results={renderSeriesKeySearchResult()}
                />
              ) }],
              [requiredLabel('素材名称'), 'material_name', { type: 'text' }],
              ['素材上传', '', { type: 'custom', render: () => (
                <Uploader
                  isImage={false}
                  maxsize={200 * 1024 * 1024}
                  ossKeyType="resource"
                  multiple={false}
                  accept="mp4,mkv,avi,mov"
                  beforeUpload={({ files }) => {
                    currentUploadFileKey.value = files[0].name + createNumberId()
                    return Promise.resolve(currentUploadFileKey.value)
                  }}
                  onUploadSuccess={file => {
                    console.log('upload success', file)
                    promise.value = new Promise(() => {
                      const video = document.createElement('video')
                      video.preload = 'metadata'
                      video.src = file.url as string
                      video.onloadedmetadata = () => {
                        const width = video.videoWidth
                        const height = video.videoHeight
                        const resolution = `${width}x${height}`
                        const videoInfo = {
                          temp_path: file.temp_path || '',
                          duration: Math.round(video.duration),
                          file_path: file.temp_path || '',
                          file_size: file.file?.size || 0,
                          serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
                          file_resolution: resolution,
                        }
                        currentUploadFileResult.value = videoInfo
                        console.log('key', file.before_upload_resp, uploadMap.value)
                        if (file.before_upload_resp && uploadMap.value.has(file.before_upload_resp as string)) {
                          const item = uploadMap.value.get(file.before_upload_resp as string)
                          if (item) {
                            void apiUploadFile({
                              data: [{
                                ...currentUploadFileResult.value,
                                material_id: item?.materialId,
                              }],
                            }).then(res => {
                              if (res.code === 200) {
                                uploadMap.value.delete(file.before_upload_resp as string)
                                void search()
                              }
                            }).catch(() => {
                              item.uploadStatus = 3
                            })
                          }
                        }
                      }
                    })
                  }}
                >
                  <x-uploader-wrapper class="w-full flex-col h-[120px] flex justify-center items-center  border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer">
                    <div>上传视频, 支持MP4, MKV, AVI，MOV格式</div>
                  </x-uploader-wrapper>
                </Uploader>
              ) }],
            ]}
          />
          <div class="flex justify-end gap-x-2 px-[20px]">
            <Button class="btn btn-sm btn-default" onClick={() => closeUploadDialog()}>取消</Button>
            <Button class="btn btn-sm btn-primary" disabled={loading.value} onClick={upload}>
              {loading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              提交
            </Button>
          </div>
        </>
      )),
      beforeClose: () => {
        uploadFormData.value = cloneDeep(defaultUploadFormData)
        searchResults.value = []
        searchResults2.value = []
      },
    })
  }

  const openPlayDialog = (data?: M.MaterialUploadItem) => {
    openDialog({
      title: '预览',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[390px]',
      body: <video class="w-[350px] max-h-[550px]" src={data?.material_url} controls />,
    })
  }

  watch(() => formData.value.material_artist_id, () => {
    formData.value.display_my_assets = 0
    if (formData.value?.material_artist_id && formData.value?.material_artist_id?.length > 1 && formData.value?.material_artist_id?.includes('me')) {
      formData.value.material_artist_id = ['me']
    }
    if (formData.value.material_artist_id && formData.value.material_artist_id.length === 1 && formData.value.material_artist_id[0] === 'me') {
      formData.value.display_my_assets = 1
    }
  })

  onMounted(async () => {
    void apiGetRoleList({ role_id: 8 }).then(res => {
      roleList.value = res.data?.list.map(item => ({
        label: item.Username,
        value: item.Username,
      })) ?? []
      roleList.value.push({
        label: '只看我的',
        value: 'me',
      })
      dataReady.value = true
    })
    // 更新上一次关闭页面前的上传状态
    const uploadingListFromStore = localStorage.getItem('uploadingList')
    if (uploadingListFromStore) {
      const uploadingList = JSON.parse(uploadingListFromStore) as number[]
      if (uploadingList.length) {
        await apiUploadFile({
          data: uploadingList.map(item => ({
            material_id: item,
            file_path: '',
          })),
        })
        localStorage.removeItem('uploadingList')
      }
    }
    void search(1)
  })

  return () => (
    <NavFormTablePager>{{
      nav: () => (
        <ul>
          <li><RouterLink to="/material-upload">素材上传</RouterLink></li>
        </ul>
      ),
      form: () => (
        <Form
          dataReady={dataReady.value}
          data={formData.value}
          onChange={(path, value) => {
            set(formData.value, path, value)
          }}
          onReset={() => {
            formData.value = cloneDeep(defaultFormData)
            void search(1)
          }}
          onSubmit={() => search(1)}
          items={[
            ['短剧名称', 'title', { type: 'text' }],
            ['短剧ID', 'series_key', { type: 'text' }],
            ['素材名称', 'material_name', { type: 'text' }],
            ['素材师', 'material_artist_id', {
              type: 'multi-select',
              search: true,
              options: roleList.value,
            }],
            ['上传时间', 'upload_time', { type: 'datetime' }, { transform: transformDatetime }],
            [<div class="w-1 h-6" />, '', { type: 'custom', render: () => <div class="text-gray-400 h-[34px] leading-[34px]">至</div> }],
            [<Empty />, 'upload_done_time', { type: 'datetime' }, { transform: transformDatetime }],
            ['上传状态', 'upload_status', { type: 'select', options: uploadStatusOptions }, { transform: transformNumber }],
          ]}
        />
      ),
      tableActions: () => (
        <x-table-actions class="flex justify-end items-center">
          <Button class="btn btn-primary btn-sm" onClick={() => openUploadMaterialDialog()}>上传素材</Button>
        </x-table-actions>
      ),
      table: () => (
        <Table list={tableData.value} columns={columns} class="tm-table-fix-last-column" loading={searching.value} />
      ),
      pager: () => (
        <Pager total={total.value} class="justify-end"
          v-model:page={formData.value.page_info.page_index}
          onUpdate:page={p => search(p)}
          v-model:size={formData.value.page_info.page_size}
          onUpdate:size={p => search(1)}
        />
      ),
    }}
    </NavFormTablePager>
  )
})

export default MaterialUploadPage
