/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Button, Checkbox, DateTime, openDialog, Pager, showAlert, transformNumber } from '@skynet/ui'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useUserStrategyLayer } from './use-user-strategy-layer'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { apiEditUserStrategyLayerPriority, apiEditUserStrategyLayerStatus, popupApi, apiStrategyGroupLayerEstimate } from './user-strategy-layer-api'
import dayjs from 'dayjs'
import { get_k_sso_token } from 'src/lib/device-id'
// import qs from 'qs'

type UserStrategyLayerOptions = {
  props: {}
}
export const UserStrategyLayer = createComponent<UserStrategyLayerOptions>({
  props: {},
}, props => {
  const { Form, params, Table, list, loading, page, pageSize, total, search } = useUserStrategyLayer()

  const router = useRouter()
  const currentPriority = ref(0)

  const checkedItems = ref<number[]>([])

  const isAll = () => {
    return list.value.every(item => checkedItems.value.includes(item.id))
  }

  const exportExcel = async () => {
    // const sort = {}
    // const result = JSON.parse(JSON.stringify({ ...params.value }))

    try {
      // const queryString = qs.stringify({ layer_ids: checkedItems.value || [] }).toString()
      // const queryString = encodeURIComponent({ ...params.value, sort})

      // 发送请求到服务器
      const response = await fetch(`${import.meta.env.VITE_DRAMA_API_URL}/strategy-group/layer/export?layer_ids=${(checkedItems.value || []).join(',')}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Device: 'Web',
          Token: get_k_sso_token() || '',
        },
      })
      // 检查响应是否成功
      if (!response.ok) {
        showAlert('下载失败', 'error')
        return
      }
      // 获取文件流
      const blob = await response.blob()
      // 检查 Blob 是否有效
      if (blob.size === 0) {
        showAlert('下载的文件为空', 'error')
        return
      }
      // 创建一个下载链接并触发下载
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `用户分层${dayjs().format('MMDD')}.xlsx` // 设置下载的文件名
      document.body.appendChild(link)
      link.click() // 自动点击链接以开始下载
      document.body.removeChild(link) // 清理链接
      URL.revokeObjectURL(link.href) // 释放内存
    } catch (error) {
      showAlert('下载失败', 'error')
    }
  }

  onMounted(() => {
    params.value.persona_type = 0
    void search(1)
  })

  const onListTypeChange = (persona_type: number) => {
    params.value.persona_type = persona_type
    void search(1)
  }

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <x-nav class={mc('space-y-2')}>
            <ul>
              <li>用户分层</li>
            </ul>
            <x-nav-tab role="tablist" class="tabs-boxed tabs w-1/3 bg-slate-200">
              <x-tab class={mc('tab', params.value.persona_type === 0 ? 'tab-active' : '')} onClick={() => onListTypeChange(0)}>普通画像</x-tab>
              <x-tab class={mc('tab', params.value.persona_type === 1 ? 'tab-active' : '')} onClick={() => onListTypeChange(1)}>剧维度画像</x-tab>
            </x-nav-tab>
          </x-nav>
        ),
        form: () => (
          <Form
            class="flex-col"
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            hideEmptyError
            onReset={() => {
              params.value = { }
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              [
                ['用户分层ID', 'layer_id', { type: 'number' }, { transform: transformNumber }],
                ['用户分层名称', 'name', { type: 'text' }],
                ['排序', 'sort', {
                  type: 'select',
                  options: [
                    { label: '创建时间：从新到旧', value: 'created-DESC' },
                    { label: '创建时间：从旧到新', value: 'created-ASC' },
                    { label: 'ID：从大到小', value: 'id-DESC' },
                    { label: 'ID：从小到大', value: 'id-ASC' },
                    { label: '优先级：从大到小', value: 'priority-DESC' },
                    { label: '优先级：从小到大', value: 'priority-ASC' },
                    // { label: '冻结', value: 4 },
                  ],
                }],
              ],
              <h2 class="relative my-2 text-lg font-semibold">预设分层条件</h2>,
              ['投放类型：', 'query_condition.CampaignType', {
                type: 'checkbox-group',
                options: [
                  { label: '免费类型', value: 'free' },
                  { label: '付费类型', value: 'paid' },
                  { label: '潜力付费类型', value: 'potential' },
                ] },
              { class: 'flex flex-row items-center' },
              ],
              ['用户类型：', 'query_condition.UserType', {
                type: 'checkbox-group',
                options: [
                  { label: '新用户', value: '1' },
                  { label: '老用户', value: '2' },
                  { label: '回归用户', value: '3' },
                ],
              },
              { class: 'flex flex-row items-center' },
              ],
              ['区域标签：', 'query_condition.Region', {
                type: 'checkbox-group',
                options: [
                  { label: 'T0', value: 'T0' },
                  { label: 'T1', value: 'T1' },
                  { label: 'T2', value: 'T2' },
                  { label: 'T3', value: 'T3' },
                  { label: 'T4', value: 'T4' },
                ],
              },
              { class: 'flex flex-row items-center' },
              ],
              ['广告价值档位：', 'query_condition.AdValueLevel', {
                type: 'checkbox-group',
                options: [
                  { label: '0（空）', value: '0' },
                  { label: '1', value: '1' },
                  { label: '2', value: '2' },
                  { label: '3', value: '3' },
                  { label: '4', value: '4' },
                  { label: '5', value: '5' },
                  { label: '6', value: '6' },
                  { label: '7', value: '7' },
                ],
              },
              { class: 'flex flex-row items-center' },
              ],
              ['是否付费用户', 'query_condition.IsPaid', {
                type: 'checkbox-group',
                options: [
                  { label: '否', value: '0' },
                  { label: '是', value: '1' },
                ],
              },
              { class: 'flex flex-row items-center' },
              ],
              ['用户设备：', 'query_condition.Device', {
                type: 'checkbox-group',
                options: [
                  { label: 'IOS', value: 'ios' },
                  { label: '安卓', value: 'android' },
                ],
              },
              { class: 'flex flex-row items-center' },
              ],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            用户分层列表
            <x-hide-when-in-dialog class="flex items-center gap-2">
              <Button
                class="btn btn-primary btn-sm"
                onClick={exportExcel}
              >
                导出Excel
              </Button>
              <Button class="btn btn-primary btn-sm" onClick={() => {
                void router.push('/user-strategy-layer/create' + `?persona_type=${params.value.persona_type}`)
              }}
              >
                新增用户分层
              </Button>
            </x-hide-when-in-dialog>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            [
              () => (
                <Checkbox
                  label=""
                  modelValue={isAll()}
                  onUpdate:modelValue={(value: unknown) => {
                    if (value) {
                      checkedItems.value = list.value.map(item => item.id)
                    } else {
                      checkedItems.value = []
                    }
                  }}
                />
              ),
              row => {
                const id = row.id
                return (
                  <Checkbox
                    label=""
                    disabled={!!!id}
                    modelValue={checkedItems.value.includes(id)}
                    onUpdate:modelValue={(value: unknown) => {
                      if (value) {
                        if (!checkedItems.value.includes(id)) {
                          checkedItems.value.push(id)
                        }
                      } else {
                        const rowIndex = checkedItems.value.findIndex(i => i === id)
                        if (rowIndex !== -1) {
                          checkedItems.value.splice(rowIndex, 1)
                        }
                      }
                    }}
                  />
                )
              },
              { class: mc('w-[60px]') },
            ],
            ['用户分层id', 'id', { class: 'w-[100px]' }],
            ['用户分层名称', 'name', { class: 'w-[200px]' }],
            ['备注', 'desc', { class: 'w-[300px]' }],
            ['排序', row => (
              <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                <input
                  type="number"
                  class={mc('grow w-100px')}
                  value={row.priority}
                  onFocus={() => {
                    currentPriority.value = +(row.priority || '')
                  }}
                  onInput={(e: Event) => {
                    currentPriority.value = +((e.target as HTMLInputElement).value || '')
                  }}
                  onKeydown={(e: KeyboardEvent) => {
                    if (e.key !== 'Enter') {
                      return
                    }
                    if (currentPriority.value === row.priority) {
                      return
                    }
                    void apiEditUserStrategyLayerPriority({
                      id: row.id,
                      priority: currentPriority.value,
                    }).then(() => {
                      void search(page.value)
                      showAlert('修改成功')
                    }).catch(() => {
                      showAlert('修改失败')
                    })
                  }}
                  onBlur={() => {
                    if (currentPriority.value === row.priority) {
                      return
                    }
                    void apiEditUserStrategyLayerPriority({
                      id: row.id,
                      priority: currentPriority.value,
                    }).then(() => {
                      void search(page.value)
                      showAlert('修改成功')
                    }).catch(() => {
                      showAlert('修改失败')
                    })
                  }}
                />
              </label>
            ), { class: 'w-[100px]' }],
            [
              '创建时间',
              row => <DateTime value={row.created * 1000} />,
              { class: 'w-[150px]' },
            ],
            [
              '更新时间',
              row => <DateTime value={row.updated * 1000} />,
              { class: 'w-[150px]' },
            ],
            ['更新人', 'updated_operator_name', { class: 'w-[200px]' }],
            [<span class="px-3">操作</span>,
              row => (
                <div class="flex gap-x-2">
                  <Button class="btn btn-outline btn-primary btn-xs" onClick={() => {
                    void router.push('/user-strategy-layer/edit?id=' + row.id)
                  }}
                  >编辑
                  </Button>
                  <Button class="btn btn-outline btn-xs bg-error" onClick={() => {
                    const hideDeleteDialog = openDialog({
                      title: '删除',
                      mainClass: 'pb-0 px-5',
                      body: (
                        <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <x-delete-episode-body>确认删除 用户分层【{row.name}】吗？</x-delete-episode-body>
                          <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                            <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                            <button class="btn btn-primary btn-sm" onClick={() => {
                              void apiEditUserStrategyLayerStatus({
                                id: row.id,
                                operation: 0,
                              }).then(() => {
                                showAlert('删除成功')
                                hideDeleteDialog()
                                void search(page.value)
                              }).catch((error: any) => {
                                showAlert(error.response.data.message || error.response.data.err_msg || '操作失败')
                              })
                            }}
                            >确定
                            </button>
                          </x-delete-episode-footer>
                        </x-delete-episode-confirm-dialog>
                      ),
                    })
                  }}
                  >
                    删除
                  </Button>
                  <Button class="btn btn-outline btn-xs" onClick={async () => {
                    try {
                      const rs = await popupApi.getPersona(row.id + '')
                      if (!rs.data) {
                        return
                      }
                      const d = rs.data
                      d.id && delete d.id
                      await popupApi.updatePersona(d)
                      showAlert('复制成功')
                      void search(page.value)
                    } catch (error: any) {
                      showAlert(error.response.data.message || error.response.data.err_msg || '复制失败')
                    }
                  }}
                  >
                    复制
                  </Button>
                  <Button class="btn btn-primary btn-xs" onClick={async () => {
                    const rs = await apiStrategyGroupLayerEstimate({
                      layer_id: row.id,
                    })
                    if (!rs || !rs.data) {
                      return
                    }
                    const createDialog = openDialog({
                      title: '预估人数',
                      mainClass: 'pb-0 px-5',
                      body: (
                        <div class="text-center">
                          <p class="mb-[14px]">数据更新时间： <b>{rs.data.updated}</b></p>
                          <p>预估满足分层条件人数：<b>{rs.data.num}</b></p>
                        </div>
                      ),
                    })
                  }}
                  >预估人数
                  </Button>
                </div>
              ), {
                class: 'w-[250px]',
              },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default UserStrategyLayer
