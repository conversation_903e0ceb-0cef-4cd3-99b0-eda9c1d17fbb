import { createComponent, createStringId, fn, JsonSimpleValue } from '@skynet/shared'
import { MergeClass } from '../ui'
import { Radio } from './radio'

export type RadioGroupOptions = {
  props: {
    modelValue?: JsonSimpleValue
    disabled?: boolean
    options?: RadioOption[]
  }
  emits: {
    'update:modelValue': (value: JsonSimpleValue) => void
  }
}

export type RadioOption = { value: JsonSimpleValue, label: string, disabled?: boolean }

export const RadioGroup = createComponent<RadioGroupOptions>({
  props: {
    modelValue: '',
    disabled: false,
    options: [],
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const name = createStringId('radio-group')
  return () => (
    <MergeClass baseClass="flex flex-wrap gap-6 input-sm py-0 px-0">
      {
        props.options.length > 0
          ? props.options.map(opt => (
            <Radio name={name} value={opt.value} label={opt.label} disabled={opt.disabled}
              checked={props.modelValue === opt.value}
              onUpdate:checked={value => {
                emit('update:modelValue', value ? opt.value : '')
              }}
            />
          ))
          : null
      }
    </MergeClass>
  )
})
