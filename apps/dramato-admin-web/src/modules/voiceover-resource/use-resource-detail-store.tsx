/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from "vue"
import { apiGetAiVoiceResource } from './voiceover-resource-api'
import { apiGetSingleSubtitle, apiGetSeriesCharacters } from 'src/modules/role-mark/role-mark-api'
import { apiGetResourcePublishDetail } from 'src/modules/resource-publish/resource-publish-api'
import { showFailToast } from "@skynet/ui"

export const useResourceDetailStore =  () => {
  return {
    loading,
    getList,
    characters,
    annotations,
    list,
    defaultLanguageCode,
    getCharacters,
    setSeriesId,
    getAnnotations,
    cdnUrl,
    getRealSubtitle,
    getDetail,
    currentResource,
    emotions,
    effects,
    emotions_cn,
    effects_cn,
    origin_syllable_list,
    shorten_syllable_list
  }
}

const series_resource_id = ref<number>(-1)
const loading = ref(false)
const characters = ref<Api.RoleMark.ICharacter[]>([])
const annotations = ref<Api.RoleMark.IAnnotation[]>([])

const emotions = ref<string[]>([])
const effects = ref<string[]>([])
const emotions_cn = ref<string[]>([])
const effects_cn = ref<string[]>([])
const origin_syllable_list = ref<Api.VoiceoverResource.ISyllable[]>([])
const shorten_syllable_list = ref<Api.VoiceoverResource.ISyllable[]>([])

const list = ref<Api.VoiceoverResource.IMultipleResource[]>([])
const defaultLanguageCode = ref<string>('')
const cdnUrl = 'https://img.tianmai.cn/'
const currentResource = ref<M.IResourcePublishDetail>()

const setSeriesId = (id: number) => {
  series_resource_id.value = id
}

const getList = async (showLoading = true) => {
  if (showLoading) {
    loading.value = true
  }
  const res = await apiGetAiVoiceResource({
    series_resource_id: series_resource_id.value,
  })
  // 将数据按照剧集号(num)重新组织
  const srts = res.data?.srts || []
  const episodeMap = new Map()

  srts.forEach(srt => {
    const language = srt.language
    srt.infos?.forEach(info => {
      const { num } = info
      if (!episodeMap.has(num)) {
        episodeMap.set(num, { num })
      }
      const episode = episodeMap.get(num)
      // 为每种语言添加相关路径信息
      episode[`${language}_ora_video_path`] = info.ora_video_path
      episode[`${language}_pure_video_path`] = info.pure_video_path
      episode[`${language}_srt_path`] = info.srt_path
      episode[`${language}_audio_path`] = info.audio_path
      episode[`${language}_annotation`] = info.annotation
      episode[`${language}_episode_path`] = info.episode_path
      episode[`${language}_voice`] = info.voice
      episode[`${language}`] = srt.language
    })
  })
  // 转换为数组形式
  list.value = Array.from(episodeMap.values()).sort((a, b) => a.num - b.num)
  defaultLanguageCode.value = res.data?.default_language_code || ''
  loading.value = false
}

const getCharacters = async () => {
  try {
    const res = await apiGetSeriesCharacters({
      series_resource_id: series_resource_id.value,
    })
    characters.value = res?.data?.characters || []
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    loading.value = false
  }
}

const getAnnotations = async (num: number, lang: string) => {
  const res = await apiGetSingleSubtitle({
    series_resource_id: series_resource_id.value,
    serial_number: num,
    language: lang
  })
  // 已经填入的
  annotations.value = res.data?.annotations || []
  emotions.value = res.data?.emotions || []
  effects.value = res.data?.effects || []
  emotions_cn.value = res.data?.emotions_cn || []
  effects_cn.value = res.data?.effects_cn || []
  shorten_syllable_list.value = res.data?.shorten_syllable_list || []
  origin_syllable_list.value = res.data?.origin_syllable_list || []
}

function getRealSubtitle(path: string) {
  if (!path) return ''
  const url = path.indexOf('http') === 0 ? path : `${cdnUrl}${path}`
  return url
}

const getDetail = async () => {
  try {
    const res = await apiGetResourcePublishDetail({
      id: series_resource_id.value
    })
    currentResource.value = res.data || {}
  } catch (error: any) {
    showFailToast(error.response.data.err_msg || '获取详情失败')
  }
}
