/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetUserInfo } from './user-query-api'
import { trim } from 'lodash-es'

export const useUserQuery = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    search,
    page,
    pageSize,
    total,
  }
}

const Form = CreateForm<M.UserQuery.Params>()
const params = ref<M.UserQuery.Params>({
  // user_id: 1000432,
})

const Table = CreateTableOld<M.UserQuery.Response>()
const list = ref<M.UserQuery.Response[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(1)

const search = async (_page?: number) => {
  try {
    loading.value = true
    _page = _page || page.value + 1
    const res = await apiGetUserInfo({
      // ...params.value,
      user_ids: !params.value.user_id ? [] : (params.value.user_id?.split('\n') || []).map(item => Number(item)),
      uids: !params.value.uid ? [] : (params.value.uid?.split('\n') || []).map(item => Number(item)),
      emails: !params.value.email ? [] : (params.value.email?.split('\n') || []).map(item => trim(item)),
      page_index: _page,
      page_size: pageSize.value,
    })
    loading.value = false
    list.value = res?.data?.list || []
    total.value = res.data?.total || 0
    page.value = _page
  } catch (error: any) {
    showAlert(error.response.data.message, 'error')
  }
}
