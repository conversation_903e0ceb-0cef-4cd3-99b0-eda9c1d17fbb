<template>
  <x-tabs class="flex justify-between items-center py-1.5 px-10">
    <div @click="() => router.push(config.path || '/')" :key="config.path" v-for="config in tabConfig"
      class="flex flex-col items-center justify-center">
      <SvgIcon :noColor="true" v-if="route.path === config.path" :name="config.fillIcon" class="size-[26px]"
        :class="route.path === config.path ? 'text-[#FC2763]' : 'text-[#797B7D]'" />
      <SvgIcon :noColor="true" v-else :name="config.icon" class="size-[26px]"
        :class="route.path === config.path ? 'text-[#FC2763]' : 'text-[#797B7D]'" />
      <span class="font-normal text-[10px] leading-3 mt-1"
        :class="route.path === config.path ? 'text-[#FC2763]' : 'text-[#797B7D]'">
        {{ config.name }}
      </span>
    </div>
  </x-tabs>
</template>

<script setup lang="ts">
import { SvgIcon } from '@skynet/ui';
import { useRouter } from 'vue-router';
import { useRoute } from 'vue-router';
import { T } from './home-tabs.i18n';

const route = useRoute();
const router = useRouter();
console.log('route', route);

const tabConfig = [
  { name: T.homeTabs.home.ref, icon: 'ic-home', fillIcon: 'ic-home-fill', path: '/' },
  { name: T.homeTabs.myList.ref, icon: 'ic-list', fillIcon: 'ic-list-fill', path: '/my-list' },
  { name: T.homeTabs.profile.ref, icon: 'ic-profile', fillIcon: 'ic-profile-fill', path: '/profile' },
];
</script>

<style scoped></style>
