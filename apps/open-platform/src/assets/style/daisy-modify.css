.radio:disabled {
  opacity: 0.7 !important;
}

.dropdown:is(:not(details)) .dropdown-content {
  z-index: 2;
}

.el-menu-item.is-active {
  background-color: rgba(252, 39, 99, 0.08);
  border-radius: 8px;
}

.el-menu-item:hover, .el-sub-menu__title:hover {
  background-color: #FAF7FA!important;
  border-radius: 8px;
}

.el-dropdown-menu__item {
  padding: 8px !important;
}
.el-dropdown-menu__item:not(.is-disabled):focus, .el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: #FAF7FA!important;
  border-radius: 8px;
  color: #0B080B !important;
}

.el-dropdown-menu {
  padding: 4px !important;
  border-radius: 8px !important;
  box-shadow: 0px 2px 16px 0px rgba(11, 8, 11, 0.10) !important;
}

.el-popper {
  border: none !important;
}

.el-menu-item, .el-sub-menu__title {
  margin-bottom: 2px;
}

.el-form-item {
  margin-bottom: 24px !important;
} 
.el-input, .el-textarea {
  --el-input-bg-color: #FAF7FA !important;
  --el-input-height: 38px !important;
  --el-input-border: 1px solid transparent !important;
}

.el-input__wrapper, .el-textarea__inner, .el-select__wrapper {
  --el-input-border-color: transparent;
  --el-border-color-hover: transparent;
  --el-input-hover-border-color: transparent;
  --el-border-color: transparent;
  --el-input-focus-border-color: #CCCACB;
  --el-input-placeholder-color: #A1A0A3;
  --el-select-input-focus-border-color: #CCCACB;
  /* box-shadow: none!important; */
}

.el-input__wrapper:hover,.el-textarea__inner:hover,.el-select__wrapper:hover {
 background-color: #F3F0F3 !important;
}

.el-select__wrapper {
  --el-color-primary: #CCCACB;
}

.el-select__wrapper {
  min-height: 38px!important;
}

.el-input .el-input__count .el-input__count-inner, .el-textarea .el-input__count {
  background-color: #FAF7FA !important;

}

.el-select__wrapper{
  background-color: #FAF7FA !important;
}

.el-date-editor.el-input .el-input__wrapper {
 flex-direction: row-reverse;
}

.el-date-editor .el-icon {
  width: 20px;
}

.el-radio {
  --el-radio-input-height: 16px !important;
  --el-radio-input-width: 16px !important;
 
}
.el-radio .el-radio__inner:after {
  width: 7px;
  height: 7px;
 }

 .el-radio .el-radio__input.is-checked+.el-radio__label {
  color: initial;
 }
 
 .el-upload .el-upload-dragger {
  --el-upload-dragger-padding-horizontal: 0px;
  --el-upload-dragger-padding-vertical: 0px;
 }

 .el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before{
  content: none !important;
 }

 .el-icon svg, .el-icon {
  width: 20px !important;
  height: 20px !important;
  /* color: #434546; */
 }

 .el-popper .el-popper__arrow:before {
  content: none;
 }

 .el-overlay-dialog .el-dialog {
  --el-dialog-padding-primary: 24px;
 }

 .el-dialog .el-dialog__title {
  color: #0B080B;
  font-weight: 500;
  font-size: 18px;
 }

 .el-sub-menu .el-sub-menu__icon-arrow {
  margin-top: -10px !important;
 }

 .el-pagination.is-background .btn-next.is-active, .el-pagination.is-background .btn-prev.is-active, .el-pagination.is-background .el-pager li.is-active {
  background-color: #FFF0F3 !important;
  color: #FC2763 !important;
 }
 .el-pager li {
  color: #A1A0A3 !important;
 }
 .el-pager li:hover {
  color: #434546 !important;
  background-color: #F3F0F3!important;
 }
 .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon {
  height: 16px;
  width: 16px;
 }

 .el-message .el-message__content {
  color: #0B080B !important;
 }

 .el-message--success {
  --el-message-text-color: #1DD864 !important;
 }
 .el-message--warning {
  --el-message-text-color: #FF7809!important;
 }

 .el-message--error {
  --el-message-text-color: #FC2763!important;
 }