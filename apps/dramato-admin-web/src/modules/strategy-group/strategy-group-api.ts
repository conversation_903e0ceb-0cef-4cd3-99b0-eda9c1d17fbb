import { tryParseFloat } from '@skynet/shared'

import { httpClient } from 'src/lib/http-client'

export const apiListStrategyGroup = (params: Api.StrategyGroup.Request.List) => {
  return httpClient.post<Api.StrategyGroup.Response.List>('/strategy-group/list', params)
}

export const apiExportStrategyGroupExcel = (params: Api.StrategyGroup.Request.List) => {
  return httpClient.post<Api.StrategyGroup.Response.List>('/strategy-group/export', params)
}

export const apiDeleteStrategyGroup = (params: { ids: string[] }) => {
  return httpClient.post<Api.StrategyGroup.Response.List>('/strategy-group/delete', params)
}

export const apiUpdateStrategyGroupPriority = (params: { id: string, priority: number }) => {
  return httpClient.post<Api.StrategyGroup.Response.List>('/strategy-group/set-priority', params)
}

export const transformSetUserConfig = (value: undefined | null | [string, [number, number]]) => {
  if (!value) return value
  return [value[0], value[1].sort((a, b) => a - b).join('-')]
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const transformSetCustomUsersConfig = (value: undefined | null | Record<string, []>) => {
  if (!value) return value
  const panel_config = {
    ...value,
    hide_ad: !value.custom_panel_config.find((v: string) => v === 'show_ad'),
    hide_recharge: !value.custom_panel_config.find((v: string) => v === 'show_recharge'),
    hide_membership: !value.custom_panel_config.find((v: string) => v === 'show_membership'),
  }
  delete value.custom_panel_config
  return panel_config
}

export const transformSetUserConfigMap = {
  'user_config.custom_users_config.Cpi': [transformSetUserConfig],
  'user_config.custom_users_config.Ecpm': [transformSetUserConfig],
  'user_config.custom_users_config.Cpm': [transformSetUserConfig],
  'user_config.custom_users_config.AdsEcpm': [transformSetUserConfig],
  panel_config: [transformSetCustomUsersConfig],
}
// save_type  1 未上架，2 草稿
export const apiSaveStrategyGroup = (params: Partial<M.StrategyGroup> & { save_type: 1 | 2 | 3 }) => {
  if (typeof params.custom_series_ids === 'string') {
    if (params.custom_series_ids === '') {
      params.custom_series_ids = []
    } else {
      params.custom_series_ids = params.custom_series_ids.split(/[\s,，]+/)
        .filter(Boolean)
        .map(Number)
        .filter(num => !isNaN(num))
    }
  }
  return httpClient.post<ApiResponse<M.StrategyGroup>>('/strategy-group/save', params, {
    transformRequestData: transformSetUserConfigMap,
    transformResponseData: transformGetUserConfigMap,
  })
}

export const transformGetUserConfig = (value: undefined | null | [string, string]) => {
  if (!value) return value
  return [value[0], value[1].split('-').map(str => tryParseFloat(2, str, null))]
}
export const transformGetPanelConfig = (value: undefined | null | Record<string, Record<string, boolean>>) => {
  if (!value) return value
  const custom_panel_config = []
  if (!value.hide_ad) {
    custom_panel_config.push('show_ad')
  }
  if (!value.hide_recharge) {
    custom_panel_config.push('show_recharge')
  }
  if (!value.hide_membership) {
    custom_panel_config.push('show_membership')
  }
  return {
    ...value,
    custom_panel_config,
  }
}

export const transformGetUserConfigMap = {
  'data.user_config.custom_users_config.Cpi': [transformGetUserConfig],
  'data.user_config.custom_users_config.Cpm': [transformGetUserConfig],
  'data.user_config.custom_users_config.Ecpm': [transformGetUserConfig],
  'data.user_config.custom_users_config.AdsEcpm': [transformGetUserConfig],
  'data.panel_config': [transformGetPanelConfig],
}
export const apiGetStrategyGroupDetails = (params: { id: string }) => {
  return httpClient.get<ApiResponse<M.StrategyGroup>>('/strategy-group/detail', params, {
    transformResponseData: transformGetUserConfigMap,
  })
}

/** operation 1 上架 2 下架 3 冻结 4 解除冻结  */
export const apiUpdateStrategyGroupState = (params: { id: string, operation: number }) => {
  return httpClient.post<unknown>('/strategy-group/status/update', params)
}

export const apiGetStrategyGroupUserProfile = ({ platform, scene }: { platform: string, scene?: string }) => {
  return httpClient.get<Api.StrategyGroupUserProfile.Response.List>('/strategy-group/user-profile-item', { platform, scene })
}

export const apiGetStrategyGroupAdChannelList = () => {
  return httpClient.get<Api.StrategyGroupAdChannel.Response.List>('/strategy-group/ad-channel-list')
}

export const apiBatchGetProduct = (params: number[]) => {
  return httpClient.post<ApiResponse<{
    items: M.RechargeLevel[]
  }>>('/wallet/product/batch_get', { ids: params })
}
export const strategyGroupApi = {
  updateStrategyGroupSave: (d: M.UserStrategyLayer.UserStrategy) => {
    if (typeof d.custom_series_ids === 'string') {
      if (d.custom_series_ids === '') {
        d.custom_series_ids = []
      } else {
        d.custom_series_ids = d.custom_series_ids.split(/[\s,，]+/)
          .filter(Boolean)
          .map(Number)
          .filter(num => !isNaN(num))
      }
    }
    return httpClient.post<ApiResponse<M.StrategyGroup>>('/strategy-group/save', d)
  },
  getStrategyGroupDetail: (id: string) =>
    httpClient.get<ApiResponse<M.StrategyGroup>>('/strategy-group/detail', { id }),
}
