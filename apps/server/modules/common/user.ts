import { tryParse<PERSON><PERSON> } from '@skynet/shared'
import Cookies from 'js-cookie'
import type { User } from '~/modules/login/login'
import { h5Track } from '~/lib/h5-track'

type UserInfoBase = {
  auth_key?: string
  auth_secret?: string
  name?: string
  icon?: string
  user_id?: number
  user_type?: number
  is_new_user?: boolean
}

export const getUser = (): User => {
  const auth = Cookies.get('auth_params') ?? '{}'
  return tryParseJson(auth, {} as User)
}

export const setNewUserToFalse = () => {
  const domain1 = location.hostname
  const rootDomain = domain1.substring(domain1.indexOf('.') + 1)
  const existingUserInfo = Cookies.get('auth_params')

  if (existingUserInfo) {
    const userInfoObj = JSON.parse(existingUserInfo)
    userInfoObj.is_new_user = false
    Cookies.set('auth_params', JSON.stringify(userInfoObj), {
      expires: 365,
      path: '/',
      domain: rootDomain,
    })
  }
}

export const setUser = (data: User, is_new_user?: boolean) => {
  const domain1 = location.hostname
  const rootDomain = domain1.substring(domain1.indexOf('.') + 1)
  try {
    const userInfo: UserInfoBase = {
      auth_key: data?.auth_key,
      auth_secret: data?.auth_secret,
      name: data?.name,
      icon: data?.icon,
      user_id: data?.user_id,
      user_type: data?.user_type,
    }
    if (is_new_user) userInfo.is_new_user = true;

    Cookies.set('auth_params', JSON.stringify(userInfo), {
      expires: 365, path: '/', domain: rootDomain,
    })
  } catch (error) {
    h5Track('h5', 'set_auth_local', 'failed')
  }
}

export const emptyUser = () => {
  // localStorage.removeItem('auth_params')
  Cookies.remove('auth_params')
}
