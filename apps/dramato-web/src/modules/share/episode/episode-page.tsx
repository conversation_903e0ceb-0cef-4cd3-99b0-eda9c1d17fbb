import { createComponent } from '@skynet/shared'
import { openDialog } from 'src/modules/common/dialog'
import { arrow, dialogBg, gift, loading, logo, pause, play } from './images/images'
import { Button } from '@skynet/ui'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { goToApp } from 'src/lib/go-to-app'
import { isIOS } from '@vueuse/core'
import { apiEpisode } from './episode-api'
import { showToast } from 'src/modules/common/toast/toast'
import { track } from 'src/lib/track'
import './episode.css'
import { usePlayer } from './use-player'
import { useRouter } from 'vue-router'

export const EpisodePage = createComponent(null, () => {
  const router = useRouter()
  const subtitles = ref<{ start?: number, end?: number, text?: string }[]>([])
  // const isEndedCauseOpen = ref(false)
  const episode = ref<Episode.Episode>()
  const noContentAvailable = ref(true)
  const isDialogShow = ref(false)
  const showPoster = ref<boolean>(true)
  const initialized = ref<boolean>(false)
  const needPlay = ref<boolean>(false)
  const currentSubtitle = ref<string>()

  const episodeId = router.currentRoute.value.params.episodeId as string
  const subtitleLanguage = router.currentRoute.value.query.subtitle_language as string | undefined
  const audioTrackLanguage = router.currentRoute.value.query.audiotrack_language as string ?? 'en-US'

  const { t } = useLocale(router.currentRoute.value.query.language as string)

  const { player, isPlaying, showPlayOrPause, initPlayer, showLoading } = usePlayer()

  const episodeUrl = computed(() => {
    return !!episode.value?.episode_list?.[0]?.external_audio_h264_m3u8 ? episode.value?.episode_list?.[0]?.external_audio_h264_m3u8 : episode.value?.episode_list?.[0]?.video_url ?? ''
  })

  const preloadImages = (urls: string[]) => {
    urls.forEach(url => {
      const img = new Image()
      img.src = url
    })
  }
  preloadImages([arrow, dialogBg, gift, logo, pause, play, loading])

  const customPlayerEventsListener = () => {
    player.value?.on('canplay', () => {
      console.log('custom canplay', initialized.value)
      if (initialized.value) return
      initialized.value = true
      switchAudioTrack()
      if (needPlay.value) player.value?.play()
    })

    player.value?.on('ready', () => {
      console.log('custom ready')
      customSubtitle()
    })

    player.value?.on('ended', () => {
      console.log('custom ended')
      isPlaying.value = false
      showPoster.value = true
      open()
      void nextTick(() => {
      // 每次播放完销毁是为了兼容部分浏览器用自带播放器播放，且播放结束不返回原页面
        player.value?.dispose()
        player.value = undefined
        initialized.value = false
        initPlayer({
          source: episodeUrl.value,
          id: 'mse',
        })
        customPlayerEventsListener()
      })
    })
  }
  const switchAudioTrack = () => {
    // internal api
    const audioSelectorOptions = document.querySelectorAll('.prism-audio-selector .selector-list li')
    const languageOptionDom = Array.from(audioSelectorOptions).filter(item => item.textContent === audioTrackLanguage)?.[0] as HTMLElement
    if (languageOptionDom) {
      languageOptionDom.click()
    }
    return
  }

  const togglePlay = () => {
    showPlayOrPause.value = true
    if (player.value) {
      if (player.value?.getStatus() === 'pause') {
        player.value?.play()
      } else {
        player.value?.pause()
      }
    }
  }

  const getSubtitle = async () => {
    if (episode.value?.episode_list?.[0]?.subtitle_list && typeof subtitleLanguage !== 'undefined') {
      const subtitle = episode.value.episode_list[0].subtitle_list.find(item => item.language === subtitleLanguage)
      if (subtitle) {
        const res = await fetch(subtitle.subtitle)
        const data = await res.text()
        parseSRT(data)
      }
    }
  }

  const getEpisode = () => {
    void apiEpisode(episodeId).then(async ({ data }) => {
      if (data) {
        const { finish_status } = data.series_info
        if (finish_status === 3) {
          noContentAvailable.value = false
          episode.value = data.series_info
          await getSubtitle()
          initPlayer({
            source: episodeUrl.value,
            id: 'mse',
          })
          customPlayerEventsListener()
          open()
        } else {
          noContentAvailable.value = true
          showToast(t('share.toast.takeOffShelf'))
        }
      }
    }).catch(e => {
      noContentAvailable.value = true
      showToast(t('share.toast.takeOffShelf'))
    })
  }

  // 解析 RST 文件
  const parseSRT = (content: string) => {
    const regex = /(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n([\s\S]*?)(?=\n\n|$)/g
    const matches = content.matchAll(regex)
    const result = []
    // @ts-expect-error never mind
    for (const match of matches) {
      const start = parseTime(match[1])
      const end = parseTime(match[2])
      const text = match[3].trim()
      result.push({ start, end, text })
    }
    subtitles.value = result
  }

  // 将时间字符串转换为秒数
  const parseTime = (timeString: string) => {
    const parts = timeString.split(':')
    const seconds = parseFloat(parts[2].replace(',', '.'))
    return (+parts[0]) * 3600 + (+parts[1]) * 60 + seconds
  }

  onUnmounted(() => {
    // 释放script和link引入的资源
    releaseSource()
  })

  onMounted(async () => {
    track('H5_series', 'page', 'show', {
      series_id: episodeId,
      share_user_id: router.currentRoute.value.query.share_user_id,
    })

    void dynamicImportFile('//static-v1.mydramawave.com/frontend_static/aliplayer-min.css')
    await dynamicImportFile('//static-v1.mydramawave.com/frontend_static/aliplayer.js').then(() => {
      getEpisode()
    })
  })

  let timer: number | undefined

  watch(() => showPlayOrPause.value, () => {
    if (timer) {
      clearTimeout(timer)
    }
    if (showPlayOrPause.value) {
      timer = window.setTimeout(() => {
        showPlayOrPause.value = false
      }, 3000)
    }
  })

  const dynamicImportFile = (filepath: string) => {
    return new Promise((resolve, reject) => {
      if (filepath.endsWith('.js')) {
        const script = document.createElement('script')
        // 创建标签还要标记一下这个是动态引入的，在页面销毁之前删除
        script.dataset.dynamic = 'true'
        script.src = filepath
        script.type = 'text/javascript'
        script.onload = () => resolve(script)
        script.onerror = () => reject(new Error(`Failed to load script: ${filepath}`))
        document.head.appendChild(script)
      } else if (filepath.endsWith('.css')) {
        const link = document.createElement('link')
        link.dataset.dynamic = 'true'
        link.href = filepath
        link.rel = 'stylesheet'
        link.onload = () => resolve(link)
        link.onerror = () => reject(new Error(`Failed to load stylesheet: ${filepath}`))
        document.head.appendChild(link)
      } else {
        reject(new Error(`Unsupported file type: ${filepath}`))
      }
    })
  }

  const releaseSource = () => {
    // 删除script或者link标签dataset.dynamic = 'true'
    const scripts = document.querySelectorAll('script[data-dynamic="true"]')
    const stylesheets = document.querySelectorAll('link[data-dynamic="true"]')
    scripts.forEach(script => script.remove())
    stylesheets.forEach(stylesheet => stylesheet.remove())
  }

  const handleClick = () => {
    const deepLink = `dramawave://dramawave.app?redirect=${encodeURIComponent('/detail?id=' + episode.value?.id + '&from_share=1')}`
    const universalLink = `https://mydramawave.com?redirect=${encodeURIComponent('/detail?id=' + episode.value?.id + '&from_share=1')}`
    // 使用 JavaScript 超时机制，检测应用是否已安装，已安装跳转到app，未安装跳转到appStoreLink
    // 实现H5唤端功能，当IOS时，使用universalLink,当Android时使用 deepLink 唤端
    goToApp(isIOS ? universalLink : deepLink)
  }

  const customSubtitle = () => {
    const video = document.getElementById('mse')?.getElementsByTagName('video')[0]
    if (!video) return
    console.log('subtitles', subtitles.value)
    // 更新字幕函数
    function updateSubtitle() {
      if (!video) return
      const currentTime = video.currentTime

      // 查找当前时间对应的字幕
      if (subtitles.value.length > 0) {
        const subtitle = subtitles.value.find(sub => {
          if (!sub.start || !sub.end) return false
          return currentTime >= sub.start && currentTime <= sub.end
        })

        if (subtitle) {
          currentSubtitle.value = subtitle.text!
        } else {
          currentSubtitle.value = undefined
        }
      }
    }

    // 每次播放时更新字幕
    video.addEventListener('timeupdate', updateSubtitle)
  }

  const open = () => {
    track('H5_series', 'popup', 'show', {
      series_id: episodeId,
      share_user_id: router.currentRoute.value.query.share_user_id,
    })
    isDialogShow.value = true
    openDialog({
      title: '',
      closeVisible: true,
      body: (
        <div style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="flex min-h-[260px] flex-col bg-no-repeat p-2 pt-5">
          <img src={gift} class="fixed right-0 top-0 h-[102px] w-[104px] object-cover" width={104} height={102} />
          <x-title class="line-clamp-3 w-[calc(100%-104px+0.5rem)] px-2 text-lg font-bold leading-snug text-[var(--black)]">{t('share.episodeDialog.title')}</x-title>
          <x-description class="mt-2 line-clamp-2 w-[calc(100%-5rem)] pl-2 text-sm font-normal text-[var(--grey-7)]">{t('share.episodeDialog.description')}</x-description>
          <x-card class="mt-3 flex h-auto w-full flex-col items-start gap-0 rounded-xl bg-[var(--white)] pb-3">
            <x-tips style="flex: 1 1 auto;" class="h-5 w-auto grow-0 rounded-br-xl rounded-tl-xl bg-[#fc2763]/20 px-2 py-1 text-[10px] font-bold text-[var(--brand-6)]">{t('share.episodeDialog.tips')}</x-tips>
            <x-content class="mt-1 grid w-full grid-cols-[34.41px_auto] items-start gap-x-3 rounded-xl py-2 pl-2 pr-6 font-normal">
              <img src={episode.value?.cover} class="row-span-2 h-12 max-h-12 w-[34.41px] rounded-lg object-cover" />
              <div class="mt-[6.5px] truncate text-sm text-[#090609]">{episode.value?.name}</div>
              <div class="mb-[6.5px] mt-1 truncate text-xs text-[#797b7d]">{episode.value?.desc}</div>
            </x-content>
            <Button onClick={() => {
              track('H5_series', 'popupwatch', 'click', {
                series_id: episodeId,
                share_user_id: router.currentRoute.value.query.share_user_id,
              })
              handleClick()
            }} class="relative mx-2 mt-4 flex h-11 w-[calc(100%-1rem)] flex-nowrap items-center justify-center rounded-lg border-none bg-[#fc2763] text-base leading-tight text-white/90"
            >
              <div class="w-full truncate px-2">
                {t('share.episodeDialog.button')}
              </div>
              <div class="absolute -top-[18px] right-1 h-[18px] w-auto rounded-xl border border-solid border-[#fff8e6] bg-[var(--gradient-1)] px-2 text-left text-[10px] leading-4 text-[var(--white)]">
                {t('share.episodeDialog.limit24')}
                <img src={arrow} class="z-up absolute -bottom-[7px] right-[23px] h-2 w-auto object-cover" />
              </div>
            </Button>
          </x-card>
        </div>
      ),
      beforeClose: () => {
        track('H5_series', 'popupclose', 'click', {
          series_id: episodeId,
          share_user_id: router.currentRoute.value.query.share_user_id,
        })
        isDialogShow.value = false
        showPoster.value = false
        try {
          // 如果是首次关闭弹窗触发播放器播放，但是此时播放器还未初始化，会报错
          player.value?.play()
        } catch (e) {
          needPlay.value = true
        }
      },
    })
  }

  return () => (
    noContentAvailable.value
      ? null
      : (
          <div class="pc:w-[calc(var(--phone-page-max-width))] pad:w-[calc(var(--phone-page-max-width))] size-full">
            <x-video-player id="mse" class="relative flex size-full items-center justify-center" />
            <x-video-controls class="share-bottom">
              {/* logo */}
              <img src={logo} class="z-12 z-up-up fixed right-3 top-0 size-11 object-cover" />
              {/* 自定义播放暂停按钮 */}
              {showPlayOrPause.value && (
                <img src={isPlaying.value ? pause : play} onClick={() => {
                  togglePlay()
                  if (!isPlaying.value) {
                    track('H5_series', 'play', 'click', {
                      series_id: episodeId,
                      share_user_id: router.currentRoute.value.query.share_user_id,
                    })
                  }
                }} class="w-18 h-18 fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 object-cover"
                />
              )}
              {/* 加载按钮 */}
              { !isDialogShow.value && showLoading.value && (
                <x-loading-wrapper class="z-up-up fixed left-0 top-0 flex size-full items-center justify-center">
                  <img src={loading} class="size-16 animate-spin object-cover" />
                </x-loading-wrapper>
              ) }
              {/* 封面区域 */}
              {showPoster.value && <img src={episode.value?.cover} class="z-up fixed left-0 top-0 size-full object-cover" />}
              {/* 字幕区域 */}
              {currentSubtitle.value && <x-subtitle class="pc:w-[calc(var(--phone-page-max-width))] pad:w-[calc(var(--phone-page-max-width))] subtitle z-up fixed left-1/2 block w-full -translate-x-1/2 text-wrap break-words bg-transparent px-3 text-center text-[24px] text-[var(--white)]">{currentSubtitle.value}</x-subtitle>}
            </x-video-controls>
            <x-footer class="pc:w-[calc(var(--phone-page-max-width))] z-up-up pad:w-[calc(var(--phone-page-max-width))] share-bottom fixed bottom-0 left-1/2 flex h-auto w-full -translate-x-1/2 flex-col rounded-t-lg bg-[rgba(36,_37,_38,_0.65)] px-3 pt-2 backdrop-blur-[2px]">
              <x-episode class="grid grid-cols-[44.49px_auto] gap-x-3">
                <img src={episode.value?.cover} class="max-h-15 row-span-2 h-auto w-[44.49px] rounded object-cover" />
                <x-episode-title class="mt-[11px] truncate text-base leading-tight text-[var(--white)]">{episode.value?.name}</x-episode-title>
                <x-episode-description class="mb-[11px] mt-1 truncate text-xs font-normal text-[var(--grey-7)]">{episode.value?.desc}</x-episode-description>
              </x-episode>
              <Button onClick={() => {
                track('H5_series', 'watchnow', 'click', {
                  series_id: episodeId,
                  share_user_id: router.currentRoute.value.query.share_user_id,
                })
                handleClick()
              }} class="mt-2 h-11 rounded-lg border-none bg-[var(--brand-6)] text-base leading-tight text-[rgba(255,_251,_255,_0.9)]"
              >{t('share.watchNow')}
              </Button>
            </x-footer>
          </div>
        )
  )
})

export default EpisodePage
