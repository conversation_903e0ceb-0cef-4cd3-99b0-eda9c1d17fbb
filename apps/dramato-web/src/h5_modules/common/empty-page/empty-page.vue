<template>
  <van-empty
    :class="['custom-empty', customClass]"
    :image="image"
    :image-size="imageSize"
    :description="description"
  />
</template>

<script setup lang="ts">
import { Empty as VanEmpty   } from 'vant';
import defaultEmptyImg from 'src/h5_modules/images/search-no-result.webp';
interface Props {
  /**​ 自定义空状态图片 */
  image?: string;
  /**​ 图片尺寸，支持数字（单位px）或字符串 */
  imageSize?: number | string;
  /**​ 描述文字，传空时不显示 */
  description?: string;
  /**​ 自定义根元素类名 */
  customClass?: string;
}
const props = withDefaults(defineProps<Props>(), {
  image: defaultEmptyImg,
  imageSize: 80,
  description: '暂无数据',
  customClass: '',
  customStyle: () => ({})
});
</script>