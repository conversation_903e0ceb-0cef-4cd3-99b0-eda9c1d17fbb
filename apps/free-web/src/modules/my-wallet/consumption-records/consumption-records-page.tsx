import { createComponent, getQuery } from '@skynet/shared'
import { Back } from 'src/modules/common/back/back'
import ticketIcon from '../images/ticket.webp'
import noData from '../images/no-data.svg'
import { computed, onMounted, ref, watch, watchEffect } from 'vue'
import { useSwipe } from '@vueuse/core'
import { useMyWalletStore } from '../use-my-wallet-store'
import dayjs from 'dayjs'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { useStretchScroll } from 'src/lib/use-stretch-scroll'
import { goToAppNoStore } from 'src/lib/go-to-app'
import { isIos } from 'src/lib/ua'
type MyEarningPagesOptions = {
  props: {}
}
export const ConsumptionRecordsPage = createComponent<MyEarningPagesOptions>({
  props: {},
}, props => {
  const wrapper = ref<HTMLDivElement>()
  const loadMore = ref<HTMLDivElement>()
  const language = getQuery('language', 'en')
  const { t } = useLocale(language)
  const { consumptionList, getConsumptionList, gettingConsumption } = useMyWalletStore()

  const { isSwiping, direction, lengthY } = useSwipe(wrapper)

  const delta = computed(() => {
    if (isSwiping.value === false || direction.value !== 'up' || lengthY.value === 0 || gettingConsumption.value) return
    if (!loadMore.value) return Infinity
    // 如果 loadMore 在 viewport 下方，距离 viewport 还有 200px，就加载更多
    const rect = loadMore.value.getBoundingClientRect()
    const clientHeight = document.documentElement.clientHeight
    return rect.top - clientHeight
  })

  watch(() => wrapper.value, () => {
    if (wrapper.value) {
      useStretchScroll(wrapper.value)
    }
  })

  onMounted(() => {
    track('consumption_records', 'page', 'show')
    document.title = t('my_wallet_in.consumption_records')
    void getConsumptionList({ next: consumptionList.value.page_info.next })
  })

  watchEffect(() => {
    if (typeof delta.value === 'undefined') return
    if (delta.value < 200 && consumptionList.value.page_info.has_more) {
      void getConsumptionList({ next: consumptionList.value.page_info.next })
    }
  })

  const handleClick = (data: any) => {
    const deepLink = 'freereels://freereels.app/detail?id=' + data.id + '&episode_id=' + data.episode_id
    const universalLink = 'freereels://freereels.app/detail?id=' + data.id + '&episode_index=' + data.episode_index
    // 使用 JavaScript 超时机制，检测应用是否已安装，已安装跳转到app，未安装跳转到appStoreLink
    // 实现H5唤端功能，当IOS时，使用universalLink,当Android时使用 deepLink 唤端
    console.log('点击按钮', universalLink)
    goToAppNoStore(isIos() ? universalLink : deepLink)
  }

  return () => (
    <x-my-earning-page class="relative flex flex-col bg-[#fff] text-[var(--text-5)] overflow-hidden size-full">
      <Back title={t('my_wallet_in.consumption_records')} isWhite={false} class="bg-[#fff] text-[#090609] shrink-0" />
      <x-history class="relative z-up flex flex-col flex-1 bg-[#fff] rounded-t-lg overflow-hidden">
        <x-history-area class="flex flex-col flex-1 w-full overflow-hidden">
          {
            consumptionList.value?.items?.length > 0
              ? (
                  <x-history-list class="flex flex-col text-[var(--text-1)] overflow-hidden size-full pt-2">
                    <x-scroll-wrapper class="block share-bottom-5" style="-webkit-overflow-scrolling: touch;" ref={wrapper}>
                      {
                        consumptionList.value.items.map(consumption => (
                          <x-history-item onClick={() => handleClick(consumption.business_data)} class="flex justify-between items-center p-3">
                            <div class="flex items-center h-full">
                              <div class="flex items-center mr-[7px] rounded-lg overflow-hidden">
                                <img class="w-[3.75rem] h-20" src={consumption.business_data.cover} />
                              </div>
                              <div class="flex flex-col h-full max-w-[32ex] flex-1 text-sm justify-between">
                                <div>
                                  <div class="line-clamp-2 text-base text-[var(--text-1)]">{consumption.business_data.name}</div>
                                  <div class="line-clamp-2 text-xs text-[var(--text-3)]">{t('my_wallet_in.EPnum', { num: consumption.business_data.episode_index })}</div>
                                </div>
                                <div class="text-[var(--text-3)] text-xs truncate">{dayjs.unix(consumption.pay_finish_time).format('YYYY-MM-DD')}</div>
                              </div>
                            </div>
                            <div class="flex flex-col flex-2 max-w-[16ex] gap-y-2 text-sm items-end">
                              <div class="flex items-center">
                                <span class="mr-1 line-clamp-2 text-sm text-[var(--text-1)]">-{consumption.amount}</span>
                                <img src={ticketIcon} class="w-4 h-4" />
                              </div>
                              {/* <div class="text-xs text-[var(--text-3)]">Expires on</div>
                          <div class="max-w-[16ex] flex items-center">
                            <span class="mr-1 text-[var(--text-3)] text-xs line-clamp-2">{dayjs.unix(consumption.business_data.expire_time).format('YYYY-MM-DD')}</span>
                          </div> */}
                            </div>
                          </x-history-item>
                        ))
                      }
                      <x-load-more ref={loadMore} class="block w-full h-[1px] invisible" />
                      {
                        !consumptionList.value.page_info.has_more
                        && (
                          <x-limit-explain class="block pt-5 pb-3 w-full text-[var(--text-3)] text-center text-sm">
                            {t('my_wallet_in.Thats_all_for_now')}
                          </x-limit-explain>
                        )
                      }
                    </x-scroll-wrapper>
                  </x-history-list>
                )
              : (
                  <x-empty class="relative flex-1 w-full">
                    <div class="top-20 left-1/2 absolute flex flex-col justify-center -translate-x-1/2">
                      <img src={noData} class="w-40 h-40" />
                      <div class="text-[var(--text-4)] text-center">{t('coins.earnings.noRecords')}</div>
                    </div>
                  </x-empty>
                )

          }
        </x-history-area>
      </x-history>
    </x-my-earning-page>
  )
})

export default ConsumptionRecordsPage
