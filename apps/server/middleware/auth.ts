import { generate_k_device_hash } from '~/lib/device-id'
import { useApi } from '~/composables/useApi'
import type { User } from '~/modules/login/login';
export default defineNuxtRouteMiddleware(async (to, from) => {
  if (import.meta.client) return
  const config = useRuntimeConfig()
  const auth_params = useCookie('auth_params',
    {
      maxAge: 60 * 60 * 24 * 365,
      priority: 'high',
      secure: true,
      domain: config.public.cookieDomain,
      default: () => {
        return {
          user_id: undefined,
          user_type: 0,
          name: '',
          icon: '',
          auth_key: '',
          auth_secret: '',
          is_new_user: false,
        }
      }
    },
  )
  const k_device_hash = useCookie('k_device_hash',
    {
      maxAge: 60 * 60 * 24 * 365,
      priority: 'high',
      secure: true,
      domain: config.public.cookieDomain,
    })
  if (!k_device_hash.value) {
    const headers = useRequestHeaders()
    const userAgent = headers['user-agent'] || ''
    k_device_hash.value = generate_k_device_hash(userAgent)
  }
  console.info('user_id:', auth_params.value.user_id)
  console.info('to.path:', to.path)
  console.info('from.path:', from.path)
  if (!auth_params.value?.user_id) {
    const res = await useApi().request<User>('/h5-api/anonymous/login', {
      method: 'POST',
      body: {
        device_id: k_device_hash.value,
      },
      skipAuth: true,
    })
    if (!res) {
      console.info('登录失败')
      return
    }
    auth_params.value = {
      user_id: res.user_id,
      user_type: res.user_type,
      name: res.name,
      icon: res.icon,
      auth_key: res.auth_key,
      auth_secret: res.auth_secret,
      is_new_user: true,
    }
    const nuxtApp = useNuxtApp()
    nuxtApp.provide('auth_params', auth_params.value)
    console.info('nuxtApp provide auth_params', nuxtApp.$auth_params)
  }
});
