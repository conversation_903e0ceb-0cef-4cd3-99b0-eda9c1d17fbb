import { computed, ref, watch } from 'vue'
import { useLocale } from '../i18n/i18n'
import { useSeriesItem } from './use-series-item'

export function useSubtitles(series_id: string) {
  const { getCurrentEpisode } = useSeriesItem(series_id)
  const { currentLocale } = useLocale()
  const subtitles = ref<{ start: number, end: number, text: string }[]>([])
  const subtitleFileList = ref<M.Subtitle[]>([])
  const srtFileUrl = computed(() => {
    const subtitle = subtitleFileList.value.find(s => s.language === currentLocale.value.language)
    return subtitle?.subtitle
  })
  const currentEpisode = computed(() => getCurrentEpisode())

  watch(() => currentEpisode.value, episode => {
    subtitleFileList.value = episode?.subtitle_list ?? []
  }, { immediate: true })
  watch(() => srtFileUrl.value, async url => {
    if (url) {
      const res = await fetch(url)
      const content = await res.text()
      parseSRT(content)
    }
  })
  return {
    subtitles,
  }
  function parseSRT(content: string) {
    const regex = /(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n([\s\S]*?)(?=\n\n|$)/g
    const matches = content.matchAll(regex)
    const result = []
    // @ts-expect-error never mind
    for (const match of matches) {
      const start = parseTime(match[1])
      const end = parseTime(match[2])
      const text = match[3].trim()
      result.push({ start, end, text })
    }
    subtitles.value = result
  }
  // 将时间字符串转换为秒数
  function parseTime(timeString: string) {
    const parts = timeString.split(':')
    const seconds = parseFloat(parts[2].replace(',', '.'))
    return (+parts[0]) * 3600 + (+parts[1]) * 60 + seconds
  }
}
