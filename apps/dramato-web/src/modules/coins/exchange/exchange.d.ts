export type MyExchange = {
  id: number
  desc: string
  exchange_amount: string
  amount: number
  amount_str: string
  gift_card_secret?: string
  account?: string
  created: number
  type: string
}

export type ExchangeCard = {
  id: number
  name: string
  image_url: string
  can_choose: boolean
  gift_list: GiftType[]
}

export type ExchangeDetails = {
  exchange_list: ExchangeCard[]
  coin_info: string
  show_help_dialog: boolean
}

export type CoinsGift = {
  welfare_id: number
  welfare_key: string
  title: string
  sub_title: string
  task_total_gold: number
  status: number // 状态 1 可以兑换 2 超过限制无法兑换
  button_text: string
  user_diamonds: number
  icon: string
  coins: number
  button_can_click: boolean
}

export type GiftType = {
  id: string// gift_id
  name: string// gift_name
  image_url: string// 图片地址
  amount: string// 礼品金额
  user_coins: number// 用户金币
  gift_coins: number// 礼品金币
  ratio: number// 兑换比例
  can_exchange: boolean// 是否能兑换
  button_tips?: string
  type: string // 礼品类型 'amazon' | 'paypal' | 'spotify' | 'mercado_pago' | 'ovo'
  button_txt?: string
  button_link?: string
  tags?: string[]
  cash_success_btn_txt?: string
  cash_success_btn_link?: string
  task_id?: number
}
