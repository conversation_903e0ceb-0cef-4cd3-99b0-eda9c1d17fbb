import { createComponent } from '@skynet/shared'
import { Button, CreateForm } from '@skynet/ui'
import { set } from 'lodash-es'
import { ref } from 'vue'
import { SendCodeButton } from '../session/send-code-button'
import { useMe } from './use-me'
type ChangePhoneNumberOptions = {
  props: {}
}
export const ChangePhoneNumber = createComponent<ChangePhoneNumberOptions>({
  props: {},
}, props => {
  type Data = {
    code: string
  }
  const formData = ref({
    code: '',
  })
  const Form = CreateForm<Data>()
  const onSave = () => {
    console.log(formData.value)
  }
  const error = ref({})
  const { me } = useMe()
  const step = ref(1)
  const onNext = () => {
    step.value = 2
  }
  return () => (
    <x-change-phone-number class="block">
      <Form
        class="cp-form [&_x-label]:w-[100px] flex-col"
        hideEmptyError onChange={(path, value) => set(formData.value, path, value)} onSubmit={onSave} error={error.value} data={formData.value}
        items={[
          step.value === 1 && ['原手机号', 'old_phone', {
            type: 'custom',
            render: () => (
              <div>
                <span class="leading-[38px]">{obfuscatePhone(me.value.phone)}</span>
              </div>
            ),
          }, { class: 'cp-form-item' }],
          step.value === 1 && [<>手机验证码<span class="text-[var(--brand-6)]">*</span></>, 'code', {
            type: 'text', class: 'leading-[22px] py-2 px-4 bg-[#faf7fa] rounded-lg',
            placeholder: '请输入剧名', suffix: () => (
              <SendCodeButton class="absolute right-4 top-1/2 -translate-y-1/2 transform text-[var(--brand-6)]" phone={me.value.phone} />
            ),
            maxlength: 15,
            wrapperClass: 'relative w-full grow',
          }, { class: 'cp-form-item' }],
          step.value === 2 && ['新手机号', 'phone', {
            type: 'text', class: 'leading-[22px] py-2 px-4 bg-[#faf7fa] rounded-lg',
            placeholder: '请输入新手机号',
            maxlength: 15,
            wrapperClass: 'relative w-full grow',
          }, { class: 'cp-form-item' }],
          step.value === 2 && [<>手机验证码<span class="text-[var(--brand-6)]">*</span></>, 'code2', {
            type: 'text', class: 'leading-[22px] py-2 px-4 bg-[#faf7fa] rounded-lg',
            placeholder: '请输入剧名', suffix: () => (
              <SendCodeButton class="absolute right-4 top-1/2 -translate-y-1/2 transform text-[var(--brand-6)]" phone={me.value.phone} />
            ),
            maxlength: 15,
            wrapperClass: 'relative w-full grow',
          }, { class: 'cp-form-item' }],
        ]}
        actionClass="[&_x-label]:hidden"
        actions={() => <Button disabled={!formData.value.code} class="btn btn-md" onClick={onNext}>下一步</Button>}
      />
    </x-change-phone-number>
  )
})

export default ChangePhoneNumber

function obfuscatePhone(phone: string) {
  return phone.replace(/(\d{3})(\d{6})(\d{2})/, '$1 **** **$3')
}
