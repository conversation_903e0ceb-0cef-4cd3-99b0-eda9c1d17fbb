import { createComponent } from '@skynet/shared'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useSeriesPackageAddFormStore } from './use-series-package-add-form-store'
import { cloneDeep, set } from 'lodash-es'
import { Button, CreateTableOld, DateTime, Icon, openDialog, TableColumnOld } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import ShortDrama from '../short-drama/short-drama-page'
import { ref } from 'vue'
import { listingStatusList } from '../short-drama/short-drama-api'
import { Uploader } from '../common/uploader/uploader'

type Step2BatchUploadFormOptions = {
  props: {}
}
export const Step2BatchUploadForm = createComponent<Step2BatchUploadFormOptions>({
  props: {},
}, props => {
  const {
    formData,
    Form,
    curTab,
    twoStepError,
  } = useSeriesPackageAddFormStore()

  const isUploadingExcel = ref(false)
  const isUploadedExcelFail = ref(false)

  return () => (
    <Form
      class="flex flex-1 flex-col gap-y-3"
      hasAction={false}
      error={twoStepError.value}
      onChange={(path, value) => {
        set(formData.value || {}, path, value)
      }}
      items={[
        [
          requiredLabel('上传指定生效短剧'),
          curTab.value === 'ios_config' ? 'package_config.package_type_2_config.ios_url' : 'package_config.package_type_2_config.android_url',
          {
            type: 'custom',
            render: () => (
              <x-upload-cover class="grid gap-y-2">
                <Uploader
                  accept="xlsx"
                  maxsize={1024 * 1024 * 10}
                  disabled={formData.value.needDisabled}
                  class="h-[100px] w-full cursor-pointer overflow-hidden rounded-md border border-dashed"
                  beforeUpload={() => {
                    if (isUploadingExcel.value || formData.value.needDisabled) {
                      return Promise.resolve(false)
                    }
                    isUploadingExcel.value = true
                    return Promise.resolve(isUploadingExcel.value)
                  }}
                  onUploadSuccess={d => {
                    if (!formData.value.package_config) {
                      formData.value.package_config = {}
                    }
                    if (!formData.value.package_config.package_type_2_config) {
                      formData.value.package_config.package_type_2_config = {}
                    }
                    formData.value.package_config.package_type_2_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url'] = d.temp_path
                    isUploadingExcel.value = false
                    isUploadedExcelFail.value = false
                  }}
                  onUploadFailed={() => {
                    isUploadingExcel.value = false
                    isUploadedExcelFail.value = true
                  }}
                  isImage={true}
                  multiple={false}
                  uploadUrl="/seriespackage/import"
                  showFileList
                >
                  <span class="flex size-full items-center justify-center">{
                    isUploadingExcel.value
                      ? (
                          <div class="flex flex-col items-center gap-y-2">
                            <span class="loading loading-spinner size-4" />
                            <span>上传中</span>
                          </div>
                        )
                      : '上传文件'
                  }
                  </span>
                </Uploader>
              </x-upload-cover>

            ),
          },
          {
            class: 'col-span-1',
            hint: (
              <x-upload-cover-tip class="mt-2 flex flex-col gap-y-1 text-sm text-gray-600">
                { formData.value.package_config && formData.value.package_config.package_type_2_config && formData.value.package_config.package_type_2_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url']
                && (
                  <span class="flex items-center gap-x-2">
                    <a
                      href={'https://static.mydramawave.com/' + formData.value.package_config.package_type_2_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url']}
                      target="_blank">{formData.value.package_config.package_type_2_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url']}
                    </a>
                    <Icon name="ant-design:delete-filled" class="cursor-pointer" onClick={() => {
                      if (formData.value.needDisabled) {
                        return
                      }
                      if (!formData.value.package_config) {
                        formData.value.package_config = {}
                      }
                      if (!formData.value.package_config.package_type_2_config) {
                        formData.value.package_config.package_type_2_config = {}
                      }
                      formData.value.package_config.package_type_2_config[curTab.value === 'ios_config' ? 'ios_url' : 'android_url'] = ''
                    }}
                    />
                  </span>
                )}
                { isUploadedExcelFail.value && <span class="text-red-500">上传失败, 请重试</span>}
                <x-tip>
                  提示：
                </x-tip>
                <x-tip>
                  支持 xlsx 格式，大小限制 10M 以内 <a href="https://static.mydramawave.com/seriespackage/config/1901901902288101376.xlsx" class="text-primary" target="_blank">上传模版.xlsx</a>
                </x-tip>
              </x-upload-cover-tip>
            ),
          },
        ],
      ]}
      data={formData.value}
    />
  )
})

export default Step2BatchUploadForm
