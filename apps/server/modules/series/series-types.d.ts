declare namespace M {
  interface SearchKeywords {
    keyword: string
    highlight: string
  }
  interface Dramato {
    id: string // ID
    pay_mode: 'IAP' | 'IAA' | string // 支付模式
    name: string // 名称
    desc: string // 简介
    cover: string // 封面
    view_count: number // 观看数
    follow_count: number // 追剧数
    episode_count: number // 集数
    finish_status: 1 | 2 | number// 更新状态: 1 - 未完结； 2 - 已完结
    following: boolean // 是否追剧
    labels: string // 分类信息
    custom_tag: string[] // 自定义标签
    series_tag: string[] // 分类信息数组形式
    start_episode: number // 开始集数
    view_episode: number // 观看到哪一集
    view_episode_ms: number // 观看集对应到多少毫秒
    episode_price: number // 本剧每集金币价格
    lang: string // 用户设置的字幕语言
    episode_list: Episode[] // 剧集列表
    episode?: Episode // 当前剧集
    next_episode: Episode // 下一集
    view_time: number // 时间戳(秒)
    follow_time: number // 时间戳(秒)
    free: boolean // 是否免费
    discount: string // 折扣信息
    discount_price: number // 折扣价格
    biz_tags: BizTag[] // 卖点标签
    content_tags: string[] // 内容标签
    hot_score: string // 热度值
    free_start: number // 免费开始时间
    free_end: number // 免费结束时间
    info: Info // 附加信息
    rank: Rank // 榜单信息
    can_reserve: boolean // 是否可以预约
    reserved: boolean // 是否已预约
    cover_bgcolor: string // 封面图底色
    release_round: number // 首发
    highlight?: {
      title: string
    }
  }
  interface Episode {
    audio: string[]
    cover: string
    duration: number
    episode_price: number
    external_audio_h264_m3u8: string
    external_audio_h265_m3u8: string
    id: string
    index: number
    m3u8_url: string
    name: string
    original_audio_language: string
    playload: string
    region: unknown
    subtitle_list: Subtitle[]
    unlock: boolean
    update_time: number
    user_unlocked: boolean
    video_type: string
    video_url: string
    mp4_urls?: Mp4Url
    caption_urls?: CaptionUrl[]
    new?: true
  }
  interface Subtitle {
    language: string
    type: string
    subtitle: string
    display_name: string
  }
}

declare namespace Api {
  namespace Series {
    type GetSeriesItemResponse = ApiResponse<{
      info: M.Dramato
    }>
    type ViewDramaRequest = {
      series_id: string// 剧ID
      episode_id: string// 集ID
    }
    type UnlockEpisodeRequest = {
      series_id: string// 剧ID
      episode_id: string// 集ID
    }
    type UnlockEpisodeResponse = ApiResponse<{
      id: string
      name: string
      cover: string
      video_url: string
      index: number
      unlock: boolean
      duration: number
      video_type: string
      new: boolean
      update_time: number
      coupon_unlock: boolean // true-兑换券解锁
    }>
  }
}
