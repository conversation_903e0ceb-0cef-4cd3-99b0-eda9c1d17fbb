import { createComponent } from '@skynet/shared'
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

type LandingPageOptions = {
  props: {}
}

export const LandingPage = createComponent<LandingPageOptions>({
  props: {},
}, () => {
  const route = useRoute()
  const router = useRouter()

  onMounted(() => {
    const af_dp = route.query.af_dp?.toString()
    if (!af_dp) {
      // 如果没有af_dp参数,重定向到首页
      void router.replace('/')
      return
    }

    // 解析af_dp参数获取series_id
    const series_id = af_dp // 这里假设af_dp就是series_id,根据实际情况可能需要其他解析逻辑

    // 重定向到series页面
    const queryParams = new URLSearchParams({
      return_to: '/',
      ...route.query, // 保留当前路由参数
    }).toString()

    window.location.href = window.location.origin + `/series/${series_id}?${queryParams}`

    // void router.replace({
    //   path: `/series/${series_id}`,
    //   query: {
    //     return_to: '/',
    //     ...route.query,
    //   },
    // })
  })

  return () => null // 这是一个纯逻辑组件,不需要渲染内容
})

export default LandingPage
