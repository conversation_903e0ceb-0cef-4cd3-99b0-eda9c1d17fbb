import { ref } from 'vue'
import { apiGetAliwallexCardOrder, apiGetBanner, apiGetProductList } from './activity-payment-api'
import { getQueries, tryParseJson } from '@skynet/shared'
import { AxiosError } from 'axios'
import { isEmpty } from 'lodash-es'
import { useRouter } from 'vue-router'

const user = ref<{
  user_id: string
  user_avatar: string
}>()
const refereeAvatar = ref('')
const bonus = ref(0)
const status = ref(0) // 0: 未充值 1: 充值并分享成功 2: 好友充值成功未领取 3: 好友充值成功已领取
const storeUser = localStorage.getItem('user')
if (storeUser) {
  user.value = tryParseJson(storeUser, {
    user_id: '',
    user_avatar: '',
  })
}

export const useActivityPayment = () => {
  const router = useRouter()
  const productList = ref<M.ActivityPayment.PaymentResponse>()
  const bannerList = ref<M.ActivityPayment.Banner[]>([])
  const { country_code } = getQueries({
    country_code: '',
    share_success: '0',
  })

  const getProductList = async () => {
    const res = await apiGetProductList({ uid: user.value?.user_id ? Number(user.value?.user_id) : 0, country_code }).catch(err => {
      console.log('err', err)
    })
    if (res?.code === 200) {
      productList.value = res.data
    }
    if (res?.code === 401) {
      void router.push('/login')
    }
  }

  const getReturnUrl = () => {
    const params = new URLSearchParams(location.search)
    params.set('uid', user.value?.user_id ?? '')
    return `${location.origin}/activity/recharge-success?${params.toString()}`
  }

  const activityAirwallexPay = async (id: number, shareId: string) => {
    try {
      if (!user.value?.user_id) return
      const returnUrl = getReturnUrl()
      await apiGetAliwallexCardOrder({ product_id: id, return_url: returnUrl, uid: user.value.user_id, referer_uid: shareId, source: 1 })
    } catch (error: unknown) {
      if ((error as AxiosError)?.response?.data && !isEmpty((error as AxiosError)?.response?.data)) {
        document.open()
        document.write((error as AxiosError).response?.data as string)
        document.close()
      }
    }
  }

  const getBanner = async () => {
    const res = await apiGetBanner()
    if (res?.code === 200) {
      bannerList.value = res.data?.banner_items || []
    }
  }

  return {
    user,
    productList,
    getProductList,
    activityAirwallexPay,
    refereeAvatar,
    bonus,
    bannerList,
    getBanner,
    status,
  }
}
