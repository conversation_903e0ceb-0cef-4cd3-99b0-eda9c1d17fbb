import { httpClient } from 'src/lib/http-client'
import { DramaItem } from 'src/modules/content-template/series-store.tsx'

export const apiGetSpendBoardMaterialList = (data: M.SpendBoardMaterial.params) =>
  httpClient.post<ApiResponse<M.SpendBoardMaterial.ListResponse>>('/material/task_list', data)

export const apiGetSpendBoardMaterialLink = (data: {
  series_key: string
}) =>
  httpClient.post<ApiResponse<{ url: string }>>('/material/task_list_res_download', data)

export const apiJuGenSpendBoardMaterialLink = (data: {
  series_key: string
}) =>
  httpClient.post<ApiResponse<boolean>>('/material/task_list_mat_gen', data)

export const apiGetSpendBoardMaterialDesc = (data: {
  series_key: string
}) =>
  httpClient.post<ApiResponse<{ desc: string }>>('/material/task_list_series_desc', data)

export const apiGetSpendBoardTask = (data: {
  series_key: string
  status: number
}) =>
  httpClient.post<ApiResponse<{ desc: string }>>('/material/spend_board_gettask', data)

export const apiGetTaskList = (data: M.SpendBoardMaterial.TaskRequest) =>
  httpClient.post<ApiResponse<M.SpendBoardMaterial.TaskListResponse>>('/material/deploy_task_list', data)

export const apiGetSeriesList = (data: M.SpendBoardMaterial.SeriesRequest) =>
  httpClient.post<ApiResponse<M.SpendBoardMaterial.SeriesResponse>>('/material/series_list', data)

export const apiQueryByKeywords = (data: {
  drama_name?: string
}) => {
  return httpClient.post<ApiResponse<{ list: DramaItem[] }>>('/material/query_list_by_drama_name', data)
}

export const apiResourceQueryByKeywords = (data: {
  resource_name?: string
}) => {
  return httpClient.post<ApiResponse<{ list: {
    resource_id: number
    resource_name: string
  }[] }>>('/material/query_list_by_resource_name', data)
}

export const apiGetDeployCalendar = (data: {
  page_index: number
  page_size: number
}) =>
  httpClient.post<ApiResponse<M.SpendBoard.DeployCalendar>>('/material/deploy_calendar', data)
