declare namespace M {
  namespace Rank {
    interface params {
      pid?: number
      type?: number
      app_id?: number
      language_version?: string
    }

    interface RankInfo {
      id?: number
      name?: string
      priority?: number
      type?: number
    }

    interface Rank extends CreateRank {
      state: number
      updated: number
      create_user_name: string
      created: number
      update_user_name: string
    }

    interface ListResponse {
      list: ListItem[]
      total: number
    }

    interface PriceConfig {
      c: string[]
      v: number
    }

    interface SeriesInfo {
      name?: string
      series_id?: string
      hot?: string
    }

    interface CreateRank {
      pid?: number
      id?: number
      auto_type?: number
      type?: number
      name?: string
      theme_img?: string
      theme_text_color?: string
      theme_bg_color?: string
      priority?: number
      size?: number
      status?: number
      tag_id_list?: string[]
      series_info?: SeriesInfo[]
      series_count?: number
    }
  }
}
