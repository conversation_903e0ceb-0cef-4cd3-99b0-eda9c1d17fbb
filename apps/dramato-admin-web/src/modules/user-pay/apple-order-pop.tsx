import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, showFailToast, transformNumber } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { useUserPay } from './use-user-pay'
import { ElButton, ElForm, ElFormItem, ElInput } from 'element-plus'
import { apiFetchAppleOrder } from './user-pay-api'
import dayjs from 'dayjs'
export const AppleOrderPop = createComponent(null, () => {
  interface ResultData {
    expires_date: number
    original_txn_id: string
    out_txn_id: string
    product_id: string
    revocation_date: number
    success_time: number
  }
  const {
    closeOrderPopupModal,
    orderForm,
    isUpdating,
    onSave,
  } = useUserPay()
  const formRules = z.object({
    user_id: z.number().min(1, '请输入用户ID'),
    out_txn_id: z.string().min(1, '请输入订单ID'),
    sku_id: z.string().min(1, '请输入sku_id'),
    purchase_token: z.string().min(1, '请输入支付渠道token'),
  })
  const fetchForm = ref({
    out_txn_id: '',
  })
  const searched = ref(false)

  const loading = ref(false)

  const resultData = ref<ResultData>({
    expires_date: 0,
    original_txn_id: '',
    out_txn_id: '',
    product_id: '',
    revocation_date: 0,
    success_time: 0,
  })

  const fetchAppleOrder = async () => {
    if (!fetchForm.value.out_txn_id) {
      showFailToast('请输入订单号')
      return
    }
    const data = {
      out_txn_id: fetchForm.value.out_txn_id,
    }
    loading.value = true
    try {
      const res = await apiFetchAppleOrder(data)
      resultData.value = res?.data || {}
      searched.value = true
    } catch (e) {
      resultData.value.out_txn_id = ''
    } finally {
      searched.value = true
      loading.value = false
    }
  }
  onMounted(() => {
    searched.value = false
  })

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <div class="flex gap-x-2 min-h-[200px]">
          <ElForm labelWidth={120}>
            <ElFormItem label="订单号">
              <div class="flex gap-x-2">
                <ElInput class="w-[200px]" v-model={fetchForm.value.out_txn_id} />

                <ElButton loading={loading.value} type="primary" onClick={() => {
                  void fetchAppleOrder()
                }}>查询</ElButton>
              </div>
            </ElFormItem>
            {searched.value && !resultData.value.out_txn_id && (
              <div class="flex flex-col gap-y-2">
                <div class="text-center text-gray-500">订单不存在</div>
              </div>
            )}
            {resultData.value.out_txn_id && (
              <div>
                <ElFormItem label="三方单号">
                  <div>{resultData.value.out_txn_id}</div>
                </ElFormItem>
                <ElFormItem label="原单号">
                  <div>{resultData.value.original_txn_id}</div>
                </ElFormItem>
                <ElFormItem label="订单完成时间">
                  <div>{dayjs(resultData.value.success_time * 1000).format('YYYY-MM-DD HH:mm:ss')}</div>
                </ElFormItem>
                <ElFormItem label="订阅过期时间">
                  <div>{dayjs(resultData.value.expires_date * 1000).format('YYYY-MM-DD HH:mm:ss')}</div>
                </ElFormItem>
                <ElFormItem label="是否已退款">
                  <div>{resultData.value.revocation_date ? dayjs(resultData.value.revocation_date * 1000).format('YYYY-MM-DD HH:mm:ss') : '否'}</div>
                </ElFormItem>
                <ElFormItem label="sku_id">
                  <div>{resultData.value.product_id}</div>
                </ElFormItem>
              </div>
            )}
          </ElForm>
        </div>
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <ElButton  class="btn  btn-sm" onClick={closeOrderPopupModal.value}>关闭</ElButton>
      </div>
    </>
  )
})
