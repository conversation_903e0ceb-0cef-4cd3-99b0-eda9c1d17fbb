import { httpClient, HttpRequestConfig } from 'src/lib/http-client'

export const apiGetMaterialList = (data: {
  media: string
  order_column: string
  order_type: string
  page: number
}, config?: HttpRequestConfig) =>
  httpClient.post<ApiResponse<{
    records: M.IDramaMaterial[]
    total: number
    page: number
    page_size: number
  }>>('/dataeye/material', data, config)

export const apiGetTargetedMaterialList = (data: {
  media: string
  order_column: string
  order_type: string
  page: number
  page_size: number
}, config?: HttpRequestConfig) =>
  httpClient.post<ApiResponse<{
    records: M.IDramaMaterial[]
    total: number
    page: number
  }>>('/dataeye/material_by_key', data, config)
