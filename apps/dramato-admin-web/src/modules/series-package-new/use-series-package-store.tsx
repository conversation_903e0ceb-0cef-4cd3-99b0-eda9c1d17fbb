/* eslint-disable @typescript-eslint/no-explicit-any */
import { mc } from '@skynet/shared'
import { Button, Checkbox, CreateForm, CreateTableOld, DateTime, Icon, openDialog, showAlert, TableColumnOld } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiCreateSeriesPackage, apiDeleteSeriesPackage, apiListSeriesPackage, apiUpdateSeriesPackagePriority, apiUpdateSeriesPackageState } from './series-package-api'
import { cloneDeep, omit } from 'lodash-es'
import SeriesPackageAddForm from './series-package-add-form'
import { dialogMainClass } from './series-package-page'
import { useSeriesPackageAddFormStore } from './use-series-package-add-form-store'
import { get_k_sso_token } from 'src/lib/device-id'
import qs from 'qs'
import dayjs from 'dayjs'

type ListParams = Omit<Api.SeriesPackage.ListQuest.query, 'page_info'>
const SearchForm = CreateForm<ListParams>()
const listParams = ref<ListParams>({})
const Table = CreateTableOld<M.SeriesPackage.Details>()
const list = ref<M.SeriesPackage.Details[]>([])
const page = ref<number>(1)
const pageSize = ref<number>(20)
const total = ref<number>(1)
const loading = ref<boolean>(false)
const currentPriority = ref<number>(0)

const isCommon = ref<boolean>(true)

const search = async (_page?: number, _import_types?: number[]) => {
  _page = _page || page.value + 1
  loading.value = true
  if (listParams.value.sort_desc) {
    listParams.value.sort = listParams.value.sort_desc.split('-')[0]
    listParams.value.sort_type = listParams.value.sort_desc.split('-')[1]
  }
  checkedItems.value = []
  const res = await apiListSeriesPackage({
    ...listParams.value,
    import_types: _import_types && _import_types.length > 0 ? _import_types : isCommon.value ? [1, 2, 3, 4] : [5],
    page_info: { page_index: _page, page_size: pageSize.value },
  })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.total || 0
  page.value = _page
}

const copyBtn = async (row: M.SeriesPackage.Details) => {
  try {
    let d = row
    d = {
      ...omit(d, ['id', 'state']),
      package_name: d.package_name + '副本',
    }
    await apiCreateSeriesPackage(d)
    showAlert('复制成功')
    void search(page.value)
  } catch (error: any) {
    showAlert(error.response.data.message || error.response.data.err_msg || '复制失败', 'error')
  }
}

const checkedItems = ref<number[]>([])

const isAll = () => {
  return list.value.every(item => checkedItems.value.includes(item.id as number))
}

const columns = computed(() => [
  [
    () => (
      <Checkbox
        label=""
        modelValue={isAll()}
        onUpdate:modelValue={(value: unknown) => {
          if (value) {
            checkedItems.value = list.value.map(item => item.id as number)
          } else {
            checkedItems.value = []
          }
        }}
      />
    ),
    row => {
      const id = row.id as number
      return (
        <Checkbox
          label=""
          disabled={!!!id}
          modelValue={checkedItems.value.includes(id)}
          onUpdate:modelValue={(value: unknown) => {
            if (value) {
              if (!checkedItems.value.includes(id)) {
                checkedItems.value.push(id)
              }
            } else {
              const rowIndex = checkedItems.value.findIndex(i => i === id)
              if (rowIndex !== -1) {
                checkedItems.value.splice(rowIndex, 1)
              }
            }
          }}
        />
      )
    },
    { class: mc('w-[60px]', isCommon.value ? '' : '') },
  ],
  [isCommon.value ? '剧包ID' : '集合ID', 'id', { class: mc('w-[100px]', isCommon.value ? '' : '') }],
  [isCommon.value ? '剧包名称' : '集合名称', 'package_name', { class: mc('w-[200px]', isCommon.value ? '' : '') }],
  ['集合方式', row => ['', '正选 ', '反选 '][row.package_config?.package_type_5_config?.collection_type || 0], { class: mc('w-[100px]', isCommon.value ? 'hidden' : '') }],
  ['剧包数量', 'item_count', { class: mc('w-[100px]', isCommon.value ? '' : '') }],
  ['应用场景', row => ['', '商业化策略', '付费卡点策略', '首页内容', '活动', '运营位弹窗'][row.scene_type || 0], { class: mc('w-[130px]', isCommon.value ? '' : 'hidden') }],
  ['添加方式', row => ['', '手动导入', '批量上传', '筛选导入', '指定范围'][row.import_type || 0], { class: mc('w-[100px]', isCommon.value ? '' : 'hidden') }],
  ['备注', 'remark', { class: 'w-[120px]' }],
  ['优先级', row => (
    <x-input class="flex flex-row items-center gap-x-2">
      <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
        <input
          type="number"
          class={mc('grow w-100px')}
          value={row.priority}
          disabled={!row.isEdit}
          // ant-design:edit-filled
          onFocus={() => {
            currentPriority.value = +(row.priority || '')
          }}
          onInput={(e: Event) => {
            currentPriority.value = +((e.target as HTMLInputElement).value || '')
          }}
          onKeydown={(e: KeyboardEvent) => {
            if (e.key !== 'Enter') {
              return
            }
            if (currentPriority.value === row.priority) {
              return
            }
            void apiUpdateSeriesPackagePriority({
              id: row.id || 0,
              priority: currentPriority.value,
            }).then(() => {
              void search(page.value)
            })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
          }}
          onBlur={() => {
            if (currentPriority.value === row.priority) {
              return
            }
            void apiUpdateSeriesPackagePriority({
              id: row.id || 0,
              priority: currentPriority.value,
            }).then(() => {
              void search(page.value)
            })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
          }}
        />
      </label>
      <Icon name="ant-design:edit-filled" class="size-4 cursor-pointer" onClick={() => {
        row.isEdit = true
      }}
      />
    </x-input>
  ), { class: mc('w-[200px]', isCommon.value ? '' : 'hidden') }],
  ['创建时间', row => (<DateTime value={(row?.created || 0) * 1000} />), { class: 'w-[150px]' }],
  ['创建人', 'created_operator_name', { class: 'w-[150px]' }],
  ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: 'w-[150px]' }],
  ['更新人', 'updated_operator_name', { class: 'w-[200px]' }],
  [<span class="px-3">操作</span>, row => (
    <x-hide-when-in-dialog class="flex gap-x-2">
      <Button class="btn btn-xs" onClick={() => {
        const { formData, curStep, closeAddDialog } = useSeriesPackageAddFormStore()
        curStep.value = 1
        formData.value = cloneDeep(row)
        delete formData.value.state
        delete formData.value.created
        delete formData.value.updated
        delete formData.value.created_operator_name
        delete formData.value.updated_operator_name
        delete formData.value.created_operator_id
        delete formData.value.updated_operator_id
        delete formData.value.deleted
        formData.value.import_type = isCommon.value ? formData.value.import_type : 5
        closeAddDialog.value = openDialog({
          title: '编辑剧包',
          body: () => <SeriesPackageAddForm />,
          mainClass: dialogMainClass,
          customClass: '!w-[1000px] overflow-hidden',
        })
      }}>
        编辑
      </Button>
      <Button
        class="btn btn-error btn-xs text-white"
        disabled={row.id === 10000}
        onClick={() => {
          const hideDeleteDialog = openDialog({
            title: '删除',
            mainClass: 'pb-0 px-5',
            body: (
              <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                <x-delete-episode-body>确认删除 剧包【{row.package_name}】吗？</x-delete-episode-body>
                <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                  <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                  <button class="btn btn-primary btn-sm" onClick={() => {
                    void apiDeleteSeriesPackage({
                      id: row.id || 0,
                    })
                      .then(() => {
                        void search(page.value)
                        hideDeleteDialog()
                      })
                      .catch((error: any) => {
                        showAlert(error.response.data.message, 'error')
                      })
                  }}
                  >确定
                  </button>
                </x-delete-episode-footer>
              </x-delete-episode-confirm-dialog>
            ),
          })
        }}
      >
        删除
      </Button>
      <Button class="btn btn-success text-white btn-xs" onClick={() => copyBtn(row)}>
        复制
      </Button>
    </x-hide-when-in-dialog>
  ), {
    class: 'w-[200px] hide-when-in-dialog',
  },
  ],
] as TableColumnOld<M.SeriesPackage.Details>[])

const exportExcel = async () => {
  const sort = {}
  const result = JSON.parse(JSON.stringify({ ...listParams.value }))

  try {
    const queryString = qs.stringify({ ...result, sort, ids: checkedItems.value.join(',') }).toString()
    // const queryString = encodeURIComponent({ ...listParams.value, sort})

    // 发送请求到服务器
    const response = await fetch(`${import.meta.env.VITE_DRAMA_API_URL}/seriespackage/export?${queryString}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        Device: 'Web',
        Token: get_k_sso_token() || '',
      },
    })
    // 检查响应是否成功
    if (!response.ok) {
      showAlert('下载失败', 'error')
      return
    }
    // 获取文件流
    const blob = await response.blob()
    // 检查 Blob 是否有效
    if (blob.size === 0) {
      showAlert('下载的文件为空', 'error')
      return
    }
    // 创建一个下载链接并触发下载
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `剧包信息${dayjs().format('MMDD')}.xlsx` // 设置下载的文件名
    document.body.appendChild(link)
    link.click() // 自动点击链接以开始下载
    document.body.removeChild(link) // 清理链接
    URL.revokeObjectURL(link.href) // 释放内存
  } catch (error) {
    showAlert('下载失败', 'error')
  }
}

export const useSeriesPackageStore = () => {
  return {
    SearchForm,
    listParams,
    Table,
    list,
    page,
    pageSize,
    total,
    loading,
    search,
    columns,
    exportExcel,
    checkedItems,
    isCommon,
  }
}
