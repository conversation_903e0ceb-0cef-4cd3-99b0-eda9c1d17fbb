import { shallowRef, type Component } from 'vue'
import type { InputRender } from './form-types'

type InputComponent = Component<{
  value?: unknown
  onInput?: null | ((value?: unknown) => void)
  error?: string
}>

const renders = shallowRef<Record<string, InputRender | InputComponent>> ({
  image: () => <div>尚未实现，请自行实现 useFormStore().renders.value.image </div>,
})
export function useFormStore() {
  return {
    renders,
  }
}

export function isInputRender(
  render: InputRender | InputComponent,
): render is InputRender {
  return typeof render === 'function'
}
