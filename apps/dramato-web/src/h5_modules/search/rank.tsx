import { createComponent } from '@skynet/shared'
import { SvgIcon } from '@skynet/ui'
type RankOptions = {
  props: {
    rank: number
  }
}
export const Rank = createComponent<RankOptions>({
  props: {
    rank: 0,
  },
}, props => {
  return () => (
    props.rank > 0 && (
      <x-rank class="absolute left-0 top-0 block h-8 w-5 rounded-br-lg rounded-tl-lg bg-[#434546]">
        <SvgIcon name={`rank${props.rank}`} class="size-full rounded-br-lg rounded-tl-lg" />
        {props.rank > 3 && <span class="absolute left-1/2 top-1/2 z-up -translate-x-1/2 -translate-y-1/2 text-sm font-bold text-[#FDFBFC]">{props.rank}</span>}
      </x-rank>
    )
  )
})

export default Rank
