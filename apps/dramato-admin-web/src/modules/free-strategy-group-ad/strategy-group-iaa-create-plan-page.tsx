import { createComponent } from '@skynet/shared'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, openDialog, transformInteger, transformNumber, transformTimestamp } from '@skynet/ui'
import dayjs from 'dayjs'
import { range, set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { apiSaveStrategyGroupAds } from './strategy-group-ad-api'
import { useStrategyGroupAdStore } from './strategy-group-ad-store'
import { useListParamsStore } from './use-list-params-store'

export const StrategyGroupIaaCreatePlanPage = createComponent(null, props => {
  const { formData, Form, resetTaskFormData, stepTwoError, stepThreeError, validateAllSteps, stepOneError } = useStrategyGroupAdStore()

  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')
  const router = useRouter()
  const onSubmit = async () => {
    if (!validateAllSteps()) {
      openDialog({
        title: '出错提示',
        body: (
          <div>请检查表单<br />
            {JSON.stringify(stepOneError.value)}
            {JSON.stringify(stepTwoError.value)}
            {JSON.stringify(stepThreeError.value)}
          </div>
        ),
      })
      return
    }
    if (formData.value.user_config?.active_users_config?.start_days && formData.value.user_config?.active_users_config?.end_days) {
      if (formData.value.user_config?.active_users_config?.start_days > formData.value.user_config?.active_users_config?.end_days) {
        // 交换起止时间
        const temp = formData.value.user_config.active_users_config.start_days
        formData.value.user_config.active_users_config.start_days = formData.value.user_config.active_users_config.end_days
        formData.value.user_config.active_users_config.end_days = temp
      }
    }
    const response = await apiSaveStrategyGroupAds({
      ...formData.value,
      save_type: 1,
      pay_mode: route.path.includes('iaa') ? 'IAA' : 'IAP',
    })
    if (!response.data) return
    formData.value = response.data
    setTimeout(() => {
      void router.push({ path: route.path, query: { ...route.query, has_edit: 'false' } }).then(() => {
        const closeDialog = openDialog({
          title: '提示',
          body: (
            <div>保存成功
              <DialogFooter okText="返回列表页" onOk={() => {
                closeDialog()
                const { listParams, initListParams } = useListParamsStore()
                listParams.value = { ...initListParams }
                void router.push('/free-ad-strategy-group/iaa')
              }} cancelText="留在当前页" onCancel={() => closeDialog()}
              />
            </div>
          ),
        })
      })
    })
  }

  return () => (
    <div class="flex flex-col gap-4 p-8">
      <Form
        class="space-y-4 block"
        data={formData.value}
        error={stepThreeError.value}
        onChange={(path, value) => {
          if (['task_config.auto_up'].includes(path) && value === 'Invalid Date') {
            set(formData.value || {}, path, undefined)
            return
          }
          set(formData.value, path, value)
        }}
        onReset={() => resetTaskFormData(true)}
        onSubmit={onSubmit}
        items={[
          [
            requiredLabel('任务优先级(1最高)'),
            'task_config.priority',
            {
              type: 'select',
              options: range(1, 99).map(i => ({
                label: '' + i,
                value: i,
              })),
              disabled: isViewMode.value,
            },
            { transform: transformInteger, class: 'w-300px' },
          ],
          [
            requiredLabel('策略持续时间'),
            'task_config.duration',
            {
              type: 'number',
              placeholder: '填 -1 表示不限',
              suffix: '天',
              min: -1,
              disabled: isViewMode.value,
            },
            { transform: transformNumber, class: 'w-300px' },
          ],
          [
            requiredLabel('自动上架'),
            'task_config.auto_up',
            {
              type: 'radio',
              options: [
                { label: '是', value: 1, disabled: isViewMode.value },
                { label: '否', value: 0, disabled: isViewMode.value },
              ],
            },
            { transform: transformInteger },
          ],
          [
            '自动上架时间',
            'task_config.up_time',
            {
              type: 'datetime',
              min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              rawFormat: 'YYYY-MM-DD HH:mm:ss',
              class: 'w-[200px]',
              disabled: formData.value.task_config?.auto_up === 0 || isViewMode.value,
            },
            {
              transform: transformTimestamp,
              hint: (
                <x-upload-cover-tip class="mt-2 text-gray-600 text-sm flex flex-col gap-y-1">
                  <x-tip>
                    提示：这里设定的是<strong>北京时间</strong>, 不填写表示不限
                  </x-tip>
                </x-upload-cover-tip>
              ),
            },
          ],
        ]}
        actions={() => (
          <>
            <hr class="mb-4" />
            <div class="flex justify-between gap-x-2">
              <Button class="btn btn-sm" onClick={() => {
                void router.push({ path: `/free-ad-strategy-group/iaa/create/product?id=${formData.value.id}`, query: { ...route.query } })
              }} type="button"
              >上一步
              </Button>
              {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button>}
              {isViewMode.value ? null : <Button class="btn btn-primary btn-sm" type="submit">保存</Button>}
            </div>
          </>
        )}
      />

    </div>
  )
})
export default StrategyGroupIaaCreatePlanPage
