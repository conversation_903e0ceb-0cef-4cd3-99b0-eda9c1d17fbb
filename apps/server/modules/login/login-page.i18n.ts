import { mergeI18n } from 'src/h5_modules/i18n/i18n'
export const T = mergeI18n(
  {
    'en-US': {
      loginInPage: {
        facebookLogin: 'Sign in with Facebook',
        googleLogin: 'Sign in with Google',
        loginFailed: 'Login Failed',
        agreeMsg: 'If you continue, you agree to：',
        termsOfUse: 'Terms of Use',
        privacyPolicy: 'Privacy Policy',
      },
    },
    'ja-JP': {
      loginInPage: {
        facebookLogin: 'Facebookでログイン',
        googleLogin: 'Googleでログイン',
        loginFailed: 'ログインに失敗しました',
        agreeMsg: 'アカウントにログインすることで、以下を読んで同意したことになります：',
        termsOfUse: '利用規約',
        privacyPolicy: 'プライバシーポリシー',
      },
    },
    'ko-KR': {
      loginInPage: {
        facebookLogin: '페이스북으로 로그인',
        googleLogin: '구글로 로그인',
        loginFailed: '로그인 실패',
        agreeMsg: '계정에 로그인하면 다음을 읽고 동의한 것으로 간주됩니다:',
        termsOfUse: '이용 약관',
        privacyPolicy: '개인정보 보호정책',
      },
    },
    'es-ES': {
      loginInPage: {
        facebookLogin: 'Iniciar sesión con Facebook',
        googleLogin: 'Iniciar sesión con Google',
        loginFailed: 'Inicio de sesión fallido',
        agreeMsg: 'Al iniciar sesión, usted ha leído y aceptado:',
        termsOfUse: 'Términos de uso',
        privacyPolicy: 'Política de Privacidad',
      },
    },
    'pt-PT': {
      loginInPage: {
        facebookLogin: 'Fazer login com o Facebook',
        googleLogin: 'Fazer login com o Google',
        loginFailed: 'Falha no login',
        agreeMsg: 'Ao fazer login na conta, você leu e concorda com:',
        termsOfUse: 'Termos de uso',
        privacyPolicy: 'Política de privacidade',
      },
    },
    'vi-VN': {
      loginInPage: {
        facebookLogin: 'Đăng nhập bằng Facebook',
        googleLogin: 'Đăng nhập bằng Google',
        loginFailed: 'Đăng nhập thất bại',
        agreeMsg: 'Khi đăng nhập, bạn đã đọc và đồng ý với:',
        termsOfUse: 'Điều khoản sử dụng',
        privacyPolicy: 'Chính sách bảo mật',
      },
    },
    'th-TH': {
      loginInPage: {
        facebookLogin: 'เข้าสู่ระบบด้วยFacebook',
        googleLogin: 'เข้าสู่ระบบด้วยGoogle',
        loginFailed: 'การเข้าสู่ระบบไม่สำเร็จ',
        agreeMsg: 'เมื่อเข้าสู่ระบบบัญชี คุณได้อ่านและยอมรับ:',
        termsOfUse: 'ข้อกำหนดการใช้งาน',
        privacyPolicy: 'นโยบายความเป็นส่วนตัว',
      },
    },
    'id-ID': {
      loginInPage: {
        facebookLogin: 'Masuk dengan Facebook',
        googleLogin: 'Masuk dengan Google',
        loginFailed: 'Masuk Gagal',
        agreeMsg: 'Dengan masuk, Anda telah membaca dan menyetujui:',
        termsOfUse: 'Ketentuan Penggunaan',
        privacyPolicy: 'Kebijakan Privasi',
      },
    },
    'tl-PH': {
      loginInPage: {
        facebookLogin: 'Mag-sign in gamit ang Facebook',
        googleLogin: 'Mag-sign in gamit ang Google',
        loginFailed: 'Nabigo ang pag-login',
        agreeMsg: 'Sa pag-sign in sa iyong account, nabasa at sinang-ayunan mo:',
        termsOfUse: 'Mga Tuntunin sa Pag-gamit',
        privacyPolicy: 'Patakaran sa Privacy',
      },
    },
    'fr-FR': {
      loginInPage: {
        facebookLogin: 'Se connecter avec Facebook',
        googleLogin: 'Se connecter avec Google',
        loginFailed: 'Échec de la connexion',
        agreeMsg: 'En vous connectant à votre compte, vous avez lu et accepté :',
        termsOfUse: 'Conditions d\'Utilisation',
        privacyPolicy: 'Politique de Confidentialité',
      },
    },
    'de-DE': {
      loginInPage: {
        facebookLogin: 'Mit Facebook anmelden',
        googleLogin: 'Mit Google anmelden',
        loginFailed: 'Anmeldung fehlgeschlagen',
        agreeMsg: 'Durch das Einloggen in Ihr Konto haben Sie Folgendes gelesen und akzeptiert:',
        termsOfUse: 'Nutzungsbestimmungen',
        privacyPolicy: 'Datenschutzrichtlinien',
      },
    },
    'it-IT': {
      loginInPage: {
        facebookLogin: 'Accedi con Facebook',
        googleLogin: 'Accedi con Google',
        loginFailed: 'Accesso non riuscito',
        agreeMsg: 'Accedendo al tuo account, hai letto e accettato:',
        termsOfUse: 'Termini di Utilizzo',
        privacyPolicy: 'Informativa sulla Privacy',
      },
    },
    'ru-RU': {
      loginInPage: {
        facebookLogin: 'Войти с помощью Facebook',
        googleLogin: 'Войти с помощью Google',
        loginFailed: 'Ошибка Входа',
        agreeMsg: 'Войдя в аккаунт, вы прочитали и согласились с:',
        termsOfUse: 'Условия Использования',
        privacyPolicy: 'Политика Конфиденциальности',
      },
    },
    'tr-TR': {
      loginInPage: {
        facebookLogin: 'Facebook ile oturum açın',
        googleLogin: 'Google ile oturum açın',
        loginFailed: 'Giriş başarısız',
        agreeMsg: 'Hesabınıza giriş yaparak şunları okudunuz ve kabul ettiniz:',
        termsOfUse: 'Kullanım Koşulları',
        privacyPolicy: 'Gizlilik Politikası',
      },
    },
    'ms-MY': {
      loginInPage: {
        facebookLogin: 'Log masuk dengan Facebook',
        googleLogin: 'Log masuk dengan Google',
        loginFailed: 'Log Masuk Gagal',
        agreeMsg: 'Dengan log masuk akaun, anda telah membaca dan bersetuju dengan:',
        termsOfUse: 'Peruntukan Penggunaan',
        privacyPolicy: 'Polisi Privasi',
      },
    },
    'zh-TW': {
      loginInPage: {
        facebookLogin: 'Facebook 登入',
        googleLogin: 'Google 登入',
        loginFailed: '登入失敗',
        agreeMsg: '登錄帳戶，您已閱讀並同意：',
        termsOfUse: '使用條款',
        privacyPolicy: '隱私政策',
      },
    },
    'zh-CN': {
      loginInPage: {
        facebookLogin: 'Facebook 登录',
        googleLogin: 'Google 登录',
        loginFailed: '登录失败',
        agreeMsg: '登录账户，您已阅读并同意：',
        termsOfUse: '使用条款',
        privacyPolicy: '隐私政策',
      },
    },
  },
)
