import { httpClient } from 'src/lib/http-client'

export const apiGetMessageList = (data: M.Message.ListParams) =>
  httpClient.post<ApiResponse<M.Message.ListResponse>>('/open_platform/msg/message_list', data)

export const apiGetUnreadMessageCount = () =>
  httpClient.post<ApiResponse<M.Message.UnreadCountResponse>>('/open_platform/msg/unread_message_num')

export const apiSetMessageRead = (data: M.Message.ReadMessageRequest) =>
  httpClient.post<ApiResponse<boolean>>('/open_platform/msg/read_message', data)
