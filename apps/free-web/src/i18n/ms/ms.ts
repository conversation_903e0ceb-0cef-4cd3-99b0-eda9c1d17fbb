export const ms = {
  // 通用
  common: {
    cancel: 'Batal',
  },
  // 分享页
  share: {
    watchNow: 'Tonton Sekarang',
  },
  'customer-service-center': {
    title: '<PERSON><PERSON>t Khidmat Pelanggan',
    description: 'Penerangan',
    'description-placeholder': '<PERSON>rikan butiran untuk membantu kami memahami masalah tersebut',
    'email-placeholder': 'Masukkan e-mel anda untuk menerima kemas kini maklum balas',
    'submit-feedback': 'Hantar',
    'enter-feedback': 'Maklum balas isu lain',
    'upload-pictures': 'Muat naik gambar',
    feedback: 'Maklum balas',
    faq: {
      question1: 'Mengapa tertera \'Iklan Belum Sedia Lagi\'?',
      answer1: 'Kadangkala, sambungan pelayan atau pengiklan yang tidak stabil boleh menyebabkan masalah. Kami mengesyorkan anda mencuba semula kemudian dan tidak mengklik "Tonton Iklan" terlalu kerap. Had iklan mungkin dikenakan disebabkan oleh dasar pihak ketiga atau sekatan negara anda. Kami sentiasa bekerjasama dengan penyedia iklan kami untuk meningkatkan pengalaman menonton iklan anda.',
      question2: 'Mengapa tertera \'Rangkaian Tidak Tersedia\' untuk iklan?',
      answer2: 'Sila cuba mulakan semula apl dengan menutupnya sepenuhnya dan kemudian membukanya semula. Jika iklan masih tidak dimuatkan, tunggu sebentar sebelum mencuba lagi dan elakkan mengklik terlalu kerap. Jika isu itu berterusan, anda boleh menghubungi melalui sistem Maklum Balas kami. Kami juga bekerjasama dengan pasukan iklan kami untuk meningkatkan pengalaman tontonan. Terima kasih atas kesabaran anda.',
    },
  },
  toast: {
    'upload-failed': 'Muat naik gagal, sila muat naik semula',
    'submit-success': 'Hantar dengan jayanya',
    'email-incorrect': 'Format e- mel tidak betul',
  },
  not_found: {
    title: 'Halaman yang anda cari tidak wujud',
  },
  coins: {
    earnings: {
      title: 'Pendapatan Saya',
      total: 'Jumlah Syiling',
      exchange: 'Pertukaran',
      earningsHistory: 'Sejarah Pendapatan',
      exchangeHistory: 'Sejarah Pertukaran',
      noRecords: 'Tiada rekod',
      last50earnings: 'Hanya 50 pendapatan terakhir dipaparkan',
      last50exchanges: 'Hanya 50 rekod pertukaran terakhir dipaparkan',
      benefits: 'Faedah',
      gotIt: 'faham',
    },
    exchange: {
      title: 'Zon Pertukaran',
      notOpen: 'Belum dibuka, sila tunggu',
      receiveBtn: 'Terima dengan Gembira',
      earnCoins: 'Dapatkan Syiling',
      redeemRequiredXCoins: 'Penebusan memerlukan {X} syiling.',
      exchangeSuccess: 'Ditebus Berjaya',
      cardId: 'ID Kad',
      exchangeFailed: 'Penebusan Gagal',
      transferFailedInsufficientStock: 'Kehabisan stok. Sila datang awal esok.',
      gotItBtn: 'Faham',
      transferFailedRiskControl: 'Akaun tidak biasa, penebusan tidak dapat dilakukan buat sementara waktu.',
      tryAgainBtn: 'Cuba Lagi Nanti',
      transferConfirm: 'Pengesahan Pemindahan',
      paypalAccountEmail: 'E-mel Akaun PayPal',
      confirmPaypalAccountEmail: 'Sahkan e-mel Akaun PayPal',
      transferX: 'Pemindahan {X}',
      transferXConfirm: 'Sahkan Pemindahan {X}',
      transferToYourAccountX: 'Dipindahkan ke akaun anda {X}',
      confirmBtn: 'Sahkan',
      transferSuccessTitle: 'Kami sedang menghantar wang ke akaun PayPal anda!',
      transferSuccessContentX: 'Wang anda akan sampai dalam masa 5 hari ke akaun {X}.',
      copySuccess: 'Salinan Berjaya',
      enterEmailError: 'Sila masukkan alamat emel yang sah',
      enterEmailValidError: 'Ralat input, sila masukkan semula',
      emailNotMatchError: 'Alamat emel tidak sepadan, sila masukkan semula',
    },
  },
  my_wallet: {
    title: 'Dompet Saya',
    help_dialog: {
      title: 'Peraturan Penggunaan Tiket',
      content: `1 Tiket yang Dibeli = 1 Tiket Ganjaran yang Dibeli = 1 Tiket Ganjaran AD = 1 Tiket Pertukaran Syiling – Semua ini boleh digunakan untuk membuka kunci episod.
Semua tiket membuka hak menonton episod yang tidak akan tamat tempoh, tetapi Purchased Reward Ticket akan tamat tempoh selepas tempoh tertentu.
Tiket yang dibeli boleh diperoleh melalui pembelian terus.
Tiket Ganjaran yang Dibeli hanya boleh diperoleh semasa tempoh promosi tertentu melalui pembelian.
Tiket Ganjaran AD boleh diperoleh dengan menyelesaikan tugas melihat iklan.
Tiket Pertukaran Syiling boleh diperolehi dengan menukar syiling.`,
      content1: `1. 1 Tiket yang Dibeli = 1 Tiket Ganjaran yang Dibeli = 1 Tiket Ganjaran AD = 1 Tiket Pertukaran Syiling – Semua ini boleh digunakan untuk membuka kunci episod.`,
      content2: `2. Semua tiket membuka hak menonton episod yang tidak akan tamat tempoh, tetapi Purchased Reward Ticket akan tamat tempoh selepas tempoh tertentu.`,
      content3: `3. Tiket yang dibeli boleh diperoleh melalui pembelian terus.`,
      content4: `4. Tiket Ganjaran yang Dibeli hanya boleh diperoleh semasa tempoh promosi tertentu melalui pembelian.`,
      content5: `5. Tiket Ganjaran AD boleh diperoleh dengan menyelesaikan tugas melihat iklan.`,
      content6: `6. Tiket Pertukaran Syiling boleh diperolehi dengan menukar syiling.`,
    },
    account_balance: 'Baki Akaun',
    purchased_tickets: 'Tiket yang Dibeli',
    purchase_reward_tickets: 'Membeli Tiket Ganjaran',
    coin_exchange_tickets: 'Tiket Pertukaran Syiling',
    ad_reward_tickets: 'Tiket Ganjaran AD',
    top_up: 'Tambah Nilai',
    purchase_history: 'Sejarah Pembelian',
    rewards_history: 'Sejarah Ganjaran',
    exchange_history: 'Sejarah Pertukaran',
    consumption_records: 'Rekod Penggunaan',
    auto_unlock: 'Bahagian Membuka Kunci Automatik',
  },
  top_up: {
    exchange_title: 'Pertukaran Syiling',
    purchase_title: 'Pembelian',
    tickets: 'Tiket',
    coins: 'Syiling',
    balance: 'Baki',
    title: 'Kedai',
    tips: 'Petua',
    tips_1: '1. Platform kami menawarkan kedua-dua drama pendek percuma dan drama akses awal premium.',
    tips_2: '2. Untuk menonton kandungan akses awal lebih awal, anda perlu menebusnya dengan Tiket. Tiket ini boleh diperolehi melalui pembelian, pertukaran syiling, atau dengan menonton iklan.',
    tips_3: '3. Semua tiket membuka hak menonton episod yang tidak akan tamat tempoh, tetapi Purchased Reward Ticket akan tamat tempoh selepas tempoh tertentu.',
    tips_4: '4. Tiket Ganjaran yang Dibeli akan digunakan dahulu apabila membuka kunci episod. Jika baki tidak mencukupi, tiket biasa anda akan digunakan secara automatik.',
    tips_5: '5. Jika pembayaran tambah nilai anda berjaya tetapi baki anda belum dikemas kini, sila cuba muat semula dompet anda.',
    tips_6: '6. Untuk sebarang pertanyaan lain, sila hubungi kami melalui "Profil" -> "Pusat Khidmat Pelanggan" -> "Maklum Balas".',
    exchange_dialog_title: 'Adakah anda pasti mahu bertukar?',
    redeem_required: 'Tebus diperlukan',
    exchange_success: 'xxx Tiket berjaya ditukar',
    balance_not_enough: 'Baki tidak mencukupi',
    exchange_failed: 'Pertukaran gagal',
  },
  meal_check_in: {
    sleeping_link: 'Dapatkan Syiling Semasa Tidur',
    rules_title: 'Peraturan Aktiviti',
    benefits_title: 'Benefits',
    rules_1: '1. Setiap hari, terdapat empat waktu untuk anda mendaftar makanan anda: Sarapan: 07:00-10:00; Makan Tengah Hari: 11:00-14:00; Makan Malam: 17:00-20:00; Snek Tengah Malam: 21:00-24:00.',
    rules_2: '2. Jika anda terlepas masa pendaftaran, anda boleh menonton video untuk menuntut ganjaran.',
    rules_3: '3. Ganjaran direset setiap hari pada tengah malam, jadi jangan lupa untuk menuntut ganjaran anda.',
  },
  sleep_check_in: {
    earn_link: 'Catat Makanan Untuk Dapatkan',
    benefits_title: 'Benefits',
    rules_1: '1. Setiap malam dari pukul 7 malam hingga 12 malam, anda boleh mengaktifkan mod \'Tidur untuk Menjana\'.',
    rules_2: '2. Selepas mengaktifkan, pada keesokan harinya, selepas tidur selama 8 jam, anda boleh menuntut ganjaran tidur antara pukul 8 pagi hingga 12 tengah hari.',
    rules_3: '3. Jika anda terlepas masa untuk menuntut ganjaran, anda boleh menonton video antara pukul 12 tengah hari hingga 7 malam pada keesokan harinya untuk menuntut ganjaran.',
  },

}
