export function round(num: number, precision = 2) {
  return +(Math.round(parseFloat(num + 'e+' + precision)) + 'e-' + precision)
}
// console.log(roundToTwoDecimals(1.005)) // 1.01（避免浮点精度问题）
// console.log(roundToTwoDecimals(0.005)) // 0.01

/**
 * 友好显示数字
 * - 0-999
 * - 1.0K-999.99K
 * - 1.0M-
 */
export function friendly(num: number) {
  if (num < 0) {
    return 0
  } else if (num < 1_000) {
    return num
  } else if (num < 1_000_000) {
    return `${round(num / 1000, 2)}K`
  } else if (num < 1_000_000_000) {
    return `${round(num / 1_000_000, 2)}M`
  }
}

// console.log(friendly(1000)) // 1.0K
// console.log(friendly(999990)) // 999.99k
// console.log(friendly(1000000)) // 1.0M

/**
 * 获取不小于 min 不大于 max 的值
 */
export function between(num: number, min: number, max: number) {
  return Math.min(Math.max(num, min), max)
}

/**
 * 判断 num 是否在 min 和 max 之间
 */
export function isBetween(num: number, min: number, max: number) {
  return num >= min && num <= max
}
