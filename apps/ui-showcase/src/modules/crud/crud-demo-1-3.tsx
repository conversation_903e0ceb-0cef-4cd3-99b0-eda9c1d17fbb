import { createComponent } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { Wrapper } from 'src/layout/wrapper'
import { ref } from 'vue'
import { set } from 'lodash-es'
type CrudDemo1Options = {
  props: {}
}
export const CrudDemo1Dot3 = createComponent<CrudDemo1Options>({
  props: {},
}, () => {
  type Data = {
    age: number[]
  }
  const Form = CreateForm<Data>()
  const formData = ref<Data>({
    age: [1000],
  })
  const options = ref([
    // 1 到 100
    ...Array.from({ length: 100 }, (_, i) => i + 1).map(i => ({ label: i + '岁', value: i })),
  ])
  return () => (
    <Wrapper>
      <Form
        onReset={(_e, data) => { formData.value = data }}
        data={formData.value}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        items={[
          { label: '年龄', path: 'age',
            class: 'w-[22em]',
            input: {
              type: 'multi-select',
              options: options.value,
              search: { debounce: 200, placeholder: '搜索' },
            } },
        ]}
      />
    </Wrapper>
  )
})

export default CrudDemo1Dot3
