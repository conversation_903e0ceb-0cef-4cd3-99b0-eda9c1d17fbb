export const playerConfig = {
  mediaType: 'video',
  height: '100%',
  width: '100%',
  isMobileSimulateMode: 'mobile',
  autoplay: !1,
  videoFillMode: 'cover',
  playsinline: !0,
  closeVideoDblclick: !0,
  closeVideoTouch: !0,
  closeVideoPreventDefault: !0,
  closeVideoStopPropagation: !0,
  controls: false,
  start: {
    mode: 'auto',
  },
  play: {
    disable: !0,
  },
  volume: {
    disable: !0,
  },
  playbackrate: {
    disable: !0,
  },
  definition: {
    disable: !0,
    list: [],
  },
  replay: {
    disable: !0,
  },
  fullscreen: {
    disable: !0,
  },
  'x5-video-player-fullscreen': !1,
  'x5-video-player-type': 'h5',
}
