import type { AxiosResponse } from 'axios'

type ResponseError = { response: AxiosResponse }
export const isResponseError = (error: unknown): error is ResponseError => {
  if (!(error instanceof Object)) {
    return false
  }
  if (!('response' in error)) {
    return false
  }
  const response = error['response'] as AxiosResponse
  if (!('status' in response)) {
    return false
  }
  return true
}
