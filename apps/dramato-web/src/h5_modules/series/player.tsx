import { createComponent, fn, getQuery, isPc, required } from '@skynet/shared'
import { MergeClass, SvgIcon } from '@skynet/ui'
import { preventDefault } from '@skynet/ui/utils'
import { Fn, useEventListener, useThrottleFn } from '@vueuse/core'
import 'shaka-player/dist/controls.css'
import shaka from 'shaka-player/dist/shaka-player.compiled'
import { h5Track } from 'src/lib/h5-track'
import { computed, onBeforeUnmount, onMounted, ref, shallowRef, watch } from 'vue'
import { getUser } from '../common/user'
import { useStatistics } from '../statistics/statistics'
import PlayerProgress from './player-progress'
import { useSeriesItem } from './use-series-item'
import { useImmersive } from './use-immersive'
import { useOngoingTrack } from './use-ongoing-track'

type PlayerOptions = {
  props: {
    src?: string | null
    autoPlay?: boolean
    loading?: boolean
    seriesId?: string
    episodeId?: string
    subtitles?: { start: number, end: number, text: string }[]
    paused?: boolean
    loadType: 'ongoing' | 'pre'
  }
  emits: {
    onceClick: Fn
    doubleClick: Fn
    firstPlay: Fn
    endPlay: Fn
    timeUpdate: (t: number, d: number) => void
    canPlay: Fn
    waitStart: Fn
  }
}

export const Player = createComponent<PlayerOptions>({
  props: {
    src: null,
    autoPlay: false,
    loading: false,
    paused: false,
    seriesId: '',
    episodeId: '',
    subtitles: [],
    loadType: required,
  },
  emits: {
    onceClick: fn,
    doubleClick: fn,
    firstPlay: fn,
    endPlay: fn,
    timeUpdate: fn,
    canPlay: fn,
    waitStart: fn,
  },
}, (props, { emit }) => {
  let player: shaka.Player | null = null
  let videoTimer: number | null = null
  let pauseTimer: number | null = null
  const pauseButtonVisible = ref(false)
  const videoRef = ref<HTMLVideoElement>()
  const canPlay = ref(false)
  const isPlaying = ref<boolean | undefined>(undefined)
  const loaded = ref(false)
  const { immersive, cancelImmersiveTimer, resetImmersiveTimer } = useImmersive(props.seriesId)
  const everTouched = ref(false) // 是否曾经触摸过
  const playbackStartedAt = ref(0) // 播放开始时间
  const { getSlotIndex, findEpisode, polyfillInstalled, obviousWatermarkVisible, nonObviousWatermarkVisible, nonObviousWatermarkPosition,
    currentEpisodeId, isPlayingEpisode,
  } = useSeriesItem(props.seriesId)
  const { sessionId, playbackId, refreshPlaybackId } = useStatistics()
  const episode = computed(() => findEpisode(props.episodeId))
  const currentSubtitle = ref<string | undefined>(undefined)
  const me = getUser()
  const clickArea = ref<HTMLDivElement>()
  const touchTimer = ref<number | null>(null)
  const wrapper = ref<HTMLDivElement>()
  const playCount = ref(0)
  const currentTime = ref(0)
  const totalTime = ref(0)
  const trackVideoPlayWaitStart = useThrottleFn(_trackVideoPlayWaitStart)
  const trackVideoPlayView = useThrottleFn(_trackVideoPlayView)
  const trackVideoPlayFinish = useThrottleFn(_trackVideoPlayFinish)
  const trackVideoPlayWait = useThrottleFn(_trackVideoPlayWait, 10)
  const isWaiting = ref(false)
  const waiting_started_at = ref<number | null>(null)
  const waiting_ended_at = ref<number | null>(null)
  const trackVideoPlayEnd = useThrottleFn(() => {
    if (playbackStartedAt.value) {
      _trackVideoPlayEnd({
        playback_duration: Date.now() - playbackStartedAt.value,
        playback_position: currentTime.value || 0,
      })
    }
  })
  const trackVideoPlayStart = useThrottleFn((playback_position?: number) => {
    _trackVideoPlayStart(playback_position)
  })
  const trackVideoLoadStart = useThrottleFn(_trackVideoLoadStart)
  const trackVideoLoadEnd = useThrottleFn(_trackVideoLoadEnd)
  const active = computed(() => {
    return props.episodeId === currentEpisodeId.value
  })
  const activatedAt = shallowRef<number | null>(null)
  const trackPlayOngoing = useThrottleFn(_trackPlayOngoing, 4000) // play ongoing 每 5 秒上报一次，这里限制 4 秒冷却时间
  const trackLoadOngoing = useThrottleFn(_trackLoadOngoing, 1000) // load ongoing 每 2 秒上报一次，这里限制 1 秒冷却时间
  const { pausedAt, waitedAt } = useOngoingTrack(props.seriesId, props.episodeId, {
    onPlayOngoing5Seconds: () => {
      void trackPlayOngoing()
    },
    onWaitOngoing2Seconds: () => {
      void trackLoadOngoing()
    },
  })
  waitedAt.value = Date.now()

  watch(() => active.value, v => {
    if (v) {
      activatedAt.value = Date.now()
    }
  }, { immediate: true })

  function onCloseWindow() {
    if (waiting_started_at.value && isWaiting.value) {
      _trackVideoPlayWait(waiting_started_at.value, Date.now())
    }
    _trackVideoPlayEnd({
      playback_duration: Date.now() - playbackStartedAt.value,
      playback_position: currentTime.value || 0,
    })
    refreshPlaybackId()
  }
  onMounted(() => {
    window.addEventListener('beforeunload', onCloseWindow)
    window.addEventListener('visibilitychange', onVisibilityChange)
  })
  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', onCloseWindow)
    window.removeEventListener('visibilitychange', onVisibilityChange)
    // void trackVideoPlayEnd()
    refreshPlaybackId()
    unloadVideo()
    if (waiting_started_at.value && isWaiting.value) {
      void trackVideoPlayWait(waiting_started_at.value, Date.now())
    }
  })

  watch(() => props.src, src => {
    if (src && player) {
      void player.load(src).then(() => {
        afterLoad()
      })
    }
    if (!src) {
      pausedAt.value = Date.now()
    }
  }, { immediate: false })
  watch(() => [props.loadType, canPlay.value], ([t, can]) => {
    if (t === 'ongoing' && can) {
      void trackVideoPlayView()
    }
  }, { immediate: true })

  onMounted(() => {
    initVideo()
    void initPlayer()
  })

  useEventListener(clickArea, 'click', (e: TouchEvent) => {
    e.preventDefault()
    // e.stopPropagation()
    if (touchTimer.value) {
      onDoubleClick()
      window.clearTimeout(touchTimer.value)
      touchTimer.value = null
      return
    }
    touchTimer.value = window.setTimeout(() => {
      onOnceClick()
      touchTimer.value = null
    }, 200)
  })

  useEventListener(videoRef, 'touchstart', (e: TouchEvent) => {
    e.preventDefault()
    e.stopPropagation()
  })
  useEventListener(videoRef, 'timeupdate', () => {
    if (!videoRef.value) return
    const currentTime = videoRef.value.currentTime

    // 查找当前时间对应的字幕
    if (props.subtitles.length > 0) {
      const subtitle = props.subtitles.find(sub => {
        if (!sub.start || !sub.end) return false
        return currentTime >= sub.start && currentTime <= sub.end
      })

      if (subtitle) {
        currentSubtitle.value = subtitle.text!
      } else {
        currentSubtitle.value = undefined
      }
    }
  })

  watch(() => isPlaying.value, v => {
    const video = videoRef.value
    if (!video) return
    if (active.value) {
      isPlayingEpisode.value = !!v
    }
    if (v === true) {
      pausedAt.value = null
      waitedAt.value = null
      void video.play()
      video.muted = false
      playCount.value += 1
      if (playCount.value === 1) {
        emit('firstPlay')
      }
    } else if (v === false) {
      pausedAt.value = Date.now()
      void video.pause()
    }
  })

  watch(() => immersive.value, () => {
    if (pauseTimer) {
      window.clearTimeout(pauseTimer)
      pauseTimer = null
    }
  })
  watch(() => props.paused, p => {
    // 切换剧集时，isPlaying.value 的初始值为 undefined
    isPlaying.value = undefined
    if (!videoRef.value) {
      isPlaying.value = false
      return
    }
    if (p) {
      videoRef.value?.pause()
    } else {
      void videoRef.value?.play().catch(() => {
        isPlaying.value = false
      })
    }
  }, { immediate: true })
  watch(() => [waiting_started_at.value, waiting_ended_at.value], ([started_at, ended_at]) => {
    if (started_at && ended_at && ended_at > started_at) {
      void trackVideoPlayWait(started_at, ended_at)
    }
  }, { immediate: true })

  return () => (
    <MergeClass tag="x-player" ref={wrapper} baseClass="relative w-screen h-safe-screen flex items-center justify-center overflow-hidden">
      <img
        class={['w-full absolute min-h-[700px] pc:min-h-auto', { hidden: loaded.value }]}
        src={episode.value?.cover ?? 'https://static-v1.mydramawave.com/frontend_static/common-images/black.png'}
      />
      <video ref={videoRef} class="w-full min-h-[700px] pc:min-h-auto" autoplay={props.autoPlay} controls={false} onDblclick={preventDefault}
        playsinline webkit-playsinline tab-index="-1"
        poster={episode.value?.cover ?? 'https://static-v1.mydramawave.com/frontend_static/common-images/black.png'}
      />
      <SvgIcon noColor name="watermark" class="color-white absolute left-4 top-[65px] mt-[env(safe-area-inset-top,0px)] h-[12px] w-[92px]" />
      {/* 明纹 */}
      {obviousWatermarkVisible.value
      && <span class="absolute right-4 top-[65px] mt-[env(safe-area-inset-top,0px)] whitespace-nowrap text-[10px] text-white opacity-20">{me.user_id}</span>}
      {/* 暗纹 */}
      {
        nonObviousWatermarkVisible.value
        && (
          <span class="absolute left-0 top-0 mt-[env(safe-area-inset-top,0px)] whitespace-nowrap text-[10px] text-white opacity-20"
            style={{
              transform: `translateX(${nonObviousWatermarkPosition.value.x}px) translateY(${nonObviousWatermarkPosition.value.y}px)`,
            }}>{me.user_id}
          </span>
        )
      }
      {immersive.value ? null : renderUi() }
      {/* 点击区域 */}
      <x-click-area ref={clickArea} class="z-up-up absolute left-0 top-0 h-[calc(100vh-var(--vh-offset,0px)-120px)] w-full" />
      {(!immersive.value || isPc) && !props.loading && canPlay.value && isPlaying.value === false
      && (
      // 播放按钮
        <x-play-status key="play" class="absolute left-1/2 top-1/2 z-[3] flex -translate-x-1/2 -translate-y-1/2 touch-none items-center justify-center p-4"
          onClick={onClickPlayOrPause}
        >
          <SvgIcon name="play" no-color class="color-white size-[72px]" />
        </x-play-status>
      ) }
      {((isPc && isPlaying.value === true && !immersive.value) || (!props.loading && isPlaying.value === true && everTouched.value && !immersive.value))
      && (
        // 暂停按钮
        <x-play-status key="pause" class="absolute left-1/2 top-1/2 z-[3] flex -translate-x-1/2 -translate-y-1/2 touch-none items-center justify-center p-4"
          onClick={onClickPlayOrPause}
        >
          <SvgIcon name="pause" no-color class="color-white size-[72px]" />
        </x-play-status>
      )}
      {currentSubtitle.value && <x-subtitle class="z-up-up absolute inset-x-0 top-[calc(100vh-var(--vh-offset,0px)-232px)] mx-auto line-clamp-3 flex h-[90px] max-w-[calc(100%-56px)] items-center justify-center text-center text-2xl font-bold text-white [text-shadow:_0px_1px_2px_rgb(0_0_0_/_0.90)]">{currentSubtitle.value}</x-subtitle>}
    </MergeClass>
  )

  // #region 帮助函数

  // 初始化 shaka player
  async function initPlayer() {
    if (!videoRef.value || !wrapper.value) return
    if (!polyfillInstalled.value) {
      shaka.polyfill.installAll()
      polyfillInstalled.value = true
    }
    if (!shaka.Player.isBrowserSupported()) {
      return window.alert('Browser not supported!')
    }
    player = new shaka.Player()
    if (player) {
      player.addEventListener('error', event => {
        console.log('shaka error')
        console.log(event)
        _trackError()
      })
      await player.attach(videoRef.value)
      await player.load(props.src)
      afterLoad()
    }
  }
  function afterLoad() {
    if (!player) return
    try {
      // 有人建议在 load 之后再配置更好
      player.configure({ abr: { enabled: false } })
      // 获取所有 video tracks
      const tracks = player.getVariantTracks()

      // 找出最小分辨率的 track
      let minTrack = tracks[0]
      for (const track of tracks) {
        if ((track.height && minTrack.height) ? track.height < minTrack.height
          : (track.height === minTrack.height && track.bandwidth < minTrack.bandwidth)) {
          minTrack = track
        }
      }
      // 选择这个最小分辨率的 track
      player.selectVariantTrack(minTrack, /* clearBuffer= */ false)
    } catch (err) {
      player.configure({ abr: { enabled: true } })
      _trackError()
    }
  }
  function initVideo() {
    const video = videoRef.value
    if (!video) return
    video.addEventListener('canplay', handleCanPlay)
    video.addEventListener('canplaythrough', handleCanPlayThrough)
    video.addEventListener('waiting', handleWaiting)
    video.addEventListener('play', handlePlay)
    video.addEventListener('playing', handlePlaying)
    video.addEventListener('pause', handlePause)
    video.addEventListener('ended', handleEnded)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('seeking', handleSeeking)
    video.addEventListener('seeked', handleSeeked)
    video.addEventListener('loadstart', handleLoadStart)
    video.addEventListener('loadeddata', handleLoadedData)
  }
  function unloadVideo() {
    void player?.destroy()
    isPlaying.value = false
    canPlay.value = false
    if (videoRef.value) {
      videoRef.value.removeEventListener('canplay', handleCanPlay)
      videoRef.value.removeEventListener('canplaythrough', handleCanPlayThrough)
      videoRef.value.removeEventListener('waiting', handleWaiting)
      videoRef.value.removeEventListener('play', handlePlay)
      videoRef.value.removeEventListener('playing', handlePlaying)
      videoRef.value.removeEventListener('pause', handlePause)
      videoRef.value.removeEventListener('ended', handleEnded)
      videoRef.value.removeEventListener('timeupdate', handleTimeUpdate)
      videoRef.value.removeEventListener('seeking', handleSeeking)
      videoRef.value.removeEventListener('seeked', handleSeeked)
      videoRef.value.removeEventListener('loadstart', handleLoadStart)
      videoRef.value.removeEventListener('loadeddata', handleLoadedData)
      videoRef.value.muted = true
    }
    if (videoTimer) {
      window.clearInterval(videoTimer)
      videoTimer = null
    }
  }

  // 视频事件处理
  function handleCanPlay() {
    canPlay.value = true
    waitedAt.value = null
    emit('canPlay')
  }
  function handleCanPlayThrough() {
    isWaiting.value = false
    waitedAt.value = null
    waiting_ended_at.value = Date.now()
  }
  function handleSeeking() {
  }
  function handleSeeked() {

  }
  function handleTimeUpdate() {
    const video = videoRef.value
    if (!video) return
    currentTime.value = video.currentTime || 0
    totalTime.value = video.duration || 0
    emit('timeUpdate', currentTime.value, totalTime.value)
    if (Math.floor(currentTime.value) % 3 === 1 && currentTime.value < 11) {
      h5Track('video', 'play', 'time_update', {
        audio_language: episode.value?.original_audio_language || '语言_Original',
        load_type: props.loadType,
        playback_id: playbackId.value,
        playback_position: currentTime.value || 0,
        series_id: props.seriesId,
        session_id: sessionId.value,
        slot: getSlotIndex(props.episodeId),
        video_id: props.episodeId,
      })
    }

    if (!currentTime.value) return
    // 查找当前时间对应的字幕
    if (props.subtitles.length > 0) {
      const subtitle = props.subtitles.find(sub => {
        if (!sub.start || !sub.end) return false
        return currentTime.value >= sub.start && currentTime.value <= sub.end
      })
      if (subtitle) {
        currentSubtitle.value = subtitle.text!
      } else {
        currentSubtitle.value = undefined
      }
    }
  }

  function handleWaiting() {
    canPlay.value = false
    if (active.value) {
      void trackVideoPlayWaitStart()
      isWaiting.value = true
      waiting_started_at.value = Date.now()
      waitedAt.value = Date.now()
      waiting_ended_at.value = null
    }
  }

  function renderUi() {
    if (!canPlay.value) return null
    return (
      <x-player-ui class="z-up absolute inset-0">
        {/* 渐变遮罩 */}
        <x-mask class="absolute left-0 top-[calc(100vh-var(--vh-offset,0px)-60px)] h-[60px] w-full bg-gradient-to-b from-transparent to-black/100" />
        {/* 进度条 */}
        <PlayerProgress loading={props.loading} video={videoRef} currentTime={currentTime.value || 0} totalTime={totalTime.value || Infinity}
          onSeek={onSeek}
          onSeekStart={onSeekStart}
          class="absolute inset-x-4 top-[calc(100vh-var(--vh-offset,0px)-118px)] h-[66px]" />
      </x-player-ui>
    )
  }

  /*****************************************************************************
   * 追踪函数
   ****************************************************************************/
  function _trackError() {
    h5Track('video', 'play', 'error', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      from: getQuery('from_page', ''),
      interaction_type: getQuery('interaction_type', 2),
      load_type: props.loadType,
      playback_id: playbackId.value,
      playback_position: currentTime.value || 0,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(props.episodeId),
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
    })
  }

  function _trackVideoPlayWaitStart() {
    if (!props.src) {
      return
    }
    h5Track('video', 'play', 'wait_start', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      from: getQuery('from_page', ''),
      interaction_type: getQuery('interaction_type', 2),
      load_type: props.loadType,
      playback_id: playbackId.value,
      playback_position: currentTime.value || 0,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(props.episodeId),
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
    })
  }
  function _trackVideoPlayView() {
    if (!props.src) {
      return
    }
    h5Track('video', 'play', 'view', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      from: getQuery('from_page', ''),
      interaction_type: getQuery('interaction_type', 2),
      load_type: props.loadType,
      playback_id: playbackId.value,
      playback_position: currentTime.value || 0,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(props.episodeId),
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
    })
  }

  function _trackVideoPlayFinish() {
    if (!props.src) return
    h5Track('video', 'play', 'finish', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      from: getQuery('from_page', ''),
      interaction_type: getQuery('interaction_type', 2),
      load_type: props.loadType,
      playback_id: playbackId.value,
      playback_position: currentTime.value || 0,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(props.episodeId),
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
    })
  }

  function _trackVideoPlayEnd(additionalParams: { playback_duration: number, playback_position: number }) {
    if (!props.src) return
    h5Track('video', 'play', 'end', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      from: getQuery('from_page', ''),
      interaction_type: getQuery('interaction_type', 2),
      load_type: props.loadType,
      playback_id: playbackId.value,
      playback_position: additionalParams.playback_position,
      playback_duration: additionalParams.playback_duration,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(props.episodeId),
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
    })
  }

  function _trackVideoPlayStart(playback_position: number = currentTime.value || 0) {
    if (!props.src) return
    h5Track('video', 'play', 'start', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      from: getQuery('from_page', ''),
      interaction_type: getQuery('interaction_type', 2),
      load_type: props.loadType,
      playback_id: playbackId.value,
      playback_position: playback_position,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(props.episodeId),
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
    }, {
      relative_time: activatedAt.value ? Date.now() - activatedAt.value : undefined,
    })
  }

  function _trackVideoLoadStart() {
    if (!props.src) return
    h5Track('video', 'load', 'start', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      from: getQuery('from_page', ''),
      interaction_type: getQuery('interaction_type', 2),
      load_type: props.loadType,
      on_screen: props.loadType === 'ongoing' ? 1 : 0,
      playback_position: currentTime.value || 0,
      series_id: props.seriesId,
      session_id: sessionId.value,
      playback_id: playbackId.value,
      slot: getSlotIndex(props.episodeId),
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
    })
  }

  function _trackVideoLoadEnd() {
    if (!props.src) return
    h5Track('video', 'load', 'end', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      from: getQuery('from_page', ''),
      interaction_type: getQuery('interaction_type', 2),
      load_type: props.loadType,
      load_duration: videoRef.value?.duration || 0,
      on_screen: props.loadType === 'ongoing' ? 1 : 0,
      playback_id: playbackId.value,
      playback_position: currentTime.value || 0,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(props.episodeId),
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
    })
  }

  function _trackVideoPlayWait(started_at: number, ended_at: number) {
    if (!props.src) return
    h5Track('video', 'play', 'wait', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      from: getQuery('from_page', ''),
      interaction_type: getQuery('interaction_type', 2),
      load_type: props.loadType,
      load_duration: videoRef.value?.duration || 0,
      on_screen: props.loadType === 'ongoing' ? 1 : 0,
      playback_id: playbackId.value,
      playback_position: currentTime.value || 0,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(props.episodeId),
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
    }, {
      started_at,
      ended_at,
      duration: ended_at - started_at,
    })
  }

  function onClickPlayOrPause() {
    isPlaying.value = !isPlaying.value
    if (isPlaying.value) {
      void videoRef.value?.play?.()
      pauseButtonVisible.value = true
    }
    if (!isPlaying.value && !isPc) {
      if (pauseTimer) {
        window.clearTimeout(pauseTimer)
        pauseTimer = null
      }
      pauseTimer = window.setTimeout(() => {
        pauseButtonVisible.value = false
        pauseTimer = null
      }, 3000)
    }
  }

  function onSeekStart(from: number) {
    cancelImmersiveTimer()
  }
  function onSeek(from: number, to: number) {
    _trackVideoPlayEnd({
      playback_duration: Date.now() - playbackStartedAt.value,
      playback_position: from,
    })
    playbackStartedAt.value = Date.now()
    void trackVideoPlayStart(to)
    resetImmersiveTimer()
  }

  function handleLoadStart() {
    void trackVideoLoadStart()
  }

  function handleLoadedData() {
    waitedAt.value = null
    loaded.value = true
    void trackVideoLoadEnd()
  }

  function handlePlay() {
    isPlaying.value = true
    playbackStartedAt.value = Date.now()
    void trackVideoPlayStart()
    resetImmersiveTimer()
  }
  function handlePlaying() {
    isWaiting.value = false
    waiting_ended_at.value = Date.now()
  }

  function handlePause() {
    cancelImmersiveTimer()
    isPlaying.value = false
    void trackVideoPlayEnd()
  }

  function onOnceClick() {
    if (isPc) {
      // pc 上单击等于 phone 上的双击
      isPlaying.value = !isPlaying.value
      emit('doubleClick')
    } else {
      everTouched.value = true
      immersive.value = !immersive.value
      emit('onceClick')
    }
  }
  function onDoubleClick() {
    isPlaying.value = !isPlaying.value
    emit('doubleClick')
  }

  function handleEnded() {
    emit('endPlay')
    void trackVideoPlayFinish()
  }

  function _trackPlayOngoing() {
    if (!props.src) return
    h5Track('video', 'play', 'ongoing', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      load_type: props.loadType,
      playback_id: playbackId.value,
      playback_position: currentTime.value || 0,
      playback_duration: 5000,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(),
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
      is_completed: Math.abs(totalTime.value - currentTime.value) < 3000,
      subtitles_language: episode.value?.subtitle_list?.[0]?.language,
      playload: episode.value?.playload,
    })
  }
  function _trackLoadOngoing() {
    if (!props.src) return
    h5Track('video', 'load', 'ongoing', {
      audio_language: episode.value?.original_audio_language || '语言_Original',
      load_type: props.loadType,
      playback_id: playbackId.value,
      playback_position: currentTime.value || 0,
      load_duration: 2000,
      series_id: props.seriesId,
      session_id: sessionId.value,
      slot: getSlotIndex(),
      video_id: props.episodeId,
      video_type: episode.value?.video_type,
      on_screen: props.loadType === 'ongoing' ? 1 : 0,
      interaction_type: getQuery('interaction_type', 2),
    })
  }
  function onVisibilityChange() {
    if (document.visibilityState === 'visible' && props.loadType === 'ongoing') {
      void videoRef.value?.play()
    }
  }
  // #endregion
})

export default Player
