import { httpClient } from 'src/lib/http-client'
import { GetTokenError } from '../common/errors'

export const sessionApi = {
  signInWithPassword: async (data: { phone: string, pwd: string }) => {
    const response = await httpClient.post<ApiResponse<{ token: string }>>('/open_platform/user/pwd_login', data)
    const token = response.data.token
    if (!token) throw new GetTokenError()
    return token
  },
  signInWithCode: async ({ phone, code }: { phone: string, code: string }) => {
    const response = await httpClient.post<ApiResponse<{ token: string }>>('/open_platform/user/code_login', { phone, code })
    const token = response.data.token
    if (!token) throw new GetTokenError()
    return token
  },
  loginWithFacebook: async params => {
    const response = await httpClient.post<ApiResponse<{ token: string }>>('/open_platform/user/facebook_login', params)
    const token = response.data.token
    if (!token) throw new GetTokenError()
    return token
  },
  loginWithGoogle: async params => {
    const response = await httpClient.post<ApiResponse<{ token: string }>>('/open_platform/user/google_login', params)
    const token = response.data.token
    if (!token) throw new GetTokenError()
    return token
  },
  signUp: async (data: { phone: string, pwd: string, code: string }) => {
    return await httpClient.post<ApiResponse>('/open_platform/user/register', data)
  },
  sendCode: async (phone: string) => {
    const response = await httpClient.post<ApiResponse<{
      code?: string // 测试环境会返回 code
    }>>('/open_platform/user/send_code', { phone })
    return response.data?.code
  },
  signOut: async () => {
    return await httpClient.post<ApiResponse>('/open_platform/user/logout')
  },
}
