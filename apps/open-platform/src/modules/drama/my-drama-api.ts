import { httpClient } from 'src/lib/http-client'

export const apiCreateOrUpdateDrama = (data: M.MyDrama.DramaCreateParams) =>
  httpClient.post<ApiResponse<M.MyDrama.DramaCreateParams>>('/open_platform/resource/create_or_update', data)

export const apiGetDramaDetail = (data: M.MyDrama.DramaGetDetailRequest) =>
  httpClient.post<ApiResponse<M.MyDrama.DramaGetDetailResponse>>('/open_platform/resource/detail', data)

export const apiGetDramaList = (data: M.MyDrama.DramaListRequest) =>
  httpClient.post<ApiResponse<M.MyDrama.DramaListResponse>>('/open_platform/resource/list', data)

export const apiSubmitAudit = (data: M.MyDrama.DramaAuditRequest) =>
  httpClient.post<ApiResponse>('/open_platform/resource/audit', data)

export const apiUploadDramaResource = (data: M.MyDrama.DramaResourceUploadRequest) =>
  httpClient.post<ApiResponse<{ temp_path: string }>>('/open_platform/resource/upload', data)

export const apiDeleteDramaResource = (data: M.MyDrama.DramaResourceDeleteRequest) =>
  httpClient.post<ApiResponse>('/open_platform/resource/delete_file', data)

export const apiGetTempPath = (data: M.MyDrama.TempPath) =>
  httpClient.post<ApiResponse<M.MyDrama.TempPath>>('/open_platform/common/temppath', data)
