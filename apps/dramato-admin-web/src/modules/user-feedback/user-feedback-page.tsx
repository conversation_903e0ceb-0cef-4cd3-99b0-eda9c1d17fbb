import { createComponent } from '@skynet/shared'
import { onMounted } from 'vue'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Button, DateTime, Pager, showFailToast, showSuccessToast, SvgIcon, transformNumber, transformTimestamp } from '@skynet/ui'
import { set } from 'lodash-es'
import { useUserFeedback } from './use-user-feedback'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useImagePreviewStore } from '@skynet/ui/image/use-preview-store'
import { apiGetDramaId, apiGetUserFeedbackExport } from './user-feedback-api'
import { langKey, langValue } from '../resource/constant'
import { RouterLink } from 'vue-router'

type UserFeedbackOptions = {
  props: {}
}
export const UserFeedbackPage = createComponent<UserFeedbackOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    problemTypes,
    getProblemTypes,
    orderStatusOptions,
  } = useUserFeedback()

  const {
    showImagePreviewDialog,
  } = useImagePreviewStore()

  const { appOptions } = useAppAndLangOptions(() => params.value.app_name, {})

  const renderTitle = (row: M.UserFeedback.Item) => {
    return (
      <div class="relative">
        {row.release_round === 1 ? <SvgIcon class="absolute -left-4 -top-4 size-8" name="ic_release" /> : null}
        <span>{row.series_key}</span>
      </div>
    )
  }

  onMounted(() => {
    void getProblemTypes()
    void search(1)
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>反馈收集</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = {
                problem_type: 0,
                series_key: '',
                episode_key: '',
                resource_ids: '',
              }
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              ['UID', 'user_id', { type: 'number' }, { transform: transformNumber }],
              ['用户邮箱', 'email', { type: 'text' }],
              ['剧ID', 'series_key', { type: 'text' }],
              ['集ID', 'episode_key', { type: 'text' }],
              ['资源ID', 'resource_ids', { type: 'text' }],
              ['操作人', 'operator', { type: 'text' }],
              ['端', 'app_name', {
                type: 'select',
                class: 'w-[240px]',
                autoInsertEmptyOption: false,
                options: appOptions.value.map(i => ({
                  label: i.label,
                  value: i.label,
                })),
              }],
              ['问题类型', 'problem_type', {
                type: 'select',
                class: 'w-[240px]',
                autoInsertEmptyOption: false,
                options: [
                  {
                    value: 0,
                    label: '- 全部 -',
                  },
                  ...problemTypes.value.map(i => ({
                    label: i.problem_type,
                    value: i.id,
                  }))],
              }, {
                transform: transformNumber,
              }],
              ['版本号', 'app_version', { type: 'text' }],
              ['反馈状态', 'feedback_status', { type: 'select', options: [
                {
                  value: -1,
                  label: '全部',
                },
                ...orderStatusOptions,
              ], autoInsertEmptyOption: false }, { transform: transformNumber }],
              ['反馈日期起始', 'start_time', { type: 'datetime', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD HH:mm' }, {
                transform: transformTimestamp,
              }],
              ['反馈日期截止', 'end_time', { type: 'datetime', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD HH:mm' }, {
                transform: transformTimestamp,
              }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex justify-end">
            <Button class="btn btn-primary btn-sm" onClick={async () => {
              showSuccessToast('正在导出……')
              const response = await apiGetUserFeedbackExport({
                ...params.value,
                page_info: {
                  offset: 0, size: 20000,
                },
              })
              // 检查响应是否成功
              if (!response.ok) {
                showFailToast('下载失败')
                return
              }
              // 获取文件流
              const blob = await response.blob()
              // 检查 Blob 是否有效
              if (blob.size === 0) {
                showFailToast('下载的文件为空')
                return
              }
              const url = window.URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = '用户反馈.csv'
              a.click()
              window.URL.revokeObjectURL(url)
            }}>导出</Button>
          </div>
        ),
        table: () => (
          <Table
            loading={loading.value}
            list={list.value}
            class="tm-table-fix-last-column"
            columns={[
              ['问题类型', row => {
                const problemTypeDesc = problemTypes.value.find(item => item.id === row.problem_type)
                return problemTypeDesc ? <div class="badge badge-outline">{problemTypeDesc.problem_type}</div> : '-'
              }, { class: 'w-[160px]' }],
              ['反馈状态', row => {
                return row?.feedback_status === 0 ? <div class="badge badge-success badge-outline">待回复</div>
                  : row?.feedback_status === 1 ? <div class="badge badge-primary badge-outline">处理中</div>
                    : row?.feedback_status === 2 ? <div class="badge badge-info badge-outline">已关闭</div> : '-'
              }, { class: 'w-[100px]' }],
              ['用户反馈描述', 'description', { class: 'w-[200px]' }],
              ['用户反馈图', row => (
                <x-image-list>
                  {row.image_list && row.image_list.length > 0
                    ? (
                        <x-image-content class="flex items-center gap-x-2">
                          {row.image_list.slice(0, 1).map((image, index) => <img class="w-[40px] object-cover" referrerpolicy="no-referrer" key={index} src={image} />)}
                          <x-see
                            class="btn btn-link btn-xs"
                            onClick={() => showImagePreviewDialog({
                              imageList: row.image_list.map(i => ({
                                src: i, width: 1000,
                              })),
                            })}
                          >查看
                          </x-see>
                        </x-image-content>
                      )
                    : null}
                </x-image-list>
              ), { class: 'w-[200px]' }],
              ['用户反馈翻译-中文', 'translated_description', { class: 'w-[200px]' }],
              ['用户反馈翻译-英文', 'en_description', { class: 'w-[200px]' }],
              ['操作人', 'operator', { class: 'w-[120px]' }],
              ['资源ID', 'resource_id', { class: 'w-[90px]' }],
              ['剧 ID', row => (
                <a class="link" href="javascript:void(0);" onClick={async () => {
                  showSuccessToast('正在跳转……')
                  const res = await apiGetDramaId({
                    app_name: row.app_name,
                    series_key: row.series_key!,
                  })
                  window.open(`${location.origin}/short-drama/${res.data?.drama_id}?type=listing`)
                }}
                >{renderTitle(row)}
                </a>
              ), { class: 'w-[125px]' }],
              ['集 ID', row => {
                return row.episode_key ? (
                  <div>
                    <div>{row.episode_key}</div>
                    {row.serial_number && row.language_code ? <div>- {row.serial_number}集【{langValue[langKey.findIndex(i => i === row.language_code)]}】</div> : null }
                  </div>
                ) : '-'
              }, { class: 'w-[125px]' }],
              ['UID', 'user_id', { class: 'w-[90px]' }],
              ['用户邮箱', 'email', { class: 'w-[110px]' }],
              ['反馈日期', row => (<DateTime value={(row?.created || 0) * 1000} />), { class: 'w-[150px]' }],

              ['会员权益', row => row.vip_level ? '会员/' + row.vip_period : '非会员', { class: 'w-[200px]' }],
              ['会员有效期', row => row.vip_level ? <DateTime value={row.vip_expire * 1000} /> : '-', { class: 'w-[200px]' }],
              ['订阅类型', row => row.vip_period, { class: 'w-[200px]' }],
              ['App端', 'app_name', { class: 'w-[100px]' }],
              ['版本号', 'app_version', { class: 'w-[100px]' }],
              ['操作', row => {
                return (
                  <div>
                    <RouterLink class="btn btn-link btn-xs" to={`/user-feedback/detail/${row.id}`}>详情</RouterLink>
                  </div>
                )
              }, {
                class: 'w-[100px]',
              }],
            ]}
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(1)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default UserFeedbackPage
