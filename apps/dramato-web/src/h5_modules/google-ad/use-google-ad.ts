import { initGoogleAds } from 'src/init/init-third-party'
import { ref } from 'vue'

const clientWidth = window.innerWidth
const clientHeight = window.innerHeight
const padding = {
  top: (clientHeight - 520) / 2,
  right: (clientWidth - 330) / 2,
  bottom: (clientHeight - 520) / 2,
  left: (clientWidth - 330) / 2,
}
const adTemplate = `
  <ins class="adsbygoogle"
     style="display:inline-block;width:330px;height:520px"
     data-ad-client="ca-pub-4028685783554495"
     data-ad-slot="8311153139"></ins>
`
const adCache: Record<string, Ad> = {}

export const useGoogleAd = () => {
  return {
    getAdWidth,
    getAdHeight,
    padding,
    adCache,
    prefetchGoogleAd,
  }
}

const prefetchingGoogleAd = ref(false)

async function prefetchGoogleAd(callback?: (actionType: string) => void) {
  if (prefetchingGoogleAd.value) return Promise.reject('不能重复预加载广告，已经在加载中')
  console.log('开始预加载广告')
  const clientWidth = getAdWidth()
  const clientHeight = getAdHeight()
  const div = document.createElement('div')
  return new Promise<HTMLElement | null>((resolve, reject) => {
    const mountRoot = document.getElementById('mountRoot')
    if (!mountRoot) return
    prefetchingGoogleAd.value = true
    // 不能把 div 放在屏幕之外，否则会导致广告不填充
    div.style.position = 'fixed'
    div.style.left = padding.left + 'px'
    div.style.top = padding.top + 'px'
    div.style.zIndex = '999'
    div.style.width = `${clientWidth}px`
    div.style.height = `${clientHeight}px`
    div.style.background = 'transparent'
    div.style.opacity = '0.01'
    div.style.pointerEvents = 'none'
    div.style.touchAction = 'none'
    div.innerHTML = adTemplate
    mountRoot.appendChild(div)
    void initGoogleAds?.()?.then(() => {
      const adsbygoogle = window.adsbygoogle || []
      adsbygoogle.push({})
    })
    // 获取 div 的第一个子标签
    const ad = div.children[0] as HTMLElement
    // 监听 ad 的属性变化，如果 data-ad-status 变成 filled 或者 unfilled，就说明加载完成
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.attributeName === 'style') {
          const height = (mutation.target as HTMLElement).style.height
          console.log('广告高度变为', height)
          if (height === '0px') {
            resolve(null)
            callback?.('invalidAd')
            prefetchingGoogleAd.value = false
          }
        } else if (mutation.attributeName === 'data-ad-status') {
          const adsByGoogleStatus = (mutation.target as HTMLElement).getAttribute('data-adsbygoogle-status')
          const status = (mutation.target as HTMLElement).getAttribute('data-ad-status')

          // 获取并打印 style.height 的值
          const height = (mutation.target as HTMLElement).style.height

          if (status === 'filled' && height !== '0px') {
            resolve(ad)
            prefetchingGoogleAd.value = false
          } else if (status === 'unfilled' || adsByGoogleStatus === 'done') {
            resolve(null)
            prefetchingGoogleAd.value = false
          }
        }
      })
    })
    observer.observe(ad, { attributes: true })
  }).finally(() => {
    // 隐藏 div
    div.style.display = 'none'
    div.style.left = '-9999px'
    div.style.left = '-9999px'
    setTimeout(() => {
      // 删除 div
      if (div.parentNode) {
        div.parentNode.removeChild(div)
      }
    }, 1000)
  })
}

function getAdWidth() { return 330 }
function getAdHeight() { return 520 }
