import { createComponent, fn } from '@skynet/shared'
import { SvgIcon } from '@skynet/ui'
type PaymentProductOptions = {
  props: {
    product: M.ActivityPayment.Product
    isSelected: boolean
    isHot: boolean
  }
  emits: {
    click: (product: M.ActivityPayment.Product) => void
  }
}
export const PaymentProduct = createComponent<PaymentProductOptions>({
  props: {
    product: {
      product_id: 0,
      product_type: '',
      title: '',
      currency: '',
      currency_symbol: '',
      price: 0,
      sku_id: '',
      delivery_details: {
        quanity: 0,
        bonus: 0,
      },
    },
    isSelected: false,
    isHot: false,
  },
  emits: {
    click: fn,
  },
}, (props, { emit }) => {
  return () => (
    <x-payment-product class={`relative flex shrink-0 grow-0 flex-col rounded-xl border-[0.50px] border-solid  bg-white p-2.5 pt-4 ${props.isSelected ? 'border-[#FF7C4E] bg-gradient-to-r from-stone-50 to-orange-200 ' : 'border-gray-200 '} ${props.isHot ? 'w-full' : 'w-[calc(50%-0.25rem)]'}`} onClick={() => {
      emit('click', props.product)
    }}>
      <x-label class="absolute inset-0 flex h-4 w-full items-center justify-end gap-x-1">
        {
          props.isHot ? (
            <x-hot class="flex h-4 items-center rounded-b-lg bg-gradient-to-r from-fuchsia-500 to-red-500 px-1 py-0.5">
              <SvgIcon name="ic-fire-fill" class="size-3" />
              <span class="text-[10px] font-bold">HOT</span>
            </x-hot>
          ) : null
        }
        <x-discount class="flex h-4 items-center gap-x-0.5 rounded-bl-lg rounded-tr-xl bg-orange-400 px-1 py-0.5 text-[10px] font-bold">
          <SvgIcon name="ic-increase" class="size-3" />
          {(props.product?.delivery_details?.bonus / props.product?.delivery_details?.quanity * 100).toFixed(0) + '%'}
        </x-discount>
      </x-label>
      <div class={`flex ${props.isHot ? 'justify-between' : 'flex-col'}`}>
        <div class="flex flex-col items-start justify-between">
          <x-coins class="flex items-center gap-x-1">
            <SvgIcon name="ic-coin" class="size-5" />
            <span class="truncate text-2xl font-bold text-[#0B080B]">{ props.product?.delivery_details?.quanity }</span>
          </x-coins>
          <x-bonus class="mt-1 flex items-center gap-x-1">
            <span class="truncate text-sm font-normal text-[#434546]"> +{props.product?.delivery_details?.bonus} Bonus</span>
          </x-bonus>
        </div>
        <x-price class={`mt-2 flex h-8 items-center justify-center rounded-lg text-base font-medium leading-tight text-[#FC2763] ${props.isHot ? 'w-36 self-end' : 'w-full'} ${props.isSelected ? 'bg-white' : 'bg-[#FFF0F3]'}`}>
          <span>{props.product?.currency_symbol}</span>
          <span class="ml-1">{(props.product?.price / 100).toFixed(2)}</span>
        </x-price>
      </div>
    </x-payment-product>
  )
})

export default PaymentProduct
