# Skynet

前端 mono repo。理论上这个仓库包含 2024 年 7 月之后的所有前端项目源代码。

## monorepo

Monorepo 是指在一个仓库中管理多个项目。本仓库通过 pnpm 来实现多项目管理。

我们既可以用 VSCode 打开根目录，也可以用 VSCode 单独打开 apps 目录中的某个项目进行开发。

**根目录的主要作用是：**

- 使用 pnpm-workspace.yaml 来让所有子项目的依赖保持相同的版本号
- 使用 pnpm dev 来同时运行所有子项目的开发脚本（一般没有必要这么做，推荐直接到子目录中运行 pnpm dev

## 子项目之间互相引用

1. 通过包名引用
    - 我们应该通过 `import {x} from '@skynet/xxx'` 来引用其他项目，而不应该通过相对路径来引用。
    - 同时我们还需要在子项目的 package.json 中添加 `"@skynet/xxx": "workspace:*"`
1. 避免循环引用
    - 假设 apps/skylink-web 引用了 packages/skylink-sdk，那么 packages/skylink-sdk 就不应该引用 apps/skylink-web。

## 开发

apps 目录中的每个项目都可以独立开发，具体开发方式请参考各项目的 README。 流程都是类似的，以 apps/skylink-web 为例：
注意：pnpm 版本需要指定为9.5

1. `pnpm i`
2. `cd apps/skylink-web`
3. `pnpm dev`

你也可以直接在根目录运行 `pnpm dev` 来开发所有项目。

## 部署

apps 目录中的每个项目都可以独立部署，具体部署方式请参考各项目的 README。流程都是类似的，以 apps/skylink-web 项目的 test 环境部署为例：

1. `cd apps/skylink-web`
1. `pnpm i; pnpm deploy:test`

`pnpm deploy:test` 脚本会在本地 build 代码，然后把 dist 目录上传至另一个远程仓库，该远程仓库的 test 分支和
main 分支有自动部署流程。

