import { bindLoading, createCachedFn, createUuid } from '@skynet/shared'
import { ref } from 'vue'
import { useGoogleAd } from '../google-ad/use-google-ad'
import { seriesApi } from './series-api'

// createCachedFn 内部不能使用 computed 和 watch，只能暴露函数！！！
export const useSeriesItem = createCachedFn((series_id: string) => {
  const seriesItem = ref<M.Dramato | null>(null)
  const currentEpisodeId = ref<string | null>(null)
  const currentAdId = ref<string | null>(null)
  const fetchingSeriesItem = ref(false)
  const sessionId = ref(new Date().getTime())
  const unlockOverlayVisible = ref(false)
  const productPanelVisible = ref(false)
  const manualUnlockPanelVisible = ref(false)
  const obviousWatermarkVisible = ref(false)
  const faqPanelVisible = ref(false)
  const nonObviousWatermarkVisible = ref(false)
  const requestingFollow = ref(false)
  const activeEpisodes = ref<Array<M.Episode | Ad>>([])
  const activeIndex = ref<number>()
  const polyfillInstalled = ref(false)
  const isPlayingEpisode = ref(false)
  const { adCache } = useGoogleAd()
  /**
   * 这些剧集后面是有广告的
   */
  const episodeIdsHavingAdAfterIt = ref<string[]>([])
  /**
   * 非明显水印的位置
   */
  const nonObviousWatermarkPosition = ref<{ x: number, y: number }>({ x: 0, y: 0 })
  const episodeSelectMenuVisible = ref(false)
  /**
   * 用户进入页面时第一个剧集的ID
   */

  /**
   * 用户触发付费点，登录检查
   */
  const anonymousUserLoginCheck = ref(false)

  function getEpisodesWithAds() {
    const episodes = seriesItem.value?.episode_list ?? []
    const result: Array<M.Episode | Ad> = [...episodes]
    episodeIdsHavingAdAfterIt.value.forEach(id => {
      const index = result.findIndex(episode => episode && !('kind' in episode) && episode.id === id)
      if (index !== -1) {
        result.splice(index + 1, 0, adCache[id])
      }
    })
    return result
  }
  function prefetchVideo() {
    const prev = getPreviousEpisode()
    if (prev) {
      const url = getPlayUrl(prev)
      void fetch(url)
    }
    const next = getNextEpisode()
    if (next) {
      const url = getPlayUrl(next)
      void fetch(url)
    }
  }
  function getPlayUrl(episodeOrEpisodeId?: string | M.Episode) {
    const ep = typeof episodeOrEpisodeId === 'string'
      ? findEpisode(episodeOrEpisodeId)
      : episodeOrEpisodeId
    if (!ep) return ''
    return ep.external_audio_h264_m3u8 || ep.m3u8_url
      || ep.video_url || ep.external_audio_h265_m3u8 || ''
  }
  function getCurrentEpisode() {
    return findEpisode(currentEpisodeId.value) ?? null
  }
  function getSlotIndex(episodeId?: string) {
    const id = episodeId || currentEpisodeId.value
    const currentIndex = getIndexById(id)
    return currentIndex + 1
  }
  function getIndexById(episodeId?: string | null) {
    if (!episodeId) return -1
    return seriesItem.value?.episode_list.findIndex(episode => episode.id === episodeId) ?? -1
  }
  function getCurrentEpisodeIndex() {
    return seriesItem.value?.episode_list.findIndex(episode => episode.id === currentEpisodeId.value) ?? -1
  }
  function getCurrentEpisodeNumber(callback?: (n: number) => void) {
    const index = getCurrentEpisodeIndex()
    if (index === -1) {
      return null
    }
    if (seriesItem.value?.episode_list[0]) {
      const result = callback?.(index + 1)
      return result ?? index + 1
    }
    return null
  }
  function getNextEpisode() {
    const index = seriesItem.value?.episode_list.findIndex(episode => episode.id === currentEpisodeId.value) ?? -1
    return seriesItem.value?.episode_list[index + 1] ?? null
  }
  function getPreviousEpisode() {
    return getPreviousEpisodeById(currentEpisodeId.value)
  }
  function findEpisodeByIndex(index: number) {
    if (index < 0 || index >= (seriesItem.value?.episode_list.length ?? 0)) return null
    return seriesItem.value?.episode_list[index] ?? null
  }
  function findEpisode(id?: string | null) {
    if (!id) return null
    return seriesItem.value?.episode_list.find(episode => episode.id === id) ?? null
  }
  function getPreviousEpisodeById(id?: string | null) {
    if (!id) return null
    const index = seriesItem.value?.episode_list.findIndex(episode => episode.id === id) ?? -1
    return seriesItem.value?.episode_list[index - 1] ?? null
  }
  function getNextEpisodeById(id?: string | null) {
    if (!id) return null
    const index = seriesItem.value?.episode_list.findIndex(episode => episode.id === id) ?? -1
    return seriesItem.value?.episode_list[index + 1] ?? null
  }
  async function unlockEpisode(episode_id: string) {
    const response = await seriesApi.unlockEpisode({ series_id, episode_id })
    if (!response?.data) return
    const ep = findEpisode(episode_id)
    if (!ep) return
    Object.assign(ep, response.data)
    unlockOverlayVisible.value = false
    productPanelVisible.value = false
  }
  /**
   * 手动解锁剧集。
   * 一般在发现用户有余额但是没有开启自动解锁功能的时候，才会调用此函数来解锁剧集。
   * 如果发现用户开启了自动解锁功能，则应该优先调用 `unlockEpisode` 来解锁剧集。
   * @param episode_id
   * @param autoUnlock 是否设置自动解锁
   * @returns
   */
  async function manualUnlockEpisode(episode_id: string, autoUnlock: boolean) {
    const episode = findEpisode(episode_id)
    if (!episode) throw new Error('Episode not found')
    if (episode.unlock) return // 已经解锁了
    const response = await seriesApi.manualUnlockEpisode({
      series_id: series_id,
      episode_id: episode_id,
      auto_unlock: autoUnlock,
    })
    if (!response?.data) return
    Object.assign(episode, response.data)
    manualUnlockPanelVisible.value = false
    unlockOverlayVisible.value = false
  }
  async function follow(yes: boolean) {
    if (yes) {
      await bindLoading(seriesApi.follow(series_id), requestingFollow)
    } else {
      await bindLoading(seriesApi.unfollow(series_id), requestingFollow)
    }
  }
  async function fetchSeriesItem() {
    const response = await bindLoading(seriesApi.getSeriesItem(series_id), fetchingSeriesItem)
    seriesItem.value = response ?? null
    return response
  }
  /**
   * 提前加载前后 1 集视频
   */
  function updateActiveEpisodes() {
    const currentIndex = getCurrentEpisodeIndex()
    if (currentIndex === -1) return
    const episodeList = seriesItem.value?.episode_list ?? []
    const start = Math.max(0, currentIndex - 1)
    const end = Math.min(episodeList.length, currentIndex + 1)
    activeEpisodes.value = episodeList.slice(start, end + 1)
    episodeIdsHavingAdAfterIt.value.forEach(id => {
      insertAdAfter(id)
    })
    activeIndex.value = currentIndex
  }

  function removeAdAfter(episode_id: M.Episode['id']) {
    const cache = adCache[episode_id]
    if (cache) {
      delete adCache[episode_id]
    }
    const index = activeEpisodes.value.findIndex(episode => !('kind' in episode) && episode.id === episode_id)
    if (index === -1) return
    activeEpisodes.value.splice(index + 1, 0)
    episodeIdsHavingAdAfterIt.value = episodeIdsHavingAdAfterIt.value.filter(id => id !== episode_id)
  }
  function insertAdAfter(episode_id: M.Episode['id'], dom?: HTMLElement) {
    const cache = adCache[episode_id]
    if (!dom && cache?.dom) {
      dom = cache.dom
    }
    if (!dom) return
    const adId = cache?.id ?? createUuid('ad-')
    if (!cache) {
      adCache[episode_id] = { id: adId, kind: 'ad', dom: dom }
    }
    const index = activeEpisodes.value.findIndex(episode => !('kind' in episode) && episode.id === episode_id)
    if (index === -1) return
    activeEpisodes.value.splice(index + 1, 0, { kind: 'ad', id: adId, dom })
    if (episodeIdsHavingAdAfterIt.value.includes(episode_id)) return
    episodeIdsHavingAdAfterIt.value.push(episode_id)
  }

  function getIndexInActiveEpisodes(episodeIdOrAdId: string): number {
    if (episodeIdOrAdId.startsWith('ad-')) {
      const index = activeEpisodes.value.findIndex(item => item.id === episodeIdOrAdId)
      const episode = activeEpisodes.value[index - 1]
      const result = getIndexInActiveEpisodes(episode.id)
      return result + 1
    } else {
      const index = getIndexById(episodeIdOrAdId)
      const prevIds = seriesItem.value?.episode_list.slice(0, index).map(episode => episode.id) ?? []
      const adCount = episodeIdsHavingAdAfterIt.value.filter(id => prevIds?.includes(id)).length
      return index + adCount
    }
  }

  return {
    getIndexInActiveEpisodes,
    episodeIdsHavingAdAfterIt,
    insertAdAfter,
    removeAdAfter,
    seriesItem,
    currentEpisodeId,
    fetchSeriesItem,
    fetchingSeriesItem,
    getCurrentEpisode,
    getNextEpisode,
    getPreviousEpisode,
    findEpisode,
    unlockEpisode,
    getPreviousEpisodeById,
    getNextEpisodeById,
    follow,
    getCurrentEpisodeIndex,
    getCurrentEpisodeNumber,
    sessionId,
    getSlotIndex,
    unlockOverlayVisible,
    productPanelVisible,
    manualUnlockPanelVisible,
    episodeSelectMenuVisible,
    manualUnlockEpisode,
    obviousWatermarkVisible,
    nonObviousWatermarkVisible,
    nonObviousWatermarkPosition,
    prefetchVideo,
    requestingFollow,
    activeEpisodes,
    updateActiveEpisodes,
    activeIndex,
    getIndexById,
    getEpisodesWithAds,
    anonymousUserLoginCheck,
    currentAdId,
    findEpisodeByIndex,
    polyfillInstalled,
    faqPanelVisible,
    isPlayingEpisode,
  }
})
