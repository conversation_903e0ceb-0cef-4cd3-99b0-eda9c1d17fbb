import { get, mapKeys, pick, pickBy } from 'lodash-es'
import { ComputedRef, PropType, Ref, computed, defineComponent, h, isRef, ref } from 'vue'
import { ZodEffects, ZodError, ZodErrorMap, ZodIssueCode, ZodParsedType, util, z } from 'zod'
import { mc } from './merge-class'

export const rule = z

export type ValidationErrorItem = { message: string }

export type ValidationError = Record<string, ValidationErrorItem[]>

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Schema = ReturnType<typeof z.object> | ReturnType<typeof z.array> | ZodEffects<any>

export const useValidator = <T extends undefined | null | Record<string, unknown> | Array<Record<string, unknown>>>(
  formData: Ref<T> | ComputedRef<T>,
  schema: Schema | Ref<Schema | null> | ComputedRef<Schema | null>,
) => {
  const error = ref<ValidationError>({})
  const validate = (...names: Array<string | number>) => {
    names.forEach(name => {
      error.value[name] = []
    })
    const e = getErrors(formData.value, schema)
    const picked = pick(e, names)
    Object.assign(error.value, picked)
    return isEmptyError(picked) ? true : false
  }
  const validatePrefix = (...namePrefixes: Array<string | number>) => {
    namePrefixes.forEach(name => {
      error.value[name] = []
    })
    for (const key in error.value) {
      if (namePrefixes.some(p => key.startsWith(p.toString()))) {
        error.value[key] = []
      }
    }
    const e = getErrors(formData.value, schema)
    const picked = pickBy(e, (_, key) => namePrefixes.some(p => key.startsWith(p.toString())))
    Object.assign(error.value, picked)
    return isEmptyError(picked) ? true : false
  }
  const getErrors = (formData: Partial<T>, _schema: typeof schema, options: GetErrorsOptions = {}) => {
    const { include, exclude } = options
    const error = {} as ValidationError
    try {
      (isRef(_schema) ? _schema.value : _schema)?.parse(formData, { errorMap })
    } catch (err: unknown) {
      if (err instanceof ZodError) {
        err.errors.forEach(e => {
          const ps = e.path.join('.')
          if (include && !include.some(item => ps.startsWith(item))) return
          if (exclude && exclude.some(item => ps.startsWith(item))) return
          getOrSetItem(error as Record<string | number, ValidationErrorItem[]>, e.path, { message: e.message })
        })
      }
    }
    return error
  }
  type GetErrorsOptions = { exclude?: string[], include?: string[] }
  /**
   * 验证所有字段.
   * 如果 options 中有 exclude，则排除掉这些字段；
   * 如果 options 中有 include，则只验证这些字段；
   * 如果 options 中没有 exclude 和 include，则验证所有字段.
   * @example validateAll({ exclude: ['a', 'b'] })
   */
  const validateAll = (options?: GetErrorsOptions) => {
    error.value = getErrors(formData.value, schema, options)
    return !hasError()
  }
  /**
   * 判断是否有错误.
   * 如果 key 为 undefined，则判断是否有任何错误；
   * 如果 key 不为 undefined，则判断是否有指定 key 的错误.
   */
  const hasError = (key?: string) => {
    if (key === undefined) {
      return Object.keys(error.value).some(key => hasErrorByKey(key))
    } else {
      return hasErrorByKey(key)
    }
  }
  const hasErrorByKey = (key: string) => {
    return !!error.value[key]?.[0]
  }
  const _hasErrorWithPrefix = (prefix: string) => {
    return hasErrorWithPrefix(error.value, prefix)
  }
  const ErrorSpan = defineComponent({
    props: {
      keyName: {
        type: [Number, String, Array] as PropType<number | string | Array<number | string>>,
        required: true,
      },
      class: {
        type: String,
      },
    },
    setup(props) {
      const name = props.keyName instanceof Array ? props.keyName.join('.') : props.keyName
      const item = computed(() => error.value[name])
      return () =>
        isNotEmptyErrorItems(item.value) && (
          h('span', { class: mc('', props.class) }, { default: () => item.value[0]?.message })
        )
    },
  })
  /**
   * 获取带有指定前缀的错误信息.
   * 假设 error 为 { 'a': [error1], 'a.b': [error2], 'a.b.c': [error3], 'a.b.c.d': [error4] },
   * 那么 errorWithPrefix('a.b') 返回 { '': [error2], 'c': [error3], 'c.d': [error4] }
   * ```
   */
  const _errorWithPrefix = (path: string) => {
    return errorWithPrefix(error.value, path)
  }

  return {
    validateAll,
    validate,
    validatePrefix,
    error,
    errorWithPrefix: _errorWithPrefix,
    ErrorSpan,
    hasError,
    hasErrorWithPrefix: _hasErrorWithPrefix,
  }
}

/**
 * 获取第一个错误信息
 */
export const getMessageByName = <T extends Record<string, ValidationErrorItem[]>>(error: T, name: keyof T) => {
  return error[name]?.[0]?.message || ''
}
/**
 * 获取所有错误信息
 */
export const getMessagesByName = <T extends Record<string, ValidationErrorItem[]>>(error: T, name: keyof T) => {
  return error[name]?.join('，') || ''
}

export const getMessages = <T extends Record<string, ValidationErrorItem[]>>(error: T): string[] => {
  const errors = Object.entries(error).reduce((acc, [key, items]) => {
    acc[key] = items.map(item => item.message)
    return acc
  }, {} as Record<string, string[]>)
  return Object.values(errors).flat().filter(Boolean)
}

function getOrSetItem(
  obj: Record<string, ValidationErrorItem[]>,
  path: Array<string | number>,
  item: ValidationErrorItem,
) {
  const value = get(obj, path) as ValidationErrorItem[]
  if (value) {
    value.push(item)
  } else {
    obj[path.join('.')] = [item]
  }
  return obj
}

/**
 * 判断是否为空错误
 * @param error
 */
export const isEmptyErrorItems = (error?: ValidationErrorItem[]) => {
  return error?.[0] === undefined
}

export const isNotEmptyErrorItems = (error?: ValidationErrorItem[]) => {
  return !isEmptyErrorItems(error)
}

export const isEmptyError = (error: Record<string, ValidationErrorItem[] | undefined>) => {
  return !isNotEmptyError(error)
}

export const isNotEmptyError = (error: Record<string, ValidationErrorItem[] | undefined>) => {
  return Object.values(error).some(isNotEmptyErrorItems)
}

/**
 * 递归地将obj中value为undefined的值全部改为空字符串
 * @param obj
 * @returns
 */
export const convertUndefinedToString = (obj: Record<string, any>) => {
  for (const key in obj) {
    if (obj[key] === undefined) {
      obj[key] = ''
    } else if (typeof obj[key] === 'object') {
      convertUndefinedToString(obj[key])
    }
  }
}

export const validate = (data: unknown, _schema: Schema) => {
  const error = {} as ValidationError
  try {
    _schema.parse(data, { errorMap })
  } catch (err: unknown) {
    if (err instanceof ZodError) {
      err.errors.forEach(e => {
        getOrSetItem(error as Record<string | number, ValidationErrorItem[]>, e.path, { message: e.message })
      })
    }
  }
  return error
}

const errorWithPrefix = (error: ValidationError, path: string) => {
  const prefix = `${path}.`
  return {
    '': error[path],
    ...mapKeys(
      pickBy(error, (_, key) => key.startsWith(prefix)), // 找到以 prefix 开头的字段
      (_, key) => key.substring(prefix.length), // 删掉 key 里的 prefix
    ),
  }
}

export const hasErrorWithPrefix = (_error: ValidationError, prefix: string) => {
  const error = errorWithPrefix(_error, prefix)
  return !isEmptyError(error)
}

// 这些代码来自 https://github.com/colinhacks/zod/blob/master/src/locales/en.ts
// 由 AI 自动把英文翻译成了中文
export const errorMap: ZodErrorMap = (issue, _ctx) => {
  if (_ctx.defaultError) {
    return { message: _ctx.defaultError }
  }
  let message: string
  switch (issue.code) {
    case ZodIssueCode.invalid_type:
      console.log('_ctx:', _ctx, issue)
      if (issue.received === ZodParsedType.undefined) {
        message = '必填'
      } else {
        message = `期望的是 ${issue.expected}，但接收到的是 ${issue.received}`
      }
      break
    case ZodIssueCode.invalid_literal:
      message = `无效的字面量值，期望的是 ${JSON.stringify(issue.expected, util.jsonStringifyReplacer)}`
      break
    case ZodIssueCode.unrecognized_keys:
      message = `对象中包含未识别的键：${util.joinValues(issue.keys, ', ')}`
      break
    case ZodIssueCode.invalid_union:
      message = '输入无效'
      break
    case ZodIssueCode.invalid_union_discriminator:
      message = `无效的鉴别器值。期望的是 ${util.joinValues(issue.options)}`
      break
    case ZodIssueCode.invalid_enum_value:
      message = `无效的枚举值。期望的是 ${util.joinValues(issue.options)}，但接收到的是 '${issue.received}'`
      break
    case ZodIssueCode.invalid_arguments:
      message = '函数参数无效'
      break
    case ZodIssueCode.invalid_return_type:
      message = '函数返回类型无效'
      break
    case ZodIssueCode.invalid_date:
      message = '日期无效'
      break
    case ZodIssueCode.invalid_string:
      if (typeof issue.validation === 'object') {
        if ('includes' in issue.validation) {
          message = `无效输入：必须包含 "${issue.validation.includes}"`
          if (typeof issue.validation.position === 'number') {
            message = `${message}，且位置必须大于或等于 ${issue.validation.position}`
          }
        } else if ('startsWith' in issue.validation) {
          message = `无效输入：必须以 "${issue.validation.startsWith}" 开头`
        } else if ('endsWith' in issue.validation) {
          message = `无效输入：必须以 "${issue.validation.endsWith}" 结尾`
        } else {
          util.assertNever(issue.validation)
        }
      } else if (issue.validation !== 'regex') {
        message = `无效的 ${issue.validation}`
      } else {
        message = '无效'
      }
      break
    case ZodIssueCode.too_small:
      // eslint-disable-next-line no-case-declarations
      const minMessage = issue.exact ? '恰好' : issue.inclusive ? '至少' : '超过'
      if (issue.type === 'array') message = `数组${minMessage}包含 ${issue.minimum} 个元素`
      else if (issue.type === 'string') message = issue.minimum > 1 ? `${minMessage}包含 ${issue.minimum} 个字符` : '必填'
      else if (issue.type === 'number') message = `数字必须${minMessage} ${issue.minimum}`
      else if (issue.type === 'date') message = `日期必须${minMessage} ${new Date(Number(issue.minimum)).toLocaleString()}`
      else message = '输入无效'
      break
    case ZodIssueCode.too_big:
      // eslint-disable-next-line no-case-declarations
      const maxMessage = issue.exact ? '恰好' : issue.inclusive ? '最多' : '少于'
      if (issue.type === 'array') message = `数组的长度必须${maxMessage} ${issue.maximum} 个元素`
      else if (issue.type === 'string') message = `字符串的长度必须${maxMessage} ${issue.maximum} 个字符`
      else if (issue.type === 'number') message = `数字必须${maxMessage} ${issue.maximum}`
      else if (issue.type === 'bigint') message = `BigInt 数值必须${maxMessage} ${issue.maximum}`
      else if (issue.type === 'date') message = `日期必须${maxMessage} ${new Date(Number(issue.maximum)).toLocaleString()}`
      else message = '输入无效'
      break
    case ZodIssueCode.custom:
      message = '输入无效'
      break
    case ZodIssueCode.invalid_intersection_types:
      message = '交集结果无法合并'
      break
    case ZodIssueCode.not_multiple_of:
      message = `数字必须是 ${issue.multipleOf} 的倍数`
      break
    case ZodIssueCode.not_finite:
      message = '数字必须是有限的'
      break
    default:
      message = _ctx.defaultError
      util.assertNever(issue)
  }
  return { message }
}
