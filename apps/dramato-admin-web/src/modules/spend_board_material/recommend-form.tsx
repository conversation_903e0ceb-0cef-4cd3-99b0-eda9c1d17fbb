/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert, SvgIcon, transformTimestamp, transformInteger } from '@skynet/ui'
import { set } from 'lodash-es'
// import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { ref } from 'vue'
import { Fn } from '@vueuse/core'
import dayjs from 'dayjs'
import { apiAddEditTaskListStatus, apiDeleteTaskListStatus } from '../spend_board/spend_board-api'

type RecommendFormOptions = {
  props: {
    languages: Array<{ label: string, value: string }>
    series_resource_id: number
    recommends: Array<{
      lang: string
      deploy_task_time: number
      team: 1 | 2 // 1 昆仑 2 盈亮
      comment: string // 备注
      audio_type: number
    }>
  }
  emits: {
    cancel: Fn
    ok: () => void
  }
}
export const RecommendForm = createComponent<RecommendFormOptions>({
  props: {
    languages: [],
    series_resource_id: 0,
    recommends: [],
  },
  emits: {
    cancel: fn,
    ok: fn,
  },
}, (props, { emit }) => {
  const isUpdating = ref(false)
  const params = ref<{
    lang: string
    deploy_task_time: number
    comment: string
    team: 1 | 2
    local: boolean
  }[]>(props.recommends.length
    ? props.recommends.map(i => ({
      lang: i.lang.includes('??') ? i.lang : i.lang + '??' + i.audio_type,
      audio_type: i.audio_type,
      deploy_task_time: i.deploy_task_time,
      comment: i.comment,
      team: i.team,
      local: false,
    }))
    : [{
        lang: '',
        deploy_task_time: 0,
        comment: '',
        team: 1,
        local: true,
      }])

  const Form = CreateForm<{
    lang: string
    deploy_task_time: number
    comment: string
    team: 1 | 2
  }[]>()

  const formRules = z.array(z.object({
    deploy_task_time: z.number().min(1, '请选择剧单日期'),
    lang: z.string().min(1, '请选择推荐语言'),
    team: z.number().min(1, '请选择投放团队'),
  }))

  const { error, validateAll } = useValidator(params, formRules)

  const addItem = () => {
    params.value.push({
      lang: '',
      deploy_task_time: 0,
      comment: '',
      team: 1,
      local: true,
    })
  }

  const deleteItem = (index: number) => {
    if (params.value[index].local) {
      params.value.splice(index, 1)
      if (params.value.length === 0) {
        addItem()
      }
      return
    }
    // delete from server
    void apiDeleteTaskListStatus({
      lang: params.value[index].lang.split('??')[0],
      audio_type: +params.value[index].lang.split('??')[1],
      team: params.value[index].team,
      series_resource_id: props.series_resource_id,
    }).then(() => {
      params.value.splice(index, 1)
      if (params.value.length === 0) {
        addItem()
      }
    })
  }

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="grid grid-cols-1 gap-y-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            console.log('path', path, 'value', value)
            set(params.value || {}, path, value)
          }}
          items={
            params.value.map((lang, i) =>
              [
                'grid grid-cols-5 gap-y-1 p-2',
                [
                  '推荐语言',
                  i + '.lang',
                  {
                    type: 'select',
                    options: props.languages,
                    autoInsertEmptyOption: false,
                  },

                ],
                [
                  '制定投放团队',
                  i + '.team',
                  {
                    type: 'select',
                    options: [{ label: '昆仑', value: 1 }, { label: '盈亮', value: 2 }],
                    autoInsertEmptyOption: false,
                  },
                  {
                    transform: transformInteger,
                  },
                ], [
                  '剧单日期',
                  i + '.deploy_task_time',
                  { type: 'date', class: 'leading-[32px]', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD' },
                  {
                    transform: transformTimestamp,
                  },
                ],
                [
                  '任务备注',
                  i + '.comment',
                  {
                    type: 'text',
                    placeholder: '请输入任务备注',
                  },
                ], [
                  '',
                  '',
                  {
                    type: 'custom', render: () => (
                      <div class="flex justify-start gap-x-2">
                        <SvgIcon
                          class={mc('size-6 cursor-pointer text-gray-500')}
                          name="ic_close"
                          onClick={() => deleteItem(i)}
                        />
                        {i === params.value.length - 1
                        && (
                          <SvgIcon
                            class={mc('size-7 -mt-[3px] cursor-pointer text-gray-500')}
                            name="ic_add"
                            onClick={addItem}
                          />
                        )}
                      </div>
                    ),
                  },
                  { class: 'pt-7' },
                ]],
            )
          }
          data={params.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={() => emit('cancel')}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const exclude: string[] = []

            if (!validateAll({ exclude })) {
              console.log('err', error)

              return
            }

            isUpdating.value = true
            void apiAddEditTaskListStatus({
              series_resource_id: props.series_resource_id,
              tasks: params.value.map(i => ({
                lang: i.lang.split('??')[0],
                audio_type: +i.lang.split('??')[1],
                deploy_task_time: i.deploy_task_time,
                team: i.team,
                comment: i.comment,
              })),
            }).then(() => {
              emit('ok')
            }).catch((error: any) => {
              showAlert(error.response.data.err_msg || error.response.data.message || '更新失败', 'error')
            }).finally(() => {
              isUpdating.value = false
            })
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确认推荐'}
        </Button>
      </div>
    </>
  )
})

export default RecommendForm
