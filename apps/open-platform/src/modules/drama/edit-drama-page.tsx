import { computed, defineComponent, reactive, ref, watchEffect } from 'vue'
import { ElInput, ElButton, ElSelect, ElOption, ElForm, ElFormItem, ElDatePicker, ElRadioGroup, ElRadio, FormRules, FormInstance, ElImage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { Uploader } from '../common/uploader/uploader'
import { useMyDrama } from './use-my-drama'
import { Button, SvgIcon } from '@skynet/ui'
import { langCodeConfig } from '../common/constants'
import { useI18n } from 'vue-i18n'

export default defineComponent({
  name: 'EditDramaPage',
  setup() {
    const { t } = useI18n()
    const router = useRouter()
    const route = useRoute()
    const { currentDrama, createOrUpdateDrama, getTempPath, getDramaDetail, dramaDetailLoading, dramaCreateLoading, checkIsEditable } = useMyDrama()

    watchEffect(() => {
      if (route?.params.id) {
        void getDramaDetail(route.params.id as string)
      }
    })

    const isEditable = computed(() => {
      return checkIsEditable(currentDrama.value.audit_status!)
    })

    const form = ref<FormInstance>()
    const handleSubmit = async () => {
      console.log('提交表单:', currentDrama.value)
      await form.value?.validate(async (valid, fields) => {
        if (valid) {
          console.log('submit!')
          if (isEditable.value) {
            const res = await createOrUpdateDrama(currentDrama.value)
            if (res?.code === 200) {
              void router.push('/my-drama/upload/' + res.data.resource_key)
            }
          } else {
            void router.push('/my-drama/upload/' + currentDrama.value.resource_key)
          }
        } else {
          console.log('error submit!', fields)
        }
      })
    }

    const rules = reactive<FormRules<M.MyDrama.DramaCreateParams>>({
      title: [
        { required: true, message: t('titleCannotBeEmpty'), trigger: 'blur' },
        { min: 0, max: 15, message: t('titleCannotExceed15Characters'), trigger: 'blur' },
      ],
      description: [
        { required: true, message: t('profileCannotBeEmpty'), trigger: 'blur' },
        { min: 50, max: 500, message: t('introductionShouldBeBetween50And500Words'), trigger: 'blur' },
      ],
      video_type: [
        { required: true, message: t('videoTypeCannotBeEmpty'), trigger: 'change' },
      ],
      count: [
        { required: true, message: t('totalNumberOfEpisodesCannotBeEmpty'), trigger: 'blur' },
        // { type: 'number', message: '总集数必须为数字', trigger: 'blur' },
      ],
      unlocked_episodes: [
        { required: true, message: t('unlockEpisodesCannotBeEmpty'), trigger: 'blur' },
        // { type: 'number', message: '解锁集数必须为数字', trigger: 'blur' },
      ],
      language_code: [
        { required: true, message: t('vocalLanguageCannotBeEmpty'), trigger: 'change' },
      ],
      subtitle_language_code: [
        { required: true, message: t('subtitleLanguageCannotBeEmpty'), trigger: 'change' },
      ],
      auth_start_time: [
        { required: true, message: t('authorizationStartTimeCannotBeEmpty'), trigger: 'change' },
      ],
      auth_end_time: [
        { required: true, message: t('authorizationEndTimeCannotBeEmpty'), trigger: 'change' },
      ],
    })

    return () => (
      <div class="p-6">
        <div class="mb-8 flex cursor-pointer items-center" onClick={() => history.back()}>
          <SvgIcon name="ic-chevron-down" class="mr-1 size-6" />
          <h1 class="text-[22px] font-medium">{t('releaseNewDramaBasicInfo')}</h1>
        </div>

        <div class="flex gap-8" v-loading={dramaDetailLoading.value || dramaCreateLoading.value}>
          <div class="flex flex-col items-center gap-3">
            {
              currentDrama.value.vertical_cover_for_show
                ? (
                    <ElImage
                      src={currentDrama.value.vertical_cover_for_show}
                      class="h-[164px] w-[123px] overflow-hidden rounded-lg object-cover"
                    >
                      {{ error: () => (
                        <SvgIcon name="video-card" class="size-full text-transparent " />
                      ),
                      placeholder: () => (
                        <SvgIcon name="video-card" class="size-full text-transparent " />
                      ) }}
                    </ElImage>
                  )
                : (
                    <SvgIcon name="video-card" class="h-[164px] w-[123px] text-transparent " />
                  )
            }
            <Uploader
              accept="png,jpg,jpeg"
              maxsize={1024 * 1024 * 10}
              ossKeyType="resource"
              isImage={false}
              class="  cursor-pointer overflow-hidden rounded-md"
              onUploadSuccess={async d => {
                currentDrama.value.vertical_cover = d.temp_path!
                const data = await getTempPath({ path: d.temp_path! })
                currentDrama.value.vertical_cover_for_show = data.data.path
              }}
              showFileList={false}
            ><Button class="btn btn-info btn-sm py-[5px]">{t('upload')}</Button>
            </Uploader>

          </div>

          <ElForm disabled={!isEditable.value} ref={form} model={currentDrama.value} rules={rules} class="flex-1" label-position="left" labelWidth={80}>
            <ElFormItem label={t('titleOfThePlay')} prop="title">
              <ElInput
                v-model={currentDrama.value.title}
                placeholder={t('enterTitle')}
                maxlength={15}
                show-word-limit
              />
            </ElFormItem>

            <ElFormItem label={t('introduction')} prop="description">
              <ElInput
                v-model={currentDrama.value.description}
                type="textarea"
                rows={9}
                placeholder={t('enterShortPlaySummary')}
                maxlength={500}
                resize="none"
                show-word-limit
              />
            </ElFormItem>
            <ElFormItem label={t('videoType')} prop="video_type">
              <ElRadioGroup v-model={currentDrama.value.video_type}>
                <ElRadio label={t('videoWithSubtitles')} value={1} />
                <ElRadio label={t('videoWithoutSubtitles')} value={2} />
              </ElRadioGroup>
            </ElFormItem>

            <ElFormItem label={t('totalEpisodes')} prop="count">
              <ElInput
                v-model={currentDrama.value.count}
                class="w-full"
                type="number"

              >
                {{
                  suffix: () => <span class="text-[#A1A0A3]">{t('episodes')}</span>,
                }}
              </ElInput>
            </ElFormItem>

            <ElFormItem label={t('unlockEpisodes')} prop="unlocked_episodes">
              <ElInput
                v-model={currentDrama.value.unlocked_episodes}
                class="w-full"
                type="number"
              >
                {{
                  suffix: () => <span class="text-[#A1A0A3]">{t('episodes')}</span>,
                }}
              </ElInput>
            </ElFormItem>

            <ElFormItem label={t('vocalLanguage')} prop="language_code">
              <ElSelect
                v-model={currentDrama.value.language_code}
                class="w-full"
                placeholder={t('selectVocalLanguage')}
              >
                {
                  langCodeConfig.map(d => (
                    <ElOption label={t(d.label)} value={d.value} key={d.value} />
                  ))
                }
              </ElSelect>
            </ElFormItem>

            <ElFormItem label={t('subtitleLanguage')} prop="subtitle_language_code">
              <ElSelect
                v-model={currentDrama.value.subtitle_language_code}
                class="w-full"
                placeholder={t('selectVocalLanguage')}
              >
                {
                  langCodeConfig.map(d => (
                    <ElOption label={d.label} value={d.value} key={d.value} />
                  ))
                }
              </ElSelect>
            </ElFormItem>
            <ElFormItem label={t('authorizationTime')} prop="auth_start_time">
              <ElDatePicker
                class=" flex-1"
                v-model={currentDrama.value.auth_start_time}
                type="date"
                value-format="x"
                clearable={false}
                placeholder={t('selectAuthorizationStartTime')}
                prefixIcon={<SvgIcon name="ic-calendar-days" class="!size-5" />}
              >
                {{
                  suffix: () => <span class="text-[#A1A0A3]">年</span>,
                }}
              </ElDatePicker>
              <span class="mx-4">{t('to')}</span>
              <ElDatePicker
                class=" flex-1"
                v-model={currentDrama.value.auth_end_time}
                type="date"
                value-format="x"
                placeholder={t('selectAuthorizationEndTime')}
                clearable={false}
                prefixIcon={<SvgIcon name="ic-calendar-days" class="!size-5" />}
              />
            </ElFormItem>
          </ElForm>
        </div>

        <div class="mt-8 flex justify-end gap-4">
          <Button class="btn btn-info btn-sm w-[120px] py-3" onClick={() => history.back()}> {t('cancel')}</Button>
          <Button onClick={handleSubmit} class="btn btn-primary btn-sm w-[120px] py-3">{t('nextStep')}</Button>
        </div>
      </div>
    )
  },
})
