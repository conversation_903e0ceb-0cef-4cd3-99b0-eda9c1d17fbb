#!/bin/bash
# 设置错误处理函数
handle_error() {
  echo "发生错误，正在回到原始分支: $CURRENT_BRANCH"
  git checkout "$CURRENT_BRANCH"
  exit 1
}

# 设置错误捕获
set -e
trap handle_error ERR

# Get the name of the current branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

# Get the name of the default branch (main or master)
# DEFAULT_BRANCH=$(git remote show origin | grep "HEAD branch" | sed 's/.*: //')
DEFAULT_BRANCH=main

# If we couldn't determine the default branch, assume it's main
if [ -z "$DEFAULT_BRANCH" ]; then
  DEFAULT_BRANCH="main"
fi

echo "Current branch: $CURRENT_BRANCH"
echo "Default branch: $DEFAULT_BRANCH"

# Checkout the default branch if not already on it
if [ "$CURRENT_BRANCH" != "$DEFAULT_BRANCH" ]; then
  git checkout "$DEFAULT_BRANCH"
fi

# Remove all local branches except the default branch and the current branch
git branch | grep -v "^\*" | grep -v "$DEFAULT_BRANCH" | grep -v "$CURRENT_BRANCH" | xargs -r git branch -d

# If we switched branches earlier, go back to the original branch
if [ "$CURRENT_BRANCH" != "$DEFAULT_BRANCH" ]; then
  git checkout "$CURRENT_BRANCH"
fi

echo "完成。除了 $DEFAULT_BRANCH 和 $CURRENT_BRANCH 之外的所有本地分支都已被删除。"
