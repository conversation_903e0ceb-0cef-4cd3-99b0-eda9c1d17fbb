<template>
  <div class="voiceover-editor" v-loading="videoLoading" element-loading-background="rgba(122, 122, 122, 0.8)">
    <div class="flex justify-start items-center gap-2 my-4">
      <span class="text-xl font-bold">第{{ serial_number }}集</span>
    </div>
    <!-- 添加视频播放器区域 -->
    <div class="video-player-container" v-if="currentVideo">
      <div class="w-[400px] justify-center items-center flex relative">
        <m3u8-player-wrapper
          v-if="currentVideo"
          class=" h-full"
          :currentVideo="currentVideo"
          :subtitles="subtitles"
          @player-ready="(e: any) => { playerReadyChange(e) }"
        />
        <div class="absolute top-0 left-0 w-full h-full"></div>
      </div>
      <div class="flex-1">
        <Editor
          customClass="w-full h-full"
          :code="code"
          :options="{
            language: 'plaintext',
            formatOnPaste: false,
            tabSize: 2,
            inDiffEditor: false,
            minimap: {
              enabled: false,
            } }"
          :isAsync="true"
          :currentTime="currentTime"
          :onChange="e => {
            code = e
          }"
          @videoProgress="(time: number) => {
            if (videoRef) videoRef?.seek(time)
          }"
        />
      </div>
      <div class="flex-1 p-2" style="background-color: #fff;">
        <h2 class="text-2xl font-bold mb-2">配音编辑</h2>
        <audio ref="audioPlayer" class="w-[80%] ml-4 mb-2" v-if="form.voice_path" controls :src="form.voice_path"></audio>
        <el-form class="mt-2 w-[80%] ml-10" :model="form" label-width="80px">
          <el-form-item label="开始时间">
            <div class="flex items-center gap-2">
              <el-input v-model="form.start_time"  />
              <el-button @click="handleStartChange" type="primary" :disabled="form.order == 0">移动</el-button>
            </div>
          </el-form-item>
          <el-form-item label="时长">
            <el-input v-model="form.time"  />
          </el-form-item>
          <el-form-item label="速度">
            <div class="flex items-center gap-2">
              <div class="w-[200px]">
                <el-slider :min="0.5" :max="2" :step="0.1" v-model="form.speed" />
              </div>
              <div class="whitespace-nowrap ml-2">{{ form.speed }}倍</div>
              <el-button @click="handleSpeedChange" type="primary" :disabled="form.order == 0">确定</el-button>
            </div>
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="form.character" placeholder="请选择角色">
              <el-option :key="item.id" v-for="item in totalData.characters" :label="item.name" :value="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="情感">
            <el-select v-model="form.emotion" placeholder="请选择情感">
              <el-option :key="item" v-for="(item, index) in totalData.emotions" :label="totalData.emotions_cn[index]" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="特效音">
            <el-select v-model="form.effect" placeholder="请选择特效音">
              <el-option :key="item" v-for="(item, index) in totalData.effects" :label="totalData.effects_cn[index]" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="文本">
            <el-input v-model="form.text"  />
          </el-form-item>
          <div class="flex justify-center">
            <el-button type="primary" @click="handleRegenerate" :disabled="form.order == 0">重新生成</el-button>

            <uploadWrapper />
          </div>
        </el-form>
      </div>
    </div>
    <div class="no-video" v-else>
      <el-empty description="请导入视频"></el-empty>
    </div>
    
    <!-- 时间轴编辑器 -->
    <div class="mt-4 h-[350px]">
      <timeLineEditor
        ref="timelineEditor"
        :voicesList="voicesList"
        :totalData="totalData"
        :serial_number="serial_number"
        @prevEpisode="handlePrevEpisode"
        @nextEpisode="handleNextEpisode"
        @voice-split="handleVoiceSplit"
        @voice-delete="handleVoiceDelete"
        @voice-selected="handleVoiceSelected"
        @video-imported="handleVideoImported"
        @time-change="handleTimeChange"
        @play-state-change="handlePlayStateChange"
      />
    </div>
    <div class="flex justify-end items-center gap-2 mt-4">
      <el-button @click="handleCancel" size="large">取消</el-button>
      <el-button @click="handleSave" type="success" size="large">保存</el-button>
      <el-button @click="handleCombine" :loading="btnActing" size="large" type="primary">合成</el-button>
    </div>

    <input 
      type="file" 
      ref="audioInput" 
      accept="audio/*" 
      style="display: none" 
      @change="handleAudioImport"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted, onUnmounted, watch, defineComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import timeLineEditor from './timeLineEditor.vue'
import { openDialog, showFailToast } from '@skynet/ui'
import { M3u8Player } from '../resource/components/m3u8-player'
import Editor from '../resource/components/editor'
import { apiGetVoiceList, apiCombineVoice, apiAdjustVoice, apiRecreateVoice, apiUploadAudio, apiSplitAudio, apiDeleteVoice } from './voiceover-editor-api'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { Uploader } from '../common/uploader/uploader'
import { useGlobalLeaveWarning } from 'src/lib/use-leave'

const route = useRoute()
const router = useRouter()
const series_resource_id = ref<number>(0)
const serial_number = ref<number>(0)
const language = ref<string>('')
const btnActing = ref(false)
const audioInput = ref<HTMLInputElement | null>(null)
const form = ref({
  time: 0,
  character: '',
  emotion: '',
  timbres: '',
  text: '',
  voice_path: '',
  order: 0,
  speed: 1,
  start_time: '0',
  speaker: '',
  speed: 1,
  effect: ''
})
const { start } = useGlobalLeaveWarning()
const currentVideoType = ref(1)
const totalData = ref<any>({})
const playerRef = ref<any>(null)
const tempFiles = ref<any>([])
const hasUploadFiles = ref<any>([])
const uploadFilePath = ref<string>('')
const uploadWrapper = defineComponent({
  setup(props, { emit }) {
    return () => (
      <Uploader
        accept="mp4,mp3,wav"
        ossKeyType="resource"
        isImage={false}
        showFileList={false}
        maxsize={1024 * 1024 * 1024 * 2}
        onUploadFailed={() => {
          ElMessage.error('上传失败')
        }}
        onUploadSuccess={async (file: any) => {
          console.log('file',file)
          uploadFilePath.value = file.temp_path || ''
          if(uploadFilePath.value) {
            const audioUrl = URL.createObjectURL(file.file)
            const audio = new Audio(audioUrl)
            
            // 设置音频属性以减少延迟
            audio.preload = 'auto'
            audio.defaultPlaybackRate = 1.0
            
            await new Promise((resolve) => {
              audio.onloadedmetadata = () => resolve(null)
            })

            const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
            const arrayBuffer = await file.file.arrayBuffer()
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
            const duration = Number(audioBuffer.duration.toFixed(3))
            console.log('duration',duration)

            apiUploadAudio({
              series_resource_id: series_resource_id.value,
              serial_number: serial_number.value,
              language: language.value,
              order: form.value.order,
              duration: duration,
              oss_path: uploadFilePath.value,
            }).then((res: any) => {
              if(res?.data?.success) {
                const exData = timelineEditor?.value?.exportTimeline()
                const timelineData = exData?.audio
                {/* for(const item of timelineData || []) {
                  for(const item2 of voicesList.value) {
                    if(item.order === item2.order) {
                      item.start_time = Number((item2.start_time / 1000).toFixed(3))
                      item.head = item2.head
                      item.tail = item2.tail
                    }
                  }
                } */}
                let newVoice = {}
                for(const item of voicesList.value || []) {
                  if(item.order === form.value.order) {
                    item.voice_path = `${cdnUrl}${res?.data?.oss_path}`
                    newVoice = item
                  }
                }

                ElMessage.success('上传成功')

                timelineEditor.value?.resetClip(newVoice)
                {/* timelineEditor.value?.handleInitVoicesData() */}
              }else{
                ElMessage.error(res?.data?.err_msg || '上传失败')
              }
            })
          }
        }}
      >
        <x-uploader-wrapper class="block">
          <el-button disabled={form.value.order == 0} class="ml-4" type="primary" >上传</el-button>
        </x-uploader-wrapper>
      </Uploader>
    )
  }
})


// 创建一个包装组件来渲染 M3u8Player
const m3u8PlayerWrapper = defineComponent({
  props: {
    currentVideo: String,
    subtitles: Array,
  },
  emits: ['player-ready'],
  setup(props, { emit }) {
    return () => (
      <>
        <M3u8Player
          currentLanguage="en"
          width={'100%'}
          height={'100%'}
          useStorageSetting={false}
          subtitles={props.subtitles as any}
          hideControlBar={true}
          url={props.currentVideo as any}
          onPlayerReady={(e: any) => {
            emit('player-ready', e)
          }}
        />
      </>
    )
  }
})

const handleVoiceDelete = (data: any) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '删除中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    apiDeleteVoice({
      series_resource_id: series_resource_id.value,
      serial_number: serial_number.value,
      language: language.value,
      order: data.order,
    }).then((res: any) => {
      if(res?.data?.success) {
        ElMessage.success('删除成功')
        timelineEditor.value?.deleteClip(data.order)
      }
    }).finally(() => {
      loading.close()
    })
  })
}

const handleVoiceSplit = (data: any) => {
  const loading = ElLoading.service({
    lock: true,
    text: '分割中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  console.log({
    series_resource_id: series_resource_id.value,
    serial_number: serial_number.value,
    language: language.value,
    src_order: data.src_order,
    sep_seg: data.sep_seg,
    src_seg: data.src_seg,
  })
  // return false
  apiSplitAudio({
    series_resource_id: series_resource_id.value,
    serial_number: serial_number.value,
    language: language.value,
    src_order: data.src_order,
    sep_seg: data.sep_seg,
    src_seg: data.src_seg,
  }).then((res: any) => {
    if(res?.data?.new_seg) {
      ElMessage.success('分割成功')
      timelineEditor.value?.appendClip({
        ...res?.data?.new_seg, 
        type: 'audio', 
        voice_path: `${cdnUrl}${res?.data?.new_seg?.voice_path}`,
        start_time: res?.data?.new_seg?.start_time / 1000,
      })
    }
  }).finally(() => {
    loading.close()
  })
}

const handleAudioImport = async (event: Event) => {
    ElMessageBox.confirm('确定要上传吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      const input = event.target as HTMLInputElement
      const file = input.files?.[0]
      if (!file) return

      try {
        const audioUrl = URL.createObjectURL(file)
        const audio = new Audio(audioUrl)
        
        // 设置音频属性以减少延迟
        audio.preload = 'auto'
        audio.defaultPlaybackRate = 1.0
        
        await new Promise((resolve) => {
          audio.onloadedmetadata = () => resolve(null)
        })

        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        const arrayBuffer = await file.arrayBuffer()
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
        const duration = audioBuffer.duration
        console.log('duration',duration)

      } catch (error) {
        console.error('音频上传失败:', error)
      }

      input.value = ''
  })
}

const handleStartChange = () => {

  timelineEditor.value?.handleStartChange({...form.value, start_time: formatTimeToSec(form.value.start_time)})
  console.log('form.value.start_time',form.value.start_time)
}

const handleSpeedChange = () => {
  console.log('form.value.order',form.value.order)
  console.log('form.value.speed',form.value.speed)
  timelineEditor.value?.setClipSpeed(form.value.order, form.value.speed)
}

const handleCancel = () => {
  console.log('取消')
  router.back()
}

const handleSave = () => {
  ElMessageBox.confirm('确定要保存吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    let timelineData: any = []
    if(timelineEditor.value){
      const exData = timelineEditor.value.exportTimeline()
      timelineData = exData.audio
      console.log('timelineData',timelineData)
    }
    apiAdjustVoice({
      series_resource_id: series_resource_id.value,
      serial_number: serial_number.value,
      language: language.value,
      voices: timelineData,
    }).then(() => {
      ElMessage.success('保存成功')
    })
  })
}

const handleCombine = () => {
  ElMessageBox.confirm('确定要合成吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    let timelineData: any = []
    if(timelineEditor.value){
      const exData = timelineEditor.value.exportTimeline()
      timelineData = exData.audio
      console.log('timelineData',timelineData)
    }
    btnActing.value = true
    apiAdjustVoice({
      series_resource_id: series_resource_id.value,
      serial_number: serial_number.value,
      language: language.value,
      voices: timelineData,
    }).then(() => {

      apiCombineVoice({
        series_resource_id: series_resource_id.value,
        serial_numbers: [serial_number.value],
        languages: [language.value],
      }).then(() => {
        ElMessage.success('提交合成任务成功')
        btnActing.value = false
        // router.back()
      }).finally(() => {
        btnActing.value = false
      })
    }).catch(() => {
      btnActing.value = false
    })
  })
}

const getSpeekerValue = () => {
  const result = {
    speaker: '',
    model: ''
  }
  for(const item of totalData.value?.characters || []) {
    if(item.name === form.value.character) {
      result.speaker = item.timbre_title
      result.model = item.timbre_model
      break
    }
  }
  return result
}

const handleRegenerate = () => {
  if(!form.value.order) {
    ElMessage.error('请先选择配音')
    return
  }
  ElMessageBox.confirm('确定要重新生成吗？', '提示', {
    confirmButtonText: '确定',  
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '生成中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const exData = timelineEditor?.value?.exportTimeline()
    const timelineData = exData?.audio
    for(const item of timelineData || []) {
      for(const item2 of voicesList.value) {
        if(item.order === item2.order) {
          item.start_time = Number((item2.start_time / 1000).toFixed(3))
          item.head = item2.head
          item.tail = item2.tail
        }
      }
    }
    console.log('timelineData',timelineData)
    console.log('voicesList.value',voicesList.value)
    const speakerValue = getSpeekerValue()
    apiRecreateVoice({
      series_resource_id: series_resource_id.value,
      serial_number: serial_number.value,
      language: language.value,
      order: form.value.order,
      sentence: form.value.text,
      emotion: form.value.emotion,
      model: speakerValue.model,
      speed: form.value.speed,
      speaker: speakerValue.speaker,
      effect: form.value.effect,
    }).then((res: any) => {
      if(res?.data?.success) {
        let newVoice = {}
        for(const item of voicesList.value || []) {
          if(item.order === res?.data?.order) {
            form.value.voice_path = `${cdnUrl}${res?.data?.oss_path}`
            item.duration = res?.data?.duration
            item.emotion = res?.data?.emotion
            item.encoded_audio = res?.data?.encoded_audio
            item.language_code = res?.data?.language_code
            item.series_resource_id = res?.data?.series_resource_id
            item.speaker = res?.data?.speaker
            item.volume = res?.data?.volume
            item.voice_path = `${cdnUrl}${res?.data?.oss_path}`
            item.tts_text = res?.data?.tts_text
            item.effect = res?.data?.effect
            newVoice = item
          }
        }

        ElMessage.success('重新生成成功')
        timelineEditor.value?.resetClip(newVoice)
        // timelineEditor.value?.handleInitVoicesData()
      }else{
        ElMessage.error(res?.data?.err_msg || '重新生成失败')
      }


    }).finally(() => {
      loading.close()
    })
  })
}

const handleUpload = () => {
  audioInput.value?.click()
}

const handleOriginalVideo = () => {
  console.log('原始视频')
  currentVideoType.value = 1
}

const handleSilentVideo = () => {
  console.log('无声视频')
  currentVideoType.value = 2
}

const playerReadyChange = (e: any) => {
  playerRef.value = e
  playerRef.value.pause()
  console.log(playerRef.value)
  e.on('play', () => {
    console.log('play')
    timelineEditor.value?.togglePlayState(true)
    handleTimeUpdate(e.getCurrentTime() || 0)
    currentTime.value = e.getCurrentTime() || 0
  })

  e.on('pause', () => {
    console.log('pause')
    setTimeout(() => {
      timelineEditor.value?.togglePlayState(false)
    }, 0.2)
  })
  e.on('ended', () => {
    console.log('ended')
    timelineEditor.value?.togglePlayState(false)
  })
  // e.on('timeupdate', () => {
  //   handleTimeUpdate(e.getCurrentTime() || 0)
  //   currentTime.value = e.getCurrentTime() || 0
  // })
}

const handleVoiceSelected = (voice: any) => {
  console.log('voice',voice)
  form.value.time = voice.duration.toFixed(3)
  form.value.character = voice.character
  form.value.emotion = voice.emotion
  form.value.voice = voice.voice
  form.value.text = voice.tts_text
  form.value.voice_path = voice.voice_path
  form.value.order = voice.order
  form.value.start_time = formatSecToTime(voice.start_time)
  form.value.speaker = voice.speaker
  form.value.speed = voice.speed
  form.value.effect = voice.effect || ''
}

const formatSecToTime = (sec: number) => {
  //秒 转 00:00:01,000 格式，位数不足补0
  let hours = String(Math.floor(sec / 3600))
  if(hours.length < 2) {
    hours = '0' + hours
  }

  let minutes = String(Math.floor((sec % 3600) / 60))
  if(minutes.length < 2) {
    minutes = '0' + minutes
  }

  let seconds = String(Math.floor(sec % 60))
  if(seconds.length < 2) {
    seconds = '0' + seconds
  }

  let milliseconds = String(sec % 1 * 1000)
  if(milliseconds.length < 3) {
    milliseconds = milliseconds.padStart(3, '0')
  }
  milliseconds = String(parseInt(milliseconds))
  return `${hours}:${minutes}:${seconds},${milliseconds}`
}

const formatTimeToSec = (time: string) => {
  // 00:00:01,000 转 秒
  const hours = parseInt(time.split(':')[0], 10) || 0
  const minutes = parseInt(time.split(':')[1], 10) || 0
  const seconds = parseInt(time.split(':')[2].split(',')[0], 10) || 0
  const milliseconds = parseInt(time.split(':')[2].split(',')[1], 10) || 0

  return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
}



const getSubtitleContent = (path: string) => {
  return new Promise(resolve => {
    void fetch(`${cdnUrl}${path}`)
      .then(response => response.blob())
      .then(blob => {
        const reader = new FileReader()
        reader.onload = function (event) {
          const content = event.target?.result as string
          if (content.indexOf('<!doctype html>') === 0) {
            resolve('')
          } else {
            resolve(content)
          }
        }
        reader.readAsText(blob)
      })
  })
}

const cdnUrl = 'https://img.tianmai.cn/'

const subtitles = ref<M.VoiceoverEditor.srtItem[]>([])





const path = 'srt_path'
const videoPath = 'ora_video_path'
const currentRow = ref({})
const tempCode = getSubtitleContent("vt/10651/04d015c5-7440-4ce9-915f-5b0404a9bd04.srt")
const code = ref(tempCode)
const currentTime = ref(0)
const btnLoading = ref(false)
// 引用
const videoPlayer = ref<HTMLVideoElement | null>(null)
const timelineEditor = ref<InstanceType<typeof timeLineEditor> | null>(null)
const currentVideo = ref<string>('')
const videoRef = ref<any>(null)
const voicesList = ref<any>([])

const videoUrl = ref('')
// currentVideo.value = videoUrl.value

onMounted(() => {
  series_resource_id.value = Number(route.query.series_resource_id)
  serial_number.value = Number(route.query.serial_number)
  language.value = route.query.language as unknown as string
  getVoiceData()
  start()
})

const handlePrevEpisode = () => {
  if(serial_number.value > 1) {
    serial_number.value--
    getVoiceData()
  }
}

const handleNextEpisode = () => {
  console.log('serial_number.value',serial_number.value)
  serial_number.value++
  getVoiceData()
}
const videoLoading = ref(false)
const getVoiceData = () => {
  videoLoading.value = true
  apiGetVoiceList({
    "series_resource_id": series_resource_id.value,
    "serial_number": serial_number.value,
    "language": language.value
  }).then((res: any) => {
    totalData.value = res?.data
    totalData.value.bg_path = `${cdnUrl}${res?.data?.bg_path}`
    voicesList.value = formatAllVoicesData(res?.data?.voices)
    videoUrl.value = `${cdnUrl}${res?.data?.noaudio_path}`
    console.log('videoUrl.value',videoUrl.value)
    subtitles.value = [{
      language: 'en',
      type: 'srt',
      subtitle: `${cdnUrl}${res?.data?.srt_path}`,
    }]
    console.log('00000',voicesList.value)
    initSrt(res?.data?.srt_path)

    playerRef.value?.pause()
  }).then(() => {
    timelineEditor.value?.handleInitVoicesData()
    setTimeout(() => {
      currentVideo.value = videoUrl.value
      videoLoading.value = false
      if (playerRef.value) {
        // playerRef.value.seek(0)
        if(!playState.value) {
          playerRef.value.pause()
        }
      }
    }, 2000)
  })
}

const formatAllVoicesData = (data: any) => {
  if(!data) return []
  const newData = data.map((item: any) => {
    return {
      ...item,
      type: 'audio',
      voice_path: `${cdnUrl}${item.voice_path}`,
      start_time: item.start_time / 1000,
    }
  })
  return newData
}

const initSrt = async (val: string) => {
  code.value = await getSubtitleContent(val) as string
}


// 处理视频导入
const handleVideoImported = (videoUrl: string) => {
  currentVideo.value = videoUrl
}

// 处理时间轴时间变化
const handleTimeChange = (time: number) => {
  if (playerRef.value) {
    playerRef.value.seek(time)
    if(!playState.value) {
      playerRef.value.pause()
    }
    console.log(playerRef.value)
    console.log(playerRef.value.currentTime)
  }
}

const playState = ref(false)
// 处理播放状态变化
const handlePlayStateChange = (isPlaying: boolean) => {
  if (playerRef.value) {
    if (isPlaying) {
      playerRef.value.play()
      playState.value = true
    } else {
      playerRef.value.pause()
      playState.value = false
    }
  }
}

// 处理视频时间更新
const handleTimeUpdate = (time: number) => {
  if (playerRef.value && timelineEditor.value) {
    // 通知时间轴更新当前时间
    timelineEditor.value.updateCurrentTime(time)
  }
}

// 处理视频播放状态
const handlePlay = () => {
  timelineEditor.value?.setPlayState(true)
}

const handlePause = () => {
  timelineEditor.value?.setPlayState(false)
}

// 在组件卸载时清理资源
onUnmounted(() => {
  if (currentVideo.value) {
    URL.revokeObjectURL(currentVideo.value)
  }
})


const showVideoAndSrt = async () => {
  

  watch(() => [currentRow.value.num], async () => {
    btnLoading.value = true
    try {
      code.value = await getSubtitleContent('vt/10651/04d015c5-7440-4ce9-915f-5b0404a9bd04.srt') as string
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }, {
    immediate: true,
  })

  watch(() => videoRef.value, () => {
    videoRef.value.on('timeupdate', () => {
      currentTime.value = videoRef.value.getCurrentTime() || 0
    })
  })



  openDialog({
    // title: () => <div class="text-lg font-bold">第{ currentRow.value.num }集</div>,
    title: 'aaa',
    customClass: '!w-[900px] !h-[690px]',
    body: () => (
      <div class="flex flex-col">
        <div class="flex flex-row">
          <div>
            { true
              ? (
                  <M3u8Player
                  currentLanguage={'en'}
                      subtitles={subtitles.value}
                    url={videoUrl.value}
                    onPlayerReady={(e: any) => {
                      videoRef.value = e
                    }}
                  />
                )
              : '暂无视频' }
          </div>
          <div class="flex-1">
            <Editor
              customClass="w-full h-full"
              code={code.value as string}
              options={{
                language: 'plaintext',
                formatOnPaste: false,
                tabSize: 2,
                inDiffEditor: false,
                minimap: {
                  enabled: false,
                } }}
              isAsync={true}
              currentTime={currentTime.value}
              onChange={e => {
                code.value = e
              }}
              onVideoProgress={(time: number) => {
                if (videoRef.value) videoRef.value?.seek(time)
              }}
            />
          </div>
        </div>
      </div>
    ),
  })
}

// showVideoAndSrt()
</script>

<style scoped>
.voiceover-editor {
}

.video-player-container {
  flex: 1;
  display: flex;
  justify-content: center;
  gap: 10px;
  background-color: #000;
  height: calc(100vh - 470px);
  /* overflow: hidden; */
}

.video-player {
  height: calc(100vh - 470px);
}

.no-video {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #2c2c2c;
  min-height: 300px;
  max-height: 50vh;
  color: #666;
}
</style>