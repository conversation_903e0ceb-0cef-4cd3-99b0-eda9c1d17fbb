import { ref } from 'vue'
import { WelfareEatDetails } from './meal-check-in.d'
import { apiGetPrivilege, apiGetWelfareEatDetails } from './meal-check-in-api'
export const useMealCheckInStore = () => {
  const eatDetails = ref<WelfareEatDetails>()
  const privilege = ref<number>(0)
  const getEatDetails = async () => {
    const res = await apiGetWelfareEatDetails()
    if (!res.data) return
    eatDetails.value = res.data
  }

  const getPrivilege = async () => {
    const res = await apiGetPrivilege()
    if (!res.data) return
    privilege.value = res.data.popup
  }

  return {
    eatDetails,
    getEatDetails,
    getPrivilege,
    privilege,
  }
}
