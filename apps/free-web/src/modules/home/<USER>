import { createComponent } from '@skynet/shared'
import { commonBg, description, freeReels, logo, series, googlePlay, appStore } from './image/image'
export const HomePage = createComponent(null, () => {
  return () => (
    <div class="size-screen overflow-hidden px-[6.5%] relative pt-[60px] select-none">
      <img src={commonBg} class="size-full absolute top-0 left-0 object-cover" />
      <img src={series} width={756} height={1080} class="w-auto h-screen absolute top-0 right-[7.8125%] object-contain" />
      <div class="relative size-full z-up flex flex-col">
        <div class="w-full flex items-center justify-between">
          <div class="flex items-center w-full gap-x-4">
            <img src={logo} class="h-auto w-[4.491%] object-cover" />
            <div class="flex-1 h-full flex flex-col gap-y-[6px] pt-2 items-start justify-center">
              <img src={freeReels} class="w-[11.976%] h-auto object-cover" />
              <span class="text-white text-xl font-normal">A Free Platform From DramaReels</span>
            </div>
          </div>
        </div>
      </div>
      <div class="flex flex-col justify-center items-start w-[39.43%] h-[38.7%] gap-y-[18%] absolute left-[6.5%] top-[32.6%] z-up">
        <img src={description} width={757} height={264} class="w-full h-auto object-cover" />
        <div class="flex w-full justify-start gap-8">
          <img src={googlePlay} class="w-[34.58%] h-auto object-fill cursor-pointer" onClick={() => window.open('https://play.google.com/store/apps/details?id=com.freereels.app&hl=en&gl=us', '_blank')} />
          <img src={appStore} class="w-[34.58%] h-auto object-fill cursor-pointer" onClick={() => window.open('https://apps.apple.com/us/app/dramareels-pure-short-dramas/id6738081517', '_blank')} />
        </div>
      </div>
      <div class="absolute bottom-[60px] text-white/80 text-base font-normal leading-[27px] text-left z-footer">
        <p class="flex gap-4">
          <a class="underline" href="/terms.html">Terms of Use</a>
          {' '}
          |
          {' '}
          <a class="underline" href="/privacy.html">Privacy Policy</a>
        </p>
        <p>Contact Us：<EMAIL></p>
        <p>© FreeReels, All Rights Reserved SKYWORK AI PTE.LTD.</p>
      </div>
    </div>
  )
})
