import { createComponent, fn } from '@skynet/shared'
import { DramaItem, listingStatusList } from './short-drama-api'
import dayjs from 'dayjs'
import { Button, MergeClass, showAlert, SvgIcon } from '@skynet/ui'
import router from 'src/router'
import { Icon } from '@skynet/ui/icon/icon'
import { Fn, useClipboard } from '@vueuse/core'
import { computed, watch } from 'vue'
import { useShortDramaStore } from './short-drama-store'
import free from './img/free.png'
import releaseFirstImage from './img/release-first.png'

type DramaOptions = {
  props: {
    item: DramaItem
  }
  emits: {
    add: Fn
    remove: Fn
  }
}
export const Drama = createComponent<DramaOptions>({
  props: {
    item: {} as DramaItem,
  },
  emits: {
    add: fn,
    remove: fn,
  },
}, (props, { emit }) => {
  const { copy, copied } = useClipboard()
  const { selectedDramas, addDramas, removeDramas, getListingStatusDesc } = useShortDramaStore()

  const toggleDrama = () => {
    if (selectedDramas.value.find(item => item.drama_id === props.item.drama_id)) {
      removeDramas([props.item])
    } else {
      addDramas([props.item])
    }
  }

  const checked = computed(() => !!selectedDramas.value.find(item => item.drama_id === props.item.drama_id))

  watch(() => checked.value, () => {
    if (checked.value) {
      emit('add')
    } else {
      emit('remove')
    }
  })

  watch(() => copied.value, () => {
    if (copied.value) {
      showAlert('复制成功')
    }
  })

  return () => (
    <MergeClass tag="x-drama" baseClass="w-[400px] flex justify-between flex-col p-4">
      <div class="grid grid-cols-[37.5%_auto] gap-x-3 w-full">
        <div class="flex items-start gap-2 w-full cursor-pointer relative" onClick={toggleDrama}>
          {props.item.free || props.item.release_round === 1 || props.item.audio_type === 1 || props.item.audio_type === 2
            ? (
                <div class="absolute left-1 top-1 flex space-x-1">
                  {props.item.release_round === 1 ? <img src={releaseFirstImage} class="h-[17px] w-auto object-contain shadow-sm" /> : null}
                  {props.item.free ? <img src={free} class="w-40px h-auto object-contain shadow-sm" /> : null}
                  { props.item.audio_type === 1 ? <SvgIcon name="ic_voiceover" class="h-[17.4px]" /> : props.item.audio_type === 2 ? <SvgIcon name="ic_ai_voiceover" class="h-[17.4px]" /> : null }
                </div>
              )
            : null}
          <img src={props.item.cover_url} class="shrink-0 h-auto" />
          <input class="m-1 checkbox checkbox-primary checkbox-sm absolute top-0 right-0 bg-white" type="checkbox" checked={checked.value} />
        </div>
        <div class="text-sm text-gray-500 flex flex-col gap-2 truncate">
          <div class="text-lg font-bold text-gray-900 truncate" title={props.item.title}>{props.item.title}</div>
          <div class="flex items-center">
            短剧ID：
            <span class="truncate">{props.item.series_key}</span>
            {props.item.series_key && <Icon class="shrink-0 w-4 h-4 cursor-pointer ml-1" name="material-symbols:content-copy-outline" onClick={() => copy(props.item.series_key)} />}
          </div>
          <div class="truncate" title={props.item.labels}>标签：{props.item.labels}</div>
          <div class="truncate" title={props.item.episodes_number + ''}>集数：{props.item.episodes_number}</div>
          <div class="truncate">上架状态：{ listingStatusList.find(item => item.code === props.item.listing_status)?.name }</div>
          <div class="truncate">单集定价：{ props.item.episodes_price }</div>
          <div class="flex items-center" title={props.item.description}>
            简介：
            <span class="truncate">{props.item.description}</span>
            {props.item.description && <Icon class="shrink-0 w-4 h-4 cursor-pointer ml-1" name="material-symbols:content-copy-outline" onClick={() => copy(props.item.description)} />}
          </div>
          <div class="truncate">{getListingStatusDesc(props.item.listing_status)}{+props.item.listing_time > 0 ? `: ${dayjs(new Date(props.item.listing_time * 1000)).format('YYYY-MM-DD HH:mm:ss')}` : ''}</div>
        </div>
      </div>
      <div class="w-full flex justify-start mt-4 gap-4 hide-when-in-dialog">
        <Button class="btn btn-default btn-sm" onClick={() => router.push('/short-drama/' + props.item.drama_id + '?type=listing')}>修改上架状态</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => router.push('/short-drama/' + props.item.drama_id + '?type=price')}>修改定价</Button>
      </div>
    </MergeClass>
  )
})
