import { createComponent } from '@skynet/shared'
import { RouterView } from 'vue-router'
import { useToastStore } from './modules/common/toast/use-toast-store'
import { useDialogStore } from './modules/common/dialog'
import Loading from './modules/common/loading/loading'
import { useLoadingStore } from './modules/common/loading/use-loading-store'

export const App = createComponent(null, () => {
  const { renderToasts } = useToastStore()
  const { renderDialogs } = useDialogStore()
  const { pageLoading } = useLoadingStore()
  return () => (
    <>
      <RouterView />
      <div class="size-full" id="mountRoot">
        { renderDialogs() }
        {
          renderToasts().length > 0 && (
            <x-toast-root class="fixed w-full flex gap-y-4 justify-center flex-col items-center left-1/2 z-toast top-[calc(max(env(safe-area-inset-top),0px)_+_123px)] transform -translate-x-1/2">
              {renderToasts()}
            </x-toast-root>
          )
        }
      </div>
      {
        pageLoading.value && (
          <div class="size-screen fixed z-global-loading top-0 left-0 bg-[var(--surface-1)]">
            <Loading />
          </div>
        )
      }
    </>
  )
})
