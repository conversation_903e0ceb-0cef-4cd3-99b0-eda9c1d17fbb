import { createComponent } from '@skynet/shared'
import { useSearch } from './use-search'
import { RouterLink } from 'vue-router'
import searchHotIcon from 'src/h5_modules/images/search-hot-icon.webp'
import coverDefBg from 'src/h5_modules/images/cover-def.webp'
import Rank from './rank'
type DramaItemOptions = {
  props: {
    bigMode?: boolean
    dramato: M.Dramato
    rank?: number
    from?: string
  }
}
export const DramaItem = createComponent<DramaItemOptions>({
  props: {
    bigMode: false,
    from: '',
    dramato: {
      id: '', pay_mode: '', name: '', desc: '', cover: '', view_count: 0, follow_count: 0, episode_count: 0, finish_status: 0, following: false, labels: '', content_tags: [],
      custom_tag: [], series_tag: [], view_episode: 0, view_episode_ms: 0, episode_price: 0, lang: '', episode_list: [], view_time: 0, follow_time: 0, free: false,
      highlight: { name: '' },
      r_info: { // !!! 免费版本此字段类型为 string,客户端仅做透传即可
        session_id: '', request_id: '', page_num: '', page_size: '', item_idx: '', hot_score: '',
      },
      r_info1: '',
    },
    rank: 0,
  },
}, props => {
  const { withHighlight } = useSearch()
  const handleImageError = event => {
    event.target.src = coverDefBg
  }

  const goToPlay = () => {
    window.location.href = window.location.origin + `/series/${props.dramato.id}${props.from ? `?from_page=${props.from}` : ''}`
  }

  return () => (
    <x-drama-item class="relative block shrink-0">
      <div class={`flex ${props.bigMode ? 'h-41 gap-x-3' : 'h-28 gap-x-4'}`} onClick={goToPlay}>
        {props.bigMode && <Rank rank={props.rank} />}
        <img width={props.bigMode ? 124 : 84} height={props.bigMode ? 164 : 112} loading="lazy" class="h-full rounded-lg object-cover" error={handleImageError} src={props.dramato.cover} />
        <div class={`flex-1 ${props.bigMode ? 'pt-3' : 'pt-2'}`}>
          <div class="flex items-start justify-between">
            <div class="flex-1 text-sm leading-4 text-[#fdfbfc]" v-html={withHighlight(props.dramato?.highlight?.title) ?? props.dramato.name} />
            {
              props.bigMode && props.dramato.view_count > 0 && (
                <div class="leading-3.5 flex items-center gap-x-1 text-xs text-[#fc2763]">
                  <img class="mb-1 size-4" src={searchHotIcon} />
                  <span>{props.dramato.view_count}</span>
                </div>
              )
            }
          </div>
          <div class={`leading-3.5 mt-${props.bigMode ? '2' : '1'} mb-${props.bigMode ? '5' : '3'} line-clamp-${props.bigMode ? 3 : 2} text-xs text-[#797b7d]`}>
            {props.dramato.desc}
          </div>
          <div class="flex flex-wrap gap-1">
            {props.dramato.content_tags?.map(tag => (
              <div key={tag} class="leading-3.5 inline-block rounded bg-[#1d1d1e] px-2 py-0.5 text-xs text-[#797b7d]">
                {withHighlight(tag)}
              </div>
            ))}
          </div>
        </div>
      </div>
    </x-drama-item>
  )
})
