import { createComponent } from '@skynet/shared'
import { Input } from '@skynet/ui'
import { ref } from 'vue'

export const InputExamplesPrefix = createComponent(null, () => {
  const name = ref<string>('')
  return () => (
    <div class="space-y-2 p-2 markdown-body">
      <Input
        placeholder="请输入姓名"
        class="w-80 h-10 inline-block"
        inputClass="p-[0_30px_0_40px]"
        v-model={name.value}
        v-slots={{
          prefix: () => <span>姓名</span>,
          suffix: () => <span>人</span>,
        }}
      />
    </div>
  )
})

export default InputExamplesPrefix
