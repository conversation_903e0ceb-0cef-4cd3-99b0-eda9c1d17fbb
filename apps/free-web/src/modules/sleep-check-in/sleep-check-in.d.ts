export type WelfareSleepDetails = {
  welfare_id: number
  welfare_key: string
  task_total_gold: number
  status: number
  background_url: string
  time_period: number
  button_show: {
    btn_txt: string
  }
  top_show: {
    title: string
  }
  greeting_show: {
    title: string
    sub_title: string
    bottom_tips: string
  }
  bottom_show: {
    title: string
  }
  jump_widget: Widget
  interactive_broadcast_widget: Widget
  activity_rules_widget: Widget
}
interface Widget {
  widget_icon: string
  widget_link: string
  widget_text: string
}

export type WelfareSleepActivateRes = {
  data: {
    toast: {
      icon: string
      text: string
    }
  }
}
