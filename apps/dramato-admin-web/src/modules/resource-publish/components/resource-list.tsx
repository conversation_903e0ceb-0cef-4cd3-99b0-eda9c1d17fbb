/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { onMounted, ref, Ref, watch, computed, useId } from 'vue'
import { CreateTableOld, TableColumnOld, Button, openDialog, Checkbox, showFailToast, showSuccessToast, Icon, Tooltip, SvgIcon } from '@skynet/ui'
import Editor from 'src/modules/resource/components/editor'
import { useCheck } from './use-check'
import { apiSubtitleDel, apiSubtitleExtractionV2, apiUploadSaveSubtitle, apiSubtitleTrans } from 'src/modules/resource/resource-api'
import { apiGetSubtitleErrors, apiGetSubtitleErrorsDetail, apiGetSubtitleFixes, apiExtractTerminology, apiUpdateOriginalTerminology, apiGetTerminologyList, apiUpdateMark, apiDelSingleResource } from 'src/modules/resource-publish/resource-publish-api'

import { langKey, langValue, defaultClipValue } from 'src/modules/resource/constant'
import { Clip } from 'src/modules/resource/components/clip'
import { Clip1 } from 'src/modules/resource/components/clip1'

import { useRoute, useRouter, RouterLink } from 'vue-router'
import { getUuid } from 'src/modules/resource/util'
import { useUploader } from 'src/modules/common/uploader/use-uploader'
import axios from 'axios'
import { formatSubtitles } from 'src/modules/resource/util'
import { M3u8Player } from 'src/modules/resource/components/m3u8-player'
import { TermTable } from './term-table'
import { cloneDeep, trim } from 'lodash-es'

type ResourceListOptions = {
  props: {
    list?: M.IResourceAssetInfo[]
    canEdit?: boolean
    language?: string[]
    subtitleLogs?: M.IAlterSubtitle[]
    isExtractTerms?: boolean
  }
  emits: {
    refreshList: () => void
  }
}

export const ResourceList = createComponent<ResourceListOptions>({
  props: {
    list: [],
    canEdit: false,
    language: [],
    subtitleLogs: [],
    isExtractTerms: false,
  },
  emits: {
    refreshList: () => {},
  },
}, (props, { emit }) => {
  const PROCESSING = 'processing'
  const PURE_ERROR = 'failed'
  const EXTRACT_ERROR = 'extract_failed'
  const HISTORY_EXTRACT_ERROR = 'failed'
  const TRANS_ERROR = 'trans_failed'
  const tooltipRef = ref<any>(null)

  const { getResourceOssData, ossDataBj } = useUploader()
  const route = useRoute()
  const router = useRouter()
  const loading = ref<boolean>(false)
  const btnLoading = ref(false)
  const Table = CreateTableOld<M.IResourceAssetInfo>()
  const { checked, isCheckAll, checkedAllChange, checkboxChange, resetChecked } = useCheck()
  const subtitleErrorList = ref<M.IResourceSubtitleError[]>([])
  const subtitleFixesList = ref<M.IResourceSubtitleFix[]>([])
  const subtitleFixesLoading = ref(false)
  const termList = ref<M.ITerminology[]>([])

  const getSubtitleErrors = async () => {
    const res = await apiGetSubtitleErrors({
      series_resource_id: +route.params.id,
    })
    subtitleErrorList.value = (res.data?.list || []).filter((item: M.IResourceSubtitleError) => item.language_code === props.language[0] || item.language_code === 'en') || []
  }

  const getSubtitleFixes = async (n: number, code: string) => {
    subtitleFixesLoading.value = true
    try {
      const res = await apiGetSubtitleFixes({
        series_resource_id: +route.params.id,
        serial_number: n,
        language_code: props.language[0],
        srt_content: code,
      })
      subtitleFixesList.value = res.data?.errs || []
    } catch (error) {
      showFailToast('获取字幕优化建议报错')
    } finally {
      subtitleFixesLoading.value = false
    }
  }

  const getSubtitleContent = (path: string) => {
    return new Promise(resolve => {
      void fetch(path)
        .then(response => response.blob())
        .then(blob => {
          const reader = new FileReader()
          reader.onload = function (event) {
            const content = event.target?.result as string
            if (content.indexOf('<!doctype html>') === 0) {
              resolve('')
            } else {
              resolve(content)
            }
          }
          reader.readAsText(blob)
        })
    })
  }

  const getTermList = async () => {
    const termRes = await apiGetTerminologyList({
      series_resource_id: +route.params.id,
    })
    termList.value = termRes.data?.term_list || []
  }
  // lang 不存在，则不是字幕预览
  const preview = (row: M.IResourceAssetInfo, videoPath: 'origin_path' | 'pure_path' | 'pure_origin_path', lang?: string, path?: 'subtitle_path' | 'en_subtitle_path', errorList?: M.IResourceSubtitleError[]) => {
    termList.value = []
    const currentTime = ref(0)
    // 视频类型
    const type = ref(videoPath)
    // 当前集
    const curRow = ref<M.IResourceAssetInfo>(row)
    // 编辑器滚动条位置
    const top = ref(0)
    // 字幕内容
    const code = ref('')
    const remark = ref('')
    const textarea = ref('')
    const videoRef = ref<any>(null)
    // 是否开启字母同步滚动
    const isAsync = ref(true)
    // 视频地址
    const url = ref(row[type.value])
    const subtitles = ref<{
      language: string
      type: string
      subtitle: string
    }[]>([])

    void getTermList()
    const errDetail = ref<M.IResourceSubtitleDetailError[]>([])

    watch(() => videoRef.value, () => {
      videoRef.value.on('timeupdate', () => {
        currentTime.value = videoRef.value.getCurrentTime() || 0
      })
    })

    const options = [{
      label: '成片',
      value: 'origin_path',
    }, {
      label: '无字幕视频',
      value: 'pure_path',
    }, {
      label: '无字幕原视频',
      value: 'pure_origin_path',
    }]

    watch(() => [curRow.value.serial_number], async () => {
      subtitleFixesList.value = []
      // 单语言字幕
      if (lang && path) {
        code.value = await getSubtitleContent(curRow.value[path]!) as string
        subtitles.value = [{
          language: lang,
          type: 'srt',
          subtitle: curRow.value[path]!,
        }]
        remark.value = curRow.value.remark || ''
        textarea.value = curRow.value.remark || ''
      }
      const isErrorSubtitle = errorList?.find(item => item.language_code === lang && item.serial_number === curRow.value.serial_number)
      if (isErrorSubtitle && lang) {
        const res = await apiGetSubtitleErrorsDetail({
          series_resource_id: +route.params.id,
          serial_number: row.serial_number,
          language_code: lang,
        })
        errDetail.value = res.data?.errs || []
      } else {
        errDetail.value = []
      }

      if (props.language[0] === lang) {
        void getSubtitleFixes(curRow.value.serial_number, code.value)
      } else {
        subtitleFixesList.value = []
      }
    }, {
      immediate: true,
    })

    const onCaptureImage = () => {
      const video = videoRef.value._el.querySelector('video')
      const canvas = document.createElement('canvas')
      video.crossOrigin = 'anonymous' // 允许跨域访问
      canvas.width = video.videoWidth * 3
      canvas.height = video.videoHeight * 3
      const ctx = canvas.getContext('2d')
      ctx?.drawImage(video, 0, 0, canvas.width, canvas.height)
      // 导出为 base64 图片
      const imageData = canvas.toDataURL('image/jpeg', 1.0)
      console.log(imageData, 'imageData')
      const link = document.createElement('a')
      link.setAttribute('href', imageData)
      link.setAttribute('download', row.serial_number + '_' + Date.now())
      document.body.appendChild(link)
      link.click()
    }

    const onSaveSubtitle = async (row: M.IResourceAssetInfo, rawCode: string, lang: string) => {
      const formatCode = formatSubtitles(rawCode)
      btnLoading.value = true
      const fileName = `${getUuid()}.srt`
      if (!ossDataBj.value) await getResourceOssData()
      const data = ossDataBj.value
      const blob = new Blob([formatCode], { type: 'application/x-subrip' })
      const file = new File([blob], fileName, { type: 'application/x-subrip' })
      const f = new File([file], file.name, { type: file.name.endsWith('.srt') ? 'application/x-subrip' : file.type })
      const formData = new FormData()
      formData.append('key', data?.dir + '/' + f.name)
      formData.append('policy', data?.policy || '')
      formData.append('OSSAccessKeyId', data?.accessid || '')
      formData.append('success_action_status', '200')
      formData.append('signature', data?.signature || '')
      formData.append('file', f)

      const uploadRequest = async () => {
        return new Promise((resolve, reject) => {
          void axios.post(data?.host || '', formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }).then(() => {
            resolve(null)
          }).catch((error: any) => {
            if (error.response.status === 403) {
              void getResourceOssData().then(uploadRequest).then(resolve)
              return
            }
          })
        })
      }
      await uploadRequest()
      try {
        const tempPath = data?.dir + '/' + fileName || ''
        const subtitleInfo = {
          file_path: tempPath,
          serial_number: row.serial_number,
        }
        await apiUploadSaveSubtitle({
          data: [subtitleInfo],
          series_resource_id: +route.params.id,
          language_code: lang,
          alter_type: 1,
        })
        code.value = formatCode
        subtitles.value = [{
          language: lang,
          type: 'srt',
          subtitle: `https://img.tianmai.cn/${tempPath}`,
        }]
        emit('refreshList')
        void getSubtitleFixes(curRow.value.serial_number, code.value)
        void getSubtitleErrors()
        showSuccessToast('保存成功')
      } catch (error: any) {
        showFailToast(error.response.data.message || '操作失败')
      } finally {
        btnLoading.value = false
      }
    }

    const closeDialog = openDialog({
      title: () => lang
        ? (
            <div class="flex items-center space-x-2">
              <div>第{curRow.value.serial_number}集 {langValue[langKey.findIndex(s => s === lang)]}</div>
              {remark.value ? (
                <Tooltip placement="bottom-start" popWrapperClass="z-popover-in-dialog max-w-[400px]" popContent={() => (
                  <pre class="whitespace-pre-wrap break-words">备注：{remark.value}</pre>
                )}>
                  <div class="line-clamp-1 w-[400px] flex-1 text-sm text-[var(--text-2)]">
                    备注：{remark.value}
                  </div>
                </Tooltip>
              ) : null}
            </div>
          )
        : `第${curRow.value.serial_number}集`,
      mainClass: 'px-4 !py-0',
      customClass: lang ? '!w-[900px]' : '!w-[400px]',
      body: () => (
        //
        <x-video-subtile-container class="flex flex-col">
          <div class="flex flex-row">
            <x-video-area>
              { url.value
                ? (
                    <M3u8Player
                      currentLanguage={lang}
                      subtitles={subtitles.value}
                      url={url.value}
                      onPlayerReady={(e: any) => {
                        videoRef.value = e
                      }}
                    />
                  )
                : '暂无视频' }
            </x-video-area>
            {lang
              ? (
                  <x-subtitle-area class="flex-1">
                    <Editor
                      customClass="w-full h-[550px]"
                      code={code.value}
                      errs={errDetail.value}
                      warns={subtitleFixesList.value}
                      highlightTerms={termList.value.map(obj => obj.term || '')}
                      options={{
                        language: 'plaintext',
                        formatOnPaste: false,
                        tabSize: 2,
                        inDiffEditor: false,
                        minimap: {
                          enabled: false,
                        } }}
                      isAsync={isAsync.value}
                      currentTime={currentTime.value}
                      onChange={e => {
                        code.value = e
                      }}
                      scrollTop={top.value}
                      onScroll={scrollTop => {
                        top.value = scrollTop
                      }}
                      onVideoProgress={(time: number) => {
                        if (videoRef.value) videoRef.value?.seek(time)
                      }}
                    />
                  </x-subtitle-area>
                )
              : null }
            {lang === props.language[0] ? (
              <div class="flex flex-col w-[220px]">
                <TermTable
                  class="h-[500px] overflow-y-auto"
                  list={termList.value}
                  onChange={({ item }) => {
                    const index = termList.value.findIndex(obj => obj.term_id === item.term_id)
                    termList.value.splice(index, 1, {
                      ...item,
                    })
                  }}
                  onDelete={(id: number) => {
                    const index = termList.value.findIndex(obj => obj.term_id === id)
                    termList.value.splice(index, 1)
                    console.log(termList.value, 'termList.value')
                  }}
                />
                {!props.isExtractTerms ? (
                  <div class="mt-4 flex justify-end space-x-2">
                    <Button class="btn btn-primary btn-xs" disabled={btnLoading.value} onClick={() => {
                      const term_id = Math.floor(Math.random() * 1000000000000)
                      termList.value.push({
                        term_id,
                        isNew: true,
                      })
                    }}>
                      新增术语
                    </Button>
                    <Button class="btn btn-primary btn-xs" disabled={btnLoading.value} onClick={async () => {
                      btnLoading.value = true
                      try {
                        const newTerms = cloneDeep(termList.value).filter(item => item.isNew).map(item => {
                          if (item.category === 'character') {
                            item.type = 1
                          } else {
                            item.type = 2
                          }
                          delete item.isNew
                          delete item.term_id
                          delete item.delete
                          return item
                        })
                        if (termList.value.some(row => !trim(row.category) || !trim(row.term))) {
                          showFailToast('请完成术语填写或删掉不需要的术语')
                          return
                        }
                        await apiUpdateOriginalTerminology({
                          series_resource_id: +route.params.id,
                          terms: [...termList.value.filter(item => (item.delete === 0 || item.delete === 1) && !item.isNew), ...newTerms].map(item => {
                            item.src_type = 0
                            item.official_name = trim(item.official_name)
                            item.term = trim(item.term)
                            return item
                          }),
                        })
                        showSuccessToast('保存成功')
                        closeDialog()
                        delayTaskGetList()
                      } catch (error: any) {
                        showFailToast(error.response.data.err_msg || error.response.data.message || '保存失败')
                      } finally {
                        btnLoading.value = false
                      }
                    }}>
                      {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                      保存术语
                    </Button>
                  </div>
                ) : null}

              </div>
            ) : null}
          </div>
          <div class="mt-4 flex w-full flex-row items-center justify-between space-x-4">
            <div class="flex items-center">
              <div class="flex items-center">
                <Button class="btn btn-outline btn-xs" disabled={curRow.value.serial_number === props.list[0].serial_number || btnLoading.value || subtitleFixesLoading.value} onClick={async () => {
                  btnLoading.value = true
                  const curIndex = props.list.findIndex(item => item.serial_number === curRow.value.serial_number)
                  const item = props.list[curIndex - 1]
                  curRow.value = item
                  if (path) code.value = await getSubtitleContent(curRow.value[path]!) as string
                  url.value = curRow.value[type.value]
                  btnLoading.value = false
                  if (lang && path) {
                    subtitles.value = [{
                      language: lang,
                      type: 'srt',
                      subtitle: curRow.value[path]!,
                    }]
                  }
                }}
                >上一集
                </Button>
                <div class="px-4">{ curRow.value.serial_number }</div>
                <Button class="btn btn-outline btn-xs" disabled={props.list[props.list.length - 1].serial_number === curRow.value.serial_number || btnLoading.value || subtitleFixesLoading.value} onClick={async () => {
                  btnLoading.value = true
                  const curIndex = props.list.findIndex(item => item.serial_number === curRow.value.serial_number)
                  const item = props.list[curIndex + 1]
                  curRow.value = item
                  if (path) code.value = await getSubtitleContent(curRow.value[path]!) as string
                  url.value = curRow.value[type.value]
                  btnLoading.value = false
                  if (lang && path) {
                    subtitles.value = [{
                      language: lang,
                      type: 'srt',
                      subtitle: curRow.value[path]!,
                    }]
                  }
                }}
                >下一集
                </Button>
              </div>
              <select class="select select-bordered select-sm ml-4" value={type.value} onChange={(e: Event) => {
                const target = e.target as HTMLSelectElement
                const value = target.value as 'origin_path' | 'pure_path' | 'pure_origin_path'
                type.value = value
                url.value = curRow.value[type.value]
              }}
              >
                {
                  options.map(n => <option value={n.value}>{n.label}</option>)
                }
              </select>
              { props.language[0] === lang ? (
                <Tooltip
                  ref={tooltipRef}
                  triggerType="click"
                  popWrapperClass="z-popover-in-dialog w-[460px] p-4"
                  popContent={() => (
                    <div class="flex flex-col gap-y-4">
                      <textarea value={textarea.value} class="textarea textarea-bordered textarea-sm h-[200px] w-full" placeholder="请输入备注" onChange={e => {
                        textarea.value = (e.target as HTMLTextAreaElement).value
                      }} />
                      <div class="flex justify-end gap-x-2">
                        <Button class="btn btn-ghost btn-sm" onClick={() => {
                          tooltipRef.value.toggle()
                        }}>取消</Button>
                        <Button class="btn btn-primary btn-sm " disabled={btnLoading.value} onClick={async () => {
                          btnLoading.value = true
                          try {
                            await apiUpdateMark({
                              series_resource_id: +route.params.id,
                              serial_number: curRow.value.serial_number,
                              content: textarea.value || '',
                            })
                            remark.value = textarea.value
                            showSuccessToast('操作成功')
                            tooltipRef.value.toggle()
                            emit('refreshList')
                          } catch (error: any) {
                            showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                          } finally {
                            btnLoading.value = false
                          }
                        }}>
                          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                          保存
                        </Button>
                      </div>

                    </div>
                  )}>
                  <Button class="btn btn-link btn-xs">备注</Button>
                </Tooltip>
              )
                : null }
              {url.value ? <Button class="btn btn-link btn-xs" onClick={onCaptureImage}>截图</Button> : null}
              {url.value
                ? (
                    <Tooltip popWrapperClass="z-popover-in-dialog" popContent={() => (
                      <div class="flex flex-col gap-y-2">
                        <Button class="btn btn-outline btn-xs" onClick={() => {
                          checked.value = [curRow.value.serial_number]
                          // closeDialog()
                          onCleanSubtitle()
                        }}>字幕抹除</Button>
                        <Button class="btn btn-outline btn-xs" onClick={() => {
                          checked.value = [curRow.value.serial_number]
                          // closeDialog()
                          onExtractSubtitle()
                        }}>字幕提取</Button>
                      </div>
                    )}>
                      <Button class="btn btn-link btn-xs">操作</Button>
                    </Tooltip>
                  )
                : null}
            </div>
            {lang
              ? (
                  <Button class="btn btn-primary btn-sm" disabled={btnLoading.value || props.isExtractTerms} onClick={() => onSaveSubtitle(curRow.value, code.value, lang)}>
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    保存字幕
                  </Button>
                )
              : null }
          </div>
        </x-video-subtile-container>
      ),
    })
  }

  const renderPreview = (row: M.IResourceAssetInfo, path: 'origin_path' | 'pure_path' | 'subtitle_path' | 'en_subtitle_path' | 'pure_origin_path') => {
    let subtitleBtn = null
    const mediaPath = row[path]
    if (path === 'subtitle_path' || path === 'en_subtitle_path') {
      const lang = path === 'en_subtitle_path' ? 'en' : props.language[0]
      const subtitleLog = props.subtitleLogs.find(item => item.serial_number === row.serial_number && item.language_code === lang)
      if (!mediaPath) {
        subtitleBtn = '-'
      } else if (mediaPath === PROCESSING) {
        subtitleBtn = <Button class="btn btn-outline btn-accent btn-xs">处理中</Button>
      } else if (mediaPath === EXTRACT_ERROR || mediaPath === HISTORY_EXTRACT_ERROR) {
        subtitleBtn = <div class="text-red-500">提取错误</div>
      } else if (mediaPath === TRANS_ERROR) {
        subtitleBtn = <div class="text-red-500">翻译报错</div>
      } else {
        const isErrorSubtitle = subtitleErrorList.value.find(item => item.language_code === lang && item.serial_number === row.serial_number)

        subtitleBtn = (
          <div class="group hover relative flex items-center justify-center space-x-0">
            {isErrorSubtitle
              ? (
                  <Button class="btn btn-link btn-active btn-xs text-red-500" onClick={() => {
                    const id = route.params.id as string
                    void router.push(`/resource-publish/inspection-role-detail/${id}?number=${row.serial_number}`)
                  }}>
                    字幕{subtitleLog?.operate_user ? '-改' : ''}
                  </Button>
                )
              : (
                  <Button class="btn btn-link btn-active btn-xs" onClick={() => {
                    const id = route.params.id as string
                    void router.push(`/resource-publish/inspection-role-detail/${id}?number=${row.serial_number}`)
                  }}>
                    字幕{subtitleLog?.operate_user ? '-改' : ''}
                  </Button>
                )}
          </div>
        )
      }
      return subtitleBtn
    } else if (path === 'pure_path') {
      return mediaPath === PURE_ERROR
        ? <Button class="btn btn-outline btn-error btn-xs">抹除失败</Button>
        : mediaPath
          ? mediaPath === 'processing'
            ? <Button class="btn btn-outline btn-accent btn-xs">处理中</Button>
            : (
                <Button class="btn btn-link btn-active btn-xs" onClick={() => { void preview(row, path) }}>
                  查看{row.pure_compress === 1 ? '(已压缩)' : '' }
                </Button>
              )
          : '-'
    } else if (path === 'origin_path') {
      return mediaPath
        ? (
            <Button class="btn btn-link btn-active btn-xs" onClick={() => { void preview(row, path) }}>
              查看{row.origin_compress === 1 ? '(已压缩)' : '' }
            </Button>
          )
        : '-'
    } else if (path === 'pure_origin_path') {
      return mediaPath
        ? (
            <Button class="btn btn-link btn-active btn-xs" onClick={() => { void preview(row, path) }}>
              查看
            </Button>
          )
        : '-'
    }
  }

  const columns: Ref<TableColumnOld<M.IResourceAssetInfo>[]> = computed(() => {
    const c: TableColumnOld<M.IResourceAssetInfo>[] = [
      [
        () => (
          <Checkbox
            label=""
            disabled={props.list?.length === 0}
            modelValue={isCheckAll.value}
            onUpdate:modelValue={(value: boolean) => {
              checkedAllChange(value, props.list)
            }}
          />
        ),
        (row: M.IResourceAssetInfo) => {
          return (
            <Checkbox
              label=""
              modelValue={checked.value.includes(row.serial_number)}
              onUpdate:modelValue={(value: boolean) => {
                const serial_number = row.serial_number
                checkboxChange(value, serial_number, props.list)
              }}
            />
          )
        },
        { class: 'w-[60px]' },
      ],
      ['集数', 'serial_number', { class: 'w-[60px]' }],
      ['成片', row => renderPreview(row, 'origin_path'), { class: 'w-[60px] text-center' }],
      ['无字幕视频', row => renderPreview(row, 'pure_path'), { class: 'w-[120px] text-center' }],
      ['无字幕原视频', row => renderPreview(row, 'pure_origin_path'), { class: 'w-[120px] text-center' }],
      ['字幕', row => {
        return renderPreview(row, 'subtitle_path')
      }, { class: 'w-[100px] text-center' }],
      ['标注进度', row => {
        return <div>{row.annotations}/{row.sentences}</div>
      }],
    ]
    // if (props.language[0] && props.language[0] !== 'en') {
    //   c.push([
    //     '英语字幕',
    //     row => renderPreview(row, 'en_subtitle_path'), { class: 'w-[100px] text-center' }])
    // }
    c.push([
      '操作',
      row => (
        <div>
          <Button class="btn btn-link btn-xs" onClick={() => {
            const btnLoading = ref(false)
            const hideDeleteDialog = openDialog({
              title: '删除',
              mainClass: 'pb-0 px-5',
              body: (
                <x-resource-del-confirm-dialog class="flex flex-col gap-y-[25px]">
                  <x-resource-del-body>确认删除【{row.serial_number}集】吗？</x-resource-del-body>
                  <x-resource-del-footer class="flex w-full justify-end gap-x-[10px]">
                    <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                    <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
                      btnLoading.value = true
                      void apiDelSingleResource({
                        series_resource_id: +route.params.id,
                        serial_number: row.serial_number,
                      }).then(() => {
                        showSuccessToast('删除成功')
                        emit('refreshList')
                        hideDeleteDialog()
                      }).catch((error: any) => {
                        showFailToast(error.response.data.message || error.response.data.err_msg || '操作失败')
                      }).finally(() => {
                        btnLoading.value = false
                      })
                    }}
                    >
                      {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                      确定
                    </Button>
                  </x-resource-del-footer>
                </x-resource-del-confirm-dialog>
              ),
            })
          }}>删除</Button>
        </div>
      ),
      { class: 'w-[60px] text-center' },
    ])
    return c
  })

  const delayTaskGetList = () => {
    setTimeout(() => {
      emit('refreshList')
    }, 2000)
  }

  const onExtractSubtitle = () => {
    const language_code = ref(langKey[0])
    const type = ref<1 | 2 | number>(2)
    const position = ref<[number, number, number, number]>([0, 0, 0, 0])
    position.value = defaultClipValue
    const rows = props.list.filter(row => checked.value.includes(row.serial_number))
    const playingEpisodePath = ref<string>()
    if (rows.length > 0) playingEpisodePath.value = rows[0].origin_path
    const episodePlayBtns = rows.filter(item => item.origin_path && item.origin_path.indexOf('http') === 0).map(row => (
      <Button class="btn-default btn btn-xs" onClick={() => {
        playingEpisodePath.value = row.origin_path
      }}
      >{row.serial_number}
      </Button>
    ))
    const closeDialog = openDialog({
      title: '字幕抽取',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div class="flex space-x-4">
            {/* <div class="flex items-center pb-4 gap-x-2">
              字幕语言:
              <select class="select select-bordered select-sm" value={language_code.value} onChange={e => {
                const target = e.target as HTMLSelectElement
                const value = target.value
                language_code.value = value
              }}
              >
                {
                  langKey.map((n, index) => <option value={n}>{langValue[index]}</option>)
                }
              </select>
            </div> */}
            <div class="flex items-center gap-x-2 pb-4">
              类型:
              <select class="select select-bordered select-sm" value={type.value} onChange={e => {
                const target = e.target as HTMLSelectElement
                const value = target.value
                type.value = +value as unknown as 1 | 2
              }}
              >
                <option value={2}>自研</option>
                {/* <option value={1}>阿里云</option> */}
              </select>
            </div>
          </div>
          <Clip title="提取坐标" defaultValue={defaultClipValue} path={playingEpisodePath.value as string} onChange={(p: [number, number, number, number]) => {
            position.value = p
          }}
          />
          <div class="space-x-2 space-y-2 pt-4">
            {episodePlayBtns}
          </div>
          <div class="flex items-center justify-end gap-x-2 py-2">
            <Button class="btn-default btn btn-sm" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              if (type.value === 2 && !['zh-CN', 'en', 'ja'].includes(language_code.value)) {
                showFailToast('自研提取暂只支持中、英、日语')
                return
              }
              btnLoading.value = true
              try {
                await apiSubtitleExtractionV2({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(row => row.serial_number),
                  region: position.value,
                  type: type.value,
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  const isValidVideoUrl = (url: string) => {
    return url && url.indexOf('http') === 0
  }

  const onCleanSubtitle = () => {
    const job_way = ref(2)
    const reuse_detext = ref<0 | 1>(1)
    const position = ref<[number, number, number, number][]>([[0, 0, 0, 0]])
    position.value = [defaultClipValue]
    const rows = props.list.filter(row => checked.value.includes(row.serial_number))
    const currentRow = ref<M.IResourceAssetInfo>(rows[0])

    const playingEpisodePath = computed(() => {
      if (reuse_detext.value === 1) {
        return isValidVideoUrl(currentRow.value.pure_path) ? currentRow.value.pure_path : currentRow.value.origin_path
      }
      return currentRow.value.origin_path
    })

    const episodePlayBtns = rows.filter(item => item.origin_path && item.origin_path.indexOf('http') === 0).map(row => (
      <Button class="btn-default btn btn-xs" onClick={() => {
        currentRow.value = row
      }}
      >{row.serial_number}
      </Button>
    ))
    const closeDialog = openDialog({
      title: '抹除字幕',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div class="flex items-center gap-x-2 pb-4">
            <div>抹除方式:</div>
            <select class="select select-bordered select-sm" value={job_way.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              job_way.value = +value
            }}
            >
              <option value={3}>字节</option>
              <option value={2}>腾讯</option>
              <option value={1}>阿里</option>
            </select>
            <div>抹除视频:</div>
            <select class="select select-bordered select-sm" value={reuse_detext.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              reuse_detext.value = +value as 0 | 1
            }}
            >
              <option value={1}>使用已抹除的视频</option>
              <option value={0}>使用源视频</option>
            </select>
          </div>
          <Clip1 title="抹除坐标" path={playingEpisodePath.value} defaultValue={[defaultClipValue]} onChange={(p: [number, number, number, number][]) => {
            position.value = p
          }}
          />
          <div class="space-x-2 space-y-2 pt-4">
            {episodePlayBtns}
          </div>
          <div class="flex items-center justify-end gap-x-2 py-2">
            <Button class="btn-default btn btn-sm" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              try {
                btnLoading.value = true
                await apiSubtitleDel({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(row => row.serial_number),
                  regions: position.value,
                  job_way: job_way.value,
                  reuse_detext: reuse_detext.value,
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  const onEnglishTranslate = () => {
    const rows = props.list.filter(row => checked.value.includes(row.serial_number))
    const closeDialog = openDialog({
      title: '翻译',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div>
            会覆盖现有英文字幕，确定继续？
          </div>
          <div class="flex items-center justify-end gap-x-2 py-2">
            <Button class="btn-default btn btn-sm" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              try {
                btnLoading.value = true
                // 默认翻译成英语
                await apiSubtitleTrans({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(o => o.serial_number),
                  src_lang: props.language[0],
                  target_lang_list: ['en'],
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  const onTerminologyExtract = () => {
    const extractLoading = ref(false)
    const hideDeleteDialog = openDialog({
      title: '术语提取',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-extract-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-extract-body>确认提取术语</x-extract-body>
          <x-extract-footer class="mt-4 flex w-full justify-end gap-x-[10px]">
            <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" disabled={extractLoading.value} onClick={async () => {
              try {
                extractLoading.value = true
                await apiExtractTerminology({
                  series_resource_id: +route.params.id,
                })
                showSuccessToast('操作成功')
                hideDeleteDialog()
                emit('refreshList')
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              } finally {
                extractLoading.value = false
              }
            }}
            >
              {extractLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </Button>
          </x-extract-footer>
        </x-extract-confirm-dialog>
      ),
    })
  }

  onMounted(() => {
    void getSubtitleErrors()
    resetChecked()
  })

  return () => (
    <>
      {
        props.canEdit
          ? (
              <div class="flex space-x-4">
                <Button class="btn btn-primary btn-sm" disabled={checked.value.length === 0} onClick={onExtractSubtitle}>字幕抽取</Button>
                <Button class="btn btn-primary btn-sm" disabled={checked.value.length === 0} onClick={onCleanSubtitle}>字幕抹除</Button>
                {props.language && props.language[0] && props.language[0] !== 'en' ? <Button class="btn btn-primary btn-sm" disabled={checked.value.length === 0} onClick={onEnglishTranslate}>翻译成英语字幕</Button> : null }
                <Button class="btn btn-primary btn-sm" disabled={props.isExtractTerms} onClick={onTerminologyExtract}>{ props.isExtractTerms ? '提取中...' : '术语提取' }</Button>
                <Button class="btn btn-link btn-sm" onClick={() => {
                  window.open(`/voiceover-role?id=${+route.params.id}`)
                }}>角色配置概览</Button>
              </div>
            )
          : null
      }
      {subtitleErrorList.value.length > 0
        ? (
            <div class="flex">
              字幕异常数量：
              <Tooltip popContent={() =>
                subtitleErrorList.value.map(err => <div>第{err.serial_number}集，{langValue[langKey.findIndex(k => k === err.language_code)]}字幕, 错误个数: {err.err_count}</div>)}
              >
                <div class="flex cursor-pointer items-center space-x-1 text-red-500">
                  <span>{ subtitleErrorList.value.length }</span>
                  <SvgIcon class="size-3.5" name="ic_exclamation" />
                </div>
              </Tooltip>
            </div>
          )
        : null }
      <Table
        list={props.list || []}
        columns={columns.value}
        loading={loading.value}
      />
    </>
  )
})
