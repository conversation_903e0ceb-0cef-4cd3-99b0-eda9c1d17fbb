/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from "vue"
import { apiGetAiVoiceResource } from 'src/modules/voiceover-resource/voiceover-resource-api'
import { apiGetSingleSubtitle, apiGetSeriesCharacters } from 'src/modules/role-mark/role-mark-api'
import { apiGetResourcePublishDetail, apiGetSubtitleErrors, apiGetSubtitleEditable } from 'src/modules/resource-publish/resource-publish-api'
import { showFailToast } from "@skynet/ui"
import { apiGetRecordList, apiSeriesResourceList, } from 'src/modules/resource/resource-api'

export const useResourceDetailStore =  () => {
  return {
    loading,
    getList,
    characters,
    annotations,
    list,
    defaultLanguageCode,
    getCharacters,
    setSeriesId,
    getAnnotations,
    cdnUrl,
    getRealSubtitle,
    getDetail,
    currentResource,
    emotions,
    effects,
    emotions_cn,
    effects_cn,
    origin_syllable_list,
    shorten_syllable_list,
    getSubtitleRecordList,
    subtitleLogs,
    subtitleErrorList,
    getSubtitleErrors
  }
}

const series_resource_id = ref<number>(-1)
const loading = ref(false)
const characters = ref<Api.RoleMark.ICharacter[]>([])
const annotations = ref<Api.RoleMark.IAnnotation[]>([])
const subtitleLogs = ref<M.IAlterSubtitle[]>([])

const emotions = ref<string[]>([])
const effects = ref<string[]>([])
const emotions_cn = ref<string[]>([])
const effects_cn = ref<string[]>([])
const origin_syllable_list = ref<Api.VoiceoverResource.ISyllable[]>([])
const shorten_syllable_list = ref<Api.VoiceoverResource.ISyllable[]>([])

const list = ref<M.IResourceDrama[]>([])
const defaultLanguageCode = ref<string>('')
const cdnUrl = 'https://img.tianmai.cn/'
const currentResource = ref<M.IResourcePublishDetail>()
// 是否可以改
const editableLanguage = ref('')
const subtitleErrorList = ref<M.IResourceSubtitleError[]>([])

const setSeriesId = (id: number) => {
  series_resource_id.value = id
}

function findMissingNumbers(num: number, arr: M.IResourceDrama[]) {
  const result = []
  for (let i = 1; i <= num; i++) {
    if (!arr.some(row => row.serial_number === i)) {
      result.push(i)
    }
  }
  return result
}

function makeUpList(serialNumberArr: number[], o: M.IResourceDrama) {
  const keys = Object.keys(o)
  const emptyObj: { [record: string]: string | number } = {}
  keys.forEach(key => emptyObj[key] = '')
  return serialNumberArr.map(serialNumber => {
    return {
      ...emptyObj,
      serial_number: serialNumber,
    }
  })
}

const makeList = (_list: M.IResourceDrama[]) => {
  // 如果没有数据拿不到 无法补充数组
  if (_list.length === 0) {
    list.value = _list
    return
  }
  const missingRowNumber = findMissingNumbers(currentResource.value?.count || 0, _list)
  const result = makeUpList(missingRowNumber, _list[0])
  list.value = _list.concat(result as M.IResourceDrama[]).sort((a, b) => a.serial_number - b.serial_number)
}

const getList = async (showLoading = true) => {
  if (showLoading) {
    loading.value = true
  }
  const queryForm = {
    series_resource_id: series_resource_id.value,
    page_index: 1,
    page_size: 300, // 选填参数
  }
  const res = await apiSeriesResourceList({...queryForm})
  const _list = res?.data?.list.map(row => {
    row['zh-CN_subtitle_path'] = row.cn_subtitle_path
    return row
  }) || []
  loading.value = false
  makeList(_list)
  loading.value = false
}

const getCharacters = async () => {
  try {
    const res = await apiGetSeriesCharacters({
      series_resource_id: series_resource_id.value,
    })
    characters.value = res?.data?.characters || []
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    loading.value = false
  }
}

const getAnnotations = async (num: number, lang: string) => {
  const res = await apiGetSingleSubtitle({
    series_resource_id: series_resource_id.value,
    serial_number: num,
    language: lang
  })
  // 已经填入的
  annotations.value = res.data?.annotations || []
  emotions.value = res.data?.emotions || []
  effects.value = res.data?.effects || []
  emotions_cn.value = res.data?.emotions_cn || []
  effects_cn.value = res.data?.effects_cn || []
  shorten_syllable_list.value = res.data?.shorten_syllable_list || []
  origin_syllable_list.value = res.data?.origin_syllable_list || []
}

function getRealSubtitle(path: string) {
  if (!path) return ''
  const url = path.indexOf('http') === 0 ? path : `${cdnUrl}${path}`
  return url
}

const getDetail = async () => {
  try {
    const res = await apiGetResourcePublishDetail({
      id: series_resource_id.value
    })
    currentResource.value = res.data || {}
    if (currentResource.value.upload_subtitle_language_code) {
      defaultLanguageCode.value = currentResource.value.upload_subtitle_language_code[0]
    }
  } catch (error: any) {
    showFailToast(error.response.data.err_msg || '获取详情失败')
  }
}

export const getSubtitleRecordList = async () => {
  const res = await apiGetRecordList({
    series_resource_id: series_resource_id.value
  })
  subtitleLogs.value = res.data?.list || []
}

const getSubtitleErrors = async () => {
  const res = await apiGetSubtitleErrors({
    series_resource_id: series_resource_id.value
  })
  subtitleErrorList.value = res.data?.list || []
}

const getSubtitleEditable = async () => {
  const res = await apiGetSubtitleEditable()
  editableLanguage.value = res.data?.lang_code || ''
}
