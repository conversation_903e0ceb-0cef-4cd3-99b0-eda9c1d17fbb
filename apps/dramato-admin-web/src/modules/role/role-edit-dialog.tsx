/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { useRoleStore } from './use-role-store'
import { CreateForm, Button, showFailToast, showSuccessToast, Icon } from '@skynet/ui'
import { set, cloneDeep } from 'lodash-es'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { ref, onMounted } from 'vue'
import { apiUpdateRole } from './role-api'

type RoleEditDialogOptions = {
  props: {
    closeDialog: () => void
    afterSave: () => void
  }
}
export const RoleEditDialog = createComponent<RoleEditDialogOptions>({
  props: {
    closeDialog: () => {},
    afterSave: () => {},
  },
}, props => {
  const btnLoading = ref(false)
  const Form = CreateForm<M.IRole>()
  const form = ref<M.IRole>({
    role_name: '',
    access_type: 0,
    status: 1,
    access_urls: '',
    prefix_access_urls: '',
    not_access_urls: '',
    prefix_not_access_urls: '',
  })

  const formRules = z.object({
    role_name: z.string().min(1, '请输入角色名称'),
  })

  const { currentRole } = useRoleStore()
  const { error, validateAll } = useValidator(form, formRules)

  const save = async () => {
    if (!validateAll()) return
    try {
      btnLoading.value = true
      await apiUpdateRole(form.value)
      showSuccessToast('操作成功')
      props.afterSave()
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }

  onMounted(() => {
    if (currentRole.value.role) {
      form.value = cloneDeep(currentRole.value) as M.IRole
    }
  })

  return () => (
    <>
      <Form
        data={form.value}
        hasAction={false}
        error={error.value}
        class="w-full flex-col"
        onChange={(path, value) => {
          set(form.value, path, value)
        }}
        items={
          [
            [
              requiredLabel('角色名称：'),
              'role_name',
              {
                type: 'text',
              },
            ],
            [[
              requiredLabel('状态(不可修改)：'),
              'status',
              {
                type: 'radio',
                disabled: true,
                options: [{ label: '正常', value: 1 }, { label: '废弃', value: 2 }],
              },
              {
                class: 'pointer-events-none w-1/2',
              },
            ],
            [
              requiredLabel('鉴权类型(不可修改)：'),
              'access_type',
              {
                type: 'radio',
                options: [{ label: '可访问列表', value: 0 }, { label: '不可访问列表', value: 1 }]
              },
              {
                class: 'pointer-events-none w-1/2',
              },
            ]],
            [[
              'access_urls: ',
              'access_urls',
              {
                type: 'textarea',
                class: 'h-[200px]'
              },
              {
                class: 'w-1/2',
                hint: '换行分割'
              }
            ],
            [
              'prefix_access_urls: ',
              'prefix_access_urls',
              {
                type: 'textarea',
                class: 'h-[200px]'
              },
              {
                class: 'w-1/2',
                hint: '换行分割'
              }
            ]],
            [[
              'not_access_urls: ',
              'not_access_urls',
              {
                type: 'textarea',
                class: 'h-[200px]',
                disabled: true
              },
              {
                class: 'w-1/2',
                hint: '换行分割'
              }
            ],
            [
              'prefix_not_access_urls: ',
              'prefix_not_access_urls',
              {
                type: 'textarea',
                class: 'h-[200px]',
                disabled: true
              },
              {
                class: 'w-1/2',
                hint: '换行分割'
              }
            ]],
          ]
        }
      />

      <div class="flex justify-end gap-x-2">
        <Button class="btn  btn-sm" onClick={() => props.closeDialog()}>取消</Button>
        <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={save}>
          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
          确定
        </Button>
      </div>
    </>
  )
})
