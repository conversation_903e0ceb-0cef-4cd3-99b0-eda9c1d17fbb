export const hi = {
  // 通用
  common: {
  },
  // 分享页
  share: {
    watchNow: 'अभी देखें',
    episodeDialog: {
      title: 'मेरे साथ जुड़ें और अतिरिक्त सिक्के पाएं!',
      description: 'इस नाटक ने तो मुझे फंसा लिया है!',
      tips: 'सिफारिश',
      button: 'पूरा नाटक देखें',
      limit24: 'सीमित समय: 24 घंटे',
    },
    toast: {
      takeOffShelf: 'कोई सामग्री उपलब्ध नहीं',
    },
  },
  'customer-service-center': {
    title: 'ग्राहक सेवा केंद्र',
    description: 'विवरण',
    'description-placeholder': 'समस्या को समझने में हमारी सहायता करने के लिए विवरण प्रदान करें',
    'email-placeholder': 'प्रतिक्रिया अपडेट्स प्राप्त करने के लिए अपना ईमेल दर्ज करें',
    'submit-feedback': 'जमा करें',
    'enter-feedback': 'अन्य मुद्दे की प्रतिक्रिया',
    'upload-pictures': 'चित्र अपलोड करें',
    feedback: 'प्रतिक्रिया',
    'problem-type': 'समस्या प्रकार',
    'problem-type-placeholder': 'कृपया चुनें',
    'payment-issue-placeholder': 'कृपया अपना आईडी और आर्डर रसीद प्रदान करें: Google: https://pay.google.com/ (ऑर्डर नंबर GPA से शुरू होता है) iOS: ऐप स्टोर - Apple आईडी - पर्चेस हिस्ट्री (ऑर्डर आईडी)',
    'content-issue-placeholder': 'कृपया ड्रामा का शीर्षक, एपिसोड संख्या और गायब सबटाइटल भाषाएं प्रदान करें।',
    'ads-issue-placeholder': 'कृपया अपना डिवाइस मॉडल, देश, और भाषा सेटिंग्स प्रदान करें।',
    'technical-issue-placeholder': 'कृपया अपना डिवाइस मॉडल, देश, और भाषा सेटिंग्स प्रदान करें।',
    'other-issue-placeholder': 'कृपया अपना डिवाइस मॉडल, देश, और भाषा सेटिंग्स प्रदान करें।',
    'i-want-to-give-feedback': 'मैं फीडबैक देना चाहता/ती हूँ',
    'are-you-satisfied-with-this-reply': 'क्या आप इस जवाब से संतुष्ट हैं?',
    unsatisfied: 'असंतुष्ट',
    satisfied: 'संतुष्ट',
    myFeedback: 'मेरा फीडबैक',
    'reply-to-feedback': 'फीडबैक का उत्तर',
    'description-min-length': 'समस्या विवरण दर्ज करें (कम से कम 6 अक्षर).',
    'problem-type-not-choose': 'कृपया फीडबैक प्रकार चुनें.',
    'description-empty': 'इनपुट खाली नहीं हो सकता.',
  },
  terminate: {
    question: 'मैं अपनी सदस्यता का प्रबंधन कैसे करूँ？',
    desc: 'निम्नलिखित बटन पर क्लिक करें ताकि आप अनुबंध प्रबंधन पेज पर जा सकें. आप अनुबंध स्विच पर क्लिक करके अपनी सदस्यता का प्रबंधन कर सकते हैं:',
    title: 'अनुबंध का प्रबंधन',
    expiration: 'समाप्ति समय',
    manageDesc: 'अनुबंध का संबंध',
  },
  regulations: {
    howUseData: 'How We Use Your Data',
    content: `If you select "Allow" on the App Tracking Transparency permission request, we will use your information and data to optimize our services and provide you with the best experience, otherwise we will only collect the data and will not conduct any tracking activities.

If you agree to allow DramaWave and our partners and suppliers to use data from your device to analyze how you use the application, personalize content recommendations and provide more information related services, please click "Agree and Continue" to continue.
`,
    acceptAll: 'Accept All',
    dataSettings: 'Data Settings',
    next: 'Next',
    cancel: 'Cancel',
    aboutYourPrivacy: 'About Your Privacy',
    personalDataName: 'Personalised Data',
    personalDataDescription: 'We use this information to remember your choices and to provide  personalised advertising and content recommendations, notifications and  reminders, etc.',
    advertisementDataName: 'Advertisement Data',
    advertisementDataDescription: 'Cookies, identifiers of your  device, or other information may be stored or accessed on your device  for the purpose of online advertisements. User data may also be shared  with our advertising partners.',
    behaviorsDataName: 'Behavioural Data',
    behaviorsDataDescription: 'Data related to analytics, such as cookies or device identifiers, may be  stored for the purpose of measuring and analyzing usage, behavior, and  advertising performance.',
    privacyAndTerms: 'Privacy Policy and Terms of Service',
    referToPrivacy: 'Please refer to the Privacy Policy',
    referToTerms: 'Please refer to the Terms of Service',
    agreeAndSave: 'Agree & Save',
  },
  coins: {
    earnings: {
      title: 'मेरा कमाई',
      total: 'कुल हीरे',
      exchange: 'विनिमय',
      earningsHistory: 'कमाई का इतिहास',
      exchangeHistory: 'विनिमय का इतिहास',
      noRecords: 'कोई रिकॉर्ड नहीं',
      last50earnings: 'केवल पिछली 50 कमाई दिखाई जाती हैं',
      last50exchanges: 'केवल पिछले 50 विनिमय रिकॉर्ड दिखाई जातें हैं',
      benefits: 'लाभ',
    },
    exchange: {
      title: 'विनिमय क्षेत्र',
      notOpen: 'अब तक खुला नहीं है, कृपया संबंधित रहें',
      receiveBtn: 'खुशी से प्राप्त करें',
      earnCoins: 'हीरे कमाएँ',
      redeemRequiredXCoins: 'रिडीम आवश्यक {X} डायमंड्स',
      exchangeSuccess: 'एक्सचेंज सफल',
      cardId: 'कार्ड आईडी',
      exchangeFailed: 'एक्सचेंज विफल',
      transferFailedInsufficientStock: 'स्टॉक खत्म. कृपया कल जल्दी आइए।',
      gotItBtn: 'समझें',
      transferFailedRiskControl: 'खाता असामान्य, अस्थायी रूप से एक्सचेंज करने के योग्य नहीं।',
      tryAgainBtn: 'बाद में पुन: प्रयास करें',
      transferConfirm: 'ट्रांसफर पुष्टि करें',
      paypalAccountEmail: 'पेपैल खाता ईमेल',
      confirmPaypalAccountEmail: 'पेपैल खाते का ईमेल पुष्टि करें',
      transferX: 'ट्रांसफर {X}',
      transferXConfirm: 'ट्रांसफर {X} पुष्टि',
      transferToYourAccountX: 'आपके खाते में ट्रांसफर {X}',
      confirmBtn: 'पुष्टि',
      transferSuccessTitle: 'हम आपके पेपैल खाते में पैसे भेज रहे हैं!',
      transferSuccessContentX: 'आपके पैसे {X} खाते में 5 दिनों में आने वाले हैं।',
      copySuccess: 'कॉपी सफल',
      enterEmailError: 'कृपया एक मान्य ईमेल पता दर्ज करें',
      enterEmailValidError: 'इनपुट त्रुटि, कृपया फिर से दर्ज करें',
      emailNotMatchError: 'ईमेल पता मेल नहीं खाता, कृपया फिर से दर्ज करें',
    },
  },
  meal_check_in: {
    sleeping_link: 'सोते हुए डायमंड्स कमाएँ ',
    rules_title: 'गतिविधि नियम',
    rules_1: '1. हर दिन, आपके भोजन चेक करने के लिए चार समय स्लॉट होते हैं: नाश्ता: 07:00-10:00; दोपहर का भोजन: 11:00-14:00; रात का भोजन: 17:00-20:00; मिडनाइट स्नैक: 21:00-24:00।',
    rules_2: '2. यदि आप संबंधित चेक-इन समय को छोड़ देते हैं, तो आप एक वीडियो देखकर पुरस्कार पुनः प्राप्त कर सकते हैं।',
    rules_3: '3. पुरस्कार हर दिन मध्य रात में रीसेट हो जाते हैं, इसलिए अपने पुरस्कार दावा करना मत भूलें।',
  },
  sleep_check_in: {
    earn_link: 'पाने के लिए भोजन रिकॉर्ड करें',
    rules_1:
      '1. हर रात 19 बजे से दोपहर 12 बजे तक, आप \'पाने के लिए सोएँ\' मोड सक्रिय कर सकते/ती हैं.',
    rules_2:
      '2. इसे सक्रिय करने के बाद, अगले दिन, 8 घंटे सोने के बाद, आप सुबह 8 बजे से दोपहर 12 बजे के बीच सोने का इनाम क्लेम कर सकते/ती हैं.',
    rules_3:
      '3. यदि आप इनाम क्लेम का समय चूक जाते/ती हैं, तो आप अगले दिन दोपहर 12 बजे से शाम 7 बजे के बीच एक वीडियो देखने से पुरस्कार पुनः प्राप्त कर सकें.',
  },
  bind_email: {
    title: 'अपना ईमेल पता दर्ज करें',
    list_1: 'विशेष पुरस्कार सिक्के प्राप्त करने के लिए अपने ईमेल पता लिंक करें.',
    list_2: 'होनेवाली ईवेंट और विशेष ऑफ़र की सूचनाएँ प्राप्त करें.',
    list_3: 'नए एपिसोड के बारे में जानने वाले पहले लोग बनें.',
    verify: 'सत्यापित करें',
    email_error: 'कृपया एक वैध ईमेल पता दर्ज करें',
    verify_success: 'क्लेम सफल!',
    verify_progress: 'ईमेल का सत्यापन जारी है',
    re_enter: 'फिर से दर्ज करें',
    email_placeholder: 'कृपया आपका ईमेल पता दर्ज करें.',
    email_repeat: 'इस ईमेल ने पहले ही इस कार्यक्रम में भाग लिया है!',
  },
  toast: {
    'upload-failed': 'अपलोड विफल, कृपया फिर से अपलोड करें',
    'submit-success': 'सफलता से सबमिट करें',
    'email-incorrect': 'ईमेल प्रारूप गलत है',
  },
  not_found: {
    title: 'जो पेज आप खोज रहे/ही हैं, वह मौजूद नहीं',
  },
}
