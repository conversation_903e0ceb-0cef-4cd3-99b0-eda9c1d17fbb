import { createComponent, fn } from '@skynet/shared'
import { DramaItem } from './short-drama-api'
import { Button } from '@skynet/ui'
import { Fn } from '@vueuse/core'
type MultiSaveErrorDialogOptions = {
  props: {
    fails: {
      id: DramaItem['series_key']
      msg: string
    }[]
    type: 'price' | 'status'
  }
  emits: {
    confirm: Fn
    cancel: Fn
  }
}
export const MultiSaveErrorDialog = createComponent<MultiSaveErrorDialogOptions>({
  props: {
    fails: [],
    type: 'price',
  },
  emits: {
    confirm: fn,
    cancel: fn,
  },
}, (props, { emit }) => {
  // 根据msg分类，如果msg相同，则合并
  const msgMap = new Map<string, string[]>()
  props.fails.forEach(item => {
    if (msgMap.has(item.msg)) {
      msgMap.get(item.msg)?.push(item.id)
    } else {
      msgMap.set(item.msg, [item.id])
    }
  })
  return () => (
    <div>
      <div class="break-all">
        <p>有{props.fails.length}部短剧不符合{props.type === 'price' ? '修改定价' : '状态流转逻辑'}，请检查后重试</p>
        {
          // msg渲染
          Array.from(msgMap.entries()).map(([msg, ids]) => (
            <div class="mt-2 flex flex-col gap-1">
              <p>{msg}</p>
              <p class="line-clamp-5">短剧ID：{ids.join(',')}</p>
            </div>
          ))
        }
      </div>

      <div class="btn-group flex w-full items-center justify-end gap-3 absolute bottom-4 right-5">
        <Button class="btn btn-default btn-sm" onClick={() => emit('cancel')}>
          取消
        </Button>
        <Button class="btn btn-primary btn-sm" onClick={() => emit('confirm')}>
          确认
        </Button>
      </div>
    </div>
  )
})
