declare namespace Api {
  namespace Series {
    type GetSeriesItemResponse = ApiResponse<{
      info: M.Dramato
    }>
    type ViewDramaRequest = {
      series_id: string// 剧ID
      episode_id: string// 集ID
    }
    type UnlockEpisodeRequest = {
      series_id: string// 剧ID
      episode_id: string// 集ID
    }
    type UnlockEpisodeResponse = ApiResponse<{
      id: string
      name: string
      cover: string
      video_url: string
      index: number
      unlock: boolean
      duration: number
      video_type: string
      new: boolean
      update_time: number
      coupon_unlock: boolean // true-兑换券解锁
    }>
  }
}
