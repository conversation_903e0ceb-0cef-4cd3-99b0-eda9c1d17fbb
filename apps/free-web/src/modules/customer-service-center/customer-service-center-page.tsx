import { createComponent } from '@skynet/shared'
import { Button } from '@skynet/ui'
import router from 'src/router'
import { CollapseItem } from './collapse-item'
import { onMounted, ref } from 'vue'
import { track } from 'src/lib/track'
import { useLocale } from 'src/lib/use-locale'
type CustomerServiceCenterPageOptions = {
  props: {}
}
export const CustomerServiceCenterPage = createComponent<CustomerServiceCenterPageOptions>({
  props: {},
}, props => {
  const language = router.currentRoute.value.query.language as string
  const from = router.currentRoute.value.query.from as string

  const { t } = useLocale(language)

  const wrapper = ref<HTMLDivElement>()

  onMounted(() => {
    // 设置不同语言下的title
    document.title = t('customer-service-center.title')
    // 埋点上报
    track('csc', 'page', 'show', {
      from,
    })
  })

  return () => (
    <div ref={wrapper} class="scroll-smooth bg-[var(--surface-4)] h-screen pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] w-full overflow-y-auto">
      <x-collapse-list class="flex flex-col w-full gap-y-2 pt-5 px-3 pb-25">
        {new Array(2).fill(0).map((item, index) => (
          <CollapseItem
            key={index}
            title={t(`customer-service-center.faq.question${index + 1}`)}
            content={t(`customer-service-center.faq.answer${index + 1}`)}
          />
        ))}
      </x-collapse-list>
      <div class="fixed bottom-0 z-shareButton pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] w-[100%] left-1/2 -translate-x-1/2 h-25 bg-[var(--surface-4)]">
        <Button
          class="fixed bottom-11 z-shareButton pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] w-[calc(100%-1.5rem)] left-1/2 -translate-x-1/2 h-11 bg-[var(--brand-6)] rounded-lg border-none text-[var(--text-5)] text-base leading-tight"
          onClick={() => {
            track('csc', 'feedback', 'click')
            void router.push({ path: 'customer-service-center/feedback', query: { ...router.currentRoute.value.query, from: 'csc' } })
          }}
        >{t('customer-service-center.enter-feedback')}
        </Button>
      </div>
    </div>
  )
})

export default CustomerServiceCenterPage
