import { createComponent, mc } from '@skynet/shared'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useSeriesPackageAddFormStore } from './use-series-package-add-form-store'
import { set } from 'lodash-es'
import { transformNumber } from '@skynet/ui'
import { lang } from 'src/lib/constant'
type Step2CommonFormOptions = {
  props: {}
}
export const Step2CommonForm = createComponent<Step2CommonFormOptions>({
  props: {},
}, props => {
  const {
    formData,
    Form,
    twoStepError,
  } = useSeriesPackageAddFormStore()

  const { appOptions } = useAppAndLangOptions(() => formData.value.package_config?.package_type_4_config?.app_id, {
    onSuccess: () => {
      // const value = appOptions.value
      //   ?.find(i => i.label === ([0, 1].includes(formData.value?.user_platform || 0) ? 'Dramawave_iOS' : 'Dramawave_Android'))?.value || 0
      // if (!formData.value.package_config) {
      //   formData.value.package_config = {}
      // }
      // if (!formData.value.package_config.package_type_4_config) {
      //   formData.value.package_config.package_type_4_config = {}
      // }
      // if (!formData.value.package_config.package_type_4_config.app_id) {
      //   formData.value.package_config.package_type_4_config.app_id = value
      // }
    },
  })

  return () => (
    <Form
      class="flex flex-1 flex-col gap-y-3"
      hasAction={false}
      error={twoStepError.value}
      onChange={(path, value) => {
        set(formData.value || {}, path, value)
      }}
      items={[
        [
          '应用',
          'package_config.package_type_4_config.app_id',
          {
            type: 'select',
            options: appOptions.value
              .filter(i => {
                if (formData.value?.user_platform === 0) {
                  return (!window.location.origin.includes('-test') ? ['Dramawave_iOS', 'Dramawave_Android'] : ['Dramawave_iOS', 'Dramawave_Android']).includes(i.label)
                }
                return i.label === (formData.value?.user_platform === 2 ? 'Dramawave_Android' : 'Dramawave_iOS')
              }),
            disabled: formData.value.needDisabled,
          },
          { transform: transformNumber },
        ],
        [
          '发行语言',
          'package_config.package_type_4_config.language_version_code',
          {
            type: 'checkbox-group',
            options: [{ label: '全部', value: 'all', disabled: formData.value.needDisabled }, ...(lang.map(i => ({ ...i, disabled: formData.value.needDisabled })))],

          },
          { class: 'flex-wrap' },
        ],
        [
          '连载状态',
          'package_config.package_type_4_config.serialize_status',
          {
            type: 'radio',
            options: [
              { label: '全部', value: 0, disabled: formData.value.needDisabled },
              { label: '未完结', value: 1, disabled: formData.value.needDisabled },
              { label: '已完结', value: 2, disabled: formData.value.needDisabled },
            ],
          },
        ],
        [
          '上架状态',
          'package_config.package_type_4_config.listing_status',
          {
            type: 'checkbox-group',
            options: [
              { label: '全部', value: 0, disabled: formData.value.needDisabled },
              { label: '已上架', value: 3, disabled: formData.value.needDisabled },
              { label: '已下架', value: -1, disabled: formData.value.needDisabled },
              { label: '未上架', value: 1, disabled: formData.value.needDisabled },
              { label: '待上架', value: 2, disabled: formData.value.needDisabled },
              { label: '待下架', value: 4, disabled: formData.value.needDisabled },
            ],
          },
        ],
        [
          'vip类型',
          'package_config.package_type_4_config.vip_types',
          {
            type: 'checkbox-group',
            options: [
              { label: '非VIP剧', value: 0, disabled: formData.value.needDisabled },
              { label: 'VIP非连载剧', value: 1, disabled: formData.value.needDisabled },
              { label: 'VIP连载剧', value: 2, disabled: formData.value.needDisabled },
            ],
          },
          {
            hint: 'vip类型，空=全部。',
          },
        ],
        [
          '总集数',
          '',
          {
            type: 'custom',
            render: () => '',
          },
          {
            errorVisible: false,
          },
        ],
        [
          [
            '',
            'package_config.package_type_4_config.episode_count_begin',
            {
              type: 'text',
              disabled: formData.value.needDisabled,
            },
          ],
          [
            '',
            '',
            {
              type: 'custom',
              render: () => <span class="mt-1">{'<= 总集数 <='}</span>,
            },
          ],
          [
            '',
            'package_config.package_type_4_config.episode_count_end',
            {
              type: 'text',
              disabled: formData.value.needDisabled,
            },
          ],
        ],
        [
          '资源类型',
          'package_config.package_type_4_config.resource_type',
          {
            type: 'radio',
            options: [
              { label: '全部', value: 0, disabled: formData.value.needDisabled },
              { label: '本土', value: 1, disabled: formData.value.needDisabled },
              { label: '翻译', value: 2, disabled: formData.value.needDisabled },
            ],
          },
        ],
        [
          '付费状态',
          'package_config.package_type_4_config.free_status',
          {
            type: 'radio',
            options: [
              { label: '全部', value: 0, disabled: formData.value.needDisabled },
              { label: '免费', value: 1, disabled: formData.value.needDisabled },
              { label: '付费', value: 2, disabled: formData.value.needDisabled },
            ],
          },
        ],
        [
          '发行轮次',
          'package_config.package_type_4_config.release_round',
          {
            type: 'radio',
            options: [
              { label: '全部', value: 0, disabled: formData.value.needDisabled },
              { label: '首发', value: 1, disabled: formData.value.needDisabled },
              { label: '二轮', value: 2, disabled: formData.value.needDisabled },
            ],
          },
        ],
        [
          '保护期',
          '',
          {
            type: 'custom',
            render: () => '',
          },
          {
            errorVisible: false,
          },
        ],
        [
          [
            '',
            'package_config.package_type_4_config.protect_begin',
            {
              type: 'text',
              disabled: formData.value.needDisabled,
            },
          ],
          [
            '',
            '',
            {
              type: 'custom',
              render: () => <span class="mt-1">{'<= 保护期 <='}</span>,
            },
          ],
          [
            '',
            'package_config.package_type_4_config.protect_end',
            {
              type: 'text',
              disabled: formData.value.needDisabled,
            },
          ],
        ],
        [
          '累计金额',
          '',
          {
            type: 'custom',
            render: () => '',
          },
          {
            errorVisible: false,
          },
        ],
        [
          [
            '',
            'package_config.package_type_4_config.amount_begin',
            {
              type: 'text',
              suffix: '$',
              disabled: formData.value.needDisabled,
            },
          ],
          [
            '',
            '',
            {
              type: 'custom',
              render: () => <span class="mt-1">{'<= 累计金额 <='}</span>,
            },
          ],
          [
            '',
            'package_config.package_type_4_config.amount_end',
            {
              type: 'text',
              suffix: '$',
              disabled: formData.value.needDisabled,
            },
          ],
        ],
        [
          '投放金额',
          '',
          {
            type: 'custom',
            render: () => '',
          },
          {
            errorVisible: false,
          },
        ],
        [
          [
            '',
            'package_config.package_type_4_config.spend_begin',
            {
              type: 'text',
              suffix: '$',
              disabled: formData.value.needDisabled,
            },
          ],
          [
            '',
            '',
            {
              type: 'custom',
              render: () => <span class="mt-1">{'<= 投放金额 <='}</span>,
            },
          ],
          [
            '',
            'package_config.package_type_4_config.spend_end',
            {
              type: 'text',
              suffix: '$',
              disabled: formData.value.needDisabled,
            },
          ],
        ],
        [
          '音频类型',
          'package_config.package_type_4_config.audio_types',
          {
            type: 'checkbox-group',
            options: [
              { label: '非配音剧', value: 0, disabled: formData.value.needDisabled },
              { label: '配音上传剧', value: 1, disabled: formData.value.needDisabled },
              { label: 'AI配音剧', value: 2, disabled: formData.value.needDisabled },
            ],
          },
          {
            hint: '空数组=全部',
          },
        ],
        [
          '首次上架时间 - 按剧ID维度统计',
          '',
          {
            type: 'custom',
            render: () => '',
          },
          {
            errorVisible: false,
            class: mc((formData.value.package_config?.package_type_4_config?.audio_types || [])?.length > 0 && !formData.value.package_config?.package_type_4_config?.audio_types?.includes(0) ? '' : 'hidden'),
          },
        ],
        [
          [
            '',
            'package_config.package_type_4_config.first_list_begin',
            {
              type: 'text',
              suffix: '天',
              disabled: formData.value.needDisabled,
            },
            {
              class: mc((formData.value.package_config?.package_type_4_config?.audio_types || [])?.length > 0 && !formData.value.package_config?.package_type_4_config?.audio_types?.includes(0) ? '' : 'hidden'),
            },
          ],
          [
            '',
            '',
            {
              type: 'custom',
              render: () => <span class="mt-1">{'<= 首次上架时间 <='}</span>,
            },
            {
              class: mc((formData.value.package_config?.package_type_4_config?.audio_types || [])?.length > 0 && !formData.value.package_config?.package_type_4_config?.audio_types?.includes(0) ? '' : 'hidden'),
            },
          ],
          [
            '',
            'package_config.package_type_4_config.first_list_end',
            {
              type: 'text',
              suffix: '天',
              disabled: formData.value.needDisabled,
            },
            {
              class: mc((formData.value.package_config?.package_type_4_config?.audio_types || [])?.length > 0 && !formData.value.package_config?.package_type_4_config?.audio_types?.includes(0) ? '' : 'hidden'),
            },
          ],
        ],
        [
          '投放前',
          'package_config.package_type_4_config.spend_avg_day',
          {
            type: 'number',
            suffix: '天',
            disabled: formData.value.needDisabled,
          },
          {
            transform: transformNumber,
          },
        ],
        [
          [
            '',
            'package_config.package_type_4_config.spend_avg_begin',
            {
              type: 'text',
              suffix: '$',
              disabled: formData.value.needDisabled,
            },
          ],
          [
            '',
            '',
            {
              type: 'custom',
              render: () => <span class="mt-1">{'<= 总投放日均消耗（付费+免费） <='}</span>,
            },
          ],
          [
            '',
            'package_config.package_type_4_config.spend_avg_end',
            {
              type: 'text',
              suffix: '$',
              disabled: formData.value.needDisabled,
            },
          ],
        ],
        [
          [
            '',
            'package_config.package_type_4_config.spend_pay_avg_begin',
            {
              type: 'text',
              suffix: '$',
              disabled: formData.value.needDisabled,
            },
          ],
          [
            '',
            '',
            {
              type: 'custom',
              render: () => <span class="mt-1">{'<= 付费目标投放日均消耗 <='}</span>,
            },
          ],
          [
            '',
            'package_config.package_type_4_config.spend_pay_avg_end',
            {
              type: 'text',
              suffix: '$',
              disabled: formData.value.needDisabled,
            },
          ],
        ],
      ]}
      data={formData.value}
    />

  )
})

export default Step2CommonForm
