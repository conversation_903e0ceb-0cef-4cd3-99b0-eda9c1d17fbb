import { createComponent } from '@skynet/shared'
import { ref, watch } from 'vue'
import { loadAnimation } from '../animation/load-animation'
import loadingAnimation from 'src/modules/common/animation/loading.json'
import { MergeClass } from '@skynet/ui'
type LoadingOptions = {
  props: {}
}
export const Loading = createComponent<LoadingOptions>({
  props: {},
}, props => {
  const loadingRef = ref<HTMLDivElement>()
  watch(() => loadingRef.value, () => {
    if (!loadingRef.value) return
    void loadAnimation({
      container: loadingRef.value,
      renderer: 'svg',
      loop: true,
      autoplay: true,
      animationData: loadingAnimation,
    })
  })
  return () => (
    <MergeClass tag="x-loading" baseClass="flex justify-center items-center size-full">
      <div ref={loadingRef} class="size-11" />
    </MergeClass>
  )
})

export default Loading
