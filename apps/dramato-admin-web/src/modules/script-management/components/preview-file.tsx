/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { ref, computed } from 'vue'
// import { debounce } from 'lodash-es'

type PreviewOptions = {
  props: {
    url: string // 文件URL
    customClass?: string
  }
}

export const PreviewFile = createComponent<PreviewOptions>({
  props: {
    url: '',
    customClass: '',
  },
}, props => {
  const loading = ref(false)
  const error = ref<string>('')
  const scale = ref(1)

  const fileType = computed(() => {
    return props.url.split('.').pop()
  })


  // const handleZoomIn = () => {
  //   scale.value = scale.value + 0.1
  // }

  // const handleZoomOut = () => {
  //   scale.value = scale.value - 0.1
  // }

  // const handleResetZoom = () => {
  //   scale.value = 1
  // }

  // 滚轮滚动
  // const handleScroll = debounce(event => {
  //   event.preventDefault() // 阻止默认的滚动行为,这里不知为什么不起效,所以直接使用@wheel.prevent
  //   // 判断是否是ctrl+滚轮
  //   if (event.ctrlKey) { // 是ctrl+滚轮就放大缩小页面
  //     if (event.deltaY > 0) {
  //       handleZoomOut()
  //     } else if (event.deltaY < 0) {
  //       handleZoomIn()
  //     }
  //   } else { // 不是就滑动滚轮来翻页
  //     // event.wheelDeltaY < 0 ? nextPage() : lastPage()
  //   }
  // }, 50)

  return () => (
    <x-preview class={mc('relative margin-auto w-full overflow-auto', props.customClass)}>
      {loading.value && (
        <div class="absolute inset-0 flex items-center justify-center bg-white/80">
          <div class="loading loading-spinner loading-lg" />
        </div>
      )}

      {error.value && (
        <div class="absolute inset-0 flex items-center justify-center text-error">
          {error.value}
        </div>
      )}

      {fileType.value === 'pdf' && (
        <>
          {/* <div class="flex justify-center gap-4 bg-white p-2 shadow-sm">
            <button
              disabled={scale.value <= 0.1}
              class="btn btn-outline btn-xs"
              onClick={handleZoomOut}
            >
              缩小
            </button>
            <button
              class="btn btn-outline btn-xs"
              onClick={handleResetZoom}
            >
              重置
            </button>
            <button
              disabled={scale.value >= 2}
              class="btn btn-outline btn-xs"
              onClick={handleZoomIn}
            >
              放大
            </button>
          </div> */}
          <div class="flex w-full justify-center">
            <iframe src={props.url} width="600" height="600" style="border: none;" />
          </div>
          {/* <div class="absolute inset-0" onWheel={handleScroll} /> */}
        </>
      )}

      {(fileType.value === 'docx' || fileType.value === 'doc') && (
        <iframe src={`https://view.officeapps.live.com/op/embed.aspx?src=${props.url}`} width="600" height="600" style="border: none;" />
      )}
    </x-preview>
  )
})

export default PreviewFile
