import { httpClient } from 'src/lib/http-client'
import { filterIt, splitIt, trimIt } from '@skynet/shared'

export const serializeList = [
  {
    code: 1,
    name: '未完结',
  },
  {
    code: 2,
    name: '已完结',
  },
]

export const listingStatusList = [
  {
    code: 1,
    name: '未上架',
  },
  {
    code: 2,
    name: '待上架',
  },
  {
    code: 3,
    name: '已上架',
  },
  {
    code: 4,
    name: '待下架',
  },
  {
    code: 5,
    name: '虚拟上架',
  },
  {
    code: -1,
    name: '已下架',
  },
]

export const freeStatusList = [
  {
    code: 1,
    name: '免费',
  },
  {
    code: 2,
    name: '付费',
  },
]

export const resourceTypList = [
  {
    code: 0,
    name: '全部',
  },
  {
    code: 1,
    name: '本土',
  },
  {
    code: 2,
    name: '翻译',
  },
]

export const releaseRoundList = [
  {
    value: 0,
    label: '全部',
  },
  {
    value: 1,
    label: '首发',
  },
  {
    value: 2,
    label: '二轮',
  },
]

export type SortItem = {
  // 排序字段：publish_time（授权时间）、updated_time、listing_time（上架时间）
  field: string | '' | 'publish_time' | 'updated' | 'listing_time'
  // 是否升序 true/false
  asc: boolean
}

export type QueryDramaListParams = {
  app_id: '' | Q.App['id']
  language_version_code: string
  free_status?: number // 免费状态: 1 免费， 2 收费
  serialize_status?: number // 连载状态 1 未完结 2 已完结
  listing_status?: Array<number> // 上架状态： 1 未上架 2 待上架 3 已上架 4 待下架 -1 已下架
  listing_time_start?: string // 上架时间 'YYYY-MM-DD HH:mm:ss'
  updated_time_start?: string // 更新时间 'YYYY-MM-DD HH:mm:ss'
  publish_time_start?: string // 授权时间 'YYYY-MM-DD HH:mm:ss'
  listing_time_end?: string
  updated_time_end?: string
  publish_time_end?: string
  search_keyword: DramaItem['series_key'][]// series_key
  sort_info: SortItem[]
  page_info: PageInfo
  resource_type: 0 | 1 | 2
  series_resource_id?: number
  resource_id_or_title_list: string | string[]
  release_round: 0 | 1 | 2
  label_id_list?: number[]
  audio_type: -1 | 0 | 1 | 2
}

export type DramaItem = {
  drama_id: number
  series_key: string // 剧集唯一标识
  app_id: string // 应用唯一标识
  app_name: string // 应用名称
  title: string // 短剧标题
  labels: string // 内容标签
  description: string // 简介
  cover_url: string // 封面url
  episodes_number: number// 集数
  language_version_code: string // 语言版本code
  language_version_name: string // 语言版本名称
  start_paying_episodes: number // 开始付费集数
  episodes_price: number // 单集价格
  serialize_status: 1 | 2 // 连载状态 1 未完结 2 已完结
  is_free: 0 | 1 | number // 是否开启免费 0 未开启  1 开启
  free: boolean // 是否免费
  listing_status: -1 | 1 | 2 | 3 | 4// 上架状态： 1 未上架 2 待上架 3 已上架 4 待下架 -1 已下架
  is_time_listing: boolean // 是否定时上架 true/false
  listing_time: number // 上架时间
  is_time_removal: boolean // 是否定时下架 true/false
  removal_time: number // 下架时间
  publish_time: number // 发布时间
  created_time: number // 创建时间
  updated_time: number // 更新时间
  operator_id: number// 操作用户id
  operator_name: string// 操作用户名称
  publish_user_id: number// 发布用户id
  publish_user_name: string// 发布用户名称
  free_start: number // 免费开始时间
  free_end: number // 免费结束时间
  free_online_time?: number
  resource_type: 1 | 2 | number
  resource_id: number
  resource_title: string
  release_round: 1 | 2 | number
  app_platform: 1 | 2 | number
  unshelve_reason?: string
  audio_type: 0 | 1 | 2 // 默认 1：配音上传剧 2: AI配音剧
}
export const trimSplitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`)).map(str => str.replace(new RegExp(`^[${[' '].join('')}]+|[${[' '].join('')}]+$`, 'g'), ''))

export const apiDramaQueryList = (data: QueryDramaListParams) => {
  return httpClient.post<ApiResponse<{
    total: number
    list: DramaItem[]
  }>>('/drama/list', data, {
    transformRequestData: {
      resource_id_or_title_list: [trimSplitIt([',', ';', '，', ' ', '；']), filterIt(Boolean)],
    },
  })
}

export const apiDramaQueryByKeyword = (data: {
  app_id: number
  language_version: string
  search_keyword: string
}) => {
  return httpClient.post<ApiResponse<{
    total: number
    list: DramaItem[]
  }>>('/drama/query-by-keyword', data)
}

export type updateListingStatus = {
  id: number[]
  list_status: -1 | 1 | 2 | 3 | 4
  list_time?: string
  platform_sync_list?: boolean
  unshelve_reason?: string
}

export const apiDramaUpdateListStatus = (data: updateListingStatus) => {
  return httpClient.post<ApiResponse<{
    success: number // 成功数
    fail: [{
      id: string
      msg: string
    }]
  }>>('/drama/update-list-status', data)
}

export type updatePrice = {
  id: number[]
  episodes_price?: number
  start_paying_episodes?: number
  is_free?: number
  free_start?: number
  free_end?: number
  platform_sync_list?: boolean
}

export const apiDramaUpdatePrice = (data: updatePrice) => {
  return httpClient.post<ApiResponse<{
    success: number // 成功数
    fail: [{
      id: string
      msg: string
    }]
  }>>('/drama/update-price', data)
}

export const apiD = (data: unknown) => {
  return httpClient.post<unknown>('/drama/publish-app-drama', data)
}

export const apiDramaDetails = (dramaId: number) => {
  return httpClient.get<ApiResponse<DramaItem>>('drama/detail?drama_id=' + dramaId)
}

export const apiGetLabelList = (data: {
  language_code: string
  series_resource_id?: number
  label_type?: 1 | 2 | 3
}) =>
  httpClient.post<ApiResponse<{
    list: M.ITag[]
  }>>('/series_resource_v2/label_list', data)
