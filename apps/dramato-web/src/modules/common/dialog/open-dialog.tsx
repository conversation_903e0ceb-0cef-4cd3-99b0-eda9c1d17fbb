import { ref } from 'vue'
import type { DialogProps } from './dialog.tsx'
import { useDialogStore } from './use-dialog-store.tsx'

/**
 * 打开对话框
 * @example
 * ```tsx
 * const close = openDialog({
 *   title: "编辑",
 *   body: <YourComponent onClose={() => close()} />,
 * });
 ```
 */
export const openDialog = (options: Omit<DialogProps, 'visible'>) => {
  const dialogStore = useDialogStore()
  const index = ref<number>()
  const { addDialog, removeDialog } = dialogStore
  const open = () => {
    if (options.hideParentWhenChildOpen) {
      if (dialogStore.dialogStack && dialogStore.dialogStack.value.length > 0) {
        dialogStore.dialogStack.value.forEach(d => (d.visible = false))
      }
    }
    index.value = addDialog?.(options)
  }
  const close = () => {
    if (index.value === undefined) return
    if (options.showParentWhenChildClose) {
      if (dialogStore.dialogStack && dialogStore.dialogStack.value.length > 0 && index.value - 1 > -1) {
        dialogStore.dialogStack.value[index.value - 1].visible = true
      }
    }
    if (options.beforeClose?.() === false) return
    removeDialog(index.value)
    index.value = undefined
  }
  open()
  return close
}
