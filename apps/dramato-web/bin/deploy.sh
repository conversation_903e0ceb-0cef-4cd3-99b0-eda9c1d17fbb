#!/bin/sh

set -e

source bin/helper.sh

# 如果 MODE 为空，或者 MODE 不是 test 也不是 prod，则提示 MODE 要么为 test 要么为 prod
if [ -z "$MODE" ] || { [ "$MODE" != "test" ] && [ "$MODE" != "prod" ]; }; then
  error 'Usage: deploy.sh mode:test|prod message'
  exit 1
fi

# 如果 MESSAGE 为空，则设置提示需要设置 MESSAGE 并给出示例
if [ -z "$MESSAGE" ]; then
  error "请设置 MESSAGE 参数"
  info "示例: pnpm deploy:test 修复登录问题"
  exit 1
fi

# 如果 MESSAGE 少于五个字符，则提示至少输入五个字符
if [ ${#MESSAGE} -lt 5 ]; then
  error "MESSAGE 至少需要五个字符"
  exit 1
fi

if [ ! -f package.json ]; then
  error '当前目录必须包含 package.json 文件'
  exit 1
fi

# 如果 FORCE 不为 true，则执行分支检查和同步检查
if [ "$FORCE" != "true" ]; then
  # 获取当前 git branch
  CURRENT_BRANCH=$(git branch --show-current)
  # 如果当前分支跟 BRANCH 不相等，则提示 请切换到 $BRANCH 分支
  if [ "$CURRENT_BRANCH" != "$BRANCH" ]; then
    error "请切换到 $BRANCH 分支"
    exit 1
  fi

  # 如果当前分支有未提交的代码，则提示 请 git commit
  if [ -n "$(git status -s)" ]; then
    error "请先 git commit"
    exit 1
  fi

  # 更新当前分支， 如果当前分支跟远程分支不一致，则提示请先同步远程分支
  info "git fetch origin $BRANCH"
  git fetch origin $BRANCH
  if [ "$(git rev-parse origin/$BRANCH)" != "$(git rev-parse HEAD)" ]; then
    warn "检测到本地分支 $CURRENT_BRANCH 与远程分支 $BRANCH 不一致，请同步远程分支"
    exit 1
  fi
else
  warn "跳过分支检查"
fi

info "开始部署 $RELATIVE_PATH "

info '创建临时目录'
mkdir -p $TEMP

# 根据 NO_BUILD 标志决定是否构建 dist 目录
if [ "$NO_BUILD" = false ]; then
  info '生成 dist 目录'
  pnpm i
  pnpm run "build:$MODE"
else
  info '跳过构建 dist 目录 (--no-build 模式)'
fi
rm -rf $TEMP/dist
yes | cp -rf dist $TEMP/dist
yes | cp bin/build.sh $TEMP/build.sh
yes | cp bin/before_build.sh $TEMP/before_build.sh
info "dist 已复制到 $TEMP，你现在可以切换分支或者修改代码，不会影响部署"
info "---------------------------------------------------------"
info '创建 deploy_repo'
# 如果不存在 $TEMP/deploy_repo 目录，则下载 $BRANCH 分支到 $TEMP/deploy_repo 目录。如果存在这个目录，就获取 $BRANCH 分支的最新代码
if [ ! -d "$TEMP/deploy_repo" ]; then
  git clone -b $BRANCH "$REPO" "$TEMP/deploy_repo"
else
  cd $TEMP/deploy_repo
  git fetch origin $BRANCH
  git switch $BRANCH
  cd ..
fi

info '复制 dist 到 deploy_repo'
cd $TEMP/deploy_repo
git fetch
git reset --hard origin/$BRANCH
rm -rf $TEMP/deploy_repo/dist
yes | cp -r $TEMP/dist $TEMP/deploy_repo/dist
yes | cp $TEMP/build.sh $TEMP/deploy_repo/build.sh
yes | cp $TEMP/before_build.sh $TEMP/deploy_repo/before_build.sh
git add *
git commit -m $MESSAGE
git push -f origin $BRANCH:$BRANCH

if [ "$MODE" == 'test' ]; then
  info '自动触发 jenkins 测试环境部署'
  info "如果命令卡住，请手动执行命令 pnpm jenkins:$MODE"
  curl -X POST "http://yinghang.fang:<EMAIL>:8080/job/$JENKINS_JOB/build"
else
  info '自动触发 jenkins 生产环境部署'
  info "如果命令卡住，请手动执行命令 pnpm jenkins:$MODE"
  curl -X POST "http://yinghang.fang:<EMAIL>:8080/job/$JENKINS_JOB/build"
fi

set +e
echo 'success'
