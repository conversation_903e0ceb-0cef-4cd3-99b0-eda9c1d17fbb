export interface IPlayerOptions {
  source: string
  id: string | HTMLElement
  preload?: string
  enableH265?: boolean
  controlBarVisibility?: string
  mediaType?: string
  height?: string
  width?: string
  autoplay?: boolean
  videoFillMode?: string
  playsinline?: boolean
  useH5Prism?: boolean
  format?: string
  clickPause?: boolean
  skinLayoutIgnore?: string[]
}

export class Aliplayer {
  constructor(options: IPlayerOptions, callback?: (player: Aliplayer) => void)
  _audioTrackService: { switch(track: number): void }
  on(event: Events, callback: (data: unknown) => void): void
  off(event: Events): void
  play(): void
  pause(): void
  dispose(): void
  getStatus(): string
  setTextTracks(tracks: unknown[]): void
  trigger(event: Events, ...args: unknown[]): void
}
