import { httpClient } from 'src/lib/http-client'
import { WelfareEatDetails, ReceiveRes, ReclaimReceiveRes } from './meal-check-in.d'

// 吃饭详情
export const apiGetWelfareEatDetails = () => {
  return httpClient.get<ApiResponse<WelfareEatDetails>>('/fr-api/welfare/eat-detail')
}
// 领取奖励
export const ApiPostWelfareReceive = (data: {
  welfare_id: number
  schedule?: number
}) => {
  return httpClient.post<ApiResponse<ReceiveRes>>('/fr-api/welfare/receive', data)
}
// 活动补领
export const apiPostWelfareReclaimReceive = (data: {
  welfare_id: number
  schedule?: number
}) => {
  return httpClient.post<ApiResponse<ReclaimReceiveRes>>('/fr-api/welfare/reclaim-receive', data)
}
// 看激励视频后领取广告奖励
export const ApiPostWelfareADReceive = (data: {
  ad_extra: string
}) => {
  return httpClient.post<ApiResponse<ReceiveRes>>('/fr-api/welfare/ad-receive', data)
}
