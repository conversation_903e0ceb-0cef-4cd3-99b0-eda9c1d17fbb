import { createCachedFn, createStringId, Key } from '@skynet/shared'
import { Alert, AlertProps } from './alert'
import { h, shallowRef, VNodeChild } from 'vue'
import { AlertType } from './alert-types'

export const useAlertStore = createCachedFn((_id: Key) => {
  // 由于用的是 shallowRef，所以对 alertList 使用 split、splice 等方法时，不会触发 UI 更新
  // 因此，每次 alertList 变化时，都需要重新赋值
  const alertList = shallowRef<Array<AlertProps>>([])
  const hideList = shallowRef<Array<[string, number]>>([])
  const icons = shallowRef<Record<AlertType, VNodeChild>>({
    info: 'ℹ️',
    success: '🎉',
    warning: '️🚧',
    error: '🚨',
  })
  window.setInterval(() => {
    const now = new Date().getTime()
    hideList.value.map(([id, time]) => {
      if (time < now) {
        updateAlert(id, { visible: false })
        removeFromHideList(id)
      }
    })
  }, 200)

  const addAlert = (props: Omit<AlertProps, 'visible' | 'alertId'>) => {
    const alertId = createStringId('alert')
    alertList.value = [...alertList.value, { ...props, alertId }]
    return alertId
  }
  const updateAlert = (id: string, props: Partial<AlertProps>) => {
    const index = alertList.value.findIndex(p => p.alertId === id)
    if (index < 0) return
    alertList.value = [...alertList.value.slice(0, index), {
      ...alertList.value[index],
      ...props,
    }, ...alertList.value.slice(index + 1)]
  }
  const removeAlert = (id: string) => {
    alertList.value = alertList.value.filter(p => p.alertId !== id)
  }
  const clearAlerts = () => {
    alertList.value = []
  }
  const renderAlerts = () => {
    return alertList.value.map(props => (
      h(Alert, {
        key: props.alertId,
        ...props,
        onDestroy: () => removeAlert(props.alertId),
      })
    ))
  }
  const addToHideList = (id: string, time: number) => {
    hideList.value.push([id, time])
  }
  const removeFromHideList = (id: string) => {
    const index = hideList.value.findIndex(([itemId]) => itemId === id)
    if (index < 0) return
    hideList.value.splice(index, 1)
  }
  return {
    icons,
    alertList,
    addAlert,
    updateAlert,
    removeAlert,
    clearAlerts,
    renderAlerts,
    addToHideList,
    removeFromHideList,
  }
})
