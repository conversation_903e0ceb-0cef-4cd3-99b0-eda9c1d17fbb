/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, onUnmounted, ref } from 'vue'
import { useVoiceoverResource } from './use-voiceover-resource-store'
import { ElTable, ElTableColumn } from 'element-plus'
import { RouterLink, useRoute } from 'vue-router'
import { Button, Icon, openDialog, Radio, RadioGroup, showFailToast, showSuccessToast, SvgIcon } from '@skynet/ui'
import { apiSeparateAudio, apiEraseAudio, apiUploadSaveAudio } from 'src/modules/resource/resource-api'
import { apiGenerateAiVoice, apiAliTransVideo, apiShortAISrt, apiVoiceOptimize } from 'src/modules/voiceover-resource/voiceover-resource-api'
import { Uploader, type UploadImage } from 'src/modules/common/uploader/uploader'
import { requiredLabel } from 'src/lib/required-label'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { langKey, langValue } from 'src/modules/resource/constant'
import { ColFilter } from 'src/modules/resource/components/col-filter'
import { InjectVideoForm } from './inject-video-form'
import { useDialogStore } from '@skynet/ui'
import { apiCombineVoice } from 'src/modules/voiceover-editor/voiceover-editor-api'

type VoiceoverResourcePageOptions = {
  props: {}
}
export const VoiceoverResourcePage = createComponent<VoiceoverResourcePageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, columns, cdnUrl, audios, filterCols, selectCols, defaultLanguageCode } = useVoiceoverResource()
  const { dialogStack } = useDialogStore()
  console.log(dialogStack.value)
  const tableRef = ref<InstanceType<typeof ElTable>>()
  const route = useRoute()
  const checked = ref<Api.VoiceoverResource.IMultipleResource[]>([])
  const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()
  const tableHeight = ref(0)
  const handleSelectionChange = (selection: Api.VoiceoverResource.IMultipleResource[]) => {
    checked.value = selection
  }

  const downloadAudio = () => {
    const downloadType = ref<'audio' | 'video'>('video')
    const downloadLoading = ref(false)
    const langCode = ref<string>('')
    const closeDialog = openDialog({
      title: '下载音视频',
      mainClass: 'pb-0 px-5',
      body: () => (
        <div class="space-y-4">
          <div class="flex items-center gap-x-2">
            类型：
            <RadioGroup
              modelValue={downloadType.value}
              onUpdate:modelValue={v => {
                downloadType.value = v as 'audio' | 'video'
              }}
              options={[
                { label: '音频', value: 'audio' },
                { label: '视频', value: 'video' },
              ]}
            />
          </div>
          {downloadType.value === 'audio' ? (
            <div class="flex items-center gap-x-2">
              语言：
              <select
                value={langCode.value}
                class="select select-bordered select-sm"
                onChange={(e: Event) => {
                  langCode.value = (e.target as HTMLSelectElement).value
                }}
              >
                <option value='ora_path'>原语言</option>
                {langKey.map((lang, index) => (
                  <option value={lang}>{langValue[index]}</option>
                ))}
              </select>
            </div>
          ) : null}
          <div>
            共选中: { checked.value.length }集
          </div>
          <div>
            选中集数： {checked.value.map(row => row.num).join(',')}
          </div>
          <div class="flex items-center justify-end gap-x-2 py-2">
            <Button class="btn-default btn btn-sm" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!langCode.value && downloadType.value === 'audio') {
                showFailToast('请选择音频语言')
                return
              }
              showSuccessToast('下载中……')
              downloadLoading.value = true
              try {
                const m: {
                  [record: string]: string
                } = {}
                // _audio_path
                if (downloadType.value === 'audio') {
                  // 如果是原音频
                  if (langCode.value === 'ora_path') {
                    checked.value.map(c => {
                      const url = audios.value.find(a => a.num === c.num)?.ora_path
                      if (url) {
                        m['' + c.num] = `${cdnUrl}${url}`
                      }
                    })
                  } else {
                    checked.value.map(c => {
                      const obj = list.value.find(item => item.num === c.num)
                      if (obj) {
                        const url = obj[`${langCode.value}_audio_path`] || ''
                        if (url) m['' + c.num] = `${cdnUrl}${url}`
                      }
                    })
                  }
                } else {
                  checked.value.map(c => {
                    const obj = list.value.find(item => item.num === c.num)
                    if (obj) {
                      const url = obj[`${defaultLanguageCode.value}_ora_video_path`] || ''
                      if (url) m['' + c.num] = `${cdnUrl}${url}`
                    }
                  })
                }

                const names = Object.keys(m)
                names.forEach(name => {
                  const url = m[name]
                  if (url) {
                    const ext = url.split('.').pop() || ''
                    // 使用fetch获取音频文件并强制下载
                    fetch(url)
                      .then(response => response.blob())
                      .then(blob => {
                        const blobUrl = window.URL.createObjectURL(blob)
                        const a = document.createElement('a')
                        a.href = blobUrl
                        a.download = `${name}.${ext}`
                        document.body.appendChild(a)
                        a.click()
                        document.body.removeChild(a)
                        window.URL.revokeObjectURL(blobUrl)
                      })
                      .catch(error => {
                        console.error('下载音频文件失败:', error)
                        showFailToast('下载音频文件失败')
                      })
                  }
                })
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
              } finally {
                downloadLoading.value = false
                closeDialog()
              }
            }}
            >确认
            </Button>
          </div>
        </div>
      ),
      customClass: '!w-[500px]',
    })
  }

  const splitAudio = () => {
    const btnLoading = ref(false)
    const closeDialog = openDialog({
      title: '人声和背景音分离',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div>
            共选中: { checked.value.length }集
          </div>
          <div>
            选中集数： {checked.value.map(row => row.num).join(',')}
          </div>
          <div class="flex items-center justify-end gap-x-2 py-2">
            <Button class="btn-default btn btn-sm" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              try {
                btnLoading.value = true
                await apiSeparateAudio({
                  series_resource_id: +route.params.id,
                  serial_numbers: checked.value.map(row => row.num),
                })
                showSuccessToast('操作成功')
                delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  const eraseAudio = () => {
    const btnLoading = ref(false)
    const closeDialog = openDialog({
      title: '分离音视频',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div>
            共选中: { checked.value.length }集
          </div>
          <div class="break-words">
            选中集数： {checked.value.map(row => row.num).join(',')}
          </div>
          <div class="flex items-center justify-end gap-x-2 py-2">
            <Button class="btn-default btn btn-sm" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              try {
                btnLoading.value = true
                await apiEraseAudio({
                  series_resource_id: +route.params.id,
                  serial_numbers: checked.value.map(row => row.num),
                })
                showSuccessToast('操作成功')
                delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  const generateAIVoice = () => {
    const language_codes = ref<string[]>([])
    const saveLoading = ref(false)

    const hideDialog = openDialog({
      title: '生成AI配音',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[400px]',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <div class="flex items-center gap-x-2">
            {requiredLabel('语言:')}
            <FormMultiSelect
              search={true}
              class="w-[200px]"
              popoverWrapperClass="z-popover-in-dialog"
              options={langKey.map((n, index) => {
                return { value: langKey[index], label: langValue[index] }
              })}
              modelValue={language_codes.value}
              onUpdate:modelValue={e => {
                language_codes.value = e as string[]
              }}
            />
          </div>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
              if (language_codes.value.length === 0) {
                showFailToast('请选择支持语言')
                return
              }
              try {
                saveLoading.value = true
                await apiGenerateAiVoice({
                  series_resource_id: +route.params.id,
                  serial_numbers: checked.value.map(item => item.num),
                  languages: language_codes.value,
                })
                onQuery(+route.params.id)
                hideDialog()
                showSuccessToast('操作成功')
                saveLoading.value = false
              } catch (error: any) {
                saveLoading.value = false
                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
              }
            }}
            >
              {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }
  // 背景音优化
  const splitOpt = () => {
    const saveLoading = ref(false)
    const hideDialog = openDialog({
      title: '背景音优化',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[400px]',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <div class="flex items-center gap-x-2">
            处理集数：总共{checked.value.length}集
          </div>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
              try {
                saveLoading.value = true
                await apiVoiceOptimize({
                  series_resource_id: +route.params.id,
                  serial_numbers: checked.value.map(item => item.num),
                })
                onQuery(+route.params.id)
                hideDialog()
                showSuccessToast('操作成功')
                saveLoading.value = false
              } catch (error: any) {
                saveLoading.value = false
                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
              }
            }}
            >
              {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const shortAISrt = () => {
    const language_codes = ref<string[]>([])
    const saveLoading = ref(false)
    const allowLangs = ['en', 'th', 'vi', 'pt', 'de','ru','tr']
    const showLans = langKey.filter(item => allowLangs.includes(item))
    const showLangValue = langValue.filter((item, index) => showLans.includes(langKey[index]))
    const hideDialog = openDialog({
      title: '字幕缩短',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[400px]',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <div class="flex items-center gap-x-2">
            {requiredLabel('语言:')}
            <FormMultiSelect
              search={true}
              class="w-[200px]"
              popoverWrapperClass="z-popover-in-dialog"
              options={langKey.map((n, index) => {
                return { value: langKey[index], label: langValue[index] }
              })}
              modelValue={language_codes.value}
              onUpdate:modelValue={e => {
                language_codes.value = e as string[]
              }}
            />
          </div>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
              if (language_codes.value.length === 0) {
                showFailToast('请选择支持语言')
                return
              }
              try {
                saveLoading.value = true
                await apiShortAISrt({
                  series_resource_id: +route.params.id,
                  serial_numbers: checked.value.map(item => item.num),
                  languages: language_codes.value,
                })
                onQuery(+route.params.id)
                hideDialog()
                showSuccessToast('操作成功')
                saveLoading.value = false
              } catch (error: any) {
                saveLoading.value = false
                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
              }
            }}
            >
              {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const combineAudio = () => {
    const bindLoading = ref(false)
    const language_codes = ref<string[]>([])

    const hideDialog = openDialog({
      title: '合成音频',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[400px]',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-4">
          <div class="flex items-center gap-x-2">
            {requiredLabel('语言:')}
            <FormMultiSelect
              search={true}
              class="w-[200px]"
              popoverWrapperClass="z-popover-in-dialog"
              options={langKey.map((n, index) => {
                return { value: langKey[index], label: langValue[index] }
              })}
              modelValue={language_codes.value}
              onUpdate:modelValue={e => {
                language_codes.value = e as string[]
              }}
            />
          </div>
          <div>
            共选中: { checked.value.length }集
          </div>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={bindLoading.value} onClick={async () => {
              try {
                if (language_codes.value.length === 0) {
                  showFailToast('请选择合并音频语言')
                  return
                }
                bindLoading.value = true
                await apiCombineVoice({
                  series_resource_id:  +route.params.id,
                  serial_numbers: checked.value.map(item => item.num),
                  languages: language_codes.value,
                })
                onQuery(+route.params.id)
                hideDialog()
                showSuccessToast('操作成功')
                bindLoading.value = false
              } catch (error: any) {
                bindLoading.value = false
                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
              }
            }}
            >
              {bindLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const getAliTransVideo = () => {
    const lang = ref<string>()
    const saveLoading = ref(false)

    const hideDialog = openDialog({
      title: '阿里云视频翻译',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[400px]',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-4">
          <div class="flex items-center gap-x-2">
            {requiredLabel('翻译语言:')}
            <select class="select select-bordered select-sm" value={lang.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              lang.value = value
            }}
            >
              {
                langKey.map((n, index) => <option value={n}>{langValue[index]}</option>)
              }
            </select>
          </div>
          <div>
            共选中: { checked.value.length }集
          </div>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
              if (!lang.value) {
                showFailToast('请选择翻译语言')
                return
              }
              try {
                saveLoading.value = true
                await apiAliTransVideo({
                  series_resource_id: +route.params.id,
                  serial_numbers: checked.value.map(item => item.num),
                  lang: lang.value,
                })
                onQuery(+route.params.id)
                hideDialog()
                showSuccessToast('操作成功')
                saveLoading.value = false
              } catch (error: any) {
                saveLoading.value = false
                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
              }
            }}
            >
              {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const onUploadAudio = () => {
    const series_resource_id = +route.params.id
    const audio_type = ref< 1 | 2 | 3 | number>(1)
    const tempFiles: File[] = []
    const hasUploadFiles = ref<UploadImage[]>([])
    const btnLoading = ref(false)

    const closeDialog = openDialog({
      title: '导入音频',
      mainClass: '!p-4 !pb-0',
      body: () => (
        <>
          <div class="flex items-center gap-x-2 pb-4">
            {requiredLabel('音频类型:')}
            <select class="select select-bordered select-sm" value={audio_type.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              audio_type.value = +value
            }}
            >
              <option value={1}>原始人声声音</option>
              <option value={2}>原始背景声音</option>
              {/* <option value={3}>特效声音</option> */}
            </select>
          </div>
          <Uploader
            accept="mp4,mp3,wav"
            ossKeyType="resource"
            isImage={false}
            maxsize={1024 * 1024 * 1024 * 2}
            multiple
            beforeUpload={({ files }) => {
              files.forEach(file => tempFiles.push(file))
            }}
            onUploadSuccess={file => {
              if (hasUploadFiles.value.some(f => f?.file?.name === file.file?.name)) return
              hasUploadFiles.value.push(file)
            }}
          >
            <x-uploader-wrapper class="flex h-[120px] w-full cursor-pointer flex-col items-center justify-center  gap-y-2 overflow-hidden rounded-md border border-dashed">
              <p>上传音频</p>
              <p>上传文件需要确保音频文件名和视频对应,支持mp4、mp3、wav文件</p>
            </x-uploader-wrapper>
          </Uploader>
          <Button class="btn btn-primary btn-sm float-right mt-4" disabled={hasUploadFiles.value.length === 0 || tempFiles.length !== hasUploadFiles.value.length || btnLoading.value} onClick={async () => {
            try {
              btnLoading.value = true
              const subtitleInfos = hasUploadFiles.value.map(file => {
                return {
                  file_path: file.temp_path || '',
                  serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
                }
              })
              await apiUploadSaveAudio({
                data: subtitleInfos,
                series_resource_id,
                audio_type: audio_type.value,
              })
              void onQuery(+route.params.id)
              showSuccessToast('保存成功')
              closeDialog()
            } catch (error: any) {
              showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
            } finally {
              btnLoading.value = false
            }
          }}
          >
            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
            保存
          </Button>

        </>
      ),
    })
  }

  const delayTaskGetList = () => {
    setTimeout(() => {
      void onQuery(+route.params.id, false)
    }, 2000)
  }

  onMounted(() => {
    void onQuery(+route.params.id)
    tableHeight.value = document.body.clientHeight - 280
  })

  let interval = null
  interval = setInterval(() => {
    // 没有弹窗的时候才触发刷新
    if (dialogStack.value.length === 0) void onQuery(+route.params.id, false)
  }, 60 * 1000)

  onUnmounted(() => {
    clearInterval(interval)
  })

  return () => (
    <x-timbre-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <section class="breadcrumbs text-sm">
            <ul>
              <li>
                <RouterLink to="/resource-publish/voice-over"
                >配音管理
                </RouterLink>
              </li>
              <li>资源详情</li>
            </ul>
          </section>
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div class="space-x-4">
              <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-sm" onClick={() => {
                eraseAudio()
              }}>分离音视频</Button>
              <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-sm" onClick={() => {
                splitAudio()
              }}>人声和背景音分离</Button>
              <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-sm" onClick={() => {
                downloadAudio()
              }}>下载音视频</Button>
              <Button class="btn btn-primary btn-sm" onClick={() => {
                onUploadAudio()
              }}>导入音频</Button>
              <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-sm" onClick={() => {
                generateAIVoice()
              }}>生成AI配音</Button>
              <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-sm" onClick={() => {
                const closeDialog = openDialog({
                  title: '合成视频',
                  mainClass: 'pb-0 px-5',
                  body: () => (
                    <InjectVideoForm
                      resourceId={+route.params.id}
                      episodeNums={checked.value.map(o => o.num)}
                      onSave={() => {
                        void onQuery(+route.params.id)
                        closeDialog()
                      }}
                      onCancel={() => {
                        closeDialog()
                      }}
                    />
                  ),
                })
              }}>合成视频</Button>
              <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-sm" onClick={() => {
                getAliTransVideo()
              }}>阿里云视频翻译</Button>
              <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-sm" onClick={() => {
                combineAudio()
              }}>合成音频</Button>
              <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-sm" onClick={() => {
                shortAISrt()
              }}>字幕缩短</Button>
              <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-sm" onClick={() => {
                splitOpt()
              }}>背景音优化</Button>
            </div>
            <div class="dropdown dropdown-end dropdown-bottom">
              <div tabindex={0} role="button" class="btn btn-primary btn-xs">
                <SvgIcon class="size-3.5" name="ic_filter" />
              </div>
              <ul tabindex={0} class="z-1 menu dropdown-content w-52 rounded-box bg-base-100 p-2 shadow-sm">
                <ColFilter columns={filterCols.value} selectCols={selectCols.value} onChange={e => {
                  selectCols.value = e
                }} />
              </ul>
            </div>

          </div>
        ),
        table: () => (
          <ElTable
            ref={tableRef}
            v-loading={loading.value}
            show-header
            onSelection-change={handleSelectionChange}
            scrollbar-always-on
            maxHeight={tableHeight.value}
            data={list.value || []}
          >
            <ElTableColumn
              type="selection"
              fixed="left"
              width={50}
            />
            <ElTableColumn
              prop="num"
              label="集数"
              fixed="left"
              width={100}
            />
            {
              columns.value.map(item => {
                const { render, ...props } = item
                return (
                  <ElTableColumn
                    key={item.prop}
                    {...props}
                    v-slots={render ? {
                      default: ({ row }: { row: any }) => render?.({ row }),
                    } : undefined}
                  />
                )
              })
            }

          </ElTable>
        ),
      }}
      </NavFormTablePager>
    </x-timbre-page>
  )
})

export default VoiceoverResourcePage
