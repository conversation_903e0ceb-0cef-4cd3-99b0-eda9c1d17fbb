import { r } from '@skynet/shared'
import { DefaultLayout } from 'src/layout/default-layout'
import { ComponentsPage } from 'src/modules/examples/components-page'
import GetStartedPage from 'src/modules/get-started/get-started-page.md'
import { HomePage } from 'src/modules/home/<USER>'
import InstallationPage from 'src/modules/installation/installation-page.md'
import { RouteRecordRaw, createRouter, createWebHistory } from 'vue-router'

declare module 'vue-router' {
  interface RouteMeta {
    title?: string
  }
}

export const publicRoutes: RouteRecordRaw[] = [
  r('', '首页', HomePage),
  r('/', '前端组件库', DefaultLayout, [
    r('components', '', null, [
      r('', '组件列表', ComponentsPage),
      r('form', '表单', () => import('src/modules/form/form-examples.md')),
      r('table', '表单', () => import('src/modules/table/table-examples.md')),
      r('button', '按钮', () => import('src/modules/button/button-page.md')),
      r('color', '颜色', () => import('src/modules/color/color-page.md')),
      r('icon', '图标', () => import('src/modules/icon/icon-page.md')),
      r('input', '输入框', () => import('src/modules/input-examples/input-examples-page.md')),
      r('form-item', '表单', () => import('src/modules/form-item-examples/form-item-examples.md')),
      r('merge-class', '合并样式', () => import('src/modules/merge-class/merge-class-page.md')),
      r('client-track', '数据上报', () => import('src/modules/client-track/client-track-page.md')),
      r('alert', 'alert', () => import('src/modules/alert/alert-page.md')),
    ]),
    r('get-started', '快速上手', GetStartedPage),
    r('installation', '安装', InstallationPage),
    r('crud', '增删改查', null, [
      r('form', '增删改查', () => import('src/modules/crud/crud-page.tsx')),
    ]),
  ]),
  r('/:pathMatch(.*)*', '页面不存在', <div>404</div>),
]

console.log('publicRoutes: ', publicRoutes)

const router = createRouter({
  history: createWebHistory(),
  routes: publicRoutes,
})

export default router
