import { when } from '@skynet/shared'
import { useI18n } from 'vue-i18n'
import { deviceInfo } from './get-device-info'
import { inApp } from './jsbridge'
import { Ref } from 'vue'

export const useLocale: (language?: string) => { t: (key: string, data?: unknown) => string, locale: Ref<string> } = (language?: string) => {
  const { t, locale } = useI18n()
  const supportedLocales = ['ja', 'en', 'ko', 'es', 'th', 'id', 'vi', 'pt', 'tl', 'it', 'fr', 'de', 'tr', 'ru', 'ms', 'zh', 'in']
  if (inApp && !language) {
    void when(() => deviceInfo).then(() => {
      const localeMap = supportedLocales.map(lang => ({
        appLanguage: lang,
        // 客户端传过来的是in，就是H5的id
        deviceLanguage: lang === 'id' ? 'in' : lang,
      }))
      console.log('deviceInfo', deviceInfo?.language)
      const supportedLocale = localeMap.find(item => item.deviceLanguage === deviceInfo?.language)
      locale.value = supportedLocale?.appLanguage ?? 'en'
    })
  } else {
    const normalizedLanguage = language?.toLowerCase() ?? 'en'
    locale.value = supportedLocales.includes(normalizedLanguage) ? normalizedLanguage : 'en'
  }
  return { t, locale }
}
