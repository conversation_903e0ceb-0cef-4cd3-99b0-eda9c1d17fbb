import { encryptedHttpClient } from 'src/lib/encrypted-http-client'
// import { h5HttpClient } from 'src/lib/http-client'

export const seriesApi = {
  getSeriesItem: (id: string) => encryptedHttpClient.get<Api.Series.GetSeriesItemResponse>(`/h5-api/drama/info`, { series_id: id }).then(res => res.data?.info),
  viewDrama: (data: Api.Series.ViewDramaRequest) => encryptedHttpClient.post<ApiResponse<{ success: boolean }>>(`/h5-api/drama/view`, data),
  follow: (series_id: string) => encryptedHttpClient.post<ApiResponse<{ success: boolean }>>(`/h5-api/drama/follow`, { series_id, is_follow: 1 }),
  unfollow: (series_id: string) => encryptedHttpClient.post<ApiResponse<{ success: boolean }>>(`/h5-api/drama/follow`, { series_id, is_follow: 0 }),
  /**
   * 解锁剧集
   * 错误码：
   * - 1000: 余额不足
   * - 1018: 当前解锁的不是最新未解锁的剧集
   * - 1019: 用户未开启自动解锁
   * - 1035: 用户未开启钻石自动解锁
   * - 1020: 解锁剧集错误
   */
  unlockEpisode: (data: Api.Series.UnlockEpisodeRequest) =>
    encryptedHttpClient.post<Api.Series.UnlockEpisodeResponse>(`/h5-api/drama/unlock_episode`, data),
  manualUnlockEpisode: (data: Api.Series.UnlockEpisodeRequest & { auto_unlock: boolean }) =>
    encryptedHttpClient.post<Api.Series.UnlockEpisodeResponse>(`/h5-api/drama/unlock_episode`, {
      ...data, check_auto_unlock: 0, auto_unlock: data.auto_unlock ? 1 : 0,
    }),
  removeOtherDevice: () => encryptedHttpClient.post<ApiResponse>(`/h5-api/user/device/remove_others`),
}
