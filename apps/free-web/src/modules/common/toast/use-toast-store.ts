import { createCachedFn, createStringId, Key } from '@skynet/shared'
import { Toast, ToastProps } from './toast'
import { h, shallowRef } from 'vue'

export const useToastStore = createCachedFn((_id: Key) => {
  // 由于用的是 shallowRef，所以对 toastList 使用 split、splice 等方法时，不会触发 UI 更新
  // 因此，每次 toastList 变化时，都需要重新赋值
  const toastList = shallowRef<Array<ToastProps>>([])
  const hideList = shallowRef<Array<[string, number]>>([])
  window.setInterval(() => {
    const now = new Date().getTime()
    hideList.value.map(([id, time]) => {
      if (time < now) {
        updateToast(id, { visible: false })
        removeFromHideList(id)
      }
    })
  }, 200)

  const addToast = (props: Omit<ToastProps, 'visible' | 'toastId'>) => {
    const toastId = createStringId('toast')
    toastList.value = [...toastList.value, { ...props, toastId }]
    return toastId
  }
  const updateToast = (id: string, props: Partial<ToastProps>) => {
    const index = toastList.value.findIndex(p => p.toastId === id)
    if (index < 0) return
    toastList.value = [...toastList.value.slice(0, index), {
      ...toastList.value[index],
      ...props,
    }, ...toastList.value.slice(index + 1)]
  }
  const removeToast = (id: string) => {
    toastList.value = toastList.value.filter(p => p.toastId !== id)
  }
  const clearToasts = () => {
    toastList.value = []
  }
  const renderToasts = () => {
    return toastList.value.map(props => (
      h(Toast, {
        key: props.toastId,
        ...props,
        onDestroy: () => removeToast(props.toastId),
      })
    ))
  }
  const addToHideList = (id: string, time: number) => {
    hideList.value.push([id, time])
  }
  const removeFromHideList = (id: string) => {
    const index = hideList.value.findIndex(([itemId]) => itemId === id)
    if (index < 0) return
    hideList.value.splice(index, 1)
  }
  return {
    toastList,
    addToast,
    updateToast,
    removeToast,
    clearToasts,
    renderToasts,
    addToHideList,
    removeFromHideList,
  }
})
