import { createComponent } from '@skynet/shared'
import { Wrapper } from 'src/layouts/wrapper'
import { useRouter, useRoute } from 'vue-router'
import { apiGetScriptManageList, apiGetLabels } from './script-manage-detail-api'
import { onMounted, ref } from 'vue'
import { Tooltip } from '@skynet/ui'
import { openFile } from './util'
import dayjs from 'dayjs'
type ScriptManagePageOptions = {
  props: {}
}
export const ScriptManagePage = createComponent<ScriptManagePageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const route = useRoute()
  const scriptManageDetail = ref<M.IScriptManageDetail>()
  const labels = ref<Api.Label.Item[]>([])
  const showLabels = ref<Api.Label.Item[]>([])
  const loading = ref(false)
  const totalDetail = ref()
  const auditOpinion = ref([])
  onMounted(async () => {
    loading.value = true
    const labelRes = await apiGetLabels({ language_code: 'zh-CN' })
    labels.value = labelRes.data?.list || []
    const res = await apiGetScriptManageList({ play_key: route.params.id as string })
    console.log('res', res)
    scriptManageDetail.value = res.data?.play
    totalDetail.value = res.data
    auditOpinion.value = res.data?.play_audits || []
    showLabels.value = scriptManageDetail.value?.label_ids ? scriptManageDetail.value?.label_ids.split(',')?.map(labelId => labels.value.find(item => item.label_id === Number(labelId))) as Api.Label.Item[] : []
    loading.value = false
  })

  return () => (
    <Wrapper>
      <section class="breadcrumbs text-sm">
        <ul>
          <li class="cursor-pointer" onClick={() => void router.back()}>剧本管理</li>
          <li>剧本详情</li>
        </ul>
      </section>
      <div v-loading={loading.value} class="flex space-x-8 space-y-4 text-center text-base">
        <div class="space-y-4 w-[180px]">
          <div class="w-[180px] h-[260px] rounded-lg bg-gray-200">
            {
              scriptManageDetail.value?.cover ? <img class="w-[180px] h-[260px] object-cover rounded-lg" src={scriptManageDetail.value?.cover} alt="" /> : null
            }
          </div>
          <div class="flex items-center justify-center space-x-2">
            <span class="link link-primary" onClick={() => openFile(scriptManageDetail.value?.play_path || '')}>查看剧本</span>
            {
              totalDetail.value?.show_contract && scriptManageDetail.value?.contract_path ? <span class="link link-primary" onClick={() => openFile(scriptManageDetail.value?.contract_path || '')}>查看合同</span> : null
            }
          </div>
        </div>

        <div class="flex-1 space-y-4 text-left flex-wrap">
          <div class="flex items-center">
            <div class="w-[80px]">剧名：</div>
            <div class="flex-1 px-4 py-2 bg-gray-200 min-h-[40px] rounded-lg break-all">{scriptManageDetail.value?.title}</div>
          </div>
          <div class="flex items-center">
            <div class="w-[80px]">标签：</div>
            <div class="flex-1 flex-wrap min-h-[40px] flex items-center rounded-lg break-all space-x-4">
              {
                showLabels.value.map(obj => (
                  <Tooltip popWrapperClass="!max-w-[400px]" popContent={() => <div>{obj.meaning}</div>}>
                    <div class="badge badge-primary">{obj.content}</div>
                  </Tooltip>
                ))
              }
            </div>
          </div>
          <div class="flex items-start">
            <div class="w-[80px] pt-2">简介：</div>
            <div class="flex-1 px-4 py-2 bg-gray-200 min-h-[40px] rounded-lg break-all">
              { scriptManageDetail.value?.description }
            </div>
          </div>
        </div>
      </div>
      <div class="mt-[20px]">
        <div class="flex">
          <div class="w-[80px]">评估意见：</div>
          <div class="flex-1 px-4 py-2 bg-gray-200 min-h-[40px] rounded-lg break-all">
            {auditOpinion.value.map(item => (
              <div>
                <div>{['','一审','二审','终审'][item.audit_type]}评估意见</div>
                <div class="flex-1 px-4 py-2 bg-gray-200 min-h-[40px] rounded-lg break-all">
                  {item.audit_user}：{['','待审核','审核拒绝','放弃','审核通过'][item.detail.status]} 
                  {
                    item.detail.status > 1 ? <span class="ml-4">{item.detail.rating}。 {item.detail.rejected_reason} {dayjs(item.created*1000).format('YYYY-MM-DD HH:mm:ss')} </span> : null
                  }
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Wrapper>
  )
})

export default ScriptManagePage
