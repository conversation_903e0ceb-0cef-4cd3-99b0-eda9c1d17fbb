<template>
  <x-wallet-page class="wallet relative flex size-full flex-col overflow-y-auto bg-black px-3 text-[#cccacb]">
    <Back :is-white="true" :title="T.walletPage.myWalletStr.ref.value"
      class="z-100 sticky top-0 flex h-11 w-full shrink-0 items-center bg-black font-bold text-[#fdfbfc]" />
    <div
      class="py-4.5 h-37.5 my-5 flex w-full flex-col gap-4 rounded-lg  bg-[url('src/h5_modules/images/wallet-top.webp')] bg-[length:100%_9.375rem] bg-center bg-no-repeat px-5">
      <p class="text-xs text-[#fdfbfc] text-opacity-70">
        {{ T.walletPage.accountBalanceStr.ref.value }}
      </p>
      <div class="text-8 flex items-center font-bold text-[#fdfbfc]">
        <SvgIcon name="coin" class="mr-1 size-6 shrink-0" />{{ myWallet?.total }}
      </div>
      <div class="flex">
        <p class="flex w-[50%] items-center">
          <span class="mr-1 text-base font-bold text-[#fdfbfc]">{{ myWallet?.cash_balance }}</span><span
            class="text-xs text-[#fdfbfc]/70">{{ T.walletPage.coinsText.ref.value }}</span>
        </p>
        <p class="flex w-[50%] items-center">
          <span class="mr-1  text-base font-bold text-[#fdfbfc]">{{ myWallet?.bonus_balance }}</span><span
            class="text-xs text-[#fdfbfc]/70">{{ T.walletPage.rewardCoins.ref.value }}</span>
        </p>
      </div>
    </div>
    <div
      class="mb-5 flex h-11 items-center justify-center rounded-lg	bg-[#fc2763] px-3 text-base font-medium text-white"
      @click="() => {
        h5Track('my_wallet', 'topup', 'click', {
          balance: myWallet?.total
        })
        router.push('/store')
      }">
      {{ T.walletPage.topUp.ref.value }}
    </div>
    <div class="right-arrow py-4 pr-4 text-sm" @click="() => {
      h5Track('my_wallet', 'transactions', 'click')
      router.push('/transaction-history')
    }">
      {{ T.walletPage.transactionHistory.ref.value }}
    </div>
    <div class="right-arrow py-4 pr-4 text-sm" @click="() => {
      h5Track('my_wallet', 'rewards', 'click')
      router.push('/reward-history')
    }">
      {{ T.walletPage.rewardsHistory.ref.value }}
    </div>
    <div class="right-arrow py-4 pr-4 text-sm" @click="() => {
      h5Track('my_wallet', 'consumptions', 'click')
      router.push('/consumption-records')
    }">
      {{ T.walletPage.consumptionRecords.ref.value }}
    </div>
    <van-config-provider theme="dark">
      <van-cell class="lastCell" center :title="T.walletPage.autoUnlockStr.ref.value">
        <template #right-icon>
          <van-switch :model-value="checked" size="22px" @update:model-value="onUpdateValue" />
        </template>
      </van-cell>
    </van-config-provider>
  </x-wallet-page>
</template>
<script setup lang="ts">
import { h5Track } from 'src/lib/h5-track';
import { Back } from 'src/h5_modules/common/back/back'
import { Cell as VanCell, Switch as VanSwitch } from 'vant';
import { onMounted, ref } from 'vue';
import { useMyWallet } from './use-my-wallet';
import { ApiPostAutounlock } from './wallet-api';
import { T } from './wallet-page.i18n';
import { SvgIcon } from '@skynet/ui'
import { useRouter } from 'vue-router';
const checked = ref(false);
const router = useRouter()


const myWallet = ref({
  cash_balance: 0,
  bonus_balance: 0,
  total: 0
})

const onUpdateValue = async (newValue: boolean) => {
  h5Track('my_wallet', 'auto_unlock', 'click', {
    status: checked.value ? 0 : 1
  })
  const res = await ApiPostAutounlock({
    auto_unlock: checked.value ? 0 : 1
  })
  if (res?.code === 200) {
    checked.value = newValue
    if (wallet.value) wallet.value.auto_unlock = newValue ? 1 : 0
  }
}

const { wallet, fetchWallet } = useMyWallet()

const getWalletMy = async () => {
  await fetchWallet()
  if (!wallet.value) return
  checked.value = wallet.value.auto_unlock === 1 ? true : false;
  myWallet.value = {
    total: wallet.value.cash_balance + wallet.value.bonus_balance,
    cash_balance: wallet.value.cash_balance,
    bonus_balance: wallet.value.bonus_balance,
  }
}

onMounted(() => {
  getWalletMy();
  h5Track('my_wallet', 'page', 'show')
})
</script>

<style scoped>
.wallet {
  .lastCell {
    padding-right: 0;
    padding-left: 0;
    color: #cccacb;

  }
}
</style>
