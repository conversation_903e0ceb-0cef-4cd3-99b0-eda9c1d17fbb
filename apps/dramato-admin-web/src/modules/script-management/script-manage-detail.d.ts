declare namespace Api {
  namespace ScriptManageDetail {
    namespace Request {
      interface Params {
        play_key: string
      }
    }
    namespace Response {
      type Detail = ApiResponse<{ play: M.IScriptManageDetail, show_contract: boolean, audit_opinion: any[] }>
    }
  }

  namespace Label {
    namespace Request {
      interface Params {
        language_code: string
      }
    }
    namespace Response {
      type Detail = ApiResponse<{ list: Api.Label.Item[] }>
    }
  }
}

declare namespace M {
  interface IScriptManageDetail {
    play_key: string
    title: string
    description: string
    audit_status: number // 审核状态 1 一审待审核 2 一审拒绝 3 一审放弃 4 一审通过 5 二审待审核 6 二审拒绝 7 二审放弃 8 二审通过 9 三审待审核 10 三审拒绝 11 三审放弃 12 三审通过
    play_path: string
    cover: string
    label_ids: string
    contract_path: string
  }

}
