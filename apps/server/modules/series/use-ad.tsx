import { computed, watch } from 'vue'
import { useSeriesItem } from './use-series-item'
import { useStatistics } from '../statistics/statistics'
import { useGoogleAd } from '../google-ad/use-google-ad'

export function useAd(series_id: string) {
  const { getPlayedCount } = useStatistics()
  const { currentEpisodeId, removeAdAfter, getCurrentEpisode, insertAdAfter, currentAdId, updateActiveEpisodes,
    getIndexById,
  } = useSeriesItem(series_id)
  const { prefetchGoogleAd } = useGoogleAd()
  const currentEpisode = computed(() => getCurrentEpisode())
  /**
   * 加载 Google 广告
   */
  watch(() => getPlayedCount(), length => {
    const index = getIndexById(currentEpisodeId.value)
    if (length !== 0 && length % 3 === 0 && currentEpisodeId.value && index > 0) {
      const episodeId = currentEpisodeId.value
      void prefetchGoogleAd((actionType: string) => {
        if (actionType === 'invalidAd') {
          console.log('移除广告')
          removeAdAfter(episodeId)
        }
      }).then(dom => {
        if (!dom) {
          console.log('没有广告')
          return
        }
        if (!episodeId) return
        console.log('有广告')
        insertAdAfter(episodeId, dom)
      })
    }
  }, { immediate: true })

  watch(() => currentAdId.value, () => {
    updateActiveEpisodes()
  })
}
