import { createComponent, required } from '@skynet/shared'
import { Button, SvgIcon } from '@skynet/ui'
import { RouterLink } from 'vue-router'
import { coverImg } from './images/play-images'
import { useI18n } from 'vue-i18n'
import { i18n } from 'src/locales'
type PlayItemOptions = {
  props: {
    item: M.Play
  }
}
export const PlayItem = createComponent<PlayItemOptions>({
  props: {
    item: required,
  },
}, props => {
  const item = props.item
  const { t } = useI18n()
  return () => (
    <x-play-item
      key={item.play_key}
      class="
        my-3 grid grid-cols-[123px_auto] items-center gap-x-5 rounded-lg bg-[var(--fill-4)] p-5
        grid-areas-['pic_text''status_status''reason_reason']
      ">
      <x-cover class="flex aspect-[123/164] items-center justify-center overflow-hidden rounded-lg border border-gray-200 bg-gray-100 grid-in-[pic]">
        <img src={item.cover || coverImg} class="h-auto w-full" />
      </x-cover>
      <x-detail class="flex flex-col pb-4 grid-in-[text]">
        <h3 class="flex shrink-0 grow-0 items-center gap-2 text-lg font-medium leading-[30px] text-[var(--text-1)]">
          {item.title}
          {item.audit_status === 0 && <span class="rounded-lg bg-[var(--fill-2)] px-2 py-1 text-sm text-[var(--text-3)]">{t('draft')}</span>}
        </h3>
        <p class="mt-2 line-clamp-4 flex-1 overflow-hidden break-all text-sm leading-[22px] text-[var(--text-3)]">{item.description}</p>
        <x-actions class="mt-2 flex shrink-0 grow-0 items-center justify-end gap-3">
          <div class="mr-auto text-sm leading-[22px] text-[var(--text-1)]">{t('estimatedEpisodes')}：{item.count}</div>
          <RouterLink to={`/play/${item.play_key}`}>
            <Button class="btn btn-sm btn-ghost btn-secondary">{t('view')}</Button>
          </RouterLink>
          <RouterLink to={`/play/${item.play_key}`}>
            <Button class="btn btn-sm btn-ghost">{t('modify')}</Button>
          </RouterLink>
        </x-actions>
      </x-detail>
      <x-play-progress class="-mx-[4%] grid-in-[status]">
        <ol class="">
          <li class={[item.audit_status! >= 0 && 'green', item.audit_status === 0 && 'end']}>{t('scriptPreparation')}</li>
          <li class={[item.audit_status! >= 1 && 'green', item.audit_status === 1 && 'end', item.audit_status! === 2 && 'red']}>{t('preliminaryReview')}</li>
          <li class={[item.audit_status! >= 4 && 'green', item.audit_status === 4 && 'end']}>{t('scriptSigning')}</li>
          <li class={[item.audit_status! >= 5 && 'green', item.audit_status === 5 && 'end', item.audit_status! === 6 && 'red']}>{t('secondRoundReview')}</li>
          <li class={[item.audit_status! >= 8 && 'green', item.audit_status === 8 && 'end']}>{t('secondPayment')}</li>
          <li class={[item.audit_status! >= 9 && 'green', item.audit_status === 9 && 'end', item.audit_status! === 10 && 'red']}>{t('finalReview')}</li>
          <li class={[item.audit_status! >= 12 && 'green', item.audit_status === 12 && 'end']}>{t('finished')}</li>
          <li class="">{t('projectApproval')}</li>
        </ol>
      </x-play-progress>
      {(item.audit_failed_reason || item.audit_failed_reason_en) && (
        <x-reason class="rounded-lg bg-[var(--brand-1)] p-3 text-xs text-[var(--brand-6)] grid-in-[reason]">
          <h3 class="flex items-center gap-1"><SvgIcon name="ic-fail-fill" />{t('reviewFeedback')}：</h3>
          <p class="ml-[2em] mt-2">{i18n.global.locale.value == 'en-US' ? (item.audit_failed_reason_en || item.audit_failed_reason) : item.audit_failed_reason}</p>
        </x-reason>
      )}
    </x-play-item>
  )
})

export default PlayItem
