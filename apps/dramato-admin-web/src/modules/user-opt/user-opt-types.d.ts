declare namespace M {
  namespace UserOpt {
    interface Params {
      out_user_id?: number
      in_user_id?: number
      page_info?: {
        offset: number
        size: number
      }
    }

    interface Item {
      created: number
      operate_type: string
      out_user_id: number
      in_user_id: number
      out_user_cash_balance: number
      out_user_bonus_balance: number
      out_user_vip_type: string
      credential_image: string
      create_user: string
    }

    interface Response {
      items: Item[]
      page_info: {
        next: string
        has_more: boolean
      }
      total: number
    }
  }
}
