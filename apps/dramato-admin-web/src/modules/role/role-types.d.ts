declare namespace M {

  interface IRoleUserParams {
    source: 0 | 1 | 2 // 0 所有 1 内部用户 2 外部用户
    // role: number // 0 所有 1 超管 2 普通用户 3 只能查看资源管理，修改字幕 4 外部合作方 6 高级运营 6 初级运营
    username: string
    open_roles: number[]
    page_index?: number
    page_size?: number
  }

  interface IUserRole {
    user_ids: string[]
    role: number
    open_roles: number[]
  }

  interface ISimpleRole {
    id: number
    name: string
  }

  interface IRoleUser {
    user_id: string
    username: string
    role: number
    open_roles: number[]
    source: number
    status: number
    department: string
  }

  interface ICurrentUser {
    users?: IRoleUser[]
    user_id?: string[]
    role?: number
    open_roles?: number[]
  }

  interface IRole {
    role?: number // id
    role_name?: string
    access_urls?: string[] | string
    prefix_access_urls?: string[] | string
    not_access_urls?: string[] | string
    prefix_not_access_urls?: string[] | string
    access_type?: 1 | 0 // 鉴权类型 0 使用可访问列表 1 使用不可访问列表
    status?: 1 | 2 // 状态 1 正常 2 废弃
  }

  interface IRoleQueryParams {
    role_name: string
  }
}
