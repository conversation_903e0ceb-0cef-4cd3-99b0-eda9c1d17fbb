/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc, on } from '@skynet/shared'
import { Button, Checkbox, DateTime, Icon, openDialog, Pager, showAlert, TableColumnOld, Tooltip, transformInteger, transformStringArray } from '@skynet/ui'
import { omit, set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { apiDeleteStrategyGroup, apiGetStrategyGroupAdsDetails, apiListStrategyGroupAds, apiSaveStrategyGroupAds, apiUpdateStrategyGroupAdsState } from 'src/modules/strategy-group-ad/strategy-group-ad-api'
import { computed, onMounted, ref, watch } from 'vue'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { useStrategyGroupAdStore } from './strategy-group-ad-store'
import { useListParamsStore } from './use-list-params-store'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
type StrategyGroupAdPageOptions = {
  props: {
    checkedItem: number[] // 选中的项
    showCheckbox: boolean // 是否显示复选框
    showColumns: string[] // 显示的列
  }
  emits: {
    add: (data: M.StrategyGroup) => void
    remove: (data: M.StrategyGroup) => void
  }
}

const initListParams = {
  strategy_id: '',
  status: undefined,
  pay_mode: '',
  platform: '',
  sort: {
    created: 'DESC',
    id: 'DESC',
  },
}
export const StrategyGroupAdPage = createComponent<StrategyGroupAdPageOptions>({
  props: {
    checkedItem: [],
    showCheckbox: false,
    showColumns: [],
  },
  emits: {
    add: fn,
    remove: fn,
  },
}, (props, { emit }) => {
  const { Form, listParams, Table, list } = useListParamsStore()

  const { resetFormData } = useStrategyGroupAdStore()
  const route = useRoute()
  const router = useRouter()

  onMounted(() => {
    void search(1)
    on('refresh:strategyGroup', () => {
      void search(1)
    })
  })
  const loading = ref<boolean>(false)
  const search = async (_page?: number) => {
    _page = _page || page.value + 1
    const sort = {}
    if (listParams.value.sortType) {
      const k = listParams.value.sortType.split('-')[0]
      const v = listParams.value.sortType.split('-')[1]
      set(sort, k, v)
    }
    loading.value = true
    const res = await apiListStrategyGroupAds({ ...listParams.value, sort, page_info: { offset: (_page - 1) * pageSize.value, size: pageSize.value }, pay_mode: 'IAP' })
      .finally(() => {
        loading.value = false
      })
    list.value = res.data?.list || []
    total.value = res.data?.page_info.total || 0
    page.value = _page
  }

  const isIAP = computed(() => route.params.tabName === 'iap')

  const copyBtn = async (row: M.StrategyGroup) => {
    try {
      const rs = await apiGetStrategyGroupAdsDetails({ id: row.id + '' })
      if (!rs.data) {
        return
      }
      let d: M.StrategyGroup & { save_type: 1 | 2 } = { ...rs.data, save_type: 1 }
      d = {
        ...omit(d, ['id', 'status']),
        save_type: 1,
        name: d.name + '副本',
      }
      await apiSaveStrategyGroupAds(d)
      showAlert('复制成功')
      void search(page.value)
    } catch (error: any) {
      showAlert(error.response.data.message || error.response.data.err_msg || '复制失败')
    }
  }

  watch(() => listParams.value.sort, () => {
    void search(1)
  }, { deep: true })

  watch(() => router.currentRoute.value, () => {
    void search(1)
  }, { deep: true })

  const page = ref<number>(0)
  const pageSize = ref<number>(20)
  const total = ref<number>(1)

  const {
    list: strategyLayerList,
    ...other
  } = useUserStrategyLayer()

  const formatScene = (scene: string[]) => {
    if (!scene || !scene.length) {
      return ''
    }
    const resultArr = []
    for (const item of scene) {
      resultArr.push([
        { label: '开屏', value: 'app_open' },
        { label: '面板前', value: 'drama_free' },
        { label: '面板后', value: 'drama_lock' },
        { label: 'reward', value: 'reward' },
        { label: '退出播放器', value: 'quit_player' },
      ].find(i => i.value === item)?.label || '')
    }
    const result = resultArr.join('，')
    return result
  }

  const columns: TableColumnOld<M.StrategyGroup>[] = [
    [
      '',
      row => {
        const id = Number(row.id || '')
        return (
          <x-show-when-in-dialog>
            <Checkbox
              label=""
              disabled={!!!id}
              modelValue={props.checkedItem.includes(id)}
              onUpdate:modelValue={(value: unknown) => {
                if (value) {
                  if (!props.checkedItem.includes(id)) {
                    const found = list.value.find(i => Number(i.id || '') === id)
                    if (!found) return
                    emit('add', found)
                  }
                } else {
                  const rowIndex = props.checkedItem.indexOf(id)
                  if (rowIndex !== -1) {
                    emit('remove', row)
                  }
                }
              }}
            />
          </x-show-when-in-dialog>

        )
      },
      { class: mc('w-[30px]', props.showCheckbox ? '' : 'hidden') },
    ],
    ['策略组ID', 'id', { class: mc('w-[100px]', !props.showCheckbox || props.showColumns.includes('id') ? '' : 'hidden') }],
    ['策略组名称', 'name', { class: mc('w-[200px]', !props.showCheckbox || props.showColumns.includes('name') ? '' : 'hidden') }],
    ['用户分层名称', 'strategy_layer_names', { class: mc('w-[200px]', !props.showCheckbox || props.showColumns.includes('strategy_layer_names') ? '' : 'hidden') }],
    ['AB Test', row => (<span>{row.is_abtest ? 'Yes' : 'No'} </span>), { class: mc('w-[100px]', !props.showCheckbox || props.showColumns.includes('is_abtest') ? '' : 'hidden') }],
    ['广告位', row => (
      <span class="">
        {formatScene(row.scene || [])}
      </span>
    ), { class: mc('w-[200px]', !props.showCheckbox || props.showColumns.includes('scene') ? '' : 'hidden') }],
    ['端', 'platform', { class: mc('w-[100px]', !props.showCheckbox || props.showColumns.includes('platform') ? '' : 'hidden') }],
    [<Tooltip popContent={() => '权重越大越优先'}><span class="flex items-center gap-1">权重<Icon name="material-symbols:info" /></span></Tooltip>, 'priority', { class: mc('w-[100px]', !props.showCheckbox || props.showColumns.includes('priority') ? '' : 'hidden') }],
    ['上架状态', row => (
      <span class="badge badge-outline whitespace-nowrap">{[
        { label: '线上', value: 1 },
        { label: '草稿', value: 2 },
        { label: '未上架', value: 3 },
      ].find(item => item.value === row.status)?.label ?? row.status}
      </span>
    ), { class: mc('w-[100px]', !props.showCheckbox || props.showColumns.includes('status') ? '' : 'hidden') }],
    ['创建时间', row => (<DateTime value={(row?.created || 0) * 1000} />), { class: mc('w-[150px]', !props.showCheckbox || props.showColumns.includes('created') ? '' : 'hidden') }],
    ['创建人', 'created_operator_name', { class: mc('w-[150px]', !props.showCheckbox || props.showColumns.includes('created_operator_name') ? '' : 'hidden') }],
    ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: mc('w-[150px]', !props.showCheckbox || props.showColumns.includes('updated') ? '' : 'hidden') }],
    ['更新人', 'updated_operator_name', { class: mc('w-[150px]', !props.showCheckbox || props.showColumns.includes('updated_operator_name') ? '' : 'hidden') }],
    [<span class="px-3">操作</span>, row => (
      <div class="flex gap-x-2">
        <Button
          class={mc('btn btn-outline btn-xs', row.status === 2 ? 'hidden' : '')}
          onClick={() => {
            if (!row.id) return
            void apiUpdateStrategyGroupAdsState(
              {
                id: row.id,
                operation: row.status === 3 ? 1 : 3,
              },
            )
              .then(() => {
                void search(page.value)
              })
              .catch((error: any) => {
                showAlert(error.response.data.message, 'error')
              })
          }}
        >
          {row.status === 3 ? '上架' : '下架'}
        </Button>
        <RouterLink to={`/ad-strategy-group/${route.params.tabName as string}/create?id=${row.id}`} class={mc('btn btn-outline btn-xs', [2, 3].includes(row.status || 0) ? '' : 'hidden')}>修改</RouterLink>
        <RouterLink to={`/ad-strategy-group/${route.params.tabName as string}/create?id=${row.id}&mode=view`} class={mc('btn btn-outline btn-xs', row.status === 1 ? '' : 'hidden')}>查看</RouterLink>
        <Button
          class="btn btn-outline btn-xs"
          onClick={() => {
            const hideDeleteDialog = openDialog({
              title: '删除',
              mainClass: 'pb-0 px-5',
              body: (
                <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                  <x-delete-episode-body>确认删除 策略【{row.name}】吗？</x-delete-episode-body>
                  <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                    <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                    <button class="btn btn-primary btn-sm" onClick={() => {
                      void apiDeleteStrategyGroup({
                        ids: [+(row.id || '')],
                      })
                        .then(() => {
                          void search(page.value)
                          hideDeleteDialog()
                        })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                    >确定
                    </button>
                  </x-delete-episode-footer>
                </x-delete-episode-confirm-dialog>
              ),
            })
          }}
        >
          删除
        </Button>
        <Button class="btn btn-outline btn-xs" onClick={() => copyBtn(row)}>
          复制
        </Button>
      </div>
    ), { class: mc('w-[240px] hide-when-in-dialog', props.showCheckbox ? 'hidden' : '') }],
  ]

  onMounted(() => {
    other.page.value = 1
    other.pageSize.value = 9999
    void other.search(other.page.value)
  })
  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <x-nav class="space-y-2">
            <x-hide-when-in-dialog>
              <ul>
                <li>策略组列表</li>
              </ul>
            </x-hide-when-in-dialog>
          </x-nav>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              listParams.value = { ...initListParams }
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)} data={listParams.value} items={[
              ['策略ID', 'strategy_id', { type: 'text' }],
              [
                '分层画像',
                'strategy_layer_id',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: strategyLayerList.value.map((n, index) => {
                    return { value: n.id, label: `${n.id}/${n.name}` }
                  }),
                  maxlength: 1,
                },
                {
                  transform: [
                    (raw?: unknown) => raw ? [+raw] : [],
                    (display: string[]): number => display[0] ? +(display[0] || '') : 0,
                  ] as const,
                },
              ],
              ['状态', 'status', {
                type: 'select',
                options: [{ label: '启用', value: 1 }, { label: '未启用', value: 3 }],
              }, { transform: transformInteger }],
              ['排序', 'sortType', {
                type: 'select',
                options: [
                  { label: '创建时间：从新到旧', value: 'created-DESC' },
                  { label: '创建时间：从旧到新', value: 'created-ASC' },
                  { label: 'ID：从大到小', value: 'id-DESC' },
                  { label: 'ID：从小到大', value: 'id-ASC' },
                  { label: '优先级：从大到小', value: 'priority-DESC' },
                  { label: '优先级：从小到大', value: 'priority-ASC' },
                  // { label: '冻结', value: 4 },
                ],
              }],
              ['AB Test', 'is_abtest', {
                type: 'select',
                options: [
                  { label: '是', value: 1 },
                  { label: '否', value: 0 },
                ],
              }, { transform: transformInteger }],
              {
                label: '端',
                path: 'platform',
                input: {
                  type: 'select',
                  class: 'w-[100px]',
                  options: [
                    { label: 'iOS', value: 'ios' },
                    { label: 'Android', value: 'android' },
                  ],
                },
              },
              ['广告位', 'scene', {
                type: 'multi-select',
                options: [
                  { label: '开屏', value: 'app_open' },
                  { label: '面板前', value: 'drama_free' },
                  { label: '面板后', value: 'drama_lock' },
                  { label: 'reward', value: 'reward' },
                  { label: '退出播放器', value: 'quit_player' },
                ],
              }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              广告策略组
            </div>
            {!!props.showCheckbox || (
              <x-hide-when-in-dialog class="flex items-center gap-2">
                <Button class="btn btn-primary btn-sm" onClick={
                  () => {
                    resetFormData()
                    void router.push(`/ad-strategy-group/${route.params.tabName as string}/create`)
                  }
                }
                >{isIAP.value ? '新建方案' : '新建策略组'}
                </Button>
              </x-hide-when-in-dialog>
            )}
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} class="tm-table-fix-last-column" columns={columns} />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default StrategyGroupAdPage
