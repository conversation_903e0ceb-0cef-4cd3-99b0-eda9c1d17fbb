import { ref } from 'vue'
import { apiGetBroadcast } from '../coins-api'
import { bindLoading, when } from '@skynet/shared'

export const useBroadcast = () => {
  const broadcastPool = ref<string[]>([])

  // 轮播broadcast，每3秒轮播一次，每次轮播一个，如果播放到倒数第二个，则请求接口获取新的broadcast
  const broadcast = () => {
    if (broadcastPool.value && broadcastPool.value.length > 0) {
      const broadcast = broadcastPool.value.shift()
      if (broadcastPool.value.length === 1) {
        // 请求接口获取新的broadcast
        void getBroadcast()
      }
      return broadcast
    }
    return ''
  }
  const gettingBroadcast = ref(false)

  const getBroadcast = async () => {
    const res = await bindLoading(apiGetBroadcast(), gettingBroadcast)
    if (!res?.data) return
    broadcastPool.value = [...broadcastPool.value, ...res?.data?.list.flat()]
  }

  return {
    broadcastPool,
    broadcast,
    getBroadcast,
    gettingBroadcast,
  }
}
