import { bindLoading } from '@skynet/shared/promise-helper'
import { ref } from 'vue'
import { serverHttpClient } from '../common/server-http-client'

export const novelShareCache = useStorage<{ value: M.NovelShare, createdAt: number }>('novel-share-cache')

export function useNovelShare(novel_key: string) {
  const novel = ref<M.NovelShare | null>(null)
  const fetchingNovel = ref(false)
  return {
    novel,
    fetchNovel,
  }
  async function fetchNovel() {
    // 检查缓存是否存在且未超过6小时
    const cache = novelShareCache.value
    if (cache && cache.createdAt && cache.value) {
      const cacheAge = Date.now() - cache.createdAt
      const sixHoursInMs = 6 * 60 * 60 * 1000 // 6小时的毫秒数

      if (cacheAge < sixHoursInMs) {
        // 缓存未过期，直接使用缓存
        novel.value = cache.value
        return cache.value
      }
    }

    // 缓存不存在或已过期，发起请求
    serverHttpClient.printCurl(
      { url: `/dm-api/novel/share/info?novel_key=${novel_key}`, method: 'get' }
    )
    const res = await bindLoading(serverHttpClient.request<ApiResponse<{ novel: M.NovelShare }>>(
      { url: `/dm-api/novel/share/info?novel_key=${novel_key}`, method: 'get' }
    ), fetchingNovel)
    const v = res.data?.novel ?? null

    // 更新缓存
    if (v) {
      novelShareCache.value = {
        value: v,
        createdAt: Date.now()
      }
    }

    novel.value = v
    return v
  }
}
