import { CreateForm, CreateTableOld } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetUserFeedbackList, apiGetProblemTypes } from './user-feedback-api'

export const useUserFeedback = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    problemTypes,
    getProblemTypes,
    orderStatusOptions
  }
}

const Form = CreateForm<M.UserFeedback.Params>()
const params = ref<M.UserFeedback.Params>({
  problem_type: 0,
  series_key: '',
  episode_key: '',
  resource_ids: ''
})

const Table = CreateTableOld<M.UserFeedback.Item>()
const list = ref<M.UserFeedback.Item[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(1)

const problemTypes = ref<M.UserFeedback.ProblemType[]>([])

const search = async (_page?: number) => {
  _page = _page || page.value + 1
  loading.value = true
  const res = await apiGetUserFeedbackList({
    ...params.value,
    page_info: {
      offset: (_page - 1) * pageSize.value, size: pageSize.value } })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.page_info.total || 0
  page.value = _page
}

const getProblemTypes = async () => {
  const res = await apiGetProblemTypes()
  problemTypes.value = res.data?.list || []
}

const orderStatusOptions = [
  {
    value: 0,
    label: '待回复',
  }, {
    value: 1,
    label: '处理中',
  }, {
    value: 2,
    label: '已关闭',
  }
]
