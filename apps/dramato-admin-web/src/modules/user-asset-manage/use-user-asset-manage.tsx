import { CreateForm, showFailToast } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetUserAssetQuery } from './user-asset-manage-api'
import router from 'src/router.tsx'
export const useUserAssetManage = () => {
  return {
    Form,
    FormOne,
    params,
    search,
    loading,
    userAssetForm,
    assetMsgList,
    selectedAssetMsg,
    btnList,
    onAssetTransfers,
    transferResult,
  }
}
const Form = CreateForm<M.UserAssetManage.Params>()
const FormOne = CreateForm<M.UserAssetManage.Response>()
const params = ref<M.UserAssetManage.Params>({ old_user_id: undefined, new_user_id: undefined, is_single_user_query: 0 })
const userAssetForm = ref<M.UserAssetManage.Response>({})
const selectedAssetMsg = ref<M.UserAssetManage.UserAssetTransfer.Params>({ transfer_asset_type_list: [] })
const transferResult = ref<M.UserAssetManage.UserAssetTransfer.Response>({})
const assetMsgList = ref([{ name: '订阅权益', value: 'Subscription' }, { name: '金币', value: 'Coins' }, { name: '奖励币', value: 'Bonus&Rewards' }, { name: '兑换劵', value: 'Coupon' }, { name: '已解锁的剧集', value: 'UnlockedEpisode' }])
const loading = ref<boolean>(false)

const search = async () => {
  if (params.value.new_user_id && params.value.old_user_id) {
    loading.value = true
    const res = await apiGetUserAssetQuery({ ...params.value })
      .finally(() => {
        loading.value = false
      }).catch(() => {
        showFailToast('网络出错～')
      })
    userAssetForm.value = res?.data || {}
  } else {
    showFailToast('请先输入新老用户ID哦～')
  }
}

const onAssetTransfers = () => {
  if (selectedAssetMsg.value.transfer_asset_type_list!.length > 0) {
    console.log(selectedAssetMsg.value)
    void router.push('/asset-manage-detail')
  } else {
    showFailToast('请选择要转移的资产哦～')
  }
}

const btnList = ref<M.IActivityButton[]>([])

const setBtnList = (list: M.IActivityButton[]) => {
  btnList.value = list
}

const addBtnToList = (btn: M.IActivityButton) => {
  btnList.value.push(btn)
}

const updateBtnList = (index: number, btn: M.IActivityButton) => {
  btnList.value.splice(index, 1, btn)
}
