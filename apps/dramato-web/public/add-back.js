(function () {
  document.addEventListener('DOMContentLoaded', (e) => {
    if (typeof window.dwJsHandler !== 'undefined' || typeof window.flutter_inappwebview?.callHandler !== 'undefined') {
      return
    }
    const back = document.createElement('div')
    back.style.position = 'fixed'
    back.style.top = '0px'
    back.style.left = '0px'
    back.style.padding = '0 12px'
    back.style.width = 'calc(100% - 24px)'
    back.style.height = '44px'
    back.style.zIndex = '1000'
    back.style.color = 'white'
    back.style.backgroundColor = '#0b080b'
    back.style.display = 'flex'
    back.style.alignItems = 'center'
    back.style.justifyContent = 'space-between'
    back.appendChild(document.createElement('img'))
    back.querySelector('img').src = '/back.svg'
    back.querySelector('img').style.width = '20px'
    back.querySelector('img').style.height = '20px'
    back.querySelector('img').style.position = 'relative'
    back.querySelector('img').style.zIndex = '1001'
    back.appendChild(document.createElement('div'))
    back.querySelector('div').style.position = 'absolute'
    back.querySelector('div').style.zIndex = '1000'
    back.querySelector('div').style.top = '0px'
    back.querySelector('div').style.left = '0px'
    back.querySelector('div').style.width = '100%'
    back.querySelector('div').style.height = '100%'
    back.querySelector('div').style.color = '#fdfbfc'
    back.querySelector('div').style.fontSize = '16px'
    back.querySelector('div').style.display = 'flex'
    back.querySelector('div').style.alignItems = 'center'
    back.querySelector('div').style.justifyContent = 'center'
    back.querySelector('div').innerHTML = document.title
    back.querySelector('img').addEventListener('click', () => {
      window.history.back()
    })
    document.body.prepend(back)
    document.body.style.paddingTop = '44px'
    document.body.querySelector('#output').style.padding = '12px 16px 40px 16px'
  })
})()
