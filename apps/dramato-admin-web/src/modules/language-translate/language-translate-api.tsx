import { httpClient } from 'src/lib/http-client'

export const apiGetList = (data: M.LanguageTranslate.Params) =>
  httpClient.get<M.LanguageTranslate.Response>('/translation/project/list', data)

export const apiCreateProject = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/project/update', data)

export const apiDeleteProject = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/project/delete', data)

export const apiGetAdminUserList = () =>
  httpClient.get<M.LanguageTranslate.Response>('/translation/admin/list')

export const apiGetProjectDetail = (data: M.LanguageTranslate.Params) =>
  httpClient.get<M.LanguageTranslate.Response>('/translation/project/detail', data)

export const apiGetProjectTaskList = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/task/list', data)

export const apiGetLanguageList = (data: M.LanguageTranslate.Params) =>
  httpClient.get<M.LanguageTranslate.Response>('/translation/language', data)

export const apiCheckKeyname = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/key_name/check', data)

export const apiUpdateTask = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/task/update', data)

export const apiGetTaskDetail = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/task/detail', data)

export const apiGetTaskOption = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/task/option', data)

export const apiExportTaskExcel = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/task/excel/export', data)

export const apiGetTaskExcelModel = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/task/excel/model', data)

export const apiGetKeyList = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/key/list', data)

export const apiSearchKeyname = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/key_name/search', data)

export const apiSaveTranslate = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/task/save', data)

export const apiImportTaskExcel = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/task/excel/import', data)

export const apiGetMainKeyDetail = (data: M.LanguageTranslate.Params) =>
  httpClient.post<M.LanguageTranslate.Response>('/translation/key/detail', data)
