export const de = {
  // 通用
  common: {
    cancel: 'Abbrechen',
  },
  // 分享页
  share: {
    watchNow: 'Jetzt anschauen',
    episodeDialog: {
      title: 'Schl<PERSON>ße dich mir an und sichere dir zusätzliche Münzen!',
      description: 'Ich bin süchtig nach dieser Serie!',
      tips: 'Empfehlung',
      button: '<PERSON><PERSON> sich das ganze Drama an',
      limit24: 'Begrenzte Zeit: 24h',
    },
    toast: {
      takeOffShelf: 'Kein Inhalt verfügbar',
    },
  },
  'customer-service-center': {
    title: 'Kundendienstzentrum',
    description: 'Beschreibung',
    'description-placeholder': 'Geben Sie Einzelheiten an, damit wir das Problem besser verstehen',
    'email-placeholder': 'Geben Sie Ihre E-Mail-Adresse ein, um Feedback-Updates zu erhalten',
    'submit-feedback': 'Senden',
    'enter-feedback': 'Feedback zu anderen Problemen',
    'upload-pictures': 'Bilder hochladen',
    feedback: 'Feedback',
    faq: {
      question1: 'Warum steht da „Werbung noch nicht bereit“?',
      answer1: 'Manchmal können instabile Server- oder Werbetreibendenverbindungen Probleme verursachen. Wir empfehlen, es später noch einmal zu versuchen und nicht zu oft auf „Werbung ansehen“ zu klicken. Aufgrund von Richtlinien Dritter oder den Einschränkungen Ihres Landes können Werbegrenzen gelten. Wir arbeiten ständig mit unseren Werbeanbietern zusammen, um Ihre Erfahrung beim Ansehen von Werbung zu verbessern.',
      question2: 'Warum steht da „Netzwerk nicht verfügbar“ für Werbung?',
      answer2: 'Bitte versuchen Sie, die App neu zu starten, indem Sie sie vollständig schließen und dann erneut öffnen. Wenn die Werbung weiterhin nicht geladen wird, warten Sie eine Weile, bevor Sie es erneut versuchen, und vermeiden Sie häufiges Klicken. Wenn das Problem weiterhin besteht, können Sie über unser Feedback-System Kontakt aufnehmen. Wir arbeiten auch mit unserem Werbeteam zusammen, um das Seherlebnis zu verbessern. Vielen Dank für Ihre Geduld.',
    },
  },
  toast: {
    'upload-failed': 'Hochladen fehlgeschlagen, bitte erneut hochladen',
    'submit-success': 'Erfolgreich gesendet',
    'email-incorrect': 'Das E-Mail-Format ist falsch',
  },
  not_found: {
    title: 'Die von Ihnen gesuchte Seite existiert nicht',
  },
  regulations: {
    howUseData: 'Wie wir Ihre Daten verwenden',
    content: `Wenn Sie bei der App-Tracking-Transparenz-Berechtigungsanfrage "Erlauben" auswählen, werden wir Ihre Informationen und Daten verwenden, um unsere Dienste zu optimieren und Ihnen die beste Erfahrung zu bieten. Andernfalls werden wir nur Daten sammeln und keine Tracking-Aktivitäten durchführen.

Wenn Sie damit einverstanden sind, dass DramaWave und unsere Partner und Lieferanten Daten von Ihrem Gerät verwenden, um zu analysieren, wie Sie die Anwendung nutzen, Inhaltsempfehlungen zu personalisieren und weitere informationsbezogene Dienste bereitzustellen, klicken Sie bitte auf "Zustimmen und fortfahren", um fortzufahren.`,
    acceptAll: 'Alle akzeptieren',
    dataSettings: 'Daten Einstellungen',
    next: 'Weiter',
    confirm: 'Bestätigen',
    aboutYourPrivacy: 'Über Ihre Privatsphäre',
    personalDataName: 'Personalisierte Daten',
    personalDataDescription: 'Wir verwenden diese Informationen, um Ihre Auswahl zu speichern und personalisierte Werbung sowie Inhaltsempfehlungen, Benachrichtigungen und Erinnerungen bereitzustellen.',
    advertisementDataName: 'Werbedaten',
    advertisementDataDescription: 'Cookies, Kennungen Ihres Geräts oder andere Informationen können für Online-Werbezwecke auf Ihrem Gerät gespeichert oder abgerufen werden. Benutzerdaten können auch mit unseren Werbepartnern geteilt werden.',
    behaviorsDataName: 'Verhaltensdaten',
    behaviorsDataDescription: 'Analysebezogene Daten wie Cookies oder Gerätekennungen können gespeichert werden, um Nutzung, Verhalten und Werbeleistung zu messen und zu analysieren.',
    privacyAndTerms: 'Datenschutzrichtlinie und Nutzungsbedingungen',
    referToPrivacy: 'Bitte lesen Sie die Datenschutzrichtlinie',
    referToTerms: 'Bitte lesen Sie die Nutzungsbedingungen',
    agreeAndSave: 'Zustimmen & Speichern',
  },
  coins: {
    earnings: {
      title: 'Meine Einnahmen',
      total: 'Gesamtzahl der Münzen',
      exchange: 'Austausch',
      earningsHistory: 'Einnahmeverlauf',
      exchangeHistory: 'Austauschverlauf',
      noRecords: 'Kein Datensatz',
      last50earnings: 'Es werden nur die letzten 50 Einnahmen angezeigt',
      last50exchanges: 'Es werden nur die letzten 50 Börsendatensätze angezeigt',
      benefits: 'Vorteile',
      gotIt: 'Verstanden',
    },
    exchange: {
      title: 'Austauschzone',
      notOpen: 'Noch nicht geöffnet, bitte bleiben Sie dran',
      receiveBtn: 'Freuen Sie sich, es zu erhalten',
      earnCoins: 'Münzen verdienen',
      redeemRequiredXCoins: 'Erforderlich {X} Coins einlösen',
      exchangeSuccess: 'Tausch erfolgreich',
      cardId: 'KARTEN-ID',
      exchangeFailed: 'Tausch fehlgeschlagen',
      transferFailedInsufficientStock: 'Ausverkauft. Bitte kommen Sie morgen früh wieder.',
      gotItBtn: 'Verstanden',
      transferFailedRiskControl: 'Ungewöhnliches Konto, vorübergehend nicht möglich zu tauschen.',
      tryAgainBtn: 'Bitte später noch einmal versuchen',
      transferConfirm: 'Überweisung bestätigen',
      paypalAccountEmail: 'Paypal-Konto-E-Mail',
      confirmPaypalAccountEmail: 'Paypal-Konto-E-Mail bestätigen',
      transferX: '{X} überweisen',
      transferXConfirm: '{X} Überweisung bestätigen',
      transferToYourAccountX: 'Überweisung auf Ihr Konto {X}',
      confirmBtn: 'Bestätigen',
      transferSuccessTitle: 'Wir senden das Geld an Ihr PayPal-Konto!',
      transferSuccessContentX: 'Ihr Geld wird innerhalb von 5 Tagen auf das Konto von {X} ankommen.',
      copySuccess: 'Kopieren erfolgreich',
      enterEmailError: 'Bitte geben Sie eine gültige E-Mail-Adresse ein',
      enterEmailValidError: 'Eingabefehler, bitte erneut eingeben',
      emailNotMatchError: 'Die E-Mail-Adresse stimmt nicht überein, bitte erneut eingeben',
    },
  },
  my_wallet: {
    title: 'Brieftasche',
    help_dialog: {
      title: 'Ticket-Nutzungsregeln',
      content1: `1. 1 Gekauftes Ticket = 1 Belohnungsticket beim Kauf = 1 Belohnungsticket durch Werbung = 1 Münze-Tausch-Ticket – Alle diese können verwendet werden, um Episoden freizuschalten.`,
      content2: `2. Alle Tickets gewähren dauerhaftes Ansehen von Episoden, aber die Purchased Reward Tickets verfallen nach einer bestimmten Zeit.`,
      content3: `3. Gekaufte Tickets können durch direkten Kauf erworben werden.`,
      content4: `4. Belohnungstickets beim Kauf können nur während spezieller Aktionszeiträume durch Kauf erhalten werden.`,
      content5: `5. Belohnungstickets durch Werbung können durch das Abschließen von Werbeaufgaben verdient werden.`,
      content6: `6. Münze-Tausch-Tickets können durch den Tausch von Münzen erhalten werden.`,
    },
    account_balance: 'Kontostand',
    purchased_tickets: 'Gekaufte Tickets',
    purchase_reward_tickets: 'Belohnungstickets beim Kauf',
    coin_exchange_tickets: 'Münze-Tausch-Tickets',
    ad_reward_tickets: 'Belohnungstickets durch Werbung',
    top_up: 'Aufladen',
    purchase_history: 'Kaufhistorie',
    rewards_history: 'Belohnungshistorie',
    exchange_history: 'Tauschhistorie',
    consumption_records: 'Verbrauchsaufzeichnungen',
    auto_unlock: 'Folgen auf Auto-Freischaltung',
  },
  top_up: {
    exchange_title: 'Münze-Tausch',
    purchase_title: 'Kauf',
    tickets: 'Tickets',
    coins: 'Münzen',
    balance: 'Guthaben',
    title: 'Shop',
    tips: 'Tipps',
    tips_1: '1. Unsere Plattform bietet sowohl kostenlose Kurzdramen als auch Premium-Dramen mit frühem Zugang.',
    tips_2: '2. Um Inhalte mit frühem Zugang im Voraus zu sehen, müssen Sie Tickets einlösen. Diese Tickets können durch Kauf, Münzen-Tausch oder durch das Ansehen von Werbeanzeigen erhalten werden.',
    tips_3: '3. Alle Tickets gewähren dauerhaftes Ansehen von Episoden, aber die Purchased Reward Tickets verfallen nach einer bestimmten Zeit.',
    tips_4: '4. Belohnungstickets beim Kauf werden zuerst verwendet, wenn Episoden freigeschaltet werden. Wenn das Guthaben nicht ausreicht, werden automatisch Ihre regulären Tickets verwendet.',
    tips_5: '5. Wenn Ihre Aufladezahlung erfolgreich war, aber Ihr Guthaben nicht aktualisiert wurde, versuchen Sie bitte, Ihre Geldbörse zu aktualisieren.',
    tips_6: '6. Bei weiteren Fragen können Sie uns gerne über „Profil“ -> „Kundendienstzentrum“ -> „Feedback“ kontaktieren.',
    exchange_dialog_title: 'Sind Sie sicher, dass Sie umtauschen möchten?',
    redeem_required: 'Einlösen erforderlich',
    exchange_success: 'xxx billets échangés avec succès',
    balance_not_enough: 'Solde insuffisant',
    exchange_failed: 'Échange échoué',
  },
  my_wallet_in: {
    transaction_history: 'Transaktionsverlauf',
    EPnum: 'EP.{num}',
    Expired: 'Ablaufen',
    AD: 'Werbung',
    Expires_on_num: 'Läuft ab am {num}',
    Thats_all_for_now: 'Das ist alles',
    consumption_records: 'Verbrauchsaufzeichnungen',
  },
  meal_check_in: {
    sleeping_link: 'Verdienen Sie Münzen, während Sie schlafen',
    rules_title: 'Aktivitätsregeln',
    benefits_title: 'Benefits',
    rules_1: '1. Jeden Tag gibt es vier Zeitfenster, in denen Sie Ihre Mahlzeiten einchecken können: Frühstück: 07:00-10:00; Mittagessen: 11:00-14:00; Abendessen: 17:00-20:00; Mitternachtssnack: 21:00-24:00.',
    rules_2: '2. Wenn Sie die entsprechende Zeit verpasst haben, können Sie ein Video ansehen, um Ihre Belohnung nachzuholen.',
    rules_3: '3. Die Belohnungen werden jeden Tag um Mitternacht zurückgesetzt, also vergessen Sie nicht, Ihre Belohnung abzuholen.',
  },
  sleep_check_in: {
    earn_link: 'Mahlzeiten aufzeichnen, um zu verdienen',
    benefits_title: 'Benefits',
    rules_1: '1. Sie können jeden Abend von 19:00 bis 24:00 den \'Schlafen um zu verdienen\'-Modus aktivieren.',
    rules_2: '2. Nachdem Sie den Modus aktiviert haben, können Sie am nächsten Tag nach 8 Stunden Schlaf zwischen 8:00 und 12:00 die Schlafbelohnung beanspruchen.',
    rules_3: '3. Wenn Sie die Zeit zum Beanspruchen der Belohnung verpassen, können Sie am nächsten Tag zwischen 12:00 und 19:00 ein Video ansehen, um die Belohnung erneut zu beanspruchen.',
  },
}
