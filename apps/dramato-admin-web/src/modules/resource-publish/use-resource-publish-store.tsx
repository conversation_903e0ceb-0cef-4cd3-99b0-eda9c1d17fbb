/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { useValidator } from '@skynet/shared'
import { openDialog, CreateTableOld, TableColumnOld, showFailToast, Button, showSuccessToast, showToast, SvgIcon, Icon, Input, CreateForm } from '@skynet/ui'
import { Uploader, type UploadImage } from 'src/modules/common/uploader/uploader'
import { langValue, langKey, langKeyForCol } from 'src/modules/resource/constant'
import { FileList } from 'src/modules/resource/components/file-list'
import { set, get } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { apiUploadCoverUrl } from './resource-publish-api'
import { z } from 'zod'
import { apiBaiduDiskUploadProgress, apiUploadSaveVideo, apiUploadSaveSubtitle, apiUploadSaveAudio, apiBaiduDiskUpload, apiGetRecordList } from 'src/modules/resource/resource-api'
import { apiDelNetDisk, apiFixSubtitle, apiGetSeriesInfoV2, apiUploadInfoExcel, apiGetLabelList, apiUpdateSeriesInfo, apiGetTaskRole, apiDistributeTask, apiGetTask } from './resource-publish-api'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { LABEL_OPTIONS } from './constant'
import { DistributeTable } from './components/distribute-table'
import { DistributeInspectionTable } from './components/distribute-inspection-table'

const taskLoading = ref(false)
const distributeLoading = ref(false)
const episodeSeriesInfoList = ref<M.IEpisodeSeriesInfo[]>([])
const uploadSyncList = ref<M.IDiskInfoUploadProgress[]>([])
const uploadSyncLoading = ref(false)
const passFormData = ref<M.IPassForm>({})

const rootLabels = ref<M.ITag[]>([])
const firstLabels = ref<M.ITag[]>([])
const secondLabels = ref<M.ICascaderLabel[]>([])
const allLabels = ref<M.ITag[]>([])

const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()

type IVideoInfo = {
  temp_path: string
  duration: number
  file_path: string
  file_size: number
  serial_number: number
}

const diskUploadProgress: { [record: string]: string } = {
  source: '带字幕视频',
  clear: '不带字幕视频',
  cover: '封面',
}

langKeyForCol.forEach((lang, index) => {
  diskUploadProgress[`${lang}_subtitle`] = `${langValue[index]}字幕`
})

const sync_status_colors = ['', 'bg-primary text-white', 'bg-primary text-white', 'bg-green-500 text-white', 'bg-red-500 text-white']
const sync_status_map: { [record: number]: string } = {
  1: '同步中',
  2: '同步中',
  3: '已同步',
  4: '失败',
}

const subtitleLogs = ref<M.IAlterSubtitle[]>([])

const rules = z.object({
  root_label_ids: z.array(z.number()).min(1, { message: '请选择一级标签' }),
  first_label_ids: z.array(z.number()).min(1, { message: '请选择二级标签' }),
  second_label_ids:
    z.any().refine(e => {
      const labelTypes = LABEL_OPTIONS.filter(row => row.value !== 1 && row.value !== 2)
      const m: { [key: number]: number } = {}
      labelTypes.map(o => {
        m[o.value] = 0
      })

      if (e.length > 0) {
        // item[0] 有 3、4、5 三种情况 并且 3、5单选， 4可以多选
        // 3、5 是单选，所以 item 应该是一个数字；4 是多选，所以 item 应该是一个数组
        e.forEach((item: unknown) => {
          if (Array.isArray(item) && item.length === 2) {
            m[item[0] as 3 | 4 | 5]++
          } else if (typeof item === 'number') {
            const type = allLabels.value.find(label => label.label_id === item)?.content_type
            m[type as 3 | 4 | 5]++
          }
        })
        const isValid = Object.keys(m).every(o => {
          if (+o === 5) {
            if (m[+o] === 0) return false // 必须选择
            if (m[+o] > 1) return false // 不能多选
          }
          if (+o === 4 || +o === 3) {
            // 检查角色是否被选择
            if (m[+o] === 0) return false // 必须选择
          }
          return true
        })
        return isValid
      } else {
        return false
      }
    }, { message: '请选择三级标签,情节（多选）、角色（多选）、背景（单选）' }),
})
const validateInfoForCn = useValidator(passFormData, rules)
const validateInfo = useValidator(passFormData, rules)

const refreshSyncList = async (series_resource_id: number) => {
  uploadSyncLoading.value = true
  try {
    const res = await apiBaiduDiskUploadProgress({
      series_resource_id: series_resource_id,
    })
    uploadSyncList.value = res.data?.list || []
  } catch (error: any) {
    showFailToast(error.response.data.msg || error.response.data.message || '操作失败')
  } finally {
    uploadSyncLoading.value = false
  }
}

const checkVideos = (files: File[]) => {
  return Promise.all(files.map(file => {
    return new Promise(resolve => {
      const videoElement = document.createElement('video')
      const videoURL = URL.createObjectURL(file)
      videoElement.src = videoURL
      // 确保视频已加载元数据，否则 videoWidth 和 videoHeight 可能为 0
      videoElement.addEventListener('loadedmetadata', () => {
        const width = videoElement.videoWidth
        const height = videoElement.videoHeight
        console.log(`视频清晰度: ${width}x${height}`)
        const resolution = `${width}x${height}`
        const invalidResolutions = ['1080x1920', '1920x1080', '1216x2160', '2160x1216', '1440x2560', '2560x1440', '2160x3840', '3840x2160']
        resolve({
          fileName: file.name,
          validate: Math.min(width, height) >= 1080 && invalidResolutions.includes(resolution),
        })
      })
    })
  }))
}

const checkLooseVideos = (files: File[]) => {
  return Promise.all(files.map(file => {
    return new Promise(resolve => {
      const videoElement = document.createElement('video')
      const videoURL = URL.createObjectURL(file)
      videoElement.src = videoURL
      // 确保视频已加载元数据，否则 videoWidth 和 videoHeight 可能为 0
      videoElement.addEventListener('loadedmetadata', () => {
        const width = videoElement.videoWidth
        const height = videoElement.videoHeight
        console.log(`视频清晰度: ${width}x${height}`)
        resolve({
          fileName: file.name,
          validate: Math.min(width, height) >= 720,
        })
      })
    })
  }))
}

const showUploadProgress = (list: M.IDiskInfoUploadProgress[], id: number, cb: () => void) => {
  const saveLoading = ref(false)
  const notFinishList = list.filter(row => row.sync_status !== 3)
  const typeSet = new Set()
  list.forEach(row => typeSet.add(row.netdisk_type))
  const typeArr = Array.from(typeSet) as string[]
  const result: { [record: string]: M.IDiskInfoUploadProgress[] } = {}
  list.forEach(row => {
    if (!result[row.netdisk_type]) result[row.netdisk_type] = []
    result[row.netdisk_type].push(row)
  })
  const Table = CreateTableOld<M.IDiskInfoUploadProgress>()
  const columns: TableColumnOld<M.IDiskInfoUploadProgress>[] = [
    ['集数', 'serial_number', { class: 'w-[30px]' }],
    ['资源类型', (row: M.IDiskInfoUploadProgress) => {
      if (row.netdisk_type && diskUploadProgress[row.netdisk_type]) {
        return diskUploadProgress[row.netdisk_type]
      }
    }, { class: 'w-[55px]' }],
    ['同步状态', row => {
      return (
        <div class={`badge ${sync_status_colors[row.sync_status]} gap-2`}>
          { sync_status_map[row.sync_status] }
        </div>
      )
    }, { class: 'w-[65px]' }],
    ['报错信息', row => {
      return row.sync_status === 4 ? row.err_msg : ''
    }],
  ]
  return (
    <>
      {
        typeArr.map(type => {
          const _list = result[type]
          const _successList = _list.filter(row => row.sync_status === 3)
          return <div class="text-sm text-[var(--text-2)]">{diskUploadProgress[type]}: {_successList.length}/{_list.length}</div>
        })
      }
      {
        notFinishList.length > 0
          ? (
              <Button class="btn btn-xs btn-primary my-2" onClick={() => {
                const hideDialog = openDialog({
                  title: '提示',
                  mainClass: 'pb-0 px-5',
                  body: () => (
                    <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                      <x-status-body>确认清理网盘任务</x-status-body>
                      <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                        <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                        <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
                          try {
                            saveLoading.value = true
                            await apiDelNetDisk({
                              ids: [id],
                              operate_type: 1,
                            })
                            hideDialog()
                            showSuccessToast('操作成功')
                            cb && cb()
                            saveLoading.value = false
                            void refreshSyncList(id)
                          } catch (error: any) {
                            saveLoading.value = false
                            showFailToast(error.response.data.message || '操作失败')
                          }
                        }}
                        >
                          {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                          确定
                        </button>
                      </x-status-footer>
                    </x-status-confirm-dialog>
                  ),
                })
              }}
              >任务清理
              </Button>
            )
          : null
      }
      <Table list={notFinishList} columns={columns} />
    </>
  )
}

const onDiskUpload = (series_resource_id: number, refresh: () => void, subtitle_up_language_code: string) => {
  const Form = CreateForm<M.IDiskInfo>()
  const form = ref<M.IDiskInfo>({
    series_resource_id: series_resource_id,
    pure_video_path: '',
    origin_video_path: '',
    subtitle_path: '',
    subtitle_language_code: '',
    cover_path: '',
  })

  const showFileList = (path: string, chooseFileExts: string[] = []) => {
    const dir = ref(get(form.value, path, '/'))
    const closeDialog = openDialog({
      showParentWhenChildClose: false,
      title: '网盘导入',
      mainClass: '!p-4 !pb-0 !pt-0',
      body: () => (
        <FileList
          dir={dir.value}
          onClose={() => {
            closeDialog()
          }}
          chooseFileExts={chooseFileExts}
          onSave={(str: string) => {
            set(form.value, path, str)
            closeDialog()
          }}
          onDirChange={(e: string) => dir.value = e}
        />
      ),
    })
  }

  const closeDialog = openDialog({
    title: '网盘资源上传',
    mainClass: '!p-4 !pb-0',
    hideParentWhenChildOpen: true,
    body: () => (
      <>
        <Form
          class="grid grid-cols-4 gap-y-3"
          data={form.value}
          hasAction={false}
          onChange={(path, value: any) => {
            set(form.value, path, value)
          }}
          items={[
            { label: '含字幕视频地址', class: 'col-span-4', path: 'origin_video_path', input: { type: 'custom', render: () => {
              return (
                <div class="flex w-full items-center gap-x-4">
                  <Input type="text" disabled class="input input-sm input-disabled block w-full" modelValue={form.value.origin_video_path} placeholder="请选择目录" clearable onUpdate:modelValue={e => {
                    form.value.origin_video_path = e as string
                  }} />
                  <SvgIcon class="cursor-pointer" name="ic_dir" onClick={() => showFileList('origin_video_path')} />
                </div>
              )
            } } },
            { label: '不含字幕视频地址', class: 'col-span-4', path: 'pure_video_path', input: { type: 'custom', render: () => {
              return (
                <div class="flex w-full items-center gap-x-4">
                  <Input type="text" disabled class="input input-sm input-disabled block w-full" modelValue={form.value.pure_video_path} placeholder="请选择目录" clearable onUpdate:modelValue={e => {
                    form.value.pure_video_path = e as string
                  }} />
                  <SvgIcon class="cursor-pointer" name="ic_dir" onClick={() => showFileList('pure_video_path')} />
                </div>
              )
            } } },
            { label: '字幕目录地址', class: 'col-span-4', path: 'subtitle_path', input: { type: 'custom', render: () => {
              return (
                <div class="flex w-full items-center gap-x-4">
                  <Input type="text" disabled class="input input-sm input-disabled block w-full" modelValue={form.value.subtitle_path} placeholder="请选择目录" clearable onUpdate:modelValue={e => {
                    form.value.subtitle_path = e as string
                  }} />
                  <SvgIcon class="cursor-pointer" name="ic_dir" onClick={() => showFileList('subtitle_path')} />
                </div>
              )
            } } },
            { label: '封面图地址', class: 'col-span-4', path: 'cover_path', input: { type: 'custom', render: () => {
              return (
                <div class="flex w-full items-center gap-x-4">
                  <Input type="text" disabled class="input input-sm input-disabled block w-full" modelValue={form.value.cover_path} placeholder="请选择目录" clearable onUpdate:modelValue={e => {
                    form.value.cover_path = e as string
                  }} />
                  <SvgIcon class="cursor-pointer" name="ic_dir" onClick={() => showFileList('cover_path', ['jpg', 'png', 'jpeg'])} />
                </div>
              )
            } } },
          ]}
        />
        <Button class="btn btn-primary btn-sm float-right mt-4" disabled={taskLoading.value} onClick={async () => {
          try {
            if (!form.value.pure_video_path
              && !form.value.origin_video_path
              && !form.value.subtitle_path
              && !form.value.cover_path) {
              showFailToast('请选择上传地址')
              return
            }
            taskLoading.value = true
            await apiBaiduDiskUpload({ ...form.value, subtitle_language_code: subtitle_up_language_code })
            showSuccessToast('操作成功！')
            closeDialog()
          } catch (error: any) {
            showFailToast(error.response.data.msg || error.response.data.message || '操作失败')
          } finally {
            taskLoading.value = false
          }
        }}
        >
          {taskLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
          提交下载任务
        </Button>
      </>
    ),
  })
}

const onUploadVideo = (series_resource_id: number, partner_name: string, refresh: () => void) => {
  const promises = ref<Promise<unknown>[]>([])
  const tempFiles: File[] = []
  const hasUploadFiles: UploadImage[] = []
  const uploadType = ref<1 | 2>(1)

  const closeDialog = openDialog({
    title: '上传视频',
    mainClass: '!p-4 !pb-0',
    showParentWhenChildClose: false,
    body: () => (
      <>
        <Input
          type="radio"
          class="tm-radio pb-4"
          options={[
            {
              value: 1,
              label: '含字幕',
            },
            {
              value: 2,
              label: '无字幕',
            },
          ]}
          modelValue={uploadType.value}
          onUpdate:modelValue={value => uploadType.value = value as 1 | 2}
        />
        <Uploader
          accept="mp4,mkv,avi,mov"
          isImage={false}
          maxsize={1024 * 1024 * 1024 * 10}
          ossKeyType="resource"
          multiple
          beforeUpload={async ({ files }) => {
            const isLooseCheck = partner_name.toLowerCase() === 'dramaclub'
            const checkVideoMethod = isLooseCheck ? checkLooseVideos : checkVideos
            const responses = await checkVideoMethod(files) as { fileName: string, validate: boolean }[]
            const unValidVideos = responses.filter(res => !res.validate)
            if (unValidVideos.length > 0) {
              showToast({
                message: isLooseCheck ? `${unValidVideos.map(row => row.fileName).join(',')}, 视频清晰度没有达到720P` : `${unValidVideos.map(row => row.fileName).join(',')}, 视频清晰度没有达到1080P或分辨率不正确`,
                duration: 5000,
                icon: 'fail',
              })
              return false
            }
            files.forEach(file => tempFiles.push(file))
          }}
          onUploadSuccess={file => {
            if (hasUploadFiles.some(f => f?.file?.name === file.file?.name)) return
            hasUploadFiles.push(file)
            const ps = new Promise(resolve => {
              const video = document.createElement('video')
              video.preload = 'metadata'
              video.src = file.url as string
              video.onloadedmetadata = () => {
                const width = video.videoWidth
                const height = video.videoHeight
                const resolution = `${width}x${height}`
                const videoInfo = {
                  temp_path: file.temp_path || '',
                  duration: Math.round(video.duration),
                  file_path: file.temp_path || '',
                  file_size: file.file?.size || 0,
                  serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
                  file_resolution: resolution,
                }
                resolve(videoInfo)
              }
            })
            promises.value.push(ps)
          }}
        >
          <x-uploader-wrapper class="flex h-[120px] w-full cursor-pointer flex-col items-center  justify-center overflow-hidden rounded-md border border-dashed">
            <div>上传视频, 支持MP4, MKV, AVI，MOV格式</div>
            <div>大小限制10G</div>
          </x-uploader-wrapper>
        </Uploader>
        <Button class="btn btn-primary btn-sm float-right mt-4" disabled={promises.value.length === 0 || tempFiles.length !== promises.value.length} onClick={async () => {
          try {
            const videoInfos = await Promise.all(promises.value) as IVideoInfo[]
            await apiUploadSaveVideo({
              data: videoInfos,
              series_resource_id,
              type: uploadType.value,
            })
            showSuccessToast('操作成功！')
            refresh && refresh()
            closeDialog()
          } catch (error: any) {
            showFailToast(error.response.data.message || '操作失败')
          }
        }}
        >保存
        </Button>
      </>
    ),
  })
}

const onUploadAudio = (series_resource_id: number, refresh: () => void) => {
  const audio_type = ref< 1 | 2 | 3 | number>(1)
  const tempFiles: File[] = []
  const hasUploadFiles = ref<UploadImage[]>([])

  const closeDialog = openDialog({
    title: '上传音频',
    mainClass: '!p-4 !pb-0',
    showParentWhenChildClose: false,
    body: () => (
      <>
        <div class="flex items-center gap-x-2 pb-4">
          {requiredLabel('音频类型:')}
          <select class="select select-bordered select-sm" value={audio_type.value} onChange={e => {
            const target = e.target as HTMLSelectElement
            const value = target.value
            audio_type.value = +value
          }}
          >
            <option value={1}>原始声音</option>
            <option value={2}>背景声音</option>
            <option value={3}>特效声音</option>
          </select>
        </div>
        <Uploader
          accept="mp4,mp3,wav"
          ossKeyType="resource"
          isImage={false}
          maxsize={1024 * 1024 * 1024 * 2}
          multiple
          beforeUpload={({ files }) => {
            files.forEach(file => tempFiles.push(file))
          }}
          onUploadSuccess={file => {
            if (hasUploadFiles.value.some(f => f?.file?.name === file.file?.name)) return
            hasUploadFiles.value.push(file)
          }}
        >
          <x-uploader-wrapper class="flex h-[120px] w-full cursor-pointer flex-col items-center justify-center  gap-y-2 overflow-hidden rounded-md border border-dashed">
            <p>上传音频</p>
            <p>上传文件需要确保音频文件名和视频对应,支持mp4、mp3、wav文件</p>
          </x-uploader-wrapper>
        </Uploader>
        <Button class="btn btn-primary btn-sm float-right mt-4" disabled={hasUploadFiles.value.length === 0 || tempFiles.length !== hasUploadFiles.value.length} onClick={async () => {
          try {
            const subtitleInfos = hasUploadFiles.value.map(file => {
              return {
                file_path: file.temp_path || '',
                serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
              }
            })
            await apiUploadSaveAudio({
              data: subtitleInfos,
              series_resource_id,
              audio_type: audio_type.value,
            })
            refresh && refresh()
            showSuccessToast('保存成功')
            closeDialog()
          } catch (error: any) {
            showFailToast(error.response.data.message || '操作失败')
          }
        }}
        >保存
        </Button>

      </>
    ),
  })
}

const onUploadCaption = (series_resource_id: number, refresh: () => void, language_code: string) => {
  const tempFiles: File[] = []
  const hasUploadFiles = ref<UploadImage[]>([])

  const closeDialog = openDialog({
    title: '上传字幕',
    mainClass: '!p-4 !pb-0',
    showParentWhenChildClose: false,
    body: () => (
      <>
        <Uploader
          accept="vtt,srt,srtx"
          ossKeyType="resource"
          isImage={false}
          maxsize={1024 * 1024 * 1}
          multiple
          beforeUpload={({ files }) => {
            files.forEach(file => tempFiles.push(file))
          }}
          onUploadSuccess={file => {
            if (hasUploadFiles.value.some(f => f?.file?.name === file.file?.name)) return
            hasUploadFiles.value.push(file)
          }}
        >
          <x-uploader-wrapper class="flex h-[120px] w-full cursor-pointer flex-col items-center justify-center  gap-y-2 overflow-hidden rounded-md border border-dashed">
            <p>上传字幕, 支持VTT, SRT、SRTX格式</p>
            <p>上传文件需要确保字幕文件名和视频对应</p>
          </x-uploader-wrapper>
        </Uploader>
        <Button class="btn btn-primary btn-sm float-right mt-4" disabled={hasUploadFiles.value.length === 0 || tempFiles.length !== hasUploadFiles.value.length} onClick={async () => {
          try {
            const subtitleInfos = hasUploadFiles.value.map(file => {
              return {
                file_path: file.temp_path || '',
                serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
              }
            })
            await apiUploadSaveSubtitle({
              data: subtitleInfos,
              series_resource_id,
              language_code,
            })
            await apiFixSubtitle({
              series_resource_id: series_resource_id,
              lang_list: [language_code],
              serial_numbers: subtitleInfos.map(row => row.serial_number),
            })
            refresh && refresh()
            showSuccessToast('保存成功')
            closeDialog()
          } catch (error: any) {
            showFailToast(error.response.data.message || '操作失败')
          }
        }}
        >保存
        </Button>

      </>
    ),
  })
}

const onUploadImage = (series_resource_id: number, refresh: () => void) => {
  const isUploading = ref(false)
  const closeDialog = openDialog({
    title: '上传封面',
    mainClass: '!p-4 !pb-0 justify-center',
    body: () => (
      <x-upload-cover class="grid gap-y-2">
        <Uploader
          accept="png,jpg,jpeg"
          maxsize={1024 * 1024 * 2}
          dimension={[3, 4]}
          multiple={false}
          ossKeyType="resource"
          class="size-[200px] cursor-pointer overflow-hidden rounded-md border border-dashed"
          beforeUpload={() => {
            isUploading.value = true
          }}
          onUploadSuccess={async d => {
            try {
              isUploading.value = true
              await apiUploadCoverUrl({
                id: series_resource_id,
                path: d.temp_path!,
              })
              isUploading.value = false
              refresh && refresh()
              showSuccessToast('上传成功！')
              closeDialog()
            } catch (error: any) {
              isUploading.value = false
              showFailToast(error.response.data.message || '操作失败')
            }
          }}
          isImage={false}
        >
          {
            isUploading.value
              ? <span class="flex size-full items-center justify-center">图片上传中……</span>
              : <span class="flex size-full items-center justify-center">封面资源</span>
          }
        </Uploader>
        <x-upload-cover-tip class="text-sm text-gray-600">png,jpg,jpeg格式，大小限制2M, 3:4比例</x-upload-cover-tip>
      </x-upload-cover>
    ),
  })
}

const getEpisodeSeriesInfoList = async (params: { series_resource_id: number }) => {
  const res = await apiGetSeriesInfoV2(params)
  const list = res.data?.list
  const allLang = [...langKey]
  episodeSeriesInfoList.value = allLang.map(langCode => {
    const item = list?.find(row => row.language_code === langCode)
    if (item) {
      return item
    } else {
      return {
        cover: '',
        title: '',
        description: '',
        is_sync: 0,
        language_code: langCode,
        push_content_list: [],
        horizontal_cover: '',
      }
    }
  })
}

const updateEpisodeSeriesInfoList = async (data: { series_resource_id: number, list: M.IEpisodeSeriesInfo[] }) => {
  try {
    await apiUpdateSeriesInfo(data)
    showSuccessToast('操作成功')
  } catch (error: any) {
    showFailToast(error.response.data.message || '创建失败')
  }
}

// 上传信息
const onUploadResourceInfo = (cb: () => void) => {
  const uploading = ref(false)
  const inputRef = ref<HTMLInputElement>()
  const fileChange = async (e: Event) => {
    const files = (e.target as HTMLInputElement)?.files || []
    const fileList = Array.from(files)
    if (fileList && fileList.length > 0) {
      const file = fileList[0]
      const ext = file.name.split('.').pop()?.toLowerCase() || ''
      if (ext !== 'csv' && ext !== 'xlsx') {
        showFailToast('仅支持上传csv、xlsx文件');
        (inputRef.value as HTMLInputElement).value = ''
        return
      }
      try {
        uploading.value = true
        await apiUploadInfoExcel({ file })
        showSuccessToast('操作成功！')
        createDialog()
        cb && cb()
      } catch (error: any) {
        showFailToast(error.response.data.message || '操作失败！');
        (inputRef.value as HTMLInputElement).value = ''
      } finally {
        uploading.value = false
      }
    }
  }
  const createDialog = openDialog({
    title: '批量新增',
    mainClass: 'pb-0 px-5',
    body: () => (
      <div>
        <div>
          上传模板：
          <a class="link" target="_blank" href="https://rg975ojk5z.feishu.cn/wiki/VQGpwLkMGiGS0CkyVMBc2xJinVd">跳转模板</a>
        </div>
        <div class="mt-4">
          <input ref={inputRef} disabled={uploading.value} class="file-input file-input-xs file-input-primary w-full max-w-xs" type="file" multiple={false} accept="csv" onChange={fileChange} />
          <div class="mt-2 text-xs">{requiredLabel('仅支持上传.csv，.xlsx文件')}</div>
        </div>
      </div>
    ),
  })
}

const getRootLabels = async () => {
  const rootRes = await apiGetLabelList({
    language_code: 'zh-CN',
  })
  allLabels.value = rootRes.data?.list || []
  rootLabels.value = rootRes.data?.list.filter(row => row.content_type === 1) || []

  firstLabels.value = rootRes.data?.list.filter(row => row.content_type === 2) || []

  const labelTypes = LABEL_OPTIONS.filter(row => row.value !== 1 && row.value !== 2)
  const labels = labelTypes.map(row => {
    return {
      ...row,
      children: rootRes.data?.list.filter(item => item.content_type === row.value).map(item => {
        return {
          contentType: item.content_type || 0,
          type: row.label,
          label: item.content,
          value: item.label_id,
          meaning: item.meaning || '',
        }
      }),
    }
  })
  secondLabels.value = labels
}

const getLabelList = () => {
  if (rootLabels.value.length > 0 && firstLabels.value.length > 0 && secondLabels.value.length > 0) return
  void getRootLabels()
}

/**
 * @description 当前页面下 0: 没有身份 1：组长 2: 组员
 */
const resourceRole = ref<0 | 1 | 2 | null>(0)
const leaders = ref<string[]>([])
const members = ref<string[]>([])
const all_members = ref<string[]>([])

// 每个资源页面都请求一次。 获取当前角色和 可以分派任务的人
const getTaskRole = async (data: { type: M.ITaskType }) => {
  leaders.value = []
  members.value = []
  all_members.value = []
  resourceRole.value = 0
  const res = await apiGetTaskRole(data)
  resourceRole.value = res.data.role
  leaders.value = res.data?.leaders.map((row: { name: string }) => row.name) || []
  members.value = res.data?.members.map((row: { name: string }) => row.name) || []
  all_members.value = res.data?.all_members.map((row: { name: string }) => row.name) || []
}

// 组长分配任务
const onDistributeTaskNoTeam = (data: { type: M.ITaskType, resource_ids: number[] }, cb?: () => void) => {
  const hideDialog = openDialog({
    title: '分配任务',
    mainClass: 'pb-0 px-5',
    customClass: '!w-[440px]',
    body: () => (
      <DistributeInspectionTable
        users={members.value}
        resourceIds={data.resource_ids}
        type={data.type}
        onCancel={() => {
          hideDialog()
        }}
        onSelect={() => {
          cb && cb()
          hideDialog()
        }}
      />
    ),
  })
}

// 组长分配任务
const onDistributeTask = (data: { type: M.ITaskType, resource_ids: number[] }, cb?: () => void) => {
  const hideDialog = openDialog({
    title: '分配任务',
    mainClass: 'pb-0 px-5',
    customClass: '!w-[440px]',
    body: () => (
      <DistributeTable
        resourceIds={data.resource_ids}
        type={data.type}
        onCancel={() => {
          hideDialog()
        }}
        onSelect={() => {
          cb && cb()
          hideDialog()
        }}
      />
    ),
  })
}

// 组员获取任务
const onGetTask = (type: M.ITaskType, cb?: () => void) => {
  const hideDialog = openDialog({
    title: '领取任务',
    mainClass: 'pb-0 px-5',
    customClass: '!w-[440px]',
    body: () => (
      <x-distribute-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-distribute-tip>继续领取？</x-distribute-tip>
        <x-distribute-footer class="flex w-full justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" disabled={distributeLoading.value} onClick={async () => {
            try {
              distributeLoading.value = true
              await apiGetTask({
                type,
              })
              hideDialog()
              showSuccessToast('操作成功')
              cb && cb()
              distributeLoading.value = false
            } catch (error: any) {
              distributeLoading.value = false
              showFailToast(error.response.data.message || '操作失败')
            }
          }}
          >
            {distributeLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
            确定
          </button>
        </x-distribute-footer>
      </x-distribute-confirm-dialog>
    ),
  })
}

const getSubtitleRecordList = async (id: number) => {
  const res = await apiGetRecordList({
    series_resource_id: id,
  })
  subtitleLogs.value = res.data?.list || []
}

export const useResourceStore = () => {
  return {
    onUploadVideo,
    onDiskUpload,
    onUploadCaption,
    onUploadAudio,
    onUploadImage,
    getEpisodeSeriesInfoList,
    episodeSeriesInfoList,
    updateEpisodeSeriesInfoList,
    refreshSyncList,
    showUploadProgress,
    uploadSyncList,
    uploadSyncLoading,
    passFormData,
    validateInfoForCn,
    validateInfo,
    onUploadResourceInfo,
    allLabels,
    firstLabels,
    rootLabels,
    secondLabels,
    getLabelList,
    // 任务相关
    getTaskRole,
    resourceRole,
    leaders,
    members,
    all_members,
    onDistributeTask,
    onGetTask,
    subtitleLogs,
    getSubtitleRecordList,
    onDistributeTaskNoTeam
  }
}
