import { createComponent } from '@skynet/shared/create-component'
import { PhoneLayout } from '~/layouts/phone-layout'
import { TransitionLayout } from '~/layouts/transition-layout'
import type { WalletDataInfo } from '~/modules/profile/profile'
import SeriesItemPage from '~/modules/series/series-item-page'
type ClientContentOptions = {
  props: {
    data: M.Dramato
    wallet: WalletDataInfo
  }
}

export const required = Symbol('required')

export const ClientContent = createComponent<ClientContentOptions>({
  name: 'ClientContent',
  props: {
    data: {
      type: Object,
      required: true,
    },
    wallet: {
      type: Object,
      required: true,
    },
  },
}, props => {
  return () => (
    <x-phone-layout class="block">
      <PhoneLayout>
        <TransitionLayout>
          <SeriesItemPage data={props.data} wallet={props.wallet} />
        </TransitionLayout>
      </PhoneLayout>
    </x-phone-layout>
  )
})

export default ClientContent
