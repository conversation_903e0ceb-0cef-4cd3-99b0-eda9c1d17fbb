import { use<PERSON>alida<PERSON> } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { ref } from 'vue'
import { z } from 'zod'
const Form = CreateForm<M.StrategyGroup>()
const defaultFormData = () => {
  return {
    name: '',
    id: '',
    user_type: 2,
    user_platform: 0,
    platform: 'ios',
    scene: ['purchase'],
    ad_type: 1, // 广告类型 1:解锁广告 2:激励广告
    reward_ads: { list: [] },
    product_config: {
      ios_config: {
        pay_model: 'IAP',
        application: ['pay'],
        recharge_option: [],
        member_vip_option: [],
      },
      android_config: {
        pay_model: 'IAP',
        application: ['pay'],
        recharge_option: [],
        member_vip_option: [],
      },
    },
    task_config: undefined,
    drama_list: undefined,
    iap_strategys: undefined,
    send_pro_user: 1,
    iap_strategy_ids: undefined,
    user_config: {
      active_users_config: {
        latest_days: 7,
        start_days: '',
        end_days: '',
      },
      custom_users_config: {},
      af_ad_config: {
        adset_list: [{
          code: '',
          name: '',
          id: '',
        }],
      },
    },
    scene_type: [],
    ad_config: {},
  } as M.StrategyGroup
}
const formData = ref<M.StrategyGroup>(defaultFormData())
const activeUserType = ref<'latest_days' | 'start_and_end'>('latest_days')

const resetFormData = () => {
  formData.value = defaultFormData()
  console.log('formData.value', formData.value)
}

const resetUserFormData = () => {
  const { name, user_type, user_platform, platform, user_config } = defaultFormData()
  formData.value = { ...formData.value, name, user_type, user_platform, user_config, platform }
}

const resetProductFormData = (isIAA?: boolean) => {
  if (isIAA) {
    formData.value = {
      ...formData.value,
      drama_list: [],
      ad_id: undefined,
      unlock_nodes: [],
      ad_info: undefined,
    }
  } else {
    formData.value.product_config = defaultFormData().product_config
  }
}

const resetSolutionFormData = () => {
  formData.value = {
    ...formData.value,
    ad_config: {},
    scene_type: [],
  }
}

const resetTaskFormData = (isIAA?: boolean) => {
  if (isIAA) {
    formData.value.task_config = undefined
  } else {
    formData.value.task_config = defaultFormData().task_config
  }
}

const stepOneRules = z.object({
  name: z.string().min(1, '必填'),
  platform: z.string().min(1, '必填'),
  user_type: z.number(),
  ad_platform: z.string().min(1, '请选择广告平台'),
  // zod user_config为null通过校验
  user_config: z.optional(z.object({
    active_users_config: z.optional(z.object({
      latest_days: z.number().min(1),
      start_days: z.string().min(1),
      end_days: z.string().min(1),
    })),
    af_ad_config: z.object({
      adset_list: z.array(z.object({
        id: z.string().min(1),
        code: z.string().min(1),
        name: z.string().min(1),
      })).min(1, { message: '必填' }),
    }),
  })),
})

const { error: stepOneError, validateAll: _validateStepOne } = useValidator(formData, stepOneRules)

const validateStepOne = () => {
  const exclude = [
    !formData.value.user_type && ['user_config', 'user_config.active_users_config', 'user_config.af_ad_config'],
    formData.value.user_type === 1 && ['user_config.custom_users_config', 'user_config.af_ad_config'],
    formData.value.user_type === 2 && ['user_config.active_users_config', 'user_config.af_ad_config', 'user_config.custom_users_config.platform_version'],
    formData.value.user_type === 3 && ['user_config.active_users_config', 'user_config.custom_users_config'],
    activeUserType.value === 'latest_days' && ['user_config.active_users_config.start_days', 'user_config.active_users_config.end_days'],
    activeUserType.value === 'start_and_end' && ['user_config.active_users_config.latest_days'],
  ].filter(Boolean).flat() as string[]
  const valid = _validateStepOne({ exclude })
  stepStatusList.value[0] = valid ? 'valid' : 'invalid'
  console.log('stepOneError', stepOneError.value)
  return valid
}

const stepTwoRules = z.object({
  ad_id: z.number().min(1, '必填'),
  unlock_nodes: z.array(z.any()).min(1, '必填'),
})

const { error: stepTwoError, validateAll: _validateStepTwo } = useValidator(formData, stepTwoRules)

const validateStepTwo = () => {
  const valid = _validateStepTwo()
  stepStatusList.value[1] = valid ? 'valid' : 'invalid'
  return valid
}

const stepRulesForSolution = z.object({
  scene_type: z.array(z.string()).min(1, '必填'),
})

const { error: stepErrorForSolution, validateAll: _validateStepSolution, ErrorSpan: ErrorSpanForSolution } = useValidator(formData, stepRulesForSolution)

const validateStepTwoForIap = () => {
  const exclude = [
    // formData.value.ad_type === 1 && ['reward_ads'],
    // formData.value.ad_type === 2 && ['reward_ads'],
  ].filter(Boolean).flat() as string[]
  const valid = _validateStepSolution({ exclude })
  stepStatusList.value[1] = valid ? 'valid' : 'invalid'
  return valid
}

const stepThreeRules = z.object({
  task_config: z.object({
    priority: z.optional(z.number().min(0)),
    duration: z.number().min(-1),
    auto_up: z.number().min(-1).optional(),
  }),
})

const { error: stepThreeError, validateAll: _validateStepThree } = useValidator(formData, stepThreeRules)

const validateStepThree = () => {
  const valid = _validateStepThree()
  stepStatusList.value[2] = valid ? 'valid' : 'invalid'
  return valid
}

const validateAllSteps = () => {
  return validateStepOne() && validateStepTwo() && validateStepThree()
}

const validateAllStepsForIap = () => {
  return validateStepOne() && validateStepTwoForIap() && validateStepThree()
}

const stepStatusList = ref<Array<'invalid' | 'valid'>>(['invalid', 'invalid', 'invalid'])

const userProfileList = ref<Api.StrategyGroupUserProfile.Item[]>([])
const fetchingUserProfileList = ref(false)

const platformMap: Record<number, string> = {
  0: 'All',
  1: 'IOS',
  2: 'Android',
}
export const useStrategyGroupAdStore = () => {
  return {
    platformMap,
    formData,
    resetFormData,
    Form,
    stepStatusList,
    stepOneError,
    stepTwoError,
    stepErrorForSolution,
    ErrorSpanForSolution,
    validateStepTwoForIap,
    stepThreeError,
    validateStepOne,
    validateStepTwo,
    validateStepThree,
    validateAllSteps,
    validateAllStepsForIap,
    activeUserType,
    resetProductFormData,
    resetTaskFormData,
    resetUserFormData,
    resetSolutionFormData,
    userProfileList,
    fetchingUserProfileList,
  }
}

export const useFreeStrategyGroupAdStore = useStrategyGroupAdStore
