import { createComponent, getQueries } from '@skynet/shared'
import { Back } from 'src/h5_modules/common/back/back'
import { useLocale } from 'src/lib/use-locale'
import { onMounted, ref } from 'vue'
import { jsBridge } from 'src/lib/jsbridge'
import { close, open } from './images/images'
import dayjs from 'dayjs'
import { apiGetSubscribeStatus, apiSubscribe } from './feedback/feedback-api'
import { showToast } from '../common/toast/toast'

type TerminateOptions = {
  props: {}
}
export const Terminate = createComponent<TerminateOptions>({
  props: {},
}, props => {
  const { showNavigation, language } = getQueries({ showNavigation: true, language: '' })

  const { t } = useLocale(language)
  const showBack = ref(false)

  const info = ref<{
    uid: number
    expire_time: number // 时间戳(秒)
    status: number
  }>({
    uid: 0,
    expire_time: 0,
    status: 2, // 状态(0-预备;1-正常; 2-取消; 3-结束),0/1--开，2/3--关
  })

  const flag = ref(false)

  const changeStatus = async () => {
    if (flag.value) {
      return
    }
    flag.value = true
    const status = [0, 1].includes(info.value.status) ? 2 : 1
    await apiSubscribe(status)
    void apiGetSubscribeStatus().then(res => {
      info.value = res.data || {
        uid: 0,
        expire_time: 0,
        status: 2, // 状态(0-预备;1-正常; 2-取消; 3-结束),0/1--开，2/3--关
      }
    }).finally(() => {
      flag.value = false
    })
    showToast(t('toast.submit-success'))
  }

  onMounted(() => {
    document.title = t('terminate.title')

    void apiGetSubscribeStatus().then(res => {
      info.value = res.data || {
        uid: 0,
        expire_time: 0,
        status: 2, // 状态(0-预备;1-正常; 2-取消; 3-结束),0/1--开，2/3--关
      }
    })
  })

  onMounted(() => {
    if (jsBridge.inDramawaveApp) {
      showBack.value = !showNavigation
    } else {
      showBack.value = true
    }
  })

  return () => (
    <div class="size-full bg-[var(--black)]">
      {showBack.value && (
        <Back
          title={t('terminate.title')}
          isWhite={true}
          class="shrink-0 bg-transparent" />
      ) }
      <x-collapse class="pt-5 flex w-full flex-1 flex-col gap-y-2 overflow-auto px-3">
        <x-collapse-item class="flex justify-between items-center text-3.5 h-11 text-[#cccacb]">
          <x-collapse-item-title>UID</x-collapse-item-title>
          <x-collapse-item-content class="text-[#FDFBFC]">{info.value.uid || '-空-'}</x-collapse-item-content>
        </x-collapse-item>
        <x-collapse-item class="flex justify-between items-center text-3.5 h-11 text-[#cccacb]">
          <x-collapse-item-title>{t('terminate.expiration')}</x-collapse-item-title>
          <x-collapse-item-content class="text-[#FDFBFC]">{info.value.expire_time > 0 ? dayjs(info.value.expire_time * 1000).format('YYYY.MM.DD') : '-空-'}</x-collapse-item-content>
        </x-collapse-item>
        <x-collapse-item class="flex justify-between items-center text-3.5 h-11 text-[#cccacb]">
          <x-collapse-item-title>{t('terminate.manageDesc')}</x-collapse-item-title>
          <x-collapse-item-content class="text-[#FDFBFC]">
            <img onClick={changeStatus} class="w-10 h-5" alt="" src={[0, 1].includes(info.value.status) ? open : close} />
          </x-collapse-item-content>
        </x-collapse-item>
      </x-collapse>
    </div>
  )
})

export default Terminate
