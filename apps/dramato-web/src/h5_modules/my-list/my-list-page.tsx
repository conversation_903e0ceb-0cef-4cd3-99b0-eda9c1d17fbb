import { createComponent } from '@skynet/shared'
import { noData } from '../images/images'
import { onMounted } from 'vue'
import { useMyListStore } from './use-my-list-store'
import DramaCard from './component/drama-card'
import InfinityScroll from '../common/infinity-scroll/infinity-scroll'
import { T } from './my-list-i18n'
import { h5Track } from 'src/lib/h5-track'
import { useRouter } from 'vue-router'

type MyListPageOptions = {
  props: {}
}
export const MyListPage = createComponent<MyListPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const {
    getDramaFeeds,
    dramaFeeds,
    viewHistoryList,
    getViewHistoryList,
    hasMoreDramaFeeds,
    loadingDramaFeeds,
    dramaFeedsNext,
    isFirstLoading,
  } = useMyListStore()

  onMounted(() => {
    dramaFeedsNext.value = 'offset=0'
    hasMoreDramaFeeds.value = true
    isFirstLoading.value = true

    void Promise.all([getDramaFeeds(true), getViewHistoryList()]).finally(() => {
      isFirstLoading.value = false
    })
  })

  onMounted(() => {
    h5Track('my_list', 'page', 'show')
  })

  return () => {
    if (isFirstLoading.value) {
      return (
        <x-my-list-page class="block h-full overflow-y-auto px-3 pb-3">
          <x-history-list class="flex flex-col gap-y-2">
            <x-title class="text-4.5 leading-11 block h-11 font-bold text-white">{T.myList.watchHistory()}</x-title>
            <x-history-list-content class="grid grid-cols-3 gap-2">
              { [1, 2, 3]?.map((drama, index) => (
                <x-drama-card class="block h-auto overflow-hidden rounded-lg">
                  <div class="!h-37.25 skeleton-animation relative block !w-full overflow-hidden rounded-lg" />
                  <x-drama-desc-wrap class="flex flex-col items-start justify-start gap-y-1 p-2">
                    <div class="!h-4.5 skeleton-animation w-full rounded-sm" />
                    <div class="skeleton-animation !h-3 !w-16 rounded-sm" />
                  </x-drama-desc-wrap>
                </x-drama-card>
              ))}
            </x-history-list-content>
            <x-my-list class="flex flex-col gap-y-2">
              <x-title class="text-4.5 leading-11 block h-11 font-bold text-white">{T.myList.myList()}</x-title>
              <x-my-list-content class="grid grid-cols-3 gap-2">
                { [1, 2, 3, 4, 5, 6, 7, 8, 9]?.map((drama, index) => (
                  <x-drama-card class="block h-auto overflow-hidden rounded-lg">
                    <div class="!h-37.25 skeleton-animation relative block !w-full overflow-hidden rounded-lg" />
                    <x-drama-desc-wrap class="flex flex-col items-start justify-start gap-y-1 p-2">
                      <div class="!h-4.5 skeleton-animation w-full rounded-sm" />
                      <div class="skeleton-animation !h-3 !w-16 rounded-sm" />
                    </x-drama-desc-wrap>
                  </x-drama-card>
                ))}
              </x-my-list-content>
            </x-my-list>
          </x-history-list>
        </x-my-list-page>
      )
    }

    if ((!dramaFeeds?.value || dramaFeeds?.value.length <= 0) && (!viewHistoryList.value || viewHistoryList.value.length <= 0)) {
      return (
        <x-empty class="relative  flex size-full flex-col px-3 pb-3">
          <x-title class="text-4.5 leading-11 block h-11 font-bold text-white">{T.myList.myList()}</x-title>
          <div class="flex flex-1 flex-col items-center justify-center gap-y-5">
            <img src={noData} class="size-40" />
            <div class="text-4 text-center text-[#a1a2a3]">{T.myList.noData()}</div>
          </div>
        </x-empty>
      )
    }

    if ((!dramaFeeds?.value || dramaFeeds?.value.length <= 0)) {
      return (
        <x-my-list-page class="pb-15 block h-full overflow-y-auto px-3">
          {
            viewHistoryList.value && viewHistoryList.value.length > 0 ? (
              <x-history-list class="flex flex-col gap-y-2">
                <x-title class="text-4.5 leading-11 block h-11 font-bold text-white">{T.myList.watchHistory()}</x-title>
                <x-history-list-content class="grid grid-cols-3 gap-2">
                  { viewHistoryList.value?.slice(0, 3)?.map((drama, index) => <DramaCard key={`RecommendationDramaCard-${drama.key}-${index}`} is_last_history={index === 2 && viewHistoryList.value.length > 3} drama={drama} />)}
                </x-history-list-content>
              </x-history-list>
            ) : null
          }

          <x-my-list class="flex flex-col gap-y-16">
            <x-title class="text-4.5 leading-11 block h-11 font-bold text-white">{T.myList.myList()}</x-title>
            <div class="flex flex-1 flex-col items-center justify-center gap-y-5">
              <img src={noData} class="size-40" />
              <div class="text-4 text-center text-[#a1a2a3]">{T.myList.noFlow()}</div>
              <div class="text-3.5 h-8 cursor-pointer rounded-lg bg-[#FC2763] px-4 font-medium leading-8 text-[#FFFFFF] !outline-none" onClick={() => router.push('/')}>
                {T.myList.goToHome()}
              </div>
            </div>
          </x-my-list>

        </x-my-list-page>
      )
    }

    return (
      <x-my-list-page class="block h-full overflow-y-auto px-3 pb-3">
        <InfinityScroll
          hasMore={hasMoreDramaFeeds.value}
          next={dramaFeedsNext.value}
          loading={loadingDramaFeeds.value}
          onLoad={() => getDramaFeeds(false)}
        >
          {
            viewHistoryList.value && viewHistoryList.value.length > 0 ? (
              <x-history-list class="flex flex-col gap-y-2">
                <x-title class="text-4.5 leading-11 block h-11 font-bold text-white">{T.myList.watchHistory()}</x-title>
                <x-history-list-content class="grid grid-cols-3 gap-2">
                  { viewHistoryList.value?.slice(0, 3)?.map((drama, index) => <DramaCard name="/library/history" key={`RecommendationDramaCard-${drama.key}-${index}`} is_last_history={index === 2 && viewHistoryList.value.length > 3} drama={drama} />)}
                </x-history-list-content>
              </x-history-list>
            ) : null
          }

          <x-my-list class="flex flex-col gap-y-2">
            <x-title class="text-4.5 leading-11 block h-11 font-bold text-white">{T.myList.myList()}</x-title>
            <x-my-list-content class="grid grid-cols-3 gap-2">
              { dramaFeeds.value?.map((drama, index) => <DramaCard name="/library" key={`RecommendationDramaCard-${drama.key}-${index}`} drama={drama} />)}
            </x-my-list-content>
          </x-my-list>
        </InfinityScroll>

      </x-my-list-page>
    )
  }
})

export default MyListPage
