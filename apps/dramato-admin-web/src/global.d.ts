// 本文件不得出现 import 和 export 语句，否则会导致全局类型全部失效
// 如果你一定要 import 或者 export，请新建 xxx.d.ts 文件，并放在 src/types 目录下
type JsonValue = string | number | boolean | null | undefined | Array<JsonValue> | { [key: string]: JsonValue }
type PPick<T, K extends keyof T> = Partial<Pick<T, K>>
type RPick<T, K extends keyof T> = Required<Pick<T, K>>
type RPPick<T, P extends keyof T, R extends keyof T> = RPick<T, P> & PPick<T, R>

interface Window {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  AliPlayer: any
}

type PageInfo = { // 分页信息
  page_index: number// 当前页
  page_size: number// 页大小
}
type PageInfo2 = { // 分页信息
  offset: number
  size: number
  total: number
  has_more: boolean
}

type ListStore<T extends {}> = { list: T }

declare global {
  interface Window {
    wx: unknown
    HlsJsPlugin: unknown
    Aliplayer: unknown
  }
}
/** 所有 api 接口的响应数据都应该准守该格式 */
type ApiResponse<T = unknown> = {
  code: number
  data?: T
  msg?: string
  message?: string
}
type ApiPagedResponse<T = unknown> = ApiResponse<{
  items: T[]
  page_info: PageInfo2
}>

declare namespace Lottie {
  export interface AnimationItem {
    play()

    stop()

    pause()

    // one param speed (1 is normal speed)
    setSpeed(speed: number)

    // one param direction (1 is normal direction)
    setDirection(direction: number)

    // If false, it will respect the original AE fps. If true, it will update as much as possible. (true by default)
    setSubframe(flag: boolean)

    // first param is a numeric value. second param is a boolean that defines time or frames for first param
    goToAndPlay(value: number, isFrame: boolean)

    // first param is a numeric value. second param is a boolean that defines time or frames for first param
    goToAndStop(value: number, isFrame: boolean)

    // first param is a single array or multiple arrays of two values each(fromFrame,toFrame), second param is a boolean for forcing the new segment right away
    playSegments(segments: number[] | number[][], forceFlag: boolean)

    // To destroy and release resources.
    destroy()
  }

  export interface AnimationConfig {
    // an Object with the exported animation data.
    animationData?: unknown

    // the relative path to the animation object. (animationData and path are mutually exclusive)
    path?: string

    // true / false / number
    loop?: boolean | number

    // true / false it will start playing as soon as it is ready
    autoplay?: boolean

    // animation name for future reference
    name?: string

    // 'svg' / 'canvas' / 'html' to set the renderer
    renderer?: string

    // the dom element on which to render the animation
    container?: unknown
  }
}

declare class LottyPlayer {
  // optional parameter name to target a specific animation
  play(name?: string)

  // optional parameter name to target a specific animation
  stop(name?: string)

  // first param speed (1 is normal speed) with 1 optional parameter name to target a specific animation
  setSpeed(speed: number, name?: string)

  // first param direction (1 is normal direction.) with 1 optional parameter name to target a specific animation
  setDirection(direction: number, name?: string)

  // default 'high', set 'high','medium','low', or a number > 1 to improve player performance. In some animations as low as 2 won't show any difference.
  setQuality(quality: string | number)

  // param usually pass as location.href. Its useful when you experience mask issue in safari where your url does not have # symbol.
  setLocationHref(href: string)

  // returns an animation instance to control individually.
  loadAnimation(params: Lottie.AnimationConfig): Lottie.AnimationItem

  // you can register an element directly with registerAnimation. It must have the "data-animation-path" attribute pointing at the data.json url
  registerAnimation(element: unknown, animationData?: unknown)

  // looks for elements with class "lottie"
  searchAnimations(animationData: unknown?, standalone: boolean?, renderer?: string)

  // To destroy and release resources. The DOM element will be emptied.
  destroy(name?: string)
}

declare const Lottie: LottyPlayer

declare module 'lottie-web' {
  export = Lottie
  export as namespace Lottie;
}
declare module 'pdfjs-dist/build/pdf' {
  export * from 'pdfjs-dist/types/src/pdf'
}

// 本文件不得出现 import 和 export 语句，否则会导致全局类型全部失效
// 如果你一定要 import 或者 export，请新建 xxx.d.ts 文件，并放在 src/types 目录下

interface loginForm {
  username: string
  password: string
  mfa: string
  usernameError?: string
  passwordError?: string
  mfaError?: string
  isThirdUser?: number | boolean
}
