import { createComponent, getQueriesWithParser, queryParsers } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { FunctionalComponent, onUnmounted, ref, watch } from 'vue'
import { RouterLink, RouterView, useRoute } from 'vue-router'
import { useStrategyGroupStore } from './strategy-group-store'
import { apiGetStrategyGroupDetails } from './strategy-group-api'
import router from 'src/router'
type StrategyGroupCreatePageOptions = {
  props: {}
}

const Item: FunctionalComponent<{ number: number }, { click: (e: Event) => void }> = (props, { slots, emit }) => (
  <MergeClass tag="x-item" baseClass="flex items-center gap-2" onClick={e => emit('click', e)}>
    <span class="size-8 bg-blue-500 rounded-full flex items-center justify-center text-white
      [.router-link-exact-active_&]:bg-red-600"
    >
      {props.number}
    </span>

    {slots.default?.()}
  </MergeClass>
)
export const StrategyGroupCreateLayout = createComponent<StrategyGroupCreatePageOptions>({
  props: {},
}, props => {
  const { formData, validateAllSteps, stepStatusList } = useStrategyGroupStore()
  const isDataReady = ref<boolean>(false)

  watch(() => formData.value, () => {
    if (isDataReady.value) {
      void router.push({ path: router.currentRoute.value.path, query: { ...router.currentRoute.value.query, has_edit: 'true' } })
    }
  }, {
    deep: true,
  })

  const { id } = getQueriesWithParser({ id: queryParsers.string('') })
  const route = useRoute()
  watch(() => route.query.id,
    async v => {
      if (v) {
        isDataReady.value = false
        const res = await apiGetStrategyGroupDetails({ id })
        if (!res.data) return
        formData.value = res.data
        setTimeout(() => {
          isDataReady.value = true
        })
      } else {
        isDataReady.value = true
      }
    }, { immediate: true })
  const navList = [
    ['目标条件', 'target'],
    ['商品配置', 'product'],
    // ['任务计划', 'plan'],
  ]
  const onClickNav = (i: number) => (e: Event) => {
    validateAllSteps()
    if (i === 0) return
    if (stepStatusList.value[i - 1] !== 'valid') {
      e.stopPropagation()
      e.preventDefault()
    }
  }

  onUnmounted(() => {
    isDataReady.value = false
  })

  return () => (
    <x-strategy-group-create-page class="block my-4">
      <header class="flex justify-start items-center p-4 gap-4 sticky top-top-bar left-0 z-up bg-white border-b">
        <div class="breadcrumbs text-sm">
          <ul>
            <li><RouterLink to="/strategy-group">策略组</RouterLink></li>
            <li>{route.query.id ? '编辑策略组' : '新建策略组'}</li>
          </ul>
        </div>
      </header>
      <div class="relative flex divide-x items-start justify-start flex-1 overflow-hidden">
        <aside class="w-64 bg-white grow-0 shrink-0 sticky top-0 left-0
          flex justify-center items-start min-h-40 py-20"
        >
          <x-list class="block">
            {navList.map(([name, path], i) => (
              [
                i !== 0 && <hr class="border-t-0 border-b-0 border-l-0 border-r border-solid border-gray-500 w-0 h-10 ml-4" />,
                <RouterLink to={{ path, query: { ...route.query } }}>
                  <Item number={i + 1} onClick={onClickNav(i)}>
                    {name}
                  </Item>
                </RouterLink>]
            )).flat()}
          </x-list>
        </aside>
        <main class="bg-white  grow-1 shrink-1 flex-1 overflow-hidden">
          <RouterView />
        </main>
      </div>

    </x-strategy-group-create-page>
  )
})

export default StrategyGroupCreateLayout
