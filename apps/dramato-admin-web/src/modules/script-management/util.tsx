import { showFailToast, Tooltip } from '@skynet/ui'
import { auditStatus } from './constant'
import { trim } from 'lodash-es'

const getColor = (value: number) => {
  const colorMap: Record<number, string> = {
    1: 'badge-primary',
    2: 'badge-error',
    3: 'badge-default',
    4: 'badge-success',
    5: 'badge-primary',
    6: 'badge-error',
    7: 'badge-default',
    8: 'badge-success',
    9: 'badge-primary',
    10: 'badge-error',
    11: 'badge-default',
    12: 'badge-success',
  };
  return colorMap[value] || 'default';
}

export const statusDesc = (value: number) => {
  const numericValue = Number(value);
  const isValidStatus = numericValue in auditStatus;

  return (
    <div class={`badge ${getColor(numericValue)}`}>
      {isValidStatus ? auditStatus[numericValue as keyof typeof auditStatus] : ''}
    </div>
  );
}

export const openFile = (url: string) => {
  const extension = url.split('.').pop()
  console.log('extension', extension, url)
  if (extension === 'pdf') {
    window.open(url, '_blank')
  } else if (extension === 'docx') {
    window.open(`https://view.officeapps.live.com/op/embed.aspx?src=${url}`, '_blank')
  } else if (extension === 'doc') {
    window.open(`https://view.officeapps.live.com/op/embed.aspx?src=${url}`, '_blank')
  } else {
    showFailToast('不支持的文件类型')
  }
}

export const auditStatusShow = [
  {
    value: 1,
    label: '待审核',
  },
  {
    value: 2,
    label: '审核拒绝',
  },
  {
    value: 3,
    label: '放弃',
  },
  {
    value: 4,
    label: '审核通过',
  },
]

export const renderStatusDesc = (row: M.IScriptManageListItem) => {
  const label = auditStatusShow.find(item => item.value === row.audit_status)?.label || '无'
  const color = {
    1: 'badge-primary',
    2: 'text-red-500',
    3: 'text-gray-500',
    4: 'text-green-500',
  }[row.audit_status || 0] || ''

  if (row.audit_status === 2) {
    return (
      <Tooltip popWrapperClass="!max-w-[400px]" popContent={() => row.audit_failed_reason}>
        <div class="overflow-hidden text-red-500 tooltip line-clamp-1">
          <span class="text-[var(--text-3] text-xs">审核驳回：{row.audit_failed_reason}</span>
        </div>
      </Tooltip>
    )
  } else {
    return <div class={`badge badge-outline ${color}`}>{label}</div>
  }
}

export const trimSplitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`)).map(str => str.replace(new RegExp(`^[${[' '].join('')}]+|[${[' '].join('')}]+$`, 'g'), ''))

export const checkEpisodeTitleOrId = (value: string) => {
  if (!value) return []
  // 如果数组中list每个元素都为数字，且大于 剧集id符合是长度为10的字符串 没有特殊符号
  const trimSplit = trimSplitIt([',', ';', '，', ' ', '；', '\t'])
  const list = trimSplit(value)
  const isEpisodeIds = list.filter(item => {
    return /^[a-zA-Z0-9]{10}$/.test(item)
  })
  if (isEpisodeIds.length > 0) {
    return isEpisodeIds
  } else {
    return [trim(value)]
  }
}

/**
 * @description 获取审核阶段
 * @param row
 * @returns 返回审核阶段
 */
export const getAuditPeriod = (row: M.IScriptManageListItem) => {
  const isAuditableStatus = [1, 5, 9]
  const audit_status = row.audit_status
  const isAuditable = isAuditableStatus.includes(audit_status)
  if (audit_status < 5) {
    return {
      value: 1,
      label: '一审',
      isAuditable,
      auditStatus: audit_status,
    }
  } else if (audit_status < 9) {
    return {
      value: 2,
      label: '二审',
      isAuditable,
      auditStatus: audit_status,
    }
  } else {
    return {
      value: 3,
      label: '终审',
      isAuditable,
      auditStatus: audit_status,
    }
  }
}
