import { createComponent } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { FormItems } from '@skynet/ui/form/form-types'
import { ref } from 'vue'
export const NestedFormItems = createComponent(null, () => {
  type Data = { a: string, b: string, c: string, d: string }
  const Form = CreateForm<Data>()
  const data = ref<Data>({ a: 'a', b: 'b', c: 'c', d: 'd' })
  const items: FormItems = [
    ['第一层', 'a', { type: 'text' }],
    [
      'border border-gray-800 p-4 flex-col',
      ['第二层', 'b', { type: 'text' }],
      [
        'border border-gray-800 p-4 flex-col',
        ['第三层', 'c', { type: 'text' }],
        [
          'border border-gray-800 p-4 flex-col',
          ['第四层', 'd', { type: 'text' }],
        ],
      ],
    ],
  ]
  return () => (
    <Form data={data.value} items={items} class="flex flex-col gap-y-2 max-w-[40em]" />
  )
})

export default NestedFormItems
