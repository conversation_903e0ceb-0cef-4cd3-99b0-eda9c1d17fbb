export const ja = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: '人気の選択',
    },
  },
  myList: {
    watchHistory: `視聴履歴`,
    myList: 'マイリスト',
    seeAll: 'すべて見る',
    noData: '一致する情報は見つかりませんでした',
  },
  // 分享页
  share: {
    watchNow: '今すぐ見る',
    episodeDialog: {
      title: '一緒に参加して追加のコインをゲットしよう！',
      description: 'このドラマに夢中です！',
      tips: 'おすすめ',
      button: 'ドラマをフルで見る',
      limit24: '限られた時間：24h',
    },
    toast: {
      takeOffShelf: 'コンテンツがありません',
    },
  },
  'customer-service-center': {
    title: 'カスタマーサービスセンター',
    description: '説明',
    'description-placeholder': 'できる限り詳しくご説明ください',
    'email-placeholder': 'フィードバックの最新情報を受け取るには、メールアドレスをご入力ください。',
    'submit-feedback': '送信',
    'enter-feedback': 'その他の問題のフィードバック',
    'upload-pictures': '画像のアップロード',
    feedback: 'フィードバック',
    'problem-type': '問題の種類',
    'problem-type-placeholder': '選択してください',
    'payment-issue-placeholder': 'IDと注文の領収書を提供してください：Google: https://pay.google.com/ （注文番号はGPAで始まります）iOS: App Store - Apple ID - 購入履歴（注文番号）',
    'content-issue-placeholder': 'ドラマのタイトル、エピソード番号、および欠けている字幕の言語を提供してください。',
    'ads-issue-placeholder': 'デバイスのモデル、国、言語設定を提供してください。',
    'technical-issue-placeholder': 'デバイスのモデル、国、言語設定を提供してください。',
    'other-issue-placeholder': '詳細情報を提供してください。デバイスの型番と国名も含めてお知らせください。',
    'i-want-to-give-feedback': 'フィードバックする',
    'are-you-satisfied-with-this-reply': 'この回答にご満足いただけましたでしょうか？',
    unsatisfied: '不満',
    satisfied: '満足',
    myFeedback: '私のフィードバック',
    'reply-to-feedback': 'フィードバックに返信する',
    'description-min-length': '問題の説明を入力してください（6文字以上）。',
    'problem-type-not-choose': '問題の種類を選択してください。',
    'description-empty': '入力は空白にできません。',
  },
  terminate: {
    question: 'サブスクリプションはどうやって管理しますか？',
    desc: '下のボタンをクリックして契約管理ページに進み、契約切り替えでサブスクリプションを管理できます。',
    title: '契約管理',
    expiration: '有効期限',
    manageDesc: '契約状態',
  },
  toast: {
    'upload-failed': 'アップロードに失敗、再提出してください',
    'submit-success': '送信完了',
    'email-incorrect': 'メールのフォーマットが正しくありません',
  },
  not_found: {
    title: 'お探しのページは存在しません',
  },
  regulations: {
    howUseData: 'データの使用方法',
    content: `アプリトラッキング透明性の許可リクエストで「許可」を選択した場合、私たちはあなたの情報とデータを使用してサービスを最適化し、最高の体験を提供します。そうでない場合、データを収集するだけで、追跡活動は行いません。

DramaWaveおよび私たちのパートナーやサプライヤーが、アプリケーションの使用方法を分析し、コンテンツの推奨をパーソナライズし、情報関連のサービスを提供するために、デバイスからのデータを使用することに同意する場合は、「同意して続行」をクリックしてください。`,
    acceptAll: 'すべて受け入れる',
    dataSettings: 'データ設定',
    next: '次へ',
    cancel: 'キャンセル',
    aboutYourPrivacy: 'プライバシーについて',
    personalDataName: 'パーソナライズされたデータ',
    personalDataDescription: 'この情報を使用して、選択を記憶し、パーソナライズされた広告やコンテンツの推奨、通知、リマインダーなどを提供します。',
    advertisementDataName: '広告データ',
    advertisementDataDescription: 'オンライン広告の目的で、クッキー、デバイスの識別子、またはその他の情報がデバイスに保存またはアクセスされることがあります。ユーザーデータは、広告パートナーと共有されることもあります。',
    behaviorsDataName: '行動データ',
    behaviorsDataDescription: 'クッキーやデバイス識別子などの分析関連データは、使用状況、行動、広告のパフォーマンスを測定および分析するために保存されることがあります。',
    privacyAndTerms: 'プライバシーポリシーと利用規約',
    referToPrivacy: 'プライバシーポリシーをご参照ください',
    referToTerms: '利用規約をご参照ください',
    agreeAndSave: '同意して保存',
  },
  coins: {
    earnings: {
      title: '収入',
      total: 'トータルダイヤモンド',
      exchange: '交換',
      earningsHistory: '収入履歴',
      exchangeHistory: '交換履歴',
      noRecords: '記録なし',
      last50earnings: '表示されるのは最後の50件の収入のみです',
      last50exchanges: '表示されるのは最後の50件の交換履歴のみです',
      benefits: '特典',
      gotIt: '了解',
    },
    exchange: {
      title: '交換ゾーン',
      notOpen: 'まだオープンしていません、ご期待ください。',
      receiveBtn: '幸運なお知らせ',
      earnCoins: 'ダイヤモンドをもらう',
      redeemRequiredXCoins: '交換するに{X}ダイヤモンドが必要',
      exchangeSuccess: '交換に成功',
      cardId: 'カードID',
      exchangeFailed: '交換に失敗',
      transferFailedInsufficientStock: 'ストック不足、明日に早めに来てね',
      gotItBtn: '了解',
      transferFailedRiskControl: 'アカウント異常、しばらく交換できません',
      tryAgainBtn: '後でお試しください',
      transferConfirm: '転送を確認する',
      paypalAccountEmail: 'PayPalアカウントメールアドレス',
      confirmPaypalAccountEmail: 'PayPalアカウントメールアドレスを確認する',
      transferX: '{X}振り込む',
      transferXConfirm: '{X}振込を確認',
      transferToYourAccountX: 'あなたのアカウントに{X}を振り込む',
      confirmBtn: '確認',
      redeemFailed: '引き換えに失敗しました',
      redeemFailedContent: 'エラーが発生しました。後ほど再試行してください。',
      transferSuccessTitle: 'お金をあなたのに送っています！',
      transferSuccessContent: 'お金は5分から72時間以内に到着します。',
      copySuccess: 'コピー成功',
      enterEmailError: '有効なメールアドレスを入力してください。',
      enterEmailValidError: '入力エラーです、再度入力してください',
      emailNotMatchError: 'メールアドレスが一致しません、再度入力してください',
    },
  },
  meal_check_in: {
    sleeping_link: '寝てダイヤモンドを獲得可能',
    rules_title: 'アクティビティルール',
    benefits_title: 'Benefits',
    rules_1: '1. 毎晩19時から24時まで、「寝てお金を稼ぐ」モードをオンにできます。',
    rules_2: '2. モードをオンにした翌日、8時間寝た後、8時から12時まで寝る報酬を受け取ることができます。',
    rules_3: '3. 報酬の受け取り時間を逃した場合、翌日の12時から19時までに動画を視聴して報酬を再取得できます。',
  },
  sleep_check_in: {
    earn_link: '食事を記録して獲得',
    benefits_title: 'Benefits',
    rules_1: '1. 毎日、食事の記録には4つの時間帯があります。朝食：07:00-10:00、昼食：11:00-14:00、夕食：17:00-20:00、夜食：21:00-24:00。',
    rules_2: '2. 対応する時間を逃した場合、ビデオを見て報酬を回収できます。',
    rules_3: '3. 報酬は毎日深夜にリセットされるので、忘れずに報酬を受け取ってください。',
  },
  bind_email: {
    title: 'メールアドレスをご入力ください',
    list_1: 'メールアドレスを登録すると、専用の報酬コインがもらえます。',
    list_2: '今後開催予定のイベントや特別オファーの通知を受け取る。',
    list_3: '新しいエピソードをいち早くチェックする。',
    email_placeholder: 'メールアドレスをご入力ください。',
    verify: '認証',
    email_error: '有効な電子メールアドレスを入力してください',
    verify_progress: 'メールアドレス認証中',
    verify_success: '受取に成功！',
    re_enter: '再度入力',
    email_repeat: 'メールアクティビティページ このメールアドレスはすでにイベントに参加しました',
  },
  infinityScroll: {
    end: 'The End',
    loading: '読み込み中...',
  },
}
