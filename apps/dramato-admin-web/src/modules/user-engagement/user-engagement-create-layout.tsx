import { createComponent, getQueriesWithParser, queryParsers } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { FunctionalComponent, onUnmounted, ref, watch } from 'vue'
import { RouterLink, RouterView, useRoute } from 'vue-router'
import { apiNewUserStrategyDetail } from './user-engagement-api'
import { userEngagementStore } from './user-engagement-store'
type userEngagementCreatePage = {
  props: {}
}

const Item: FunctionalComponent<{ number: number }, { click: (e: Event) => void }> = (props, { slots, emit }) => (
  <MergeClass tag="x-item" baseClass="flex items-center gap-2" onClick={e => emit('click', e)}>
    <span class="flex size-8 items-center justify-center rounded-full bg-blue-500 text-white
      [.router-link-exact-active_&]:bg-red-600"
    >
      {props.number}
    </span>

    {slots.default?.()}
  </MergeClass>
)
export const userEngagementCreateLayout = createComponent<userEngagementCreatePage>({
  props: {},
}, props => {
  const { formData, resetFormData } = userEngagementStore()

  const { id } = getQueriesWithParser({ id: queryParsers.int(0) })
  const route = useRoute()
  watch(() => route.query.id,
    async v => {
      if (v) {
        resetFormData()
        const res = await apiNewUserStrategyDetail({ id })
        if (!res.data) return
        formData.value = res.data
      } else {
        resetFormData()
      }
    }, { immediate: true })
  const navList = [
    ['目标条件', 'target'],
    ['策略配置', 'strategy'],
  ]
  const onClickNav = (i: number, name: string) => (e: Event) => {
    // console.log(i, name)
    // if (name === '策略配置') {
    //   if (!validateStepOne()) return
    // }
  }
  return () => (
    <x-user-engagement-create-page class="flex h-full flex-col py-4">
      <header class="top-top-bar z-up sticky left-0 flex items-center justify-start gap-4 border-b bg-white p-4">
        <div class="breadcrumbs text-sm">
          <ul>
            <li><RouterLink to="/new-user-engagement">新用户承接策略</RouterLink></li>
            <li>{route.query.id ? '编辑' : '新建'}</li>
          </ul>
        </div>
      </header>
      <div class="relative flex h-full flex-1 items-start justify-start divide-x overflow-hidden">
        <aside class="sticky left-0 top-0 flex min-h-40 w-64 shrink-0
          grow-0 items-start justify-center bg-white py-20"
        >
          <x-list class="block">
            {navList.map(([name, path], i) => (
              [
                i !== 0 && <hr class="ml-4 h-10 w-0 border-y-0 border-l-0 border-r border-solid border-gray-500" />,
                <RouterLink to={{ path, query: { ...route.query } }}>
                  <Item number={i + 1} onClick={onClickNav(i, name)}>
                    {name}
                  </Item>
                </RouterLink>]
            )).flat()}
          </x-list>
        </aside>
        <main class="grow-1 shrink-1 h-full flex-1 overflow-hidden bg-white">
          <RouterView />
        </main>
      </div>

    </x-user-engagement-create-page>
  )
})

export default userEngagementCreateLayout
