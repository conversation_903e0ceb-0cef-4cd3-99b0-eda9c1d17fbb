import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, showSuccessToast, transformInteger } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import { ref } from 'vue'
import { apiRechargeBonus } from './tools-api'
import { set } from 'lodash-es'
import { z } from 'zod'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { RouterLink } from 'vue-router'

interface FormInfo {
  user_id?: number // 用户ID
  bonus?: number // 金币
}

export const RechargeBonusPag = createComponent(null, () => {
  const Form = CreateForm<FormInfo>()
  const formInfo = ref<FormInfo>({
    bonus: 0,
  })

  const formRules = z.object({
    user_id: z.number({ message: '请输入用户ID' }),
    bonus: z.number({ message: '请输入金币数' }).min(1, '最小1'),
  })

  const { error, validateAll } = useValidator(formInfo, formRules)

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li><RouterLink to="/tools/recharge_bonus">充值金币</RouterLink></li>
          </ul>
        ),
        form: () => (
          <x-recharge-bonus-form class="flex flex-col gap-y-3 pb-4">
            <Form
              class="grid gap-y-3 grid-cols-2 flex-1"
              hasAction={false}
              error={error.value}
              onChange={(path, value) => {
                set(formInfo.value || {}, path, value)
              }}
              items={[
                {
                  label: requiredLabel('用户ID'),
                  path: 'user_id',
                  input: {
                    type: 'number',
                    placeholder: '请输入用户ID',
                  },
                  transform: transformInteger,
                },
                {
                  label: requiredLabel('金币数'),
                  path: 'bonus',
                  input: {
                    type: 'number',
                    placeholder: '请输入整数1-99999',
                    min: 1,
                    max: 99999,
                    suffix: <>个</>,
                  },
                  transform: transformInteger,
                },

              ]}
              data={formInfo.value}
            />
            <x-recharge-bonus-actions class="flex justify-end gap-x-2">
              <Button class="btn btn-primary btn-sm" onClick={async () => {
                if (!validateAll({ exclude: [] })) {
                  return
                }

                await apiRechargeBonus({
                  user_id: formInfo.value.user_id || 0,
                  bonus: formInfo.value.bonus || 0,
                })

                showSuccessToast('操作成功')
              }}
              >确定
              </Button>
            </x-recharge-bonus-actions>
          </x-recharge-bonus-form>
        ),
      }}
    </NavFormTablePager>
  )
})

export default RechargeBonusPag
