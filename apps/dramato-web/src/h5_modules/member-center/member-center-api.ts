// import { h5HttpClient } from 'src/lib/http-client'
import { encryptedHttpClient } from 'src/lib/encrypted-http-client'
import { StoreDataInfo } from 'src/h5_modules/store/store'
import { VipDataInfo } from './member-center'

export const apiVipCenterData = () => {
  return encryptedHttpClient.get<ApiResponse<VipDataInfo>>(`h5-api/wallet/vip/center`)
}

export const apiProductList = () => {
  return encryptedHttpClient.get<ApiResponse<StoreDataInfo>>(`h5-api/wallet/product/list?scene=store`)
}
