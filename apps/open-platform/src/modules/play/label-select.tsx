import { createComponent, fn, mc } from '@skynet/shared'
import { useLabelList } from '../label/use-label-list'
import { onMounted, ref } from 'vue'
import { <PERSON><PERSON>, <PERSON><PERSON>Footer, Tab } from '@skynet/ui'
import { labelTypes } from 'src/lib/constant'
import { Fn } from '@vueuse/core'
import { useI18n } from 'vue-i18n'

type LabelSelectOptions = {
  props: {
    modelValue: number[] // 支持 modelValue
  }
  emits: {
    'update:modelValue': (value: number[]) => void
    close: Fn
  }
}

export const LabelSelect = createComponent<LabelSelectOptions>({
  props: {
    modelValue: [],
  },
  emits: {
    'update:modelValue': fn,
    close: fn,
  },
}, (props, { emit }) => {
  const { labelList, fetchLabelList } = useLabelList()
  const tab = ref(labelTypes[0].value.toString())
  const selectedLabels = ref<number[]>([...props.modelValue]) // 内部维护 modelValue
  const { t } = useI18n()

  onMounted(() => {
    void fetchLabelList()
  })

  const onSelect = (labelId: number) => {
    if (selectedLabels.value.includes(labelId)) {
      selectedLabels.value = selectedLabels.value.filter(id => id !== labelId)
    } else {
      selectedLabels.value.push(labelId)
    }
  }

  const onConfirm = () => {
    emit('update:modelValue', selectedLabels.value)
    emit('close')
  }

  return () => (
    <x-label-select>
      <div class="flex gap-8">
        <Tab
          class="flex shrink-0 grow-0 flex-col gap-0"
          itemClass={selected => mc('py-4', selected ? 'cursor-default text-brand-6' : 'cursor-pointer text-text-1')}
          items={labelTypes.map(item => [item.value.toString(), () => t(item.label)])}
          modelValue={tab.value.toString()}
          onUpdate:modelValue={v => tab.value = v}
        />
        <main class="grid max-h-[20em] flex-1 grid-cols-[repeat(auto-fill,minmax(160px,1fr))] grid-rows-[repeat(auto-fill,46px)] gap-2 overflow-auto">
          {labelList.value.filter(item => item.content_type === Number(tab.value)).map(item => (
            <x-label key={item.label_id}
              onClick={() => onSelect(item.label_id)}
              class={
                mc('flex items-center justify-center rounded-lg px-5 py-3 text-sm leading-[22px]',
                  selectedLabels.value.includes(item.label_id) ? 'text-brand-6 bg-brand-1 ' : 'bg-fill-1 text-text-1')
              }>
              {item.content}
            </x-label>
          ))}
        </main>
      </div>
      <x-actions class="flex justify-end gap-4">
        <Button class="btn btn-secondary btn-md px-8" onClick={() => emit('close')}>{t('common.cancel')}</Button>
        <Button class="btn btn-md px-8" onClick={onConfirm}>{t('common.confirm')}</Button>
      </x-actions>
    </x-label-select>
  )
})

export default LabelSelect
