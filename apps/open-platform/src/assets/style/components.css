.btn {
  @apply bg-[var(--btn-bg)] text-white px-5 py-[13px] leading-5 rounded-lg whitespace-nowrap text-ellipsis font-bold text-base
   border border-[transparent];
  &:hover {
    @apply bg-[var(--btn-bg-hover)];
  }
  &:active {
    @apply bg-[var(--btn-bg-active)];
  }
  &-sm {
    @apply py-[5px] text-sm px-4 font-normal;
  }
  &-md {
    @apply py-[11px] text-sm px-4 font-normal;
  }
  &[disabled] {
    @apply opacity-30 cursor-not-allowed;
  }
  &-ghost {
    @apply bg-transparent border border-solid border-[var(--btn-bg)] text-[var(--btn-bg)];
    &:hover {
      @apply bg-transparent shadow-sm;
    }
    &:active {
      @apply bg-transparent shadow-sm;
    }
  }
  /* 次要按钮 */
  &-secondary {
    @apply bg-[var(--btn-secondary-bg)] text-[#434546];
    &:hover {
      @apply bg-[var(--btn-secondary-hover)];
    }
    &:active {
      @apply bg-[var(--btn-secondary-active)];
    }
    &.btn-ghost {
      @apply bg-transparent border border-solid border-[var(--btn-secondary-bg)] text-[var(--text-3)];
    }
  }
  /* info按钮 */
  &-info {
    @apply bg-[var(--btn-info-bg)] text-[var(--btn-info-text)];
    &:hover {
      @apply bg-[var(--btn-info-hover)];
    }
    &:active {
      @apply bg-[var(--btn-info-active)];
    }
    &.btn-ghost {
      @apply bg-transparent border border-solid border-[var(--btn-info-ghost-border)] text-[var(--btn-info-ghost-text)];
    }
  }
}
.hide-scrollbar {
  -ms-overflow-style: none; /* for Internet Explorer, Edge */
  scrollbar-width: none; /* for Firefox */
}
.hide-scrollbar::-webkit-scrollbar {
  @apply hidden;
}

.alert {
  @apply bg-white py-3 px-6 rounded-lg flex items-center gap-2;
}

x-play-progress {
  @apply text-[var(--text-3)] block;
  > ol {
    @apply flex flex-nowrap justify-between items-center text-center my-4
      [&>li]:pt-6 [&>li]:relative [&>li]:flex-1 [&>li]:before:content-[''] [&>li]:before:bg-[var(--fill-3)] [&>li]:before:size-2
      [&>li]:before:rounded-full [&>li]:before:absolute [&>li]:before:z-up [&>li]:before:top-2 [&>li]:before:left-1/2 [&>li]:before:transform
      [&>li]:before:translate-x-[-50%] [&>li]:after:content-[''] [&>li]:after:absolute [&>li]:after:top-[11px] [&>li]:after:-left-1/2
      [&>li]:after:border-t-2 [&>li]:after:border-dashed [&>li]:after:w-full [&>li:first-child]:after:hidden [&>li.green]:before:bg-[#1DD864] [&>li.green]:before:border [&>li.green]:before:border-white [&>li.green]:after:border-[#1DD864] [&>li.green]:after:border-solid [&>li.green]:after:border
      [&>li.red]:before:bg-red-500 [&>li.red]:before:size-3 [&>li.red]:text-[var(--brand-6)] [&>li.red]:before:-translate-y-0.5
      [&>li.end]:before:size-3 [&>li.end]:before:-translate-y-0.5
      ;
  }
}
.page-btn {
  @apply py-1 px-3 rounded-md bg-[var(--fill-1)] text-[var(--text-3)] text-center hover:text-[var(--text-1)] border-transparent border leading-6
}
.page-btn-active {
  @apply !bg-[var(--brand-1)] !text-[var(--brand-6)] !border-[var(--brand-6)]
}
.cp-form {
  @apply [&_x-label]:shrink-0  [&_x-label]:leading-[38px]
    [&_x-error]:text-[var(--brand-6)] [&_x-error]:text-xs [&_x-error]:mt-2
    [&_input.is-error]:border [&_input.is-error]:border-[var(--brand-6)]
    [&_textarea.is-error]:border [&_textarea.is-error]:border-[var(--brand-6)];
}
.cp-form-item {
  @apply gap-x-2 !grid grid-areas-["label_input""label_error"] grid-cols-[min-content_1fr]
    [&_x-input-wrapper]:h-[38px]
    [&_x-label]:grid-in-[label] [&_x-input-wrapper]:grid-in-[input] [&_x-error]:grid-in-[error];
}