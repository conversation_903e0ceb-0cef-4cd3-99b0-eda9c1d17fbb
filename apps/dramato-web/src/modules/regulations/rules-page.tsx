import { createComponent, getQuery } from '@skynet/shared'
import { useLocale } from 'src/lib/use-locale'
import right from './source/right.svg'
import { Button } from '@skynet/ui'
import { jsBridge } from 'src/lib/jsbridge'
import { useRouter } from 'vue-router'
type RulesPageOptions = {
  props: {}
}
export const RulesPage = createComponent<RulesPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const language = getQuery('language', 'en')
  const country = getQuery('country_code', 'US')
  const { t } = useLocale(language)
  return () => (
    <div class="size-full bg-[var(--black)] text-[var(--white)]">
      <div class="border-b-solid border-b border-b-[var(--grey-10)] px-3 py-5 text-center text-base leading-tight text-[var(--white)]">{t('regulations.privacyAndTerms')}</div>
      <x-nav class="flex flex-col text-sm text-[var(--white)]">
        <x-nav-item class="border-b-solid flex justify-between gap-3 border-b border-b-[var(--grey-10)] px-3 py-4" onClick={() => location.href = `/rules/privacy?language=${language}&country_code=${country}`}>
          <span>{t('regulations.referToPrivacy')}</span>
          <img src={right} class="size-5" />
        </x-nav-item>
        <x-nav-item class="border-b-solid flex justify-between gap-3 border-b border-b-[var(--grey-10)] px-3 py-4" onClick={() => location.href = `/rules/terms?language=${language}&country_code=${country}`}>
          <span>{t('regulations.referToTerms')}</span>
          <img src={right} class="size-5" />
        </x-nav-item>
      </x-nav>
      <x-bottom class="z-1000 pc:w-[calc(var(--phone-page-max-width))] pad:w-[calc(var(--phone-page-max-width))] share-bottom-8 fixed bottom-0 flex w-full flex-col items-center justify-center gap-2 bg-[var(--black)] p-3 text-base">
        <Button class="flex h-11 w-full items-center justify-center rounded-lg border-none bg-[var(--brand-6)] text-[var(--white)]" onClick={() => {
          if (jsBridge.inDramawaveApp) {
            const consentStr = window.localStorage.getItem('consent')
            const consentFromStorage = consentStr ? JSON.parse(consentStr) as Consent.Consent : null
            void jsBridge('google_analytics_consent_status', { ...consentFromStorage, close_page: true })
          } else {
            router.back()
          }
        }}
        >{t('regulations.agreeAndSave')}
        </Button>
      </x-bottom>
    </div>
  )
})

export default RulesPage
