/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { RouterLink } from 'vue-router'
import { CreateTableOld, Checkbox, TableColumnOld, Button, SvgIcon, Icon, Input, RadioGroup } from '@skynet/ui'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { ResourceForm } from './components/resource-form'
import { useCheck } from './use-check'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, onUnmounted, ref } from 'vue'
import { openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { Subtitle } from './components/subtitle'
import { useResourceStore } from './use-resource-store'
import { apiGetResourceDetail, apiSeriesResourceList, apiEpisodeDelete, apiAudioTrans, apiSubtitleDel, apiEraseAudio, apiSubtitleExtraction, apiSubtitleTrans, apiSubtitleInject, apiResourceSync, apiUploadSaveSubtitle, apiGetRecordList, apiUploadSaveAudio, apiSeparateAudio, apiFixSubtitle, apiAudioTransCode, apiExternalSubtitleRegion, apiRetryTrans } from './resource-api'
import { useRoute } from 'vue-router'
import { langKey, langValue, langKeyForCol, defaultClipValue } from './constant'
import { Clip } from './components/clip'
import { useUploader } from 'src/modules/common/uploader/use-uploader'
import { AuthButton } from './components/auth-button'
import axios from 'axios'
import { requiredLabel } from 'src/lib/required-label'
import { useMenu } from 'src/modules/menu/use-menu'
import dayjs from 'dayjs'
import { M3u8Player } from './components/m3u8-player'
import { getUuid } from './util'
import { getSubtitleContent } from 'src/modules/common/subtitle/until'

type ResourceDetailPageOptions = {
  props: {}
}

const PROCESSING = 'processing'
const TRANS_ERROR = 'trans_failed'
const REMOVE_ERROR = 'failed'
const PURE_ERROR = 'failed'
const EXTRACT_ERROR = 'extract_failed'
const HISTORY_EXTRACT_ERROR = 'failed'

export const ResourceDetailPage = createComponent<ResourceDetailPageOptions>({
  props: {},
}, props => {
  const route = useRoute()
  const { getRole } = useMenu()

  const { onUploadVideo, onUploadCaption, onDiskUpload, onPreviewVideo, onUploadAudio, getEpisodeSeriesInfoList, episodeSeriesInfoList, updateEpisodeSeriesInfoList, findMissingNumbers, makeUpList, showColumnHeader, setMissingData, getTags, uploadSyncList, refreshSyncList, showSyncSummary, getTranscodeVideos, externalTransProgress } = useResourceStore()
  const { getResourceOssData, ossDataBj } = useUploader()
  const role = getRole() as string
  const resourceDetail = ref<M.ISourceInfo & { series_resource_id?: number }>({
    series_resource_id: undefined,
    count: undefined,
    title: '',
    release_round: 0,
    resource_type: 0,
    unlocked_episodes: 0,
    serialize_status: 0,
  })
  const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()
  const list = ref<M.IResourceDrama[]>([])
  // 字幕修改日志
  const subtitleLogs = ref<M.IAlterSubtitle[]>([])
  const loading = ref(false)
  const seriesInfoCommitLoading = ref(false)
  const total = ref(0)
  const Table = CreateTableOld<M.IResourceDrama>()
  let dynamicColumns: TableColumnOld<M.IResourceDrama>[] = []
  const columns = ref<any[]>([])
  const selectCols = ref<string[]>([])
  const queryForm = ref<M.IResourceSeriesParams>({
    series_resource_id: +route.params.id,
    page_index: 1,
    page_size: 300, // 选填参数
  })
  const { checked, isCheckAll, checkedAllChange, checkboxChange, resetChecked } = useCheck()
  const previewVideo = (path: string) => {
    onPreviewVideo(path)
  }

  const getOriginalSubtitleContent = async (row: M.IResourceDrama, index: number) => {
    const path = row[`${langKeyForCol[index]}_subtitle_path`]
    const content = await getSubtitleContent(path)
    return content
  }

  const getRefSubtitleContent = async (row: M.IResourceDrama, lang: string) => {
    const cnPath = row[`${lang}_subtitle_path`]
    const content = await getSubtitleContent(cnPath)
    return content
  }

  const previewAudio = (path: string) => {
    openDialog({
      title: '音频查看',
      mainClass: 'px-4 !py-0',
      customClass: '!w-[400px]',
      body: () => <audio src={path} controls />,
    })
  }

  const previewM3U8 = (obj: M.IExternalTransProgress, row: M.IResourceDrama) => {
    const subtitles = langKeyForCol.filter((item, index) => {
      return row[`${langKeyForCol[index]}_subtitle_path`] && row[`${langKeyForCol[index]}_subtitle_path`].indexOf('http') === 0
    }).map((key, index) => {
      return {
        language: key,
        type: 'normal',
        subtitle: row[`${langKeyForCol[index]}_subtitle_path`],
      }
    })
    openDialog({
      title: `第${obj.serial_number}集`,
      mainClass: 'px-4 !py-0',
      customClass: '!w-[400px]',
      body: () => (
        <M3u8Player subtitles={subtitles} url={obj.play_path as string} region={row.region} />
      ),
    })
  }

  const previewSubtitle = async (row: M.IResourceDrama, langIndex: number) => {
    const curRow = ref<M.IResourceDrama>(row)
    const cnSubTitle = await getRefSubtitleContent(curRow.value, 'cn')
    const content = await getOriginalSubtitleContent(curRow.value, langIndex)
    const obj = subtitleLogs.value.find(r => r.serial_number === row.serial_number && r.language_code === langKey[langIndex]) || null
    const oprObj = ref<M.IAlterSubtitle | null>(obj)
    // 左侧对照永远选择 中文
    const reference = ref({
      code: cnSubTitle,
      language: 'cn',
    })
    const original = ref({
      code: content,
      language: langValue[langIndex],
    })
    const loading = ref(false)
    const createDialog = openDialog({
      title: '字幕校对',
      canEscClose: false,
      mainClass: 'px-4 !py-0',
      hideParentWhenChildOpen: true,
      customClass: '!w-[1000px] !min-h-[60vh]',
      body: () => (
        <>
          <div>集数：{curRow.value.serial_number} </div>
          <Subtitle
            loading={loading.value}
            reference={reference.value}
            original={original.value}
            pageNum={curRow.value.serial_number}
            onClose={() => createDialog()}
            onLanguageChange={async (lang: string) => {
              const code = await getRefSubtitleContent(curRow.value, lang)
              reference.value = {
                code: code,
                language: lang,
              }
            }}
            onAfterSave={async ({ code, language }: { code: string, language: string }) => {
              loading.value = true
              const fileName = `${getUuid()}.srt`
              if (!ossDataBj.value) await getResourceOssData()
              const data = ossDataBj.value
              const blob = new Blob([code], { type: 'application/x-subrip' })
              const file = new File([blob], fileName, { type: 'application/x-subrip' })
              const f = new File([file], file.name, { type: file.name.endsWith('.srt') ? 'application/x-subrip' : file.type })
              const formData = new FormData()
              formData.append('key', data?.dir + '/' + f.name)
              formData.append('policy', data?.policy || '')
              formData.append('OSSAccessKeyId', data?.accessid || '')
              formData.append('success_action_status', '200')
              formData.append('signature', data?.signature || '')
              formData.append('file', f)

              const uploadRequest = async () => {
                return new Promise((resolve, reject) => {
                  void axios.post(data?.host || '', formData, {
                    headers: {
                      'Content-Type': 'multipart/form-data',
                    },
                  }).then(() => {
                    resolve(null)
                  }).catch((error: any) => {
                    if (error.response.status === 403) {
                      void getResourceOssData().then(uploadRequest).then(resolve)
                      return
                    }
                  })
                })
              }
              await uploadRequest()
              try {
                const subtitleInfo = {
                  file_path: data?.dir + '/' + fileName || '',
                  serial_number: curRow.value.serial_number,
                }
                await apiUploadSaveSubtitle({
                  data: [subtitleInfo],
                  series_resource_id: +route.params.id,
                  language_code: langKey[langIndex],
                  alter_type: 1,
                })
                void taskGetList()
                showSuccessToast('保存成功')
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                loading.value = false
              }
            }}
            v-slots={{
              default: () => (
                <div class="flex items-center">
                  <Button class="btn btn-xs btn-outline" disabled={curRow.value.serial_number === list.value[0].serial_number} onClick={async () => {
                    const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
                    const item = list.value[curIndex - 1]
                    const cnSubTitle = await getRefSubtitleContent(item, reference.value.language)
                    const content = await getOriginalSubtitleContent(item, langIndex)
                    // 左侧对照永远选择 中文
                    reference.value = {
                      code: cnSubTitle,
                      language: reference.value.language,
                    }
                    original.value = {
                      language: langValue[langIndex],
                      code: content,
                    }
                    oprObj.value = subtitleLogs.value.find(r => r.serial_number === item.serial_number && r.language_code === langKey[langIndex]) || null
                    curRow.value = item
                  }}
                  >上一页
                  </Button>
                  <div class="px-4">{ curRow.value.serial_number }</div>
                  <Button class="btn btn-xs btn-outline" disabled={list.value[list.value.length - 1].serial_number === curRow.value.serial_number} onClick={async () => {
                    const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
                    const item = list.value[curIndex + 1]
                    const cnSubTitle = await getRefSubtitleContent(item, reference.value.language)
                    const content = await getOriginalSubtitleContent(item, langIndex)
                    // 左侧对照永远选择 中文
                    reference.value = {
                      code: cnSubTitle,
                      language: reference.value.language,
                    }
                    original.value = {
                      language: langValue[langIndex],
                      code: content,
                    }
                    oprObj.value = subtitleLogs.value.find(r => r.serial_number === item.serial_number && r.language_code === langKey[langIndex]) || null
                    curRow.value = item
                  }}
                  >下一页
                  </Button>
                  <div class="ml-4">
                    { oprObj.value ? `${oprObj.value.operate_user}: ${dayjs(oprObj.value.operate_time * 1000).format('YYYY-MM-DD HH:mm:ss')}` : null }
                  </div>
                </div>
              ),
            }}
          />
        </>

      ),
    })
  }

  const filterCols = ref<{ value: string, label: string }[]>(dynamicColumns.map(row => {
    return {
      value: row[0] as string,
      label: row[0] as string,
    }
  }))

  langKey.map((row, index) => {
    const videoLabel = `${langValue[index]}`
    selectCols.value.push(videoLabel)

    filterCols.value.push({
      value: videoLabel,
      label: videoLabel,
    })
  })

  const deleteResource = (row: M.IResourceDrama, type: 1 | 2 | 3) => {
    console.log(row, 'IResourceDrama')
    const delLoading = ref(false)
    const closeDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: (
        <>
          <div>确认删除当前音频？</div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" disabled={delLoading.value} onClick={async () => {
              const subtitleInfos = [{
                file_path: '',
                serial_number: row.serial_number,
              }]
              delLoading.value = true
              try {
                await apiUploadSaveAudio({
                  data: subtitleInfos,
                  series_resource_id: +row.series_resource_id,
                  audio_type: type,
                })
                showSuccessToast('操作成功')
                closeDialog()
                void delayTaskGetList()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                delLoading.value = false
              }
            }}
            >
              {delLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              提交
            </Button>
          </div>
        </>

      ),
    })
  }

  const renderDynamicColumns = () => {
    dynamicColumns = []
    langKey.map((key, index) => {
      let w = 'w-[120px]'
      if (langValue[index].length + 2 > 7) {
        w = 'w-[140px]'
      } else if (langValue[index].length + 2 > 5) {
        w = 'w-[130px]'
      } else if (langValue[index].length + 2 > 4) {
        w = 'w-[125px]'
      }

      if (selectCols.value.length > 0 && selectCols.value.includes(`${langValue[index]}`)) {
        dynamicColumns.push([
          () => showColumnHeader(langKeyForCol[index], `${langValue[index]}`, true, (e: number[]) => {
            checked.value = [...e]
          }),
          (row: M.IResourceDrama) => {
            let videoButton = null
            let subtitleBtn = null
            let audioBtn = null
            const videoPath = row[`${langKeyForCol[index]}_episode_path`]
            if (videoPath === PROCESSING) {
              videoButton = <Button class="btn btn-xs btn-outline btn-accent">处理中</Button>
            } else if (videoPath === REMOVE_ERROR) {
              videoButton = <Button class="btn btn-xs btn-outline btn-error">合成失败</Button>
            } else {
              videoButton = videoPath ? <Button class="btn btn-active btn-xs btn-link" onClick={() => { previewVideo(videoPath) }}>视频</Button> : '-'
            }

            const subtitlePath = row[`${langKeyForCol[index]}_subtitle_path`]
            const obj = subtitleLogs.value.find(r => r.serial_number === row.serial_number && r.language_code === langKey[index])

            if (subtitlePath === PROCESSING) {
              subtitleBtn = <Button class="btn btn-xs btn-outline btn-accent">处理中</Button>
            } else if (subtitlePath === TRANS_ERROR) {
              subtitleBtn = (
                <>
                  <Button class="btn btn-xs btn-link text-red-500" onClick={() => { void previewSubtitle(row, index) }}>翻译错误</Button>
                  <div class="tooltip" data-tip="重试">
                    <SvgIcon class="cursor-pointer" name="ic_refresh" onClick={async () => {
                      try {
                        await apiRetryTrans({
                          series_resource_id: +route.params.id,
                          serial_number: row.serial_number,
                          language_code: langKey[index],
                          trans_type: 1,
                        })
                        delayTaskGetList()
                        showSuccessToast('操作成功')
                      } catch (error: any) {
                        showFailToast(error.response.data.message || '操作失败')
                      }
                      delayTaskGetList()
                    }}
                    />
                  </div>
                </>
              )
            } else if (subtitlePath === EXTRACT_ERROR || subtitlePath === HISTORY_EXTRACT_ERROR) {
              subtitleBtn = <Button class="btn btn-xs btn-link text-red-500">提取错误</Button>
            } else {
              subtitleBtn = subtitlePath ? <Button class="btn btn-active btn-xs btn-link" onClick={() => { void previewSubtitle(row, index) }}>{ obj && obj.operate_user ? '字幕-改' : '字幕'}</Button> : '-'
            }

            const audioPath = row[`${langKeyForCol[index]}_audio_path`]
            if (audioPath === PROCESSING) {
              audioBtn = <Button class="btn btn-xs btn-outline btn-accent">处理中</Button>
            } else if (audioPath === TRANS_ERROR) {
              audioBtn = (
                <>
                  <Button class="btn btn-xs btn-link text-red-500" onClick={() => { void previewSubtitle(row, index) }}>翻译错误</Button>
                  <div class="tooltip" data-tip="重试">
                    <SvgIcon class="cursor-pointer" name="ic_refresh" onClick={async () => {
                      try {
                        await apiRetryTrans({
                          series_resource_id: +route.params.id,
                          serial_number: row.serial_number,
                          language_code: langKey[index],
                          trans_type: 2,
                        })
                        delayTaskGetList()
                        showSuccessToast('操作成功')
                      } catch (error: any) {
                        showFailToast(error.response.data.message || '操作失败')
                      }
                    }}
                    />
                  </div>
                </>
              )
            } else if (audioPath === EXTRACT_ERROR || audioPath === HISTORY_EXTRACT_ERROR) {
              audioBtn = <Button class="btn btn-xs btn-link text-red-500">提取错误</Button>
            } else {
              // 选择无字幕、无音频的视频 + 对应音频
              audioBtn = audioPath ? <Button class="btn btn-active btn-xs btn-link" onClick={() => { void previewAudio(audioPath) }}>音频</Button> : '-'
            }

            return (
              <div>
                <div>{videoButton}</div>
                <div>{subtitleBtn}</div>
                <div>{audioBtn}</div>
              </div>
            )
          },
          { class: `${w} text-center` },
        ])
      }
    })

    const formatAudio = (path: string, row: M.IResourceDrama) => {
      let audioButton = null
      if (path === PROCESSING) {
        audioButton = <Button class="btn btn-xs btn-outline btn-accent">处理中</Button>
      } else if (path === TRANS_ERROR) {
        audioButton = <Button class="btn btn-xs btn-link text-red-500">翻译报错</Button>
      } else {
        // 选择无字幕、无音频的视频 + 对应音频
        audioButton = path
          ? (
              <div class="hover group flex space-x-0 justify-center items-center relative">
                <Button class="btn btn-active btn-xs btn-link" onClick={() => { previewAudio(path) }}>查看</Button>
                <SvgIcon class="group-hover:block hidden size-4 cursor-pointer absolute right-0 top-0" name="ic_close_fill" onClick={() => {
                  deleteResource(row, 2)
                }}
                />
              </div>
            )
          : '-'
      }
      return audioButton
    }

    columns.value = [
      [
        () => (
          <Checkbox
            label=""
            disabled={list.value?.length === 0}
            modelValue={isCheckAll.value}
            onUpdate:modelValue={(value: boolean) => {
              checkedAllChange(value, list.value)
            }}
          />
        ),
        (row: M.IResourceDrama) => {
          return (
            <Checkbox
              label=""
              modelValue={checked.value.includes(row.serial_number)}
              onUpdate:modelValue={(value: boolean) => {
                const serial_number = row.serial_number
                checkboxChange(value, serial_number, list.value)
              }}
            />
          )
        },
        { class: 'w-[60px]' },
      ],
      ['集数', 'serial_number', { class: 'w-[70px]' }],
      [
        () => showColumnHeader('origin_path', '含字幕视频', false), (row: M.IResourceDrama) => {
          return row.origin_path ? <Button class="btn btn-active btn-xs btn-link" onClick={() => { previewVideo(row.origin_path) }}>预览</Button> : '-'
        }, { class: 'w-[100px] text-center' }],
      [() => showColumnHeader('pure_path', '无字幕视频', false, (e: number[]) => {
        checked.value = [...e]
      }), (row: M.IResourceDrama) => {
        return row.pure_path === PURE_ERROR ? <Button class="btn btn-xs btn-outline btn-error">抹除失败</Button> : row.pure_path ? row.pure_path === 'processing' ? <Button class="btn btn-xs btn-outline btn-accent">处理中</Button> : <Button class="btn btn-active btn-xs btn-link" onClick={() => { previewVideo(row.pure_path) }}>预览</Button> : '-'
      }, { class: 'w-[100px] text-center' }],

      ['纯净资源', (row: M.IResourceDrama) => {
        return row.no_audio_path === PURE_ERROR
          ? <Button class="btn btn-xs btn-outline btn-error">抹除失败</Button>
          : row.no_audio_path
            ? row.no_audio_path === 'processing'
              ? <Button class="btn btn-xs btn-outline btn-accent">处理中</Button>
              : (
                  <div class="flex items-center justify-center flex-col">
                    <Button class="btn btn-active btn-xs btn-link"
                      onClick={() => { previewVideo(row.no_audio_path) }}
                    >
                      纯视频
                    </Button>
                    <Button class="btn btn-active btn-xs btn-link"
                      onClick={() => { previewAudio(row.origin_audio_path) }}
                    >
                      纯音频
                    </Button>
                  </div>
                )
            : '-'
      }, { class: 'w-[140px] text-center' }],

      ['转码视频', (row: M.IResourceDrama) => {
        const serial_number = row.serial_number
        const obj = externalTransProgress.value.find(item => item.serial_number === serial_number)
        let subtitleBtn = null
        if (!obj || obj.audio_external_status === 0) {
          subtitleBtn = '-'
        } else if (obj.audio_external_status === 1) {
          subtitleBtn = <Button class="btn btn-xs btn-outline btn-accent">处理中</Button>
        } else if (obj.audio_external_status === 3) {
          subtitleBtn = <Button class="btn btn-xs btn-outline btn-error">外挂失败</Button>
        } else if (obj.sync_status !== 1)  {
          subtitleBtn = <Button class="btn btn-xs btn-outline btn-error" onClick={() => { void previewM3U8(obj, row) }}>视频(未同步)</Button>
        } else {
          subtitleBtn = <Button class="btn btn-active btn-xs btn-link" onClick={() => { void previewM3U8(obj, row) }}>视频</Button>
        }
        return subtitleBtn
      }, { class: 'w-[100px] text-center' }],

      ['原始音频', (row: M.IResourceDrama) => {
        return formatAudio(row.origin_voice_path, row)
      }, { class: 'w-[100px] text-center' }],
      ['背景音频', (row: M.IResourceDrama) => {
        return formatAudio(row.bg_voice_path, row)
      }, { class: 'w-[100px] text-center' }],
      ['特效音频', (row: M.IResourceDrama) => {
        return formatAudio(row.effect_voice_path, row)
      }, { class: 'w-[100px] text-center' }],
      ...dynamicColumns,
      ['操作', (row: M.IResourceDrama) => {
        const delLoading = ref(false)
        return (
          <Button class="btn btn-xs btn-link" onClick={() => {
            const hideDeleteDialog = openDialog({
              title: '操作',
              mainClass: 'pb-0 px-5',
              body: () => (
                <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                  <x-delete-episode-body>确认删除吗？</x-delete-episode-body>
                  <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
                    <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                    <Button class="btn btn-primary btn-sm" disabled={delLoading.value} onClick={async () => {
                      try {
                        delLoading.value = true
                        await apiEpisodeDelete({ series_resource_id: +route.params.id, serial_number_list: [row.serial_number] })
                        showSuccessToast('操作成功')
                        void onQuery()
                        hideDeleteDialog()
                      } catch (error: any) {
                        showFailToast(error.response.data.message || '操作失败')
                      } finally {
                        delLoading.value = false
                      }
                    }}
                    >
                      {delLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                      确定
                    </Button>
                  </x-delete-episode-footer>
                </x-delete-episode-confirm-dialog>
              ),
            })
          }}
          >删除
          </Button>
        )
      }, { class: 'w-[90px]' }],
    ]
  }

  renderDynamicColumns()

  const makeList = (_list: M.IResourceDrama[]) => {
    // 如果没有数据拿不到 无法补充数组
    if (_list.length === 0) {
      list.value = _list
      return
    }
    const missingRowNumber = findMissingNumbers(resourceDetail.value.count || 0, _list)
    const result = makeUpList(missingRowNumber, _list[0])
    list.value = _list.concat(result as M.IResourceDrama[]).sort((a, b) => a.serial_number - b.serial_number)

    setMissingData(_list, resourceDetail.value.count || 0, total.value)
  }

  const getList = async () => {
    loading.value = true
    try {
      await getSubtitleRecordList()
      const res = await apiSeriesResourceList(queryForm.value)
      const _list = res?.data?.list || []
      total.value = res?.data?.total || 0
      loading.value = false
      makeList(_list)
    } catch (error) {
      loading.value = false
    }
  }

  const getSubtitleRecordList = async () => {
    const res = await apiGetRecordList({
      series_resource_id: +route.params.id,
    })
    subtitleLogs.value = res.data?.list || []
  }

  const taskGetList = async () => {
    await getSubtitleRecordList()
    await getTranscodeVideos(+route.params.id)
    const res = await apiSeriesResourceList(queryForm.value)
    makeList(res?.data?.list || [])
    total.value = res?.data?.total || 0
  }

  const delayTaskGetList = () => {
    setTimeout(async () => {
      const res = await apiSeriesResourceList(queryForm.value)
      makeList(res?.data?.list || [])
      total.value = res?.data?.total || 0
    }, 2000)
  }

  const onQuery = async () => {
    resetChecked()
    queryForm.value.page_index = 1
    await getList()
  }

  const getResourceDetail = async () => {
    const res = await apiGetResourceDetail({
      series_resource_id: +route.params.id,
    })
    resourceDetail.value = res?.data || {
      count: 0,
      title: '',
      unlocked_episodes: 0,
      serialize_status: 0,
    }
  }

  const splitAudio = () => {
    const rows = list.value.filter(row => checked.value.includes(row.serial_number))
    const closeDialog = openDialog({
      title: '音频拆分',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div>
            共选中: { rows.length }集
          </div>
          <div>
            选中集数： {rows.map(row => row.serial_number).join(',')}
          </div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" onClick={async () => {
              try {
                await apiSeparateAudio({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(row => row.serial_number),
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              }
            }}
            >确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  const eraseAudio = () => {
    const rows = list.value.filter(row => checked.value.includes(row.serial_number))
    const closeDialog = openDialog({
      title: '分离音视频',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div>
            共选中: { rows.length }集
          </div>
          <div>
            选中集数： {rows.map(row => row.serial_number).join(',')}
          </div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" onClick={async () => {
              try {
                await apiEraseAudio({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(row => row.serial_number),
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              }
            }}
            >确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  const optSubtitle = () => {
    const btnLoading = ref(false)
    const lang_list = ref<string[]>([])
    const rows = list.value.filter(row => checked.value.includes(row.serial_number))
    const closeDialog = openDialog({
      title: '优化字幕',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div class="flex flex-col justify-start items-start py-2 gap-x-2">
            <div>
              选中集数：
              <span>{checked.value.join(', ')}</span>
            </div>
            <div>选中数/总集数：
              <span class={checked.value.length === resourceDetail.value.count || 0 ? 'text-[var(--text-1)]' : 'text-[var(--error-3)]'}>{checked.value.length}</span> / {resourceDetail.value.count}
            </div>
          </div>
          <div class="flex justify-start items-center py-2 gap-x-2">
            <div class="pb-2">字幕语言：</div>
            <FormMultiSelect
              class="w-[200px]"
              popoverWrapperClass="z-popover-in-dialog"
              options={langKey.map((n, index) => {
                return { value: langKey[index], label: langValue[index] }
              })}
              modelValue={lang_list.value}
              onUpdate:modelValue={e => {
                lang_list.value = e as string[]
              }}
            />
          </div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" disabled={btnLoading.value} onClick={async () => {
              try {
                if (lang_list.value.length === 0) {
                  showFailToast('请选择优化字幕语言')
                  return
                }
                btnLoading.value = true
                await apiFixSubtitle({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(row => row.serial_number),
                  lang_list: lang_list.value,
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.message || error.response.data.err_msg || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  const eraseSubtitle = () => {
    const job_way = ref(2)
    const position = ref<[number, number, number, number]>([0, 0, 0, 0])
    const rows = list.value.filter(row => checked.value.includes(row.serial_number))
    const playingEpisodePath = ref(rows[0].origin_path)
    const episodePlayBtns = rows.filter(item => item.origin_path && item.origin_path.indexOf('http') === 0).map(row => (
      <Button class="btn btn-xs btn-default" onClick={() => {
        playingEpisodePath.value = row.origin_path
      }}
      >{row.serial_number}
      </Button>
    ))
    const closeDialog = openDialog({
      title: '抹除字幕',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div class="flex items-center pb-4 gap-x-2">
            <div>抹除方式:</div>
            <select class="select select-bordered select-sm" value={job_way.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              job_way.value = +value
            }}
            >
              <option value={2}>腾讯</option>
              <option value={1}>阿里</option>
            </select>
          </div>
          <Clip title="抹除坐标" path={playingEpisodePath.value} onChange={(p: [number, number, number, number]) => {
            position.value = p
          }}
          />
          <div class="pt-4 space-x-2 space-y-2">
            {episodePlayBtns}
          </div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" onClick={async () => {
              try {
                await apiSubtitleDel({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(row => row.serial_number),
                  regions: [position.value],
                  job_way: job_way.value,
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              }
            }}
            >确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  const translateAudio = () => {
    const src_lang = ref(langKey[0])
    const target_lang_list = ref<string[]>([])
    const btnLoading = ref(false)
    const closeDialog = openDialog({
      title: '音频翻译',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div class="flex items-center gap-x-6 justify-center p-4">
            <div>
              <div class="text-[var(--text-2)] pb-2">源音频语言：</div>
              <select class="select select-bordered select-sm" value={src_lang.value} onChange={e => {
                const target = e.target as HTMLSelectElement
                const value = target.value
                src_lang.value = value
              }}
              >
                {
                  langKey.map((n, index) => <option value={n}>{langValue[index]}</option>)
                }
              </select>
            </div>
            <SvgIcon name="ic_transform" />
            <div>
              <div class="text-[var(--text-2)] pb-2">翻译音频语言：</div>
              <div class="flex items-center">
                <FormMultiSelect
                  class="w-[200px]"
                  popoverWrapperClass="z-popover-in-dialog"
                  options={langKey.map((n, index) => {
                    return { value: langKey[index], label: langValue[index] }
                  })}
                  modelValue={target_lang_list.value}
                  onUpdate:modelValue={e => {
                    target_lang_list.value = e as string[]
                  }}
                />
                <Checkbox
                  label=""
                  modelValue={target_lang_list.value.length === langKey.length}
                  onUpdate:modelValue={(value: boolean) => {
                    if (value) {
                      target_lang_list.value = [...langKey]
                    } else {
                      target_lang_list.value = []
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" disabled={btnLoading.value} onClick={async () => {
              if (target_lang_list.value.length === 0) {
                showFailToast('请选择翻译目标语言！')
                return
              }
              try {
                const rows = list.value.filter(row => checked.value.includes(row.serial_number))
                btnLoading.value = true
                await apiAudioTrans({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(row => row.serial_number),
                  src_lang: src_lang.value,
                  target_lang_list: target_lang_list.value,
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确认
            </Button>
          </div>

        </>
      ),
    })
  }

  const translateSubtitle = () => {
    const src_lang = ref(langKey[0])
    const target_lang_list = ref<string[]>([])
    const btnLoading = ref(false)
    const closeDialog = openDialog({
      title: '字幕翻译',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div class="flex items-center gap-x-6 justify-center p-4">
            <div>
              <div class="text-[var(--text-2)] pb-2">源字幕语言：</div>
              <select class="select select-bordered select-sm" value={src_lang.value} onChange={e => {
                const target = e.target as HTMLSelectElement
                const value = target.value
                src_lang.value = value
              }}
              >
                {
                  langKey.map((n, index) => <option value={n}>{langValue[index]}</option>)
                }
              </select>
            </div>
            <SvgIcon name="ic_transform" />
            <div>
              <div class="text-[var(--text-2)] pb-2">翻译字幕语言：</div>
              <div class="flex items-center">
                <FormMultiSelect
                  class="w-[200px]"
                  popoverWrapperClass="z-popover-in-dialog"
                  options={langKey.map((n, index) => {
                    return { value: langKey[index], label: langValue[index] }
                  })}
                  modelValue={target_lang_list.value}
                  onUpdate:modelValue={e => {
                    target_lang_list.value = e as string[]
                  }}
                />
                <Checkbox
                  label=""
                  modelValue={target_lang_list.value.length === langKey.length}
                  onUpdate:modelValue={(value: boolean) => {
                    if (value) {
                      target_lang_list.value = [...langKey]
                    } else {
                      target_lang_list.value = []
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" disabled={btnLoading.value} onClick={async () => {
              if (target_lang_list.value.length === 0) {
                showFailToast('请选择翻译目标语言！')
                return
              }
              try {
                const rows = list.value.filter(row => checked.value.includes(row.serial_number))
                btnLoading.value = true
                await apiSubtitleTrans({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(row => row.serial_number),
                  src_lang: src_lang.value,
                  target_lang_list: target_lang_list.value,
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确认
            </Button>
          </div>

        </>
      ),
    })
  }
  /**
   * 外挂音频转码
   */
  const transcode = () => {
    const external_languages = ref<string[]>([])
    const audio_original_language = ref<string>('')
    const rows = list.value.filter(row => checked.value.includes(row.serial_number))
    const syncLoading = ref(false)
    const validRows = rows.filter(item => item.pure_path && item.pure_path.indexOf('http') === 0)
    const region = validRows[0].region || defaultClipValue
    const playingEpisodePath = ref(rows[0].pure_path)
    const episodePlayBtns = validRows.map(row => (
      <Button class="btn btn-xs btn-default" onClick={() => {
        playingEpisodePath.value = row.pure_path
      }}
      >{row.serial_number}
      </Button>
    ))
    const position = ref(region)

    const closeDialog = openDialog({
      title: '转码',
      mainClass: 'pb-0 px-5',
      body: () => (
        <div class="space-y-2">
          <div>
            选中集数：
            <span>{checked.value.join(', ')}</span>
          </div>
          <div>选中数/总集数：
            <span class={checked.value.length === resourceDetail.value.count || 0 ? 'text-[var(--text-1)]' : 'text-[var(--error-3)]'}>{checked.value.length}</span> / {resourceDetail.value.count}
          </div>
          <div class="flex items-center gap-x-2">
            音频默认语音:
            <select class="select select-bordered select-sm" value={audio_original_language.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              audio_original_language.value = value
            }}
            >
              {
                langKey.map((n, index) => <option value={n}>{langValue[index]}</option>)
              }
            </select>
          </div>
          <div class="flex items-center gap-x-2">
            外挂音频语言:
            <FormMultiSelect
              search={true}
              class="w-[200px]"
              popoverWrapperClass="z-popover-in-dialog"
              options={langKey.map((n, index) => {
                return { value: langKey[index], label: langValue[index] }
              })}
              modelValue={external_languages.value}
              onUpdate:modelValue={e => {
                external_languages.value = e as string[]
              }}
            />
          </div>
          <Clip title="插入字幕位置" defaultValue={position.value} path={playingEpisodePath.value} onChange={(p: [number, number, number, number]) => {
            position.value = p
          }}
          />
          <div class="pt-4 space-x-2 space-y-2">
            {episodePlayBtns}
          </div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" disabled={syncLoading.value} onClick={async () => {
              if (audio_original_language.value.length === 0) {
                showFailToast('请选择音频默认语音！')
                return
              }
              if (external_languages.value.length === 0) {
                showFailToast('请选择外挂语言！')
                return
              }
              try {
                syncLoading.value = true

                await apiExternalSubtitleRegion({
                  series_resource_id: +route.params.id,
                  series_numbers: rows.map(row => row.serial_number),
                  region: position.value,
                })

                await apiAudioTransCode({
                  series_resource_id: +route.params.id,
                  series_numbers: rows.map(row => row.serial_number),
                  audio_original_language: audio_original_language.value,
                  external_languages: external_languages.value,
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                setTimeout(() => {
                  void getTranscodeVideos(+route.params.id)
                }, 2000)
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.message || error.response.data.err_msg || '操作失败')
              } finally {
                syncLoading.value = false
              }
            }}
            >
              {syncLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确认
            </Button>
          </div>
        </div>
      ),
      customClass: '!w-[500px]',
    })
  }

  const extractSubtitle = () => {
    const language_code = ref(langKey[0])
    const type = ref<1 | 2 | number>(2)
    const position = ref<[number, number, number, number]>([0, 0, 0, 0])
    const rows = list.value.filter(row => checked.value.includes(row.serial_number))
    const playingEpisodePath = ref(rows[0].origin_path)
    const episodePlayBtns = rows.filter(item => item.origin_path && item.origin_path.indexOf('http') === 0).map(row => (
      <Button class="btn btn-xs btn-default" onClick={() => {
        playingEpisodePath.value = row.origin_path
      }}
      >{row.serial_number}
      </Button>
    ))
    const closeDialog = openDialog({
      title: '提取字幕',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div class="flex items-center pb-4 gap-x-2">
            字幕语言:
            <select class="select select-bordered select-sm" value={language_code.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              language_code.value = value
            }}
            >
              {
                langKey.map((n, index) => <option value={n}>{langValue[index]}</option>)
              }
            </select>
          </div>
          <div class="flex items-center pb-4 gap-x-2">
            类型:
            <select class="select select-bordered select-sm" value={type.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              type.value = +value as unknown as 1 | 2
            }}
            >
              <option value={2}>自研</option>
              <option value={1}>阿里云</option>
            </select>
          </div>
          <Clip title="提取坐标" path={playingEpisodePath.value} onChange={(p: [number, number, number, number]) => {
            position.value = p
          }}
          />
          <div class="pt-4 space-x-2 space-y-2">
            {episodePlayBtns}
          </div>
          <div class="flex justify-end items-center py-2 gap-x-2">
            <Button class="btn btn-sm btn-default" onClick={() => {
              closeDialog()
            }}
            >取消
            </Button>
            <Button class="btn btn-sm btn-primary" onClick={async () => {
              if (type.value === 2 && !['zh-CN', 'en', 'ja'].includes(language_code.value)) {
                showFailToast('自研提取暂只支持中、英、日语')
                return
              }
              try {
                await apiSubtitleExtraction({
                  series_resource_id: +route.params.id,
                  serial_numbers: rows.map(row => row.serial_number),
                  region: position.value,
                  type: type.value,
                  target_lang: language_code.value,
                })
                showSuccessToast('操作成功')
                void delayTaskGetList()
                closeDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              }
            }}
            >确认
            </Button>
          </div>
        </>
      ),
      customClass: '!w-[500px]',
    })
  }

  onMounted(async () => {
    void getTags('zh-CN', +route.params.id)
    void getEpisodeSeriesInfoList({
      series_resource_id: +route.params.id,
    })
    await getTranscodeVideos(+route.params.id)
    await getResourceDetail()
    await getList()
    void refreshSyncList(+route.params.id)
    resetChecked()
  })
  let interval = null
  interval = setInterval(() => {
    void taskGetList()
  }, 60 * 1000)
  onUnmounted(() => {
    clearInterval(interval)
  })

  return () => (
    <NavFormTablePager stickyHeader>{{
      nav: () => (
        <section class="breadcrumbs text-sm">
          <ul>
            <li><RouterLink to="/resource">资源管理</RouterLink></li>
            <li>资源详情</li>
          </ul>
        </section>
      ),
      form: () => (
        <>
          <div class="flex items-center gap-x-4">
            <ResourceForm
              disabled={true}
              btnHidden={true}
              title={resourceDetail.value.title}
              count={resourceDetail.value.count}
              release_round={resourceDetail.value.release_round}
              resource_type={resourceDetail.value.resource_type}
              unlocked_episodes={resourceDetail.value.unlocked_episodes}
              serialize_status={resourceDetail.value.serialize_status}
              label_ids={resourceDetail.value.label_ids}
              free_online_time={resourceDetail.value.free_online_time}
            />
            {resourceDetail.value.series_resource_id
              ? (
                  <AuthButton class="btn btn-sm btn-primary mt-1" blackRoleList={['3']} onClick={() => {
                    void getTags('zh-CN', +route.params.id)
                    const createDialog = openDialog({
                      title: '编辑资源',
                      mainClass: 'pb-0 px-5',
                      body: (
                        <ResourceForm
                          disabled={true}
                          title={resourceDetail.value.title}
                          count={resourceDetail.value.count}
                          release_round={resourceDetail.value.release_round}
                          resource_type={resourceDetail.value.resource_type}
                          unlocked_episodes={resourceDetail.value.unlocked_episodes}
                          serialize_status={resourceDetail.value.serialize_status}
                          label_ids={resourceDetail.value.label_ids}
                          free_online_time={resourceDetail.value.free_online_time}
                          clazz="w-full flex flex-col"
                          onClose={() => createDialog()}
                          onAfterSave={async () => {
                            await getResourceDetail()
                            await taskGetList()
                            createDialog()
                          }}
                        />
                      ),
                    })
                  }}
                  >
                    编辑
                  </AuthButton>
                )
              : null }
          </div>
          {/* <div>
            <div class="collapse bg-white">
              <input type="checkbox" />
              <div class="collapse-title" onClick={() => collapse.value = !collapse.value}>
                剧集信息校验
                <SvgIcon name="ic_arrow_down_fil" />
              </div>
              <div class="collapse-content overflow-hidden !pl-0 mt-2">
                <TranslateForm series_resource_id={+route.params.id} />
                <div class="overflow-auto">
                  <div class="flex gap-x-2 ">
                    {
                      episodeSeriesInfoList.value.map(row => <SeriesInfoCard item={row} />)
                    }
                  </div>
                </div>
                <div class="flex py-2 justify-end">
                  <Button class="btn mr-2 btn-sm btn-primary" disabled={seriesInfoCommitLoading.value} onClick={saveSeriesInfo}>
                    {seriesInfoCommitLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                    保存
                  </Button>
                </div>
              </div>
            </div>
          </div> */}
        </>
      ),
      tableActions: () => (
        <div class="flex justify-between">
          <div class="flex items-center space-x-2 flex-row">
            <span>批量操作：</span>
            <div class="dropdown">
              <div>
                <div tabindex={0} role="button" class="btn btn-sm btn-primary">导入资源</div>
              </div>
              <ul tabindex={0} class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                {role === '3'
                  ? null
                  : (
                      <>
                        <li>
                          <a href="javascript:void(0);" onClick={() => { onDiskUpload(+route.params.id, taskGetList) }}>网盘导入</a>
                        </li>
                        <li>
                          <a href="javascript:void(0);" onClick={() => { onUploadVideo(+route.params.id, taskGetList) }}>导入视频</a>
                        </li>
                      </>
                    )}
                <li>
                  <a href="javascript:void(0);" onClick={() => { onUploadCaption(+route.params.id, taskGetList) }}>导入字幕</a>
                </li>
                {role === '3'
                  ? null
                  : (
                      <li>
                        <a href="javascript:void(0);" onClick={() => { onUploadAudio(+route.params.id, taskGetList) }}>导入音频</a>
                      </li>
                    )}
              </ul>
            </div>

            <div class="dropdown">
              <div>
                <div tabindex={0} {...(checked.value.length === 0 ? { disabled: true } : {})} role="button" class="btn btn-sm btn-primary">处理字幕</div>
              </div>
              <ul tabindex={0} class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                {/* <li>
                  <a href="javascript:void(0);" onClick={() => { extractSubtitle() }}>提取字幕
                  </a>
                </li> */}
                <li>
                  <a href="javascript:void(0);" onClick={() => { eraseSubtitle() }}>抹除字幕
                  </a>
                </li>
                <li>
                  <a href="javascript:void(0);" onClick={() => { optSubtitle() }}>优化字幕
                  </a>
                </li>
              </ul>
            </div>

            {role === '3'
              ? null
              : (
                  <div class="dropdown">
                    <div>
                      <div tabindex={0} {...(checked.value.length === 0 ? { disabled: true } : {})} role="button" class="btn btn-sm btn-primary">处理音频</div>
                    </div>
                    <ul tabindex={0} class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                      <li>
                        <a href="javascript:void(0);" onClick={() => {
                          eraseAudio()
                        }}
                        >分离音视频
                        </a>
                        <a href="javascript:void(0);" onClick={() => {
                          splitAudio()
                        }}
                        >拆解音频
                        </a>
                      </li>
                    </ul>
                  </div>
                )}
            {/* <AuthButton blackRoleList={['3']} disabled={checked.value.length === 0} class="btn btn-sm btn-primary" onClick={() => translateSubtitle()}>翻译字幕</AuthButton> */}
            <div class="dropdown">
              <div>
                <div tabindex={0} {...(checked.value.length === 0 ? { disabled: true } : {})} role="button" class="btn btn-sm btn-primary">翻译</div>
              </div>
              <ul tabindex={0} class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                <li>
                  <a href="javascript:void(0);" onClick={() => {
                    translateSubtitle()
                  }}
                  >翻译字幕
                  </a>
                </li>
                { role === '3'
                  ? null
                  : (
                      <li>
                        <a href="javascript:void(0);" onClick={() => {
                          translateAudio()
                        }}
                        >翻译音频
                        </a>
                      </li>
                    )}
              </ul>
            </div>
            <AuthButton blackRoleList={['3']} class="btn btn-sm btn-primary" disabled={checked.value.length === 0} onClick={transcode}>
              转码
            </AuthButton>
            <AuthButton blackRoleList={['3']} class="btn btn-sm btn-primary" disabled={checked.value.length === 0} onClick={() => {
              const src_lang_list = ref<string[]>([])
              const inject_type = ref<0 | 1>(0)
              const position = ref<[number, number, number, number]>([0, 0, 0, 0])
              const rows = list.value.filter(row => checked.value.includes(row.serial_number))
              const playingEpisodePath = ref(rows[0].origin_path)
              const episodePlayBtns = rows.filter(item => item.pure_path && item.pure_path.indexOf('http') === 0).map(row => (
                <Button class="btn btn-xs btn-default" onClick={() => {
                  playingEpisodePath.value = row.pure_path
                }}
                >{row.serial_number}
                </Button>
              ))
              const closeDialog = openDialog({
                title: '合成视频',
                mainClass: 'pb-0 px-5',
                body: () => (
                  <>
                    <div class="flex items-center pb-4 gap-x-2">
                      {requiredLabel('合成类型:')}
                      <RadioGroup
                        class="tm-radio"
                        options={[
                          {
                            value: 0,
                            label: '合成带字幕视频',
                          },
                          {
                            value: 1,
                            label: '合成带字幕和音频的视频',
                          },
                        ]}
                        modelValue={inject_type.value}
                        onUpdate:modelValue={(e: unknown) => inject_type.value = e as 0 | 1}
                      />
                    </div>
                    <div class="flex items-center pb-4 gap-x-2">
                      {requiredLabel('字幕语言:')}
                      <FormMultiSelect
                        class="w-[200px]"
                        popoverWrapperClass="z-popover-in-dialog"
                        options={langKey.map((n, index) => {
                          return { value: langKey[index], label: langValue[index] }
                        })}
                        modelValue={src_lang_list.value}
                        onUpdate:modelValue={e => {
                          src_lang_list.value = e as string[]
                        }}
                      />
                      <Checkbox
                        label=""
                        modelValue={src_lang_list.value.length === langKey.length}
                        onUpdate:modelValue={(value: boolean) => {
                          if (value) {
                            src_lang_list.value = [...langKey]
                          } else {
                            src_lang_list.value = []
                          }
                        }}
                      />
                    </div>
                    <Clip title="插入字幕坐标" defaultValue={defaultClipValue} path={playingEpisodePath.value} onChange={(p: [number, number, number, number]) => {
                      position.value = p
                    }}
                    />
                    <div class="pt-4 space-x-2 space-y-2">
                      {episodePlayBtns}
                    </div>
                    <div class="flex justify-end items-center py-2 gap-x-2">
                      <Button class="btn btn-sm btn-default" onClick={() => {
                        closeDialog()
                      }}
                      >取消
                      </Button>
                      <Button class="btn btn-sm btn-primary" onClick={async () => {
                        if (src_lang_list.value.length === 0) {
                          showFailToast('请选择语言！')
                          return
                        }
                        try {
                          let _position = position.value
                          if (position.value[0] === 0 && position.value[1] === 0 && position.value[2] === 0 && position.value[3] === 0)
                            _position = defaultClipValue
                          await apiSubtitleInject({
                            series_resource_id: +route.params.id,
                            serial_numbers: rows.map(row => row.serial_number),
                            region: _position,
                            src_lang_list: src_lang_list.value,
                            inject_type: inject_type.value,
                          })
                          showSuccessToast('操作成功')
                          void delayTaskGetList()
                          closeDialog()
                        } catch (error: any) {
                          showFailToast(error.response.data.err_msg || '操作失败')
                        }
                      }}
                      >确认
                      </Button>
                    </div>
                  </>
                ),
                customClass: '!w-[500px]',
              })
            }}
            >
              合成视频
            </AuthButton>
            <AuthButton blackRoleList={['3']} class="btn btn-sm btn-primary" onClick={() => {
              const language_codes = ref<string[]>([])
              const subtitle_languages = ref<string[]>([])
              const type = ref(3)
              const rows = list.value.filter(row => checked.value.includes(row.serial_number))
              const syncLoading = ref(false)
              const closeDialog = openDialog({
                title: '视频同步',
                mainClass: 'pb-0 px-5',
                body: () => (
                  <div class="space-y-2">
                    <div>
                      选中集数：
                      <span>{checked.value.join(', ')}</span>
                    </div>
                    <div>选中数/总集数：
                      <span class={checked.value.length === resourceDetail.value.count || 0 ? 'text-[var(--text-1)]' : 'text-[var(--error-3)]'}>{checked.value.length}</span> / {resourceDetail.value.count}
                    </div>
                    <div>
                      <Input
                        type="radio"
                        class="tm-radio"
                        options={[
                          // {
                          //   value: 1,
                          //   label: '源视频同步',
                          // },
                          // {
                          //   value: 2,
                          //   label: '合成视频同步',
                          // },
                          {
                            value: 3,
                            label: '外挂音频同步',
                          },
                        ]}
                        modelValue={type.value}
                        onUpdate:modelValue={(value: unknown) => {
                          type.value = value as number
                          language_codes.value = []
                          subtitle_languages.value = []
                        }}
                      />
                    </div>
                    <div class="flex items-center gap-x-2">
                      剧集语言:
                      <FormMultiSelect
                        maxlength={type.value === 1 ? 1 : null}
                        class="w-[200px]"
                        popoverWrapperClass="z-popover-in-dialog"
                        options={langKey.map((n, index) => {
                          return { value: langKey[index], label: langValue[index] }
                        })}
                        modelValue={language_codes.value}
                        onUpdate:modelValue={e => {
                          language_codes.value = e as string[]
                        }}
                      />
                      <Checkbox
                        label=""
                        modelValue={language_codes.value.length === langKey.length}
                        onUpdate:modelValue={(value: boolean) => {
                          if (value) {
                            language_codes.value = [...langKey]
                          } else {
                            language_codes.value = []
                          }
                        }}
                      />
                    </div>
                    {
                      type.value === 3
                        ? (
                            <div class="flex items-center pb-4 gap-x-2">
                              字幕语言:
                              <FormMultiSelect
                                search={true}
                                class="w-[200px]"
                                popoverWrapperClass="z-popover-in-dialog"
                                options={langKey.map((n, index) => {
                                  return { value: langKey[index], label: langValue[index] }
                                })}
                                modelValue={subtitle_languages.value}
                                onUpdate:modelValue={e => {
                                  subtitle_languages.value = e as string[]
                                }}
                              />
                              <Checkbox
                                label=""
                                modelValue={subtitle_languages.value.length === langKey.length}
                                onUpdate:modelValue={(value: boolean) => {
                                  if (value) {
                                    subtitle_languages.value = [...langKey]
                                  } else {
                                    subtitle_languages.value = []
                                  }
                                }}
                              />
                            </div>
                          )
                        : null
                    }
                    <div class="flex justify-end items-center py-2 gap-x-2">
                      <Button class="btn btn-sm btn-default" onClick={() => {
                        closeDialog()
                      }}
                      >取消
                      </Button>
                      <Button class="btn btn-sm btn-primary" onClick={() => {
                        if (!type.value) {
                          showFailToast('请选择同步视频')
                          return
                        }
                        if (language_codes.value.length === 0) {
                          showFailToast('请选择语言！')
                          return
                        }
                        const commitCheckDialog = openDialog({
                          title: '同步确认',
                          mainClass: 'px-4 !py-0',
                          customClass: '!w-[400px]',
                          body: () => (
                            <>
                              <div class="space-y-2">
                                <div>
                                  选中集数：
                                  <span>{checked.value.join(', ')}</span>
                                </div>
                                <div>选中数/总集数：
                                  <span class={checked.value.length === resourceDetail.value.count || 0 ? 'text-[var(--text-1)]' : 'text-[var(--error-3)]'}>{checked.value.length}</span> / {resourceDetail.value.count}
                                </div>
                                <div>
                                  同步类型：<div class="badge badge-outline">{ type.value === 1 ? '源视频同步' : type.value === 2 ? '合成视频同步' : '外挂音频同步' }</div>
                                </div>
                                <div class="flex items-center flex-wrap gap-x-2">
                                  剧集语言: {language_codes.value.map(code => <div class="badge badge-primary badge-outline">{langValue[langKey.findIndex(k => k === code)]}</div>)}
                                </div>
                                {type.value === 3
                                  ? (
                                      <div class="flex items-center flex-wrap pb-4 gap-x-2">
                                        字幕语言: {subtitle_languages.value.map(code => <div class="badge badge-primary badge-outline">{langValue[langKey.findIndex(k => k === code)]}</div>)}
                                      </div>
                                    )
                                  : null}
                              </div>
                              <div class="flex justify-end items-center py-2 gap-x-2">
                                <Button class="btn btn-sm btn-default" onClick={() => {
                                  commitCheckDialog()
                                }}
                                >取消
                                </Button>
                                <Button class="btn btn-sm btn-primary" disabled={syncLoading.value} onClick={async () => {
                                  try {
                                    syncLoading.value = true
                                    await apiResourceSync({
                                      series_resource_id: +route.params.id,
                                      episode_resouce_id: rows.map(row => row.serial_number),
                                      language_codes: language_codes.value,
                                      type: type.value,
                                      subtitle_languages: subtitle_languages.value,
                                    })
                                    showSuccessToast('操作成功')
                                    void delayTaskGetList()
                                    closeDialog()
                                  } catch (error: any) {
                                    showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                                  } finally {
                                    syncLoading.value = false
                                  }
                                }}
                                >
                                  {syncLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                                  提交
                                </Button>
                              </div>
                            </>
                          ),
                        })
                      }}
                      >确认
                      </Button>
                    </div>
                  </div>
                ),
                customClass: '!w-[500px]',
              })
            }}
            >剧集同步
            </AuthButton>
          </div>
          <div class="space-x-2">
            { showSyncSummary(uploadSyncList.value, +route.params.id) }
            {/* <button class="btn-square btn-sm btn" title="字幕校对日志" onClick={() => {
                showValidateDialog()
              }}
              >
                <SvgIcon name="ic_log" />
              </button> */}
          </div>
        </div>
      ),
      table: () => (
        <Table
          list={list.value || []}
          class="w-full tm-table-fix-last-column tm-table-fix-header"
          columns={columns.value}
          loading={loading.value}
        />
      ),
      // pager: () => (
      //   <Pager class="justify-end" v-model:page={queryForm.value.page_index} v-model:size={queryForm.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
      // ),
    }}
    </NavFormTablePager>
  )
})

export default ResourceDetailPage
