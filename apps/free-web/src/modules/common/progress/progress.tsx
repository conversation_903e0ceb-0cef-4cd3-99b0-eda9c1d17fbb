import { createComponent, mc } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
type ProgressOptions = {
  props: {
    value: number
    max: number
    class?: string
  }
}
export const Progress = createComponent<ProgressOptions>({
  props: {
    value: 0,
    max: 100,
    class: '',
  },
}, props => {
  return () => (
    <MergeClass baseClass="block rounded-[400px] bg-[var(--fill-3)] h-2" class={props.class}>
      <div class={mc('rounded-l-[400px] bg-[var(--brand-6)] h-full', Number(props.value.toFixed(2)) >= props.max ? 'rounded-r-[400px]' : '')} style={{ width: `${Math.max(2, (Number((props.value / props.max).toFixed(2)) > 1 ? 1 : Number((props.value / props.max).toFixed(2))) * 100)}%` }} />
    </MergeClass>
  )
})
