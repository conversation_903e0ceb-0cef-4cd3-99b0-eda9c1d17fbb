import { watch } from 'vue'
import { useSeriesItem } from './use-series-item'
import { useInterval } from '@vueuse/core'

export function useWatermark(series_id: string) {
  const { obviousWatermarkVisible, nonObviousWatermarkVisible, nonObviousWatermarkPosition } = useSeriesItem(series_id)
  /**
   * 明显水印的计数器，当 count 是 30 的倍数的时候显示水印
   */
  const obviousWatermarkCount = useInterval(1000)

  watch(() => obviousWatermarkCount.value, c => {
    if (c % 30 === 0) {
      obviousWatermarkVisible.value = true
      setTimeout(() => {
        obviousWatermarkVisible.value = false
      }, 4000)
    }
    const rect = { width: window.innerWidth, height: window.innerHeight }
    if (c % 10 === 0) {
      const r = Math.random()
      const r2 = Math.random()
      if (r >= 0.5) {
        nonObviousWatermarkVisible.value = true
        nonObviousWatermarkPosition.value = { x: r * rect.width, y: r2 * rect.height }
        setTimeout(() => {
          nonObviousWatermarkVisible.value = false
        }, 4000)
      }
    }
  })
  return {
    nonObviousWatermarkVisible,
    nonObviousWatermarkPosition,
  }
}
