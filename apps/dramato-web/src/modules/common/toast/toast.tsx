import { ClassName, createComponent, fn, mc, required } from '@skynet/shared'
import { useToastStore } from './use-toast-store'
import { computed, onMounted, ref, VNodeChild, watchEffect } from 'vue'
import { ToastType } from './toast-types'
import { Fn, TransitionPresets, useTransition } from '@vueuse/core'
import { MergeClass } from '@skynet/ui'

export interface ToastProps {
  toastId: string
  content?: VNodeChild | null
  type?: ToastType
  autoHide?: boolean | number
  /**
   * 由于 toast 的出现和消失伴随动画，所以 visible 为 false 时，不代表 toast 一定不被用户看见
   */
  visible?: boolean
  class?: ClassName
}

interface ToastOptions {
  props: ToastProps
  emits: {
    destroy: Fn
  }
}
export const Toast = createComponent<ToastOptions>({
  props: {
    toastId: required,
    content: null,
    type: 'success',
    autoHide: 2000,
    visible: true,
    class: '',
  },
  emits: {
    destroy: fn,
  },
}, (props, { emit }) => {
  const { addToHideList, removeFromHideList } = useToastStore()
  const duration = computed(() => typeof props.autoHide === 'number' ? props.autoHide : 5000)
  onMounted(() => {
    watchEffect(() => {
      const time = new Date().getTime() + duration.value
      console.log('3: ', props.autoHide, time)
      props.autoHide
        ? addToHideList(props.toastId, time)
        : removeFromHideList(props.toastId)
    })
  })
  const toast = ref<HTMLElement | null>(null)
  const y = ref(300)
  const targetY = useTransition(y, {
    duration: 300,
    transition: TransitionPresets.easeOutCubic,
    onFinished: () => {
      if (props.visible === false) {
        emit('destroy')
        setTimeout(() => {
          // 兼容oppo不消失出现残影，但是点击屏幕可以消失
          // 所以模拟触发一次点击事件
          document.body?.dispatchEvent(new Event('click'))
          document.body?.click()
        }, 1000)
      }
    },
  })
  watchEffect(() => {
    if (props.visible) {
      y.value = 0
    } else {
      y.value = -300
    }
  })

  return () => (
    <MergeClass
      ref={toast}
      class={mc('inline-flex transform translate-y-full -mt-15vh px-4 py-3 bg-[#434546] rounded-lg shadow max-w-80vw', props.class)}
      style={{
        transform: `translateY(${targetY.value}%)`,
        opacity: (100 - Math.abs(targetY.value) / 3) / 100,
      }}
    >
      <div class="text-center text-[var(--white)] text-sm font-normal">{props.content}</div>
    </MergeClass>
  )
})

export const showToast = (content: ToastProps['content'], type?: ToastProps['type'], toastProps?: Omit<ToastProps, 'content' | 'type' | 'toastId'>) => {
  const { addToast, updateToast } = useToastStore()
  const id = addToast({ content, type, ...toastProps })
  const hideToast = () => updateToast(id, { visible: false })
  return hideToast
}
