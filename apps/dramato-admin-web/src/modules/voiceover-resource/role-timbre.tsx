/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { ElTable, ElTableColumn } from 'element-plus'
import { onMounted, ref } from 'vue'
import { apiGetAiVoiceCharacterList, apiUpdateAiVoiceCharacter } from 'src/modules/voiceover-role/voiceover-role-api'
import { useRoute } from 'vue-router'
import { Button, openDialog, showFailToast, SvgIcon } from '@skynet/ui'
import { cloneDeep } from 'lodash-es'
import { AudioPlayer } from 'src/modules/voiceover-role/audio-player'
import { apiGetFilterTimbre } from 'src/modules/timbre/timbre-api'
import { ElPagination } from 'element-plus'

type RoleTimbreOptions = {
  props: {
    lang: string
  }
  emits: {
    changeEpisodeNum: (num: number) => void
  }
}
export const RoleTimbre = createComponent<RoleTimbreOptions>({
  props: {
    lang: '',
  },
  emits: {
    changeEpisodeNum: (num: number) => {},
  },
}, (props, { emit }) => {
  const route = useRoute()
  const loading = ref(false)
  const list = ref<Api.VoiceoverRole.VoiceoverRoleItem[]>([])
  const timbreList = ref<Api.Timbre.TimbreItem[]>([])
  const allTimbreList = ref<Api.Timbre.TimbreItem[]>([])
  const cdnUrl = 'https://img.tianmai.cn/'
  const timbreListLoading = ref(false)
  const pageInfo = ref<Api.PageInfo>({
    page: 1,
    page_size: 20,
    total: 0,
  })
  const keyword = ref('')
  const getTimbreList = async (gender?: number) => {
    timbreListLoading.value = true
    const res = await apiGetFilterTimbre({
      page: pageInfo.value.page,
      page_size: pageInfo.value.page_size,
      language: props.lang,
      gender: gender || -1,
      desc: keyword.value,
    })
    timbreList.value = res.data?.timbre_list || []
    allTimbreList.value = res.data?.timbre_list || []
    timbreListLoading.value = false
    pageInfo.value.total = res.data?.total || 0
  }

  const getList = async () => {
    try {
      loading.value = true
      const res = await apiGetAiVoiceCharacterList({
        series_resource_id: +route.params.id,
      })
      list.value = res.data?.character_list || []
    } catch (error) {
      list.value = []
    } finally {
      loading.value = false
    }
  }

  const filterMethod = (query: string) => {
    if (query) {
      timbreList.value = allTimbreList.value.filter(row => row.desc?.includes(query) || row.title?.includes(query))
    } else {
      timbreList.value = allTimbreList.value
    }
  }

  onMounted(() => {
    void getTimbreList()
    void getList()
  })

  return () => (
    <x-role-timbre class="block w-[200px] h-full overflow-auto sticky top-0">
      <ElTable
        v-loading={loading.value}
        data={list.value || []}
      >
        <ElTableColumn
          prop="name"
          label="角色"
          width={120}
          v-slots={{
            default: ({ row }: { row: Api.VoiceoverRole.VoiceoverRoleItem }) => {
              return (
                <div class="space-x-0.5 overflow-hidden">
                  <span>{row.name}</span>
                  <span class="link link-primary" onClick={() => {
                    emit('changeEpisodeNum', row.min_serial_number)
                  }}>({row.min_serial_number})</span>
                </div>
              )
            },
          }} />
        <ElTableColumn
          prop="name"
          label="音色"
          width={80}
          v-slots={{
            default: ({ row }: { row: Api.VoiceoverRole.VoiceoverRoleItem }) => {
              const selectedTimbres = ref(row?.timbres || [])
              const gender = ref(-1)
              const lang = props.lang
              const index = ref(selectedTimbres.value.findIndex(timbre => timbre.lang === lang))
              return (
                <div class="">
                  <div class="link link-primary" onClick={(e: Event) => {
                    const btnLoading = ref<boolean>(false)
                    timbreList.value = allTimbreList.value
                    const closeDialog = openDialog({
                      title: '配音音色',
                      customClass: '!w-[400px]',
                      body: () => (
                        <div v-loading={timbreListLoading.value}>
                          <div class="flex space-x-2">
                            <select value={gender.value} class="select-sm select select-bordered" onChange={async (e: Event) => {
                              gender.value = +(e.target as HTMLSelectElement).value
                              await getTimbreList(gender.value)
                              // filterMethod(keyword.value)
                            }}>
                              <option value={-1}>全部</option>
                              <option value={0}>未知</option>
                              <option value={1}>男</option>
                              <option value={2}>女</option>
                            </select>
                            <input value={keyword.value} class="input input-sm input-bordered w-[300px]" onChange={(e: Event) => {
                              const query = (e.target as HTMLInputElement).value
                              keyword.value = query
                              void getTimbreList(gender.value)
                            }} placeholder="请输入音色名称或者描述" />
                          </div>
                          <div class="mt-4 h-[450px] overflow-y-auto">
                            {
                              selectedTimbres.value[index.value]?.timbre_id
                                ? (
                                    <div class="mb-4 flex cursor-pointer flex-col border border-gray-200 px-4 py-2 hover:bg-[var(--bg-2)]">
                                      <div class="flex flex-col gap-y-2 text-sm font-bold">
                                        <div class="flex justify-between">
                                          <div>{selectedTimbres.value[index.value]?.timbre_name}</div>
                                          <div class="badge badge-primary badge-sm">已选</div>
                                        </div>
                                        <div class="text-sm text-[var(--text-2)]">{allTimbreList.value.find(item => item.timbre_id === selectedTimbres.value[index.value]?.timbre_id)?.desc}</div>
                                        <AudioPlayer key={selectedTimbres.value[index.value]?.timbre_id} src={`${cdnUrl}${selectedTimbres.value[index.value]?.sample_path || ''}`} preload="none" />
                                      </div>
                                    </div>
                                  ) : null
                            }
                            {
                              timbreList.value.length > 0
                                ? timbreList.value.map(item => (
                                  <div class="flex cursor-pointer flex-col gap-y-2 border border-gray-200 px-4 py-2 hover:bg-[var(--bg-2)]">
                                    <div class="flex items-center justify-between text-sm font-bold">
                                      <div class="line-clamp-1 flex-1" title={item.title}>{item.title}</div>
                                      <div class="flex w-[70px] justify-end">
                                        <Button class="btn btn-ghost btn-xs" disabled={btnLoading.value} onClick={async () => {
                                          const selectedTimbreId = ref<number | string>(selectedTimbres.value[index.value]?.timbre_id || '')
                                          const samplePath = ref<string>(timbreList.value.find(o => o.timbre_id === selectedTimbres.value[index.value]?.timbre_id)?.sample_path || '')
                                          selectedTimbreId.value = item.timbre_id || ''
                                          samplePath.value = item.sample_path || ''
                                          const voiceoverLangItem = {
                                            lang: lang,
                                            timbre_id: selectedTimbreId.value as number,
                                            timbre_name: allTimbreList.value.find(item => item.timbre_id === selectedTimbreId.value)?.title || '',
                                            sample_path: samplePath.value,
                                            desc: item.desc
                                          }
                                          btnLoading.value = true
                                          try {
                                            // 检查是否存在相同的音色
                                            const isExistSameTimbre = list.value.some(item =>
                                              item.timbres && item.timbres.some(timbre =>
                                                timbre.lang === lang && timbre.timbre_id === selectedTimbreId.value && item.character_id !== row.character_id
                                              )
                                            )

                                            if (isExistSameTimbre) {
                                              showFailToast('该音色已被其他角色使用')
                                            }

                                            await apiUpdateAiVoiceCharacter({
                                              character_id: row.character_id,
                                              timbres: [voiceoverLangItem],
                                            })

                                            void getList()
                                            if (!isExistSameTimbre) {
                                              closeDialog();
                                            } else {
                                              if (index.value === -1) {
                                                selectedTimbres.value.push(voiceoverLangItem)
                                                index.value = selectedTimbres.value.length - 1
                                              } else {
                                                selectedTimbres.value.splice(index.value, 1, voiceoverLangItem)
                                              }
                                            }
                                          } catch (error: any) {
                                            showFailToast(error.response.data.err_msg || '操作失败')
                                          } finally {
                                            btnLoading.value = false
                                          }
                                        }}>
                                          使用
                                        </Button>
                                      </div>
                                    </div>
                                    <div class="text-sm text-[var(--text-2)]">{item.desc}</div>
                                    <AudioPlayer key={item.timbre_id} src={`${cdnUrl}${item.sample_path || ''}`} />
                                  </div>
                                )) : <div class="h-[60px] leading-[60px] text-center text-[var(--text-2)]"> {timbreListLoading.value ? '加载中……' : '暂无数据'}</div>
                            }
                          </div>
                          <div class="flex justify-end mt-4">
                            <ElPagination onUpdate:current-page={async (page: number) => {
                              pageInfo.value.page = page
                              await getTimbreList(gender.value)
                            }} onUpdate:page-size={async (size: number) => {
                              pageInfo.value.page_size = size
                              await getTimbreList(gender.value)
                            }} pageSize={pageInfo.value.page_size} total={pageInfo.value.total} layout=" prev, pager, next" />
                          </div>
                        </div>
                      ),
                    })
                  }}>{selectedTimbres.value[index.value]?.timbre_name || '设置音色'}</div>
                  {
                    selectedTimbres.value[index.value]?.timbre_name ? (
                      <SvgIcon name="ic_del" class="size-5 cursor" onClick={() => {
                        const btnLoading = ref(false)
                        const hideDialog = openDialog({
                          title: '',
                          mainClass: 'pb-0 px-5',
                          body: () => (
                            <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                              <x-status-body>是否确认清除当前音色？</x-status-body>
                              <x-status-footer class="w-full flex justify-end gap-x-[10px]">
                                <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                                  btnLoading.value = true
                                  try {
                                    await apiUpdateAiVoiceCharacter({
                                      character_id: row.character_id,
                                      timbres: [{
                                        lang: selectedTimbres.value[index.value].lang,
                                        timbre_id: 0
                                      }],
                                    })
                                    void getList()
                                    hideDialog()
                                  } catch (error: any) {
                                    showFailToast(error.response.data.err_msg || '操作失败')
                                  } finally {
                                    btnLoading.value = false
                                  }
                                }}
                                >确定
                                </button>
                              </x-status-footer>
                            </x-status-confirm-dialog>
                          ),
                        })
                      }} />
                    ) : null
                  }
                </div>
              )
            },
          }}
        />
      </ElTable>
    </x-role-timbre>
  )
})

export default RoleTimbre
