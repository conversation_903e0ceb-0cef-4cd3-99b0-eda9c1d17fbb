import { createComponent, fn, mc } from '@skynet/shared'
import { Button, Input, openDialog } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { MultiSaveErrorDialog } from './multi-save-error-dialog'
import { Icon } from '@skynet/ui/icon/icon'
import { useShortDramaStore } from './short-drama-store'
import dayjs from 'dayjs'
type MultiModifyDramaPriceOptions = {
  emits: {
    cancel: Fn
    allSaveSuccess: Fn
    updateData: Fn
  }
}
export const MultiModifyDramaPrice = createComponent<MultiModifyDramaPriceOptions>({
  emits: {
    cancel: fn,
    allSaveSuccess: fn,
    updateData: fn,
  },
}, (_props, { emit }) => {
  const { selectedDramas, loading, episodesPrice, startPayingEpisodes, freeStartTime, freeEndTime, isFree, updateDramasPrice, platform_sync_list } = useShortDramaStore()
  const save = () => {
    updateDramasPrice(true, false, fails => {
      emit('updateData')
      if (fails && fails.length > 0) {
        const close = openDialog({
          title: '部分短剧修改失败',
          body: (
            <MultiSaveErrorDialog
              type="price"
              fails={fails}
              onCancel={() => {
                close()
              }}
              onConfirm={() => {
                close()
              }}
            />
          ),
        })
      } else {
        emit('allSaveSuccess')
      }
    })
  }

  return () => (
    <div class="text-sm text-gray-500">
      <div class="font-medium text-[#141414] text-base">已查询{selectedDramas.value.length}部短剧，该操作将修改这{selectedDramas.value.length}部短剧的定价配置</div>
      <div class="flex flex-col items-start gap-4 py-4 flex-wrap">
        <div class="flex items-center gap-2">
          <span class="shrink-0">免费时间段</span>
          <input
            step={1}
            class="input input-bordered input-sm w-full max-w-xs"
            type="datetime-local"
            value={freeStartTime.value ? dayjs(freeStartTime.value * 1000).format('YYYY-MM-DD HH:mm:ss') : 0}
            onInput={(event: Event) => freeStartTime.value = dayjs((event.target as HTMLInputElement).value).unix()}
          />
          ~
          <input
            step={1}
            min={freeStartTime.value ? dayjs(freeStartTime.value * 1000).format('YYYY-MM-DD HH:mm:ss') : dayjs().format('YYYY-MM-DD HH:mm:ss')}
            class="input input-bordered input-sm w-full max-w-xs"
            type="datetime-local"
            value={freeEndTime.value ? dayjs(freeEndTime.value * 1000).format('YYYY-MM-DD HH:mm:ss') : 0}
            onInput={(event: Event) => freeEndTime.value = dayjs((event.target as HTMLInputElement).value).unix()}
          />
        </div>
        <label class={mc('flex items-center gap-2', isFree.value ? 'opacity-40' : '')}>
          <span class="shrink-0">开始付费集数</span>
          <Input
            type="number"
            class="w-[200px]"
            inputClass="input input-bordered input-sm w-full max-w-xs"
            v-model={startPayingEpisodes.value}
          />
        </label>
        <label class={mc('flex items-center gap-2', isFree.value ? 'opacity-40' : '')}>
          <span class="shrink-0">单集定价</span>
          <Input
            type="number"
            class="w-[200px]"
            inputClass="input input-bordered input-sm w-full max-w-xs pr-16"
            v-model={episodesPrice.value}
            placeholder="1-10000的整数"
            min={1}
            max={10000}
            v-slots={{
              suffix: () => <span class="text-gray-800">金币/集</span>,
            }}
          />
        </label>
        { selectedDramas.value.every(item => import.meta.env.VITE_DRAMA_WAVE_APP_ID.split(',').includes('' + item.app_id))
          ? (
              <div class="gap-8 py-4">
                <label class="flex items-center cursor-pointer gap-2">
                  <span class="shrink-0">双端同步</span>
                  <input class="checkbox checkbox-sm checkbox-primary" type="checkbox" v-model={platform_sync_list.value} />
                </label>
              </div>
            )
          : null}
      </div>
      <div class="btn-group flex w-full items-center justify-end gap-3 absolute bottom-4 right-5">
        <Button class="btn btn-default btn-sm" onClick={() => emit('cancel')}>
          取消
        </Button>
        <Button class="btn btn-primary btn-sm" onClick={save} disabled={loading.value}>
          {loading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
          保存
        </Button>
      </div>
    </div>
  )
})
