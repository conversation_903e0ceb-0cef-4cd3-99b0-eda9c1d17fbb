import { ref } from 'vue'
import { MyEarnings } from './my-earnings/my-earnings'
import { apiGetCoins, apiGetExchangeCoinsList, apiGetExchangeDetails, apiGetLastRedeemAccount, apiGetMyEarnings, apiGetMyExchange } from './coins-api'
import { CoinsGift, ExchangeDetails, MyExchange } from './exchange/exchange'
import { bindLoading } from '@skynet/shared'
import { useLoadingStore } from '../common/loading/use-loading-store'
import { exchangeCoins } from './images/images'

const exchangePageParams = ref<ExchangeDetails>()

export const useCoinsStore = () => {
  const coins = ref<number>()
  const defaultMyEarnings = {
    list: [],
    page: 1,
    page_size: 10,
    has_more: false,
  }
  const gettingMyEarnings = ref(false)
  const gettingMyExchange = ref(false)
  const account = ref()

  const defaultMyExchange = {
    list: [],
    page: 1,
    page_size: 10,
    has_more: false,
  }

  const myEarnings = ref<{
    list: MyEarnings[]
    has_more: boolean
    page: number
    page_size: number
    last_id?: number
  }>(defaultMyEarnings)

  const myExchange = ref<{
    list: MyExchange[]
    has_more: boolean
    page: number
    page_size: number
    last_id?: number
  }>(defaultMyExchange)

  const getCoins = async () => {
    const res = await apiGetCoins()
    if (!res.data) return
    coins.value = res.data.coins.amount
  }

  const getMyEarnings = async (page: number, page_size: number, last_id?: number) => {
    const res = await bindLoading(apiGetMyEarnings({ page, page_size, last_id }), gettingMyEarnings)
    if (!res.data || !res.data.gold_flow_list) return
    // 过滤掉myEarnings.value.list重复的id
    const uniqueList = res.data.gold_flow_list.filter(item => !myEarnings.value.list.some(t => t.id === item.id))
    myEarnings.value = {
      list: [...myEarnings.value.list, ...uniqueList],
      has_more: res.data?.has_more,
      page,
      page_size,
      last_id: res.data?.last_id, // 取最后一个id
    }
  }

  const getMyExchange = async (page: number, page_size: number, last_id?: number) => {
    const res = await bindLoading(apiGetMyExchange({ page, page_size, last_id }), gettingMyExchange)
    if (!res.data) return
    // 过滤掉myExchange.value.list重复的id
    const uniqueList = res.data.list.filter(item => !myExchange.value.list.some(t => t.id === item.id))
    myExchange.value = {
      list: [...myExchange.value.list, ...uniqueList],
      has_more: res.data?.has_more,
      page,
      page_size,
      last_id: res.data?.last_id, // 取最后一个id
    }
  }

  const resetMyEarnings = () => {
    myEarnings.value = defaultMyEarnings
  }

  const resetMyExchange = () => {
    myExchange.value = defaultMyExchange
  }

  const { pageLoading } = useLoadingStore()

  const getExchangeDetails = async () => {
    const [res1, res2] = await bindLoading(Promise.allSettled([apiGetExchangeDetails(), apiGetExchangeCoinsList()]), pageLoading)
    exchangePageParams.value = res1.status === 'fulfilled' ? res1.value.data : undefined
    const coinsGifts = res2.status === 'fulfilled' ? res2.value.data?.reward_list : []
    if (!coinsGifts || coinsGifts.length === 0) return
    exchangePageParams.value?.exchange_list.push({
      id: exchangePageParams.value?.exchange_list.length ?? 0,
      name: 'Coins',
      image_url: '',
      can_choose: true,
      gift_list: coinsGifts?.map(item => ({
        type: 'coins',
        id: item.welfare_id.toString(),
        name: item.title,
        image_url: item.icon,
        amount: item.coins.toString(),
        user_coins: item.user_diamonds ?? 0,
        status: item.status,
        gift_coins: item.task_total_gold,
        ratio: (item.user_diamonds ?? 0) / item.task_total_gold,
        can_exchange: item.button_can_click,
        welfare_key: item.welfare_key,
        button_txt: item.button_text,
      })),
    })
  }

  const getLastRedeemAccount = async (type: string) => {
    if (account.value) return
    const res = await apiGetLastRedeemAccount(type)
    if (!res.data) return
    account.value = res.data.account
  }

  return {
    coins,
    gettingMyEarnings,
    gettingMyExchange,
    myEarnings,
    myExchange,
    exchangePageParams,
    getCoins,
    getMyEarnings,
    getMyExchange,
    resetMyEarnings,
    resetMyExchange,
    getExchangeDetails,
    getLastRedeemAccount,
    account,
    pageLoading,
  }
}
