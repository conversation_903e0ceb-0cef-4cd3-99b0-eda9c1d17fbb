export const en = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: 'Popular Choices',
    },
  },
  myList: {
    watchHistory: 'Watch History',
    myList: 'My List',
    seeAll: 'Alles ansehen',
    noData: 'No related results',
  },
  // 分享页
  share: {
    watchNow: 'Watch Now',
    episodeDialog: {
      title: 'Join me and grab extra coins!',
      description: 'I\'m hooked on this drama!',
      tips: 'Recommendation',
      button: 'Watch Full Drama',
      limit24: 'limited time: 24h',
    },
    toast: {
      takeOffShelf: 'No Content Available',
    },
  },
  'customer-service-center': {
    title: 'Customer Service Center',
    description: 'Description',
    'description-placeholder': 'Provide details to help us understand the problem',
    'email-placeholder': 'Enter your email to receive feedback updates',
    'submit-feedback': 'Submit',
    'enter-feedback': 'Other issues feedback',
    'upload-pictures': 'Upload pictures',
    feedback: 'Feedback',
    'problem-type': 'Problem type',
    'problem-type-placeholder': 'Please select',
    'payment-issue-placeholder': 'Please provide your ID and order receipt:Google: https://pay.google.com/ (order number starts with GPA...) iOS: App store - Apple ID - purchase history (Order ID)',
    'content-issue-placeholder': 'Please provide the drama title, episode numbers and missing subtitle languages',
    'ads-issue-placeholder': 'Please provide your device model, country, and language settings',
    'technical-issue-placeholder': 'Please provide your device model, country, and language settings',
    'other-issue-placeholder': 'Please provide more details, including your device model and country',

    'i-want-to-give-feedback': 'I want to give feedback',
    'reply-to-feedback': 'Reply to feedback',
    'are-you-satisfied-with-this-reply': 'Are you satisfied with this reply?',
    unsatisfied: 'unsatisfied',
    satisfied: 'satisfied',
    myFeedback: 'My Feedback',
    'description-min-length': 'Enter problem description (at least 6 characters).',
    'problem-type-not-choose': 'Please select the feedback type.',
    'description-empty': 'Input cannot be blank.',
  },
  terminate: {
    question: 'How do I manage my subscription？',
    desc: 'Click the button below to enter the contract management page. You can manage your subscription by clicking the contract switch:',
    title: 'Contract Management',
    expiration: 'Expiration time',
    manageDesc: 'Contractual Relationship',
  },
  regulations: {
    howUseData: 'How We Use Your Data',
    content: `If you select "Allow" on the App Tracking Transparency permission request, we will use your information and data to optimize our services and provide you with the best experience, otherwise we will only collect the data and will not conduct any tracking activities.

If you agree to allow DramaWave and our partners and suppliers to use data from your device to analyze how you use the application, personalize content recommendations and provide more information related services, please click "Agree and Continue" to continue.
`,
    acceptAll: 'Accept All',
    dataSettings: 'Data Settings',
    next: 'Next',
    cancel: 'Cancel',
    aboutYourPrivacy: 'About Your Privacy',
    personalDataName: 'Personalised Data',
    personalDataDescription: 'We use this information to remember your choices and to provide  personalised advertising and content recommendations, notifications and  reminders, etc.',
    advertisementDataName: 'Advertisement Data',
    advertisementDataDescription: 'Cookies, identifiers of your  device, or other information may be stored or accessed on your device  for the purpose of online advertisements. User data may also be shared  with our advertising partners.',
    behaviorsDataName: 'Behavioural Data',
    behaviorsDataDescription: 'Data related to analytics, such as cookies or device identifiers, may be  stored for the purpose of measuring and analyzing usage, behavior, and  advertising performance.',
    privacyAndTerms: 'Privacy Policy and Terms of Service',
    referToPrivacy: 'Please refer to the Privacy Policy',
    referToTerms: 'Please refer to the Terms of Service',
    agreeAndSave: 'Agree & Save',
  },
  coins: {
    earnings: {
      title: 'My Earnings',
      total: 'Total Diamonds',
      exchange: 'Exchange',
      earningsHistory: 'Earnings History',
      exchangeHistory: 'Exchange History',
      noRecords: 'No Record',
      last50earnings: 'Only the last 50 earnings are displayed',
      last50exchanges: 'Only the last 50 exchange records are displayed',
      benefits: 'Benefits',
      gotIt: 'Got it',
    },
    exchange: {
      title: 'Exchange Zone',
      notOpen: 'Not yet opened, please stay tuned',
      receiveBtn: 'Happily Receive',
      earnCoins: 'Earn Diamonds',
      redeemRequiredXCoins: 'Redeem Required {X} Diamonds',
      exchangeSuccess: 'Exchange Successfully',
      cardId: 'Card ID',
      exchangeFailed: 'Exchange Failed',
      transferFailedInsufficientStock: 'Out of stock. Please come early tomorrow.',
      gotItBtn: 'Got it',
      transferFailedRiskControl: 'Account unusual, temporarily unable to exchange.',
      tryAgainBtn: 'Try again Later',
      transferConfirm: 'Transfer Confirm',
      paypalAccountEmail: 'Paypal Account email',
      confirmPaypalAccountEmail: 'Confirm Paypal Account email',
      transferX: 'Transfer {X}',
      transferXConfirm: 'Transfer {X} Confirm',
      transferToYourAccountX: 'Transfer to your account {X}',
      confirmBtn: 'Confirm',
      redeemFailed: 'Redemption failed',
      redeemFailedContent: 'There was an error. Please try again later.',
      transferSuccessTitle: 'We\'re sending the money!',
      transferSuccessContent: 'Your money will arrive within 5 minutes to 72 hours.',
      copySuccess: 'Copy Success',
      enterEmailError: 'Please enter a valid email address',
      enterEmailValidError: 'Input error, please re-enter',
      emailNotMatchError: 'The email address does not match, please re-enter',
    },
  },
  meal_check_in: {
    sleeping_link: 'Earn Diamonds While Sleeping',
    rules_title: 'Activity Rules',
    benefits_title: 'Benefits',
    rules_1: '1. Every day, there are four time slots for you to check in your meals: Breakfast: 07:00-10:00; Lunch: 11:00-14:00; Dinner: 17:00-20:00; Midnight Snack: 21:00-24:00.',
    rules_2: '2. If you miss the corresponding check-in time, you can watch a video to reclaim the reward.',
    rules_3: '3. Rewards reset every day at midnight, so don\'t forget to claim your rewards.',
  },
  sleep_check_in: {
    earn_link: 'Record meals to earn',
    benefits_title: 'Benefits',
    rules_1:
      '1. From 7 PM to 12 AM every night, you can activate the \'Sleep to Earn\' mode.',
    rules_2:
      '2. After activating it, the next day, after sleeping for 8 hours, you can claim the sleep reward between 8 AM and 12 PM.',
    rules_3:
      '3. If you miss the reward claim time, you can watch a video between 12 PM and 7 PM the next day to reclaim the reward.',
  },
  bind_email: {
    title: 'Enter Your Email Address',
    list_1: 'Link your email address to receive exclusive reward coins.',
    list_2: 'Get notifications about upcoming events and special offers.',
    list_3: 'Be the first to know about new episodes.',
    verify: 'Verify',
    email_error: 'Please enter a valid email address',
    verify_success: 'Claim successful!',
    verify_progress: 'Email is being verified',
    re_enter: 'Re-enter',
    email_placeholder: 'Please enter your email address.',
    email_repeat: 'This email has already participated in the event!',
  },
  toast: {
    'upload-failed': 'Upload failed, please upload again',
    'submit-success': 'Submit successfully',
    'email-incorrect': 'The email format is incorrect',
  },
  not_found: {
    title: 'The page you are looking for does not exist',
  },
  infinityScroll: {
    end: 'The End',
    loading: 'Loading...',
  },
}
