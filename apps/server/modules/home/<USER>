declare namespace M {
  type CaptionUrl = {
    lang: string
    name: string
    url: string
  }

  type Mp4Url = {
    trans_720p: string
    trans_540p: string
    trans_360p: string
  }

  type EpisodeInfo = {
    id: string
    name: string
    cover: string
    video_url: string
    mp4_urls: Mp4Url
    caption_urls: CaptionUrl[]
    index: number
    unlock: true
    duration: number
    episode_price: number
    video_type: string
    new: true
    update_time: number
  }

  type RInfo = {
    session_id: string
    request_id: string
    page_num: string
    page_size: string
    item_idx: string
  }

  type Drama = {
    key?: string
    cover?: string
    title?: string
    desc?: string
    tag?: string[]
    content_tags?: string[]
    episode_count?: number
    view_episode?: number
    view_count?: number
    follow_count?: number
    episode_info?: EpisodeInfo
    release_round?: number // 1: 首发
    link_type?: number
    link?: string
    r_info?: RInfo
    style?: number // 1是左下角，2是榜单，3是橙色的，4是灰色的纯文字
    id?: string
    name?: string
    bg_color?: string
  }

  type HomepageModule = {
    type?: string
    module_name?: string // 模块展示名称
    module_desc?: string // 模块描述
    module_key?: string
    scene_source?: string // 场景来源
    show_title?: boolean // 是否展示标题,
    items?: Drama[]
  }

  type Homepage = {
    module_list: HomepageModule[]
  }

  type Tab = {
    name: string
    tab_key: string
    position_index: number // 位置
    business_name: string
  }
}

declare namespace Api {

  namespace GetTabList {
    /** 首页-Tab列表  请求参数 */
    type Query = {}
    /** 首页-Tab列表  响应 */
    type Response = ApiResponse<{
      list: M.Tab[]
    }>
  }

  namespace GetHomeModuleList {
    /** 首页-Tab内容页  请求参数 */
    type Query = {
      tab_key: string
      position_index: number
      first: string // 传字符串
    }
    /** 首页-Tab内容页  响应 */
    type Response = ApiResponse<{
      items: HomepageModule[]
      page_info: PageInfo
    }>
  }

  namespace GetFeed {
    /** 首页- tab-Feed 请求参数 */
    type Query = {
      module_key: string // 必填
      next: string
    }
    /** 首页- tab-Feed 响应 */
    type Response = ApiResponse<{
      page_info: PageInfo
      items: Drama[]
    }>
  }

  namespace GetTabs {
    /** 首页-Tab列表 请求参数 */
    type Query = {}
    /** 首页-Tab列表 响应 */
    type Response = ApiResponse<TabList>
  }

}
