import { httpClient } from 'src/lib/http-client'

export const apiGetMyWallet = () => {
  return httpClient.get<ApiResponse<MyWallet.MyWallet>>('/fr-api/wallet/my')
}

export const apiGetPurchaseHistoryList = (data: { next?: string }) => {

  console.log('purchaseHistory 请求中')
  return httpClient.get<ApiResponse<MyWallet.RechargeHistoryList>>('/fr-api/wallet/rewards/list', data)
}

export const apiGetConsumptionList = (data: { next?: string }) => {
  return httpClient.get<ApiResponse<MyWallet.RechargeHistoryList>>('/fr-api/wallet/consumption/list', data)
}
export const apiAutoUnlock = (data: { auto_unlock: number }) => {
  return httpClient.post<ApiResponse<MyWallet.MyWallet>>('/fr-api/wallet/autounlock/change', data)
}
