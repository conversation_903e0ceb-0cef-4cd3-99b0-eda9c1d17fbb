import { createComponent } from '@skynet/shared'
import free from '../image/free.png'
type RecommendLayoutOptions = {
  props: {
    title: string
    list: M.DramaItem[]
    show_title?: boolean
  }
}
export const RecommendLayout = createComponent<RecommendLayoutOptions>({
  props: {
    title: '',
    list: [],
    show_title: true,
  },
}, props => {
  const dramaItem = (drama: M.DramaItem) => {
    return (
      <div class="flex flex-col relative items-center w-full bg-[#1d1d1e] rounded-md">
        {drama.free && <img src={free} class="absolute right-1 top-1 w-40px h-auto object-cover shadow-sm" />}
        <img class="object-cover rounded-md" src={drama.cover} />
        <div class="w-full text-left text-[#fdfbfc] text-sm font-normal my-2 font-['SF Pro'] line-clamp-2 px-2">{drama.title}</div>
      </div>
    )
  }
  return () => (
    props.list && (
      <x-recommend class="w-full flex flex-col gap-2">
        {props.show_title && <x-recommend-title class="w-full h-[50px] pt-4 pb-3 text-[#fdfbfc] text-lg font-bold font-['SF Pro'] leading-snug">{props.title}</x-recommend-title>}
        <x-recommend-content class="w-full flex gap-2">
          <x-recommend-column-1 class="w-1/2 flex flex-col gap-2 flex-wrap">
            {props.list.length > 0 && dramaItem(props.list[0])}
            {props.list.length > 2 && props.list.slice(2).filter((_, index) => index % 2 === 1).map(item => dramaItem(item))}
          </x-recommend-column-1>
          <x-recommend-column-2 class="w-1/2 flex flex-col gap-2 flex-wrap">
            {props.list.length > 1 && dramaItem(props.list[1])}
            {props.list.length > 2 && props.list.slice(2).filter((_, index) => index % 2 === 0).map(item => dramaItem(item))}
          </x-recommend-column-2>
        </x-recommend-content>
      </x-recommend>
    )
  )
})
