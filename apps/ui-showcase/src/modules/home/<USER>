import { createComponent } from '@skynet/shared'
import { TopBar } from '../top-bar/top-bar'
import { Icon } from '@skynet/ui'
import { Button } from '@skynet/ui'
import { RouterLink } from 'vue-router'
export const HomePage = createComponent(null, () => {
  return () => (
    <x-home-page class="block bg-fill-4 mt-4">
      <TopBar />
      <main class="flex flex-col items-center justify-center py-20 gap-10">
        <icon-wrapper class="inline-block relative mt-20">
          <Icon name="logos:vue" class="size-40" />
          <Icon name="logos:element" class="size-20 -mb-5 absolute left-1/2 -translate-x-1/2 bottom-full" />
        </icon-wrapper>
        <RouterLink to="/components">
          <Button class="btn bg-[#41B883] text-white">开始使用</Button>
        </RouterLink>
      </main>
    </x-home-page>
  )
})
