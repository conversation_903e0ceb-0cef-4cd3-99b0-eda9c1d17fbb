<template>
  <div class="flex flex-col items-center" :class="(seriesItem && !immersive) ? 'visible' : 'invisible'"
    @click="episodeSelectMenuVisible = true">
    <SvgIcon name="ic-first-fill" class="size-7" />
    <span class="text-xs font-normal leading-tight text-white ">{{ CT.episodes.ref }}</span>
  </div>
  <Popup v-model:show="episodeSelectMenuVisible" position="bottom" class="rounded-t-xl bg-black p-3 pt-4"
    :style="{ height: 'auto' }">
    <div class="flex gap-4">
      <Image class="overflow-hidden  rounded-md" width="2.38rem" height="3.19rem" :src="seriesItem?.cover" />
      <div class="flex-1">
        <div className="text-[#fdfbfc] mb-2 text-base leading-tight">
          {{ seriesItem?.name || '' }}
        </div>
        <span className="h-[22px]  px-2 py-1 bg-[#2e2f30] rounded-[100px] text-[#cccacb] text-xs">
          {{ t1 }}
        </span>
      </div>
      <div>
        <SvgIcon name="ic_close" class="size-[18px]" @click="() => episodeSelectMenuVisible = false" />
      </div>
    </div>
    <x-episode-select-rang class="my-4 block">
      <span v-for="(text, index) in rangesText" :key="text" class="group">
        <span class="text-sm text-[#CCCACB]" :class="currentChunk === index ? ' text-[#FC2763]' : ''"
          @click="currentChunk = index">{{ text
          }}</span>
        <Divider vertical class="!border-text-3 group-last:hidden" />
      </span>
    </x-episode-select-rang>
    <Swipe stop-propagation :initial-swipe="currentChunk" vertical :loop="false" :show-indicators="false"
      style="height: calc((100vw / 6) * 5)" @change="onSwipe">
      <SwipeItem v-for="(item, index) in ranges" :key="index">
        <x-episode-select class="grid grid-cols-6 grid-rows-5 gap-2 ">
          <template v-for="episode in item" :key="episode.id">
            <span :class="currentEpisodeId === episode.id ? 'bg-[#FC2763] text-white !font-bold' : ''" class="relative inline-flex aspect-square w-full items-center justify-center overflow-hidden rounded-lg
          bg-[#2E2F30] text-lg font-medium text-[#FDFBFC] " @click="() => {
            episodeSelectMenuVisible = false;
            if (!episode.unlock) {
              emit('unlock', episode.id) // 点小锁
              return;
            }
            handleEpisodeClick(episode.id)
          }">{{
            episode.index }}
              <span v-if="!episode.unlock && myWallet?.vip_level === 0"
                class="absolute right-0 top-0 h-3.5 w-4 bg-[#434546]">
                <SvgIcon name="lock" class="-mt-4 ml-0.5 size-3" />
              </span>
              <SvgIcon
                v-else-if="episode.unlock !== false && episode.video_type === 'charge' && myWallet?.vip_level && myWallet?.vip_level > 0"
                name="tag-vip-2" class="absolute -top-[0.19rem] right-0 w-1/2" />
            </span>
          </template>
        </x-episode-select>
      </SwipeItem>
    </Swipe>
  </Popup>
</template>

<script setup lang="ts">
import { Swipe, SwipeItem } from 'vant';
import { betterReplace } from '@skynet/shared';
import { SvgIcon } from '@skynet/ui';
// import { apiWalletMy } from 'src/h5_modules/profile/profile-api';
import { Divider, Image, Popup } from 'vant';
import { computed, onBeforeMount, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { WalletDataInfo } from '../../profile/profile';
import { useMyWallet } from '../../wallet/use-my-wallet'
import { useSeriesItem } from '../use-series-item';
import { CT } from 'src/h5_modules/i18n/common.i18n';
import { T } from '../series-item-page.i18n';
import { useImmersive } from '../use-immersive';
const emit = defineEmits(['unlock']);

const rangeSize = 30;
const route = useRoute()
const router = useRouter()
const id = computed(() => route.params.series_id.toString() ?? '')
const { seriesItem, currentEpisodeId, episodeSelectMenuVisible } = useSeriesItem(id.value)
const t1 = computed(() => T.seriesItemPage.allEpisodes({ N: seriesItem.value?.episode_count }))
const { wallet } = useMyWallet()
const myWallet = ref<WalletDataInfo>(wallet.value);
const { immersive } = useImmersive(id.value)


// onBeforeMount(async () => {
//   // const res = await apiWalletMy();
//   myWallet.value = res.data
// })
watch(wallet, () => {
  myWallet.value = wallet.value
})

const episodes = computed(() => {
  return seriesItem.value?.episode_list || []
})
const currentChunk = ref(Math.floor((seriesItem.value?.episode?.index || 0) / rangeSize))

const ranges = computed(() => {
  const chunkSize = Math.ceil(episodes.value.length / rangeSize);
  const aa = new Array(chunkSize).fill(0).map((_, index) => {
    const start = index * rangeSize;
    const end = Math.min(start + rangeSize, episodes.value.length);
    return episodes.value.slice(start, end);
  });
  return aa;
})

const rangesText = computed(() => {
  const chunkSize = Math.ceil(episodes.value.length / rangeSize);
  return new Array(chunkSize).fill(0).map((_, index) => {
    const start = index * rangeSize + 1;
    const end = Math.min(start + rangeSize - 1, episodes.value.length);
    return `${start}-${end}`;
  });
})

const currentRange = computed(() => {
  return ranges.value[currentChunk.value]
})

const handleEpisodeClick = (id: string) => {
  const currentIndex = episodes.value.findIndex(item => item.id === currentEpisodeId.value)
  const nextIndex = episodes.value.findIndex(item => item.id === id)
  betterReplace(router, {
    params: { episode_id: id },
    query: { from_episode: currentIndex < nextIndex ? 'prev' : 'next' }
  })
}

const onSwipe = (index: number) => {
  setTimeout(() => {
    currentChunk.value = index
  }, 500);
}
// 支持的语言
</script>
