import { MergeClass } from '@skynet/ui'
import { FunctionalComponent } from 'vue'
import { report } from '@skynet/client-track'

export const ClientTrackDemo1: FunctionalComponent<{}> = () => {
  const onClick = () => {
    report({
      event: 'click',
      type: 'button',
      page_title: document.title,
      href: location.href,
    })
  }
  return (
    <MergeClass tag="div">
      <button class="btn btn-sm" onClick={onClick}>点击上报</button>
    </MergeClass>
  )
}

export default ClientTrackDemo1
