import { createComponent } from '@skynet/shared'
import { useRouter } from 'vue-router'
import { ElMenu, ElMenuItem, ElSubMenu, ElDivider, ElImage } from 'element-plus'
import { RouterView } from 'vue-router'
import { SvgIcon } from '@skynet/ui'
import { useMessage } from '../message/use-message'
import { computed, onBeforeMount, onMounted, ref } from 'vue'
import OpenPlatformLogo from '../logo/open-platform-logo'
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { useSession } from '../session/use-session'
import { useMe } from '../user/use-me'
import LangSwitch from '../common/lang-switch'
import { useI18n } from 'vue-i18n'

export const MainLayout = createComponent(null, () => {
  const { t } = useI18n()
  const router = useRouter()
  const { getUnreadMessageCount } = useMessage()
  const { signOut } = useSession()
  const { me, fetchMe } = useMe()

  const unreadMessageCount = ref(0)
  const handleOpen = (key: string, keyPath: string[]) => {
    // console.log(key, keyPath)
  }

  const handleClose = (key: string, keyPath: string[]) => {
    // console.log(key, keyPath)
  }
  onBeforeMount(async () => {
    void fetchMe()
    unreadMessageCount.value = await getUnreadMessageCount()
  })

  const handleSignOut = async () => {
    await signOut()
    void router.push('/sign_in')
  }

  const activeMenu = computed(() => {
    const path = router.currentRoute.value.path
    if (path === '/') return '/'
    return '/' + path.split('/')[1]
  })
  return () => (
    <x-main-layout class="block bg-[var(--fill-1)]">
      <x-top-bar class="sticky top-0 z-footer block bg-white">
        <nav class="mx-auto flex h-full max-w-[var(--pc-page-max-width)] items-center justify-between px-8 py-[22px]">
          <OpenPlatformLogo />

          <x-actions class="flex items-center">
            <div class="relative cursor-pointer" onClick={() => router.push('/message')}>
              <SvgIcon name="ic_bell" class="size-5" />
              {unreadMessageCount.value > 0 && (<div class="absolute -right-[3px] top-0 flex size-2.5 items-center justify-center rounded-full bg-brand-6 text-[6.6px] font-bold text-white">{ unreadMessageCount.value }</div>)}
            </div>
            <ElDivider direction="vertical" class="!mx-5 inline-block !h-4" />
            <ElDropdown trigger="click">
              {{
                default: () => (
                  <div class="flex cursor-pointer items-center">
                    <ElImage class="mr-2 size-6 rounded-full" src={me.value?.avatar}>
                      {{ default: () => <SvgIcon name="ic_avatar" class="size-6" />, error: () => <SvgIcon name="ic_avatar" class="size-6" /> }}
                    </ElImage>
                    <span class="mr-1">{me.value?.nickname || me.value?.phone}</span>
                    <SvgIcon name="ic-down" class="size-4" />
                  </div>
                ),
                dropdown: () => (
                  <ElDropdownMenu>
                    <ElDropdownItem onClick={() => router.push('/user')}><SvgIcon name="ic-cog-8-tooth" class="mr-2 size-5" />{t('profile')}</ElDropdownItem>
                    <ElDropdownItem onClick={handleSignOut}><SvgIcon name="ic-right-start-on-rectangle" class="mr-2 size-5" />{t('logOut')}</ElDropdownItem>
                  </ElDropdownMenu>
                ),
              }}
            </ElDropdown>
            <LangSwitch />
          </x-actions>
        </nav>
      </x-top-bar>
      <section class="mx-auto flex max-w-[var(--pc-page-max-width)] gap-4 px-8 py-4">
        <aside class="fixed shrink-0 grow-0">
          <ElMenu
            defaultActive={activeMenu.value}
            class="min-h-[calc(100vh-var(--top-bar-height))] w-[237px]  overflow-hidden rounded-xl !p-3"
            onOpen={handleOpen}
            onClose={handleClose}
            router={true}
          >
            <ElMenuItem index="/workbench" route="/workbench" v-slots={{
              title: () => (
                <>
                  <SvgIcon name="ic-computer-desktop" class="mr-2" />
                  <span>{t('workbench')}</span>
                </>
              ),
            }}
            />
            <ElSubMenu index="/" v-slots={{
              title: () => (
                <>
                  <SvgIcon name="ic-bookmark-square" class="mr-2" />
                  <span>{t('workManagement')}</span>
                </>
              ),
            }}
            >
              <ElMenuItem index="/play" onClick={() => router.push('/play')}>{t('shortPlayScript')}</ElMenuItem>
              <ElMenuItem index="/my-drama" onClick={() => router.push('/my-drama')}>{t('shortPlay')}</ElMenuItem>
            </ElSubMenu>
            <ElMenuItem index="/data-center" route="/data-center" v-slots={{
              title: () => (
                <>
                  <SvgIcon name="ic-data-analysis" class="mr-2" />
                  <span>{t('dataCenter')}</span>
                </>
              ),
            }}
            />
            <ElMenuItem index="/wallet" route="/wallet" v-slots={{
              title: () => (
                <>
                  <SvgIcon name="ic-wallet" class="mr-2" />
                  <span>{t('profitAnalysis')}</span>
                </>
              ),
            }}
            />
          </ElMenu>
        </aside>
        <RouterView class="ml-64 min-h-[calc(100vh-var(--top-bar-height))] flex-1  rounded-lg bg-white p-6" />
      </section>
    </x-main-layout>
  )
})

export default MainLayout
