/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, CreateForm, CreateTableOld, Icon, openDialog, showFailToast, showSuccessToast, SvgIcon } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiGetTimbreList, apiDelTimbre } from './timbre-api'

export const useTimbre = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    onQuery,
    onReset,
    columns,
    total,
    onPageChange,
    onPageSizeChange,
    getList,
    sourceOptions,
    cdnUrl
  }
}
const defaultParams = {
  gender: -1,
  page_index: 1,
  page_size: 20,
}
const Form = CreateForm<Api.Timbre.ListReqParams>()
const params = ref<Api.Timbre.ListReqParams>({ ...defaultParams })
const total = ref<number>(0)
const Table = CreateTableOld<Api.Timbre.TimbreItem>()
const list = ref<Api.Timbre.TimbreItem[]>([])
const loading = ref<boolean>(false)
const btnLoading = ref<boolean>(false)
const cdnUrl = 'https://img.tianmai.cn/'

const columns = computed(() => [
  { prop: 'title', label: '音色名称', minWidth: 120, fixed: true },
  { prop: 'desc', label: '音色描述', minWidth: 140 },
  { prop: 'sample_path', label: '音色试听', minWidth: 200, render: (scope: { row: Api.Timbre.TimbreItem }) => {
    return (
      <div>
        <audio src={`${cdnUrl}${scope.row.sample_path}`} preload="none" controls />
      </div>
    )
  } },
  { label: '操作', width: 80, fixed: 'right', render: (scope: { row: Api.Timbre.TimbreItem }) => {
    return (
      <div>
        <Button class="btn btn-link btn-xs" onClick={() => {
          const hideDialog = openDialog({
            title: '删除音色',
            mainClass: 'pb-0 px-5',
            body: () => (
              <x-timbre-confirm-dialog class="flex flex-col gap-y-[25px]">
                <x-timbre-body>是否确认删除【{scope.row.title}】?</x-timbre-body>
                <x-timbre-footer class="w-full flex justify-end gap-x-[10px]">
                  <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                  <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                    btnLoading.value = true
                    try {
                      await apiDelTimbre({ timbre_id: scope.row.timbre_id || 0 })
                      showSuccessToast('操作成功')
                      hideDialog()
                      onQuery()
                    } catch (error: any) {
                      showFailToast(error.response.data.err_msg || '删除失败')
                    } finally {
                      btnLoading.value = false
                    }
                  }}
                  >
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    确定
                  </button>
                </x-timbre-footer>
              </x-timbre-confirm-dialog>
            ),
          })
        }}>删除</Button>
      </div>
    )
  } },
])

const onReset = () => {
  params.value = { ...defaultParams }
  void onQuery()
}

const getList = async () => {
  try {
    loading.value = true
    const res = await apiGetTimbreList(params.value)
    list.value = res.data?.timbre_list || []
    total.value = res.data?.total || 0
  } catch (error) {
    list.value = []
  } finally {
    loading.value = false
  }
}

const onPageChange = (page: number) => {
  params.value.page_index = page
  void getList()
}

const onPageSizeChange = (size: number) => {
  params.value.page_size = size
  params.value.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_index = 1
  void getList()
}

const sourceOptions: {
  label: string
  value: string
}[] = [
  { label: 'minimax', value: 'minimax' },
  { label: 'hume', value: 'hume' },
  { label: 'elevent', value: 'elevent' },
]
