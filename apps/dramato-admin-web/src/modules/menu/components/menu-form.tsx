import { createComponent, fn, useValidator } from '@skynet/shared'
import { CreateForm, transformNumber } from '@skynet/ui'
import { ref, watchEffect } from 'vue'
import { set } from 'lodash-es'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { useMenu } from '../use-menu'

type MenuFormOptions = {
  props: {
    menuItem: M.IMenuItem
  }
  emits: {
    menuItemChange: (menuItem: M.IMenuItem) => void
  }
}
export const MenuForm = createComponent<MenuFormOptions>({
  props: {
    menuItem: {
      parent_name: '',
      parent_id: -1,
      id: -1,
      path: '',
      title: '',
      roles: [],
      serial_number: 0
    }
  },
  emits: {
    menuItemChange: () => fn
  }
}, (props, { emit }) => {
  const { roles } = useMenu()

  const Form = CreateForm<M.IMenuItem>()
  const menuForm = ref<M.IMenuItem>({
    parent_id: -1,
    id: undefined,
    path: '',
    title: '',
    children: [],
    roles: []
  })

  const formRules = z.object({
    title: z.string().min(1, '请输入菜单名称').max(20, '最多20个字符'),
  })

  const { error, validateAll } = useValidator(menuForm, formRules)

  const onSave = () => {
    if (!validateAll()) {
      return
    }
    emit('menuItemChange', menuForm.value)
  }

  watchEffect(() => {
    menuForm.value = { ...props.menuItem }
  })

  return () => (
    <Form
      class="flex-col"
      data={menuForm.value}
      error={error.value}
      onChange={(path, value) => {
        set(menuForm.value, path, value)
      }}
      onSubmit={onSave}
      items={[
        {
          label: '父菜单',
          path: 'parent_name',
          input: {
            disabled: true,
            type: 'text',
          },
        },
        {
          label: requiredLabel('菜单/目录名称'),
          path: 'title',
          input: {
            type: 'text',
          },
        },
        {
          label: '菜单路由',
          path: 'path',
          hint: requiredLabel('如果菜单路由为空则是目录'),
          input: {
            type: 'text',
            disabled: menuForm.value.parent_id === 0 || menuForm.value.parent_id === -1,
          },
        },
        {
          label: '角色',
          path: 'roles',
          input: {
            type: 'multi-select',
            popoverWrapperClass: 'z-popover-in-dialog',
            options: roles.value.map(row => {
              return {
                value: row.id,
                label: row.id === 1 ? `${row.name}(默认选中)` : row.name,
                disabled: row.id === 1
              }
            }),
            class: 'w-[520px]'
          },
        },
        {
          label: '排序',
          path: 'serial_number',
          hint: requiredLabel('越小排越前面'),
          input: {
            type: 'number',
          },
          transform: transformNumber
        }
      ]}
    />
  )
})
