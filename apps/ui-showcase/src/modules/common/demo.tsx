import { ClassName, createComponent, mc, removeIndent, render, Renderable, useBool, usePromise } from '@skynet/shared'
import { Button, Icon, MergeClass, showAlert } from '@skynet/ui'
import { computed, ref, watch } from 'vue'
import('highlight.js/styles/github.css')
import hljs from 'highlight.js/lib/core'
import typescript from 'highlight.js/lib/languages/typescript'
import xml from 'highlight.js/lib/languages/xml'
import { useClipboard } from '@vueuse/core'

hljs.registerLanguage('xml', xml)
hljs.registerLanguage('typescript', typescript)

type ExampleOptions = {
  props: {
    component: Renderable
    code?: string
    buttonClass?: ClassName
    codeClass?: ClassName
  }
}
export const Demo = createComponent<ExampleOptions>({
  props: {
    component: '',
    code: '',
    buttonClass: '',
    codeClass: '',
  },
}, props => {
  const component = ref<Renderable | null>(null)
  const code = ref(props.code)

  if (typeof props.component === 'string' && props.component !== '') {
    if (props.component.startsWith('.')) {
      throw new Error('component 不能以 . 开头')
    }
    usePromise(import(/* @vite-ignore */props.component), {
      finally: (data, error, loading) => {
        if (!data?.default) throw new Error(`${props.component as string} 加载失败或者没有默认导出`)
        component.value = data.default
      },
    })
    if (!props.code) {
      usePromise(import(/* @vite-ignore */props.component + '?raw'), {
        finally: (data, error, loading) => {
          if (!data?.default) throw new Error(`${props.component as string} 加载失败或者没有默认导出`)
          code.value = data.default
        },
      })
    }
  } else {
    component.value = props.component
  }

  const [codeVisible, toggle] = useBool(false)
  const { copy, copied } = useClipboard()
  watch(() => copied.value, v => {
    if (v) { showAlert('复制成功') }
  })

  const highlightedCode = computed(() => hljs.highlight(
    removeIndent(code.value),
    { language: 'typescript' },
  ).value)

  return () => (
    <MergeClass tag="div" baseClass="block border border-gray-200">
      <div class="p-4">
        {component.value && render(component.value)}
      </div>
      <div class="border-t border-gray-200 py-2 px-2 bg-gray-100 flex gap-2">
        {codeVisible.value
          ? (
              <Button class={mc('border border-gray-300 rounded-md px-2 py-1 bg-white flex gap-1 items-center', props.buttonClass)} onClick={() => toggle(false)}>
                <Icon name="mdi:code-tags" />
                隐藏代码
              </Button>
            )
          : (
              <Button class={mc('border border-gray-300 rounded-md px-2 py-1 bg-white flex gap-1 items-center', props.buttonClass)} onClick={() => toggle(true)}>
                <Icon name="mdi:code-tags" />
                查看代码
              </Button>
            )}
        <Button class="border border-gray-300 rounded-md px-2 py-1 bg-white flex gap-1 items-center" onClick={() => copy(removeIndent(code.value))}>
          <Icon name="mdi:content-copy" />
          复制
        </Button>
      </div>
      {codeVisible.value && (
        <MergeClass baseClass="max-h-[80vh] overflow-auto " class={props.codeClass}>
          <pre><code v-html={highlightedCode.value} /></pre>
        </MergeClass>
      )}
    </MergeClass>
  )
})
