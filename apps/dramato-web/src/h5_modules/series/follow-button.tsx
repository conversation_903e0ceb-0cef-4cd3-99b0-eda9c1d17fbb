import { createComponent, friendly, required } from '@skynet/shared'
import { SvgIcon } from '@skynet/ui'
import { CT } from '../i18n/common.i18n'
import { computed, onMounted } from 'vue'
import { useLocale } from '../i18n/i18n'
import { useSeriesItem } from './use-series-item'
import { showToast } from 'vant'
import { T } from './follow-button.i18n'
import { T as TS } from './series-item-page.i18n'
type FollowButtonOptions = {
  props: {
    seriesId: string
  }
}
export const FollowButton = createComponent<FollowButtonOptions>({
  props: {
    seriesId: required,
  },
}, props => {
  const { seriesItem, follow, requestingFollow } = useSeriesItem(props.seriesId)
  async function onClick() {
    if (!seriesItem.value) return
    if (requestingFollow.value) return
    if (seriesItem.value.following) {
      seriesItem.value.following = false
      seriesItem.value.follow_count -= 1
      showToast(T.followButton.removedFromMyList())
      await follow(false).catch(() => {
        seriesItem.value!.following = true
        seriesItem.value!.follow_count += 1
        showToast(CT.operationFailed())
      })
    } else {
      seriesItem.value.following = true
      seriesItem.value.follow_count += 1
      showToast(T.followButton.addedToMyList())
      await follow(true).catch(() => {
        seriesItem.value!.following = false
        seriesItem.value!.follow_count -= 1
        showToast(CT.operationFailed())
      })
    }
  }
  return () => seriesItem.value ? (
    <x-follow-button class="flex flex-col items-center" onClick={onClick}>
      <SvgIcon name="ic-collectno-fill" noColor class={['size-7', seriesItem.value.following && 'text-brand-6']} />
      <span class="text-xs capitalize">{
        seriesItem.value.following ? friendly(seriesItem.value.follow_count) : TS.seriesItemPage.follow()
      }</span>
    </x-follow-button>
  ) : seriesItem.value
})

export default FollowButton
