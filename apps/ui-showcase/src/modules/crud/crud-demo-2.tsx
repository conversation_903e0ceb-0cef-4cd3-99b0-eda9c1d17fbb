import { createComponent } from '@skynet/shared'
import { CreateTableOld } from '@skynet/ui'
import { Wrapper } from 'src/layout/wrapper'
import { ref } from 'vue'
type CrudDemo1Options = {
  props: {}
}
export const CrudDemo1 = createComponent<CrudDemo1Options>({
  props: {},
}, () => {
  const Table = CreateTableOld<{
    id: number
    name: string
  }>()
  const list = ref([{
    id: 0, name: '<PERSON> Frank Frank Frank Frank Frank Frank Frank Frank Frank Frank Frank Frank Frank Frank Frank Frank <PERSON>',
  }, {
    id: 1, name: 'Jack',
  }])
  return () => (
    <div class="space-y-4">
      <Wrapper>
        <Table
          class="tm-table-fix-first-column tm-table-fix-last-column"
          list={list.value}
          columns={[
            ['ID', 'id', { class: 'w-[100px]' }],
            ['姓名', 'name', { class: 'w-[1200px]' }],
            ['操作', () => (
              <div class="space-x-2">
                <button class="btn btn-xs btn-outline">Edit</button>
                <button class="btn btn-xs btn-outline">Delete</button>
              </div>
            ), { class: 'w-[200px]' },
            ],
          ]}
        />
      </Wrapper>
    </div>
  )
})

export default CrudDemo1
