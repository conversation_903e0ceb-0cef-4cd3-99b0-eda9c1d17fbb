import { createComponent, mc } from '@skynet/shared'
import { TransitionPresets, useTransition } from '@vueuse/core'
import { preloadImages } from 'src/lib/preload-images'
import { track } from 'src/lib/track'
import { useLocale } from 'src/lib/use-locale'
import { Back } from 'src/modules/common/back/back'
import Loading from 'src/modules/common/loading/loading'
import { showToast } from 'src/modules/common/toast/toast'
import router from 'src/router'
import { onMounted, onUnmounted, ref, watch, watchEffect } from 'vue'
import { useCoinsStore } from '../coins-store'
import { amazonCard, amazonDialog, background, coins, dialogBg, horn, mercadoPagoCard, mercadoPagoDialog, ovoCard, ovoDialog, paypalCard, paypalDialog } from '../images/images'
import { ExchangeCard, ExchangeDetails } from './exchange'
import { AmazonGift } from './gifts/amazon-gift'
import { PaypalGift } from './gifts/paypal-gift'
import { useBroadcast } from './use-broadcast'
import { SpotifyGift } from './gifts/spotify-gift'
import { MercadoPagoGift } from './gifts/mercado-pago-gift'
import { OvoGift } from './gifts/ovo-gift'
import { Button } from '@skynet/ui'
import { openDialog } from 'src/modules/common/dialog'
import { useBetterScroll } from 'src/lib/use-better-scroll'
export const ExchangePage = createComponent(null, props => {
  const from = router.currentRoute.value.query.from
  const isFromMyEarnings = from === 'myearnings'
  const broadcasts = ref<string[]>([])
  const activeCard = ref<string>()

  preloadImages([horn, dialogBg, amazonCard, paypalCard, amazonDialog, mercadoPagoCard, ovoCard, paypalDialog, ovoDialog, mercadoPagoDialog])

  const { getBroadcast, broadcast, broadcastPool } = useBroadcast()
  const wrapper = ref<HTMLDivElement>()
  const { t } = useLocale()

  const { getExchangeDetails, exchangePageParams, pageLoading, getLastRedeemAccount } = useCoinsStore()

  const decodeString = (str: string) => {
    return decodeURIComponent(str).replace(/<red>(.*?)<\/red>/g, `<span class="text-[var(--brand-7)]">$1</span>`)
  }

  let interval: number | undefined = undefined

  const y = ref(100)

  const targetY = useTransition(y, {
    duration: 500,
    transition: TransitionPresets.easeOutCubic,
  })

  const handleClickCard = (item: ExchangeCard) => {
    track('exchangezone', 'brand', 'click')
    if (item.can_choose) {
      activeCard.value = item.name
    } else {
      showToast(t('coins.exchange.notOpen'))
    }
  }

  watch(() => exchangePageParams.value, () => {
    if (exchangePageParams.value) {
      if (exchangePageParams.value.exchange_list && exchangePageParams.value.exchange_list.filter(item => item.can_choose).length > 0) {
        activeCard.value = exchangePageParams.value.exchange_list.filter(item => item.can_choose)[0].name
      }
    }
  })

  watchEffect(() => {
    if (broadcastPool.value.length > 0 && !interval) {
      interval = window.setInterval(() => {
        const b = broadcast()
        if (b) {
          y.value -= 100
          broadcasts.value.push(decodeString(b))
        }
      }, 5000)
    }
  })

  const isScroll = ref(false)
  const handleScroll = () => {
    isScroll.value = true
  }

  const openHelpDialog = () => {
    const closeDialog = openDialog({
      title: 'Aturan Penukaran Hadiah',
      closeVisible: false,
      mainClass: '',
      body: (
        <x-explain-dialog>
          <x-explain id="explain-wrapper" class="block mt-1 px-[26px] h-[200px] font-normal text-[var(--text-1)] text-sm break-words overflow-hidden">
            <x-wrapper class="block pb-7" style="-webkit-overflow-scrolling: touch;">
              <p>1. Platform ini menyediakan fitur penarikan tunai, yang dapat ditarik ke akun pembayaran Anda (seperti Paypal, OVO, dll, sesuai dengan tampilan halaman yang sebenarnya).</p>
              <p>2. Platform ini menyediakan fitur pertukaran kartu hadiah, yang akan diberikan dalam bentuk kode kartu ke akun FreeReels Anda (seperti kartu hadiah Amazon, Google Play Store, dll, sesuai dengan tampilan halaman yang sebenarnya).</p>
              <p>3. Penarikan tunai dan kartu hadiah dibagi menjadi berbagai tingkatan berdasarkan nilai hadiah. Ketika koin Anda memenuhi persyaratan penarikan, Anda dapat melakukan penarikan. Setiap pengguna hanya dapat melakukan penarikan/pertukaran sekali dalam sehari, dengan jumlah total tidak terbatas. Beberapa tingkatan adalah keuntungan satu kali, hanya untuk pengguna baru. Untuk memberikan pengalaman penarikan yang lebih baik, FreeReels akan secara berkala memberikan kuota penarikan sementara atau kuota yang dapat ditarik setelah memenuhi persyaratan tertentu kepada sebagian atau seluruh pengguna (baru). Jika pengguna memperoleh jenis kuota ini, jumlah penggunaan terbatas. Persyaratan spesifik, kuota, dan batasan jumlah penggunaan dapat dilihat pada tampilan halaman.</p>
              <p>4. Ketika pengguna memilih untuk menarik tunai, akan dikenakan biaya transfer yang harus ditanggung oleh pengguna. Jumlah tunai yang diterima akhirnya adalah jumlah setelah biaya transfer dipotong.</p>
              <p>5. Ketika pengguna memilih untuk menarik tunai, Anda perlu memasukkan akun platform pembayaran pihak ketiga Anda di FreeReels. Pastikan Anda memasukkan dengan benar untuk menghindari kegagalan penarikan.</p>
              <p>6. Ketika pengguna memilih untuk menarik tunai, biasanya akan diterima dalam waktu 3-5 hari (jika bertepatan dengan puncak penarikan atau hari libur, waktu mungkin akan diperpanjang). Selama puncak aktivitas, karena kemacetan jaringan, pengguna mungkin tidak dapat melakukan penarikan dalam waktu singkat. Platform akan berusaha keras untuk memulihkan fungsi penarikan secepat mungkin. Namun, platform tidak bertanggung jawab atas hal ini.</p>
              <p>7. Penarikan tunai dan pertukaran kartu hadiah memiliki batas total stok harian. Kami berkomitmen bahwa kegiatan ini adalah nyata dan efektif, tetapi jumlah stok setiap wilayah akan disesuaikan sesuai dengan tahap kegiatan lokal. Misalnya, pada awal peluncuran, platform akan mengatur selama beberapa waktu untuk memastikan bahwa hadiah dapat diberikan kepada pengguna yang sebenarnya. Seiring dengan matangnya kegiatan di wilayah tersebut, platform akan berusaha agar setiap pengguna mendapatkan hadiah yang seharusnya. Namun, platform tidak bertanggung jawab atas hal ini.</p>
              <p>8. Jika Anda tidak masuk ke halaman manfaat selama 30 hari berturut-turut, atau tidak melakukan penarikan atau pertukaran kartu hadiah dalam periode kegiatan ini (satu saja), maka semua manfaat yang telah diberikan platform kepada Anda sebelumnya akan kedaluwarsa. Kegagalan untuk menarik atau menukarkan kartu hadiah dalam waktu yang ditentukan dianggap sebagai penghapusan hak Anda untuk menarik atau menukarkan kartu hadiah. Saldo hadiah dalam akun Anda akan direset menjadi nol, dan platform tidak akan dan tidak berkewajiban memberikan kompensasi dalam bentuk apa pun.</p>
              <p>9. Kami menggunakan teknologi kecerdasan buatan canggih untuk menganalisis perilaku Anda. Untuk melindungi keamanan akun dan aset Anda selama proses penarikan/pertukaran hadiah, platform berhak untuk meninjau pesanan Anda, membatasi jumlah penarikan/jumlah akun, dan meningkatkan langkah-langkah keamanan kapan saja (termasuk tetapi tidak terbatas pada verifikasi SMS, verifikasi identitas, pengenalan wajah, dll). Jika Anda gagal melalui verifikasi keamanan, Anda tidak akan dapat melakukan penarikan/pertukaran hadiah. Jika ditemukan tindakan palsu atau kecurangan lainnya, kami berhak untuk menghentikan Anda dari menggunakan platform (mengisi kode undangan, mengumpulkan koin, menarik tunai, mendapatkan hadiah) serta mencabut hadiah yang telah Anda peroleh. Pengguna harus menanggung konsekuensi negatif dari tidak dapat menarik/pertukaran hadiah, dan platform tidak bertanggung jawab atas hal ini.</p>
              <p>10. Hadiah atau keuntungan yang diperoleh pengguna melalui platform mungkin memerlukan potongan pajak atau pelaporan pajak oleh platform. Untuk memenuhi kewajiban hukum ini, platform perlu mengumpulkan dan memberikan informasi terkait pajak kepada otoritas pajak sesuai dengan persyaratan yang berlaku, termasuk informasi identitas pengguna, jumlah keuntungan, dan lainnya. Jika pengguna gagal memberikan informasi atau memberikan informasi yang salah, dapat menyebabkan kesalahan dalam pelaporan pajak. Jika platform tidak dapat memprosesnya, pengguna harus melakukan pelaporan pajak secara mandiri, dan konsekuensi negatif lainnya akan ditanggung oleh pengguna sendiri.</p>
              <p>11. Dalam batas-batas hukum dan peraturan yang berlaku, platform berhak untuk mengubah atau menyesuaikan aturan ini. Perubahan atau penyesuaian yang terkait akan dipublikasikan di halaman aturan dan akan berlaku segera setelah dipublikasikan. Jika pengguna terus berpartisipasi dalam kegiatan, maka dianggap bahwa pengguna menyetujui dan menerima aturan yang telah diubah atau disesuaikan. Jika pengguna menolak perubahan atau penyesuaian aturan, maka pengguna dapat memilih untuk tidak berpartisipasi dalam kegiatan yang telah diubah.</p>
            </x-wrapper>
          </x-explain>
          <x-footer class="relative flex flex-col bg-white">
            <x-mask class="-top-[38px] absolute bg-gradient-to-b from-[rgba(255,_255,_255,_0)] to-[rgba(255,_255,_255,_1)] w-full h-10" />
            <x-got-it>
              <Button class="bg-[var(--brand-7)] mx-4 mt-4 mb-6 no-tap-color border-none rounded-lg w-[calc(100%-2rem)] h-11 text-[var(--text-5)] text-base outline-none" onClick={() => closeDialog()}>{t('coins.earnings.gotIt')}</Button>
            </x-got-it>
          </x-footer>
        </x-explain-dialog>
      ),
    })
    setTimeout(() => {
      useBetterScroll(document.getElementById('explain-wrapper') as HTMLElement)
    }, 200)
  }

  onMounted(() => {
    document.title = t('coins.exchange.title')
    track('exchangezone', 'page', 'show', {
      enter_from: from,
    })
    window.addEventListener('scroll', handleScroll)
    void getBroadcast()
    void getExchangeDetails()
    void getLastRedeemAccount()
  })

  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
    if (interval) {
      window.clearInterval(interval)
    }
  })

  return () => (
    pageLoading.value
      ? (
          <Loading />
        )
      : (
          <x-my-exchange-page class="relative flex flex-col gap-y-4 bg-[var(--surface-4)] text-[var(--text-5)] size-full" ref={wrapper}>
            <img src={background} class="top-0 left-0 z-0 absolute w-full h-auto" />
            <Back title={t('coins.exchange.title')} isWhite={true} isCloseWebview={isFromMyEarnings ? false : true} class={mc('bg-transparent shrink-0', isScroll.value && window.scrollY > 44 ? 'bg-[var(--brand-6)]' : '')}>
              { exchangePageParams.value?.show_help_dialog && <p class="absolute right-3 text-[var(--text-5)] text-sm z-up-up" onClick={openHelpDialog}>Aturan</p> }
            </Back>
            <x-rules class="relative z-up flex items-center gap-x-[2px] px-5 pt-4 font-bold text-base">
              <img src={coins} class="mb-[1px] w-4 h-4" />
              <span>{exchangePageParams.value?.coin_info?.split('≈')[0]}</span>
              <span>≈</span>
              <span>{exchangePageParams.value?.coin_info?.split('≈')[1]}</span>
            </x-rules>
            <x-broadcast class="relative z-up bg-[var(--fill-4)] mx-3 py-[6px] pl-11 rounded-lg h-7 font-normal text-[var(--text-1)] text-xs">
              <img src={horn} class="-top-1 left-2 absolute size-7" />
              <x-wrapper class="block relative h-full overflow-hidden">
                <x-broadcast-list
                  class="block relative size-full" style={{
                    transform: `translateY(${targetY.value}%)`,
                  }}
                >
                  {
                    broadcasts.value?.map(broadcast => (
                      <x-broadcast-item innerHTML={broadcast ?? ''} class="block m-0 p-0 truncate transform" />
                    ),
                    )
                  }
                </x-broadcast-list>
              </x-wrapper>
            </x-broadcast>
            <x-exchange-area class="relative z-up flex flex-col bg-[var(--fill-5)] mx-3 rounded-lg w-[calc(100%-1.5rem)]">
              <x-title class="block p-3 font-bold text-[var(--text-1)] text-lg">{t('coins.earnings.exchange')}</x-title>
              <x-cards class="flex justify-start items-center gap-x-3 px-3 pt-2 pb-3">
                {
                  exchangePageParams.value?.exchange_list?.map(item => {
                    return (
                      <x-card onClick={() => handleClickCard(item)} class={mc('box-border h-[68px] flex justify-center items-center w-[calc(33.333%-0.5rem)] shrink-0 grow-0 rounded-lg px-[10px] py-[14px] border-solid border-[1px] border-[var(--line-1)]', activeCard.value === item.name ? 'border-[2px] border-[var(--line-4)]' : '')}>
                        <img class={mc('object-cover w-full h-auto bg-white rounded-lg', item.can_choose ? '' : 'opacity-20')} src={item.image_url} />
                      </x-card>
                    )
                  })
                }
              </x-cards>
              <x-gift-list class="flex flex-col pb-3">
                {
                  exchangePageParams.value?.exchange_list
                    ?.filter(item => item.name === activeCard.value)[0]
                    ?.gift_list?.map(
                      item => item.type === 'amazon'
                        ? <AmazonGift gift={item} />
                        : item.type === 'paypal'
                          ? <PaypalGift gift={item} />
                          : item.type === 'spotify'
                            ? <SpotifyGift gift={item} />
                            : item.type === 'mercado_pago'
                              ? <MercadoPagoGift gift={item} />
                              : item.type === 'ovo'
                                ? <OvoGift gift={item} />
                                : null)
                }
              </x-gift-list>
            </x-exchange-area>
          </x-my-exchange-page>
        )
  )
})

export default ExchangePage
