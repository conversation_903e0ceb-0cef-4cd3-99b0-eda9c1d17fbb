import { createComponent } from '@skynet/shared'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, openDialog, transformInteger, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { apiSaveStrategyGroupAds } from './strategy-group-ad-api'
import { useStrategyGroupAdStore } from './strategy-group-ad-store'
import { useListParamsStore } from './use-list-params-store'

export const StrategyGroupIapCreatePlanPage = createComponent(null, props => {
  const { formData, Form, resetTaskFormData, stepThreeError, stepOneError, validateAllStepsForIap, stepErrorForSolution } = useStrategyGroupAdStore()

  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')
  const router = useRouter()
  const onSubmit = async () => {
    if (!validateAllStepsForIap()) {
      openDialog({
        title: '出错提示',
        body: (
          <div>请检查表单<br />
            {JSON.stringify(stepOneError.value)}
            {JSON.stringify(stepErrorForSolution.value)}
            {JSON.stringify(stepThreeError.value)}
          </div>
        ),
      })
      return
    }
    if (formData.value.user_config?.active_users_config?.start_days && formData.value.user_config?.active_users_config?.end_days) {
      if (formData.value.user_config?.active_users_config?.start_days > formData.value.user_config?.active_users_config?.end_days) {
        // 交换起止时间
        const temp = formData.value.user_config.active_users_config.start_days
        formData.value.user_config.active_users_config.start_days = formData.value.user_config.active_users_config.end_days
        formData.value.user_config.active_users_config.end_days = temp
      }
    }
    const response = await apiSaveStrategyGroupAds({
      ...formData.value,
      save_type: 1,
      pay_mode: route.path.includes('iaa') ? 'IAA' : 'IAP',
    })
    if (!response.data) return
    formData.value = {
      ...response.data,
      scene_type: Object.keys(response.data.ad_config || {}) || [],
    }
    setTimeout(() => {
      void router.push({ path: route.path, query: { ...route.query, has_edit: 'false' } }).then(() => {
        const closeDialog = openDialog({
          title: '提示',
          body: (
            <div>保存成功
              <DialogFooter okText="返回列表页" onOk={() => {
                closeDialog()
                const { listParams, initListParams } = useListParamsStore()
                listParams.value = { ...initListParams }
                void router.push('/free-ad-strategy-group/iap')
              }} cancelText="留在当前页" onCancel={() => closeDialog()}
              />
            </div>
          ),
        })
      })
    })
  }

  return () => (
    <div class="flex flex-col gap-4 p-8">
      <Form
        class="space-y-4 block"
        data={formData.value}
        error={stepThreeError.value}
        onChange={(path, value) => {
          if (['task_config.auto_up'].includes(path) && value === 'Invalid Date') {
            set(formData.value || {}, path, undefined)
            return
          }
          set(formData.value, path, value)
        }}
        onReset={() => resetTaskFormData(true)}
        onSubmit={onSubmit}
        items={[
          [
            requiredLabel('权重'),
            'task_config.priority',
            {
              type: 'number',
              placeholder: '请输入 1 ~ 99，越大越优先',
              disabled: isViewMode.value,
            },
            { transform: transformInteger, class: 'w-300px' },
          ],
          [
            requiredLabel('方案持续时间'),
            'task_config.duration',
            {
              type: 'number',
              placeholder: '填 -1 表示不限',
              suffix: '天',
              min: -1,
              disabled: isViewMode.value,
            },
            { transform: transformNumber, class: 'w-300px' },
          ],
        ]}
        actions={() => (
          <>
            <hr class="mb-4" />
            <div class="flex justify-between gap-x-2">
              <Button class="btn btn-sm" onClick={() => {
                void router.push({ path: `/free-ad-strategy-group/iap/create/solution?id=${formData.value.id}`, query: { ...route.query } })
              }} type="button"
              >上一步
              </Button>
              {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button>}
              {isViewMode.value ? null : <Button class="btn btn-primary btn-sm" type="submit">保存</Button>}
            </div>
          </>
        )}
      />

    </div>
  )
})
export default StrategyGroupIapCreatePlanPage
