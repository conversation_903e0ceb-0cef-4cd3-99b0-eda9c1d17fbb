declare namespace M {
    type Feedback = {
      description: string
      image_list: string[]
      email: string
      problem_type?: number
    }
    type ProblemType = {
      id: number
      problem_type: string
    }

    namespace MyFeedback {
      namespace List {
        interface Feedback {
          work_order_id: number // 工单id
          description: string
          feedback_status: number // 反馈状态 0:待回复(未回复) 1:处理中（已回复） 2:已关闭
          feedback_status_text: string // replied、unresponded、closed
          has_red_dot: boolean // 是否展示红点
          un_read_msg_num: number // 未读消息数
          op_time: number
        }
      }

      namespace Details {
        interface Chat {
          answer?: Chat[]
          avatar: string// 头像url
          name: string// 昵称
          description: string
          image_list: string[]
          created: number
        }

        interface WorkOrderInfo {
          work_order_id: number // 工单id
          problem_type: number // 问题类型
          email: string // 邮箱
          feedback_status: number // 反馈状态 0:待回复 1:处理中 2:已关闭
          can_reply: boolean // 是否可以回复（已关闭工单不可以回复）
          comment: {
            satisfied_status: number // 几星评价
          }
        }

        interface Details {
          work_order_info: WorkOrderInfo
          chat_list: Chat[]
        }
      }
    }
}
