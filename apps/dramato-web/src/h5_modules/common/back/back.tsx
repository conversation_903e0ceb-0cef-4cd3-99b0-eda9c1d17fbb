import { ClassName, createComponent, fn, mc, tryCall } from '@skynet/shared'
import backLight from './images/back-light.svg'
import back from './images/back.svg'
import { onMounted, ref, VNodeChild } from 'vue'
import { isIos } from 'src/lib/ua'
import { useRouter } from 'vue-router'
import { jsBridge } from 'src/lib/jsbridge'
type BackOptions = {
  props: {
    hasBack?: boolean
    // isCloseWebview?: boolean
    title?: string | (() => VNodeChild)
    isWhite?: boolean
    class?: ClassName
    backTo?: string | (() => void)
    titleLeft?: boolean
    // onBack?: () => void
  }
  slots: {
    default?: () => unknown
  }
}
export const Back = createComponent<BackOptions>({
  props: {
    hasBack: true,
    title: '',
    isWhite: false,
    class: '',
    backTo: '',
    titleLeft: false,

  },
}, (props, { slots }) => {
  const isSupportedAllScreen = ref<boolean>(false)
  onMounted(() => {
    if (!jsBridge.inDramawaveApp) {
      isSupportedAllScreen.value = false
      return
    }

    try {
      void jsBridge<{ app_version_code: number, app_version: string }>('getDeviceInfo').then(({ app_version_code, app_version }) => {
        console.log('>> current app_version_code', app_version_code)

        if (!isIos()) {
          isSupportedAllScreen.value = app_version_code >= 1370000
        } else {
          isSupportedAllScreen.value = +(app_version.replaceAll('.', '')) >= 1370
        }
      })
    } catch (error) {
      isSupportedAllScreen.value = false
    }
  })
  const router = useRouter()
  return () => (
    <x-back class={mc(
      'bg-[var(--surface-1)] z-100 block',
      jsBridge.inDramawaveApp && isSupportedAllScreen.value ? 'nav-top-11' : jsBridge.inDramawaveApp || isIos() ? 'nav-top-8' : '',
      props.class,
    )}
    >
      <div class={mc(
        'px-3 flex items-center static top-0 h-11 w-full',
        props.title ? 'justify-center' : 'justify-start',
      )}>
        {
          props.hasBack && (
            <img src={props.isWhite ? back : backLight} class="relative left-0 top-0 z-up-up size-6 shrink-0 object-cover" onClick={() => {
              if (props.backTo) {
                if (typeof props.backTo === 'string') {
                  void router.push(props.backTo)
                } else {
                  !!props.backTo && props.backTo()
                }
              } else {
                router.back()
              }
            }}
            />
          )
        }
        {
          typeof props.title === 'string' && (
            <x-title class={mc(
              'text-4 z-up flex flex-1  items-center truncate font-medium',
              props.titleLeft ? 'justify-start' : 'justify-center',
            )}>
              {props.title}
            </x-title>
          )
        }
        {

          typeof props.title === 'function' && props.title()
        }
        {slots.default?.()}
      </div>
    </x-back>
  )
})
