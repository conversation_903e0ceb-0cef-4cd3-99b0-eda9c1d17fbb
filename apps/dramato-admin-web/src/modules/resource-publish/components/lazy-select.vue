<template>
  <div class="relative">
    <!-- 未激活状态显示当前值 -->
    <div v-if="!isActive" @click="handleActivate"
      class="input input-bordered input-sm w-full cursor-pointer truncate px-2">
      <template v-if="modelValue">
        <template v-if="Array.isArray(modelValue)">
          {{ getSelectedLabels }}
        </template>
        <template v-else>
          {{ getSelectedLabel }}
        </template>
      </template>
      <span v-else class="text-gray-400">{{ $attrs.placeholder || '请选择' }}</span>
    </div>

    <!-- 激活状态显示 select -->
    <el-select v-else v-model="modelValueProxy" v-bind="$attrs" @blur="handleBlur" ref="selectRef">
      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'

const props = defineProps({
  modelValue: [String, Number, Object, Array],
  loadOptions: {
    type: Function,
    required: true
  },
  updateIndex: {
    type: Number,
    required: false,
    default: 1
  },
})

const emit = defineEmits(['update:modelValue'])
const isActive = ref(false)
const options = ref([])
const selectRef = ref<any>()

const modelValueProxy = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val)
})

watch(() => props.updateIndex, async () => options.value = await props.loadOptions())

// 获取选中的标签文本
const getSelectedLabel = computed(() => {
  if (!props.modelValue) return ''
  const option = options.value.find(opt => opt.value === props.modelValue)
  return option?.label || '-'
})

// 获取多选的标签文本
const getSelectedLabels = computed(() => {
  if (!Array.isArray(props.modelValue)) return ''
  return props.modelValue
    .map(val => options.value.find(opt => opt.value === val)?.label || '')
    .filter(Boolean)
    .join(', ') || '-'
})

const handleActivate = async () => {
  if (!options.value.length) {
    options.value = await props.loadOptions()
  }
  isActive.value = true

  await nextTick()

  // 确保 select 已经渲染
  setTimeout(() => {
    if (selectRef.value) {
      // 直接设置 select 的内部状态
      selectRef.value.expanded = true  // 展开下拉框
      selectRef.value.focus()          // 聚焦输入框
    }
  }, 0)
}

const handleBlur = () => {
  setTimeout(() => {
    if (!selectRef.value?.expanded) {  // 只有在下拉框关闭时才隐藏
      isActive.value = false
    }
  }, 200)
}

// 初始加载选项数据
onMounted(async () => {
  if (props.modelValue) {
    options.value = await props.loadOptions()
  }
})

</script>

<style>
/* 确保下拉框样式正确 */
.lazy-select-popper {
  z-index: 9999 !important;
}

.el-select {
  width: 100%;
}
</style>
