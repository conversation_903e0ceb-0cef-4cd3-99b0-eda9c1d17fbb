import { ref } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { WelfareSleepDetails } from './sleep-check-in.d'
import { apiGetWelfareSleepDetails } from './sleep-check-in-api'
import { when } from '@skynet/shared'
import { authInfo } from 'src/lib/get-auth'

export const useSleepCheckInStore = () => {
  const { t } = useLocale()
  const sleepDetails = ref<WelfareSleepDetails>()
  const getSleepDetails = () => {
    void when(() => authInfo).then(async () => {
      const res = await apiGetWelfareSleepDetails()
      if (!res.data) return
      sleepDetails.value = res.data
    })
    // const res = {
    //   data: {
    //     welfare_id: 4,
    //     welfare_key: '',
    //     task_total_gold: 2400,
    //     status: 1, // 0: 等待开启 1:可领取 2: 待领取 3:领取完成 4:可补领 5:可开启
    //     time_period: 3, // 1:上午 2:下午 3: 晚上
    //     background_url: 'background_url', // 背景
    //     button_show: {
    //       btn_txt: 'Congrates on Treasure Box Reward',
    //     },
    //     top_show: {
    //       title: 'Congrates on Treasure Box Reward',
    //     },
    //     greeting_show: {
    //       title: 'Good morning',
    //       sub_title: 'It\'s 14:34 on December 30, 2024.',
    //       bottom_tips: 'Rest well, live well',
    //     },
    //     bottom_show: {
    //       title: 'Congrates on Treasure Box Reward',
    //       // "sub_title": "8 boxes available to open today"
    //     },
    //     jump_widget: { // 跳转挂件
    //       widget_icon: '',
    //       widget_link: '',
    //       widget_text: '',
    //     },
    //     interactive_broadcast_widget: { // 交互播报挂件
    //       widget_icon: '',
    //       widget_link: '',
    //       widget_text: 'Sleep_r_activateivate_after 19:00 every day',
    //     },
    //     activity_rules_widget: { // 活动规则挂件
    //       widget_icon: '',
    //       widget_link: '',
    //       widget_text: '',
    //     },
    //   },
    // }
    // sleepDetails.value = res.data
  }

  return {
    sleepDetails,
    getSleepDetails,
  }
}
