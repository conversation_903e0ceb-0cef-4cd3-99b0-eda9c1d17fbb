declare namespace Api {
  namespace VoiceoverRole {
    interface ListReqParams {
      series_resource_id?: number // 剧名
      page_index?: number // 当前页索引
      page_size?: number // 每页大小
    }

    interface VoiceoverRoleListResp {
      character_list: VoiceoverRoleItem[] // 剧列表
      total: number // 总数
    }

    interface VoiceoverTimbreItem {
      lang: string
      timbre_id: number
      timbre_name: string
      sample_path: string
    }

    interface UpdateVoiceoverTimbreItem {
      lang: string
      timbre_name?: string
      timbre_id: number
    }

    interface VoiceoverRoleItem {
      character_id: number
      name: string
      official_name: string
      timbres: VoiceoverTimbreItem[]
      min_serial_number: number

      age: string
      gender: 0 | 1 | 2 | number
      voice_labels: string[] | string
      role_labels: string[] | string
    }

    interface UpdateReqParams {
      character_id: number
      timbres: UpdateVoiceoverTimbreItem[]
    }

    interface CharacterFeature {
      character_id: number
      gender: 0 | 1 | 2 | number
      age: string
      voice_labels: string
      role_labels: string
    }
  }
}
