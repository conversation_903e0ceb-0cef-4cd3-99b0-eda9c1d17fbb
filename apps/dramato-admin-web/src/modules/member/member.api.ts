import { httpClient } from 'src/lib/http-client'

type PageInfo = {
  next: string
  size: number

  has_more: boolean
  total: number
}

type MemberListParams = {
  page_size: number // 每页数据
  next: string // 下一页数据偏移量 使用返回的next
  app_id?: string // 查询的APP ID
  title?: string // 档位名称
  store?: string // 上架的商店
  status: number // 上架状态 //状态：0.所有 1-启用，2-禁用
}

export const apiGetMemberList = (data: MemberListParams) =>
  httpClient.post<ApiResponse<{
    items: M.MemberLevel[]
    page_info: PageInfo
    total: number
  }>>('/wallet/product/membership/list', data)

export const apiCreateMember = (data: M.MemberLevel) =>
  httpClient.post<ApiResponse<boolean>>('/wallet/product/create', data)

export const apiUpdateMember = (data: M.MemberLevel) =>
  httpClient.post<ApiResponse<boolean>>('/wallet/product/edit', data)

export const apiUpdateMemberPriority = (data: {
  id: number
  priority: number
}) =>
  httpClient.post<ApiResponse<boolean>>('/wallet/product/priority', data)
