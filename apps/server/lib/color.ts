/**
 * 将 ARGB 颜色值转换为 rgba() 格式
 * @param {string} argb - ARGB 颜色值，例如 "0xFFB25D5B"
 * @returns {string} - 返回 CSS 格式的 rgba() 字符串
 */
export function argbToRgba(argb: string, customAlpha?: number) {
  // 去掉 "0x" 前缀，确保只保留十六进制部分
  const hex = argb.replace('0x', '')

  // 检查输入是否是有效的 8 位十六进制字符串
  if (hex.length !== 8) {
    console.log('Invalid ARGB color format. It should be a 8-digit hexadecimal string.')
    return [0, 0, 0, 0]
  }

  // 提取 Alpha、Red、Green、Blue 的十六进制值
  const alphaHex = hex.slice(0, 2) // 前两位是 Alpha 通道
  const redHex = hex.slice(2, 4) // 接下来的两位是红色
  const greenHex = hex.slice(4, 6) // 再接下来的两位是绿色
  const blueHex = hex.slice(6, 8) // 最后两位是蓝色

  // 将十六进制转换为十进制
  const alpha = parseInt(alphaHex, 16) / 255 // 将 Alpha 转换为 0-1 的小数
  const red = parseInt(redHex, 16) // 红色分量
  const green = parseInt(greenHex, 16) // 绿色分量
  const blue = parseInt(blueHex, 16) // 蓝色分量

  // 返回 rgba() 格式的字符串
  return [red, green, blue, customAlpha ?? alpha]
  // return `rgba(${red}, ${green}, ${blue}, ${customAlpha || alpha.toFixed(2)})`
}
