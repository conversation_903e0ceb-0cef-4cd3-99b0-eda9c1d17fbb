import { createComponent, getQueries } from '@skynet/shared'
import { track } from 'src/lib/track'
import { useLocale } from 'src/lib/use-locale'
import { onMounted } from 'vue'

export const EpisodePage = createComponent(null, () => {
  const { language, share_user_id, channel } = getQueries({
    language: 'en',
    share_user_id: 0,
    channel: '',
  })
  // 多语言翻译
  const { t } = useLocale(language)
  onMounted(() => {
    // 埋点上报
    track('share', 'page', 'show', {
      share_user_id,
      channel,
    })
    document.title = t('share.episode.title')
  })

  // 参考dramato-web/src/modules/share/series/series-page.tsx
  // 跳转app方案：
  // ios: universalLink(跟当前域名不同域)
  // android: deeplink + 剪切板

  return () => (
    <div>EpisodePage </div>
  )
})

export default EpisodePage
