import { createComponent, fn, SlotFn } from '@skynet/shared'
import Loading from 'src/modules/common/loading/loading'
import { onMounted, ref } from 'vue'
import { T } from './infinity-scroll.i18n'
import { loading } from './images/images'

type InfinityScrollOptions = {
  props: {
    hasMore: boolean
    next: string // 格式：offset=10&page_size=10
    loading: boolean
    loadingText?: string
    endText?: string
  }
  emits: {
    load: (next: string) => void
  }
  slots: {
    default: () => SlotFn
  }
}

export const InfinityScroll = createComponent<InfinityScrollOptions>({
  props: {
    hasMore: false,
    next: '',
    loading: false,
    loadingText: '',
    endText: '',
  },
  emits: {
    load: fn,
  },
}, (props, { emit, slots }) => {
  const containerRef = ref<HTMLElement>()
  const endRef = ref<HTMLElement>()

  onMounted(() => {
    // 创建观察器
    const observer = new IntersectionObserver(entries => {
      const entry = entries[0]
      if (entry.isIntersecting && props.hasMore && !props.loading) {
        emit('load', props.next)
      }
    }, {
      rootMargin: '1000px', // 提前1000px触发
      threshold: 0,
    })

    // 观察底部元素
    if (endRef.value) {
      observer.observe(endRef.value)
    }

    // 首次加载检查是否需要填充满屏幕
    if (props.hasMore && containerRef.value) {
      const checkContent = () => {
        const containerHeight = containerRef.value?.getBoundingClientRect().height || 0
        const viewportHeight = window.innerHeight

        if (containerHeight < viewportHeight && props.hasMore && !props.loading) {
          console.log('填充满屏幕', props.next)

          emit('load', props.next)
        }
      }

      checkContent()
      // 给内容渲染一点时间后再次检查
      setTimeout(checkContent, 100)
    }

    return () => {
      observer.disconnect()
    }
  })

  return () => (
    <div ref={containerRef} class="content-visibility-auto">
      {slots.default?.()}
      {
        props.hasMore && props.loading && (
          <div class="flex items-center justify-center py-5">
            <x-loading class="flex items-center justify-center gap-x-1">
              <img src={loading} class="size-5 animate-spin" />
              <x-loading-text class="text-xs text-[#434546]">{props.loadingText || T.infinityScroll.loading()}</x-loading-text>
            </x-loading>
          </div>
        )
      }
      {
        !props.loading && (
          <div ref={endRef} class="py-5 text-center text-xs text-[#434546]">
            {!props.hasMore && `- ${props.endText || T.infinityScroll.end()} -`}
          </div>
        )
      }
    </div>
  )
})

export default InfinityScroll
