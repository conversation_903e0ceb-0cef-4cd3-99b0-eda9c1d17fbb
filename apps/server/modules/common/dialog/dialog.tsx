import { createComponent, fn, mc } from '@skynet/shared'
import { type Fn, TransitionPresets, useTransition } from '@vueuse/core'
import closeDialog  from './images/close.webp'
import { onMounted, onUnmounted, ref, type VNodeChild, watchEffect } from 'vue'

export type DialogProps = {
  title?: string | (() => VNodeChild)
  customClose?: unknown
  body: unknown
  visible?: boolean
  closeVisible?: boolean
  customClass?: string
  beforeClose?: () => void | boolean
  canEscClose?: boolean
  mainClass?: string
  closeClass?: string
  preventScroll?: boolean
}

export type DialogOptions = {
  props: DialogProps
  emits: {
    close: Fn
  }
}

export const Dialog = createComponent<DialogOptions>({
  props: {
    title: '',
    customClose: '',
    body: '',
    visible: false,
    closeVisible: false,
    customClass: '',
    beforeClose: fn,
    canEscClose: true,
    mainClass: '',
    closeClass: '',
    preventScroll: true,
  },
  emits: {
    close: fn,
  },
}, (props, { emit }) => {
  const wrapper = ref<HTMLElement>()
  onMounted(() => {
    props.preventScroll && document.body.classList.add('overflow-hidden')
    // props.canEscClose && window.addEventListener('keydown', keyDownFn)
    const el = document.activeElement
    if (!wrapper.value) return
    if (!wrapper.value.contains(el)) {
      const focusable = wrapper.value.querySelector('input , textarea , button')
      if (!focusable) {
        wrapper.value.focus()
      } else {
        // @ts-expect-error 不认识？
        focusable.focus()
      }
    }
  })
  // const close = () => {
  //   if (props.beforeClose?.() === false) return
  //   emit('close')
  // }
  // esc 关闭弹窗
  // const keyDownFn = (e: KeyboardEvent) => {
  //   if (e.key === 'Escape') {
  //     close()
  //   }
  // }
  const y = ref(100)
  const targetY = useTransition(y, {
    duration: 300,
    transition: TransitionPresets.easeOutCubic,
    onFinished: () => {
      if (props.visible === false) {
        emit('close')
        setTimeout(() => {
          // 兼容oppo不消失出现残影，但是点击屏幕可以消失
          // 所以模拟触发一次点击事件
          document.body?.dispatchEvent(new Event('click'))
          document.body?.click()
        }, 1000)
      }
    },
  })
  watchEffect(() => {
    if (props.visible) {
      y.value = 0
    } else {
      y.value = -100
    }
  })

  onUnmounted(() => {
    props.preventScroll && document.body.classList.remove('overflow-hidden')
    // window.removeEventListener('keydown', keyDownFn)
  })
  return () =>
    props.visible
      ? (
          <div
            class="z-130 relative"
            tabindex="0" ref="wrapper"
            style={{
              opacity: (100 - Math.abs(targetY.value) / 3) / 100,
            }}
          >
            <div class="fixed inset-0 z-up size-full bg-[#0B080B99]" />
            <div class={
              mc('fixed flex items-center w-[calc(100%-80px)] z-up-up max-w-[var(--phone-page-max-width)] pad:max-w-[var(--phone-page-max-width)] pc:max-w-[var(--phone-page-max-width)] h-auto left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 pc:left-1/2 pc:top-1/2 pc:-translate-x-1/2 pc:-translate-y-1/2 pad:left-1/2 pad:top-1/2 pad:-translate-x-1/2 pad:-translate-y-1/2 transform flex-col gap-4 pc:rounded-xl pad:rounded-xl pt-4 pad:h-auto pc:h-auto overflow-auto', props.customClass)
            }
            >
              {props.title
              && (
                <header class="shrink-0 grow-0 px-5">
                  {props.title instanceof Function
                    ? props.title()
                    : <div class="text-lg font-medium leading-relaxed text-slate-950">{props.title}</div>}
                </header>
              )}
              <main class={mc('flex-1 w-full overflow-hidden overflow-y-auto h-auto max-h-full', props.mainClass)}>
                {props.body instanceof Function ? props.body() : <>{props.body}</>}
              </main>
              {
                props.closeVisible && (
                  <footer>
                    <img src={closeDialog} class="relative flex size-8 items-center justify-center" onClick={() => emit('close')} />
                  </footer>
                )
              }
            </div>
          </div>
        )
      : null
})
