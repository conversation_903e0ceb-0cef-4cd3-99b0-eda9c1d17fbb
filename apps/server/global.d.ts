type JsonValue = string | number | boolean | null | undefined | JsonArray | JsonObject
type JsonObject = Record<string, JsonValue>
type JsonArray = JsonValue[]

/** 所有 api 接口的响应数据都应该准守该格式 */
type ApiResponse<T = unknown> = {
  code: number
  data?: T
  msg: string
  message?: string
}

type PageInfo = {
  next: string
  has_more: boolean
}

type Ad = {
  kind: 'ad'
  id: string
  dom: HTMLElement
}

declare interface Window {
  startReportTime: number
}
