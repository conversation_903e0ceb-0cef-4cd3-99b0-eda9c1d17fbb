/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, getQuery } from '@skynet/shared'
import { Button, Icon, showFailToast } from '@skynet/ui'
import { ref, onBeforeUnmount } from 'vue'
import { getUrlParams } from 'src/lib/url-param'
import { apiInternalLoginStatus } from './login-api'
import { get_k_sso_openid } from 'src/lib/device-id'
import { useRouter } from 'vue-router'
import { useLoginStore } from './use-login-store'

export const SsoRedirectPage = createComponent(null, () => {
  const returnQuery = getQuery('returnTo', '')
  const router = useRouter()
  const { setUserInfo } = useLoginStore()
  const limit = 400
  const timeout = Date.now() + limit * 1000
  const countdownInterval = ref<NodeJS.Timeout | null>(null)
  const checkLoginStatusInterval = ref<NodeJS.Timeout | null>(null)
  const countdown = ref(limit)
  // TODO： 暂时登录成功的标识 可能不是token
  const openid = get_k_sso_openid()

  const redirectToLoginPage = () => {
    window.location.href = '/login'
  }

  if (!openid) {
    setTimeout(() => {
      redirectToLoginPage()
    }, 3000)
  }

  const startCountdown = () => {
    countdownInterval.value = setInterval(() => {
      const current = Date.now()
      countdown.value = Math.max(0, Math.ceil((timeout - current) / 1000))
      if (current >= timeout) {
        redirectToLoginPage()
      }
    }, 1000)
  }
  startCountdown()

  // 新增的函数：轮询 apiInternalLoginStatus
  const pollLoginStatus = () => {
    // 替换为实际的重定向页面
    const {redirect_to: redirectTo} = getUrlParams(window.location.href)
    const checkLoginStatus = async () => {
      try {
        const res = await apiInternalLoginStatus({ open_id: localStorage.getItem('k_sso_openid') || '' })
        // TODO: 判断登录成功的方式
        if (res.code === 200) {
          console.log('checkLoginStatus', res)
          if (res?.data?.status === 'success') {
            setUserInfo(res?.data?.user || {})
            localStorage.setItem('userInfo', JSON.stringify(res.data.user))
            localStorage.setItem('k_sso_token', res.data?.token || '')
            if (returnQuery) {
              if (returnQuery.startsWith('http')) {
                location.href = returnQuery
              } else {
                void router.push(returnQuery)
              }
            } else {
              void router.push('/')
            }
          }else if(res?.data?.status === 'cancel') {
            window.location.href = '/login'
          }
        } else {
          const errData = res.data as any
          showFailToast(errData?.message || errData?.msg || errData?.err_msg || '登录失败')
        }
      } catch (err: any) {
        showFailToast(err.response.data.err_msg || err.response.data.message || '登录失败')
      }
    }
    checkLoginStatusInterval.value = setInterval(checkLoginStatus, 5000) // 每5秒检查一次
  }
  pollLoginStatus()

  onBeforeUnmount(() => {
    if (countdownInterval.value) {
      clearInterval(countdownInterval.value)
    }
    if (checkLoginStatusInterval.value) {
      clearInterval(checkLoginStatusInterval.value)
    }
  })

  // apiInternalLoginStatus 轮训这个接口，并且当返回成功时候 跳回 redirect_to 的页面
  return () => (
    <x-sso-redirect class="w-[100vw] h-[100vh] block relative">
      <iframe width="100%" height="100%" src="https://applink.feishu.cn/client/bot/open?appId=cli_a768dc533c75d01c" />
      <div class="absolute bottom-0 left-0 w-[100vw] text-center p-4 text-base">
        <div class="flex items-center justify-center space-x-1">
          <Icon name="line-md:loading-twotone-loop" class="size-4" />
          <span class="text-green-500">wait for verify</span>
          <Button class="btn btn-link btn-xs" onClick={() => window.location.href = '/login'}>Back to login</Button>
        </div>
        <div>
          This login will expire after {countdown.value}s
        </div>
      </div>
    </x-sso-redirect>
  )
})
export default SsoRedirectPage
