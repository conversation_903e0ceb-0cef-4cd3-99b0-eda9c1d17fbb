import { report } from '@skynet/client-track'
import { when } from '@skynet/shared'
import Cookies from 'js-cookie'
import { jsBridge } from './jsbridge'

const getDataFromHeaders = (key: string) => {
  const headers = JSON.parse(Cookies.get('headers') ?? '{}')
  return headers?.[key] ?? undefined
}

const getDataFromParams = (key: string) => {
  const params = JSON.parse(Cookies.get('params') ?? '{}')
  return params?.[key] ?? undefined
}

export const track = (origin: string, entity: string, event: string, extra?: object) => {
  void when(() => getDataFromHeaders('device-id') && getDataFromHeaders('app-version') && getDataFromParams('user_id')).then(() => {
    report({
      event: `${origin}_${entity}_${event}`,
      device_hash: getDataFromHeaders('device-id'),
      app_version: getDataFromHeaders('app-version'),
      user_id: getDataFromParams('user_id') + '',
      event_info: JSON.stringify({
        origin,
        entity,
        event,
        ...extra,
      }),
    })
  }).catch(() => {
    // do nothing
    report({
      event: `${origin}_${entity}_${event}`,
      user_source: 'web',
      device_hash: 'unknown',
      app_version: 'unknown',
      user_id: 'unknown',
      event_info: JSON.stringify({
        origin,
        entity,
        event,
        ...extra,
      }),
    })
  })
}

export const trackNative = (origin: string, entity: string, event: string, extra?: object) => {
  void jsBridge('track', {
    eventName: `${origin}_${entity}_${event}`,
    uploadNow: true,
    params: {
      ...extra,
    },
  })
}
