import { createComponent } from '@skynet/shared'
import { ref, watch } from 'vue'
import { useHomeConfig } from './use-home-config'
import { apiGetHomepagePreview } from './home-config-api'
import logo from './image/logo.svg'
import { BillboardLayout } from './layout/billboard-layout'
import { BannerLayout } from './layout/banner-layout'
import { HorizontalLayout } from './layout/horizontal-layout'
import { VerticalLayout } from './layout/vertical-layout'
import { RecommendLayout } from './layout/recommend-layout'

export const HomePreview = createComponent(null, props => {
  const loading = ref(false)
  const list = ref<M.HomepageUnit[]>([])
  const { refresh, searchForm } = useHomeConfig()

  watch(() => refresh.value, newVal => {
    if (newVal && searchForm.value.app_id && searchForm.value.language) {
      const params = {
        app_id: searchForm.value.app_id,
        language: searchForm.value.language,
      }
      loading.value = true
      void apiGetHomepagePreview(params).then(res => {
        if (!res.data) return
        list.value = res.data.list
      }).finally(() => {
        loading.value = false
      })
    }
  })

  return () => (
    <div class="mockup-phone scale-80">
      <div class="camera" />
      <div class="display">
        <div class="artboard justify-start artboard-demo phone-5 p-[16px] bg-[#414141] space-y-4 select-none">
          <x-scroll class="min-h-full w-full overflow-x-hidden overflow-y-auto hide-scrollbar">
            <x-logo class="w-full flex justify-start px-3 py-1.5 items-center">
              <img src={logo} class="h-7 w-auto" width={139} height={28} />
            </x-logo>
            {
              loading.value
                ? <x-phone-loading class="text-white font-bold flex size-full justify-center items-center">加载中...</x-phone-loading>
                : list.value.map(item => {
                  switch (item.type) {
                    case 'banner':
                      return item.items && item.items.length > 2 ? <BannerLayout list={item.items} title={item.module_name} /> : null
                    case 'billboard':
                      return item.items ? <BillboardLayout list={item.items} show_title={item.show_title === 1} title={item.module_name} /> : null
                    case 'column_horizontal':
                      return item.items ? <HorizontalLayout list={item.items} show_title={item.show_title === 1} title={item.module_name} /> : null
                    case 'column_vertical':
                      return item.items ? <VerticalLayout list={item.items} show_title={item.show_title === 1} title={item.module_name} /> : null
                    case 'recommend':
                      return item.items ? <RecommendLayout list={item.items} show_title={item.show_title === 1} title={item.module_name} /> : null
                  }
                })
            }
          </x-scroll>
        </div>
      </div>
    </div>
  )
})
