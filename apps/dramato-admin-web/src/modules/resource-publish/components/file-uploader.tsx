/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn } from '@skynet/shared'
import { ElUpload } from 'element-plus'
import type { UploadProps, UploadProgressEvent, UploadRequestOptions, UploadUserFile, UploadStatus } from 'element-plus'
import { onMounted, onUnmounted, ref } from 'vue'
import { useUploader } from 'src/modules/common/uploader/use-uploader'
import { useExpose } from '@skynet/ui/use/use-expose'

type FileUploaderOptions = {
  props: {}
  emits: {
    success: (data: { file: M.UploadUserFileWithPath }) => void
    remove: ({ file }: { file: M.UploadUserFileWithPath }) => void
  }
}
export const FileUploader = createComponent<FileUploaderOptions>({
  props: {},
  emits: {
    success: () => fn,
    remove: () => fn,
  },
}, (props, { emit }) => {
  const { ossDataBj, getResourceOssData, ossDataBjLoading } = useUploader()
  const fileList = ref<UploadUserFile[]>([])
  const dialogVisible = ref(false)
  const dialogImageUrl = ref('')
  const uploadRef = ref()

  const handlePictureCardPreview: UploadProps['onPreview'] = uploadFile => {
    dialogImageUrl.value = uploadFile.url!
    dialogVisible.value = true
  }

  class UploadAjaxError extends Error {
    name = 'UploadAjaxError'
    status: number
    method: string
    url: string

    constructor(message: string, status: number, method: string, url: string) {
      super(message)
      this.status = status
      this.method = method
      this.url = url
    }
  }

  function getError(
    action: string,
    option: UploadRequestOptions,
    xhr: XMLHttpRequest,
  ) {
    let msg: string
    if (xhr.response) {
      msg = `${xhr.response.error || xhr.response}`
    } else if (xhr.responseText) {
      msg = `${xhr.responseText}`
    } else {
      msg = `fail to ${option.method} ${action} ${xhr.status}`
    }

    return new UploadAjaxError(msg, xhr.status, option.method, action)
  }

  function getBody(xhr: XMLHttpRequest): XMLHttpRequestResponseType {
    const text = xhr.responseText || xhr.response
    if (!text) {
      return text
    }

    try {
      return JSON.parse(text)
    } catch {
      return text
    }
  }

  const handleRemove = (file: UploadUserFile) => {
    emit('remove', {
      file,
    })
  }

  const handleStart = (rawFile: UploadUserFile, newBlob: Blob) => {
    rawFile.uid = Date.now() + Math.random()
    uploadRef.value.handleStart(rawFile)
    uploadRef.value?.submit()
  }

  const clean = () => {
    fileList.value = []
  }

  const uploadHandler = (options: any) => {
    const data = ossDataBj.value
    const file = options.file as File & { path: string, url: string }
    // 创建文件对象并添加到列表中
    const uploadFile: UploadUserFile = {
      name: file.name,
      size: file.size,
      uid: Date.now(),
      status: 'ready' as UploadStatus,
      percentage: 0,
      raw: file as any,
    }

    const formData = new FormData()
    const path = data?.dir + '/' + file.name
    formData.append('key', path)
    formData.append('policy', data?.policy || '')
    formData.append('OSSAccessKeyId', data?.accessid || '')
    formData.append('success_action_status', '200')
    formData.append('signature', data?.signature || '')
    formData.append('Content-Type', file.type || 'image/jpeg')
    formData.append('file', file)
    file.path = path
    const xhr = new XMLHttpRequest()

    if (xhr.upload) {
      xhr.upload.addEventListener('progress', evt => {
        const progressEvt = evt as UploadProgressEvent
        progressEvt.percent = evt.total > 0 ? (evt.loaded / evt.total) * 100 : 0
        uploadFile.percentage = progressEvt.percent
        options?.onProgress && options?.onProgress(progressEvt)
      })
    }

    xhr.addEventListener('load', () => {
      if (xhr.status < 200 || xhr.status >= 300) {
        uploadFile.status = 'error' as UploadStatus
        return options.onError && options.onError(getError(data?.host || '', options, xhr))
      }
      uploadFile.status = 'success' as UploadStatus
      uploadFile.url = file.name
      options.onSuccess && options.onSuccess(getBody(xhr))

      emit('success', {
        file: uploadFile,
      })
    })

    xhr.addEventListener('error', () => {
      uploadFile.status = 'error' as UploadStatus
      options.onError && options.onError(getError(data?.host || '', options, xhr))
    })

    xhr.open('POST', data?.host || '')
    xhr.send(formData)
    return xhr
  }

  onMounted(() => {
    void getResourceOssData()
  })

  onUnmounted(() => {
    ossDataBj.value = undefined
  })

  useExpose({
    uploadHandler,
    handleStart,
    clean
  })

  return () => (
    <x-file-uploader class="flex">
      <ElUpload
        ref={uploadRef}
        v-loading={ossDataBjLoading.value}
        disabled={ossDataBjLoading.value}
        multiple
        auto-upload
        accept="image/png,image/jpeg,image/jpg"
        v-model:file-list={fileList.value}
        action=""
        list-type="picture-card"
        on-preview={handlePictureCardPreview}
        on-remove={handleRemove}
        http-request={uploadHandler}
      />
      <el-dialog append-to-body v-model={dialogVisible.value}>
        <img w-full src={dialogImageUrl.value} alt="Preview Image" />
      </el-dialog>

    </x-file-uploader>
  )
})

export default FileUploader
