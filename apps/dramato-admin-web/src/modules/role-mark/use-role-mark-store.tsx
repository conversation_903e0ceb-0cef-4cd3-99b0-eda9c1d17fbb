/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Checkbox, CreateTableOld, Icon, openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { ref, watch } from 'vue'
import { apiGetAiVoiceResource, apiGetSingleSubtitle, apiSaveSingleSubtitleRole, apiGetSeriesCharacters, apiSaveSubtitleContent } from './role-mark-api'
import { M3u8Player } from '../resource/components/m3u8-player'
import { CharacterTable } from './character'
import { Subtitle } from 'src/modules/common/subtitle/subtitle'
import { apiUpdateRoleTerminology } from 'src/modules/resource-publish/resource-publish-api'
import { trim } from 'lodash-es'
import { useTopStore } from './use-top-store.tsx'
import { mc } from '@skynet/shared'

export const useRoleMark = () => {
  return {
    Table,
    list,
    loading,
    onQuery,
    getList,
    showVideoAndSrt,
    cdnUrl,
  }
}

const Table = CreateTableOld<Api.RoleMark.IAiVoiceResourceInfo>()
const loading = ref<boolean>(false)
const list = ref<Api.RoleMark.IAiVoiceResourceInfo[]>([])
const defaultLanguageCode = ref<string>('')
const cdnUrl = 'https://img.tianmai.cn/'
const characters = ref<Api.RoleMark.ICharacter[]>([])
const series_resource_id = ref<number>()
const isBatchSelect = ref(false)
const emotions = ref<string[]>([])
const effects = ref<string[]>([])
const emotions_cn = ref<string[]>([])
const effects_cn = ref<string[]>([])

const getList = async (id: number) => {
  const res = await apiGetAiVoiceResource({
    series_resource_id: id,
  })
  list.value = res.data?.srts.find(item => item.language === res.data?.default_language_code)?.infos || []
  defaultLanguageCode.value = res.data?.default_language_code || ''
}

const getCharacters = async (id: number) => {
  try {
    const res = await apiGetSeriesCharacters({
      series_resource_id: id,
    })
    characters.value = res?.data?.characters || []
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    loading.value = false
  }
}

const onQuery = (id: number) => {
  series_resource_id.value = id
  void getCharacters(id)
  void getList(id)
}

const getSubtitleContent = (path: string) => {
  return new Promise(resolve => {
    void fetch(getRealSubtitle(path))
      .then(response => response.blob())
      .then(blob => {
        const reader = new FileReader()
        reader.onload = function (event) {
          const content = event.target?.result as string
          if (content.indexOf('<!doctype html>') === 0) {
            resolve('')
          } else {
            resolve(content)
          }
        }
        reader.readAsText(blob)
      })
  })
}

function getRealSubtitle(path: string) {
  const url = path.indexOf('http') === 0 ? path : `${cdnUrl}${path}`
  console.log(url, 'url----')
  return url
}

const showVideoAndSrt = async (row: Api.RoleMark.IAiVoiceResourceInfo, resourceList: Api.RoleMark.IAiVoiceResourceInfo[], series_resource_id: number) => {
  const path = 'srt_path'
  const videoType = ref<'ora_video_path' | 'pure_video_path'>('pure_video_path')
  const currentRow = ref(row)
  const tempCode = await getSubtitleContent(row[path]) as string
  const code = ref<string>(tempCode)
  const videoRef = ref<any>(null)
  const currentTime = ref(0)
  const btnLoading = ref(false)
  const url = ref('')
  const subtitleRef = ref()
  const isAsync = ref(true)

  const subtitles = ref([{
    language: defaultLanguageCode.value,
    type: 'normal',
    subtitle: getRealSubtitle(currentRow.value[path]),
  }])
  // 用于置顶排序
  const {
    saveData,
    getData,
  } = useTopStore()

  const annotations = ref<Api.RoleMark.IAnnotation[]>([])

  watch(() => [currentRow.value.num], async () => {
    btnLoading.value = true
    isBatchSelect.value = false
    try {
      const res = await apiGetSingleSubtitle({
        series_resource_id,
        serial_number: currentRow.value.num,
      })
      // 已经填入的
      annotations.value = res.data?.annotations || []
      emotions.value = res.data?.emotions || []
      effects.value = res.data?.effects || []
      emotions_cn.value = res.data?.emotions_cn || []
      effects_cn.value = res.data?.effects_cn || []
      const subtile = await getSubtitleContent(currentRow.value[path]) as string
      code.value = subtile
      url.value = getRealSubtitle(currentRow.value[path])
      subtitles.value = [{
        language: defaultLanguageCode.value,
        type: 'normal',
        subtitle: getRealSubtitle(currentRow.value[path]),
      }]
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }, {
    immediate: true,
  })

  watch(() => videoRef.value, () => {
    videoRef.value.on('timeupdate', () => {
      currentTime.value = videoRef.value.getCurrentTime() || 0
    })
  })

  const getFormatSubtitle = (list: Api.RoleMark.ISubtitleLine[]) => {
    return list.map(item => {
      const orderLine = `${item.order}`
      return `${orderLine}\n${item.time}\n${item.content}\n`
    }).join('\n')
  }

  const onSaveSubtitle = async () => {
    const _list = subtitleRef.value.getContent() as Api.RoleMark.ISubtitleLine[]
    const isContentChange = subtitleRef.value.getContentChange()
    const list = _list.map((item, index) => {
      item.order = index + 1
      return item
    })

    const arr = list.filter(item => !item.content || !item.time)
    if (arr.length > 0) {
      showFailToast(`字幕第${arr.map(item => item.order).join(',')}请完成字幕内容及时间的填写`)
      return
    }

    const result: any[] = []

    list.map((item, index: number) => {
      const selectedCharacters = item.selectedCharacters || []
      selectedCharacters.map(item => {
        const curCharacter = characters.value.find(o => o.character_id === item.character_id)
        result.push({
          character_id: item.character_id,
          ...item,
          ...curCharacter,
          order: index + 1,
        })
      })
    })
    const formatCode = getFormatSubtitle(list)
    btnLoading.value = true
    console.log(formatCode, 'formatCode---')
    try {
      if (isContentChange) {
        console.log(isContentChange, 'isContentChange')
        const res = await apiSaveSubtitleContent({
          serial_number: currentRow.value.num,
          series_resource_id: series_resource_id,
          language_code: defaultLanguageCode.value,
          content: formatCode,
          audio_type: 0,
          alter_type: 1
        })
        currentRow.value[path] = res.data?.subtitle_uri || ''
      }

      await apiSaveSingleSubtitleRole({
        series_resource_id,
        serial_number: currentRow.value.num,
        annotations: result.map(o => {
          return {
            order: o.order,
            character_id: o.character_id,
            character_type: o.character_type || 0,
            emotion: o.emotion,
            effect: o.effect
          }
        }),
      })
      showSuccessToast('保存成功')
      await getList(series_resource_id)
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }

  const sortRoles = () => {
    const topFixedList = getData('FIXED_KEY_' + series_resource_id) as M.ITerminology[]
    console.log('topFixedList', topFixedList)
    const _list = subtitleRef.value.getContent() as Api.RoleMark.ISubtitleLine[]
    // 统计角色名出现的频次
    const characterFrequency: Record<string, number> = {}

    // 遍历字幕行，统计每个角色出现的次数
    _list.forEach(line => {
      if (line.selectedCharacters && line.selectedCharacters.length > 0) {
        line.selectedCharacters.forEach(c => {
          characterFrequency[c.name] = (characterFrequency[c.name] || 0) + 1
        })
      }
    })

    // 先根据置顶列表排序，再根据频次排序
    const sortedList = [...characters.value.map(item => {
      item.isFixed = false
      return item
    })].sort((a, b) => {
      // 如果不在置顶列表中或者置顶列表为空，则按照频次排序
      const freqA = characterFrequency[a.name] || 0
      const freqB = characterFrequency[b.name] || 0
      return freqB - freqA // 降序排列，出现频次高的排在前面
    })

    // 如果有置顶列表
    if (topFixedList && topFixedList.length > 0) {
      // 创建一个新的排序列表
      const newSortedList: Api.RoleMark.ICharacter[] = []

      // 首先添加置顶列表中的角色（按照置顶列表的顺序）
      topFixedList.forEach(fixedItem => {
        const foundCharacter = sortedList.find(char => char.character_id === fixedItem.character_id)
        if (foundCharacter) {
          newSortedList.push({
            ...foundCharacter,
            isFixed: true,
          })
        }
      })

      // 然后添加其余未置顶的角色（保持原有的频次排序）
      sortedList.forEach(character => {
        // 检查该角色是否已经在新列表中（即是否为置顶角色）
        if (!newSortedList.some(item => item.character_id === character.character_id)) {
          newSortedList.push(character)
        }
      })

      // 用新排序的列表替换原列表
      characters.value = newSortedList
    } else {
      characters.value = sortedList
    }
  }

  openDialog({
    title: () => (
      <div class="flex">
        <div class="w-[309px]">
          <div class="text-lg font-bold">第{ currentRow.value.num }集</div>
        </div>
        <Button class={mc('btn btn-xs ml-4', isBatchSelect.value ? 'btn-default' : 'btn-primary')} onClick={() => {
          isBatchSelect.value = !isBatchSelect.value
        }}>{ isBatchSelect.value ? '取消批量' : '批量选择' }</Button>
      </div>
    ),
    customClass: '!w-[87vw] !max-w-[1200px] !h-[690px]',
    body: () => (
      <x-video-subtile-container
        v-loading={btnLoading.value}
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.3)"
        class="flex flex-col">
        <div class="flex flex-row">
          <x-video-area>
            { currentRow.value[videoType.value]
              ? (
                  <M3u8Player
                    url={`${cdnUrl}${currentRow.value[videoType.value]}`}
                    subtitles={subtitles.value}
                    currentLanguage={defaultLanguageCode.value}
                    onPlayerReady={(e: any) => {
                      videoRef.value = e
                    }}
                  />
                )
              : <div class="flex h-[550px] w-[309px] items-center justify-center text-[var(--text-2)]">暂无视频</div> }
          </x-video-area>
          <x-subtitle-area class="h-[550px] flex-1 basis-[550px] overflow-auto">
            <Subtitle
              ref={subtitleRef}
              class="h-full"
              url={url.value}
              currentTime={currentTime.value}
              onVideoProgress={(time: number) => {
                if (videoRef.value) videoRef.value?.seek(time)
              }}
              onAnnotationChange={(annotationList: Api.RoleMark.IAnnotation[]) => {
                annotations.value = annotationList
              }}
              onChangeRoleOrder={(setNextLine = true) => {
                sortRoles()
                if (setNextLine) subtitleRef.value.setNextLine()
              }}
              showContentText={false}
              isBatchSelect={isBatchSelect.value}
              characters={characters.value}
              emotions={emotions.value}
              effects={effects.value}
              emotions_cn={emotions_cn.value}
              effects_cn={effects_cn.value}
              annotations={annotations.value}
              isAsyncScroll={isAsync.value}
            />
          </x-subtitle-area>
          <x-term class="w-[250px]">
            <CharacterTable
              loading={btnLoading.value}
              list={characters.value}
              onInsertRole={row => {
                subtitleRef.value.setRole(row)
                sortRoles()
              }}
              onFixTop={row => {
                const topFixedList = getData('FIXED_KEY_' + series_resource_id) || []
                const fixedIndex = topFixedList.findIndex((item: M.ITerminology) => item.character_id === row.character_id)
                if (fixedIndex > -1) {
                  topFixedList.splice(fixedIndex, 1)
                } else {
                  topFixedList?.push({
                    ...row,
                  })
                }
                saveData('FIXED_KEY_' + series_resource_id, topFixedList)
                sortRoles()
              }}
              onSave={async (tempList: M.ITerminology[]) => {
                const newTermList = tempList.filter(item => !item.character_id)
                const updatedTermList = tempList.filter(item => item.character_id && item.update)
                const termList = newTermList.concat(updatedTermList).map(row => {
                  row.term = row.name
                  row.type = 1
                  row.src_type = 1
                  row.category = 'character'
                  if (row.character_id) row.term_id = row.character_id
                  return row
                })
                if (termList.some(item => !trim(item.name))) {
                  showFailToast('请完成术语填写或删掉不需要的术语')
                  return
                }
                btnLoading.value = true
                try {
                  await apiUpdateRoleTerminology({
                    series_resource_id: series_resource_id,
                    terms: termList,
                  })
                  await getCharacters(series_resource_id)
                  sortRoles()
                  showSuccessToast('保存成功')
                } catch (error: any) {
                  showFailToast(error.response.data.message || '操作失败')
                } finally {
                  btnLoading.value = false
                }
              }} />
          </x-term>
        </div>
        <div class="mt-4 flex w-full flex-row items-center justify-between space-x-4">
          <div class="flex w-full items-center justify-between">
            <div class="flex items-center">
              <Button class="btn btn-outline btn-xs" disabled={currentRow.value.num === resourceList[0].num || btnLoading.value} onClick={() => {
                const curIndex = resourceList.findIndex(item => item.num === currentRow.value.num)
                const item = resourceList[curIndex - 1]
                currentRow.value = item
              }}
              >上一集
              </Button>
              <div class="px-4">{ currentRow.value.num }</div>
              <Button class="btn btn-outline btn-xs" disabled={resourceList[resourceList.length - 1].num === currentRow.value.num || btnLoading.value} onClick={() => {
                const curIndex = resourceList.findIndex(item => item.num === currentRow.value.num)
                const item = resourceList[curIndex + 1]
                currentRow.value = item
              }}
              >下一集
              </Button>
              <select
                class="select select-bordered select-sm ml-4 !w-[120px]"
                value={videoType.value}
                onInput={(e: Event) => {
                  videoType.value = (e.target as HTMLSelectElement).value as 'ora_video_path' | 'pure_video_path'
                }}
              >
                <option value="ora_video_path">原视频</option>
                <option value="pure_video_path">无字幕视频</option>
              </select>
              <label class="flex items-center ml-4 cursor-pointer">
                <Checkbox
                  label=""
                  modelValue={isAsync.value}
                  onUpdate:modelValue={(value: boolean) => {
                    isAsync.value = value
                  }}
                />
                <span>同步滚动</span>
              </label>
            </div>
            <div class="flex">
              <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => onSaveSubtitle()}>
                {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                保存字幕
              </Button>
            </div>
          </div>
        </div>
      </x-video-subtile-container>
    ),
  })
}
