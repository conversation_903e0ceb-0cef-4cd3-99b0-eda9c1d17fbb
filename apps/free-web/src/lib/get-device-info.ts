import { inApp, jsBridge } from './jsbridge'

export type DeviceInfo = {
  app_name?: string
  platform?: string
  app_version?: string
  device_id?: string
  device_manufacturer?: string
  device_model?: string
  manufacturer?: string
  model?: string
  country?: string
  content_language?: string
  language?: string
  safe_area_height?: number
  appsflyer_id?: string
  device_hash?: string
  timezone?: string
}

export let deviceInfo: DeviceInfo | undefined

export const getDeviceInfo = async () => {
  if (!inApp) return
  if (deviceInfo) return deviceInfo
  deviceInfo = await jsBridge<DeviceInfo>('getDeviceInfo')
  return deviceInfo
}
