import BScroll from '@better-scroll/core'
import { Ref, ref, watch } from 'vue'

export const useBetterScroll = (dom?: Ref<HTMLElement | undefined>, direction: 'vertical' | 'horizontal' = 'vertical'): {
  bsInstance: Ref<InstanceType<typeof BScroll> | null>
  init: () => void
} => {
  const bsInstance = ref<InstanceType<typeof BScroll> | null>(null)

  watch(() => dom?.value, () => {
    if (dom?.value) {
      init()
    }
  })

  const init = () => {
    if (!dom?.value) return
    void import('@better-scroll/core').then(({ default: BScroll }) => {
      bsInstance.value = new BScroll(dom.value!, {
        click: true,
        tap: 'tap',
        scrollX: direction === 'horizontal',
        scrollY: direction === 'vertical',
      })
    })
  }

  return {
    bsInstance: bsInstance as Ref<InstanceType<typeof BScroll> | null>,
    init,
  }
}
