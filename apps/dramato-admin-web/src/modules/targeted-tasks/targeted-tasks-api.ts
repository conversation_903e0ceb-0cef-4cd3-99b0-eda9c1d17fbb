import { httpClient } from 'src/lib/http-client'

export const apiGetTaskList = () =>
  httpClient.post<ApiResponse<{
    task_list: M.TargetedTasks.ListRequest.Item[]
  }>>('/dataeye/task_list', {})

export const apiResetTask = (data: { task_id: number, serial_number: number }) =>
  httpClient.post<ApiResponse<{ success: boolean }>>('/newadmin/dataeye/search_retry', data)

export const apiCancelTask = (data: { task_id: number, serial_number: number }) =>
  httpClient.post<ApiResponse<{ success: boolean }>>('/dataeye/search_cancel', data)

export const apiSaveTask = (data: M.TargetedTasks.SaveRequest.Task) =>
  httpClient.post<ApiResponse<{ success: boolean }>>('/newadmin/dataeye/search', data)

export const apiSyncTaskList = () =>
  httpClient.post<ApiResponse<{ success: boolean }>>('/dataeye/search_sync', {})
