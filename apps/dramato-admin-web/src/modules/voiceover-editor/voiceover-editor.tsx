import { createComponent, fn } from '@skynet/shared'
import { openDialog, showFailToast } from '@skynet/ui'
import { M3u8Player } from '../resource/components/m3u8-player'
import Editor from '../resource/components/editor'
import { ElEmpty } from 'element-plus'
import { ref, onMounted, onUnmounted } from 'vue'
import timeLineEditor from './timeLineEditor.vue'

type VoiceoverEditorOptions = {
  props: {
    checkedItem: number[] 
    showCheckbox: boolean
    showColumns: string[]
  }
  emits: {
    videoImported: (url: string) => void
    timeChange: (time: number) => void
    playStateChange: (isPlaying: boolean) => void
  }
}

interface M3u8PlayerRef {
  seek: (time: number) => void
  getCurrentTime: () => number
  on: (event: string, callback: () => void) => void
}

export const VoiceoverEditor = createComponent<VoiceoverEditorOptions>({
  props: {
    checkedItem: [],
    showCheckbox: false,
    showColumns: [],
  },
  emits: {
    videoImported: fn,
    timeChange: fn,
    playStateChange: fn,
  },
}, (props, { emit }) => {
  const videoPlayer = ref<HTMLVideoElement | null>(null)
  const timelineEditor = ref<InstanceType<typeof timeLineEditor> | null>(null)
  const currentVideo = ref<string>('')
  const videoRef = ref<M3u8PlayerRef | null>(null)
  const code = ref<string>('')
  const currentTime = ref(0)

  const cdnUrl = 'https://img.tianmai.cn/'
  const subtitles = ref([{
    language: 'en',
    type: 'srt',
    subtitle: `${cdnUrl}vt/10651/04d015c5-7440-4ce9-915f-5b0404a9bd04.srt`,
  }])

  const getSubtitleContent = async (path: string): Promise<string> => {
    const response = await fetch(`${cdnUrl}${path}`)
    const blob = await response.blob()
    return new Promise(resolve => {
      const reader = new FileReader()
      reader.onload = function(event) {
        const content = event.target?.result as string
        resolve(content.indexOf('<!doctype html>') === 0 ? '' : content)
      }
      reader.readAsText(blob)
    })
  }

  // 事件处理函数
  const handleTimeUpdate = () => {
    if (videoPlayer.value && timelineEditor.value) {
      timelineEditor.value.updateCurrentTime(videoPlayer.value.currentTime)
    }
  }

  const handlePlay = () => {
    timelineEditor.value?.setPlayState(true)
    emit('playStateChange', true)
  }

  const handlePause = () => {
    timelineEditor.value?.setPlayState(false)
    emit('playStateChange', false)
  }

  const handleVideoImported = (videoUrl: string) => {
    currentVideo.value = videoUrl
    emit('videoImported', videoUrl)
  }

  const handleTimeChange = (time: number) => {
    if (videoPlayer.value) {
      videoPlayer.value.currentTime = time
      emit('timeChange', time)
    }
  }

  const handlePlayStateChange = (isPlaying: boolean) => {
    if (videoPlayer.value) {
      if (isPlaying) {
        videoPlayer.value.play()
      } else {
        videoPlayer.value.pause()
      }
      emit('playStateChange', isPlaying)
    }
  }

  // 初始化字幕
  const initSubtitle = async () => {
    code.value = await getSubtitleContent("vt/10651/04d015c5-7440-4ce9-915f-5b0404a9bd04.srt")
  }

  onMounted(() => {
    void initSubtitle()
  })

  onUnmounted(() => {
    if (currentVideo.value) {
      URL.revokeObjectURL(currentVideo.value)
    }
  })

  return () => (
    <div class="voiceover-editor">
      {currentVideo.value ? (
        <div class="video-player-container">
          <div class="flex-1">
            <video
              ref={videoPlayer}
              class="video-player"
              src={currentVideo.value}
              onTimeupdate={handleTimeUpdate}
              onPlay={handlePlay}
              onPause={handlePause}
            />
          </div>
          <div class="flex-1">
            <M3u8Player
              currentLanguage="en"
              subtitles={subtitles.value}
              url={currentVideo.value}
              onPlayerReady={(e: M3u8PlayerRef) => {
                videoRef.value = e
                videoRef.value.on('timeupdate', () => {
                  currentTime.value = videoRef.value?.getCurrentTime() || 0
                })
              }}
            />
          </div>
          <div class="flex-1">
            <Editor
              customClass="w-full h-[550px]"
              code={code.value}
              options={{
                language: 'plaintext',
                formatOnPaste: false,
                tabSize: 2,
                inDiffEditor: false,
                minimap: {
                  enabled: false,
                }
              }}
              isAsync={true}
              currentTime={currentTime.value}
              onChange={(e: string) => {
                code.value = e
              }}
              onVideoProgress={(time: number) => {
                videoRef.value?.seek(time)
              }}
            />
          </div>
        </div>
      ) : (
        <div class="no-video">
          <ElEmpty description="请导入视频" />
        </div>
      )}
      
      <timeLineEditor
        ref={timelineEditor}
        onVideoImported={handleVideoImported}
        onTimeChange={handleTimeChange}
        onPlayStateChange={handlePlayStateChange}
      />
    </div>
  )
})

export default VoiceoverEditor