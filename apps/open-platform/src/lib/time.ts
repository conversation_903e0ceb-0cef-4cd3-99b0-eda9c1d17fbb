import dayjs from 'dayjs'

export const fromNow = (timestamp?: number) => {
  if (!timestamp) return ''
  const diffYear = dayjs(new Date()).diff(dayjs(timestamp), 'year')
  const diffMonth = dayjs(new Date()).diff(dayjs(timestamp), 'month')
  const diffDay = dayjs(new Date()).diff(dayjs(timestamp), 'day')
  const diffHours = dayjs(new Date()).diff(dayjs(timestamp), 'hours')
  const diffMinutes = dayjs(new Date()).diff(dayjs(timestamp), 'minutes')
  return diffYear > 0 ? `${diffYear}年前` : diffMonth > 0 ? `${diffMonth}个月前` : diffDay > 0 ? `${diffDay}天前` : diffHours > 0 ? `${diffHours}小时前` : diffMinutes > 0 ? `${diffMinutes}分钟前` : '刚刚'
}

export const diffRange = (start: string, end?: string) => {
  // 计算两个日期之间的差值
  const startDate = dayjs(start)
  const endDate = dayjs(end === '' ? new Date() : end)
  const diffTime = endDate.diff(startDate, 'month')
  const diffYear = Math.floor(diffTime / 12)
  const diffMonth = diffTime % 12
  return diffYear > 0 ? `${diffYear}年${diffMonth}个月` : `${diffMonth}个月`
}

export const formatDate = (date?: string) => {
  if (!date || date === '') return '至今'
  return dayjs(date).format('YYYY年MM月')
}
