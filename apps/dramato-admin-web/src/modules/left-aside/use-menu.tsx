/* eslint-disable @typescript-eslint/no-explicit-any */
import { showFailToast } from '@skynet/ui'
import { ref } from 'vue'
import { MenuItem, apiGetMenu } from './menu-api'

export type SubMenu = {
  name: string
  menuTitles: string[]
  children: MenuItem[]
}

const setRole = (roleId: string) => {
  localStorage.setItem('role', roleId)
}

const getRole = () => {
  return localStorage.getItem('role')
}

export const useMenu = () => {
  const menus = ref<SubMenu[]>([])
  // 菜单展示
  menus.value = [{
    name: '内容运营',
    menuTitles: ['资源管理', '剧集管理', '应用短剧管理'],
    children: [],
  }, {
    name: '内容管理',
    menuTitles: ['首页模块配置', 'banner管理', '内容模板', '推送通知', '运营位管理'],
    children: [],
  }, {
    name: '应用资产',
    menuTitles: ['应用管理'],
    children: [],
  }, {
    name: '收银台管理',
    menuTitles: ['会员档位管理', '充值档位管理'],
    children: [],
  }, {
    name: '短剧情报',
    menuTitles: ['短剧排行', 'Top素材投放表', '定向素材投放表', '素材推送'],
    children: [],
  }, {
    name: '商业化',
    menuTitles: ['广告策略组', '剧集定价'],
    children: [],
  }]

  const getMenuInfo = async () => {
    try {
      const menuRes = await apiGetMenu()
      // setRole('' + menuRes.data?.role || '1')
      const res = await Promise.resolve({
        data: {
          menu: [{
            path: '/resource',
            title: '资源管理',
            role: [0, 1],
          }, {
            path: '/episode',
            title: '剧集管理',
            role: [0],
          }, {
            path: '/short-drama',
            title: '应用短剧管理',
            role: [0],
          }, {
            path: '/home-config',
            title: '首页模块配置',
            role: [0],
          }, {
            path: '/banner',
            title: 'banner管理',
            role: [0],
          }, {
            path: '/content-template',
            title: '内容模板',
            role: [0],
          }, {
            path: '/notification',
            title: '推送通知',
            role: [0],
          }, {
            path: '/activity-popup',
            title: '运营位管理',
            role: [0],
          }, {
            path: '/application',
            title: '应用管理',
            role: [0],
          }, {
            path: '/member',
            title: '会员档位管理',
            role: [0],
          }, {
            path: '/recharge-level',
            title: '充值档位管理',
            role: [0],
          }, {
            path: '/drama-rank',
            title: '短剧排行',
            role: [0],
          }, {
            path: '/material',
            title: 'Top素材投放表',
            role: [0],
          }, {
            path: '/targeted-material',
            title: '定向素材投放表',
            role: [0],
          }, {
            path: '/material-push',
            title: '素材推送',
            role: [0],
          }, {
            path: '/strategy-group',
            title: '广告策略组',
            role: [0],
          }, {
            path: '/episode-pricing',
            title: '剧集定价',
            role: [0],
          },
          ],
          role: 1,
        },
        code: 0,
        message: '',
      })
      // 默认给个权限小的
      setRole('' + menuRes.data?.role || '1')
      menus.value.forEach(subMenu => {
        res.data?.menu.forEach(m => {
          if (subMenu.menuTitles.includes(m.title)) {
            subMenu.children.push(m)
          }
        })
      })
    } catch (error: any) {
      showFailToast(error.response.data.message || '菜单获取失败')
    }
  }
  return {
    getMenuInfo,
    menus,
    getRole,
  }
}
