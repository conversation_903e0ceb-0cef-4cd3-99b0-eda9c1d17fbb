import { createComponent, fn, mc, tryParseFloat } from '@skynet/shared'
import { Checkbox, MergeClass } from '@skynet/ui'
import { set } from 'lodash-es'
import { ref } from 'vue'
type Value = [string, ...Array<[number | null, number | null]>]
type ToggleRangeOptions = {
  props: {
    modelValue?: Value
    inputClass?: string
  }
  emits: {
    'update:modelValue': (value: Value) => void
  }
}
export const ToggleRange = createComponent<ToggleRangeOptions>({
  props: {
    modelValue: ['invalid', [null, null]],
    inputClass: '',
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const onChange = (path: string | number, value: string | number | null) => {
    const clone = [...props.modelValue] as Value
    set(clone, path, value)
    emit('update:modelValue', clone)
  }

  const disabled = ref(['/strategy-group/create/target'].includes(location.pathname))

  return () => (
    <MergeClass baseClass="flex gap-4 items-center">
      <Checkbox modelValue={props.modelValue[0] === 'valid'}  disabled={disabled.value} label="使用" onUpdate:modelValue={v =>
        onChange(0, v ? 'valid' : 'invalid')} class="shrink-0"
      />
      {props.modelValue
      && (
        <>
          <input min="0" type="number" step="0.01" disabled={disabled.value} value={props.modelValue[1]?.[0]} class={mc('input input-bordered input-sm flex-1', props.inputClass)}
            onInput={e => onChange('1.0', tryParseFloat(2, (e.target as HTMLInputElement).value, null))}
          />
          <span class="shrink-0">至</span>
          <input min="0" type="number" step="0.01" disabled={disabled.value} value={props.modelValue[1]?.[1]} class={mc('input input-bordered input-sm flex-1', props.inputClass)}
            onInput={e => onChange('1.1', tryParseFloat(2, (e.target as HTMLInputElement).value, null))}
          />
        </>
      )}
    </MergeClass>
  )
})
