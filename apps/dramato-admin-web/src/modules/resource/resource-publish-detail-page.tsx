/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { RouterLink } from 'vue-router'
import { CreateTableOld, Checkbox, TableColumnOld, Button, SvgIcon, Icon, openDialog, showFailToast, showSuccessToast, Tooltip, CreateForm, transformNumber } from '@skynet/ui'
import { useCheck } from './use-check'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, onUnmounted, ref, watch, computed } from 'vue'
import { useResourceStore } from './use-resource-store'
import { useRoute, useRouter } from 'vue-router'
import { apiSeriesResourceList, apiGetRecordList, apiRetryTrans, apiSubtitleInject, apiSubtitleTrans } from './resource-api'
import { apiGetResourcePublishDetail, apiGetSubtitleErrors, apiChangeAudit, apiCommitSubtitleCheckLog, apiGetSubtitleErrorsDetail, apiGetSubtitleEditable, apiSubtitleSyncVoice, apiGetTermsStatusList, apiTransLanguageTerms } from 'src/modules/resource-publish/resource-publish-api'
import { langKey, langValue, langKeyForCol, defaultClipValue, TERM_STATUS_LIST } from './constant'
import dayjs from 'dayjs'
import { M3u8Player } from './components/m3u8-player'
import Editor from './components/editor'
import { z } from 'zod'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { ShowStatusDesc } from 'src/modules/resource-publish/components/show-status-desc'
import { FileUploader } from 'src/modules/resource-publish/components/file-uploader'
import { ColFilter } from './components/col-filter'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { useMenu } from 'src/modules/menu/use-menu'
import { getSubtitleContent } from 'src/modules/common/subtitle/until'
import { SubtitleWithVideo } from 'src/modules/resource/components/subtitle-with-video'
import { ElTooltip } from 'element-plus'
import { SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { apiShortAISrt } from 'src/modules/voiceover-resource/voiceover-resource-api'

type ResourceDetailPageOptions = {
  props: {}
}

const PROCESSING = 'processing'
const TRANS_ERROR = 'trans_failed'
const PURE_ERROR = 'failed'
const EXTRACT_ERROR = 'extract_failed'
const HISTORY_EXTRACT_ERROR = 'failed'

export const ResourceDetailPage = createComponent<ResourceDetailPageOptions>({
  props: {},
}, props => {
  const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()
  const route = useRoute()
  const router = useRouter()
  const { onPreviewVideo, findMissingNumbers, makeUpList, showColumnHeader, setMissingData, refreshSyncList, getTranscodeVideos, externalTransProgress } = useResourceStore()
  const { getUserRoles } = useMenu()
  const hasChecked = new Map()
  const resourceDetail = ref<M.IResourcePublishDetail & { series_resource_id?: number }>({
    series_resource_id: undefined,
    count: undefined,
    title: '',
    audit_process: [],
    audit_all_subtitle_process: false,
  })
  const fileUploaderRef = ref<M.FileUploaderInstance>()

  const list = ref<M.IResourceDrama[]>([])
  const subtitleErrorList = ref<M.IResourceSubtitleError[]>([])
  // 术语状态列表
  const termsList = ref<M.ITermStatus[]>([])
  // 字幕修改日志
  const subtitleLogs = ref<M.IAlterSubtitle[]>([])
  const loading = ref(false)
  const total = ref(0)
  const Table = CreateTableOld<M.IResourceDrama>()
  let dynamicColumns: TableColumnOld<M.IResourceDrama>[] = []
  const columns = ref<any[]>([])
  const selectCols = ref<string[]>([])
  const queryForm = ref<M.IResourceSeriesParams>({
    series_resource_id: +route.params.id,
    page_index: 1,
    page_size: 300, // 选填参数
  })
  const btnLoading = ref(false)
  const editableLanguage = ref('')

  const rejectReason = [
    {
      label: '无字幕视频有问题',
      value: 1,
    }, {
      label: '字幕有问题',
      value: 3,
    }]

  const isFinalPage = computed(() => {
    return route.fullPath.includes('/resource-publish/detail')
  })
  // 1 资源验收 2 多语言信息 3 多语言字幕 4 终审发布 5 资源录入
  const isRejectStatus = computed(() => {
    const isReject = resourceDetail.value.audit_process?.some(item => item.status === 1) || false
    return isReject
  })

  // 非审核通过的有通过按钮
  const hasPassButton = computed(() => {
    const obj = resourceDetail.value.audit_process?.find(item => item.page_type === (isFinalPage.value ? 4 : 3))
    return obj?.status === 0
  })

  const handleParse = (e: ClipboardEvent) => {
    const data = e.clipboardData
    if (!data) return
    const blob = data.items[0]?.getAsFile()
    if (!blob) return
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = event => {
      const base64Str = event.target?.result
      if (typeof base64Str !== 'string') return
      const bytes = window.atob(base64Str.split(',')[1])
      const array = []
      for (let i = 0; i < bytes.length; i++) {
        array.push(bytes.charCodeAt(i))
      }
      const newBlob = new Blob([new Uint8Array(array)], { type: 'image/png' })
      const file = new File([newBlob], `paste_${Date.now()}.png`, { type: 'image/png' })
      fileUploaderRef.value?.handleStart(file, newBlob)
    }
  }

  const { checked, isCheckAll, checkedAllChange, checkboxChange, resetChecked } = useCheck()
  const previewVideo = (path: string, remark?: string) => {
    onPreviewVideo(path, remark)
  }

  const getOriginalSubtitleContent = async (row: M.IResourceDrama, index: number) => {
    const path = getSubtitlePathByIndex(row, index)
    const content = await getSubtitleContent(path)
    return content
  }

  const getSubtitlePathByIndex = (row: M.IResourceDrama, langIndex: number) => {
    return row[`${langKeyForCol[langIndex]}_subtitle_path`]
  }

  const getRefSubtitleContent = async (row: M.IResourceDrama, lang: string) => {
    const cnPath = row[`${lang}_subtitle_path`]
    const content = await getSubtitleContent(cnPath)
    return content
  }

  const previewAudio = (path: string) => {
    openDialog({
      title: '音频查看',
      mainClass: 'px-4 !py-0',
      customClass: '!w-[400px]',
      body: () => <audio src={path} controls />,
    })
  }

  const multiSubtitleChange = (row: M.IResourceDrama) => {
    return langKeyForCol.map((key, index) => {
      return {
        language: key,
        type: 'normal',
        subtitle: row[`${langKeyForCol[index]}_subtitle_path`],
      }
    }).filter((item, index) => {
      return item.subtitle && item.subtitle.indexOf('http') === 0
    })
  }

  const previewM3U8 = async (row: M.IResourceDrama) => {
    const obj = externalTransProgress.value.find(item => item.serial_number === row.serial_number) || null
    const m3u8VideoObj = ref<M.IExternalTransProgress | null>(obj)
    const curRow = ref<M.IResourceDrama>(row)
    const currentSubtitleLang = ref('')
    const top = ref(0)
    const code = ref('')
    const currentTime = ref(0)
    const isAsync = ref(true)

    const subtitles = ref<{
      language: string
      type: string
      subtitle: string
    }[]>(multiSubtitleChange(row))
    if (subtitles.value.length > 0) currentSubtitleLang.value = subtitles.value[0].language || ''
    code.value = await getSubtitleContent(curRow.value[`${currentSubtitleLang.value}_subtitle_path`])
    const player = ref<any>(null)

    watch(() => player.value, newVal => {
      if (newVal) {
        player.value.on('timeupdate', () => {
          currentTime.value = player.value.getCurrentTime() || 0
        })
      }
    })

    openDialog({
      title: () => (
        <div class="flex items-center space-x-2">
          <span>第{curRow.value.serial_number}集</span>
          {curRow.value.remark ? (
            <Tooltip placement="bottom-start" popWrapperClass="z-popover-in-dialog max-w-[400px]" popContent={() => (
              <pre class="whitespace-pre-wrap break-words">备注：{curRow.value.remark}</pre>
            )}>
              <div class="line-clamp-1 w-[400px] flex-1 text-sm text-[var(--text-2)]">
                备注：{curRow.value.remark}
              </div>
            </Tooltip>
          ) : null}
        </div>
      ),
      mainClass: 'px-4 !py-0',
      customClass: '!w-[750px]',
      body: () => (
        <x-video-subtile-container class="flex flex-col">
          <div class="flex flex-row">
            <x-video-area>
              { m3u8VideoObj.value?.play_path && m3u8VideoObj.value.audio_external_status === 2
                ? (
                    <M3u8Player
                      currentLanguage={currentSubtitleLang.value}
                      subtitles={subtitles.value}
                      url={m3u8VideoObj.value?.play_path}
                      region={row.region}
                      onPlayerReady={(e: any) => {
                        player.value = e
                      }}
                    />
                  )
                : '暂无转码视频' }
            </x-video-area>
            <x-subtitle-area class="flex-1">
              <Editor
                customClass="w-full h-[550px]"
                code={code.value}
                options={{
                  language: 'plaintext',
                  formatOnPaste: false,
                  tabSize: 2,
                  inDiffEditor: false,
                  minimap: {
                    enabled: false,
                  } }}
                onChange={e => {
                  code.value = e
                }}
                scrollTop={top.value}
                onScroll={scrollTop => {
                  top.value = scrollTop
                }}
                onVideoProgress={(time: number) => {
                  if (player.value) player.value.seek(time)
                }}
                isAsync={isAsync.value}
                currentTime={currentTime.value}
              />
            </x-subtitle-area>
          </div>
          <div class="mt-4 flex w-full flex-row items-center justify-start space-x-4">
            <div class="flex items-center">
              <Button class="btn btn-outline btn-xs" disabled={curRow.value.serial_number === list.value[0].serial_number || loading.value} onClick={async () => {
                const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
                const item = list.value[curIndex - 1]
                curRow.value = item
                m3u8VideoObj.value = externalTransProgress.value.find(item => item.serial_number === curRow.value.serial_number) || null
                subtitles.value = multiSubtitleChange(curRow.value)
                code.value = await getSubtitleContent(curRow.value[`${currentSubtitleLang.value}_subtitle_path`])
              }}
              >上一集
              </Button>
              <div class="px-4">{ curRow.value.serial_number }</div>
              <Button class="btn btn-outline btn-xs" disabled={list.value[list.value.length - 1].serial_number === curRow.value.serial_number || loading.value} onClick={async () => {
                const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
                const item = list.value[curIndex + 1]
                curRow.value = item
                m3u8VideoObj.value = externalTransProgress.value.find(item => item.serial_number === curRow.value.serial_number) || null
                subtitles.value = multiSubtitleChange(curRow.value)
                code.value = await getSubtitleContent(curRow.value[`${currentSubtitleLang.value}_subtitle_path`])
              }}
              >下一集
              </Button>
            </div>
            <x-subtitle-lang class="flex items-center space-x-4">
              <select class="select select-bordered select-xs" value={currentSubtitleLang.value} onChange={async (e: Event) => {
                const target = e.target as HTMLSelectElement
                const value = target.value
                currentSubtitleLang.value = value
                code.value = await getSubtitleContent(curRow.value[`${currentSubtitleLang.value}_subtitle_path`])
              }}
              >
                {
                  subtitles.value.map(n => <option value={n.language}>{n.language}</option>)
                }
              </select>
            </x-subtitle-lang>
          </div>
        </x-video-subtile-container>
      ),
    })
  }
  // 视频内字幕
  const previewM3U8WithoutSubtitle = async (row: M.IResourceDrama, langIndex: number) => {
    const curRow = ref<M.IResourceDrama>(row)
    const currentSubtitleLang = ref(langKeyForCol[langIndex])
    const top = ref(0)
    const code = ref('')
    const currentTime = ref(0)
    const isAsync = ref(true)
    const url = ref(curRow.value[`${currentSubtitleLang.value}_episode_path`])
    code.value = await getSubtitleContent(curRow.value[`${currentSubtitleLang.value}_subtitle_path`])
    const player = ref<any>(null)

    watch(() => player.value, newVal => {
      if (newVal) {
        player.value.on('timeupdate', () => {
          currentTime.value = player.value.getCurrentTime() || 0
        })
      }
    })

    openDialog({
      title: () => (
        <div class="flex items-center space-x-2">
          <span>第{curRow.value.serial_number}集</span>
          {curRow.value.remark ? (
            <Tooltip placement="bottom-start" popWrapperClass="z-popover-in-dialog max-w-[400px]" popContent={() => (
              <pre class="whitespace-pre-wrap break-words">备注：{curRow.value.remark}</pre>
            )}>
              <div class="line-clamp-1 w-[400px] flex-1 text-sm text-[var(--text-2)]">
                备注：{curRow.value.remark}
              </div>
            </Tooltip>
          ) : null}
        </div>
      ),
      mainClass: 'px-4 !py-0',
      customClass: '!w-[750px]',
      body: () => (
        <x-video-subtile-container class="flex flex-col">
          <div class="flex flex-row">
            <x-video-area>
              {
                url.value && url.value.indexOf('http') === 0
                  ? (
                      <M3u8Player
                        currentLanguage={currentSubtitleLang.value}
                        url={url.value}
                        onPlayerReady={(e: any) => {
                          player.value = e
                        }}
                      />
                    )
                  : <div class="h-[550px] w-[350px] text-center leading-[550px]">当前视频不存在</div>
              }

            </x-video-area>
            <x-subtitle-area class="flex-1">
              <Editor
                customClass="w-full h-[550px]"
                code={code.value}
                options={{
                  language: 'plaintext',
                  formatOnPaste: false,
                  tabSize: 2,
                  inDiffEditor: false,
                  minimap: {
                    enabled: false,
                  } }}
                onChange={e => {
                  code.value = e
                }}
                scrollTop={top.value}
                onScroll={scrollTop => {
                  top.value = scrollTop
                }}
                onVideoProgress={(time: number) => {
                  if (player.value) player.value.seek(time)
                }}
                isAsync={isAsync.value}
                currentTime={currentTime.value}
              />
            </x-subtitle-area>
          </div>
          <div class="mt-4 flex w-full flex-row items-center justify-start space-x-4">
            <div class="flex items-center">
              <Button class="btn btn-outline btn-xs" disabled={curRow.value.serial_number === list.value[0].serial_number || loading.value} onClick={async () => {
                const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
                const item = list.value[curIndex - 1]
                curRow.value = item
                url.value = curRow.value[`${currentSubtitleLang.value}_episode_path`]
                code.value = await getSubtitleContent(curRow.value[`${currentSubtitleLang.value}_subtitle_path`])
              }}
              >上一集
              </Button>
              <div class="px-4">{ curRow.value.serial_number }</div>
              <Button class="btn btn-outline btn-xs" disabled={list.value[list.value.length - 1].serial_number === curRow.value.serial_number || loading.value} onClick={async () => {
                const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
                const item = list.value[curIndex + 1]
                curRow.value = item
                url.value = curRow.value[`${currentSubtitleLang.value}_episode_path`]
                code.value = await getSubtitleContent(curRow.value[`${currentSubtitleLang.value}_subtitle_path`])
              }}
              >下一集
              </Button>
            </div>
          </div>
        </x-video-subtile-container>
      ),
    })
  }

  const checkSubtitle = async (data: M.ISubtitleCheck) => {
    if (hasChecked.get(`${data.language_code}_${data.serial_number}`)) {
      return
    }
    try {
      const res = await apiCommitSubtitleCheckLog(data)
      if (res.code === 200) {
        hasChecked.set(`${data.language_code}_${data.serial_number}`, false)
      }
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    }
  }

  const shortenAgain = (num: number, lang: string) => {
    const saveLoading = ref(false)
    const hideDialog = openDialog({
      title: '重新触发字幕缩短',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[400px]',
      body: () => (
        <x-inspection-confirm-dialog class="flex flex-col space-y-4">
          <x-inspection-content>
            确认重新触发字幕缩短
          </x-inspection-content>
          <x-inspection-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
              try {
                saveLoading.value = true
                await apiShortAISrt({
                  series_resource_id: +route.params.id,
                  serial_numbers: [num],
                  languages: [lang],
                  no_ai_horizontal: true,
                })
                showSuccessToast('操作成功')
                delayTaskGetList()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                saveLoading.value = false
                hideDialog()
              }
            }}
            >
              {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确认
            </button>
          </x-inspection-footer>
        </x-inspection-confirm-dialog>
      ),
    })
  }

  const previewSubtitle = (row: M.IResourceDrama, langIndex: number, errorList?: M.IResourceSubtitleError[]) => {
    const oIndex = langKey.findIndex(k => resourceDetail.value.upload_subtitle_language_code && resourceDetail.value.upload_subtitle_language_code[0] && k === resourceDetail.value.upload_subtitle_language_code[0])
    if (isFinalPage.value) {
      void router.push(`/resource-publish/final-subtitle/${+route.params.id}?number=${row.serial_number}&ref=${langKey[oIndex]}&language=${langKey[langIndex]}`)
    } else {
      void router.push(`/resource-publish/multiple-language-subtitle/${+route.params.id}?number=${row.serial_number}&ref=${langKey[oIndex]}&language=${langKey[langIndex]}`)
    }

    // const curRow = ref<M.IResourceDrama>(row)
    // const oIndex = langKey.findIndex(k => resourceDetail.value.upload_subtitle_language_code && resourceDetail.value.upload_subtitle_language_code[0] && k === resourceDetail.value.upload_subtitle_language_code[0])
    // // 右侧展示语言
    // const originalLangIndex = ref(langIndex)

    // const subtitleLang = langKeyForCol[oIndex] || 'cn'
    // const cnSubTitle = await getRefSubtitleContent(curRow.value, subtitleLang)
    // const content = await getOriginalSubtitleContent(curRow.value, langIndex)
    // const obj = subtitleLogs.value.find(r => r.serial_number === row.serial_number && r.language_code === langKey[langIndex]) || null
    // const oprObj = ref<M.IAlterSubtitle | null>(obj)
    // const videoUrl = ref(curRow.value.pure_path)
    // const videoType = ref<'pure_path' | 'origin_path'>('pure_path')
    // const errDetail = ref<M.IResourceSubtitleDetailError[]>([])
    // // 左侧对照永远选择 中文
    // const reference = ref({
    //   code: cnSubTitle,
    //   language: subtitleLang,
    // })
    // const original = ref({
    //   code: content,
    //   language: langKeyForCol[originalLangIndex.value],
    //   path: getSubtitlePathByIndex(curRow.value, originalLangIndex.value),
    // })

    // const loading = ref(false)
    // TODO: 注意
    // if (!isFinalPage.value) {
    //   void checkSubtitle({
    //     id: +route.params.id,
    //     type: 1,
    //     language_code: langKey[originalLangIndex.value],
    //     serial_number: row.serial_number,
    //   })
    // }

    // watch(() => [original.value.code], async () => {
    //   const isErrorSubtitle = errorList?.find(item => item.language_code === langKey[originalLangIndex.value] && item.serial_number === curRow.value.serial_number)
    //   if (isErrorSubtitle && langKey[originalLangIndex.value]) {
    //     const res = await apiGetSubtitleErrorsDetail({
    //       series_resource_id: +route.params.id,
    //       serial_number: curRow.value.serial_number,
    //       language_code: langKey[originalLangIndex.value],
    //     })
    //     errDetail.value = res.data?.errs || []
    //   } else {
    //     errDetail.value = []
    //   }
    // }, {
    //   immediate: true,
    // })

    // const createDialog = openDialog({
    //   title: () => (
    //     <div class="flex items-center space-x-4">
    //       <span>字幕校对</span>
    //       {errDetail.value && errDetail.value.length > 0 ? (
    //         <Tooltip popWrapperClass="z-popover-in-dialog" popContent={() => <pre class="whitespace-pre-wrap break-all">{errDetail.value.map(item => `第${item.line}行：${item.err_content}`).join('\n')}</pre>}>
    //           <div class="flex items-center space-x-1">
    //             <span class="text-xs text-[var(--text-2)]">异常</span>
    //             <SvgIcon class="size-3" name="ic_exclamation" />
    //           </div>
    //         </Tooltip>
    //       ) : null}
    //       {curRow.value.remark ? (
    //         <Tooltip placement="bottom-start" popWrapperClass="z-popover-in-dialog max-w-[400px]" popContent={() => (
    //           <pre class="whitespace-pre-wrap break-words">备注：{curRow.value.remark}</pre>
    //         )}>
    //           <div class="line-clamp-1 w-[400px] flex-1 text-sm text-[var(--text-2)]">
    //             备注：{curRow.value.remark}
    //           </div>
    //         </Tooltip>
    //       ) : null}
    //     </div>
    //   ),
    //   canEscClose: false,
    //   mainClass: 'px-4 !py-0',
    //   hideParentWhenChildOpen: true,
    //   customClass: '!w-[76vw] min-h-[520px]',
    //   body: () => (
    //     <>
    //       <SubtitleWithVideo
    //         errs={errDetail.value}
    //         loading={loading.value}
    //         reference={reference.value}
    //         original={original.value}
    //         videoUrl={videoUrl.value}
    //         videoType={videoType.value}
    //         pageNum={curRow.value.serial_number}
    //         onClose={() => createDialog()}
    //         defaultLanguage={resourceDetail.value?.upload_subtitle_language_code && resourceDetail.value?.upload_subtitle_language_code[0] ? resourceDetail.value?.upload_subtitle_language_code[0] === 'zh-CN' ? 'cn' : resourceDetail.value?.upload_subtitle_language_code[0] : '' }
    //         saveBtnDisabled={!(editableLanguage.value === original.value.language || editableLanguage.value === '')}
    //         onLanguageChange={async (lang: string) => {
    //           const code = await getRefSubtitleContent(curRow.value, lang)
    //           reference.value = {
    //             code: code,
    //             language: lang,
    //           }
    //         }}
    //         originalLanguageChangeAble={true}
    //         onOriginalLanguageChange={async (lang: string) => {
    //           const code = await getRefSubtitleContent(curRow.value, lang)
    //           originalLangIndex.value = langKeyForCol.findIndex(k => k === lang)
    //           original.value = {
    //             code: code,
    //             language: lang,
    //             path: getSubtitlePathByIndex(curRow.value, originalLangIndex.value),
    //           }
    //         }}
    //         onVideoChange={(type: 'pure_path' | 'origin_path') => {
    //           if (type === videoType.value) return
    //           videoType.value = type
    //           if (type === 'origin_path') {
    //             videoUrl.value = curRow.value.origin_path
    //           } else if (type === 'pure_path') {
    //             videoUrl.value = curRow.value.pure_path
    //           }
    //         }}
    //         onAfterSave={async ({ code, language }: { code: string, language: string }, isSync = false) => {
    //           const formatCode = formatSubtitles(code)
    //           loading.value = true

    //           try {
    //             const res = await apiSaveSubtitleContent({
    //               series_resource_id: +route.params.id,
    //               serial_number: curRow.value.serial_number,
    //               language_code: langKey[originalLangIndex.value],
    //               audio_type: 0,
    //               content: formatCode
    //             })
    //             const newSubtitlePath = res.data?.subtitle_uri || ''
    //             row[`${langKeyForCol[originalLangIndex.value]}_subtitle_path`] = newSubtitlePath
    //             original.value = {
    //               ...original.value,
    //               code: formatCode,
    //               path: newSubtitlePath,
    //             }

    //             if (isSync) {
    //               await apiSubtitleSync({
    //                 language_code: langKey[originalLangIndex.value],
    //                 serial_number: curRow.value.serial_number,
    //                 id: +route.params.id,
    //               })
    //             }

    //             void taskGetList()
    //             void getSubtitleErrors()
    //             showSuccessToast('保存成功')
    //           } catch (error: any) {
    //             createDialog()
    //             showFailToast(error.response.data.message || '操作失败')
    //           } finally {
    //             loading.value = false
    //           }
    //         }}
    //         onCloseDialog={() => {
    //           void taskGetList()
    //           createDialog()
    //         }}
    //         v-slots={{
    //           default: () => (
    //             <div class="flex items-center">
    //               <Button class="btn btn-outline btn-xs" disabled={curRow.value.serial_number === list.value[0].serial_number || loading.value} onClick={async () => {
    //                 loading.value = true
    //                 const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
    //                 const item = list.value[curIndex - 1]
    //                 curRow.value = item
    //                 const cnSubTitle = await getRefSubtitleContent(item, reference.value.language)
    //                 const content = await getOriginalSubtitleContent(item, originalLangIndex.value)
    //                 // 左侧对照永远选择 中文
    //                 reference.value = {
    //                   code: cnSubTitle,
    //                   language: reference.value.language,
    //                 }
    //                 original.value = {
    //                   ...original.value,
    //                   code: content,
    //                   path: getSubtitlePathByIndex(item, originalLangIndex.value),
    //                 }
    //                 videoUrl.value = item[videoType.value]
    //                 oprObj.value = subtitleLogs.value.find(r => r.serial_number === item.serial_number && r.language_code === langKey[originalLangIndex.value]) || null

    //                 loading.value = false
    //                 if (!isFinalPage.value) {
    //                   void checkSubtitle({
    //                     id: +route.params.id,
    //                     type: 1,
    //                     language_code: langKey[originalLangIndex.value],
    //                     serial_number: item.serial_number,
    //                   })
    //                 }
    //               }}
    //               >上一集
    //               </Button>
    //               <div class="px-4">{ curRow.value.serial_number }</div>
    //               <Button class="btn btn-outline btn-xs" disabled={list.value[list.value.length - 1].serial_number === curRow.value.serial_number || loading.value} onClick={async () => {
    //                 loading.value = true
    //                 const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
    //                 const item = list.value[curIndex + 1]
    //                 const cnSubTitle = await getRefSubtitleContent(item, reference.value.language)
    //                 const content = await getOriginalSubtitleContent(item, originalLangIndex.value)
    //                 // 左侧对照永远选择 中文
    //                 reference.value = {
    //                   code: cnSubTitle,
    //                   language: reference.value.language,
    //                 }
    //                 original.value = {
    //                   ...original.value,
    //                   code: content,
    //                   path: getSubtitlePathByIndex(item, originalLangIndex.value),
    //                 }
    //                 videoUrl.value = item[videoType.value]
    //                 oprObj.value = subtitleLogs.value.find(r => r.serial_number === item.serial_number && r.language_code === langKey[originalLangIndex.value]) || null
    //                 curRow.value = item
    //                 loading.value = false
    //                 if (!isFinalPage.value) {
    //                   void checkSubtitle({
    //                     id: +route.params.id,
    //                     type: 1,
    //                     language_code: langKey[originalLangIndex.value],
    //                     serial_number: item.serial_number,
    //                   })
    //                 }
    //               }}
    //               >下一集
    //               </Button>
    //               <div class="ml-4">
    //                 { oprObj.value ? `${oprObj.value.operate_user}:${dayjs(oprObj.value.operate_time * 1000).format('YYYY-MM-DD HH:mm')}` : null }
    //               </div>
    //             </div>
    //           ),
    //         }}
    //       />
    //     </>
    //   ),
    // })
  }

  const previewTerm = async (row: M.IResourceDrama, langIndex: number, errorList?: M.IResourceSubtitleError[]) => {
    const isTermCheck = true
    const curRow = ref<M.IResourceDrama>(row)
    const oIndex = langKey.findIndex(k => resourceDetail.value.upload_subtitle_language_code && resourceDetail.value.upload_subtitle_language_code[0] && k === resourceDetail.value.upload_subtitle_language_code[0])
    // 右侧展示语言
    const originalLangIndex = ref(oIndex)
    const subtitleLang = langKeyForCol[oIndex] || 'cn'
    const cnSubTitle = await getRefSubtitleContent(curRow.value, subtitleLang)
    const content = await getOriginalSubtitleContent(curRow.value, originalLangIndex.value)
    const obj = subtitleLogs.value.find(r => r.serial_number === row.serial_number && r.language_code === langKey[langIndex]) || null
    const oprObj = ref<M.IAlterSubtitle | null>(obj)
    const videoUrl = ref(curRow.value.pure_path)
    const videoType = ref<'pure_path' | 'origin_path'>('pure_path')
    const errDetail = ref<M.IResourceSubtitleDetailError[]>([])
    // 左侧对照永远选择 中文
    const reference = ref({
      code: cnSubTitle,
      language: subtitleLang,
    })
    const original = ref({
      code: content,
      language: langKeyForCol[originalLangIndex.value],
      path: getSubtitlePathByIndex(curRow.value, originalLangIndex.value),
    })

    const loading = ref(false)
    if (!isFinalPage.value) {
      void checkSubtitle({
        id: +route.params.id,
        type: 1,
        language_code: langKey[originalLangIndex.value],
        serial_number: row.serial_number,
      })
    }

    watch(() => [original.value.code], async () => {
      const isErrorSubtitle = errorList?.find(item => item.language_code === langKey[originalLangIndex.value] && item.serial_number === curRow.value.serial_number)
      if (isErrorSubtitle && langKey[originalLangIndex.value]) {
        const res = await apiGetSubtitleErrorsDetail({
          series_resource_id: +route.params.id,
          serial_number: curRow.value.serial_number,
          language_code: langKey[originalLangIndex.value],
        })
        errDetail.value = res.data?.errs || []
      } else {
        errDetail.value = []
      }
    }, {
      immediate: true,
    })

    const createDialog = openDialog({
      title: () => (
        <div class="flex items-center space-x-4">
          <span>术语校对</span>
          {errDetail.value && errDetail.value.length > 0 ? (
            <Tooltip popWrapperClass="z-popover-in-dialog" popContent={() => <pre class="whitespace-pre-wrap break-all">{errDetail.value.map(item => `第${item.line}行：${item.err_content}`).join('\n')}</pre>}>
              <div class="flex items-center space-x-1">
                <span class="text-xs text-[var(--text-2)]">异常</span>
                <SvgIcon class="size-3" name="ic_exclamation" />
              </div>
            </Tooltip>
          ) : null}
          {curRow.value.remark ? (
            <Tooltip placement="bottom-start" popWrapperClass="z-popover-in-dialog max-w-[400px]" popContent={() => (
              <pre class="whitespace-pre-wrap break-words">备注：{curRow.value.remark}</pre>
            )}>
              <div class="line-clamp-1 w-[400px] flex-1 text-sm text-[var(--text-2)]">
                备注：{curRow.value.remark}
              </div>
            </Tooltip>
          ) : null}
        </div>
      ),
      canEscClose: false,
      mainClass: 'px-4 !py-0',
      hideParentWhenChildOpen: true,
      customClass: `min-h-[520px] !w-[76vw] ${isTermCheck ? 'max-w-[1200px]' : '!w-[76vw]'}`,
      body: () => (
        <>
          <SubtitleWithVideo
            errs={errDetail.value}
            loading={loading.value}
            reference={reference.value}
            original={original.value}
            videoUrl={videoUrl.value}
            videoType={videoType.value}
            pageNum={curRow.value.serial_number}
            onClose={() => createDialog()}
            isTermCheck={isTermCheck}
            termLanguage={langKey[langIndex]}
            defaultLanguage={resourceDetail.value?.upload_subtitle_language_code && resourceDetail.value?.upload_subtitle_language_code[0] ? resourceDetail.value?.upload_subtitle_language_code[0] === 'zh-CN' ? 'cn' : resourceDetail.value?.upload_subtitle_language_code[0] : ''}
            saveBtnDisabled={!(editableLanguage.value === original.value.language || editableLanguage.value === '')}
            onLanguageChange={async (lang: string) => {
              const code = await getRefSubtitleContent(curRow.value, lang)
              reference.value = {
                code: code,
                language: lang,
              }
            }}
            originalLanguageChangeAble={true}
            onOriginalLanguageChange={async (lang: string) => {
              const code = await getRefSubtitleContent(curRow.value, lang)
              originalLangIndex.value = langKeyForCol.findIndex(k => k === lang)
              original.value = {
                code: code,
                language: lang,
                path: getSubtitlePathByIndex(curRow.value, originalLangIndex.value),
              }
            }}
            onVideoChange={(type: 'pure_path' | 'origin_path') => {
              if (type === videoType.value) return
              videoType.value = type
              if (type === 'origin_path') {
                videoUrl.value = curRow.value.origin_path
              } else if (type === 'pure_path') {
                videoUrl.value = curRow.value.pure_path
              }
            }}
            onCloseDialog={() => {
              void taskGetList()
              createDialog()
            }}
            v-slots={{
              default: () => (
                <div class="flex items-center">
                  <Button class="btn btn-outline btn-xs" disabled={curRow.value.serial_number === list.value[0].serial_number || loading.value} onClick={async () => {
                    loading.value = true
                    const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
                    const item = list.value[curIndex - 1]
                    curRow.value = item
                    const cnSubTitle = await getRefSubtitleContent(item, reference.value.language)
                    const content = await getOriginalSubtitleContent(item, originalLangIndex.value)
                    // 左侧对照永远选择 中文
                    reference.value = {
                      code: cnSubTitle,
                      language: reference.value.language,
                    }
                    original.value = {
                      ...original.value,
                      code: content,
                      path: getSubtitlePathByIndex(item, originalLangIndex.value),
                    }
                    videoUrl.value = item[videoType.value]
                    oprObj.value = subtitleLogs.value.find(r => r.serial_number === item.serial_number && r.language_code === langKey[originalLangIndex.value]) || null

                    loading.value = false
                    if (!isFinalPage.value) {
                      void checkSubtitle({
                        id: +route.params.id,
                        type: 1,
                        language_code: langKey[originalLangIndex.value],
                        serial_number: item.serial_number,
                      })
                    }
                  }}
                  >上一集
                  </Button>
                  <div class="px-4">{ curRow.value.serial_number }</div>
                  <Button class="btn btn-outline btn-xs" disabled={list.value[list.value.length - 1].serial_number === curRow.value.serial_number || loading.value} onClick={async () => {
                    loading.value = true
                    const curIndex = list.value.findIndex(item => item.serial_number === curRow.value.serial_number)
                    const item = list.value[curIndex + 1]
                    const cnSubTitle = await getRefSubtitleContent(item, reference.value.language)
                    const content = await getOriginalSubtitleContent(item, originalLangIndex.value)
                    // 左侧对照永远选择 中文
                    reference.value = {
                      code: cnSubTitle,
                      language: reference.value.language,
                    }
                    original.value = {
                      ...original.value,
                      code: content,
                      path: getSubtitlePathByIndex(item, originalLangIndex.value),
                    }
                    videoUrl.value = item[videoType.value]
                    oprObj.value = subtitleLogs.value.find(r => r.serial_number === item.serial_number && r.language_code === langKey[originalLangIndex.value]) || null
                    curRow.value = item
                    loading.value = false
                    if (!isFinalPage.value) {
                      void checkSubtitle({
                        id: +route.params.id,
                        type: 1,
                        language_code: langKey[originalLangIndex.value],
                        serial_number: item.serial_number,
                      })
                    }
                  }}
                  >下一集
                  </Button>
                  <div class="ml-4">
                    { oprObj.value ? `${oprObj.value.operate_user}:${dayjs(oprObj.value.operate_time * 1000).format('YYYY-MM-DD HH:mm')}` : null }
                  </div>
                </div>
              ),
            }}
          />
        </>
      ),
    })
  }

  const filterCols = ref<{ value: string, label: string }[]>(dynamicColumns.map(row => {
    return {
      value: row[0] as string,
      label: row[0] as string,
    }
  }))

  langKey.map((row, index) => {
    const videoLabel = `${langValue[index]}`
    selectCols.value.push(videoLabel)

    filterCols.value.push({
      value: videoLabel,
      label: videoLabel,
    })
  })

  const renderDynamicColumns = () => {
    dynamicColumns = []
    if (isFinalPage.value) {
      dynamicColumns.push([
        () => showColumnHeader('origin_path', '含字幕视频', false), (row: M.IResourceDrama) => {
          return row.origin_path
            ? (
                <Button class="btn btn-link btn-active btn-xs" onClick={() => { previewVideo(row.origin_path, row.remark) }}>
                  预览{row.origin_compress === 1 ? '(已压缩)' : null}
                </Button>
              )
            : '-'
        }, { class: 'w-[110px] text-center' }])
      dynamicColumns.push([() => showColumnHeader('pure_path', '无字幕视频', false, (e: number[]) => {
        checked.value = [...e]
      }), (row: M.IResourceDrama) => {
        return row.pure_path === PURE_ERROR
          ? <Button class="btn btn-outline btn-error btn-xs">抹除失败</Button>
          : row.pure_path
            ? row.pure_path === 'processing'
              ? <Button class="btn btn-outline btn-accent btn-xs">处理中</Button>
              : (
                  <Button class="btn btn-link btn-active btn-xs" onClick={() => { previewVideo(row.pure_path, row.remark) }}>
                    预览{ row.pure_compress === 1 ? '(已压缩)' : null}
                  </Button>
                )
            : '-'
      }, { class: 'w-[110px] text-center' }])

      dynamicColumns.push(['纯净资源', (row: M.IResourceDrama) => {
        return row.no_audio_path === PURE_ERROR
          ? <Button class="btn btn-outline btn-error btn-xs">抹除失败</Button>
          : row.no_audio_path
            ? row.no_audio_path === 'processing'
              ? <Button class="btn btn-outline btn-accent btn-xs">处理中</Button>
              : (
                  <div class="flex flex-col items-center justify-center">
                    <Button class="btn btn-link btn-active btn-xs"
                      onClick={() => { previewVideo(row.no_audio_path) }}
                    >
                      纯视频
                    </Button>
                  </div>
                )
            : '-'
      }, { class: 'w-[140px] text-center' }])

      dynamicColumns.push(['转码视频', (row: M.IResourceDrama) => {
        const serial_number = row.serial_number
        const obj = externalTransProgress.value.find(item => item.serial_number === serial_number)
        let subtitleBtn = null
        if (!obj || obj.audio_external_status === 0) {
          subtitleBtn = '-'
        } else if (obj.audio_external_status === 1) {
          subtitleBtn = <Button class="btn btn-outline btn-accent btn-xs">处理中</Button>
        } else if (obj.audio_external_status === 3) {
          subtitleBtn = <Button class="btn btn-outline btn-error btn-xs">外挂失败</Button>
        } else if (obj.sync_status !== 1) {
          subtitleBtn = <Button class="btn btn-outline btn-error btn-xs" onClick={() => { void previewM3U8(row) }}>视频(未同步)</Button>
        } else {
          subtitleBtn = <Button class="btn btn-link btn-active btn-xs" onClick={() => { void previewM3U8(row) }}>视频</Button>
        }
        return subtitleBtn
      }, { class: 'w-[100px] text-center' }])
    }

    langKey.map((key, index) => {
      let w = 'w-[120px]'
      if (langValue[index].length + 2 > 7) {
        w = 'w-[140px]'
      } else if (langValue[index].length + 2 > 5) {
        w = 'w-[130px]'
      } else if (langValue[index].length + 2 > 4) {
        w = 'w-[125px]'
      }
      if (selectCols.value.length > 0 && selectCols.value.includes(`${langValue[index]}`)) {
        const transButton = (language_code: string) => {
          const transLoading = ref(false)
          const hideDeleteDialog = openDialog({
            title: '提示',
            mainClass: 'pb-0 px-5',
            body: () => (
              <x-translate-subtitle-confirm-dialog class="flex flex-col gap-y-[25px]">
                <x-translate-subtitle-body>确认翻译当前语言字幕</x-translate-subtitle-body>
                <x-translate-subtitle-footer class="flex w-full justify-end gap-x-[10px]">
                  <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                  <Button class="btn btn-primary btn-sm" disabled={transLoading.value} onClick={async () => {
                    const lang = resourceDetail.value?.upload_subtitle_language_code && resourceDetail.value?.upload_subtitle_language_code[0]
                    try {
                      transLoading.value = true
                      // 默认翻译成英语
                      await apiSubtitleTrans({
                        series_resource_id: +route.params.id,
                        serial_numbers: list.value.map(item => item.serial_number),
                        src_lang: lang!,
                        target_lang_list: [language_code],
                      })
                      showSuccessToast('操作成功')
                      delayTaskGetList()
                      hideDeleteDialog()
                    } catch (error: any) {
                      showFailToast(error.response.data.err_msg || '操作失败')
                    } finally {
                      transLoading.value = false
                    }
                  }}
                  >
                    {transLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    确定
                  </Button>
                </x-translate-subtitle-footer>
              </x-translate-subtitle-confirm-dialog>
            ),
          })
        }
        dynamicColumns.push([
          () => {
            const hasSomeSubtitle = list.value.some(item => {
              return item[`${langKeyForCol[index]}_subtitle_path`] && item[`${langKeyForCol[index]}_subtitle_path`].indexOf('http') === 0
            })
            const termStatusObj = termsList.value.find(termStatus => termStatus.language_code === langKey[index])
            const showTranslateBtn = ((termStatusObj && termStatusObj.status === 2) || langKey[index] === 'zh-TW') && (!hasSomeSubtitle || getUserRoles().includes(16))
            return showColumnHeader(langKeyForCol[index], `${langValue[index]}`, true, (e: number[]) => {
              checked.value = [...e]
            }, showTranslateBtn ? transButton : undefined)
          },
          (row: M.IResourceDrama) => {
            let subtitleBtn = null
            let audioBtn = null
            let videoButton = null
            let termButton = null
            // 第一行展示术语
            const isFirstLine = row.serial_number === list.value[0].serial_number
            const termStatusObj = termsList.value.find(termStatus => termStatus.language_code === langKey[index])
            const btnLoading = ref(false)
            const videoPath = row[`${langKeyForCol[index]}_episode_path`]
            const audioPath = row[`${langKeyForCol[index]}_audio_path`]
            const isVoiceOver = audioPath?.startsWith('http')
            if (videoPath === PROCESSING) {
              videoButton = <Button class="btn btn-outline btn-accent btn-xs">处理中</Button>
            } else {
              videoButton = videoPath
                ? (
                    <>
                      {
                        videoPath === PURE_ERROR
                          ? <Button class="btn btn-outline btn-error btn-xs">合成失败</Button>
                          : <Button class="btn btn-link btn-active btn-xs" onClick={() => { void previewM3U8WithoutSubtitle(row, index) }}>{isVoiceOver ? '视频-配' : '视频'}</Button>
                      }
                      { isVoiceOver ? null : (
                        <div class="tooltip" data-tip="重新压制视频">
                          <SvgIcon class="cursor-pointer" name="ic_refresh" onClick={() => {
                            const hideDialog = openDialog({
                              title: '提示',
                              mainClass: 'pb-0 px-5',
                              body: () => (
                                <x-video-confirm-dialog class="flex flex-col gap-y-[25px]">
                                  <x-video-body>确认重新压制</x-video-body>
                                  <x-video-footer class="flex w-full justify-end gap-x-[10px]">
                                    <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                    <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                                      try {
                                        btnLoading.value = true
                                        await apiSubtitleInject({
                                          series_resource_id: +route.params.id,
                                          serial_numbers: [row.serial_number],
                                          region: defaultClipValue,
                                          src_lang_list: [langKey[index]],
                                          inject_type: 0,
                                        })
                                        delayTaskGetList()
                                        showSuccessToast('操作成功')
                                        hideDialog()
                                      } catch (error: any) {
                                        showFailToast(error.response.data.message || '操作失败')
                                      } finally {
                                        btnLoading.value = false
                                      }
                                    }}
                                    >确定
                                    </button>
                                  </x-video-footer>
                                </x-video-confirm-dialog>
                              ),
                            })
                          }}
                          />
                        </div>
                      )}
                    </>
                  )
                : '-'
            }
            const subtitlePath = row[`${langKeyForCol[index]}_subtitle_path`]
            const obj = subtitleLogs.value.find(r => r.serial_number === row.serial_number && r.language_code === langKey[index])
            // const shortTask = shortAiTasks.value.find(item => item.serial_number === num && item.language === key)
            const shortenTask = row[`${langKey[index]}_shorten_tasks`]

            if (subtitlePath === PROCESSING) {
              subtitleBtn = <Button class="btn btn-outline btn-accent btn-xs">处理中</Button>
            } else if (subtitlePath === TRANS_ERROR) {
              subtitleBtn = (
                <>
                  <Button class="btn btn-link btn-xs text-red-500" onClick={() => { void previewSubtitle(row, index) }}>翻译错误</Button>
                  <div class="tooltip" data-tip="重试">
                    <SvgIcon class="cursor-pointer" name="ic_refresh" onClick={async () => {
                      try {
                        await apiRetryTrans({
                          series_resource_id: +route.params.id,
                          serial_number: row.serial_number,
                          language_code: langKey[index],
                          trans_type: 1,
                        })
                        delayTaskGetList()
                        showSuccessToast('操作成功')
                      } catch (error: any) {
                        showFailToast(error.response.data.message || '操作失败')
                      }
                      delayTaskGetList()
                    }}
                    />
                  </div>
                </>
              )
            } else if (subtitlePath === EXTRACT_ERROR || subtitlePath === HISTORY_EXTRACT_ERROR) {
              subtitleBtn = <Button class="btn btn-link btn-xs text-red-500">提取错误</Button>
            } else if (isVoiceOver) {
              subtitleBtn = (
                <div class="flex justify-center">
                  <Button class="btn btn-link btn-xs btn-active" onClick={() => { void previewSubtitle(row, index, subtitleErrorList.value) }}>
                    字幕-配
                  </Button>
                  {shortenTask && shortenTask.status >= 0 ? (
                    <span class="text-xs text-red-500 flex items-center gap-0">
                      (缩
                      {
                        shortenTask?.status == 2 ? <el-icon class="!text-green-500"><SuccessFilled /></el-icon> : ''
                      }

                      {
                        shortenTask?.status == 3 ? (
                          <ElTooltip content={shortenTask?.err_msg} placement="top">
                            <el-icon class="text-red-500 cursor-pointer" onClick={() => shortenAgain(row.serial_number, langKey[index])}><CircleCloseFilled /></el-icon>
                          </ElTooltip>
                        ) : ''
                      }
                      )
                    </span>
                  ) : null}
                </div>
              )
            } else {
              let _lang = langKeyForCol[index]
              if (_lang === 'cn') _lang = 'zh-CN'
              const isErrorSubtitle = subtitleErrorList.value.find(item => item.language_code === _lang && item.serial_number === row.serial_number)
              subtitleBtn = subtitlePath
                ? (
                    <div class="flex justify-center">
                      <Button class={`btn btn-link btn-xs ${isErrorSubtitle ? 'text-red-500' : 'btn-active'}`} onClick={() => { void previewSubtitle(row, index, subtitleErrorList.value) }}>
                        { obj && obj.operate_user ? '字幕-改' : '字幕'}
                      </Button>
                      {shortenTask && shortenTask.status >= 0 ? (
                        <span class="text-xs text-red-500 flex items-center gap-0">
                          (缩
                          {
                            shortenTask?.status == 2 ? <el-icon class="!text-green-500"><SuccessFilled /></el-icon> : ''
                          }

                          {
                            shortenTask?.status == 3 ? (
                              <ElTooltip content={shortenTask?.err_msg} placement="top">
                                <el-icon class="text-red-500 cursor-pointer" onClick={() => shortenAgain(row.serial_number, langKey[index])}><CircleCloseFilled /></el-icon>
                              </ElTooltip>
                            ) : ''
                          }
                          )
                        </span>
                      ) : null}
                    </div>
                  )
                : '-'
            }

            if (audioPath === PROCESSING) {
              audioBtn = <Button class="btn btn-outline btn-accent btn-xs">处理中</Button>
            } else if (audioPath === TRANS_ERROR) {
              audioBtn = (
                <>
                  <Button class="btn btn-link btn-xs text-red-500" onClick={() => { void previewSubtitle(row, index) }}>翻译错误</Button>
                  <div class="tooltip" data-tip="重试">
                    <SvgIcon class="cursor-pointer" name="ic_refresh" onClick={async () => {
                      try {
                        await apiRetryTrans({
                          series_resource_id: +route.params.id,
                          serial_number: row.serial_number,
                          language_code: langKey[index],
                          trans_type: 2,
                        })
                        delayTaskGetList()
                        showSuccessToast('操作成功')
                      } catch (error: any) {
                        showFailToast(error.response.data.message || '操作失败')
                      }
                    }}
                    />
                  </div>
                </>
              )
            } else if (audioPath === EXTRACT_ERROR || audioPath === HISTORY_EXTRACT_ERROR) {
              audioBtn = <Button class="btn btn-link btn-xs text-red-500">提取错误</Button>
            } else {
              // 选择无字幕、无音频的视频 + 对应音频
              audioBtn = audioPath ? <Button class="btn btn-link btn-active btn-xs" onClick={() => { void previewAudio(audioPath) }}>音频</Button> : '-'
            }

            const curTerm = TERM_STATUS_LIST.find(t => t.value === termStatusObj?.status)
            if (curTerm) {
              if (curTerm.value === 2) {
                termButton = (
                  <Button class="btn btn-link btn-xs" onClick={() => {
                    void previewTerm(row, index)
                  }}>术语查看</Button>
                )
              } else {
                termButton = <div class={`badge badge-outline badge-sm badge-${curTerm.color}`}>{curTerm.label}</div>
              }
            } else {
              termButton = <div>-</div>
            }

            return (
              <div>
                <div>{subtitleBtn}</div>
                <div>{videoButton}</div>
                <div>{audioBtn}</div>
                { isFirstLine ? (
                  <div>
                    { termButton } { [3, 4].includes(curTerm?.value || -1) ? (
                      <div class="tooltip" data-tip="重新翻译术语">
                        <SvgIcon class="cursor-pointer" name="ic_refresh" onClick={() => {
                          const hideDialog = openDialog({
                            title: '提示',
                            mainClass: 'pb-0 px-5',
                            body: () => (
                              <x-video-confirm-dialog class="flex flex-col gap-y-[25px]">
                                <x-video-body>重新翻译术语</x-video-body>
                                <x-video-footer class="flex w-full justify-end gap-x-[10px]">
                                  <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                  <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                                    try {
                                      btnLoading.value = true
                                      await apiTransLanguageTerms({
                                        series_resource_id: +route.params.id,
                                        langs: [langKey[index]],
                                      })
                                      delayTaskGetList()
                                      showSuccessToast('操作成功')
                                      hideDialog()
                                    } catch (error: any) {
                                      showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                                    } finally {
                                      btnLoading.value = false
                                    }
                                  }}
                                  >确定
                                  </button>
                                </x-video-footer>
                              </x-video-confirm-dialog>
                            ),
                          })
                        }}
                        />
                      </div>
                    ) : null }
                  </div>
                ) : null }
              </div>
            )
          },
          { class: `${w} text-center` },
        ])
      }
    })

    columns.value = [
      [
        () => (
          <div class="z-10 flex items-center justify-center">
            <Checkbox
              label=""
              disabled={list.value?.length === 0}
              modelValue={isCheckAll.value}
              onUpdate:modelValue={(value: boolean) => {
                checkedAllChange(value, list.value)
              }}
            />
            <div>集数</div>
          </div>
        ),
        (row: M.IResourceDrama) => {
          return (
            <label class="flex cursor-pointer items-center justify-center">
              <Checkbox
                label=""
                modelValue={checked.value.includes(row.serial_number)}
                onUpdate:modelValue={(value: boolean) => {
                  const serial_number = row.serial_number
                  checkboxChange(value, serial_number, list.value)
                }}
              />
              <div>{row.serial_number}</div>
            </label>
          )
        },
        { class: 'w-[95px] text-center' },
      ],
      ...dynamicColumns,
    ]
  }

  renderDynamicColumns()

  const makeList = (_list: M.IResourceDrama[]) => {
    // 如果没有数据拿不到 无法补充数组
    if (_list.length === 0) {
      list.value = _list
      return
    }
    const missingRowNumber = findMissingNumbers(resourceDetail.value.count || 0, _list)
    const result = makeUpList(missingRowNumber, _list[0])
    list.value = _list.concat(result as M.IResourceDrama[]).sort((a, b) => a.serial_number - b.serial_number)
    setMissingData(_list, resourceDetail.value.count || 0, total.value)
  }

  const getTerms = async () => {
    const res = await apiGetTermsStatusList({
      series_resource_id: +route.params.id,
    })
    termsList.value = res.data?.list || []
  }

  const getList = async () => {
    loading.value = true
    try {
      await getSubtitleRecordList()
      await getTerms()
      const res = await apiSeriesResourceList(queryForm.value)
      const _list = res?.data?.list || []
      total.value = res?.data?.total || 0
      loading.value = false
      makeList(_list)
    } catch (error) {
      loading.value = false
    }
  }

  const onPassInFinal = () => {
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-title class="text-[var(--text-2)]">确认通过</x-title>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              btnLoading.value = true
              try {
                await apiChangeAudit({
                  id: +route.params.id,
                  page_type: 4,
                  ope_type: 2,
                })
                showSuccessToast('操作成功')
                void getResourceDetail()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                btnLoading.value = false
                hideDialog()
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const onRejectInFinal = () => {
    const files = ref<M.UploadUserFileWithPath[]>([])
    const RejectForm = CreateForm<{
      reject_reason: string
      type?: number
    }>()
    const rejectForm = ref<{
      reject_reason: string
      type?: number
    }>({
      reject_reason: '',
      type: 0,
    })
    const formRules = z.object({
      reject_reason: z.string().min(1, {
        message: '请输入驳回原因',
      }),
      type: z.number().min(1, {
        message: '请选择错误类型',
      }),
    })
    const { error, validateAll } = useValidator(rejectForm, formRules)

    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-text-area>
            <RejectForm
              class="m-auto grid w-full grid-cols-3"
              hasAction={false}
              error={error.value}
              data={rejectForm.value}
              onChange={(path, value: any) => {
                set(rejectForm.value, path, value)
              }}
              items={[
                [
                  requiredLabel('请选择错误类型：'),
                  'type',
                  {
                    type: 'select',
                    options: rejectReason,
                    placeholder: '请选择错误类型',
                    autoInsertEmptyOption: false,
                  },
                  {
                    class: 'col-span-3',
                    transform: transformNumber,
                  },
                ],
                [
                  requiredLabel('备注：'),
                  'audit_failed_reason',
                  {
                    type: 'custom',
                    render: () => (
                      <textarea
                        class="textarea textarea-bordered textarea-sm w-full"
                        placeholder="请输入审核不通过原因"
                        onPaste={(e: ClipboardEvent) => {
                          const data = e.clipboardData
                          if (!data) return
                          // 检查是否包含图片
                          const hasImage = Array.from(data.items).some(item => item.type.startsWith('image/'))
                          if (hasImage) {
                            // 如果是图片则阻止默认粘贴并上传
                            e.preventDefault()
                            handleParse(e)
                          }
                          // 如果是文本则使用默认粘贴行为
                        }}
                        onInput={(e: Event) => {
                          rejectForm.value.reject_reason = (e.target as HTMLTextAreaElement).value
                        }}
                      />
                    ),
                  },
                  {
                    class: 'col-span-3',
                  },
                ],
                [
                  '图片：',
                  'reject_image_list',
                  {
                    type: 'custom',
                    render: () => (
                      <FileUploader
                        ref={fileUploaderRef}
                        onSuccess={({ file }) => {
                          files.value.push(file)
                        }}
                        onRemove={({ file }) => {
                          const fileIndex = files.value.findIndex((item: M.UploadUserFileWithPath) => item?.uid === file?.uid)
                          files.value.splice(fileIndex, 1)
                        }} />
                    ),
                  },
                  {
                    class: 'col-span-3',
                  },
                ],
              ]}
            />
          </x-text-area>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              if (!validateAll()) return
              btnLoading.value = true
              try {
                const params: M.IAuditParams = {
                  id: +route.params.id,
                  page_type: 4,
                  reject_to_page: rejectForm.value.type,
                  ope_type: 1,
                  reject_reason: rejectForm.value.reject_reason,
                  reject_image_list: files.value.map((item: M.UploadUserFileWithPath) => item.raw.path),
                }
                if (rejectForm.value.type === 3) {
                  params['language_codes'] = ['en']
                }
                await apiChangeAudit(params)
                showSuccessToast('操作成功')
                void getResourceDetail()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                btnLoading.value = false
                hideDialog()
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const onReject = () => {
    const files = ref<M.UploadUserFileWithPath[]>([])
    const RejectForm = CreateForm<{
      reject_reason: string
    }>()
    const rejectForm = ref<{
      reject_reason: string
    }>({
      reject_reason: '',
    })
    const formRules = z.object({
      reject_reason: z.string().min(1, {
        message: '请输入驳回原因',
      }),
    })
    const { error, validateAll } = useValidator(rejectForm, formRules)

    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-text-area>
            <RejectForm
              class="m-auto grid w-full grid-cols-3"
              hasAction={false}
              error={error.value}
              data={rejectForm.value}
              onChange={(path, value: any) => {
                set(rejectForm.value, path, value)
              }}
              items={[
                [
                  requiredLabel('备注：'),
                  'audit_failed_reason',
                  {
                    type: 'custom',
                    render: () => (
                      <textarea
                        class="textarea textarea-bordered textarea-sm w-full"
                        placeholder="请输入审核不通过原因"
                        onPaste={(e: ClipboardEvent) => {
                          const data = e.clipboardData
                          if (!data) return
                          // 检查是否包含图片
                          const hasImage = Array.from(data.items).some(item => item.type.startsWith('image/'))
                          if (hasImage) {
                            // 如果是图片则阻止默认粘贴并上传
                            e.preventDefault()
                            handleParse(e)
                          }
                          // 如果是文本则使用默认粘贴行为
                        }}
                        onInput={(e: Event) => {
                          rejectForm.value.reject_reason = (e.target as HTMLTextAreaElement).value
                        }}
                      />
                    ),
                  },
                  {
                    class: 'col-span-3',
                  },
                ],
                [
                  '图片：',
                  'reject_image_list',
                  {
                    type: 'custom',
                    render: () => (
                      <FileUploader
                        ref={fileUploaderRef}
                        onSuccess={({ file }) => {
                          files.value.push(file)
                        }}
                        onRemove={({ file }) => {
                          const fileIndex = files.value.findIndex((item: M.UploadUserFileWithPath) => item?.uid === file?.uid)
                          files.value.splice(fileIndex, 1)
                        }} />
                    ),
                  },
                  {
                    class: 'col-span-3',
                  },
                ],
              ]}
            />
          </x-text-area>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              if (!validateAll()) return
              btnLoading.value = true
              try {
                await apiChangeAudit({
                  id: +route.params.id,
                  page_type: 3,
                  reject_to_page: 1,
                  ope_type: 1,
                  reject_reason: rejectForm.value.reject_reason,
                  reject_image_list: files.value.map((item: M.UploadUserFileWithPath) => item.raw.path),
                })
                showSuccessToast('操作成功')
                void getResourceDetail()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                btnLoading.value = false
                hideDialog()
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const onPass = () => {
    const PassForm = CreateForm<{
      language_codes: string[]
    }>()
    const passForm = ref<{
      language_codes: string[]
    }>({
      language_codes: [],
    })
    const formRules = z.object({
      language_codes: z.array(z.string()).min(1, '请选择完成校验的语言'),
    })
    const { error, validateAll } = useValidator(passForm, formRules)

    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-title class="text-[var(--text-2)]">请选择语言，并确认已完成该语言下全部剧集的校验与修改</x-title>
          <x-text-area>
            <PassForm
              class="m-auto grid w-full grid-cols-3"
              hasAction={false}
              error={error.value}
              data={passForm.value}
              onChange={(path, value: any) => {
                set(passForm.value, path, value)
              }}
              items={[
                [
                  requiredLabel('字幕语言:'),
                  'language_codes',
                  {
                    type: 'multi-select',
                    placeholder: '请选择通过语言',
                    search: true,
                    maxlength: 1,
                    popoverWrapperClass: 'z-popover-in-dialog',
                    options: langKey.map((key, index) => {
                      return {
                        value: key,
                        label: langValue[index],
                      }
                    }),
                  },
                  {
                    class: 'col-span-3 z-dialog',
                  },
                ],
              ]}
            />
          </x-text-area>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              if (!validateAll()) return
              btnLoading.value = true
              try {
                await apiChangeAudit({
                  id: +route.params.id,
                  page_type: 3,
                  ope_type: 2,
                  language_codes: passForm.value.language_codes,
                })
                showSuccessToast('操作成功')
                void getResourceDetail()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                btnLoading.value = false
                hideDialog()
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const getSubtitleRecordList = async () => {
    const res = await apiGetRecordList({
      series_resource_id: +route.params.id,
    })
    subtitleLogs.value = res.data?.list || []
  }

  const taskGetList = async () => {
    await getSubtitleRecordList()
    await getTranscodeVideos(+route.params.id)
    const res = await apiSeriesResourceList(queryForm.value)
    makeList(res?.data?.list || [])
    void getSubtitleErrors()
    total.value = res?.data?.total || 0
  }

  const delayTaskGetList = () => {
    setTimeout(async () => {
      const res = await apiSeriesResourceList(queryForm.value)
      makeList(res?.data?.list || [])
      total.value = res?.data?.total || 0
    }, 2000)
  }

  const getResourceDetail = async () => {
    const res = await apiGetResourcePublishDetail({
      id: +route.params.id,
    })
    resourceDetail.value = res?.data || {
      count: 0,
      title: '',
      unlocked_episodes: 0,
      serialize_status: 0,
      audit_process: [],
    }
  }

  const getSubtitleErrors = async () => {
    const res = await apiGetSubtitleErrors({
      series_resource_id: +route.params.id,
    })
    subtitleErrorList.value = res.data?.list || []
  }

  const getSubtitleEditable = async () => {
    const res = await apiGetSubtitleEditable()
    editableLanguage.value = res.data?.lang_code || ''
  }

  onMounted(async () => {
    await getTranscodeVideos(+route.params.id)
    await getResourceDetail()
    await getList()
    void getSubtitleErrors()
    void refreshSyncList(+route.params.id)
    void getSubtitleEditable()
    resetChecked()
  })
  let interval = null
  interval = setInterval(() => {
    void taskGetList()
  }, 60 * 1000)
  onUnmounted(() => {
    clearInterval(interval)
  })

  return () => (
    <NavFormTablePager stickyHeader={false}>{{
      nav: () => (
        <section class="breadcrumbs text-sm">
          <ul>
            <li>
              <RouterLink to={
                isFinalPage.value ? '/resource-publish/publish' : '/resource-publish/multi-subtitle'
              }
              >{isFinalPage.value ? '终审发布' : '多语言字幕校验'}
              </RouterLink>
            </li>
            <li>资源详情</li>
          </ul>
        </section>
      ),
      tableActions: () => (
        <div>
          <div class="flex flex-col space-y-2">
            <div class="flex items-center space-x-4 justify-between">
              <div class="flex items-center space-x-4">
                <div>
                  资源名称：{resourceDetail.value.title}
                </div>
                <div>
                  总集数: {resourceDetail.value.count}
                </div>
                {subtitleErrorList.value.length > 0
                  ? (
                      <div class="flex">
                        字幕异常数量：
                        <Tooltip popContent={() =>
                          subtitleErrorList.value.map(err => <div>第{err.serial_number}集，{langValue[langKey.findIndex(k => k === err.language_code)]}字幕, 错误个数: {err.err_count}</div>)}
                        >
                          <div class="flex cursor-pointer items-center space-x-1 text-red-500">
                            <span>{ subtitleErrorList.value.length }</span>
                            <SvgIcon class="size-3.5" name="ic_exclamation" />
                          </div>
                        </Tooltip>
                      </div>
                    )
                  : null }
              </div>
              <div class="dropdown dropdown-bottom dropdown-end">
                <div tabindex={0} role="button" class="btn btn-xs btn-primary">
                  <SvgIcon class="size-3.5" name="ic_filter" />
                </div>
                <ul tabindex={0} class="dropdown-content menu bg-base-100 rounded-box z-1 w-52 p-2 shadow-sm">
                  <ColFilter columns={filterCols.value} selectCols={selectCols.value} onChange={e => {
                    selectCols.value = e
                    renderDynamicColumns()
                  }} />
                </ul>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <ShowStatusDesc class="!mb-0 !pb-0" items={resourceDetail.value.audit_process || []} pageType={isFinalPage.value ? 4 : 3} />
              <div class="flex justify-end">
                {getUserRoles().includes(40) ? (
                  <Button {...(checked.value.length === 0 ? { disabled: true } : {})} class="btn btn-primary btn-xs" onClick={() => {
                    const language_code_list = ref<string[]>([])
                    const commitLoading = ref(false)
                    const hideDeleteDialog = openDialog({
                      title: '提示',
                      mainClass: 'pb-0 px-5',
                      customClass: '!w-[400px]',
                      body: () => (
                        <x-async-subtitle-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                            <div class="flex items-center gap-x-2">
                              {requiredLabel('选中集数:')}
                              <div class="break-words">{checked.value.join(',')}</div>
                            </div>
                            <div class="flex items-center gap-x-2">
                              {requiredLabel('语言:')}
                              <FormMultiSelect
                                search={true}
                                class="w-[200px]"
                                popoverWrapperClass="z-popover-in-dialog"
                                options={langKey.map((n, index) => {
                                  return { value: langKey[index], label: langValue[index] }
                                })}
                                modelValue={language_code_list.value}
                                onUpdate:modelValue={e => {
                                  language_code_list.value = e as string[]
                                }}
                              />
                              <label class="flex items-center ml-2">
                                <span class="text-xs text-[var(--text-2)]">全选</span>
                                <Checkbox
                                  label=""
                                  modelValue={language_code_list.value.length === langKey.length}
                                  onUpdate:modelValue={(value: boolean) => {
                                    if (value) {
                                      language_code_list.value = [...langKey]
                                    } else {
                                      language_code_list.value = []
                                    }
                                  }}
                                />
                              </label>
                            </div>
                          </x-status-confirm-dialog>
                          <x-async-subtitle-footer class="flex w-full justify-end gap-x-[10px]">
                            <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                            <button class="btn btn-primary btn-sm" disabled={commitLoading.value} onClick={() => {
                              if (language_code_list.value.length === 0) {
                                showFailToast('请选择语言')
                                return
                              }
                              commitLoading.value = true
                              void apiSubtitleSyncVoice({
                                series_resource_id: +route.params.id,
                                serial_number_list: checked.value,
                                language_code_list: language_code_list.value,
                              }).then(() => {
                                showSuccessToast('操作成功')
                                delayTaskGetList()
                              }).catch((error: any) => {
                                showFailToast(error.response.data.message || error.response.data.err_msg || '操作失败')
                              }).finally(() => {
                                commitLoading.value = false
                              })
                            }}
                            >
                              {commitLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-3" /> : null}
                              确定
                            </button>
                          </x-async-subtitle-footer>
                        </x-async-subtitle-confirm-dialog>
                      ),
                    })
                  }}>同步配音</Button>
                ) : null}
              </div>
            </div>
          </div>
        </div>
      ),
      table: () => (
        <>
          <Table
            list={list.value || []}
            class="tm-table-fix-header !max-h-[calc(100vh-340px)] w-full overflow-auto"
            columns={columns.value}
            loading={loading.value}
          />
          { resourceDetail.value.id
            ? (
                !isRejectStatus.value || hasPassButton.value || !resourceDetail.value.audit_all_subtitle_process
                  ? (
                      <div class="fixed bottom-0 right-8 z-[5] w-[calc(100vw-240px)] space-x-2 bg-white py-4 pr-4 text-right">
                        {
                          !isFinalPage.value && !isRejectStatus.value
                            ? (
                                <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={onReject}>
                                  {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-3" /> : null}
                                  驳回
                                </Button>

                              )
                            : null
                        }
                        {
                          !isFinalPage.value && !resourceDetail.value.audit_all_subtitle_process
                            ? (
                                <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={onPass}>
                                  {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-3" /> : null}
                                  完成校验
                                </Button>
                              )
                            : null
                        }
                        {
                          isFinalPage.value && !isRejectStatus.value
                            ? (
                                <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={onRejectInFinal}>
                                  {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-3" /> : null}
                                  驳回
                                </Button>
                              )
                            : null
                        }
                        {
                          isFinalPage.value && hasPassButton.value
                            ? (
                                <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={onPassInFinal}>
                                  {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-3" /> : null}
                                  通过
                                </Button>
                              )
                            : null
                        }
                      </div>
                    )
                  : null
              )
            : null }
        </>
      ),
    }}
    </NavFormTablePager>
  )
})

export default ResourceDetailPage
