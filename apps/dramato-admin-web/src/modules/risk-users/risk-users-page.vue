<template>
  <div>
    <h5 class="mb-4 pt-2">风控用户查询</h5>
    <div class="my-4 p-4 bg-white rounded-lg shadow">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="用户ID" class="w-[250px]">
          <el-input v-model="searchForm.user_id" placeholder="用户ID" />
        </el-form-item>
        <el-form-item label="设备ID" class="w-[250px]">
          <el-input v-model="searchForm.device_id" placeholder="设备ID" />
        </el-form-item>



        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="push-task-table p-4 bg-white rounded-lg shadow mb-4">
      <el-table :data="list" style="width: 100%" border v-loading="loading">
        <!-- <el-table-column prop="id" label="ID" width="80" /> -->
        <el-table-column prop="user_id" label="用户ID" width="150" />
        <el-table-column prop="uid" label="UID" width="150" />
        <el-table-column prop="control_type" label="封禁类型" width="100">
          <template #default="scope">
            <div>{{ ['无', '临时', '永久'][scope.row.control_type] }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始时间" width="200">
          <template #default="scope">
            <div>{{ dayjs(scope.row.start_time*1000).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="end_time" label="计划结束时间" width="200">
          <template #default="scope">
            <div v-if="scope.row.control_type == 2" style="font-size: 32px;">∞</div>
            <div v-else>{{ dayjs(scope.row.end_time*1000).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="200">
          <template #default="scope">
            <div><span>{{ ['封禁中', '已解除', '已解除'][scope.row.release_status] }}</span> <span v-if="scope.row.release_status > 0">- {{ scope.row.release_status == 2 ? '自动' : scope.row.release_user_id }} - {{ dayjs(scope.row.release_time*1000).format('YYYY-MM-DD HH:mm:ss') }}</span></div>
          </template>
        </el-table-column>
        <el-table-column prop="device_id" label="涉及设备ID" width="300" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <div class="flex gap-2">
              <el-button type="primary" size="mini"
                @click="handleEdit(scope.row)" :disabled="([1,2].includes(scope.row.release_status) || scope.row.control_type != 2)">解封</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container my-2 flex justify-end">
        <el-pagination v-model:current-page="searchForm.page_index" v-model:page-size="searchForm.page_size"
          :total="total" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { Plus, Delete, Download, InfoFilled } from '@element-plus/icons-vue'
import { useRiskUsers } from './use-risk-users';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute, useRouter } from 'vue-router'

const {
  config,
  saveAudit,
  onSearchRiskUsersList,
  list,
  loading,
  searchForm,
  total,
  onReset
} = useRiskUsers()


const route = useRoute()
const router = useRouter()

const dialogVisible = ref(false)
const formData = ref({})


// 处理操作方法
const handleEdit = (row: M.RiskUsersItem) => {
  ElMessageBox.confirm('确认要解封该用户？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    saveAudit({
      user_id: row.user_id,
      device_id: row.device_id,
    }).then((res: any) => {
      if (res.code === 200) {
        ElMessage.success('解封成功')
        onSearchRiskUsersList()
      } else {
        ElMessage.error(res.msg)
      }
    })
  })
}


// ----------------
onMounted(() => {
  console.log('mounted')
  onSearchRiskUsersList();

})
const onSubmit = () => {
  console.log('submit!')
  console.log(searchForm)
  onSearchRiskUsersList();
}


// 处理页码改变
const handleCurrentChange = (val: number): void => {
  searchForm.value.page_index = val
  onSearchRiskUsersList()
}

// 处理每页条数改变
const handleSizeChange = (val: number): void => {
  searchForm.value.page_size = val
  onSearchRiskUsersList()
}

</script>

<style scoped></style>./use-risk-users