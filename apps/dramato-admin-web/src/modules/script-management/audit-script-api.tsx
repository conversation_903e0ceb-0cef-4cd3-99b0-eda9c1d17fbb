import { httpClient } from 'src/lib/http-client'
import { checkEpisodeTitleOrId } from './util'

export const trimSplitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`)).map(str => str.replace(new RegExp(`^[${[' '].join('')}]+|[${[' '].join('')}]+$`, 'g'), ''))

const setAuditLabelIds = (data: M.IScriptItemDetail) => {
  const audit_label_ids = data.audit_label_ids || data.label_ids
  data.audit_label_ids = (audit_label_ids as string)?.split(',').map(Number) || []
  return data
}

const transformLabelIds = (labels: number[]) => {
  return labels.join(',') || ''
}

// 审核列表
export const apiGetAuditList = (data: Api.AuditScript.Request.Params) =>
  httpClient.post<Api.AuditScript.Response.List>('/open_platform_in/play/audit/all/list', data, {
    transformRequestData: {
      title_or_key_list: [checkEpisodeTitleOrId],
    },
  })
// 责编详情
export const apiGetAuditFirstDetail = (data: Api.AuditScriptFirstDetail.Request.Params) =>
  httpClient.post<Api.AuditScriptFirstDetail.Response.Detail>('/open_platform_in/play/audit1/minor/detail', data, {
    transformResponseData: {
      'data.play_item': [setAuditLabelIds],
    },
  },)
// 责编审核
export const apiUpdateAuditFirst = (data: Api.AuditScriptStep1.Request.Params) =>
  httpClient.post<ApiResponse<null>>('/open_platform_in/play/audit1/minor/edit', data, {
    transformRequestData: {
      'data.audit_label_ids': [transformLabelIds],
    },
  })

// 主编
export const apiGetAuditFirstMajorDetail = (data: Api.AuditScriptFirstMajorDetail.Request.Params) =>
  httpClient.post<Api.AuditScriptFirstMajorDetail.Response.Detail>('/open_platform_in/play/audit1/main/detail', data, {
    transformResponseData: {
      'data.play_item': [setAuditLabelIds],
    },
  },)

// 主编审核
export const apiUpdateAuditFirstMajor = (data: Api.AuditScriptStep1.Request.Params) =>
  httpClient.post<ApiResponse<null>>('/open_platform_in/play/audit1/main/edit', data, {
    transformRequestData: {
      'data.audit_label_ids': [transformLabelIds],
    },
  })

// 编辑人员列表
export const apiGetEditorList = () =>
  httpClient.post<ApiResponse<{
    list: M.IAuditUserList[]
  }>>('/open_platform_in/play/audit_user_list')

// 责编详情
export const apiGetAuditSecondDetail = (data: Api.AuditScriptSecondDetail.Request.Params) =>
  httpClient.post<Api.AuditScriptSecondDetail.Response.Detail>('/open_platform_in/play/audit2/minor/detail', data,
    {
      transformResponseData: {
        'data.play_item': [setAuditLabelIds],
      },
    },
  )
// 责编审核
export const apiUpdateAuditSecond = (data: Api.AuditScriptStep2.Request.Params) =>
  httpClient.post<ApiResponse<null>>('/open_platform_in/play/audit2/minor/edit', data, {
    transformRequestData: {
      'data.audit_label_ids': [transformLabelIds],
    },
  })

// 主编
export const apiGetAuditSecondMajorDetail = (data: Api.MajorAuditScriptSecondDetail.Request.Params) =>
  httpClient.post<Api.MajorAuditScriptSecondDetail.Response.Detail>('/open_platform_in/play/audit2/main/detail', data, {
    transformResponseData: {
      'data.play_item': [setAuditLabelIds],
    },
  },)

// 主编审核
export const apiUpdateAuditSecondMajor = (data: Api.AuditScriptStep2Major.Request.Params) =>
  httpClient.post<ApiResponse<null>>('/open_platform_in/play/audit2/main/edit', data, {
    transformRequestData: {
      'data.audit_label_ids': [transformLabelIds],
    },
  })

// 终审主要详情
export const apiGetAuditFinalMajorDetail = (data: Api.AuditScriptFinalDetail.Request.Params) =>
  httpClient.post<Api.AuditScriptFinalDetail.Response.Detail>('/open_platform_in/play/audit3/main/detail', data, {
    transformResponseData: {
      'data.play_item': [setAuditLabelIds],
    },
  },)

// 终审一般详情
export const apiGetAuditFinalDetail = (data: Api.AuditScriptSecondDetail.Request.Params) =>
  httpClient.post<Api.AuditScriptSecondDetail.Response.Detail>('/open_platform_in/play/audit3/minor/detail', data, {
    transformResponseData: {
      'data.play_item': [setAuditLabelIds],
    },
  },)

// 终审审核
export const apiUpdateAuditFinalMajor = (data: Api.AuditScriptStep3.Request.Params) =>
  httpClient.post<ApiResponse<null>>('/open_platform_in/play/audit3/main/edit', data, {
    transformRequestData: {
      'data.audit_label_ids': [transformLabelIds],
    },
  })

// 终审一般审核
export const apiUpdateAuditFinal = (data: Api.AuditScriptStep3Minor.Request.Params) =>
  httpClient.post<ApiResponse<null>>('/open_platform_in/play/audit3/minor/edit', data, {
    transformRequestData: {
      'data.audit_label_ids': [transformLabelIds],
    },
  })

// 上传合同
export const apiUploadContract = (data: Api.AuditScriptStep3Minor.Request.Params) =>
  httpClient.post<ApiResponse<null>>('/open_platform_in/play/upload_contract', data)
