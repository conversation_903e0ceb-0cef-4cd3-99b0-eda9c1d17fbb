
const rootFontSize = 16
/**
 * @param {string} px
 * @returns
 */
const pxToRem = (px) => {
  const n = parseFloat(px)
  return `${n / rootFontSize}rem`
}
/**
 * @param {string} name
 * @returns
 */
const px = (name) => ([_, num]) => ({ [name]: pxToRem(num) })

/**
 * @param {string} name
 * @returns
 */
const vh = (name) => ([_, num]) => ({ [name]: `${num}vh` })

/**
 * @param {string} name
 * @returns
 */
const vw = (name) => ([_, num]) => ({ [name]: `${num}vw` })

export const skynetUnoRules = [
    [/^m-([.\d]+)px$/, px('margin')],
    [/^mr-([.\d]+)px$/, px('margin-right')],
    [/^ml-([.\d]+)px$/, px('margin-left')],
    [/^mt-([.\d]+)px$/, px('margin-top')],
    [/^mb-([.\d]+)px$/, px('margin-bottom')],
    [/^p-([.\d]+)px$/, px('padding')],
    [/^pr-([.\d]+)px$/, px('padding-right')],
    [/^pl-([.\d]+)px$/, px('padding-left')],
    [/^pt-([.\d]+)px$/, px('padding-top')],
    [/^pb-([.\d]+)px$/, px('padding-bottom')],
    [/^w-([.\d]+)px$/, px('width')],
    [/^h-([.\d]+)px$/, px('height')],
    [/^right-([.\d]+)px$/, px('right')],
    [/^left-([.\d]+)px$/, px('left')],
    [/^top-([.\d]+)px$/, px('top')],
    [/^bottom-([.\d]+)px$/, px('bottom')],
    [/^max-w-([.\d]+)px$/, px('max-width')],
    [/^max-h-([.\d]+)px$/, px('max-height')],
    [/^min-w-([.\d]+)px$/, px('min-width')],
    [/^min-h-([.\d]+)px$/, px('min-height')],
    [/^text-([.\d]+)px$/, px('font-size')],
    [/^rounded-([.\d]+)px$/, px('border-radius')],

    [/^w-([.\d]+)vw$/, vw('width')],
    [/^h-([.\d]+)vh$/, vh('height')],
    [/^grid-areas-\[(.+)\]$/, ([_, name]) => ({ 'grid-template-areas': name.replace(/_/g, ' ') })],
    [/^grid-in-(.+)$/, ([_, name]) => ({ 'grid-area': name })],
]

