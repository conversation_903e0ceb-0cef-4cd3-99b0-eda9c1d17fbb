import { createComponent, useValidator } from '@skynet/shared'
import { Button, CheckboxGroup, CreateForm, transformInteger, transformNumber, transformNumber2 } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { useRechargeLevelPage } from './use-free-recharge-level'
import { currencyList, memberAttr } from 'src/lib/constant'
import { ref, watch } from 'vue'

export const RechargeLevelForm = createComponent(null, () => {
  const { currentLevel, applicationList, onEdit, onCreate, closeEditRechargeLevelModal } = useRechargeLevelPage()
  const Form = CreateForm<M.RechargeLevel>()
  const MemberAttr = ref(memberAttr)

  const formRules = z.object({
    title: z.string().min(1, '请输入档位名称').max(40, '最多40个字符'),
    app_id: z.number({ message: '请选择生效应用' }),
    priority: z.number({ message: '请选择序号' }).max(12, '最大12').min(1, '最小1'),
    currency: z.string().min(1, '请输入币种'),
    time_limit: z.number({ message: '请选输入原价' }).min(1, '最小1').max(999, '最大999').optional(),
    discount_price: z.number({ message: '请输入现价' }),
    delivery_details: z.object({
      quanity: z.number({ message: '请输入售卖金币数' }).max(99999, '最大99999').min(1, '最小1'),
      bonus: z.number({ message: '请输入赠送金币数' }).max(99999, '最大99999').optional(),
      // period: z.string().min(1, '请选择时长单位'),
    }),
    store: z.string().min(1, '请选择上架商店'),
    // google_play: z.object({
    //   google_play_id: z.string().min(1, '请输入google_play_id'),
    // }),
    // apple_store: z.object({
    //   apple_id: z.string().min(1, '请输入apple_id'),
    //   product_id: z.string().min(1, '请输入product_id'),
    // }),
  })

  const { error, validateAll } = useValidator(currentLevel, formRules)

  watch(
    () => currentLevel.value.app_id,
    () => {
      const platform = applicationList.value.find(item => item.id === currentLevel.value.app_id)?.platform
      if (platform === 1) {
        set(currentLevel.value, 'store', 'Apple Store')
      } else if (platform === 2) {
        set(currentLevel.value, 'store', 'Google Play')
      }
    },
    {
      immediate: true,
      deep: true,
    },
  )

  watch(
    () => currentLevel.value.status,
    () => {
      if (currentLevel.value.status === 2) {
        const i = currentLevel.value.props?.findIndex(item => item === 'default') as number
        i > -1 && currentLevel.value.props?.splice(i, 1)
      }
      MemberAttr.value.forEach(item => {
        item.value === 'default' && (item.disabled = currentLevel.value.status === 2)
      })
    },
  )

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <h1 class="font-medium border-l-[2px] border-l-solid border-l-primary pl-[10px]">基础信息</h1>
        <Form
          class="grid gap-y-3 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentLevel.value || {}, path, value)
          }}
          items={[
            // currentLevel.value.id ? { label: '档位ID：', path: 'id', input: { type: 'text', disabled: true } } : { label: '', path: '', input: { type: 'custom', render: () => <></> } },
            {
              label: requiredLabel('档位名称'),
              path: 'title',
              input: {
                type: 'text',
                maxlength: 40,
                placeholder: '请输入档位名称，1-40个字符',
              },
            },
            {
              label: requiredLabel('选择生效应用'),
              path: 'app_id',
              input: {
                type: 'select',
                options: applicationList.value.map(item => ({
                  label: item.app_name,
                  value: item.id,
                })),
                autoInsertEmptyOption: false,
                disabled: !!currentLevel.value.id,
              },
              transform: transformNumber,
            },
            {
              label: requiredLabel('首充档位'),
              path: 'first_recharge',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { label: '正常档位', value: 0 },
                  { label: '首充档位', value: 1 },
                  { label: '充值用户档位', value: 2 },
                ],
              },
            },
            {
              label: requiredLabel('支付面板展示'),
              path: 'status',
              transform: transformInteger,
              input: { type: 'radio',
                options: [
                  {
                    label: '展示',
                    value: 1,
                  },
                  {
                    label: '不展示',
                    value: 2,
                  },
                ],
              },
            },
            {
              label: requiredLabel('序号'),
              path: 'priority',
              input: {
                type: 'select',
                options: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(i => ({
                  label: '' + i,
                  value: i,
                })),
              },
              transform: transformInteger,
              class: currentLevel.value?.status === 2 ? 'hidden' : '',
            },
            {
              label: '限制时长',
              path: 'time_limit',
              input: {
                type: 'number',
                placeholder: '为空表示不限制，可输入整数1-999',
                min: 1,
                max: 999,
                suffix: <>h</>,
              },
              transform: transformInteger,
            },
            {
              label: requiredLabel('币种：'),
              path: 'currency',
              input: {
                type: 'select',
                options: currencyList,
                autoInsertEmptyOption: false,
                disabled: !!currentLevel.value.id,
              },
            },
            {
              label: requiredLabel('现价'),
              path: 'discount_price',
              input: {
                type: 'number',
                suffix: <>元</>,
                min: '0',
                step: '0.01',
                placeholder: '支持小数点后两位',
                disabled: !!currentLevel.value.id,
              },
              transform: transformNumber2,
            },
            {
              label: requiredLabel('售卖观影券数'),
              path: 'delivery_details.quanity',
              input: {
                type: 'number',
                placeholder: '请输入整数1-99999',
                min: 1,
                max: 99999,
                suffix: <>个</>,
              },
              transform: transformNumber,
            },
            {
              label: '赠送观影券数',
              path: 'delivery_details.bonus',
              input: {
                type: 'number',
                placeholder: '请输入整数1-99999',
                min: 1,
                max: 99999,
                suffix: <>个</>,
              },
              transform: transformNumber,
            },
            { label: requiredLabel('上架商店'),
              path: 'store',
              input: {
                type: 'select',
                disabled: true,
                autoInsertEmptyOption: false,
                options: [
                  {
                    label: 'Apple Store',
                    value: 'Apple Store',
                  },
                  {
                    label: 'Google Play',
                    value: 'Google Play',
                  },
                ],
              },
            },
            {
              label: '角标文案',
              path: 'slogan',
              input: {
                type: 'text',
              },
            },
            { label: '档位属性：', path: 'props', input: { type: 'custom', render: () => {
              return (
                <div class="flex flex-col flex-1">
                  <CheckboxGroup
                    class="-mt-1.5"
                    modelValue={currentLevel.value.props || []}
                    onUpdate:modelValue={(e: unknown) => currentLevel.value.props = e as string[]}
                    options={MemberAttr.value}
                  />
                  <div class="text-sm text-[var(--text-3)]">
                    <div>1. 高亮显示，选中后前端App高亮显示，可配置多个档位</div>
                    <div>2. 默认选中，同一个App仅会生效一个默认选中，会员档位和充值档位都有选中配置，则按优先级生效： 会员档位 &gt; 充值档位</div>
                  </div>
                </div>
              )
            } } },
          ]}
          data={currentLevel.value}
        />
        <h1 class="font-medium border-l-[2px] border-l-solid border-l-primary pl-[10px]">商店信息</h1>
        <Form
          class="grid gap-y-3 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentLevel.value || {}, path, value)
          }}
          items={currentLevel.value.store === 'Google Play'
            ? [
                { label: 'Google Play ID', path: 'google_play.google_play_id', input: { type: 'text' } },
              ]
            : [
                { label: 'AppleID', path: 'apple_store.apple_id', input: { type: 'text' } },
                { label: '产品 ID', path: 'apple_store.product_id', input: { type: 'text' } },
                { label: '订阅群组 ID', path: 'apple_store.group_identifier', input: { type: 'text' } },
              ]}
          data={currentLevel.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeEditRechargeLevelModal.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          let exclude: string[] = []
          if (currentLevel.value.status === 2) {
            exclude = ['priority']
          }
          // if (currentLevel.value.store === 'Google Play' && Number(currentLevel.value.status)) {
          //   exclude = ['apple_store.apple_id', 'apple_store.product_id']
          // }
          // if (currentLevel.value.store === 'Apple Store' && Number(currentLevel.value.status)) {
          //   exclude = ['google_play.google_play_id']
          // }

          // if (currentLevel.value.status !== 1) {
          //   exclude = ['google_play.google_play_id', 'apple_store.apple_id', 'apple_store.product_id']
          // }
          if (!validateAll({ exclude })) {
            return
          }
          !currentLevel.value?.id ? void onCreate() : void onEdit()
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
