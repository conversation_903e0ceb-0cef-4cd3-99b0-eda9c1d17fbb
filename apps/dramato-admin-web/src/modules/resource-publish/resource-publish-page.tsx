/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, Checkbox, TableColumnOld, Pager, transformTimestamp, CheckboxGroup, Button, openDialog, showSuccessToast, showFailToast, Icon, transformNumber, Tooltip } from '@skynet/ui'
import { ref, onUnmounted } from 'vue'
import { set } from 'lodash-es'
import { apiGetPublishList, apiPublishResource } from './resource-publish-api'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { publishLangValue, publishLangKey, langKey, langValue } from 'src/modules/resource/constant'
import { requiredLabel } from 'src/lib/required-label'
import { publishStatusOptions, disabledPublishStatus } from './constant'
import { renderTitle, renderStatusDesc } from './util'
import { useResourceStore } from './use-resource-publish-store'
import { taskStatusOptions, auditStatusForQuery, releaseRounds } from './constant'
import UploadImagePreview from './components/upload-image-preview'
import { ElDatePicker } from 'element-plus'

type ResourcePublishPageOptions = {
  props: {}
}
export const ResourcePublishPage = createComponent<ResourcePublishPageOptions>({
  props: {},
}, props => {
  const {
    getTaskRole,
    resourceRole,
    all_members,
    onDistributeTaskNoTeam,
    onGetTask,
  } = useResourceStore()
  const resourceType = 4
  const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()
  const loading = ref(false)
  const list = ref<M.IResourcePublishItem[]>([])
  const defaultParams: M.IResourcePublishQueryParams = {
    title: '',
    id: undefined,
    series_key_or_title_list: '',
    resource_id_or_title_list: '',
    create_start_time: undefined, // 录入开始时间
    create_end_time: undefined, // 录入结束时间
    publish_status: -1,
    claim_names: [],
    audit_status_list: [],
    task_status: 0,
    has_en_cover: 0
  }
  const form = ref<M.IResourcePublishQueryParams>({...defaultParams})
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const Table = CreateTableOld<M.IResourcePublishItem>()
  const QueryForm = CreateForm<M.IResourcePublishQueryParams>()

  const getList = async (showLoading = true) => {
    const params = {
      ...form.value,
      ...pageInfo.value,
    }
    if (showLoading) {
      loading.value = true
    }

    try {
      const res = await apiGetPublishList(params)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const showStatusDesc = (status: 0 | 1 | 2 | 3, id: number) => {
    const color = ['', 'badge-primary', 'text-red-500', 'text-green-500'][status]
    const text = ['待发布', '发布中', '发布失败', '发布完成'][status]
    return (
      <div class={`badge badge-outline ${color}`}>{text}
      </div>
    )
  }

  const getLangDesc = (langCode: string) => {
    return langValue[langKey.indexOf(langCode)]
  }

  const showProgressDesc = (row: M.IResourcePublishItem) => {
    const publishData = row.publish_data
    const successItems = publishData.filter(row => row.publish_status === 3)
    const failItems = publishData.filter(row => row.publish_status === 2)

    let detail = `已发布：${successItems.map(row => getLangDesc(row.language_code)).join(',')};`
    if (failItems.length > 0) detail += `发布报错：${failItems.map(row => getLangDesc(row.language_code)).join(',')};`
    return (
      successItems.length > 0
        ? (
            <div class="tooltip" data-tip={detail}>
              {successItems.length}/{publishData.length}
            </div>
          )
        : <div>{successItems.length}/{publishData.length}</div>
    )
  }

  const columns: TableColumnOld<M.IResourcePublishItem>[] = [
    ['资源ID', 'id', { class: 'w-[80px]' }],
    ['资源名称', row => renderTitle(row), { class: 'w-[190px] overflow-hidden clamp-line-1' }],
    ['审核状态', row => {
      return renderStatusDesc(row, true)
    }, { class: 'w-[145px] text-center' }],
    ['英文封面', row => {
      return (
        row.cover
          ? <UploadImagePreview deleteAble={false} image={row.cover} /> : '-'
      )
    }, { class: 'w-[140px]' }],
    ['多语言校验状态', row => {
      const checkedLang = row.multi_subtitle_audit_progress.filter(t => t.status === 2)
      let hasChecked = ''
      let notChecked = ''
      if (checkedLang.length === row.multi_subtitle_audit_progress.length) {
        hasChecked = '全部语言通过'
      } else {
        hasChecked = checkedLang.length > 0 ? `已通过校验：${checkedLang.map(row => langValue[langKey.findIndex(i => i === row.language_code)]).join(',')}` : ''
        notChecked = `未通过校验：${row.multi_subtitle_audit_progress.filter(t => t.status !== 2).map(row => langValue[langKey.findIndex(i => i === row.language_code)]).join(',')}`
      }
      return (
        <div class="text-center">
          <Tooltip popContent={() => hasChecked ? <div> <div>{hasChecked}</div> <div>{notChecked}</div> </div> : null}>
            <span class={checkedLang.length === row.multi_subtitle_audit_progress.length ? 'text-green-500' : ''}>{checkedLang.length}/{row.multi_subtitle_audit_progress.length}</span>
          </Tooltip>
        </div>
      )
    }, { class: 'w-[110px] text-center' }],

    ['合作方', 'partner_name', { class: 'w-[140px]' }],
    ['认领人', 'claim_name', { class: 'w-[120px]' }],
    ['多语言发布进度', row => showProgressDesc(row), { class: 'w-[110px] text-center' }],
    ['多语言发布状态', row => showStatusDesc(row.publish_status_compound, row.id), { class: 'w-[110px] text-center' }],
    ['原剧集发布状态', row => showStatusDesc(row.publish_status_source, row.id), { class: 'w-[110px] text-center' }],
    ['操作', (row, index) => (
      <div class="flex justify-center gap-x-2">
        <Button
          class="btn btn-link btn-xs"
          onClick={() => {
            window.open(`${location.origin}/resource-publish/information/${row.id}`)
          }}
        >
          信息预览
        </Button>
        <Button
          class="btn btn-link btn-xs"
          onClick={() => {
            window.open(`${location.origin}/resource-publish/detail/${row.id}`)
          }}
        >
          预览
        </Button>
        {!disabledPublishStatus.includes(row.publish_status_compound)
          ? (
              <Button
                class="btn btn-link btn-xs"
                onClick={() => {
                  const language_codes = ref<string[]>([])
                  const saveLoading = ref(false)
                  const checkboxCheck = ref<number[]>([])
                  const online_time = ref<number>()
                  if (row.online_time) online_time.value = row.online_time * 1000
                  const hideDialog = openDialog({
                    title: '多语言发布',
                    mainClass: 'pb-0 px-5',
                    customClass: '!w-[400px]',
                    body: () => (
                      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                        <div class="flex items-center gap-x-2">
                          {requiredLabel('语言:')}
                          <FormMultiSelect
                            search={true}
                            class="w-[200px]"
                            popoverWrapperClass="z-popover-in-dialog"
                            options={publishLangKey.map((n, index) => {
                              return { value: publishLangKey[index], label: publishLangValue[index] }
                            })}
                            modelValue={language_codes.value}
                            onUpdate:modelValue={e => {
                              language_codes.value = e as string[]
                            }}
                          />
                          <label class="flex items-center ml-2">
                            <span class="text-xs text-[var(--text-2)]">全选</span>
                            <Checkbox
                              label=""
                              modelValue={language_codes.value.length === publishLangKey.length}
                              onUpdate:modelValue={(value: boolean) => {
                                if (value) {
                                  language_codes.value = [...publishLangKey]
                                } else {
                                  language_codes.value = []
                                }
                              }}
                            />
                          </label>
                        </div>
                        <div class="flex items-center gap-x-2">
                          <span>上线时间:</span>
                          <ElDatePicker v-model={online_time.value} editable={false} clearable={false} type="datetime" />
                        </div>
                        <div class="flex items-center gap-x-2">
                          <CheckboxGroup
                            class="-mt-1.5"
                            modelValue={checkboxCheck.value}
                            onUpdate:modelValue={(e: unknown) => {
                              checkboxCheck.value = e as number[]
                              return
                            }}
                            options={[{
                              label: '重新转码',
                              value: 1,
                            }, {
                              label: '重新压制',
                              value: 2,
                            },
                            ]}
                          />
                        </div>
                        <x-status-footer class="w-full flex justify-end gap-x-[10px]">
                          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                          <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
                            if (language_codes.value.length === 0) {
                              showFailToast('请选择支持语言')
                              return
                            }
                            try {
                              saveLoading.value = true
                              await apiPublishResource({
                                ids: [row.id],
                                type: 2,
                                language_codes: language_codes.value,
                                re_transcode: checkboxCheck.value.includes(1) ? 1 : 0,
                                re_inject: checkboxCheck.value.includes(2) ? 1 : 0,
                                online_time: online_time.value,
                              })
                              hideDialog()
                              showSuccessToast('操作成功')
                              onQuery()
                              saveLoading.value = false
                            } catch (error: any) {
                              saveLoading.value = false
                              showFailToast(error.response.data.message || '操作失败')
                            }
                          }}
                          >
                            {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                            确定
                          </button>
                        </x-status-footer>
                      </x-status-confirm-dialog>
                    ),
                  })
                }}
              >
                { row.publish_status_compound === 3 ? '多语言重新发布' : '多语言发布'}
              </Button>
            )
          : <Button class="btn btn-link btn-xs cursor-not-allowed text-gray-500">多语言发布</Button>}
        {
          // 中文语音或者翻译局无法发原剧集发布
          row.audio_language_code === 'zh-CN' || (!row.audio_language_code && row.resource_type === 2)
            ? <Button class="btn btn-link btn-xs cursor-not-allowed text-gray-500">原剧集发布</Button>
            : !disabledPublishStatus.includes(row.publish_status_source)
                ? (
                    <Button
                      class="btn btn-link btn-xs"
                      onClick={() => {
                        const saveLoading = ref(false)
                        const online_time = ref<number>()
                        if (row.online_time) online_time.value = row.online_time * 1000
                        const hideDialog = openDialog({
                          title: '原剧集发布',
                          mainClass: 'pb-0 px-5',
                          customClass: '!w-[400px]',
                          body: () => (
                            <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                              <div class="flex items-center gap-x-2">
                                <span>上线时间:</span>
                                {/* <input
                                  type="datetime-local"
                                  value={online_time.value ? dayjs(online_time.value * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
                                  onInput={(e: Event) => online_time.value = new Date((e.target as HTMLInputElement).value).getTime() / 1000}
                                /> */}
                                <ElDatePicker v-model={online_time.value} editable={false} clearable={false} type="datetime" />
                              </div>
                              <x-status-footer class="w-full flex justify-end gap-x-[10px]">
                                <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                <button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={async () => {
                                  try {
                                    saveLoading.value = true
                                    await apiPublishResource({
                                      ids: [row.id],
                                      type: 1,
                                      online_time: online_time.value,
                                    })
                                    hideDialog()
                                    showSuccessToast('操作成功')
                                    onQuery()
                                    saveLoading.value = false
                                  } catch (error: any) {
                                    saveLoading.value = false
                                    showFailToast(error.response.data.message || '操作失败')
                                  }
                                }}
                                >
                                  {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                                  确定
                                </button>
                              </x-status-footer>
                            </x-status-confirm-dialog>
                          ),
                        })
                      }}
                    >
                      { row.publish_status_source === 3 ? '原剧集重新发布' : '原剧集发布'}
                    </Button>
                  )
                : <Button class="btn btn-link btn-xs cursor-not-allowed text-gray-500">原剧集发布</Button>
        }
        {
          resourceRole.value === 1
            ? (
                <Button
                  class="btn btn-link btn-xs"
                  onClick={() => {
                    void onDistributeTaskNoTeam({
                      type: resourceType,
                      resource_ids: [row.id],
                    }, getList)
                  }}
                >
                  分配任务
                </Button>
              )
            : null
        }
      </div>
    ), {
      class: 'w-[420px] text-center',
    },
    ],
  ]

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = { ...defaultParams, audit_status_list: [], has_en_cover: 0 }
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }

  onQuery()
  void getTaskRole({
    type: resourceType,
  })

  let interval = null
  interval = setInterval(() => {
    void getList(false)
  }, 60 * 1000)

  onUnmounted(() => {
    clearInterval(interval)
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>终审发布</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              ['资源名称/ID：', 'resource_id_or_title_list', { type: 'text', placeholder: '请输入资源名称或者id' }],
              ['剧集名称/ID：', 'series_key_or_title_list', { type: 'text', placeholder: '请输入资源名称' }],
              ['发布状态', 'publish_status', { type: 'select', options: [{
                value: -1,
                label: '全部',
              }, ...publishStatusOptions], placeholder: '请选择发布状态', autoInsertEmptyOption: false }, { transform: transformNumber }],
              ['审核状态：', 'audit_status_list', { type: 'multi-select', maxlength: 1, options: auditStatusForQuery }],
              ['合作方：', 'partner_name', { type: 'text', placeholder: '请输入合作方' }],
              ['认领人：', 'claim_names', { type: 'multi-select', search: true, options: all_members.value.map(m => {
                return {
                  value: m,
                  label: m,
                }
              }) }],
              ['认领状态：', 'task_status', { type: 'select', options: taskStatusOptions, autoInsertEmptyOption: false }, { transform: transformNumber }],
              [
                ['录入时间-开始：', 'create_start_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
                ['录入时间-结束：', 'create_end_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
              ],
              ['发行轮次：', 'release_round', { type: 'select', placeholder: '请选择发行轮次', options: releaseRounds }, { transform: transformNumber }],
              ['是否存在英文封面：', 'has_en_cover', { type: 'select', placeholder: '是否存在英文封面', options: [
                {
                  value: 1,
                  label: '存在',
                },
                {
                  value: 2,
                  label: '不存在',
                },
              ] }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex justify-between items-center">
            <span>终审发布列表</span>
            <div class="space-x-2">
              { resourceRole.value === 2
                ? (
                    <button class="btn btn-primary btn-sm" onClick={() => {
                      void onGetTask(resourceType, onQuery)
                    }}
                    >领取任务
                    </button>
                  )
                : null}
            </div>
          </x-table-actions>
        ),
        table: () => (
          <Table
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ResourcePublishPage
