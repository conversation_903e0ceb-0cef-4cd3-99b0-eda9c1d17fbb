/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { useInspectionRoleDetailStore } from './use-inspection-role-detail-store'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { langKey, langValue } from 'src/modules/resource/constant'
import Subtitle from 'src/modules/common/subtitle/subtitle1'
import { apiSaveSubtitleContent, apiSaveSingleSubtitleRole } from 'src/modules/role-mark/role-mark-api'
import { Button, Checkbox, Icon, openDialog, showFailToast, showSuccessToast, SvgIcon, Tooltip } from '@skynet/ui'
import { M3u8Player } from 'src/modules/resource/components/m3u8-player'
import Editor from 'src/modules/resource/components/editor'
import { formatSubtitles } from 'src/modules/resource/util'
import { Term } from './components/term'
import { apiUpdateRoleTerminology, apiUpdateMark } from 'src/modules/resource-publish/resource-publish-api'
import { useTopStore } from 'src/modules/role-mark/use-top-store.tsx'
import { trim } from 'lodash-es'
import { useElementSize } from '@vueuse/core'
import { useTemplateRef } from 'vue'
import { getSubtitleContent } from 'src/modules/common/subtitle/until'

type InspectionRoleDetailPageOptions = {
  props: {}
}
export const InspectionRoleDetailPage = createComponent<InspectionRoleDetailPageOptions>({
  props: {},
}, props => {
  const { setSeriesId, getDetail, getSingleSubtitleErrors, errDetail, getSubtitleFixes, subtitleFixesList, getSubtitleErrors, subtitleErrorList, currentResource, characters, annotations, getCharacters, emotions, effects, emotions_cn, effects_cn, getAnnotations, getRealSubtitle, getList, list, loading, defaultLanguageCode, onCleanSubtitle, onExtractSubtitle } = useInspectionRoleDetailStore()
  const route = useRoute()
  const router = useRouter()
  setSeriesId(+route.params.id)
  const btnLoading = ref(false)
  const resourceLoading = ref(false)
  const isInit = ref(false)
  const isAsync = ref(true)
  const serial_number = ref<number>(+(route.query.number || 1))
  const btnDisabled = ref(false)
  const currentRow = ref<Api.VoiceoverResource.IMultipleResource>({})
  const videoType = ref<'pure_video_path' | 'ora_video_path'>('pure_video_path')
  const videoUrl = ref('')
  // 备注相关
  const tooltipRef = ref<any>(null)
  const remark = ref('')
  const textarea = ref('')

  // 用于置顶排序
  const {
    saveData,
    getData,
  } = useTopStore()

  const referenceRef = ref()
  const reference = ref<{
    code: string
    language: string
  }>({
    code: '',
    language: '',
  })
  // 剧集原语言
  const referenceLangIndex = ref(-1)
  const videoRef = ref()
  const currentTime = ref(0)
  const isBatchSelect = ref(false)
  const subtitles = ref<{ language: string, type: string, subtitle: string }[]>([])
  // 左侧语言、右侧语言及技术发生变化
  const setRouter = (params: { [Record: string]: string | number }) => {
    const query = {
      ...route.query,
      ...params,
    }
    void router.replace({
      path: location.pathname,
      query,
    })
  }

  const el: any = useTemplateRef('el')
  const { height } = useElementSize(el)

  watch(() => [currentRow.value.num], async () => {
    resourceLoading.value = true
    currentTime.value = 0
    errDetail.value = []
    subtitleFixesList.value = []
    if (currentRow.value.num) {
      setRouter({
        number: currentRow.value.num,
      })
    }
    if (typeof currentRow.value.num !== 'number') return
    videoUrl.value = currentRow.value[`${langKey[referenceLangIndex.value]}_${videoType.value}`]
    serial_number.value = currentRow.value.num
    await getAnnotations(currentRow.value.num)
    sortRoles()
    const code = await getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[referenceLangIndex.value]}_srt_path`]))
    // 获取报错
    await getSingleSubtitleErrors(currentRow.value.num, defaultLanguageCode.value)
    // 获取替换优化提示
    await getSubtitleFixes(currentRow.value.num, defaultLanguageCode.value, code)
    // 左侧对照永远选择 中文
    reference.value = {
      code,
      language: langKey[referenceLangIndex.value],
    }

    subtitles.value = [
      {
        language: reference.value.language,
        type: 'normal',
        subtitle: getRealSubtitle(currentRow.value[`${langKey[referenceLangIndex.value]}_srt_path`]),
      },
    ]
    // 可选的角色 suggestion
    resourceLoading.value = false
    textarea.value = remark.value = currentResource.value?.list?.find(item => item.serial_number === currentRow.value.num)?.remark || ''
  }, {
    immediate: true,
  })

  watch(() => videoRef.value, newVal => {
    if (newVal) {
      videoRef.value.on('timeupdate', () => {
        currentTime.value = videoRef.value.getCurrentTime() || 0
      })
    }
  })

  watch(() => videoType.value, () => {
    if (videoType.value === 'ora_video_path') {
      videoUrl.value = currentRow.value[`${defaultLanguageCode.value}_ora_video_path`]
    } else if (videoType.value === 'pure_video_path') {
      videoUrl.value = currentRow.value[`${defaultLanguageCode.value}_pure_video_path`]
    }
  })

  const getFormatSubtitle = (list: Api.RoleMark.ISubtitleLine[]) => {
    return list.map(item => {
      return `${item.order}\n${item.time}\n${item.content}\n`
    }).join('\n')
  }

  const onCaptureImage = () => {
    const video = videoRef.value._el.querySelector('video')
    const canvas = document.createElement('canvas')
    video.crossOrigin = 'anonymous' // 允许跨域访问
    canvas.width = video.videoWidth * 3
    canvas.height = video.videoHeight * 3
    const ctx = canvas.getContext('2d')
    ctx?.drawImage(video, 0, 0, canvas.width, canvas.height)
    // 导出为 base64 图片
    const imageData = canvas.toDataURL('image/jpeg', 1.0)
    console.log(imageData, 'imageData')
    const link = document.createElement('a')
    link.setAttribute('href', imageData)
    link.setAttribute('download', currentRow.value.serial_number + '_' + Date.now())
    document.body.appendChild(link)
    link.click()
  }

  const sortRoles = () => {
    const topFixedList = getData('FIXED_KEY_' + (route.params.id as string)) as M.ITerminology[]
    // 统计角色名出现的频次
    const characterFrequency: Record<string, number> = {}
    const _list = referenceRef.value.getContent() as Api.RoleMark.ISubtitleLine[]
    if (_list.length > 0) {
      // 遍历字幕行，统计每个角色出现的次数
      _list.forEach(line => {
        if (line.selectedCharacters && line.selectedCharacters.length > 0) {
          line.selectedCharacters.forEach(c => {
            if (c.term || c.name) characterFrequency[c.term || c.name] = (characterFrequency[c.term || c.name] || 0) + 1
          })
        }
      })
    } else {
      annotations.value.forEach(c => {
        if (c.term || c.name) characterFrequency[c.term || c.name] = (characterFrequency[c.term || c.name] || 0) + 1
      })
    }
    // 先根据置顶列表排序，再根据频次排序
    const sortedList = [...characters.value.map(item => {
      item.isFixed = false
      return item
    })].sort((a, b) => {
      // 如果不在置顶列表中或者置顶列表为空，则按照频次排序
      const freqA = a.character_id ? characterFrequency[(a.term || a.name)!] || 0 : 0
      const freqB = b.character_id ? characterFrequency[(b.term || b.name)!] || 0 : 0
      return freqB - freqA // 降序排列，出现频次高的排在前面
    })

    // 如果有置顶列表
    if (topFixedList && topFixedList.length > 0) {
      // 创建一个新的排序列表
      const newSortedList: M.ITerminology[] = []

      // 首先添加置顶列表中的角色（按照置顶列表的顺序）
      topFixedList.forEach(fixedItem => {
        const foundCharacter = sortedList.find(char => char.character_id === fixedItem.character_id)
        if (foundCharacter) {
          newSortedList.push({
            ...foundCharacter,
            isFixed: true,
          })
        }
      })

      // 然后添加其余未置顶的角色（保持原有的频次排序）
      sortedList.forEach(character => {
        // 检查该角色是否已经在新列表中（即是否为置顶角色）
        if (!newSortedList.some(item => item.character_id === character.character_id) || !character.character_id) {
          newSortedList.push(character)
        }
      })

      // 用新排序的列表替换原列表
      characters.value = newSortedList
    } else {
      characters.value = sortedList
    }
  }

  // const pollList = () => {
  //   let isPolling = false
  //   const startPolling = async () => {
  //     if (isPolling) return
  //     isPolling = true

  //     try {
  //       await getList(false)
  //       const curItem = list.value.find(item => item.num === currentRow.value.num)
  //       if (curItem && currentRow.value[`${langKey[referenceLangIndex.value]}_srt_path`] !== curItem[`${langKey[referenceLangIndex.value]}_srt_path`]) {
  //         reference.value.code = await getSubtitleContent(getRealSubtitle(curItem[`${langKey[referenceLangIndex.value]}_srt_path`]))
  //       }
  //       if (curItem && currentRow.value[`${langKey[referenceLangIndex.value]}_${videoType.value}`] !== curItem[`${langKey[referenceLangIndex.value]}_${videoType.value}`]) {
  //         videoUrl.value = curItem[`${langKey[referenceLangIndex.value]}_${videoType.value}`]
  //       }
  //     } catch (error) {
  //       console.error('轮询请求失败:', error)
  //     } finally {
  //       isPolling = false
  //       // 等待30秒后开始下次轮询
  //       setTimeout(startPolling, 60000)
  //     }
  //   }
  //   setTimeout(startPolling, 60000)
  // }

  onMounted(async () => {
    isInit.value = false
    void getCharacters()

    await getList()
    // pollList()
    referenceLangIndex.value = langKey.findIndex(key => key === defaultLanguageCode.value)
    currentRow.value = list.value.find(item => item.num === serial_number.value) as Api.VoiceoverResource.IMultipleResource
    // await getAnnotations(currentRow.value.num)
    // 左侧对照永远选择 中文
    const subtitleLang = langKey[referenceLangIndex.value]
    reference.value = {
      code: '',
      language: subtitleLang,
    }
    subtitles.value = [
      {
        language: reference.value.language,
        type: 'normal',
        subtitle: getRealSubtitle(currentRow.value[`${langKey[referenceLangIndex.value]}_srt_path`]),
      },
    ]
    isInit.value = true
    await getDetail()
    // 获取备注
    textarea.value = remark.value = currentResource.value?.list?.find(item => item.serial_number === currentRow.value.num)?.remark || ''
  })

  return () => (
    <x-inspection-role-detail-page class="block">
      {
        isInit.value ? (
          <div class="px-4 flex flex-col h-[100vh] max-w-[1400px] mx-auto">
            <x-nav class="py-4 flex items-center space-x-4">
              <a class="btn btn-sm md:btn-md gap-2 lg:gap-3" href={`/resource-publish/inspection-detail/${+route.params.id}`}>
                <svg class="h-6 w-6 fill-current md:h-8 md:w-8 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                  <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z" />
                </svg>
                <div class="flex flex-col items-start gap-0.5 leading-[1.1]">
                  <span>返回</span>
                </div>
              </a>
              {currentResource.value?.title ? (
                <>
                  <div>资源名称：<span class="text-[var(--text-1)] font-bold">{currentResource.value?.title}</span></div>
                  <div>资源ID：<span class="text-[var(--text-1)] font-bold">{currentResource.value?.id}</span></div>
                  <div>原视频语言：<span class="text-[var(--text-1)] font-bold">{langValue[langKey.findIndex(key => currentResource.value?.upload_subtitle_language_code && key === currentResource.value?.upload_subtitle_language_code[0])]}</span></div>
                  <div>
                    {remark.value ? (
                      <Tooltip placement="bottom-start" popWrapperClass="z-popover-in-dialog max-w-[400px]" popContent={() => (
                        <pre class="whitespace-pre-wrap break-words">备注：{remark.value}</pre>
                      )}>
                        <div class="line-clamp-1 w-[400px] flex-1 text-sm text-[var(--text-2)]">
                          备注：{remark.value}
                        </div>
                      </Tooltip>
                    ) : null}
                  </div>
                  {subtitleErrorList.value.length > 0
                    ? (
                        <div class="flex">
                          字幕异常数量：
                          <Tooltip popContent={() =>
                            subtitleErrorList.value.map(err => <div>第{err.serial_number}集，{langValue[langKey.findIndex(k => k === err.language_code)]}字幕, 错误个数: {err.err_count}</div>)}
                          >
                            <div class="flex cursor-pointer items-center space-x-1 text-red-500">
                              <span>{ subtitleErrorList.value.length }</span>
                              <SvgIcon class="size-3.5" name="ic_exclamation" />
                            </div>
                          </Tooltip>
                        </div>
                      )
                    : null }
                </>
              ) : null}
            </x-nav>
            <div class="flex flex-1 flex-row overflow-y-auto overflow-x-hidden space-x-2">
              <x-video-area class="block">
                {
                  !videoUrl.value ? <div class="flex h-[550px] w-[309px] items-center justify-center text-[var(--text-2)]">暂无视频</div> : null
                }
                {
                  videoUrl.value === 'processing' ? <div class="flex h-[550px] w-[309px] items-center justify-center text-[var(--text-2)]">视频处理中……</div> : null
                }
                {
                  videoUrl.value === 'failed' ? <div class="flex h-[550px] w-[309px] items-center justify-center text-[var(--text-2)]">处理失败</div> : null
                }
                { videoUrl.value && !['processing', 'failed'].includes(videoUrl.value)
                  ? (
                      <M3u8Player
                        subtitles={subtitles.value}
                        currentLanguage={langKey[referenceLangIndex.value]}
                        url={getRealSubtitle(videoUrl.value)}
                        onPlayerReady={(e: any) => {
                          videoRef.value = e
                        }}
                      />
                    )
                  : null }
                <div class="mt-4 space-x-4">
                  <Button class="btn btn-primary btn-sm" onClick={() => {
                    onCleanSubtitle(currentRow.value, () => {
                      videoUrl.value = 'processing'
                      btnDisabled.value = true
                    })
                  }}>字幕抹除</Button>
                  <Button class="btn btn-primary btn-sm" onClick={() => {
                    onExtractSubtitle(currentRow.value, () => {
                      reference.value.code = 'processing'
                      btnDisabled.value = true
                    })
                  }}>字幕提取</Button>
                </div>
              </x-video-area>
              <x-subtitle-area class="flex h-full min-w-[500px] flex-1 overflow-auto">
                <x-reference class="w-full flex flex-col">
                  <div class="flex justify-between h-[36px]">
                    <select value={reference.value.language} disabled class="select select-bordered select-xs w-[100px]" onChange={(e: any) => {
                      const value = e.target.value as string
                      referenceLangIndex.value = langKey.findIndex(key => key === value)
                      setRouter({
                        ref: value,
                      })
                    }}
                    >
                      {
                        langKey.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
                      }
                    </select>
                    <div>
                      <Button class="btn btn-primary btn-xs" onClick={() => {
                        const _list = referenceRef.value.getContent() as Api.RoleMark.ISubtitleLine[]
                        const list = _list.map((item, index) => {
                          item.order = index + 1
                          return item
                        })

                        const formatCode = getFormatSubtitle(list)
                        const code = ref(formatCode)
                        const btnLoading = ref(false)
                        const hideDialog = openDialog({
                          mainClass: 'pb-0 px-5',
                          customClass: '!w-[600px]',
                          title: '字幕覆盖',
                          body: () => (
                            <div>
                              <Editor
                                customClass="w-full h-[550px]"
                                code={code.value}
                                options={{
                                  language: 'plaintext',
                                  formatOnPaste: false,
                                  tabSize: 2,
                                  inDiffEditor: false,
                                  minimap: {
                                    enabled: false,
                                  } }}
                                onChange={e => {
                                  code.value = e
                                }}
                              />
                              <div class="flex justify-end space-x-4">
                                <Button class="btn-default btn btn-sm" onClick={() => {
                                  hideDialog()
                                }}>取消</Button>
                                <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} loading={btnLoading.value} onClick={async () => {
                                  btnLoading.value = true
                                  try {
                                    const res = await apiSaveSubtitleContent({
                                      serial_number: currentRow.value?.num,
                                      series_resource_id: +route.params.id,
                                      language_code: reference.value.language,
                                      content: formatSubtitles(code.value),
                                      audio_type: 0,
                                      alter_type: 1
                                    })
                                    currentRow.value[`${reference.value.language}_srt_path`] = res.data?.subtitle_uri || ''
                                    reference.value.code = await getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[referenceLangIndex.value]}_srt_path`]))
                                    showSuccessToast('保存成功')
                                    hideDialog()
                                  } catch (error: any) {
                                    showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                                  } finally {
                                    btnLoading.value = false
                                  }
                                }}
                                >
                                  {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                                  保存
                                </Button>
                              </div>
                            </div>
                          ),
                        })
                      }}>字幕覆盖</Button>
                      <Button class={mc('btn btn-xs ml-4', isBatchSelect.value ? 'btn-default' : 'btn-primary')} onClick={() => {
                        isBatchSelect.value = !isBatchSelect.value
                      }}>{ isBatchSelect.value ? '取消批量' : '批量选择' }</Button>
                    </div>
                  </div>
                  <div v-loading={resourceLoading.value} class="flex-1 overflow-auto">
                    <div>
                      <Subtitle
                        ref={referenceRef}
                        class="h-full"
                        code={reference.value.code}
                        onVideoProgress={(time: number) => {
                          if (videoRef.value) videoRef.value?.seek(time)
                        }}
                        showContentText={false}
                        isBatchSelect={isBatchSelect.value}
                        errors={errDetail.value}
                        warnings={subtitleFixesList.value}
                        isAsyncScroll={isAsync.value}
                        currentTime={currentTime.value}
                        emotions={emotions.value}
                        effects={effects.value}
                        emotions_cn={emotions_cn.value}
                        effects_cn={effects_cn.value}
                        rolePick={true}
                        showCharacters={true}
                        characters={characters.value}
                        annotations={annotations.value}
                        isEditable={true}
                        rtl={reference.value.code === 'ar'}
                      />
                    </div>
                  </div>
                </x-reference>
              </x-subtitle-area>
              <x-term ref="el" class="flex-1 overflow-auto h-full">
                <Term
                  tableStyle={{
                    height: `${height.value - 60}px`,
                  }}
                  loading={btnLoading.value}
                  list={characters.value}
                  onInsertRole={row => {
                    referenceRef.value.setRole(row)
                    sortRoles()
                  }}
                  onFixTop={row => {
                    const series_resource_id = route.params.id as string
                    const topFixedList = getData('FIXED_KEY_' + series_resource_id) || []
                    const fixedIndex = topFixedList.findIndex((item: M.ITerminology) => item.character_id === row.character_id)
                    if (fixedIndex > -1) {
                      topFixedList.splice(fixedIndex, 1)
                    } else {
                      topFixedList?.push({
                        ...row,
                      })
                    }
                    saveData('FIXED_KEY_' + series_resource_id, topFixedList)
                    sortRoles()
                  }}
                  onRefreshTerm={async () => {
                    await getCharacters()
                    sortRoles()
                  }}
                  onSave={async (tempList: M.ITerminology[]) => {
                    const newTermList = tempList.filter(item => !item.character_id)
                    const updatedTermList = tempList.filter(item => item.character_id && item.update)
                    const termList = newTermList.concat(updatedTermList).map(row => {
                      row.type = 1
                      row.src_type = 1
                      row.category = 'character'
                      if (row.character_id) row.term_id = row.character_id
                      return row
                    })
                    if (termList.some(item => !trim(item.term))) {
                      showFailToast('请完成术语填写或删掉不需要的术语')
                      return
                    }
                    btnLoading.value = true
                    try {
                      await apiUpdateRoleTerminology({
                        series_resource_id: +route.params,
                        terms: termList,
                      })
                      await getCharacters()
                      sortRoles()
                      showSuccessToast('保存成功')
                    } catch (error: any) {
                      showFailToast(error.response.data.message || '操作失败')
                    } finally {
                      btnLoading.value = false
                    }
                  }} />
              </x-term>
            </div>
            <x-subtitle-operation>
              {list.value.length > 0 ? (
                <div class="py-4 flex items-center justify-between pt-4">
                  <div class="flex">
                    <Button class="btn btn-outline btn-xs" disabled={(currentRow.value?.num) === (list.value[0].num) || resourceLoading.value || btnLoading.value} onClick={() => {
                      const index = list.value.findIndex(item => item.num === currentRow.value.num)
                      currentRow.value = list.value[index - 1]
                    }}
                    >上一集
                    </Button>
                    <div class="px-4">
                      <input
                        class="input input-xs input-bordered w-[60px]"
                        disabled={resourceLoading.value || btnLoading.value}
                        value={serial_number.value}
                        onInput={(e: Event) => {
                          const value = (e.target as HTMLInputElement).value
                          serial_number.value = +value
                        }}
                        onChange={(e: Event) => {
                          const value = (e.target as HTMLInputElement).value
                          const i = list.value.findIndex(item => item.num === +value)
                          if (i === -1) {
                            showFailToast('集数有误')
                            serial_number.value = currentRow.value.num
                            return
                          }
                          currentRow.value = list.value[i]
                          serial_number.value = +value
                        }}
                        type="text"
                      />
                    </div>

                    <Button class="btn btn-outline btn-xs" disabled={list.value[list.value.length - 1].num === currentRow.value.num || resourceLoading.value || btnLoading.value} onClick={() => {
                      const index = list.value.findIndex(item => item.num === currentRow.value.num)
                      currentRow.value = list.value[index + 1]
                    }}
                    >下一集
                    </Button>
                    <select value={videoType.value} class="select select-bordered select-xs ml-4 w-[100px]" onChange={(e: any) => {
                      const value = e.target.value
                      videoType.value = value
                    }}
                    >
                      <option value="pure_video_path">无字幕视频</option>
                      <option value="ora_video_path">原视频</option>
                    </select>
                    <label class="flex items-center ml-4 cursor-pointer">
                      <Checkbox
                        label=""
                        modelValue={isAsync.value}
                        onUpdate:modelValue={(value: boolean) => {
                          isAsync.value = value
                        }}
                      />
                      <span>同步滚动</span>
                    </label>

                    <Tooltip
                      ref={tooltipRef}
                      triggerType="click"
                      popWrapperClass="z-popover-in-dialog w-[460px] p-4"
                      popContent={() => (
                        <div class="flex flex-col gap-y-4">
                          <textarea value={textarea.value} class="textarea textarea-bordered textarea-sm h-[200px] w-full" placeholder="请输入备注" onChange={e => {
                            textarea.value = (e.target as HTMLTextAreaElement).value
                          }} />
                          <div class="flex justify-end gap-x-2">
                            <Button class="btn btn-ghost btn-sm" onClick={() => {
                              tooltipRef.value.toggle()
                            }}>取消</Button>
                            <Button class="btn btn-primary btn-sm " disabled={btnLoading.value} onClick={async () => {
                              btnLoading.value = true
                              try {
                                await apiUpdateMark({
                                  series_resource_id: +route.params.id,
                                  serial_number: +currentRow.value.num,
                                  content: textarea.value || '',
                                })
                                remark.value = textarea.value
                                showSuccessToast('操作成功')
                                const itemIndex = currentResource.value?.list?.findIndex(item => item.serial_number === currentRow.value.num) as number
                                currentResource.value?.list?.splice(itemIndex, 1, {
                                  ...currentResource.value?.list[itemIndex],
                                  remark: textarea.value,
                                })
                                tooltipRef.value.toggle()
                              } catch (error: any) {
                                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                              } finally {
                                btnLoading.value = false
                              }
                            }}>
                              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                              保存
                            </Button>
                          </div>

                        </div>
                      )}>
                      <Button class="btn btn-link btn-xs">备注</Button>
                    </Tooltip>
                    {videoUrl.value ? <Button class="btn btn-link btn-xs" onClick={onCaptureImage}>截图</Button> : null}

                  </div>
                  <div>
                    <Button class="btn btn-primary btn-sm" disabled={btnLoading.value || resourceLoading.value || btnDisabled.value} onClick={async () => {
                      const _list = referenceRef.value.getContent() as Api.RoleMark.ISubtitleLine[]
                      const list = _list.map((item, index) => {
                        item.order = index + 1
                        return item
                      })

                      const formatCode = getFormatSubtitle(list)
                      btnLoading.value = true
                      const result: any[] = []

                      list.map((item, index: number) => {
                        const selectedCharacters = item.selectedCharacters || []
                        selectedCharacters.map(item => {
                          const curCharacter = characters.value.find(o => o.character_id === item.character_id)
                          result.push({
                            character_id: item.character_id,
                            ...item,
                            ...curCharacter,
                            order: index + 1,
                          })
                        })
                      })
                      try {
                        const res = await apiSaveSubtitleContent({
                          serial_number: currentRow.value?.num,
                          series_resource_id: +route.params.id,
                          language_code: reference.value.language,
                          content: formatCode,
                          audio_type: 0,
                          alter_type: 1
                        })
                        currentRow.value[`${reference.value.language}_srt_path`] = res.data?.subtitle_uri || ''

                        await apiSaveSingleSubtitleRole({
                          series_resource_id: +route.params.id,
                          serial_number: currentRow.value.num,
                          annotations: result.map(o => {
                            return {
                              order: o.order,
                              character_id: o.character_id,
                              character_type: o.character_type || 0,
                              emotion: o.emotion,
                              effect: o.effect,
                            }
                          }),
                        })
                        annotations.value = result
                        showSuccessToast('保存成功')
                      } catch (error: any) {
                        showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                      } finally {
                        btnLoading.value = false
                      }
                    }}>
                      {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                      保存字幕
                    </Button>
                  </div>
                </div>
              ) : null}
            </x-subtitle-operation>
          </div>
        ) : (
          <div class="w-[100vw] h-[100vh] flex items-center justify-center">
            <Icon name="line-md:loading-twotone-loop" class="size-5" />
            <div class="text-lg text-[var(--text-1)]">
              加载中……
            </div>
          </div>
        )
      }
    </x-inspection-role-detail-page>
  )
})

export default InspectionRoleDetailPage
