import { createComponent, mc } from '@skynet/shared'
import { background } from 'src/modules/coins/images/images'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import DefaultCover from 'src/h5_modules/images/cover-def.webp'

// export const DefaultCover = 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png'

type ThreeColumnDramaCardOptions = {
  props: {
    drama: M.Drama
  }
}

export const ThreeColumnDramaCard = createComponent<ThreeColumnDramaCardOptions>({
  props: {
    drama: {},
  },
}, props => {
  const cover = ref<string>(props.drama.cover || '')
  const loadingCover = ref<boolean>(false)

  /** Free＞Following＞Hot＞New＞Dubbed */
  const tag = computed(() => {
    if (!props.drama.tag || props.drama.tag.length <= 0) {
      return ''
    }

    if (props.drama.tag.includes('Free')) {
      return 'Free'
    }

    if (props.drama.tag.includes('Following')) {
      return 'Following'
    }

    if (props.drama.tag.includes('Hot')) {
      return 'Hot'
    }

    if (props.drama.tag.includes('Dubbed')) {
      return 'Dubbed'
    }

    return 'Free'
  })

  const router = useRouter()
  const gotoPaly = () => {
    // void router.push('/series/' + props.drama.key)
    window.location.href = window.location.origin + '/series/' + props.drama.key + '?return_to=/&from_page=home'
  }

  return () => (
    <x-three-column-drama-card class="block overflow-hidden" onClick={gotoPaly}>
      <div class="relative block aspect-[112/150.3] w-full overflow-hidden rounded-lg">
        <img
          class={mc('size-full object-cover rounded-lg', loadingCover.value ? 'opacity-0' : '')}
          src={props.drama.cover || DefaultCover}
          onLoad={() => loadingCover.value = false}
          onError={() => cover.value = DefaultCover}
        />
        {
          !!tag.value && (
            <x-three-column-drama-top-tag class="px-1.25 py-.5 leading-3.25 text-3 rounded-bl-1 rounded-tr-1 absolute right-0 top-0 block text-xs text-[#FFFFFF]"
              style={{
                backgroundImage: tag.value === 'Free' ? `linear-gradient(270deg, #04b587 0.05%, #46d988 100%)` : 'linear-gradient(90deg, #F47040 0%, #F52067 100%)',
              }}
            >
              {tag.value}
            </x-three-column-drama-top-tag>
          )
        }

        {props.drama.content_tags && props.drama.content_tags.length > 0 && (
          <x-three-column-drama-bottom-tag-list class="p-.75 absolute bottom-0 left-0 flex w-full gap-x-1 overflow-hidden">
            {
              props.drama.content_tags.map(item => (
                <x-three-column-drama-bottom-tag
                  class="rounded-1 text-2.4 px-.75 pb-.25 max-w-full truncate leading-3 text-[--white]"
                  style={{
                    background: 'rgba(66, 68, 70, 0.75)',
                  }}
                  key={item}>
                  {item}
                </x-three-column-drama-bottom-tag>
              ))
            }
          </x-three-column-drama-bottom-tag-list>
        )}

      </div>

      <x-three-column-drama-title class="max-h-10.25 text-3.25 line-clamp-2 w-full pb-1 pt-1.5 leading-4 text-[#cccacb]">{props.drama.title}</x-three-column-drama-title>
    </x-three-column-drama-card>
  )
})

export default ThreeColumnDramaCard
