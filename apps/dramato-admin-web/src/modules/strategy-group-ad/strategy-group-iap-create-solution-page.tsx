/* eslint-disable @typescript-eslint/no-explicit-any */
import { CirclePlus, Remove } from '@element-plus/icons-vue'
import { createComponent, getMessages, mc } from '@skynet/shared'
import { Button, CreateTableOld, DialogFooter, Icon, openDialog, showAlert, TableColumnOld, transformInteger, transformNumber, transformNumber3 } from '@skynet/ui'
import { FormOptions } from '@skynet/ui/form/form-types'
import { ElIcon, ElInput, ElMessage, ElOption, ElSelect, ElSwitch } from 'element-plus'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { AdvertisePage } from '../advertise/advertise-page'
import { adSceneMap, adSceneOptions } from './const'
import { apiSaveStrategyGroupAds } from './strategy-group-ad-api'
import { useStrategyGroupAdStore } from './strategy-group-ad-store'
import { useListParamsStore } from './use-list-params-store'

export const StrategyGroupIapCreateSolutionPage = createComponent(null, () => {
  const { formData, Form, stepOneError, stepErrorForSolution, validateAllStepsForIapNew, resetTaskFormData } = useStrategyGroupAdStore()

  const router = useRouter()
  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')

  const WatchAdTable = CreateTableOld<M.RewardsConfig.WatchAd & { reward_coins?: number }>()
  const checkedAdvertiseItem = ref<M.Advertise[]>([])
  const checkedAdvertiseItemForRewardWatchAd = ref<M.RewardsConfig.WatchAd[]>([])
  const currentAdvertiseList = ref<M.Advertise[]>([])

  const watchAdColumns: TableColumnOld<M.RewardsConfig.WatchAd & { reward_coins?: number }>[] = [
    ['序号', (row, index) => (
      <input
        type="number"
        class="input input-sm input-bordered h-8 w-20"
        value={row.sort_no ?? index + 1}
        onInput={(e: Event) => {
          if (!formData.value.ad_list) {
            formData.value.ad_list = []
          }
          formData.value.ad_list[index].sort_no = Number((e.target as HTMLInputElement).value) || 0
        }}
        onBlur={() => {
          formData.value.ad_list = formData.value.ad_list.sort((a, b) => {
            return a.sort_no - b.sort_no
          })

          console.log('formData.value.ad_list', formData.value.ad_list)
        }}
      />
    ), { class: 'w-[100px]' }],
    ['广告ID', 'ad_id', { class: 'w-[100px]' }],
    ['广告位名称', 'ad_name', { class: 'w-[200px]' }],
    ['应用端', 'platform', { class: 'w-[100px]' }],
    ['广告组', 'ad_unit', { class: 'w-[300px]' }],
    ['聚合平台', 'ad_platform', { class: 'w-[100px]' }],
    ['广告位类型', 'ad_type', { class: 'w-[100px]' }],
    ['操作', row => {
      return (
        <Button class="btn btn-outline btn-xs" onClick={() => deleteWatchAd(row.ad_id)}>删除</Button>
      )
    }, { class: 'w-[80px] text-center' }],
  ]

  const showImportWatchAdDialog = (platform: string) => {
    checkedAdvertiseItemForRewardWatchAd.value = []
    const hide = openDialog({
      title: '导入广告',
      customClass: '!w-2/3',
      mainClass: '[&_.hide-when-in-dialog]:hidden [&_x-hide-when-in-dialog]:hidden [&_x-show-when-in-dialog]:!block pb-0 px-5',
      body: computed(() => (
        <x-import-advertise class="relative">
          <AdvertisePage
            onAdd={advertise => {
              // 这里要转一次，因为AdvertisePage的advertise是M.Advertise类型，而formData.value.check_in.extra是M.RewardsConfig.CheckInExtra类型
              checkedAdvertiseItemForRewardWatchAd.value.push({
                ad_id: advertise.id!,
                ad_group: advertise.ad_unit,
                ad_name: advertise.name,
                type: advertise.ad_type,
                mediation_mode: advertise.mediation,
                limit: advertise.count_limit!,
                duration: advertise.period,
                coins: advertise.coins!,
                reward_coins: 0,
                ad_type: advertise.ad_type,
                ad_platform: advertise.ad_platform,
                platform: advertise.platform,
                id: (advertise.id || 0) + Date.now(),
                ad_unit: advertise.ad_unit,
              })
            }}
            onRemove={advertise => {
              checkedAdvertiseItemForRewardWatchAd.value = checkedAdvertiseItemForRewardWatchAd.value.filter(item => item.ad_id !== advertise.id)
            }}
            platform={platform}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm"
              onClick={() => {
                if (!formData.value.ad_list) {
                  formData.value.ad_list = []
                }
                formData.value.ad_list = (formData.value.ad_list ?? []).concat(checkedAdvertiseItemForRewardWatchAd.value.map((item, index) => ({
                  ...item,
                  reward_coins: item.reward_coins ?? 0,
                  sort_no: index + 1,
                })))
                hide()
              }}
            >确定
            </Button>
          </footer>
        </x-import-advertise>
      )),
    })
  }

  const deleteWatchAd = (id: number) => {
    console.log('>>>', id)
    const index = formData.value.ad_list?.findIndex(item => item.ad_id === id)
    console.log('>>>', index)
    if (index !== -1) {
      formData.value.ad_list?.splice(index, 1)
    }
  }

  const onSubmit = async () => {
    if (!validateAllStepsForIapNew()) {
      openDialog({
        title: '出错提示',
        body: (
          <div>
            {getMessages(stepOneError.value)} <br />
            {getMessages(stepErrorForSolution.value)}
          </div>
        ),
      })
      return
    }
    if (formData.value.ad_list?.length === 0) {
      ElMessage.error('请选择配置广告')
      return
    }
    if (formData.value.ad_list?.length > 0) {
      if (formData.value.platform == 'ios') {
        formData.value.ad_list = formData.value.ad_list.filter(item => item.platform == 'ios')
        if (formData.value.ad_list.length == 0) {
          ElMessage.error('请选择配置广告')
          return
        }
      } else if (formData.value.platform == 'android') {
        formData.value.ad_list = formData.value.ad_list.filter(item => item.platform == 'android')
        if (formData.value.ad_list.length == 0) {
          ElMessage.error('请选择配置广告')
          return
        }
      } else {
        const andList = formData.value.ad_list.filter(item => item.platform == 'android')
        const iosList = formData.value.ad_list.filter(item => item.platform == 'ios')
        if (andList.length == 0) {
          ElMessage.error('请选择Android广告')
          return
        }
        if (iosList.length == 0) {
          ElMessage.error('请选择IOS广告')
          return
        }
      }
    }
    if (!formData.value.scene?.includes('app_open')) {
      delete formData.value.app_open_config
    }
    if (!formData.value.scene?.includes('drama_free')) {
      delete formData.value.drama_free_config
    }
    if (!formData.value.scene?.includes('reward')) {
      delete formData.value.reward_config
    }
    if (!formData.value.scene?.includes('quit_player')) {
      delete formData.value.quit_player_config
    }
    if (!formData.value.scene?.includes('drama_lock')) {
      formData.value = {
        ...formData.value,
        reward_ads: {
          list: [],
          interstitial_config: {
            show_guide: false,
          },
        },
      }
    }
    if (formData.value?.reward_config?.strategy?.cd_time < 0) {
      ElMessage.error('reward任务广告CD时间请输入大于0的值')
      return
    }
    for (const item of formData.value.reward_config?.strategy?.task || []) {
      item.coins = Number(item.coins)
    }
    const relation = ['app_open', 'drama_free', 'reward', 'drama_lock', 'quit_player']
    const relationMap = {
      app_open: 'app_open_config',
      drama_free: 'drama_free_config',
      reward: 'reward_config',
      drama_lock: 'reward_ads',
      quit_player: 'quit_player_config',
    }
    for (const item of relation || []) {
      if (!formData.value.scene?.includes(item)) {
        continue
      }
      if (item === 'reward') {
        if (formData.value[relationMap[item]]?.ad_list_new?.length == 0) {
          ElMessage.error('请选择广告位支持广告类型和聚合平台')
          return
        }
        for (const inItem of formData.value[relationMap[item]]?.ad_list_new || []) {
          if (inItem.ad_platforms.length === 0 || inItem.ad_type === '') {
            ElMessage.error('请选择广告位支持广告类型和聚合平台')
            return
          }
        }
      } else {
        if (formData.value[relationMap[item]]?.ad_list_new?.length == 0) {
          ElMessage.error('请选择广告位支持广告类型和聚合平台')
          return
        }
        for (const inItem of formData.value[relationMap[item]]?.ad_list_new || []) {
          if (inItem.ad_platforms.length === 0 || inItem.ad_type === '') {
            ElMessage.error('请选择广告位支持广告类型和聚合平台')
            return
          }
          for (const repItem of inItem.replace_ad || []) {
            if (repItem.ad_type.length === 0 || repItem.ad_platforms.length === 0) {
              ElMessage.error('请选择广告位补位配置')
              return
            }
          }
        }
      }
    }
    console.log('formData.value', formData.value)
    const response = await apiSaveStrategyGroupAds({
      ...formData.value,
      save_type: 1,
      pay_mode: route.path.includes('iaa') ? 'IAA' : 'IAP',
    }).catch((err: any) => {
      console.log('err', err)

      showAlert(err.response.data.err_msg || err.response.data.message, 'error')
    })
    if (!response.data) return
    formData.value = response.data
    formatConfigOpen()
    setTimeout(() => {
      void router.push({ path: route.path, query: { ...route.query, has_edit: 'false' } }).then(() => {
        const closeDialog = openDialog({
          title: '提示',
          body: (
            <div>保存成功
              <DialogFooter okText="返回列表页" onOk={() => {
                closeDialog()
                const { listParams, initListParams } = useListParamsStore()
                listParams.value = { ...initListParams }
                void router.push('/ad-strategy-group/iap')
              }} cancelText="留在当前页" onCancel={() => closeDialog()}
              />
            </div>
          ),
        })
      })
    })
  }

  const formatConfigOpen = () => {
    const keyArr = ['app_open_config', 'drama_free_config', 'reward_config', 'reward_ads', 'quit_player_config', 'out_flow_config']
    for (const i in keyArr) {
      if (!formData.value[keyArr[i]]) continue
      formData.value[keyArr[i]]!.ad_list_new = formData.value[keyArr[i]]!.ad_list_new || []
      const childList = formData.value[keyArr[i]]!.ad_list_new || []
      if (childList.length === 0) {
        formData.value[keyArr[i]]?.ad_list_new.push({
          ad_type: '',
          ad_platforms: [],
          ad_rep_open: false,
          replace_ad: [],
        })
      }
      for (const item of formData.value[keyArr[i]]?.ad_list_new) {
        if (item.replace_ad.length > 0) {
          item.ad_rep_open = true
        }
      }
    }
    if (formData.value?.reward_config && !formData.value?.reward_config?.strategy?.task) {
      formData.value.reward_config.strategy = {
        cd_time: 0,
        task: [{
          sort_no: 1,
          coins: 0,
        }],
      }
    }
  }

  const dramaLockItems = computed(() => [
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 广告控制参数 </h2>,
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 面板后--解锁激励控制参数 </h2>,
    [
      '锁定蒙层样式',
      'show_unlock_button',
      {
        type: 'radio',
        options: [{
          value: '0',
          label: '默认样式',
        }, {
          value: '1',
          label: '仅展示广告解锁按钮',
        }],
      },
      {
        transform: transformNumber,
      },
    ],
    [
      '广告频次限制类型',
      'count_limit_type',
      {
        type: 'radio',
        options: [{
          value: '1',
          label: '每人/每天/每部剧维度',
        }, {
          value: '2',
          label: '每人/每天维度',
        }],
      },
      {
        transform: transformNumber,
      },
    ],
    [
      '观看广告次数上限',
      'count_limit',
      { type: 'number', min: 0, max: 500, suffix: '天' },
      { transform: transformNumber, hint: '输入范围0-500，仅支持整数' },
    ],
    [
      ['解锁剧集所需广告次数', 'watch_ad_num', { type: 'number', min: 0, max: 100, suffix: '次' }, { transform: transformNumber, hint: '输入范围0-100，仅支持整数' }],
      ['解锁集数', 'unlock_num', { type: 'number', min: 0, suffix: '集' }, { transform: transformNumber }],
      formData.value.count_limit_type === 1 && ['最后N集不支持当天解锁（选填）', 'last_lock_episode', { type: 'number', min: 0, suffix: '集', step: 1 }, { transform: transformNumber, hint: '输入范围1-100，仅支持整数' }],
    ].filter(Boolean),
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 面板后--插屏控制参数 </h2>,
    [
      'CD时间（0-99999）',
      'reward_ads.interstitial_config.cooling_time',
      { type: 'number', min: 0, max: 99999, suffix: 's' },
      { transform: transformNumber, hint: '输入范围0-99999，仅支持整数' },
    ],
    [
      '频次上限（0-99999）',
      'reward_ads.interstitial_config.watch_ad_count',
      { type: 'number', min: 0, max: 99999 },
      { transform: transformNumber, hint: '次/天，输入范围0-99999，仅支持整数' },
    ],
    [
      '集间插屏是否展示引导页',
      'reward_ads.interstitial_config.show_guide',
      {
        type: 'radio',
        options: [
          {
            value: false,
            label: '无引导',
          },
          {
            value: true,
            label: '有引导',
          },
        ],
      },
    ],
    <h2 class="col-span-2 mb-4 text-lg"> 集间插屏触发逻辑<span class="text-xs">（为空不生效，填写多个为或的关系，满足其一即生效）</span> </h2>,
    [
      '单天单部剧使用赠币解锁N集（1-99999）',
      'reward_ads.interstitial_config.reward_unlock_episodes',
      { type: 'number', min: 1, max: 99999 },
      { transform: transformNumber3 },
    ],
    [
      '单天单部剧使用广告解锁N集（1-99999）',
      'reward_ads.interstitial_config.ad_unlock_episodes',
      { type: 'number', min: 1, max: 99999 },
      { transform: transformNumber3 },
    ],
    [
      '单天单部剧使用金币解锁N集（1-99999）',
      'reward_ads.interstitial_config.balance_unlock_episodes',
      { type: 'number', min: 1, max: 99999 },
      { transform: transformNumber3 },
    ],
    [
      '单天单部剧总共解锁N集（1-99999）',
      'reward_ads.interstitial_config.all_unlock_episodes',
      { type: 'number', min: 1, max: 99999 },
      { transform: transformNumber3 },
    ],
  ] as FormOptions<FormData>['props']['items'])

  const dramaFreeItems = computed(() => [
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 广告控制参数 </h2>,
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 集间广告控制参数 </h2>,
    [
      requiredLabel('广告开始展示剧集'),
      'drama_free_config.strategy.start',
      { type: 'number', min: 0 },
      { transform: transformNumber, hint: '第N集开始插入广告' },
    ],
    [
      requiredLabel('间隔剧集'),
      'drama_free_config.strategy.skip',
      { type: 'number', min: 0 },
      { transform: transformNumber, hint: '间隔N集插入广告' },
    ],
    [
      requiredLabel('间隔时间'),
      'drama_free_config.strategy.watch_time',
      { type: 'number', min: 0, suffix: 's' },
      { transform: transformNumber },
    ],
    [
      '原生广告强制观看时长（仅控制原生广告）',
      'drama_free_config.strategy.ad_force_time',
      { type: 'number', min: 0, suffix: 's' },
      { transform: transformNumber },
    ],
    [
      '',
      'drama_free_config.strategy.reward_type',
      { type: 'radio', options: [{ label: '无激励视频', value: 'none' }, { label: '免广告激励视频', value: 'no_ads' }, { label: '解锁激励视频', value: 'unlock' }] },
    ],
    ...(formData.value.drama_free_config?.strategy?.reward_type === 'none' ? [] : formData.value.drama_free_config?.strategy?.reward_type === 'no_ads' ? [
      [
        '看第几次广告弹出激励视频弹窗',
        'drama_free_config.strategy.show_reward_ad_native_ad_count',
        { type: 'number', min: 0 },
        { transform: transformNumber },
      ],
      [
        '看激励视频免广告时间',
        'drama_free_config.strategy.skip_native_ad_time_ms',
        { type: 'number', min: 0, suffix: 'min' },
        { transform: transformNumber },
      ],
      [
        '连续几次不看激励广告免广告关闭弹窗（可输入0-999）',
        'drama_free_config.strategy.ad_switch_interval',
        { type: 'number', min: 0, suffix: '次' },
        { transform: transformNumber },
      ],
    ] : [
      [
        '解锁剧集所需广告次数',
        'drama_free_config.strategy.unlock_nums',
        { type: 'number', min: 0, suffix: '次' },
        { transform: transformNumber, hint: '输入范围0-100，仅支持整数' },
      ],
    ]),
    ['起始广告', 'drama_free_config.strategy.ad_type_start', {
      type: 'radio',
      options: [
        { label: '插屏广告', value: 'interstitial', disabled: isViewMode.value },
        { label: '原生广告', value: 'native', disabled: isViewMode.value },
        { label: '激励广告', value: 'rewarded', disabled: isViewMode.value },
        { label: 'H5', value: 'h5', disabled: true || isViewMode.value },
      ],
    }],
    [
      '广告类型间gap',
      'drama_free_config.strategy.ad_gap',
      {
        type: 'custom',
        render: () => (
          <x-gap-group class="flex flex-col gap-y-2">
            {
              formData.value.drama_free_config?.strategy?.ad_gap?.map((item, index) => {
                return (
                  <x-gap-item class="flex flex-row items-center gap-x-2">
                    <select
                      class="select select-bordered select-sm w-[120px]"
                      value={item.ad_type}
                      onInput={(e: Event) => {
                        item.ad_type = (e.target as HTMLInputElement).value || ''
                      }}
                    >
                      {[{ label: '激励广告', value: 'rewarded' }, { label: '插屏广告', value: 'interstitial' }, { label: '原生广告', value: 'native' }, { label: 'H5', value: 'h5' }].map(option => (
                        <option value={option.value}>{option.label}</option>
                      ))}
                    </select>
                    数量
                    <div class="w-[120px]">
                      <input
                        type="text"
                        class={mc('grow input input-bordered flex items-center gap-1 h-8 w-[120px]')}
                        value={item.ad_count}
                        onInput={(e: Event) => {
                          item.ad_count = Number((e.target as HTMLInputElement).value || '')
                        }}
                      />
                    </div>
                    <Icon name="ant-design:delete-filled" onClick={() => {
                      if (!formData.value.drama_free_config) {
                        formData.value.drama_free_config = {}
                      }
                      if (!formData.value.drama_free_config.strategy) {
                        formData.value.drama_free_config.strategy = {}
                      }
                      if (!formData.value.drama_free_config.strategy.ad_gap) {
                        formData.value.drama_free_config.strategy.ad_gap = []
                      }
                      formData.value.drama_free_config.strategy.ad_gap = formData.value.drama_free_config.strategy.ad_gap.filter((_, i) => i !== index)
                    }}
                    />
                  </x-gap-item>
                )
              })
            }
            <Button class="btn btn-outline btn-sm  w-[160px]" onClick={() => {
              if (!formData.value.drama_free_config) {
                formData.value.drama_free_config = {}
              }
              if (!formData.value.drama_free_config.strategy) {
                formData.value.drama_free_config.strategy = {}
              }
              if (!formData.value.drama_free_config.strategy.ad_gap) {
                formData.value.drama_free_config.strategy.ad_gap = []
              }
              formData.value.drama_free_config.strategy.ad_gap.push({ ad_type: '', ad_count: 0 })
            }}
            >新增广告类型间gap
            </Button>
          </x-gap-group>
        )
        ,
      },
    ],
  ] as FormOptions<FormData>['props']['items'])

  const outFlowItems = computed(() => [
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 广告控制参数 </h2>,
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 集间广告控制参数 </h2>,
    [
      requiredLabel('广告开始展示剧集'),
      'out_flow_config.strategy.start',
      { type: 'number', min: 0 },
      { transform: transformNumber, hint: '第N集开始插入广告' },
    ],
    [
      requiredLabel('间隔剧集'),
      'out_flow_config.strategy.skip',
      { type: 'number', min: 0 },
      { transform: transformNumber, hint: '间隔N集插入广告' },
    ],
    [
      requiredLabel('间隔时间'),
      'out_flow_config.strategy.watch_time',
      { type: 'number', min: 0, suffix: 's' },
      { transform: transformNumber },
    ],
    [
      '原生广告强制观看时长（仅控制原生广告）',
      'out_flow_config.strategy.ad_force_time',
      { type: 'number', min: 0, suffix: 's' },
      { transform: transformNumber },
    ],
    [
      '',
      'out_flow_config.strategy.reward_type',
      { type: 'radio', options: [{ label: '无激励视频', value: 'none' }, { label: '免广告激励视频', value: 'no_ads' }, { label: '解锁激励视频', value: 'unlock' }] },
    ],
    ...(formData.value.out_flow_config?.strategy?.reward_type === 'none' ? [] : formData.value.out_flow_config?.strategy?.reward_type === 'no_ads' ? [
      [
        '看第几次广告弹出激励视频弹窗',
        'out_flow_config.strategy.show_reward_ad_native_ad_count',
        { type: 'number', min: 0 },
        { transform: transformNumber },
      ],
      [
        '看激励视频免广告时间',
        'out_flow_config.strategy.skip_native_ad_time_ms',
        { type: 'number', min: 0, suffix: 'min' },
        { transform: transformNumber },
      ],
      [
        '连续几次不看激励广告免广告关闭弹窗（可输入0-999）',
        'out_flow_config.strategy.ad_switch_interval',
        { type: 'number', min: 0, suffix: '次' },
        { transform: transformNumber },
      ],
    ] : [
      [
        '解锁剧集所需广告次数',
        'out_flow_config.strategy.unlock_nums',
        { type: 'number', min: 0, suffix: '次' },
        { transform: transformNumber, hint: '输入范围0-100，仅支持整数' },
      ],
    ]),
    ['起始广告', 'out_flow_config.strategy.ad_type_start', {
      type: 'radio',
      options: [
        { label: '插屏广告', value: 'interstitial', disabled: isViewMode.value },
        { label: '原生广告', value: 'native', disabled: isViewMode.value },
        { label: '激励广告', value: 'rewarded', disabled: isViewMode.value },
      ],
    }],
    [
      '广告类型间gap',
      'out_flow_config.strategy.ad_gap',
      {
        type: 'custom',
        render: () => (
          <x-gap-group class="flex flex-col gap-y-2">
            {
              formData.value.out_flow_config?.strategy?.ad_gap?.map((item, index) => {
                return (
                  <x-gap-item class="flex flex-row items-center gap-x-2">
                    <select
                      class="select select-bordered select-sm w-[120px]"
                      value={item.ad_type}
                      onInput={(e: Event) => {
                        item.ad_type = (e.target as HTMLInputElement).value || ''
                      }}
                    >
                      {[{ label: '激励广告', value: 'rewarded' }, { label: '插屏广告', value: 'interstitial' }, { label: '原生广告', value: 'native' }].map(option => (
                        <option value={option.value}>{option.label}</option>
                      ))}
                    </select>
                    数量
                    <div class="w-[120px]">
                      <input
                        type="text"
                        class={mc('grow input input-bordered flex items-center gap-1 h-8 w-[120px]')}
                        value={item.ad_count}
                        onInput={(e: Event) => {
                          item.ad_count = Number((e.target as HTMLInputElement).value || '')
                        }}
                      />
                    </div>
                    <Icon name="ant-design:delete-filled" onClick={() => {
                      if (!formData.value.out_flow_config) {
                        formData.value.out_flow_config = {}
                      }
                      if (!formData.value.out_flow_config.strategy) {
                        formData.value.out_flow_config.strategy = {}
                      }
                      if (!formData.value.out_flow_config.strategy.ad_gap) {
                        formData.value.out_flow_config.strategy.ad_gap = []
                      }
                      formData.value.out_flow_config.strategy.ad_gap = formData.value.out_flow_config.strategy.ad_gap.filter((_, i) => i !== index)
                    }}
                    />
                  </x-gap-item>
                )
              })
            }
            <Button class="btn btn-outline btn-sm  w-[160px]" onClick={() => {
              if (!formData.value.out_flow_config) {
                formData.value.out_flow_config = {}
              }
              if (!formData.value.out_flow_config.strategy) {
                formData.value.out_flow_config.strategy = {}
              }
              if (!formData.value.out_flow_config.strategy.ad_gap) {
                formData.value.out_flow_config.strategy.ad_gap = []
              }
              formData.value.out_flow_config.strategy.ad_gap.push({ ad_type: '', ad_count: 0 })
            }}
            >新增广告类型间gap
            </Button>
          </x-gap-group>
        )
        ,
      },
    ],
  ] as FormOptions<FormData>['props']['items'])

  const openItems = computed(() => [
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 广告控制参数 </h2>,
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 开屏控制参数 </h2>,
    [
      'CD时间（0-99999）',
      'app_open_config.strategy.cooling_time',
      { type: 'number', min: 0, max: 99999, suffix: 's' },
      { transform: transformNumber },
    ],
    [
      '频次上限（0-99999）',
      'app_open_config.strategy.watch_ad_count',
      { type: 'number', min: 0, max: 99999 },
      { transform: transformNumber, hint: '次/天' },
    ],
  ] as FormOptions<FormData>['props']['items'])

  const rewardItems = computed(() => [

    <h2 class="col-span-2 mb-4 text-lg font-bold"> reward任务广告控制参数 </h2>,
    [
      'CD时间（输入整数0-99999）',
      'reward_config.strategy.cd_time',
      { type: 'number', min: 0, max: 99999, suffix: 's' },
      { transform: transformInteger, class: 'w-[320px]' },
    ],
    ['任务数量与奖励金额', 'reward_config.strategy.task', {
      type: 'custom',
      render: () => {
        return (
          <section class="border-1 rounded-lg border border-solid p-4">
            <div class="flex items-center gap-x-4">
              <div class="w-[80px]">任务排序</div>
              <div class="w-[220px]">奖励金币<span class="text-sm text-gray-500">（输入整数0-99999）</span></div>
            </div>
            {(formData.value?.reward_config?.strategy?.task || []).map((item, index) => {
              return (
                <div class="my-1">
                  <div class="flex gap-x-4">
                    <div class="w-[80px]">
                      <ElInput type="text" v-model={item.sort_no} disabled />
                    </div>
                    <div class="flex w-[220px] items-center gap-x-1">
                      <div class="flex items-center gap-x-1">
                        <ElInput onChange={() => {
                          item.coins = Number(parseInt(item.coins || '0'))
                          if (item.coins < 0) {
                            item.coins = 0
                          }
                          if (isNaN(item.coins)) {
                            item.coins = 0
                          }
                        }} type="text" v-model={item.coins} class="w-[220px]" /><span>coins</span>
                      </div>
                      {index === 0 && (
                        <ElIcon>
                          <CirclePlus
                            class="cursor-pointer text-3xl text-green-600"
                            onClick={() => {
                              formData.value?.reward_config?.strategy?.task.push({
                                sort_no: formData.value?.reward_config?.strategy?.task.length + 1,
                                coins: 20,
                              })
                            }}
                          />
                        </ElIcon>
                      )}
                      {index > 0 && (
                        <ElIcon>
                          <Remove
                            class="cursor-pointer text-3xl text-red-600"
                            onClick={() => {
                              formData.value?.reward_config?.strategy?.task.splice(index, 1)
                              formData.value?.reward_config?.strategy?.task.forEach((item, index) => {
                                item.sort_no = index + 1
                              })
                            }}
                          />
                        </ElIcon>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </section>
        )
      },
    }, { class: 'w-full' }],
  ] as FormOptions<FormData>['props']['items'])

  const quitPlayerItems = computed(() => [
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 广告控制参数 </h2>,
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 退出插屏控制参数 </h2>,
    [
      'CD时间（0-99999）',
      'quit_player_config.strategy.cooling_time',
      { type: 'number', min: 0, max: 99999, suffix: 's' },
      { transform: transformNumber },
    ],
    [
      '频次上限（0-99999）',
      'quit_player_config.strategy.watch_ad_count',
      { type: 'number', min: 0, max: 99999 },
      { transform: transformNumber, hint: '次/天' },
    ],
    [
      '单天第N次退出播放器开始播放插屏（1-99999）',
      'quit_player_config.strategy.quit_player_count',
      { type: 'number', min: 1, max: 99999 },
      { transform: transformNumber3 },
    ],
  ] as FormOptions<FormData>['props']['items'])

  const rewardAdTypeOptions = computed(() => [

    <div class="mb-4 flex items-center gap-x-2"><h2 class="col-span-2 text-lg font-bold"> 广告位支持广告类型和聚合平台 </h2><span class="text-sm text-red-600">(勾选类型前需确认客户端是否支持)</span></div>,
    ['', 'ad_id', {
      type: 'custom',
      render: () => {
        return (
          <section class="border-1 rounded-lg border border-solid p-4">
            <div class="flex gap-x-4">
              <div class="w-[160px]">广告类型</div>
              <div class="w-[220px]">聚合平台</div>
              <div class="w-[160px]">是否补位</div>
            </div>
            {(formData.value?.reward_config?.ad_list_new || []).map((item, index) => {
              return (
                <div class="my-1">
                  <div class="flex gap-x-4">
                    <div class="w-[160px]">
                      <ElSelect v-model={item.ad_type} class="w-[160px]">
                        <ElOption label="激励" value="rewarded">激励</ElOption>
                        <ElOption label="插屏" value="interstitial">插屏</ElOption>
                        <ElOption label="原生" value="native">原生</ElOption>
                        <ElOption label="开屏" value="app_open">开屏</ElOption>
                        <ElOption label="h5" value="h5">h5</ElOption>
                      </ElSelect>
                    </div>
                    <div class="flex w-[220px] items-center gap-x-1">
                      <ElSelect multiple v-model={item.ad_platforms} class="w-[220px]">
                        <ElOption value="admob">admob</ElOption>
                        <ElOption value="max">max</ElOption>
                        <ElOption value="meta">meta</ElOption>
                        <ElOption value="netbits">netbits</ElOption>
                      </ElSelect>
                      {index === 0 && (
                        <ElIcon>
                          <CirclePlus
                            class="cursor-pointer text-3xl text-green-600"
                            onClick={() => {
                              if (formData.value?.reward_config?.ad_list_new.length < 5) {
                                formData.value?.reward_config?.ad_list_new.push({
                                  ad_type: '',
                                  ad_platforms: [],
                                  replace_ad: [],
                                })
                              }
                            }}
                          />
                        </ElIcon>
                      )}
                      {index > 0 && (
                        <ElIcon>
                          <Remove
                            class="cursor-pointer text-3xl text-red-600"
                            onClick={() => {
                              formData.value?.reward_config?.ad_list_new.splice(index, 1)
                            }}
                          />
                        </ElIcon>
                      )}
                    </div>
                    <div class="w-[160px]">
                      <ElSwitch v-model={item.ad_rep_open} onChange={e => {
                        if (e) {
                          item.replace_ad.push({
                            ad_type: '',
                            ad_platforms: [],
                          })
                        } else {
                          item.replace_ad = []
                        }
                      }} />
                    </div>
                  </div>
                  {item.replace_ad.length > 0 && (
                    <div class="flex items-center gap-x-4">
                      <div class="text-[12px] text-gray-500">补位配置</div>

                      <div class="my-1 w-[320px]">
                        {item.replace_ad.map((inItem, inIndex) => {
                          return (
                            <div class="my-1 flex items-center gap-x-1">
                              <ElSelect v-model={inItem.ad_type} class="w-[220px]">
                                <ElOption label="激励" value="rewarded">激励</ElOption>
                                <ElOption label="插屏" value="interstitial">插屏</ElOption>
                                <ElOption label="原生" value="native">原生</ElOption>
                                <ElOption label="开屏" value="app_open">开屏</ElOption>
                                <ElOption label="h5" value="h5">h5</ElOption>
                              </ElSelect>

                              <ElSelect multiple v-model={inItem.ad_platforms} class="w-[220px]">
                                <ElOption value="admob">admob</ElOption>
                                <ElOption value="max">max</ElOption>
                                <ElOption value="meta">meta</ElOption>
                                <ElOption value="netbits">netbits</ElOption>
                              </ElSelect>
                              {inIndex === 0 && (
                                <ElIcon>
                                  <CirclePlus
                                    class="cursor-pointer text-3xl text-green-600"
                                    onClick={() => {
                                      if (item.replace_ad.length < 5) {
                                        item.replace_ad.push({
                                          ad_type: '',
                                          ad_platforms: [],
                                        })
                                      }
                                    }}
                                  />
                                </ElIcon>
                              )}
                              {inIndex > 0 && (
                                <ElIcon>
                                  <Remove
                                    class="cursor-pointer text-3xl text-red-600"
                                    onClick={() => {
                                      item.replace_ad.splice(inIndex, 1)
                                    }}
                                  />
                                </ElIcon>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </section>
        )
      },
    }, { class: 'w-full' }],
  ] as FormOptions<FormData>['props']['items'])

  const appOpenAdTypeOptions = computed(() => [

    <div class="mb-4 flex items-center gap-x-2"><h2 class="col-span-2 text-lg font-bold"> 广告位支持广告类型和聚合平台 </h2><span class="text-sm text-red-600">(勾选类型前需确认客户端是否支持)</span></div>,
    ['', 'ad_id', {
      type: 'custom',
      render: () => {
        return (
          <section class="border-1 rounded-lg border border-solid p-4">
            <div class="flex gap-x-4">
              <div class="w-[160px]">广告类型</div>
              <div class="w-[220px]">聚合平台</div>
              <div class="w-[160px]">是否补位</div>
            </div>
            {(formData.value?.app_open_config?.ad_list_new || []).map((item, index) => {
              return (
                <div class="my-1">
                  <div class="flex gap-x-4">
                    <div class="w-[160px]">
                      <ElSelect v-model={item.ad_type} class="w-[160px]">
                        <ElOption label="激励" value="rewarded">激励</ElOption>
                        <ElOption label="插屏" value="interstitial">插屏</ElOption>
                        <ElOption label="原生" value="native">原生</ElOption>
                        <ElOption label="开屏" value="app_open">开屏</ElOption>
                        <ElOption label="h5" value="h5">h5</ElOption>
                      </ElSelect>
                    </div>
                    <div class="flex w-[220px] items-center gap-x-1">
                      <ElSelect multiple v-model={item.ad_platforms} class="w-[220px]">
                        <ElOption value="admob">admob</ElOption>
                        <ElOption value="max">max</ElOption>
                        <ElOption value="meta">meta</ElOption>
                        <ElOption value="netbits">netbits</ElOption>
                      </ElSelect>
                      {index === 0 && (
                        <ElIcon>
                          <CirclePlus
                            class="cursor-pointer text-3xl text-green-600"
                            onClick={() => {
                              if (formData.value?.app_open_config?.ad_list_new.length < 5) {
                                formData.value?.app_open_config?.ad_list_new.push({
                                  ad_type: '',
                                  ad_platforms: [],
                                  replace_ad: [],
                                })
                              }
                            }}
                          />
                        </ElIcon>
                      )}
                      {index > 0 && (
                        <ElIcon>
                          <Remove
                            class="cursor-pointer text-3xl text-red-600"
                            onClick={() => {
                              formData.value?.app_open_config?.ad_list_new.splice(index, 1)
                            }}
                          />
                        </ElIcon>
                      )}
                    </div>
                    <div class="w-[160px]">
                      <ElSwitch v-model={item.ad_rep_open} onChange={e => {
                        if (e) {
                          item.replace_ad.push({
                            ad_type: '',
                            ad_platforms: [],
                          })
                        } else {
                          item.replace_ad = []
                        }
                      }} />
                    </div>
                  </div>
                  {item.replace_ad.length > 0 && (
                    <div class="flex items-center gap-x-4">
                      <div class="text-[12px] text-gray-500">补位配置</div>

                      <div class="my-1 w-[320px]">
                        {item.replace_ad.map((inItem, inIndex) => {
                          return (
                            <div class="my-1 flex items-center gap-x-1">
                              <ElSelect v-model={inItem.ad_type} class="w-[220px]">
                                <ElOption label="激励" value="rewarded">激励</ElOption>
                                <ElOption label="插屏" value="interstitial">插屏</ElOption>
                                <ElOption label="原生" value="native">原生</ElOption>
                                <ElOption label="开屏" value="app_open">开屏</ElOption>
                                <ElOption label="h5" value="h5">h5</ElOption>
                              </ElSelect>

                              <ElSelect multiple v-model={inItem.ad_platforms} class="w-[220px]">
                                <ElOption value="admob">admob</ElOption>
                                <ElOption value="max">max</ElOption>
                                <ElOption value="meta">meta</ElOption>
                                <ElOption value="netbits">netbits</ElOption>
                              </ElSelect>
                              {inIndex === 0 && (
                                <ElIcon>
                                  <CirclePlus
                                    class="cursor-pointer text-3xl text-green-600"
                                    onClick={() => {
                                      if (item.replace_ad.length < 5) {
                                        item.replace_ad.push({
                                          ad_type: '',
                                          ad_platforms: [],
                                        })
                                      }
                                    }}
                                  />
                                </ElIcon>
                              )}
                              {inIndex > 0 && (
                                <ElIcon>
                                  <Remove
                                    class="cursor-pointer text-3xl text-red-600"
                                    onClick={() => {
                                      item.replace_ad.splice(inIndex, 1)
                                    }}
                                  />
                                </ElIcon>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </section>
        )
      },
    }, { class: 'w-full' }],
  ] as FormOptions<FormData>['props']['items'])

  const dramaFreeAdTypeOptions = computed(() => [
    <div class="mb-4 flex items-center gap-x-2"><h2 class="col-span-2 text-lg font-bold"> 广告位支持广告类型和聚合平台 </h2><span class="text-sm text-red-600">(勾选类型前需确认客户端是否支持)</span></div>,
    ['', 'ad_id', {
      type: 'custom',
      render: () => {
        return (
          <section class="border-1 rounded-lg border border-solid p-4">
            <div class="flex gap-x-4">
              <div class="w-[160px]">广告类型</div>
              <div class="w-[220px]">聚合平台</div>
              <div class="w-[160px]">是否补位</div>
            </div>
            {(formData.value?.drama_free_config?.ad_list_new || []).map((item, index) => {
              return (
                <div class="my-1">
                  <div class="flex gap-x-4">
                    <div class="w-[160px]">
                      <ElSelect v-model={item.ad_type} class="w-[160px]">
                        <ElOption label="激励" value="rewarded">激励</ElOption>
                        <ElOption label="插屏" value="interstitial">插屏</ElOption>
                        <ElOption label="原生" value="native">原生</ElOption>
                        <ElOption label="开屏" value="app_open">开屏</ElOption>
                        <ElOption label="h5" value="h5">h5</ElOption>
                      </ElSelect>
                    </div>
                    <div class="flex w-[220px] items-center gap-x-1">
                      <ElSelect multiple v-model={item.ad_platforms} class="w-[220px]">
                        <ElOption value="admob">admob</ElOption>
                        <ElOption value="max">max</ElOption>
                        <ElOption value="meta">meta</ElOption>
                        <ElOption value="netbits">netbits</ElOption>
                      </ElSelect>
                      {index === 0 && (
                        <ElIcon>
                          <CirclePlus
                            class="cursor-pointer text-3xl text-green-600"
                            onClick={() => {
                              if (formData.value?.drama_free_config?.ad_list_new.length < 5) {
                                formData.value?.drama_free_config?.ad_list_new.push({ ad_type: '', ad_platforms: [], replace_ad: [] })
                              }
                            }}
                          />
                        </ElIcon>
                      )}
                      {index > 0 && (
                        <ElIcon>
                          <Remove
                            class="cursor-pointer text-3xl text-red-600"
                            onClick={() => formData.value?.drama_free_config?.ad_list_new.splice(index, 1)}
                          />
                        </ElIcon>
                      )}
                    </div>
                    <div class="w-[160px]">
                      <ElSwitch v-model={item.ad_rep_open} onChange={e =>
                        e ? item.replace_ad.push({ ad_type: '', ad_platforms: [] }) : (item.replace_ad = [])} />
                    </div>
                  </div>
                  {item.replace_ad.length > 0 && (
                    <div class="flex items-center gap-x-4">
                      <div class="text-[12px] text-gray-500">补位配置</div>

                      <div class="my-1 w-[320px]">
                        {item.replace_ad.map((inItem, inIndex) => {
                          return (
                            <div class="my-1 flex items-center gap-x-1">
                              <ElSelect v-model={inItem.ad_type} class="w-[220px]">
                                <ElOption label="激励" value="rewarded">激励</ElOption>
                                <ElOption label="插屏" value="interstitial">插屏</ElOption>
                                <ElOption label="原生" value="native">原生</ElOption>
                                <ElOption label="开屏" value="app_open">开屏</ElOption>
                                <ElOption label="h5" value="h5">h5</ElOption>
                              </ElSelect>

                              <ElSelect multiple v-model={inItem.ad_platforms} class="w-[220px]">
                                <ElOption value="admob">admob</ElOption>
                                <ElOption value="max">max</ElOption>
                                <ElOption value="meta">meta</ElOption>
                                <ElOption value="netbits">netbits</ElOption>
                              </ElSelect>
                              {inIndex === 0 && (
                                <ElIcon>
                                  <CirclePlus
                                    class="cursor-pointer text-3xl text-green-600"
                                    onClick={() => {
                                      if (item.replace_ad.length < 5) {
                                        item.replace_ad.push({
                                          ad_type: '',
                                          ad_platforms: [],
                                        })
                                      }
                                    }}
                                  />
                                </ElIcon>
                              )}
                              {inIndex > 0 && (
                                <ElIcon>
                                  <Remove
                                    class="cursor-pointer text-3xl text-red-600"
                                    onClick={() => {
                                      item.replace_ad.splice(inIndex, 1)
                                    }}
                                  />
                                </ElIcon>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </section>
        )
      },
    }, { class: 'w-full' }],
  ] as FormOptions<FormData>['props']['items'])

  const outFlowAdTypeOptions = computed(() => [
    <div class="mb-4 flex items-center gap-x-2"><h2 class="col-span-2 text-lg font-bold"> 广告位支持广告类型和聚合平台 </h2><span class="text-sm text-red-600">(勾选类型前需确认客户端是否支持)</span></div>,
    ['', 'ad_id', {
      type: 'custom',
      render: () => {
        return (
          <section class="border-1 rounded-lg border border-solid p-4">
            <div class="flex gap-x-4">
              <div class="w-[160px]">广告类型</div>
              <div class="w-[220px]">聚合平台</div>
              <div class="w-[160px]">是否补位</div>
            </div>
            {(formData.value?.out_flow_config?.ad_list_new || []).map((item, index) => {
              return (
                <x-row class="block my-1">
                  <x-ad-list-item class="flex gap-x-4">
                    <x-config class="block w-[160px]">
                      <ElSelect v-model={item.ad_type} class="w-[160px]">
                        <ElOption label="激励" value="rewarded">激励</ElOption>
                        <ElOption label="插屏" value="interstitial">插屏</ElOption>
                        <ElOption label="原生" value="native">原生</ElOption>
                        <ElOption label="开屏" value="app_open">开屏</ElOption>
                      </ElSelect>
                    </x-config>
                    <x-config class="flex w-[220px] items-center gap-x-1">
                      <ElSelect multiple v-model={item.ad_platforms} class="w-[220px]">
                        <ElOption value="admob">admob</ElOption>
                        <ElOption value="max">max</ElOption>
                        <ElOption value="meta">meta</ElOption>
                        <ElOption value="netbits">netbits</ElOption>
                      </ElSelect>
                      {index === 0 && (
                        <ElIcon>
                          <CirclePlus
                            class="cursor-pointer text-3xl text-green-600"
                            onClick={() => {
                              if (formData.value?.out_flow_config?.ad_list_new.length < 5) {
                                formData.value?.out_flow_config?.ad_list_new.push({ ad_type: '', ad_platforms: [], replace_ad: [] })
                              }
                            }}
                          />
                        </ElIcon>
                      )}
                      {index > 0 && (
                        <ElIcon>
                          <Remove
                            class="cursor-pointer text-3xl text-red-600"
                            onClick={() => formData.value?.out_flow_config?.ad_list_new.splice(index, 1)}
                          />
                        </ElIcon>
                      )}
                    </x-config>
                    {/* <x-config class="w-[160px]"> */}
                    <x-config class="w-[160px]">
                      <ElSwitch modelValue={!!item.replace_ad?.[0]} onChange={e =>
                        e ? item.replace_ad.push({ ad_type: '', ad_platforms: [] }) : (item.replace_ad = [])} />
                    </x-config>
                  </x-ad-list-item>
                  {item.replace_ad.length > 0 && (
                    <div class="flex items-center gap-x-4">
                      <div class="text-[12px] text-gray-500">补位配置</div>

                      <div class="my-1 w-[320px]">
                        {item.replace_ad.map((inItem, inIndex) => {
                          return (
                            <div class="my-1 flex items-center gap-x-1">
                              <ElSelect v-model={inItem.ad_type} class="w-[220px]">
                                <ElOption label="激励" value="rewarded">激励</ElOption>
                                <ElOption label="插屏" value="interstitial">插屏</ElOption>
                                <ElOption label="原生" value="native">原生</ElOption>
                                <ElOption label="开屏" value="app_open">开屏</ElOption>
                                <ElOption label="h5" value="h5">h5</ElOption>
                              </ElSelect>

                              <ElSelect multiple v-model={inItem.ad_platforms} class="w-[220px]">
                                <ElOption value="admob">admob</ElOption>
                                <ElOption value="max">max</ElOption>
                                <ElOption value="meta">meta</ElOption>
                                <ElOption value="netbits">netbits</ElOption>
                              </ElSelect>
                              {inIndex === 0 && (
                                <ElIcon>
                                  <CirclePlus
                                    class="cursor-pointer text-3xl text-green-600"
                                    onClick={() => {
                                      if (item.replace_ad.length < 5) {
                                        item.replace_ad.push({
                                          ad_type: '',
                                          ad_platforms: [],
                                        })
                                      }
                                    }}
                                  />
                                </ElIcon>
                              )}
                              {inIndex > 0 && (
                                <ElIcon>
                                  <Remove
                                    class="cursor-pointer text-3xl text-red-600"
                                    onClick={() => {
                                      item.replace_ad.splice(inIndex, 1)
                                    }}
                                  />
                                </ElIcon>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </x-row>
              )
            })}
          </section>
        )
      },
    }, { class: 'w-full' }],
  ] as FormOptions<FormData>['props']['items'])

  const dramaLockAdTypeOptions = computed(() => [

    <div class="mb-4 flex items-center gap-x-2"><h2 class="col-span-2 text-lg font-bold"> 广告位支持广告类型和聚合平台 </h2><span class="text-sm text-red-600">(勾选类型前需确认客户端是否支持)</span></div>,
    ['', 'ad_id', {
      type: 'custom',
      render: () => {
        return (
          <section class="border-1 rounded-lg border border-solid p-4">
            <div class="flex gap-x-4">
              <div class="w-[160px]">广告类型</div>
              <div class="w-[220px]">聚合平台</div>
              <div class="w-[160px]">是否补位</div>
            </div>
            {(formData.value?.reward_ads?.ad_list_new || []).map((item, index) => {
              return (
                <div class="my-1">
                  <div class="flex gap-x-4">
                    <div class="w-[160px]">
                      <ElSelect v-model={item.ad_type} class="w-[160px]">
                        <ElOption label="激励" value="rewarded">激励</ElOption>
                        <ElOption label="插屏" value="interstitial">插屏</ElOption>
                        <ElOption label="原生" value="native">原生</ElOption>
                        <ElOption label="开屏" value="app_open">开屏</ElOption>
                        <ElOption label="h5" value="h5">h5</ElOption>
                      </ElSelect>
                    </div>
                    <div class="flex w-[220px] items-center gap-x-1">
                      <ElSelect multiple v-model={item.ad_platforms} class="w-[220px]">
                        <ElOption value="admob">admob</ElOption>
                        <ElOption value="max">max</ElOption>
                        <ElOption value="meta">meta</ElOption>
                        <ElOption value="netbits">netbits</ElOption>
                      </ElSelect>
                      {index === 0 && (
                        <ElIcon>
                          <CirclePlus
                            class="cursor-pointer text-3xl text-green-600"
                            onClick={() => {
                              if (formData.value?.reward_ads?.ad_list_new.length < 5) {
                                formData.value?.reward_ads?.ad_list_new.push({
                                  ad_type: '',
                                  ad_platforms: [],
                                  replace_ad: [],
                                })
                              }
                            }}
                          />
                        </ElIcon>
                      )}
                      {index > 0 && (
                        <ElIcon>
                          <Remove
                            class="cursor-pointer text-3xl text-red-600"
                            onClick={() => {
                              formData.value?.reward_ads?.ad_list_new.splice(index, 1)
                            }}
                          />
                        </ElIcon>
                      )}
                    </div>
                    <div class="w-[160px]">
                      <ElSwitch v-model={item.ad_rep_open} onChange={e => {
                        if (e) {
                          item.replace_ad.push({
                            ad_type: '',
                            ad_platforms: [],
                          })
                        } else {
                          item.replace_ad = []
                        }
                      }} />
                    </div>
                  </div>
                  {item.replace_ad.length > 0 && (
                    <div class="flex items-center gap-x-4">
                      <div class="text-[12px] text-gray-500">补位配置</div>

                      <div class="my-1 w-[320px]">
                        {item.replace_ad.map((inItem, inIndex) => {
                          return (
                            <div class="my-1 flex items-center gap-x-1">
                              <ElSelect v-model={inItem.ad_type} class="w-[220px]">
                                <ElOption label="激励" value="rewarded">激励</ElOption>
                                <ElOption label="插屏" value="interstitial">插屏</ElOption>
                                <ElOption label="原生" value="native">原生</ElOption>
                                <ElOption label="开屏" value="app_open">开屏</ElOption>
                                <ElOption label="h5" value="h5">h5</ElOption>
                              </ElSelect>

                              <ElSelect multiple v-model={inItem.ad_platforms} class="w-[220px]">
                                <ElOption value="admob">admob</ElOption>
                                <ElOption value="max">max</ElOption>
                                <ElOption value="meta">meta</ElOption>
                                <ElOption value="netbits">netbits</ElOption>
                              </ElSelect>
                              {inIndex === 0 && (
                                <ElIcon>
                                  <CirclePlus
                                    class="cursor-pointer text-3xl text-green-600"
                                    onClick={() => {
                                      if (item.replace_ad.length < 5) {
                                        item.replace_ad.push({
                                          ad_type: '',
                                          ad_platforms: [],
                                        })
                                      }
                                    }}
                                  />
                                </ElIcon>
                              )}
                              {inIndex > 0 && (
                                <ElIcon>
                                  <Remove
                                    class="cursor-pointer text-3xl text-red-600"
                                    onClick={() => {
                                      item.replace_ad.splice(inIndex, 1)
                                    }}
                                  />
                                </ElIcon>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </section>
        )
      },
    }, { class: 'w-full' }],
  ] as FormOptions<FormData>['props']['items'])

  const quitPlayerAdTypeOptions = computed(() => [
    <div class="mb-4 flex items-center gap-x-2"><h2 class="col-span-2 text-lg font-bold"> 广告位支持广告类型和聚合平台 </h2><span class="text-sm text-red-600">(勾选类型前需确认客户端是否支持)</span></div>,
    ['', 'ad_id', {
      type: 'custom',
      render: () => {
        return (
          <section class="border-1 rounded-lg border border-solid p-4">
            <div class="flex gap-x-4"> <div class="w-[160px]">广告类型</div> <div class="w-[220px]">聚合平台</div> <div class="w-[160px]">是否补位</div> </div>
            {(formData.value?.quit_player_config?.ad_list_new || []).map((item, index) => {
              return (
                <div class="my-1">
                  <div class="flex gap-x-4">
                    <div class="w-[160px]">
                      <ElSelect v-model={item.ad_type} class="w-[160px]">
                        <ElOption label="激励" value="rewarded">激励</ElOption>
                        <ElOption label="插屏" value="interstitial">插屏</ElOption>
                        <ElOption label="原生" value="native">原生</ElOption>
                        <ElOption label="开屏" value="app_open">开屏</ElOption>
                        <ElOption label="h5" value="h5">h5</ElOption>
                      </ElSelect>
                    </div>
                    <div class="flex w-[220px] items-center gap-x-1">
                      <ElSelect multiple v-model={item.ad_platforms} class="w-[220px]">
                        <ElOption value="admob">admob</ElOption>
                        <ElOption value="max">max</ElOption>
                        <ElOption value="meta">meta</ElOption>
                        <ElOption value="netbits">netbits</ElOption>
                      </ElSelect>
                      {index === 0 && (
                        <ElIcon>
                          {/* 新增按钮 */}
                          <CirclePlus class="cursor-pointer text-3xl text-green-600" onClick={onClickAddItem} />
                        </ElIcon>
                      )}
                      {index > 0 && (
                        <ElIcon>
                          {/* 删除按钮 */}
                          <Remove class="cursor-pointer text-3xl text-red-600" onClick={() => formData.value?.quit_player_config?.ad_list_new?.splice(index, 1)} />
                        </ElIcon>
                      )}
                    </div>
                    <div class="w-[160px]">
                      <ElSwitch v-model={item.ad_rep_open} onChange={e => {
                        e ? item.replace_ad.push({ ad_type: '', ad_platforms: [] }) : item.replace_ad = []
                      }} />
                    </div>
                  </div>
                  {item.replace_ad.length > 0 && (
                    <div class="flex items-center gap-x-4">
                      <div class="text-[12px] text-gray-500">补位配置</div>

                      <div class="my-1 w-[320px]">
                        {item.replace_ad.map((inItem, inIndex) => {
                          return (
                            <div class="my-1 flex items-center gap-x-1">
                              <ElSelect v-model={inItem.ad_type} class="w-[220px]">
                                <ElOption label="激励" value="rewarded">激励</ElOption>
                                <ElOption label="插屏" value="interstitial">插屏</ElOption>
                                <ElOption label="原生" value="native">原生</ElOption>
                                <ElOption label="开屏" value="app_open">开屏</ElOption>
                                <ElOption label="h5" value="h5">h5</ElOption>
                              </ElSelect>

                              <ElSelect multiple v-model={inItem.ad_platforms} class="w-[220px]">
                                <ElOption value="admob">admob</ElOption>
                                <ElOption value="max">max</ElOption>
                                <ElOption value="meta">meta</ElOption>
                                <ElOption value="netbits">netbits</ElOption>
                              </ElSelect>
                              {inIndex === 0 && (
                                <ElIcon>
                                  <CirclePlus
                                    class="cursor-pointer text-3xl text-green-600"
                                    onClick={() => {
                                      if (item.replace_ad.length < 5) {
                                        item.replace_ad.push({ ad_type: '', ad_platforms: [] })
                                      }
                                    }}
                                  />
                                </ElIcon>
                              )}
                              {inIndex > 0 && (
                                <ElIcon>
                                  <Remove class="cursor-pointer text-3xl text-red-600" onClick={() => item.replace_ad.splice(inIndex, 1)}
                                  />
                                </ElIcon>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </section>
        )
      },
    }, { class: 'w-full' }],
  ] as FormOptions<FormData>['props']['items'])

  const currentAdListPlatform = ref('')

  watch(() => formData.value.platform, () => {
    if (formData.value?.platform == 'all') {
      currentAdListPlatform.value = 'android'
    } else {
      currentAdListPlatform.value = formData.value?.platform || 'android'
    }
  }, { immediate: true })

  const productList = computed(() => [
    <h2 class="col-span-2 mb-4 text-lg font-bold"> 配置广告 </h2>,
    ['', 'ad_id', {
      type: 'custom',
      render: () => {
        return (
          <section>
            <section>
              <x-section class="mb-4 flex items-center gap-x-2">
                {['all', 'android'].includes(formData.value.platform || '') && <Button class={`btn ${currentAdListPlatform.value == 'android' ? 'btn-primary' : ''} btn-sm`} disabled={isViewMode.value} onClick={() => currentAdListPlatform.value = 'android'}>Android广告</Button>}
                {['all', 'ios'].includes(formData.value.platform || '') && <Button class={`btn ${currentAdListPlatform.value == 'ios' ? 'btn-primary' : ''} btn-sm`} disabled={isViewMode.value} onClick={() => currentAdListPlatform.value = 'ios'}>IOS广告</Button>}
              </x-section>
            </section>
            <section class="border-1 rounded-lg border border-solid p-4">

              <x-table-actions class="flex items-center justify-between">
                <div class="space-x-2">
                  {currentAdListPlatform.value == 'android' && <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportWatchAdDialog('android')}>导入广告</Button>}
                  {currentAdListPlatform.value == 'ios' && <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportWatchAdDialog('ios')}>导入广告</Button>}
                  {/* <span class="text-sm text-gray-500">添加T+1个广告</span> */}
                </div>
              </x-table-actions>
              <hr class="my-4" />
              <WatchAdTable
                list={(formData.value?.ad_list ?? []).filter(i => i.platform == currentAdListPlatform.value).map((item, index) => ({ ...item, sort_no: item.sort_no || (index + 1) }))}
                columns={watchAdColumns}
                class="tm-table-fix-last-column"
              />
            </section>
          </section>
        )
      },
    }, { class: 'w-full' }],
  ] as FormOptions<FormData>['props']['items'])

  const currentTab = ref<M.AdScene>('app_open')

  watch(() => [formData.value.scene, currentTab.value], () => {
    const list = (formData.value?.scene || []).filter(i => Object.keys(adSceneMap).includes(i))
    if (list && list.length > 0 && !currentTab.value) {
      currentTab.value = list[0]
    }
    if (!list.includes(currentTab.value)) {
      currentTab.value = list[0] || ''
    }
    const relation: Record<string, string> = {
      drama_lock: 'reward_ads',
      reward: 'reward_config',
      app_open: 'app_open_config',
      drama_free: 'drama_free_config',
      quit_player: 'quit_player_config',
      out_flow: 'out_flow_config',
    } as const
    const key = relation[currentTab.value]
    if (!formData.value[key]) {
      formData.value[key] = {
        ad_list_new: [{ ad_type: '', ad_platforms: [], replace_ad: [] }],
        strategy: { cd_time: 0, task: [{ sort_no: 1, coins: 0 }] },
      }
    }
    if (!formData.value[key]?.ad_list_new?.[0]) {
      formData.value[key].ad_list_new = [{ ad_type: '', ad_platforms: [], replace_ad: [] }]
    }
  }, { deep: true, immediate: true })

  const items = computed(() => [
    [requiredLabel('广告位置'), 'scene', {
      type: 'checkbox-group',
      options: adSceneOptions(isViewMode.value),
    }],
    <div class="tab-sm tabs-boxed tabs flex gap-x-2 bg-white">
      {
        formData.value.scene.filter(i => Object.keys(adSceneMap).includes(i)).map(item => (
          <span
            class={mc('tab w-40 bg-gray-300 text-black', currentTab.value === item ? 'tab-active' : '')}
            onClick={() => {
              console.log('item:', item)
              currentTab.value = item
            }}
          >
            {adSceneMap[item]}
          </span>
        ))
      }
    </div>,
    // 广告位支持广告类型和聚合平台
    ...(currentTab.value === 'reward' ? rewardAdTypeOptions.value : []),
    ...(currentTab.value === 'app_open' ? appOpenAdTypeOptions.value : []),
    ...(currentTab.value === 'drama_free' ? dramaFreeAdTypeOptions.value : []),
    ...(currentTab.value === 'drama_lock' ? dramaLockAdTypeOptions.value : []),
    ...(currentTab.value === 'quit_player' ? quitPlayerAdTypeOptions.value : []),
    ...(currentTab.value === 'out_flow' ? outFlowAdTypeOptions.value : []),
    // 以下是控制参数
    ...(currentTab.value === 'app_open' ? openItems.value : []),
    ...(currentTab.value === 'drama_free' ? dramaFreeItems.value : []),
    ...(currentTab.value === 'drama_lock' ? dramaLockItems.value : []),
    ...(currentTab.value === 'reward' ? rewardItems.value : []),
    ...(currentTab.value === 'quit_player' ? quitPlayerItems.value : []),
    ...(currentTab.value === 'out_flow' ? outFlowItems.value : []),
    ...productList.value,
  ] as FormOptions<FormData>['props']['items'])

  return () => (
    <div class="flex flex-1 flex-col gap-4 overflow-hidden p-8">
      <Form
        class="flex flex-col gap-4"
        data={formData.value}
        error={stepErrorForSolution.value}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        onReset={() => {
          checkedAdvertiseItem.value = []
          currentAdvertiseList.value = []
          resetTaskFormData()
        }}
        onSubmit={onSubmit}
        items={items.value}
        actions={() => (
          <div class="flex items-center justify-between gap-x-4">
            <Button class="btn btn-sm" type="button" onClick={() => {
              void router.push({ path: `/ad-strategy-group/iap/create/target`, query: { ...route.query } })
            }}
            >上一步
            </Button>
            {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button>}
            {isViewMode.value ? null : <Button class="btn btn-primary btn-sm" type="submit">保存</Button>}
          </div>
        )}
      />

    </div>
  )
  function onClickAddItem() {
    const ad_list_new = formData.value?.quit_player_config?.ad_list_new
    if (ad_list_new && ad_list_new.length < 5) {
      ad_list_new.push({ ad_type: '', ad_platforms: [], replace_ad: [] })
    }
  }
})
export default StrategyGroupIapCreateSolutionPage
