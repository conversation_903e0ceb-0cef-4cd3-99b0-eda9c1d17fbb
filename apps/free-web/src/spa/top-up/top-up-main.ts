import { report, reportNowViaBeacon } from '@skynet/client-track'
import 'src/assets/style/global.css'
import i18n from 'src/i18n/init-i18n.ts'
import 'src/init/init.ts'
import { deviceInfo } from 'src/lib/get-device-info.ts'
import { inApp } from 'src/lib/jsbridge.ts'
import { createApp } from 'vue'
import { TopUpPage } from './top-up-page.tsx'

if (inApp) {
  report({
    event: `H5_page_load`,
    // @ts-expect-error never mind
    device_manufacturer: deviceInfo?.device_manufacturer ?? deviceInfo?.manufacturer ?? 'unknown',
    event_info: JSON.stringify({
      origin: 'H5',
      entity: 'page',
      event: 'load',
      time: Date.now(),
      url: window.location.href,
    }),
  })
}

window.addEventListener('beforeunload', reportNowViaBeacon)

const app = createApp(TopUpPage)
app.use(i18n)

app.mount('#app')
