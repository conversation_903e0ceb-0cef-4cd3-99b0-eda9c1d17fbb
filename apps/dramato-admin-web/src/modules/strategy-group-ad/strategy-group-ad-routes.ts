import { r, redirect } from '@skynet/shared'

export const strategyGroupAdRoutes = r('ad-strategy-group', '广告策略组', null, [
  redirect('', 'iap'),
  r(':tabName(iap|iaa)', '策略组', () => import('src/modules/strategy-group-ad/strategy-group-ad-page.tsx')),
  r('iap/create', 'IAP广告方案', () => import('src/modules/strategy-group-ad/strategy-group-ad-create-layout.tsx'), [
    redirect('', 'target'),
    r('target', '目标条件', () => import('src/modules/strategy-group-ad/strategy-group-iap-create-target-page.tsx')),
    r('solution', '方案配置', () => import('src/modules/strategy-group-ad/strategy-group-iap-create-solution-page.tsx')),
    r('plan', '任务计划', () => import('src/modules/strategy-group-ad/strategy-group-iap-create-plan-page.tsx')),
  ]),
])
