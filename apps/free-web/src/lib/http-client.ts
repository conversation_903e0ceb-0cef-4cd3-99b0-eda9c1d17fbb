/* eslint-disable @typescript-eslint/no-explicit-any */
import type {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  CreateAxiosDefaults,
  InternalAxiosRequestConfig,
} from 'axios'
import axios from 'axios'
import { deviceInfo } from './get-device-info.ts'
import { get_k_device_hash_from_cookie } from './device-id.ts'
import { authInfo } from './get-auth.ts'

export interface ConstructParams extends CreateAxiosDefaults {
  /**
   * 用来判断 response 是不是错误
   * @example
   * ```ts
   * isError: (response: AxiosResponse) => response.data.code !== 200
   * ```
   */
  isError?: (response: AxiosResponse) => boolean | Record<string, JsonValue> | undefined
  /**
   * 用来获取业务数据
   * 建议尽量获取全面的数据，不要只获取 data 而忽略 code
   * @example
   * ```ts
   * getData: (response: AxiosResponse) => response.data // 不推荐 response.data.data
   * ```
   */
  getData?: <T>(response: AxiosResponse<T>) => T
  /**
   * 请求拦截器
   */
  requestInterceptor?: [
    ((config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig) | null,
    ((error: unknown) => Promise<unknown>) | null,
  ]
  /**
   * 响应拦截器
   */
  responseInterceptor?: [
    ((response: AxiosResponse) => AxiosResponse) | null,
    ((error: unknown) => Promise<unknown>) | null,
  ]
}
export class HttpClient {
  instance: AxiosInstance
  isError: ConstructParams['isError']
  getData: Exclude<ConstructParams['getData'], undefined> = response => response.data
  requestInterceptor: ConstructParams['requestInterceptor']
  responseInterceptor: ConstructParams['responseInterceptor']
  constructor(params: ConstructParams) {
    const { isError, getData, requestInterceptor, responseInterceptor, ...rest } = params
    this.instance = axios.create(rest)
    this.isError = isError
    this.getData = getData || this.getData
    this.requestInterceptor = requestInterceptor
    this.responseInterceptor = responseInterceptor
    this.intercept()
  }

  private intercept() {
    this.instance.interceptors.response.use((response: AxiosResponse) => {
      const error = this.isError ? this.isError(response) : false
      if (error === true) {
        return Promise.reject({ response })
      }
      if (error instanceof Object) {
        return Promise.reject(error)
      }
      return response
    })
    if (this.requestInterceptor) {
      this.instance.interceptors.request.use(...this.requestInterceptor)
    }
    if (this.responseInterceptor) {
      this.instance.interceptors.response.use(...this.responseInterceptor)
    }
    this.instance.interceptors.response.use(null, (err: AxiosError) => {
      if ('response' in err) {
        const response = err.response as AxiosResponse
        if (response.data.err_msg?.indexOf('code: 404') >= 0) {
          console.log('404: ', 404)
          response.data.code = 404
        }
      }
      if (err.response?.status === 401) {
        // location.pathname = '/login'
      }
      return Promise.reject(err)
    })
  }

  /**
   * 发送 GET 请求
   * @param url 请求地址
   * @param params 查询参数
   * @param config 请求配置
   * @returns 请求结果
   * @example
   * ```ts
   * httpClient.get<ServerResponse<User>>("/api/user", { id: 1 })
   * ```
   */
  get<T = unknown>(
    url: string,
    params?: AxiosRequestConfig['params'],
    config = {} as Omit<AxiosRequestConfig, 'params'>,
  ) {
    return this.instance.get<T>(url, { params, ...config }).then(this.getData)
  }

  /**
   * 发送 POST 请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns 请求结果
   * @example
   * ```ts
   * httpClient.post<ServerResponse<User>>("/api/user", { id: 1 })
   * ```
   */
  post<T = unknown>(url: string, data?: any, config: AxiosRequestConfig = {}) {
    return this.instance.post<T>(url, data, config).then(this.getData)
  }

  /**
   * 发送 PUT 请求
   */
  put<T = unknown>(url: string, data?: JsonValue, config: AxiosRequestConfig = {}) {
    return this.instance.put<T>(url, data, config).then(this.getData)
  }

  /**
   * 发送 DELETE 请求
   */
  delete<T = unknown>(
    url: string,
    params?: AxiosRequestConfig['params'],
    config = {} as Omit<AxiosRequestConfig, 'params'>,
  ) {
    return this.instance.delete<T>(url, { params, ...config }).then(this.getData)
  }
}
const commonRequestInterceptor: HttpClient['requestInterceptor'] = [
  config => {
    Object.assign(config.headers, {
      'app-name': deviceInfo?.app_name ?? 'com.freereels.app',
      device: deviceInfo?.platform ?? 'web',
      'app-version': deviceInfo?.app_version ?? '1.0.0',
      'device-hash': deviceInfo?.device_id ?? get_k_device_hash_from_cookie(),
      'device-id': deviceInfo?.device_id ?? get_k_device_hash_from_cookie(),
      'device-manufacturer': deviceInfo?.device_manufacturer ?? deviceInfo?.manufacturer ?? 'web',
      'device-model': deviceInfo?.device_model ?? deviceInfo?.model ?? 'web',
      country: deviceInfo?.country ?? 'unknown',
      // 如果语言是zh，则带上国家码
      language: (deviceInfo?.language === 'zh' ? 'zh-' + deviceInfo?.country : (deviceInfo?.language ?? 'en')),
      // authorization: authInfo ? `oauth_signature=${authInfo?.oauth_signature},oauth_token=${authInfo?.oauth_token},ts=${new Date().getTime()}` : `oauth_signature=4741d34396465f0462fb8bd8c5d2ad5f,oauth_token=ZZOmz4aB6kSog73Zqn2Hjb0tfQDqXP1e,ts=1733734017476`,
      authorization: authInfo ? `oauth_signature=${authInfo?.oauth_signature},oauth_token=${authInfo?.oauth_token},ts=${new Date().getTime()}` : '',
      timezone: -new Date().getTimezoneOffset() / 60,
    })
    if (config.data && config.data.page_info) {
      config.data = {
        ...config.data,
        page_info: {
          ...config.data.page_info,
          next: config.data.page_info.offset.toString(),
        },
      }
    }
    return config
  },
  null,
]

const commonParams: ConstructParams = {
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 20000,
  withCredentials: true,
  isError: response => ![200, 404, 0].includes(response.data.code),
  getData: response => response.data,
  requestInterceptor: commonRequestInterceptor,
}
/**
 * 如果以下配置不满足你的需求，你可以修改，或者创建其他 httpClient
 */
export const httpClient = new HttpClient(commonParams)

export const keepError = (fn: (err: unknown) => void) => (err: unknown) => {
  fn(err)
  throw err
}
