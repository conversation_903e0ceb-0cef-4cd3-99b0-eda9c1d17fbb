declare namespace Api {
  namespace ResourceIncome {
    interface Params {
      res_id?: number // 资源ID
      res_title?: string // 资源名
      page_index?: number // 当前页索引
      page_size?: number // 每页大小
      month?: number | Date // 月份
      order_by?: number // 降序排序字段：1-播放PV 2-播放时长 3-完播率 4-累计关注数 5-总收入 6-订阅收入 7-充值摊销收入 8-订阅
    }

    interface ListResp {
      last_update_time?: string // 最近一次数据更新时间
      list: M.IResIncomeItem[] // 资源列表
      total: number // 总数
    }
  }
}

declare namespace M {
  interface IResIncomeItem {
    res_id?: string // 资源ID
    res_title: string // 资源名
    play_pv: number // 播放PV
    play_finish_ration: number | string // 完播率
    play_duration: number | string // 播放时长（分）
    follow_cnt: number | string // 累计关注数
    all_revenue: number | string // 总收入
    pay_revenue: number | string // 剧集收入（充值+订阅）
    ad_revenue: number | string // 广告收入
    subscribe_pay_revenue: number | string // 订阅收入
    recharge_distribute_revenue: number | string // 充值摊销收入
    subscribe_distribute_revenue: number | string // 订阅摊销收入
    month: string
  }
}
