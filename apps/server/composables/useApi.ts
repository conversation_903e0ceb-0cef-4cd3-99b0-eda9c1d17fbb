import { useRuntimeConfig, useCookie } from '#app'
import { get_authorization_for_server } from '~/lib/authorization'

export function useApi() {
  const config = useRuntimeConfig()

  type Options = {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
    headers?: Record<string, string>
    skipAuth?: boolean
    body?: Record<string, unknown>
  }
  // 通用请求方法
  const request = async <T>(url: string, options: Options = {
    method: 'GET',
    skipAuth: false,
    body: undefined,
  }) => {
    console.info('request', url, options)
    const timestamp = Date.now()
    const timeKey = 'requested:' + url + ':' + timestamp
    console.time(timeKey)
    try {
      const k_device_hash = useCookie('k_device_hash')
      // 自动拼接 baseURL
      const baseURL = config.public.apiUrl
      const headers = {
        ...options.headers,
        'content-type': 'application/json',
        'app-name': 'com.dramawave.h5', // 'com.dramawave.app',
        'app-version': '1.2.20',
        'device-hash': k_device_hash.value,
        'device-id': k_device_hash.value,
        'skip-encrypt': '1',
        authorization: options.skipAuth ? '' : get_authorization_for_server(),
        device: 'h5',
      }
      const res = await $fetch<ApiResponse<T>>(url, {
        baseURL,
        headers,
        timeout: 20000,
        ...options,
      })
      console.timeEnd(timeKey)
      // console.info('request res', res)
      return res.data as T
    } catch (error: unknown) {
      // 统一错误处理
      console.error('request error', error)
      // 你可以根据 error.response.status 做不同处理
      throw error
    }
  }

  return { request }
}
