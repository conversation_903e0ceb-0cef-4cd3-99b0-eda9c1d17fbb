/* eslint-disable @typescript-eslint/no-explicit-any */
import { openDialog } from '@skynet/ui'
import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { apiGetSplitActingList, apiMultiSplit } from './material-tools-api'
import { createCachedFn } from '@skynet/shared'

const initFromData = {
  id: undefined,    // id
  uid: "",         // 用户ID
  phone: "",       // 手机号
  avatar: "",      // 头像
  nickname: "",    // 昵称
  company: "",     // 公司名称
  usci: "",        // 统一社会信用代码
  licence_pic: "", // 营业执照图片
  auth_user: "",   // 审核人
  auth_status: 0,  // 认证状态 0:全部 1: 未认证 2: 已认证 3：认证失败
  failed_reason: "" // 审核失败原因
}

const form = ref<M.SplitActingItem>(cloneDeep(initFromData))

const config = ref<M.Config>({
  lang_items: {
    'de-DE': 'German',
    'en-US': 'English',
    'es-MX': 'Spanish',
    'fr-FR': 'French',
    'id-ID': 'Indonesian',
    'it-IT': 'Italian',
    'ja-JP': 'Japanese',
    'ko-KR': 'Korean',
    'pt-PT': 'Portuguese',
    'ru-RU': 'Russian',
    'th-TH': 'Thai',
    'tl-PH': 'Filipino',
    'tr-TR': 'Turkish',
    'vi-VN': 'Vietnamese',
    'zh-CN': '简体中文',
    'zh-TW': '繁体中文',
  },
})


export const useMaterialTools = createCachedFn(() => {
  const searchForm = ref<M.MaterialSearchOptionNew>({
    progress: 0,
    role_mark: 0,
    material_status: 0,
    page_index: 1,
    page_size: 20
  })

  const progressOptions = [
    { label: '全部', value: 0 },
    { label: '未开始', value: 1 },
    { label: '进行中', value: 2 },
    { label: '已完成', value: 3 }
  ]

  const roleMarkOptions = [
    { label: '全部', value: 0 },
    { label: '未标注', value: 1 },
    { label: '已标注', value: 2 }
  ]

  const materialStatusOptions = [
    { label: '全部', value: 0 },
    { label: '无素材', value: 1 },
    { label: '有素材', value: 2 }
  ]

  const list = ref<M.SplitActingItem[]>([])
  const total = ref<number>(10)
  const applicationList = ref<Array<{ label: string, value: number, platform: number, language: string[] }>>([])
  const closeEditPushNotificationModal = ref(() => { })
  const loading = ref(false)
  const isUpdating = ref(false)

  const InitPushNotificationOption: M.PushNotification = {
    priority: 3,
    user_identify_val: '',
    notify_btn_bg_color: '#FC2763',
  }

  const currentNotification = ref<M.PushNotification>(InitPushNotificationOption)

  const getList = async () => {
    loading.value = true
    const rs = await apiGetSplitActingList(searchForm.value)
    list.value = rs.data?.list || []
    total.value = rs.data?.total || 0
    loading.value = false
    console.log(roleList)
    list.value = roleList
  }

  const onSearchRoleList = (isFirst?: boolean) => {
    if (isFirst) {
      searchForm.value.page_index = 1
      searchForm.value.page_size = 20
    }
    void getList()
  }


  const onPageChange = (page_index: number) => {
    searchForm.value.page_index = page_index
    onSearchRoleList()
  }

  const onPageSizeChange = (page_size: number) => {
    searchForm.value.page_size = page_size
    searchForm.value.page_index = 1
    onSearchRoleList()
  }
  const multiSplit = () => {
    console.log('multiSplit')
  }
  const onReset = () => {
    searchForm.value.progress = 0
    searchForm.value.role_mark = 0
    searchForm.value.material_status = 0
    searchForm.value.page_index = 1
    searchForm.value.page_size = 20
    onSearchRoleList(true)
  }

  const pushConfig = ref<M.PushConfigData>()
  const getLangCode = (lang: string) => {
    return Object.keys(config.value.lang_items).find(key => config.value.lang_items[key] === lang)
  }

  const appTypeOptions = [
    { label: '全部', value: 3 },
    { label: '安卓', value: 1 },
    { label: 'IOS', value: 2 },
  ]
  const activityTypeOptions = [
    { label: '全部', value: 0 },
    { label: '评论活动', value: 1 }
  ]

  const activityPageOptions = [
    { label: '评论页面', value: 1 }
  ]
  const activityStatusOptions = [
    { label: '不限', value: 0 },
    { label: '待发布', value: 1 },
    { label: '已发布', value: 2 },
    { label: '下架', value: 3 }
  ]

  return {
    searchForm,
    form,
    initFromData,
    list,
    total,
    closeEditPushNotificationModal,
    InitPushNotificationOption,
    loading,
    onPageChange,
    onPageSizeChange,
    onReset,
    onSearchRoleList,
    currentNotification,
    applicationList,
    isUpdating,
    pushConfig,
    getLangCode,
    appTypeOptions,
    activityTypeOptions,
    activityPageOptions,
    activityStatusOptions,
    progressOptions,
    roleMarkOptions,
    materialStatusOptions,
    multiSplit
  }
})


