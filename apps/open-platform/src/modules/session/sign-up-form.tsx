import { createComponent } from '@skynet/shared'
import { Button, CreateForm } from '@skynet/ui'
import { useInterval } from '@vueuse/core'
import { set } from 'lodash-es'
import { keepError } from 'src/lib/http-client'
import router from 'src/router'
import { computed, ref, watch } from 'vue'
import { RouterLink } from 'vue-router'
import { sendCodeInterval } from '../common/constants'
import { SendCodeButton } from './send-code-button'
import { sessionApi } from './session-api'
import { useSession } from './use-session'
import { useI18n } from 'vue-i18n'

type SignUpFormOptions = {
  props: {}
}
export const SignUpForm = createComponent<SignUpFormOptions>({
  props: {},
}, props => {
  type Data = {
    phone: string
    pwd: string
    code: string
  }
  const { t } = useI18n()
  const { formData, signUp } = useSession()
  const serverError = ref('')
  const Form = CreateForm<Data>()
  const onSignUp = async () => {
    await signUp()
    void router.push('/sign_in')
  }
  const { counter, reset, pause, resume } = useInterval(1000, { controls: true, immediate: false })
  const finalCount = computed(() => {
    const n = sendCodeInterval - counter.value
    return n >= 0 ? n : 0
  })
  watch(() => finalCount.value, value => {
    if (value < 1) {
      pause()
      reset()
      disabled.value = false
    }
  })
  const disabled = ref(false)
  const onClickCode = async () => {
    resume()
    const code = await sessionApi.sendCode(formData.value.phone).catch(keepError(error => {
      serverError.value = t('failedToGetVerificationCode')
      pause()
      reset()
    }))
    disabled.value = true
    if (code) formData.value.code = code
  }
  return () => (
    <x-sign-in-form class="block">
      <header class="mb-8 mt-6 flex items-center leading-none">
        <h1 class="text-[var(--text-1)]">{t('registerNow')}</h1>
        <small class="ml-auto text-[var(--text-3)]">{t('alreadyHaveAnAccount')}<RouterLink to="/sign_in" class="text-[var(--brand-6)]">{t('logInNow')} </RouterLink> </small>
      </header>
      <Form data={formData.value} onSubmit={onSignUp} onChange={(path, value) => set(formData.value, path, value)}
        class="flex flex-col gap-6 [&_x-error]:hidden [&_x-label]:hidden "
        items={[
          ['手机号', 'phone', { type: 'text',
            class: 'w-[318px] !bg-[#faf7fa] p-4 leading-4 rounded-lg',
            wrapperClass: 'block h-auto',
            placeholder: t('enterPhoneNumber'),
          }, { }],
          ['密码', 'pwd', { type: 'password',
            class: 'w-[318px] !bg-[#faf7fa] p-4 leading-4 rounded-lg pr-[8em]',
            wrapperClass: 'block h-auto',
            placeholder: t('pleaseSetPassword'),
          }],
          ['验证码', 'code', { type: 'text',
            class: 'w-[318px] !bg-[#faf7fa] p-4 leading-4 rounded-lg pr-[8em]',
            wrapperClass: 'block h-auto',
            placeholder: t('enterVerificationCode'),
          }, { hint: () => (
            <SendCodeButton phone={formData.value.phone}
              class="absolute right-4 top-1/2 z-up -translate-y-1/2 text-sm text-[var(--brand-6)]">{t('sendVerificationCode')}
            </SendCodeButton>
          ), class: 'relative' }],
          serverError.value ? () => <div class="text-center text-sm text-[var(--brand-6)]">{serverError.value}</div> : null,
        ]}
        buttonWrapperClass="justify-start"
        actions={() => (
          <Button class="btn w-full" type="submit">{t('registerNow')}</Button>
        )}
      >
        {/* Add form fields here */}
      </Form>
      <p class="pt-6 text-center text-sm text-[var(--text-3)] [&_a]:text-[var(--text-link)]">{t('registrationRepresentsAgreement')}
        <a target="_blank" href="https://mydramawave.com/rules/terms.html?language=zh&country_code=CN">{t('userAgreement')}</a>和
        <a target="_blank" href="https://mydramawave.com/rules/privacy.html?language=zh&country_code=CN">{t('privacyPolicy')}</a>
      </p>
    </x-sign-in-form>
  )
})

export default SignUpForm
