import { createComponent, getQueries } from '@skynet/shared'
import { Back } from '../common/back/back'
import { T } from './activity-payment.i18n'
import { cardBg, channel, indexBg, payChannelBg, series } from './images/images'
import { onBeforeMount, onMounted, onUnmounted, ref, watch } from 'vue'
import { useActivityPayment } from './use-activity-payment'
import { SvgIcon } from '@skynet/ui'
import PaymentProduct from './payment-product'
import Swiper from '../swiper/swiper.vue'
import { setIOSTheme } from 'src/lib/set-ios-theme'
import CommonRules from './common-rules'
import CommonShare from './common-share'
import { replaceHelper } from './replace-helper'
import { useLocale } from '../i18n/i18n'
import { supportedLocaleList } from '../common/constants'
import { apiGetShareStatus } from './activity-payment-api'
import { h5Track } from 'src/lib/h5-track'
import { useRouter } from 'vue-router'

export const ActivityPaymentPage = createComponent(null, () => {
  const router = useRouter()
  const { productList, getProductList, user, refereeAvatar, bonus, getBanner, bannerList, status } = useActivityPayment()
  const { language, from } = getQueries({
    language: '',
    from: '',
  })

  const isCollapse = ref(true)
  const selectedProduct = ref<M.ActivityPayment.Product>()
  const currency = ref()
  const unitPriceCoins = ref()
  const { currentLocale } = useLocale()

  const locale = supportedLocaleList.find(item => item.shortCode === (language ?? 'en'))
  if (locale) {
    currentLocale.value = locale
  }

  watch(() => productList.value, () => {
    if (productList.value?.pay_channel_list?.length) {
      selectedProduct.value = productList.value.pay_channel_list[0].product_list[0]
      currency.value = productList.value.pay_channel_list[0].product_list[0].currency
      unitPriceCoins.value = productList.value.pay_channel_list[0].unit_price_coins
    }
  })

  onBeforeMount(() => {
    void getProductList()
    void getBanner()
    // 有uid说明是分享过了
    if (user.value?.user_id) {
      // 查询分享状态
      void apiGetShareStatus(Number(user.value.user_id)).then(res => {
        refereeAvatar.value = res.data?.referee_avatar ?? ''
        bonus.value = res.data?.bonus ?? 0
        status.value = res.data?.status ?? 0
        if (res.data?.status !== 0) {
          void router.push({ path: '/activity/recharge-success', query: { ...router.currentRoute.value.query, share_success: '1', uid: user.value?.user_id } })
        }
      }).catch(err => {
        console.log('err', err)
      })
    }
  })

  onMounted(() => {
    h5Track('H5_webrecharge', 'page', 'show', {
      from,
    })
    // 设置状态栏颜色
    setIOSTheme('#fff')
  })

  onUnmounted(() => {
    setIOSTheme('#0b080b')
  })

  const handlePay = (product_id: number, currency: string, price: string) => {
    void router.push({ path: '/activity/recharge', query: { ...router.currentRoute.value.query, product_id, currency, price } })
  }

  return () => (
    <x-activity-payment class="pt-15 relative block min-h-screen bg-[#FF4277] pb-12">
      <img src={indexBg} width={375} height={540} class="absolute inset-0 h-auto w-full object-cover" />
      <x-wrapper class="relative z-up-up block">
        {/* 标题倾斜 */}
        <x-header class="line-clamp-2 block h-20 px-3 text-center text-4xl font-bold capitalize italic leading-10 text-white [text-shadow:_0px_3px_0px_rgb(212_0_51_/_1.00)]" innerHTML={replaceHelper(T.paymentPage.activityTitle(), '#fde047', true)} />
        <x-content class="mt-285px flex flex-col px-3">
          <x-card class="relative block">
            <x-card-header class="block">
              <img src={cardBg} class="absolute inset-0 z-up h-auto w-full" />
              <x-card-title class="top-26px absolute left-3 z-up-up block text-center text-lg leading-snug text-white">
                {T.paymentPage.rechargeOption()}
              </x-card-title>
              <x-card-content class="mt-60px relative z-up-up block rounded-[20px] bg-white shadow-[0px_4px_8px_0px_rgba(207,0,37,0.50)]">
                <x-collapse class="min-h-80px relative block rounded-[20px] px-3">
                  <x-collapse-title class="h-80px absolute inset-x-3 top-0 z-up-up flex items-center" onClick={() => {
                    isCollapse.value = !isCollapse.value
                  }}>
                    <img src={payChannelBg} class="h-20px w-auto bg-transparent object-cover" width={70} height={20} />
                    <x-desc class="ml-4 flex h-full flex-1 flex-col items-start justify-between gap-y-2 py-5">
                      <div class="text-sm text-[#0B080B]">Airwallex</div>
                      {
                        currency.value && unitPriceCoins.value ? (
                          <div class="flex items-center text-xs text-[#FC2763]">
                            <span>1</span>
                            <span>{currency.value}</span>
                            <span class="ml-1">≈</span>
                            <SvgIcon name="coin" class="ml-1 size-3" />
                            <span class="ml-0.5">{unitPriceCoins.value}</span>
                          </div>
                        ) : null
                      }
                    </x-desc>
                    <x-icon class="p-3">
                      <SvgIcon name="ic-chevron-up" class={`size-5 transition-transform duration-300 ${isCollapse.value ? '' : 'rotate-180'}`} />
                    </x-icon>
                  </x-collapse-title>
                  {
                    isCollapse.value && (
                      <x-collapse-area class={`pt-80px flex flex-wrap gap-2 pb-3 transition-all duration-300 ${isCollapse.value ? 'h-auto opacity-100' : 'h-0 opacity-0'}`}>
                        {
                          productList.value?.pay_channel_list?.map(payChannel => (
                            payChannel.product_list.map((product, _index) => (
                              // 第一个hot
                              <PaymentProduct key={product.product_id} isSelected={selectedProduct.value?.product_id === product.product_id} product={product} isHot={_index === 0} onClick={() => {
                                void handlePay(product.product_id, product.currency, (product.price / 100).toFixed(2))
                                selectedProduct.value = product
                              }} />
                            ))
                          ))
                        }
                      </x-collapse-area>
                    )
                  }
                </x-collapse>
              </x-card-content>
            </x-card-header>
          </x-card>
          <x-channel class="block px-3 pb-2 pt-4">
            <img src={channel} class="h-6 w-full" />
          </x-channel>
          <CommonShare title={replaceHelper(T.paymentPage.shareTitle(), '#fde047', true)} />
          <x-series class="pointer-events-none relative mt-5 block rounded-[20px] bg-[#F71650] pt-4 shadow-[0px_-1px_0px_0px_rgba(255,255,255,0.20)]">
            <x-shadow class="absolute inset-0 z-up size-full rounded-[20px] shadow-[0px_4px_8px_0px_rgba(207,0,37,0.50)]" />
            <x-series-title class="relative z-up-up flex items-start gap-x-1 px-3 text-lg font-normal leading-snug text-white">
              <img src={series} class="mt-1 block !size-[20px] leading-snug" />
              <div class="flex flex-col text-lg font-normal">
                <p class="text-lg font-bold text-[#FEE54C]">{T.paymentPage.contentBenifits()}</p>
                <p>{T.paymentPage.seriesTitle()}</p>
              </div>
            </x-series-title>
            <Swiper noTag items={bannerList.value} class="relative z-up-up mt-3 h-48 !rounded-2xl" />
          </x-series>
        </x-content>
        <CommonRules />
      </x-wrapper>
    </x-activity-payment>
  )
})

export default ActivityPaymentPage
