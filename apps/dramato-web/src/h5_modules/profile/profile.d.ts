interface BizTag {
  name: string // 展示名称(多语言)
  type: 'special' | 'following' | 'dubbed' | 'new' | 'originals' | 'hot' | 'custom' // 类型(不变)
}

interface Rank {
  id: number // 榜单 ID
  sub_id: number // 子榜单 ID
  text_color: string // 文字颜色
  bg_color: string // 背景颜色
  img: string // 图片
  rank_name: string // 榜单名称
  rank_str: string // 榜单描述
}

interface ColorStyle {
  background: number // 背景颜色
  button: number // 按钮颜色
  font: number // 字体颜色
}

interface Info {
  color_style: ColorStyle // 颜色样式
}

export type SeriesItemInfo = M.Dramato

interface MembershipProductInfo {
  title: string
  description: string
  vip_saved: string // 货币字符串格式
  membership_type: 'ad' // 可扩展为联合类型
  watch_ad_times: number // 分钟单位
  ad_units: {
    max: string
    admob: string
  }
  grace_period_sku_id: string
  is_grace_period: boolean
  is_on_hold: boolean
}

export interface WalletDataInfo {
  cash_balance: number
  bonus_balance: number
  vip_level: 0 | 1 // 枚举状态
  vip_expire: number // Unix 时间戳（秒）
  vip_cooling_time: number // 冷却倒计时
  auto_unlock: 0 | 1 // 二进制开关
  diamond_auto_unlock: 0 | 1
  diamond_balance: number
  membership_product: MembershipProduct
  vip_used: boolean // 历史状态标识
  coupons_count: string // 字符串化数值
}
