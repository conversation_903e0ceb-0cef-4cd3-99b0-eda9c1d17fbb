import { ref } from 'vue'

const checked = ref<number[]>([])
const isCheckAll = ref(false)

const checkedAllChange = (value: boolean, list: M.IResourceAssetInfo[]) => {
  isCheckAll.value = value
  const ids = list.map(row => row.serial_number)
  if (isCheckAll.value) {
    checked.value = ids
  } else {
    checked.value = []
  }
}

const checkboxChange = (value: boolean, serial_number: number, list: M.IResourceAssetInfo[]) => {
  if (value) {
    if (!checked.value.includes(serial_number)) checked.value.push(serial_number)
    if (list.length === checked.value.length) {
      isCheckAll.value = true
    }
  } else {
    const rowIndex = checked.value.findIndex(rowId => rowId === serial_number)
    if (rowIndex !== -1) {
      checked.value.splice(rowIndex, 1)
      isCheckAll.value = false
    }
  }
}

const resetChecked  = () => {
  checked.value = []
  isCheckAll.value = false
}

export const useCheck = () => {
  return {
    checked, isCheckAll, checkedAllChange, checkboxChange, resetChecked
  }
}
