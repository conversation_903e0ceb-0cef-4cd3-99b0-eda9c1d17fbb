import { createComponent, mc, useValidator } from '@skynet/shared'
import { ref } from 'vue'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { transformNumber, SvgIcon, Button, openDialog, showAlert, DateTime, Icon, showFailToast } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { useUserComDis } from './use-user-com-dis'
import { apiCompensationGrant } from './user-com-dis-api'
import { z } from 'zod'

type UserComDisOptions = {
  props: {}
}
export const UserComDis = createComponent<UserComDisOptions>({
  props: {},
}, props => {
  const {
    Form,
    FormOne,
    FormTwo,
    params,
    loading,
    search,
    userConDisForm,
    compensationGrant,
  } = useUserComDis()
  const formRules = z.object({
    compensation_amount: z.number().min(0, '请填写发放数量').max(9999, '支持输入0-9999的整数'),
    compensation_type: z.string().min(1, '请选择发放类型'),
    compensation_reason: z.string().min(1, '请选择发放原因'),
  })
  const successWay = () => {
    console.log('successWay')
    const hideDeleteDialog = openDialog({
      title: '发放已完成',
      mainClass: 'pb-0 px-5',
      body: (
        <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-delete-episode-body>
            <div>
              <div>目标用户ID：{userConDisForm.value.old_user_asset?.user_id}</div>
              <div>发放类型：{compensationGrant.value.compensation_type}(有效期10天)</div>
              <div>发放数量：{compensationGrant.value.compensation_amount}</div>
              <div>发放原因：{compensationGrant.value.compensation_reason}</div>
              <div>备注：{compensationGrant.value.remark}</div>
            </div>
          </x-delete-episode-body>
          <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
            <Button class="btn btn-primary btn-sm" onClick={() => {
              hideDeleteDialog()
            }}
            >
              <span>关闭</span>
            </Button>
          </x-delete-episode-footer>
        </x-delete-episode-confirm-dialog>
      ),
    })
  }

  const { error, validateAll } = useValidator(compensationGrant, formRules)
  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>用户账号查询</li>
          </ul>
        ),
        form: () => (
          <Form
            submitText="查询"
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              // 261179135
              params.value = { is_single_user_query: 1 }
              userConDisForm.value = {}
            }}
            onSubmit={() => search()}
            data={params.value}
            items={[
              ['用户ID', 'old_user_id', { type: 'number', placeholder: '用户ID' }, { transform: transformNumber }],
            ]}
          />
        ),
        table: () => (
          <div>
            <div class={mc(userConDisForm.value.old_user_asset?.user_id && '!hidden', 'h-[400px] flex justify-center items-center')}>{loading.value ? <SvgIcon class="loading loading-spinner size-4" name="ic_loading" /> : '请输入用户ID查询后操作'}</div>
            <div class={mc('hidden', userConDisForm.value.old_user_asset?.user_id && 'block', 'pl-5')}>
              <x-push-desc-list class="p-10 gap-10">
                <div class=" p-5 w-[1040px] bg-gray-100 rounded-lg">
                  <h2 class="font-medium mb-2">用户账号：{userConDisForm.value.old_user_asset?.user_id}</h2>
                  <FormTwo
                    class="grid grid-cols-2"
                    hasAction={false}
                    data={userConDisForm.value}
                    items={[
                      ['国家/地区代码：', '', { type: 'custom', render: r => { return (<div>{userConDisForm.value.old_user_asset?.country_code}</div>) } }, { class: 'flex flex-row col-span-1' }],
                      ['订阅状态：', '', { type: 'custom', render: r => { return (<div>{userConDisForm.value.old_user_asset?.subscription_status || '--'} (到期时间: <DateTime value={userConDisForm.value.old_user_asset?.subscription_expire_time ? userConDisForm.value.old_user_asset?.subscription_expire_time * 1000 : '--'} />)</div>) } }, { class: 'flex flex-row col-span-1' }],
                      ['账号状态：', '', { type: 'custom', render: r => { return (<div>最后使用时间为 {userConDisForm.value.old_user_asset?.last_active_dt}</div>) } }, { class: 'flex flex-row  col-span-1' }],
                      ['金币：', '', { type: 'custom', render: r => { return (<div>{userConDisForm.value.old_user_asset?.coins} coins</div>) } }, { class: 'flex flex-row col-span-1' }],
                      ['端：', '', { type: 'custom', render: r => { return (<div>{userConDisForm.value.old_user_asset?.platform == 1 ? 'ios' : userConDisForm.value.old_user_asset?.platform == 2 ? 'android' : '不知'}</div>) } }, { class: 'flex flex-row col-span-1' }],
                      ['奖励币：', '', { type: 'custom', render: r => { return (<div>{userConDisForm.value.old_user_asset?.bonus} bonus + {userConDisForm.value.old_user_asset?.rewards} rewards</div>) } }, { class: 'flex flex-row col-span-1' }],
                    ]}
                  />
                </div>
              </x-push-desc-list>

              <div class="pl-4">
                <div>奖励/补偿发放</div>
                <div class="block text-gray-400 text-xs my-1">有过期时间，请及时通知用户</div>
              </div>
              <x-push-desc-list class="gap-10">
                <div class=" p-5 w-[1040px] bg-gray-100 rounded-lg">
                  <FormOne
                    class="grid grid-cols-1"
                    hasAction={false}
                    error={error.value}
                    data={compensationGrant.value}
                    onChange={(path, value) => {
                      set(compensationGrant.value, path, value)
                    }}
                    items={[
                      [
                        <div class="flex items-center">{requiredLabel('发放类型')}<span class="block ml-2 text-gray-400 text-xs">有效期：10天</span></div>,
                        'compensation_type',
                        {
                          type: 'select',
                          options: [
                            { label: '安抚补偿币值', value: 'Compensation' },
                            { label: '广告激励rewards', value: 'WatchAD' },
                          ],
                          class: 'w-[500px]',
                        },
                      ],
                      [
                        <div class="flex items-center">{requiredLabel('发放数量')}<span class="block ml-2 text-gray-400 text-xs">金币数</span></div>,
                        'compensation_amount',
                        {
                          type: 'number',
                          placeholder: '输入0~9999的整数',
                          class: 'w-[500px]',
                        },
                        {
                          transform: transformNumber,
                          class: 'w-[500px]',
                        },
                      ],
                      [
                        requiredLabel('发送原因'),
                        'compensation_reason',
                        {
                          type: 'select',
                          options: [
                            { label: '投诉反馈', value: '投诉反馈' },
                            { label: '漏发补偿', value: '漏发补偿' },
                            { label: '其他', value: '其他' },
                          ],
                          class: 'w-[500px]',
                        },
                      ],
                      [
                        '备注',
                        'remark',
                        {
                          type: 'textarea',
                          class: 'w-[500px]',
                        },
                      ],
                    ]}
                  />
                </div>
              </x-push-desc-list>
            </div>
            <div class={mc('hidden justify-end gap-2 w-[1040px] mt-2', userConDisForm.value.old_user_asset?.user_id && '!flex')}>
              <div class="flex justify-start gap-2">
                <Button class="btn btn-primary btn-sm w-28" onClick={() => {
                  if (validateAll()) {
                    const btnLoading = ref(false)
                    const hideDeleteDialog = openDialog({
                      title: '确认发放',
                      mainClass: 'pb-0 px-5',
                      body: (
                        <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <x-delete-episode-body>
                            <div>
                              <div>目标用户ID：{userConDisForm.value.old_user_asset?.user_id}</div>
                              <div>发放类型：{compensationGrant.value.compensation_type}(有效期10天)</div>
                              <div>发放数量：{compensationGrant.value.compensation_amount}</div>
                              <div>发放原因：{compensationGrant.value.compensation_reason}</div>
                              <div>备注：{compensationGrant.value.remark}</div>
                            </div>
                          </x-delete-episode-body>
                          <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                            <Button class={mc('btn btn-ghost btn-sm')} onClick={() => hideDeleteDialog()}>取消</Button>
                            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
                              btnLoading.value = true
                              console.log(btnLoading.value)
                              void apiCompensationGrant({
                                ...compensationGrant.value,
                                user_id: userConDisForm.value.old_user_asset?.user_id,
                              }).then(res => {
                                console.log(btnLoading.value)
                                if (res.data!.success == 1) {
                                  userConDisForm.value = {}
                                  params.value.old_user_id = undefined
                                  hideDeleteDialog()
                                  void successWay()
                                } else {
                                  showFailToast('补偿失败！')
                                }
                              }).catch(error => {
                                showFailToast(error.response.data.err_msg || '操作失败')
                              }).finally(() => {
                                btnLoading.value = false
                              })
                            }}
                            >
                              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                              <span>确认发放</span>
                            </Button>
                          </x-delete-episode-footer>
                        </x-delete-episode-confirm-dialog>
                      ),
                    })
                  } else {
                    console.log('error', error.value)
                    return
                  }
                }}
                >开始发放
                </Button>
              </div>
            </div>
          </div>
        ),
      }}
    </NavFormTablePager>
  )
})
