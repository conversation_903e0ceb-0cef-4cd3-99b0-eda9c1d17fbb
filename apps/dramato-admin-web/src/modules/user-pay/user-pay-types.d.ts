declare namespace M {
  namespace UserPay {
    interface Params {
      user_id?: number // 用户id
      uid?: number
      pay_type?: string
      out_txn_id?: string
      page_info?: {
        offset: number
        size: number
      }
    }

    interface Item {
      order_id: string
      pay_finish_time: number
      user_id?: number
      uid?: number
      status: string
      pay_amount: number
      delivery_details: {
        quanity: number
        period: string
      } // 发货详情
      platform: string
      out_txn_id: string
      original_txn_id: string
    }

    interface Response {
      items: Item[]
      page_info: {
        next: string
        has_more: boolean
      }
      total: number
    }

    interface orderFormParams {
      user_id?: number
      out_txn_id: string
      sku_id: string
      pay_channel: string
      purchase_token: string
    }

    interface AppleOrderParams {
      out_txn_id: string
    }

    interface AppleOrderResponse {
      order_id: string
      status: string
      user_id: number
      uid: number
    }
  }
}
