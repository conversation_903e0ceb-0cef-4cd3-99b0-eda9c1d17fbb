/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { useRoleStore } from './use-role-store'
import { CreateForm, Button, showFailToast, showSuccessToast, Icon, SvgIcon } from '@skynet/ui'
import { onMounted, watch } from 'vue'
import { set, cloneDeep } from 'lodash-es'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { apiUpdateUserRole } from './user-api'
import { ref } from 'vue'

type RoleEditDialogOptions = {
  props: {
    closeDialog: () => void
    afterSave: () => void
  }
}
export const RoleEditDialog = createComponent<RoleEditDialogOptions>({
  props: {
    closeDialog: () => {},
    afterSave: () => {},
  },
}, props => {
  const btnLoading = ref(false)
  const Form = CreateForm<{
    open_roles: number[]
  }>()
  const form = ref<{
    open_roles: number[]
  }>({
    open_roles: [],
  })

  onMounted(() => {
    form.value = {
      open_roles: cloneDeep(currentUser.value?.open_roles) || [],
    }
  })

  const formRules = z.object({
    open_roles: z.array(z.number()).min(0, '请选择角色'),
  })
  const { currentUser, roles, getUserMenu } = useRoleStore()
  const { error, validateAll } = useValidator(form, formRules)

  const menuList = ref<M.IUserMenuItem[]>([])
  const menuLoading = ref(false)

  watch(form, async newVal => {
    console.log(form.value.open_roles)
    if (!newVal.open_roles || !newVal.open_roles.length) return
    menuLoading.value = true
    menuList.value = await getUserMenu(newVal.open_roles)
    menuLoading.value = false
  }, { immediate: true, deep: true })

  const save = async () => {
    if (!validateAll()) return
    try {
      btnLoading.value = true
      await apiUpdateUserRole({
        user_ids: (currentUser.value?.users || []).map(row => row.user_id),
        open_roles: form.value.open_roles || [],
      })
      showSuccessToast('操作成功')
      props.afterSave()
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }

  const renderMenuItem = (menuSubItem: M.IMenuItem, parent_name: string) => {
    return (
      <li class="flex flex-row hover group items-center gap-x-4">
        <a href="javascript:void(0);">{menuSubItem.title} - {menuSubItem.path}</a>
      </li>
    )
  }

  // 目录渲染
  const renderSubTitle = (menuSubItem: M.IMenuItem, parent_name: string) => {
    const id = menuSubItem.id
    return (
      <li>
        <details open>
          <summary class="inline-flex hover group">
            <span>{menuSubItem.title}</span>
          </summary>
          <ul>
            {menuSubItem.children && renderMenu(menuSubItem.children, menuSubItem.title)}
          </ul>
        </details>
      </li>
    )
  }

  const renderMenu = (menus: M.IMenuItem[], parent_name = '根目录') => {
    return menus.map(menuSubItem => {
      if (menuSubItem.path) {
        return renderMenuItem(menuSubItem, parent_name)
      } else {
        return renderSubTitle(menuSubItem, parent_name)
      }
    })
  }

  return () => (
    <>
      <Form
        data={form.value}
        hasAction={false}
        error={error.value}
        class="w-full flex-col"
        onChange={(path, value) => {
          set(form.value, path, value)
        }}
        items={
          [
            [
              requiredLabel('选中用户：'),
              'users',
              {
                type: 'custom',
                render: () => (
                  <div class="space-x-2">
                    {
                      (currentUser.value?.users || []).map(user => <div class="badge badge-primary badge-outline">{user.username}</div>)
                    }
                  </div>
                ),
              },
            ],
            [
              requiredLabel('角色：'),
              'open_roles',
              {
                type: 'multi-select',
                options: roles.value.map(roleObj => {
                  return {
                    value: roleObj.id,
                    label: roleObj.name,
                  }
                }),
                popoverWrapperClass: 'z-popover-in-dialog',
              },
            ],
            [
              requiredLabel('菜单权限：'),
              'menu',
              {
                type: 'custom',
                render: () => (
                  <ul class="menu lg:menu-horizontal bg-base-200 rounded-box lg:mb-64">
                    {
                      menuLoading.value ? <span class="loading loading-spinner loading-sm" /> : renderMenu(menuList.value)
                    }
                  </ul>
                ),
              },
            ],
          ]
        }
      />

      <div class="flex justify-end gap-x-2">
        <Button class="btn  btn-sm" onClick={() => props.closeDialog()}>取消</Button>
        <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={save}>
          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
          确定
        </Button>
      </div>
    </>
  )
})
