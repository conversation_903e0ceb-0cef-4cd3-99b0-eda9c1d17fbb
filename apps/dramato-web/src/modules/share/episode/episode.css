
.prism-player {
    video {
        object-fit: cover;
    }
    .loading-center,
    .prism-info-display {
        display: none!important;
    }
}

.subtitle {
    bottom: 3.5rem;
    bottom: calc(constant(safe-area-inset-bottom) + 3.5rem);
    bottom: calc(env(safe-area-inset-bottom) + 3.5rem);
}


.prism-ErrorMessage {
    display: none!important;
}

.share-bottom {
    @apply pb-3;
}

.share-bottom {
    padding-bottom: calc(constant(safe-area-inset-bottom) + 0.75rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 0.75rem);
}