/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc } from '@skynet/shared'
import { add, del, fail, loading } from '../../images/images'
import { MergeClass } from '@skynet/ui'
import { jsBridge } from 'src/lib/jsbridge'
import { onMounted, ref } from 'vue'
import { useUploader } from './use-upload'
import { useLocale } from 'src/lib/use-locale'
import { useRouter } from 'vue-router'

type UploadOptions = {
  props: {
    list: string[]
  }
  emits: {
    remove: (index: number) => void
    add: (item: string) => void
  }
}
export const Upload = createComponent<UploadOptions>({
  props: {
    list: [],
  },
  emits: {
    remove: fn,
    add: fn,
  },
}, (props, { emit }) => {
  const router = useRouter()
  const { ossData, getOssData, uploadRequest, isUploading } = useUploader()
  const uploadError = ref<boolean>(false)

  const language = router.currentRoute.value.query.language as string
  const { t } = useLocale(language)

  const uploadImage = (type: string, blob: Blob) => {
    if (!ossData.value) return
    // 用时间戳生成文件名
    const fileName = `${Date.now()}.${type.split('/')[1]}`
    const file = new File([blob], fileName, { type })
    const formData = new FormData()
    formData.append('key', ossData.value.dir + '/' + fileName)
    formData.append('policy', ossData.value.policy || '')
    formData.append('OSSAccessKeyId', ossData.value.access_id || '')
    formData.append('success_action_status', '200')
    formData.append('signature', ossData.value.signature || '')
    formData.append('file', file)
    void uploadRequest(formData, t('toast.upload-failed')).then(() => {
      emit('add', ossData.value?.dir + '/' + fileName)
    }).catch(() => {
      uploadError.value = true
    })
  }

  const pickImage = () => {
    if (isUploading.value) return
    if (jsBridge.inDramawaveApp) {
      uploadError.value = false
      void jsBridge('pickImage').then((res: unknown) => {
        if (!res) return
        if (!ossData.value) return
        const r = res as { rawData: string | null, uri: string | null }
        if (r.rawData) {
          console.log(document.cookie)
          // rawData是一个base64字符串，转成bin data
          const data = atob(r.rawData.split(',')[1])
          const len = data.length
          const bytes = new Uint8Array(len)
          for (let i = 0; i < len; i++) {
            bytes[i] = data.charCodeAt(i)
          }
          const type = r.rawData.split(',')[0].split(':')[1].split(';')[0]
          const blob = new Blob([bytes], { type })
          uploadImage(type, blob)
        }
      })
    } else {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      input.style.display = 'none'

      input.onchange = async (e: Event) => {
        const file = (e.target as HTMLInputElement).files?.[0]
        if (!file || !ossData.value) return

        // 生成OSS文件名
        const extension = file.type.split('/')[1] || ''
        const fileName = `${Date.now()}.${extension}`

        // 构造表单数据
        const formData = new FormData()
        formData.append('key', ossData.value.dir + '/' + fileName)
        formData.append('policy', ossData.value.policy || '')
        formData.append('OSSAccessKeyId', ossData.value.access_id || '')
        formData.append('success_action_status', '200')
        formData.append('signature', ossData.value.signature || '')
        formData.append('file', file, fileName) // 确保文件名一致性

        try {
          await uploadRequest(formData, t('toast.upload-failed'))
          emit('add', `${ossData.value.dir}/${fileName}`)
        } catch {
          uploadError.value = true
        }
      }

      document.body.appendChild(input)
      input.click()
      document.body.removeChild(input)
    }
  }

  onMounted(() => {
    void getOssData()
  })

  return () => (
    <MergeClass tag="div" baseClass="w-full flex justify-start items-center gap-x-3">
      {props.list?.map((item, index) => (
        <x-image-wrapper class="relative size-[88px] cursor-pointer rounded-lg border border-solid border-[var(--grey-10)] bg-[var(--grey-13)]">
          <img src={ossData.value?.static_domain + '/' + item} class="absolute left-0 top-0 z-up size-full rounded-lg object-cover" />
          <img onClick={() => emit('remove', index)} src={del} class="absolute right-1 top-1 z-up-up size-5 rounded-full" />
        </x-image-wrapper>
      ))}
      {
        props.list.length < 3
        && (
          <x-upload-add onClick={pickImage} class={mc('no-tap-color size-[88px] p-8 bg-[var(--grey-13)] rounded-lg border border-solid border-[var(--grey-10)] relative flex justify-center items-center cursor-pointer', uploadError.value && 'border-[var(--brand-6)]')}>
            {!isUploading.value && <img src={add} class="size-6 object-cover" />}
            {isUploading.value && <img src={loading} class="size-5 animate-spin rounded-full" />}
            {uploadError.value && <img src={fail} class="absolute right-1 top-1 z-up-up size-5 rounded-full" />}
          </x-upload-add>
        )
      }
    </MergeClass>
  )
})
