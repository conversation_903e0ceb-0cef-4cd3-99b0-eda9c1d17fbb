import { ClassName, createComponent, mc } from '@skynet/shared'
import dayjs, { ConfigType } from 'dayjs'
import { computed } from 'vue'
type DateTimeOptions = {
  props: {
    class?: ClassName
    value?: ConfigType
    format?: 'date' | 'time' | 'datetime' | 'relative' | 'app' | string
    invalid?: string
  }
}
export const DateTime = createComponent<DateTimeOptions>({
  inheritAttrs: false,
  props: {
    class: '',
    value: null,
    format: 'datetime',
    invalid: '- 空 -',
  },
}, props => {
  const format = computed(() => {
    if (props.format === 'date') return 'YYYY-MM-DD'
    if (props.format === 'time') return 'HH:mm:ss'
    if (props.format === 'datetime') return 'YYYY-MM-DD HH:mm'
    // app 表示与天脉 App 保持一致
    if (props.format === 'app') return 'MM/DD HH:mm'
    // 目前不支持相对时间
    if (props.format === 'relative') return 'YYYY-MM-DD HH:mm'
    return props.format
  })
  const getTimeString = () => props.value as number > 0 ? dayjs(props.value).format(format.value) : props.invalid
  const result = computed(getTimeString)
  return () => (
    <time class={mc('', props.class)}>{result.value}</time>
  )
})
