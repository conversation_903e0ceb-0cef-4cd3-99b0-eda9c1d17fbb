import { createApp } from 'vue'
import router from './router.tsx'
import { App } from './app'
import '../../dramato-admin-web/src/assets/style/global.css'
import './global.css'
import 'src/init/init'
import { Wrapper } from 'src/layout/wrapper.tsx'
import { Demo } from 'src/modules/common/demo.tsx'
import { Icon } from '@iconify/vue'

const app = createApp(App)
app.use(router)
app.component('wrapper', Wrapper)
app.component('demo', Demo)
app.component('icon', Icon)

app.mount('#app')
