declare namespace Api {

  namespace VoiceoverResource {
    interface ListReqParams {
      series_resource_id?: number // 剧名
      page_index?: number // 当前页索引
      page_size?: number // 每页大小
    }

    interface IAiVoiceResourceInfo {
      num: number
      srt_path: string
      audio_path: string
      annotation: number
      voice: number
      ora_video_path: string
      pure_video_path: string
    }

    interface ISrt {
      language: string
      infos: IAiVoiceResourceInfo[]
    }

    interface IMultipleResource {
      [record?: string]: string
      num: number
    }

    interface IAiVoiceResource {
      default_language_code: string
      srts: ISrt[]
    }

    interface IResourceAudio {
      num: number
      ora_path: string
      voice_path: string
      background_path: string
      noaudio_path: string
    }

    interface IShortAiTask {
      err_msg: string
      language: string
      serial_number: number
      status: number
    }
    interface ISyllable {
      content: string
      content_syllable_count: number
      line_number: number
      shorten_content: string
      shorten_content_syllable_count: number
    }
  }
}
