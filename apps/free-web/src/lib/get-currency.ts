import { inApp, jsBridge } from './jsbridge'

export type CurrencyItem = {
  currency_symbol: string
  currency_code: string
  sku_id: string
  price: string
  localized_title: string
  key: string
  formatted_price: string
}

export let currencyList: CurrencyItem[] | undefined = undefined

export const getCurrency = async () => {
  console.log('getCurrency')
  if (!inApp) return
  if (currencyList) return currencyList
  currencyList = await jsBridge<CurrencyItem[]>('currency')
  return currencyList
}
