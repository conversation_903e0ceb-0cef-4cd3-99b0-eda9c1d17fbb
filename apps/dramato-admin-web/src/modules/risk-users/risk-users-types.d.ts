declare namespace M {
  interface PushStyle {
    push_type: string
    title_prefix: string
    title: string
    sub_title: string
    body: string
    image: string
    small_image: string
    tips: string
    tips_style: string
    media_progress: string
    has_btn1: boolean
    btn1_bg_color: string
    btn1_text: string
    btn1_deep_link: string
    has_btn2: boolean
    btn2_text: string
    btn2_deep_link: string
  }

  interface RiskUsersItem {
    id?: number
    user_id?: number
    device_id?: string
    start_time?: number
    end_time?: number
    release_status?: number
    release_user_id?: number
    release_time?: number
    control_type?: number
    uid?: number
  }

  interface AuditItem {
    id?: number
    auth_status?: number
    failed_reason?: string
  }

  interface RiskUsersItemResponse {
    total: number
    risk_user_info: RiskUsersItem[]
  }

  interface Limit {
    min: number
    max: number
  }

  interface Tips {
    [key: string]: string
  }

  interface InputField {
    limit: Limit
    tips: string
  }

  interface PushTypeTips {
    [key: string]: string
  }

  interface DeeplinkTips {
    [key: number]: string
  }

  interface Form {
    title_input: InputField
    content_input: InputField
    push_type: {
      tips: PushTypeTips
    }
    deeplink: {
      tips: DeeplinkTips
    }
    btn1: {
      limit: Limit
      tips: string
    }
    btn2: {
      limit: Limit
      tips: string
    }
  }

  interface VarTpl {
    [key: string]: string
  }

  interface PushConfigData {
    form: Form
    var_tpl: VarTpl
  }
  // 1:tile,2:content,3:tips,4:btn1,5:btn2
  enum TranslateType {
    title = 1,
    content = 2,
    tips = 3,
    btn1 = 4,
    btn2 = 5,
  }

  interface Deeplink {
    name: string
    path: string
  }

  interface TranslateItem {
    text: string | boolean
    type: M.TranslateType
    translate: Record<string, string>
  }

  interface CreatePushForm {
    push_task_id?: number
    list_type: number
    resource_scope_type: number
    resource_scope_val: string
    target_user_type: number
    target_user_val: string
    ageing_type: number
    target_app_names_source: string[]
    target_app_names: string
    timed_start_date_time: string
    timed_end_date_time: string
    end_now_add_hours: number
    end_now_add_minute: number
    style_config: {
      push_type: string
      buttonOrProgress: string
      title: string
      body: string
      image: string
      small_image: string
      has_tips: boolean
      tips: string
      tips_style: string
      media_progress: string
      media_progress_type: string
      has_btn1: boolean
      btn1_bg_color: string
      btn1_text: string
      btn1_deep_link: string
      btn1_deep_link_custom?: string
      btn1_deep_link_url: string
      has_btn2: boolean
      btn2_text: string
      btn2_deep_link: string
      btn2_deep_link_custom?: string
      btn2_deep_link_url: string
    }
    translate_list: TranslateItem[]
    rec_type?: number
  }

  interface PreviewReplaceResult {
    language: string
    title: string
    body: string
  }

  interface Config {
    lang_items: {
      [key: string]: string
    }
  }

  interface RiskUsersSearchOption {
    user_id?: number
    device_id?: string
    page_index?: number
    page_size?: number// 选填参数
  }
}
