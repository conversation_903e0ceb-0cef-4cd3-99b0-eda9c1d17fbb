import { createComponent, getQueries } from '@skynet/shared'
import { Back } from '../common/back/back'
import { alertTip, channel, rechargeBg } from './images/images'
import { onMounted, ref } from 'vue'
import { T } from './activity-payment.i18n'
import CommonButton from './common-button'
import CommonRules from './common-rules'
import { useActivityPayment } from './use-activity-payment'
import { apiGetShareStatus, apiUidLogin } from './activity-payment-api'
import { throttle } from 'lodash-es'
import { openDialog } from 'src/modules/common/dialog'
import { SvgIcon } from '@skynet/ui'
import { showToast } from 'src/modules/common/toast/toast'
import { useLocale } from '../i18n/i18n'
import { supportedLocaleList } from '../common/constants'
import { h5Track } from 'src/lib/h5-track'
import { useRouter } from 'vue-router'

export const ActivityRechargePage = createComponent(null, () => {
  const inputRef = ref<HTMLInputElement>()
  const router = useRouter()
  const { user, activityAirwallexPay, refereeAvatar, bonus, status } = useActivityPayment()
  const userId = ref(user.value?.user_id)
  const payLoading = ref(false)
  const { product_id, from, share_id, language, currency, price } = getQueries({
    product_id: 0,
    from: '',
    share_id: '',
    language: '',
    currency: '',
    price: 0,
  })
  const { currentLocale } = useLocale()
  const locale = supportedLocaleList.find(item => item.shortCode === (language ?? 'en'))
  if (locale) {
    currentLocale.value = locale
  }
  const handleInput = (e: Event) => {
    userId.value = (e.target as HTMLInputElement).value
  }
  onMounted(() => {
    inputRef.value?.focus()
    document.title = T.paymentPage.paySubtitle.ref.value
    // 有uid说明是分享过了
    if (user.value?.user_id) {
      // 查询分享状态
      void apiGetShareStatus(Number(user.value.user_id)).then(res => {
        refereeAvatar.value = res.data?.referee_avatar ?? ''
        bonus.value = res.data?.bonus ?? 0
        status.value = res.data?.status ?? 0
        if (res.data?.status !== 0) {
          void router.push({ path: '/activity/recharge-success', query: { ...router.currentRoute.value.query, share_success: '1', uid: user.value?.user_id } })
        }
      }).catch(err => {
        console.log('err', err)
      })
    }
  })

  const openAlertTip = () => {
    openDialog({
      body: (
        <div class="relative block">
          <img src={alertTip} class="w-300px h-auto object-cover" />
        </div>
      ),
      closeVisible: true,
    })
  }

  const handleConfirm = throttle(async () => {
    if (!userId.value) return
    payLoading.value = true
    h5Track('H5_webrecharge', 'confirm', 'click', {
      user_id: userId.value,
      product_id: product_id,
      currency,
      price,
    })
    try {
      // 查询分享状态
      const statusRes = await apiGetShareStatus(Number(userId.value))
      if (statusRes.code === 200) {
        refereeAvatar.value = statusRes.data?.referee_avatar ?? ''
        bonus.value = statusRes.data?.bonus ?? 0
        if (statusRes.data?.status !== 0) {
          void router.push({ path: '/activity/recharge-success', query: { ...router.currentRoute.value.query, status: statusRes.data?.status, share_success: '1' } })
        }
      }
      const loginRes = await apiUidLogin(Number(userId.value))
      if (loginRes.code === 200) {
        user.value = {
          user_id: userId.value,
          user_avatar: loginRes.data?.user_avatar ?? '',
        }
        localStorage.setItem('user', JSON.stringify(user.value))
        await activityAirwallexPay(product_id, share_id)
        payLoading.value = false
      }
    } catch (err) {
      payLoading.value = false
      showToast(
        <p class="text-center text-sm text-[#434546]">{T.paymentPage.userNotFound()}</p>,
        'error', // type
        { class: 'rounded-xl !px-4 !py-3 !bg-white' })
    }
  }, 1000)
  return () => (
    <x-activity-recharge-page class="relative block min-h-screen bg-gradient-to-b from-rose-500 to-rose-500 pb-12">
      <Back class="bg-transparent pr-9" isWhite title={T.paymentPage.paySubtitle()} />
      <x-card class="pt-60px relative block px-3">
        <img src={rechargeBg} class="absolute inset-0 left-1/2 z-up h-auto w-[calc(100%-1.5rem)] -translate-x-1/2" />
        <x-card-title class="top-26px absolute left-6 z-up-up block text-center text-lg capitalize leading-snug text-white">
          {T.paymentPage.account()}
        </x-card-title>
        <x-card-content class="relative z-up-up block rounded-[20px] bg-white px-3 pb-3 pt-6 shadow-[0px_4px_8px_0px_rgba(207,0,37,0.50)]">
          <x-dramawave-id class="outline-black-10%/10 block rounded-lg px-3 outline outline-1 outline-offset-[-1px]">
            <div class="border-black-10%/10 border-b-solid border-b py-4 text-base leading-tight text-[#434546]">DramaWave UID</div>
            <input ref={inputRef} placeholder={T.paymentPage.placeholder()} class="w-full border-none bg-transparent px-0 py-4 text-[#434546] caret-[#fc2763] outline-none placeholder:text-[#CCCACB]" value={userId.value} onInput={handleInput} />
          </x-dramawave-id>
          <x-find-uid class="flex items-center justify-end py-3">
            <div class="text-xs font-normal text-[#0766FF]" onClick={openAlertTip}>
              <span>{T.paymentPage.findUid()}</span>
            </div>
          </x-find-uid>
          <CommonButton disabled={!userId.value} text={T.paymentPage.confirm()} onClick={handleConfirm} />
        </x-card-content>
      </x-card>
      <x-channel class="block px-6 pb-2 pt-4">
        <img src={channel} class="h-6 w-full" />
      </x-channel>
      <CommonRules />
      {payLoading.value && (
        <x-mask class="fixed inset-0 z-dialog flex size-full items-center justify-center bg-[var(--mask-1)]">
          <SvgIcon name="payment/loading"
            class="size-8 animate-spin" />
        </x-mask>
      )}
    </x-activity-recharge-page>
  )
})

export default ActivityRechargePage
