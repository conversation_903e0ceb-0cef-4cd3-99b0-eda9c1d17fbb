export const ko = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: '인기 선택',
    },
  },
  myList: {
    watchHistory: `수청 기록`,
    myList: '마이 리스트',
    seeAll: '모두 보기',
    noData: '관련 결과 없음',
  },
  // 分享页
  share: {
    watchNow: '지금 보기',
    episodeDialog: {
      title: '나와 함께 참여하고 추가 코인을 얻자!',
      description: '이 드라마에 빠졌어요!',
      tips: '추천',
      button: '드라마 전체 보기',
      limit24: '제한된 시간입니다: 24h',
    },
    toast: {
      takeOffShelf: '사용할 수 있는 콘텐츠가 없습니다.',
    },
  },
  'customer-service-center': {
    title: '고객 서비스 센터',
    description: '설명',
    'description-placeholder': '문제를 이해하는 데 도움이 되는 세부 정보 제공',
    'email-placeholder': '피드백 업데이트를 받으려면 이메일을 입력하세요',
    'submit-feedback': '제출',
    'enter-feedback': '기타 문제 피드백',
    'upload-pictures': '사진 업로드',
    feedback: '피드백',
    'problem-type': '문제 유형',
    'problem-type-placeholder': '선택해주세요',
    'payment-issue-placeholder': 'ID와 주문한 영수증을 제공해 주세요: Google: https://pay.google.com/ (주문 번호는 GPA로 시작...) iOS: 앱 스토어 - Apple ID - 구매 내역(주문 ID)',
    'content-issue-placeholder': '드라마 제목, 에피소드 번호 및 누락된 자막 언어를 제공해 주시기 바랍니다',
    'ads-issue-placeholder': '설비 모델명, 국가 및 언어 설정을 제공해 주시기 바랍니다',
    'technical-issue-placeholder': '설비 모델명, 국가 및 언어 설정을 제공해 주시기 바랍니다',
    'other-issue-placeholder': '설비 모델명과 국가를 포함한 자세한 정보를 제공해 주시기 바랍니다',
    'i-want-to-give-feedback': '피드백을 주고 싶다',
    'are-you-satisfied-with-this-reply': '이번의 답변이 마음에 드십니까?',
    unsatisfied: '불만족',
    satisfied: '만족',
    myFeedback: '나의 피드백',
    'reply-to-feedback': '피드백에 대한 답변',
    'description-min-length': '문제 설명을 6글자 이상으로 입력하세요.',
    'problem-type-not-choose': '피드백의 유형을 선택하세요.',
    'description-empty': '입력 내용을 비워둘 수 없습니다.',
  },
  terminate: {
    question: 'サブスクリプションはどうやって管理しますか？',
    desc: '아래 버튼을 클릭해 구독 관리 페이지로 이동하세요. 구독 스위치를 클릭하면 구독 상태를 관리할 수 있습니다.',
    title: '구독 관리',
    expiration: '구독 만료 시각',
    manageDesc: '구독 관계',
  },
  toast: {
    'upload-failed': '업로드에 실패했습니다. 다시 업로드하세요',
    'submit-success': '제출 성공',
    'email-incorrect': '이메일 형식이 올바르지 않습니다',
  },
  not_found: {
    title: '찾을 수 없는 페이지',
  },
  regulations: {
    howUseData: '데이터 사용 방법',
    content: `앱 추적 투명성 권한 요청에서 "허용"을 선택하면 정보를 사용하여 서비스를 최적화하고 최상의 경험을 제공할 것입니다. 그렇지 않으면 데이터를 수집할 뿐, 추적 활동은 수행하지 않습니다.

DramaWave 및 파트너와 공급업체가 기기에서 데이터를 사용하여 애플리케이션 사용 방법을 분석하고 콘텐츠 추천을 개인화하며 정보 관련 서비스를 제공하는 데 동의하는 경우, "동의하고 계속"을 클릭하십시오.`,
    acceptAll: '모두 수락',
    dataSettings: '데이터 설정',
    next: '다음',
    cancel: '취소',
    aboutYourPrivacy: '개인정보 보호에 대하여',
    personalDataName: '개인화된 데이터',
    personalDataDescription: '이 정보를 사용하여 선택을 기억하고 개인화된 광고 및 콘텐츠 추천, 알림 및 알림 등을 제공합니다.',
    advertisementDataName: '광고 데이터',
    advertisementDataDescription: '온라인 광고 목적으로 쿠키, 기기의 식별자 또는 기타 정보가 기기에 저장되거나 액세스될 수 있습니다. 사용자 데이터는 광고 파트너와 공유될 수도 있습니다.',
    behaviorsDataName: '행동 데이터',
    behaviorsDataDescription: '쿠키나 기기 식별자와 같은 분석 관련 데이터는 사용, 행동 및 광고 성과를 측정하고 분석하기 위해 저장될 수 있습니다.',
    privacyAndTerms: '개인정보 보호정책 및 서비스 약관',
    referToPrivacy: '개인정보 보호정책을 참조하십시오',
    referToTerms: '서비스 약관을 참조하십시오',
    agreeAndSave: '동의하고 저장',
  },
  coins: {
    earnings: {
      title: '내 수입',
      total: '총 다이아몬드',
      exchange: '교환',
      earningsHistory: '수익 기록',
      exchangeHistory: '교환 기록',
      noRecords: '기록 없음',
      last50earnings: '마지막 50개의 수익만 표시됩니다',
      last50exchanges: '마지막 50개의 교환 기록만 표시됩니다',
      benefits: '혜택',
      gotIt: '받기',
    },
    exchange: {
      title: '교환 존',
      notOpen: '오픈 미완성,채널 고정해 주세요.',
      receiveBtn: '해피 리셉션',
      earnCoins: '다이아몬드 획득',
      redeemRequiredXCoins: '환전하려면 {X} 다이아몬드가 필요합니다. ',
      exchangeSuccess: '교환이 완료되었습니다. ',
      cardId: '카드 ID',
      exchangeFailed: '교환이 실패되었습니다.',
      transferFailedInsufficientStock: '품절되었습니다. 내일 일찍 오세요.',
      gotItBtn: '알겠습니다',
      transferFailedRiskControl: '계정이 비정상적이어서 일시적으로 교환불가입니다.',
      tryAgainBtn: '나중에 다시 시도하세요',
      transferConfirm: '전송 확인',
      paypalAccountEmail: '페이팔 계정 이메일',
      confirmPaypalAccountEmail: '페이팔 계정 이메일 확인',
      transferX: '{X}를 송금하기',
      transferXConfirm: '{X}가 송금되었습니다',
      transferToYourAccountX: '계정에 {X}를 송금하기',
      confirmBtn: '확인',
      redeemFailed: '상환 실패',
      redeemFailedContent: '오류가 발생했습니다. 나중에 다시 시도해 주세요.',
      transferSuccessTitle: '저희가 돈을 보낼 거예요!',
      transferSuccessContent: '귀하의 돈은 5분에서 72시간 이내에 도착할 것입니다.',
      copySuccess: '카피 성공',
      enterEmailError: '유효한 이메일 주소를 입력해 주세요.',
      enterEmailValidError: '입력 오류,다시 입력해 주세요.',
      emailNotMatchError: '이메일 주소가 일치하지 않습니다. 다시 입력해 주세요.',
    },
  },
  meal_check_in: {
    sleeping_link: '잠자는 동안 다이아몬드 획득하기',
    rules_title: '활동 규칙',
    benefits_title: 'Benefits',
    rules_1: '1. 매일 밤 7시부터 12시까지 \'자고 돈 버는\' 모드를 활성화할 수 있습니다.',
    rules_2: '2. 모드를 활성화한 후, 다음 날 8시간을 자고 나서 오전 8시부터 12시까지 보상받을 수 있습니다.',
    rules_3: '3. 보상 받을 시간을 놓친 경우, 다음 날 12시부터 7시까지 영상을 보고 보상을 다시 받을 수 있습니다.',
  },
  sleep_check_in: {
    earn_link: '식사를 기록하여 얻기',
    benefits_title: 'Benefits',
    rules_1: '1. 매일 네 번의 시간대에 식사를 기록할 수 있습니다: 아침: 07:00-10:00; 점심: 11:00-14:00; 저녁: 17:00-20:00; 야식: 21:00-24:00.',
    rules_2: '2. 해당 시간을 놓쳤다면, 보상을 되찾기 위해 동영상을 시청할 수 있습니다.',
    rules_3: '3. 보상은 매일 자정에 초기화되므로, 보상을 잊지 말고 꼭 받으세요.',
  },
  bind_email: {
    title: '이메일 주소를 입력해주세요',
    list_1: '이메일 주소를 연동하면 보너스 코인을 받을 수 있습니다.',
    list_2: '다가오는 이벤트 및 특별 혜택 알림 받을 수 있습니다.',
    list_3: '새로운 작품을 먼저 알 수 있습니다.',
    email_placeholder: '이메일 주소를 입력해주세요.',
    verify: '인증',
    email_error: '유효된 이메일 수소를 입력해주세요',
    verify_progress: '이메일 주소 인증중',
    verify_success: '수령 성공!',
    re_enter: '다시 입력하기',
    email_repeat: '본 이메일 조소는 이벤트에 참여했습니다!',
  },
  infinityScroll: {
    end: 'The End',
    loading: '로드 중...',
  },
}
