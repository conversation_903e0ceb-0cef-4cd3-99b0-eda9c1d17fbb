import { type StringObject, tryParseJson } from '@skynet/shared'
import { fn } from '@skynet/shared/create-component'
import { computed, Fragment, h, ref, watch, type ComputedRef } from 'vue'
import { createI18n } from 'vue-i18n'
import { z } from 'zod'
import { zodErrorMaps } from '~/lib/zod/error-map/error-map'
import { each, cloneDeep } from 'lodash-es'
import { supportedLocaleList, type SupportedLocale, supportedLocaleStrings } from '../common/constants'

type LocaleObject = typeof supportedLocaleList[0]
const currentLocale = ref<LocaleObject>(getLocaleObject())

export const i18n = createI18n({
  locale: currentLocale.value.language,
  fallbackLocale: 'en-US',
  legacy: false,
  messages: { },
})

type I18nMsg<T extends StringObject> = {
  [key in SupportedLocale ]: T
}

type Callable = {
  (arg1?: unknown, arg2?: unknown): string
  string: string
  ref: ComputedRef<string>
}

type LeafCallable<T extends StringObject> = {
  [K in keyof T]: T[K] extends string
    ? Callable
    : (T[K] extends StringObject ? LeafCallable<T[K]> : never)
}

export const mergeI18n = <T extends StringObject>(messages: I18nMsg<T>) => {
  // 递归地检查每种语言的 key 是否一致
  const firstKey = Object.keys(messages)[0] as SupportedLocale
  const firstLocale = messages[firstKey]
  const checkKeys = (loc1: SupportedLocale, obj1: StringObject, loc2: SupportedLocale, obj2: StringObject, path: string[] = []): boolean => {
    const keys1 = Object.keys(obj1)

    return keys1.every(key => {
      const newPath = path.concat(key)
      if (!(key in obj2)) {
        console.warn(`${loc2} 缺少 ${loc1} 的 ${newPath.join('.')}`)
        return false
      }

      const val1 = obj1[key]
      const val2 = obj2[key]

      if (typeof val1 === 'object' && typeof val2 === 'object') {
        return checkKeys(loc1, val1, loc2, val2, newPath)
      }

      return true
    })
  }

  ;(Object.keys(messages) as SupportedLocale[]).forEach(locale => {
    if (locale === Object.keys(messages)[0]) return
    const isValid = checkKeys(firstKey, firstLocale, locale, messages[locale])
    if (!isValid) {
      console.warn(`Translation keys mismatch found in locale: ${locale}`)
    }
  })

  each(messages, (obj, locale) => {
    // @ts-expect-error i18n 的类型写的不好
    const old = i18n.global.messages.value[locale]
    if (old) {
      each(obj, (value, key) => {
        if (key in old) {
          console.warn(`${locale} 中 ${key} 已存在`)
        }
      })
    }
    i18n.global.mergeLocaleMessage(locale, obj)
  })

  const p = new Proxy({}, {
    get(_, prop, r) {
      const path = [] as string[]
      if (typeof prop === 'symbol') return null
      path.push(prop)
      function get(_: object, prop2: string) {
        if (prop2 === 'string') {
          const str = i18n.global.t(path.join('.'))
          path.splice(0, path.length)
          return str
        } else if (prop2 === 'ref') {
          return computed(() => i18n.global.t(path.join('.')))
        } else {
          path.push(prop2)
          return proxy
        }
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const apply = (_: object, prop2: string, args: any[]) => {
        const str = i18n.global.t(path.join('.'), args[0], args[1])
        const pathStr = path.join('.')
        path.splice(0, path.length)
        if (!/<[^>]*>/.test(str)) { return str }
        const lines = str.split(/<br\s?\/?>/).map(line => h('span', { 'data-i18n': pathStr }, line))
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .reduce((acc, curr, i) => i === 0 ? [curr] : [...acc, h('br'), curr], [] as any[])
        return h(Fragment, lines)
      }
      const proxy = new Proxy(fn, { get, apply })
      return proxy
    },
  })
  return p as LeafCallable<(typeof messages)['en-US']>
}

watch(() => currentLocale.value, newLocale => {
  if (supportedLocaleStrings.includes(newLocale.language)) {
    localStorage.setItem('locale', JSON.stringify(newLocale))
    i18n.global.locale.value = newLocale.language
    z.setErrorMap(zodErrorMaps[newLocale.language] ?? zodErrorMaps['en-US'])
  }
}, { immediate: true, deep: true })
export const useLocale = () => {
  return {
    currentLocale,
  }
}

function getTimezone() {
  try {
    // 通过 Intl API 获取时区名称（如：'Asia/Shanghai'）
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    return timeZone
  } catch (e) {
    // 旧浏览器回退方案：计算 UTC 偏移量（如：UTC+8）
    const offset = new Date().getTimezoneOffset()
    const hours = Math.abs(Math.floor(offset / 60)).toString().padStart(2, '0')
    const minutes = Math.abs(offset % 60).toString().padStart(2, '0')
    const sign = offset > 0 ? '-' : '+' // 注意符号逻辑
    return `UTC${sign}${hours}:${minutes}`
  }
}

// 如果用户是简体中文，返回繁体中文，否则返回用户的语言
// 2025-03-24 产品要求，后台没有配置简体中文的原因
function checkNavigatorLocale() {
  const locale = navigator.language
  return locale === 'zh-CN' ? 'zh-TW' : locale
}

export function getLocaleString() {
  const urlLocale = new URL(window.location.href).searchParams.get('locale')
  const customLocale = localStorage.getItem('locale')
  const locale = urlLocale || customLocale || checkNavigatorLocale() || 'en-US'
  return supportedLocaleStrings.includes(locale as SupportedLocale) ? locale : 'en-US'
}
export function getLocaleObject() {
  const customLocale = tryParseJson<null | LocaleObject>(localStorage.getItem('locale'), null)
  // if (!customLocale || !customLocale.language) {
  //   return supportedLocaleList.find(l => l.language === 'en-US')!
  // }
  const l = (customLocale?.language || checkNavigatorLocale() || 'en-US') as SupportedLocale
  return cloneDeep(supportedLocaleStrings.includes(l)
    ? supportedLocaleList.find(locale => locale.language === l)!
    : supportedLocaleList.find(l => l.language === 'en-US')!)
}
