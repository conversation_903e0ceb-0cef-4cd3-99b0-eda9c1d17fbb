/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc } from '@skynet/shared'
import { Checkbox } from '@skynet/ui'
import { computed, ref, watch } from 'vue'
import { groupBy } from 'lodash-es'
type ProfileItemInputOptions = {
  props: {
    // ['valid', '1', '2', '3', '____']
    // ['valid', '1-7', '2-8', '3-9', '____']
    modelValue?: string[]
    valueLength?: 1 | 2 | number
    valueType?: 'string' | 'number'
    valuePrefix?: string
    valueConcat?: string
    valueSuffix?: string
    namePrefix?: string
    nameConcat?: string
    nameSuffix?: string
    options?: {
      value: string
      name?: string
      group_name?: string
      disabled?: boolean
    }[]
    canCustom?: boolean
    optionsType?: boolean
  }
  emits: {
    'update:modelValue': (value: string[]) => void
  }
}
export const ProfileItemsInput = createComponent<ProfileItemInputOptions>({
  props: {
    modelValue: [],
    valueLength: 1,
    valueType: 'string',
    valuePrefix: '',
    valueConcat: '',
    valueSuffix: '',
    namePrefix: '',
    nameConcat: '',
    nameSuffix: '',
    options: [],
    canCustom: false,
    optionsType: false,
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const onCheck = (checked: boolean, value: string) => {
    if (checked) {
      emit('update:modelValue', props.modelValue.concat(value))
    } else {
      emit('update:modelValue', props.modelValue.filter(v => v !== value))
    }
  }
  const getName = (name: string) => {
    if (props.valueLength === 1) {
      return props.namePrefix + name + props.nameSuffix
    } else {
      const parts = name.split('-')
      return props.namePrefix + parts[0] + props.nameConcat + parts[1] + props.nameSuffix
    }
  }

  const getCommonValue = (value: string) => {
    return props.valuePrefix + value + props.valueSuffix
  }

  const valueConcat = computed(() => props.valueConcat || '&&')

  const customValue = computed(
    () => {
      return props.modelValue
        .filter(v => !['valid', 'invalid'].includes(v)) // 排除 valid 和 invalid
        .find(value => props.options.findIndex(option => getCommonValue(option.value) === value) < 0, // 排除 options 中的值
        )
    },
  )

  const _customValue = ref<string>((customValue.value ?? '').replace(props.valuePrefix, '').replace(props.valueSuffix, ''))
  const inputType = computed(() => props.valueType === 'number' ? 'number' : 'text')

  const getCustomerItemIdx = () => props.modelValue
    .findIndex(value => !['valid', 'invalid'].includes(value) && !(props.options.find(option => getCommonValue(option.value) === value)), // 排除 options 中的值
    )

  const onCustomValueChange = (type: 'checkbox' | 'input', checked?: boolean) => {
    const customIdx = getCustomerItemIdx()
    const value = getCommonValue(_customValue.value)

    const m = [...props.modelValue]

    if (type === 'checkbox' && !checked) {
      emit('update:modelValue', m.filter((v, i) => i !== customIdx))
      return
    }

    if (customIdx < 0) {
      emit('update:modelValue', [...m, value])
      return
    }
    m[customIdx] = value

    emit('update:modelValue', m)
  }

  const getFlagIdx = () => {
    return props.modelValue.findIndex(value => ['valid', 'invalid'].includes(value))
  }

  const getFlag = () => {
    const flagIdx = getFlagIdx()
    if (flagIdx < 0) {
      return false
    }

    return props.modelValue[flagIdx] === 'valid'
  }

  watch(
    () => _customValue.value,
    () => {
      onCustomValueChange('input')
    },
    { immediate: false },
  )

  return () => (
    <x-profile-items-input class="flex flex-row flex-wrap items-center gap-x-2">
      <Checkbox modelValue={getFlag()} label="启用"
        class={mc(props.optionsType && 'pl-[80px]')}
        onUpdate:modelValue={b => {
          const flagIdx = getFlagIdx()
          const value = b ? 'valid' : 'invalid'
          const m = [...props.modelValue]
          if (flagIdx >= 0) {
            m[flagIdx] = value
          } else {
            m.unshift(value)
          }
          emit('update:modelValue', m)
        }}
      />
      {props.optionsType
        ? (
            <>
              <div class="flex flex-wrap w-[100%] flex-col">
                {Object.entries(groupBy(props.options, 'group_name')).map(([key, valueList]) => (
                  <div class="flex items-center w-[100%] leading-[30px]">
                    <div class="w-[100px] font-extrabold">{key}</div>
                    <div class="flex-1 flex flex-wrap">
                      {valueList.map(option => (
                        <Checkbox class="mr-[20px]" key={getCommonValue(option.value)} modelValue={props.modelValue.includes(getCommonValue(option.value))} label={getName(option.name || option.value)}
                          onUpdate:modelValue={b => onCheck(b, getCommonValue(option.value))}
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )
        : (
            <>
              {props.options.map(option => (
                <Checkbox key={getCommonValue(option.value)} modelValue={props.modelValue.includes(getCommonValue(option.value))} label={getName(option.name || option.value)}
                  onUpdate:modelValue={b => onCheck(b, getCommonValue(option.value))}
                />
              ))}
            </>
          )}
      {props.canCustom && (
        <>
          {props.options && props.options.length > 0
            ? (
                <div class="flex items-center">
                  <div class={mc('w-[100px] font-extrabold', !props.optionsType && 'hidden')}>自定义输入</div>
                  <Checkbox modelValue={props.modelValue.includes(getCommonValue(_customValue.value))} label=""
                    onUpdate:modelValue={b => onCustomValueChange('checkbox', b)}
                  />
                </div>
              )
            : null}
          {props.namePrefix}
          {props.valueLength === 1
            ? (
                <input class="input input-bordered flex items-center gap-1 w-[100px] h-8" value={_customValue.value}
                  type={inputType.value}
                  onChange={e => {
                    _customValue.value = (e.target as HTMLInputElement).value
                  }}
                />
              )
            : (
                <>
                  <input class="input input-bordered flex items-center gap-1 w-[100px] h-8"
                    value={_customValue.value?.split(valueConcat.value)[0] ?? ''}
                    type={inputType.value}
                    onChange={e => {
                      _customValue.value = (e.target as HTMLInputElement).value + valueConcat.value + (_customValue.value?.split(valueConcat.value)[1] ?? '')
                    }}
                  />
                  {props.nameConcat || '至'}
                  <input class="input input-bordered flex items-center gap-1 w-[100px] h-8"
                    value={_customValue.value?.split(valueConcat.value)[1] ?? ''}
                    type={inputType.value}
                    onChange={e => _customValue.value = (_customValue.value?.split(valueConcat.value)[0] ?? '') + valueConcat.value + (e.target as HTMLInputElement).value}
                  />
                </>
              )}
          {props.nameSuffix}
        </>
      )}
    </x-profile-items-input>
  )
})

export default ProfileItemsInput
