import { ref } from 'vue'
import { apiGetOssSign, apiGetResourceOssSign, StsSign } from './uploader.api'

const ossData = ref<StsSign>()
const ossDataBj = ref<StsSign>()
const ossDataLoading = ref(false)
const ossDataBjLoading = ref(false)
const ossStaticVideoPrefix = ref('')

const getResourceOssData = async () => {
  if (ossDataBjLoading.value) return ossDataBj.value
  ossDataBjLoading.value = true
  try {
    const rs = await apiGetResourceOssSign()
    ossDataBj.value = rs.data
  } catch (error) {
    // TODO: 处理错误
  } finally {
    ossDataBjLoading.value = false
  }
}

const getOssData = async () => {
  if (ossDataLoading.value) return ossData.value
  ossDataLoading.value = true
  try {
    const rs = await apiGetOssSign()
    ossData.value = rs.data
    ossStaticVideoPrefix.value = rs?.data?.oss_static_video_prefix || ''
  } catch (error) {
    // TODO: 处理错误
  } finally {
    ossDataLoading.value = false
  }
}

const resetOssData = () => {
  ossData.value = undefined
  ossDataLoading.value = false
}

export const useUploader = () => {
  return {
    ossData,
    ossDataLoading,
    ossDataBjLoading,
    getOssData,
    ossDataBj,
    getResourceOssData,
    ossStaticVideoPrefix,
    resetOssData,
  }
}
