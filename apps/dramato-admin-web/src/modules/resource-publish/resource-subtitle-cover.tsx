/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, CreateForm, Icon, showFailToast, showSuccessToast, transformNumber } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { langKey, langValue } from 'src/modules/resource/constant'
import { Wrapper } from 'src/layouts/wrapper'
import { z } from 'zod'
import { useValidator } from '@skynet/shared'
import { apiInjectSubtitle } from './resource-publish-api'
import { requiredLabel } from 'src/lib/required-label'

type ResourceInjectSubtitleOptions = {
  props: {}
}
export const ResourceInjectSubtitle = createComponent<ResourceInjectSubtitleOptions>({
  props: {},
}, props => {
  const btnLoading = ref(false)
  const Form = CreateForm<M.IInjectSubtitleParams>()
  const params = ref<M.IInjectSubtitleParams>({
    lang_list: [],
    force: false,
  })
  const formRules = z.object({
    series_resource_id: z.number().min(0, {
      message: '请输入资源ID',
    }),
    lang_list: z.array(z.string()).min(1, '请选择语言'),
  })
  const { error, validateAll } = useValidator(params, formRules)
  const onSave = async () => {
    if (!validateAll()) {
      return
    }

    btnLoading.value = true
    try {
      await apiInjectSubtitle(params.value)
      const res = await apiInjectSubtitle(params.value)
      if (res.data?.success) {
        showSuccessToast(`操作成功, ${res.data?.processing_code || 0}条处理中`)
      } else {
        showFailToast('操作失败')
      }
    } catch (error: any) {
      showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }
  return () => (
    <x-resource-subtitle-cover class="block">
      <section class="breadcrumbs px-4 text-sm mt-[24px]"><ul><li>素材字幕压制</li></ul></section>
      <Wrapper>
        <div class="w-[550px]">
          <Form
            class="grid grid-cols-1 gap-y-3"
            hasAction={false}
            data={params.value}
            error={error.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              [() => requiredLabel('资源ID：'), 'series_resource_id', { type: 'text', placeholder: '请输入资源ID' }, { transform: transformNumber }],
              [() => requiredLabel('语言：'), 'lang_list', { type: 'multi-select', placeholder: '请选择语言', options: langKey.map((key, index) => {
                return {
                  value: key,
                  label: langValue[index],
                }
              }) }],
              [() => requiredLabel('重新压制'), 'force', { type: 'radio', options: [{
                value: true,
                label: '是',
              }, {
                value: false,
                label: '否',
              }] }],
            ]}
          />
          <div class="flex justify-start mt-4 gap-x-2">
            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={onSave}>
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              开始压制
            </Button>
          </div>
        </div>
      </Wrapper>
    </x-resource-subtitle-cover>
  )
})

export default ResourceInjectSubtitle
