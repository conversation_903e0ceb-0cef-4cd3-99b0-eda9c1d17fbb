import { createComponent } from '@skynet/shared'
import { Tab } from '@skynet/ui'
import { ref, onMounted } from 'vue'
import { SessionLayout } from './session-layout'
import { SignInWithPassword } from './sign-in-with-password'
import { SignInWithCode } from './sign-in-with-code'
import { SvgIcon } from '@skynet/ui'
import { RouterLink } from 'vue-router'
import { sessionApi } from './session-api'
import { ElMessage } from 'element-plus'
import { useSession } from './use-session'
import { useI18n } from 'vue-i18n'

const goBackPath = '/'
type LoginRes = {
  is_limit?: boolean
  token?: string
  token_type?: string
}

export const SignInPage = createComponent(null, () => {
  const { t } = useI18n()
  const { signInWithGoogle, signInWithFacebook } = useSession()

  const kind = ref<'password' | 'code'>('password')
  const logoInLoading = ref(false)
  const bar = (
    <div class="absolute bottom-0 left-1/2 h-1 w-6 -translate-x-1/2 rounded-sm bg-[#fc2763]" />
  )

  const login = async (type: 'facebook' | 'google', token: string, path?: string, cb?: Function) => {
    try {
      const isFacebook = type === 'facebook'
      const params: LoginRes = isFacebook
        ? {
            is_limit: false,
            token: token,
          }
        : {
            token: token,
          }
      let response
      ElMessage('登录中...')
      if (type === 'facebook') {
        response = await signInWithFacebook(params)
      } else {
        response = await signInWithGoogle(params)
      }

      if (response) {
        if (cb) { cb() }
        if (path) {
          window.location.href = path
        }
      }
    } catch (error) {
      ElMessage.error(`${type} login error: ${error}`)
    }
  }

  const initializeFacebook = () => {
    window.fbAsyncInit = function () {
      FB.init({
        appId: '1041021310761996',
        cookie: true,
        xfbml: true,
        version: 'v1.0',
      })
    }
  }

  onMounted(() => {
    initializeFacebook()
  })

  const loginFaceBook = () => {
    try {
      FB.login(
        response => {
          try {
            if (response.error) {
              ElMessage.error(`Facebook Login Error: ${response.error.message}`)
            }

            // if (!response.authResponse) {
            //   ElMessage.error('No authResponse received from Facebook')
            // }

            if (response.authResponse.accessToken) {
              logoInLoading.value = true
              void login('facebook', response.authResponse.accessToken, goBackPath, () => {
                logoInLoading.value = false
              })
            } else {
              ElMessage.error('No access token received from Facebook')
            }
          } catch (error) {
            // ElMessage.error(error)
          }
        },
        { scope: 'public_profile,email' },
      )
    } catch (error) {
      console.error(error)
    }
  }

  const loginGoogle = () => {
    try {
      const client = google.accounts.oauth2.initTokenClient({
        client_id: '************-krdi8sbn4a38628shpi24k7nkrrkb0dr.apps.googleusercontent.com',
        scope: 'openid email profile',
        extraParams: {
          response_type: 'token id_token',
        },
        callback: async response => {
          try {
            if (response.error) {
              throw new Error(`Google OAuth Error: ${response.error}`)
            }

            if (response.access_token) {
              logoInLoading.value = true
              await login('google', response.access_token, goBackPath)
              logoInLoading.value = false
            } else {
              throw new Error('No access token received')
            }
          } catch (error) {
            ElMessage.error(error)
          }
        },
      })

      client.requestAccessToken()
    } catch (error) {
      console.error(error)
    }
  }

  return () => (
    <SessionLayout>
      <x-sign-in-page class="mx-[120px] block rounded-xl bg-white px-10 pb-8 pt-4 ">
        <Tab tag="h1" v-model={kind.value} class="mb-8 mt-4 gap-4" itemClass={active => ['py-2 relative', active ? '' : 'text-[#a1a0a3]']} items={[
          ['password', active => <>{t('passwordLogin')} {active && bar}</>],
          ['code', active => <>{t('verificationCodeLogin')} {active && bar }</>],
        ]} />
        {kind.value === 'password' ? <SignInWithPassword /> : <SignInWithCode />}
        <div class="mt-6 flex justify-between text-sm text-[#a1a0a3]">
          <div class="flex items-center">
            <span>{t('loginBy')} </span>
            <SvgIcon onClick={loginFaceBook} name="facebook_logo" class="ml-2 size-5 cursor-pointer" />
            <SvgIcon onClick={loginGoogle} name="google_logo" class="ml-2 size-5 cursor-pointer" />
          </div>
          <RouterLink to="/sign_up" class="block py-2 text-sm text-[#434546]">{t('registerNow')}</RouterLink>
        </div>
      </x-sign-in-page>
    </SessionLayout>
  )
})

export default SignInPage
