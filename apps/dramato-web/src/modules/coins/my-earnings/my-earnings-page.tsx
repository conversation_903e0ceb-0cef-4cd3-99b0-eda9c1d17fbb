import { createComponent, getQuery, mc } from '@skynet/shared'
import { Back } from 'src/modules/common/back/back'
import { coins as coinsIcon, noData, horn, background } from '../images/images'
import { Button } from '@skynet/ui'
import { computed, onMounted, onUnmounted, ref, watch, watchEffect } from 'vue'
import { useSwipe } from '@vueuse/core'
import { useCoinsStore } from '../coins-store'
import dayjs from 'dayjs'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { useBetterScroll } from 'src/lib/use-better-scroll'
import { preloadImages } from 'src/lib/preload-images'
import { useRouter } from 'vue-router'

export const MyEarningsPage = createComponent(null, props => {
  const tab = getQuery('tab', 'earnings')
  const router = useRouter()
  const activeTab = ref<string>(tab)
  const wrapper = ref<HTMLDivElement>()
  const tabWrapper = ref<HTMLDivElement>()
  const loadMore = ref<HTMLDivElement>()
  const isFromMyWallet = getQuery('from', '') === 'myWallet'
  const { t } = useLocale(getQuery('language', 'en'))
  const { myEarnings, myExchange, coins, getCoins, getMyEarnings, gettingMyEarnings, gettingMyExchange, getMyExchange, resetMyEarnings, resetMyExchange } = useCoinsStore()

  const { isSwiping, direction, lengthY } = useSwipe(wrapper)

  preloadImages([horn])

  const delta = computed(() => {
    if (isSwiping.value === false || direction.value !== 'up' || lengthY.value === 0 || gettingMyEarnings.value || gettingMyExchange.value) return
    if (!loadMore.value) return Infinity
    // 如果 loadMore 在 viewport 下方，距离 viewport 还有 200px，就加载更多
    const rect = loadMore.value.getBoundingClientRect()
    const clientHeight = document.documentElement.clientHeight
    return rect.top - clientHeight
  })

  const { bsInstance: tabWrapperBsInstance } = useBetterScroll(tabWrapper, 'horizontal')
  const { bsInstance: wrapperBsInstance, init: initWrapper } = useBetterScroll(wrapper)

  onMounted(() => {
    document.title = t('coins.earnings.title')
    track('myearnings', 'page', 'show')
    void getCoins()
  })

  onUnmounted(() => {
    tabWrapperBsInstance.value?.destroy()
    wrapperBsInstance.value?.destroy()
  })

  watch(() => activeTab.value, () => {
    if (activeTab.value === 'earnings') {
      resetMyEarnings()
      void getMyEarnings(1, 10)
    } else {
      resetMyExchange()
      void getMyExchange(1, 10)
    }
  }, {
    immediate: true,
  })

  watchEffect(() => {
    if (typeof delta.value === 'undefined') return
    if (activeTab.value === 'earnings') {
      if (delta.value < 200 && myEarnings.value.has_more && myEarnings.value.last_id !== undefined) {
        void getMyEarnings(myEarnings.value.page + 1, 10, myEarnings.value.last_id)
      }
    }
    if (activeTab.value === 'exchange') {
      if (delta.value < 200 && myExchange.value.has_more && myExchange.value.last_id !== undefined) {
        void getMyExchange(myExchange.value.page + 1, 10, myExchange.value.last_id)
      }
    }
  })

  watch(() => myEarnings.value.list, () => {
    if (myEarnings.value.list.length > 0) {
      initWrapper()
    }
  })

  watch(() => myExchange.value.list, () => {
    if (myExchange.value.list.length > 0) {
      initWrapper()
    }
  })

  return () => (
    <x-my-earning-page class="pad:max-w-[var(--phone-page-max-width)] pc:max-w-[var(--phone-page-max-width)] relative mx-auto flex size-full max-w-[var(--phone-page-max-width)] flex-col gap-y-4 overflow-hidden bg-[#f7f4f7] text-white">
      <img src={background} class="absolute left-0 top-0 z-0 h-auto w-full" />
      <Back title={t('coins.earnings.title')} isCloseWebview={isFromMyWallet ? false : true} isWhite={true} class="shrink-0 bg-transparent" />
      <div class="z-up relative flex w-full items-center justify-between px-5">
        <x-total-coins class="flex flex-1 flex-col items-start gap-y-2 truncate">
          <div class="truncate font-bold">{t('coins.earnings.total')}</div>
          <div class="flex items-center gap-x-1">
            <img src={coinsIcon} width={18} height={18} class="size-6 object-contain" />
            <span class="text-3xl font-bold">{coins.value ?? 0}</span>
          </div>
        </x-total-coins>
        <Button class="flex h-8 items-center justify-center rounded-lg border-none bg-[#f7f4f7] px-4 text-base text-[#fc2763]"
          onClick={() => {
            track('myearnings_page', 'exchangebtn', 'click')
            void router.push({ path: '/coins/exchange', query: { ...router.currentRoute.value.query, from: 'myearnings' } })
          }}
        >{t('coins.earnings.exchange')}
        </Button>
      </div>
      <x-history class="z-up relative mx-3 flex w-[calc(100%-1.5rem)] flex-1 flex-col overflow-hidden rounded-t-lg bg-[#FDFBFC]">
        <x-history-tab class="relative w-full shrink-0 overflow-hidden pb-3 pt-5" ref={tabWrapper}>
          <x-wrapper class="flex w-max gap-x-4 text-nowrap px-3">
            <x-history-tab-item
              onClick={() => activeTab.value = 'earnings'}
              class={
                mc('block shrink-0 w-1/2 text-left text-[#626466] whitespace-nowrap', activeTab.value === 'earnings' ? 'text-[#fc2763]' : '')
              }
            >{t('coins.earnings.earningsHistory')}
            </x-history-tab-item>
            <x-history-tab-item onClick={() => {
              track('myearnings_page', 'exchangetab', 'click')
              activeTab.value = 'exchange'
            }} class={mc('block shrink-0 w-1/2 text-left text-[#626466] whitespace-nowrap', activeTab.value === 'exchange' ? 'text-[#fc2763]' : '')}
            >
              {t('coins.earnings.exchangeHistory')}
            </x-history-tab-item>
          </x-wrapper>
        </x-history-tab>
        <x-history-area ref={wrapper} class="relative flex w-full flex-1 flex-col overflow-hidden">
          {
            activeTab.value === 'earnings'
              ? (
                  myEarnings.value?.list?.length > 0
                    ? (
                        <x-history-list key={activeTab.value} class="relative w-full text-[#090609]">
                          <x-wrapper class="share-bottom-5 block" style="-webkit-overflow-scrolling: touch;">
                            {
                            // 取前50条
                              myEarnings.value.list.slice(0, 50).map(earning => (
                                <x-history-item class="flex items-center justify-between gap-x-3 p-3">
                                  <div class="flex flex-1 flex-col gap-y-1 truncate text-sm">
                                    <div class="truncate">{earning.desc}</div>
                                    <div class="truncate text-[#A1A0A3]">{dayjs.unix(earning.created).format('YYYY-MM-DD')}</div>
                                  </div>
                                  <div class="flex shrink-0 items-center gap-1">
                                    <img src={coinsIcon} class="size-4 object-contain" />
                                    <div class="max-w-[12ex] truncate text-[#fc2763]">{earning.amount_str}</div>
                                  </div>
                                </x-history-item>
                              ))
                            }
                            <x-load-more ref={loadMore} class="invisible block h-px w-full" />
                            {
                              (myEarnings.value.list.length === 50 || !myEarnings.value.has_more)
                              && (
                                <x-limit-explain class="block w-full pb-3 pt-5 text-center text-xs text-[#A1A0A3]">
                                  {t('coins.earnings.last50earnings')}
                                </x-limit-explain>
                              )
                            }
                          </x-wrapper>
                        </x-history-list>
                      )
                    : (
                        <x-empty class="relative flex w-full flex-1 flex-col items-center justify-center">
                          <div class="flex flex-col justify-center gap-y-5">
                            <img src={noData} width={152} height={121} class="h-auto w-40" />
                            <div class="text-center text-[#a1a2a3]">{t('coins.earnings.noRecords')}</div>
                          </div>
                        </x-empty>
                      )
                )
              : (
                  myExchange.value?.list?.length > 0
                    ? (
                        <x-history-list class="relative w-full text-[#090609]">
                          <x-wrapper class="share-bottom-5 block">
                            {
                            // 取前50条
                              myExchange.value.list.slice(0, 50).map(exchange => (
                                <x-history-item class="flex items-center justify-between p-3">
                                  <div class="flex flex-1 flex-col gap-y-1 truncate text-sm">
                                    <div class="truncate">{exchange.desc}</div>
                                    {exchange.type === 'amazon' && <div class="truncate text-xs text-[#A1A0A3]">{t('coins.exchange.cardId')}: {exchange?.gift_card_secret}</div>}
                                    {exchange.type === 'ovo' && <div class="truncate text-xs text-[#A1A0A3]">{exchange?.account}</div>}
                                    <div class="truncate text-xs text-[#A1A0A3]">{dayjs.unix(exchange.created).format('YYYY-MM-DD')}</div>
                                  </div>
                                  <div class="flex items-center gap-1">
                                    <img src={coinsIcon} class="size-4 object-contain" />
                                    <div class="max-w-[10ex] truncate text-base text-[#4d4e4f]">{exchange.amount_str}</div>
                                  </div>
                                </x-history-item>
                              ))
                            }
                            <x-load-more ref={loadMore} class="invisible block h-px w-full" />
                            {
                              (myExchange.value.list.length === 50 || !myExchange.value.has_more)
                              && (
                                <x-limit-explain class="block w-full pb-3 pt-5 text-center text-xs text-[#A1A0A3]">
                                  {t('coins.earnings.last50exchanges')}
                                </x-limit-explain>
                              )
                            }
                          </x-wrapper>
                        </x-history-list>
                      )
                    : (
                        <x-empty class="relative flex w-full flex-1 flex-col items-center justify-center">
                          <div class="flex flex-col justify-center gap-y-5">
                            <img src={noData} width={152} height={121} class="h-auto w-40" />
                            <div class="text-center text-[#a1a2a3]">{t('coins.earnings.noRecords')}</div>
                          </div>
                        </x-empty>
                      )
                )
          }
        </x-history-area>
      </x-history>
    </x-my-earning-page>
  )
})

export default MyEarningsPage
