import { createComponent, getQueries } from '@skynet/shared'
import { Back } from '../common/back/back'
import { friendSuccessBg, successBg } from './images/images'
import CommonRules from './common-rules'
import { T } from './activity-payment.i18n'
import CommonShare from './common-share'
import { computed, onMounted, ref } from 'vue'
import { replaceHelper } from './replace-helper'
import { useActivityPayment } from './use-activity-payment'
import { SvgIcon } from '@skynet/ui'
import { apiGetShareStatus } from './activity-payment-api'
import { useLocale } from '../i18n/i18n'
import { supportedLocaleList } from '../common/constants'
import { h5Track } from 'src/lib/h5-track'

export const ActivityRechargeSuccessPage = createComponent(null, () => {
  const payLoading = ref(true)
  const { uid, language, currency, price, product_id } = getQueries({
    uid: '',
    language: '',
    currency: '',
    price: 0,
    product_id: '',
  })
  const { refereeAvatar, bonus, status } = useActivityPayment()

  const isShareSuccess = computed(() => status.value === 1 || status.value === 2 || status.value === 3)
  const isFriendSuccess = computed(() => status.value === 2 || status.value === 3)

  const { currentLocale } = useLocale()
  const locale = supportedLocaleList.find(item => item.shortCode === (language ?? 'en'))
  if (locale) {
    currentLocale.value = locale
  }
  onMounted(() => {
    h5Track('H5_webrecharge', 'purchase_succ', 'show', {
      user_id: uid,
      price,
      currency,
      paysource: 'airwallex',
      product_id,
    })
    payLoading.value = false
    // 有uid说明是分享过了
    if (uid) {
      // 查询分享状态
      void apiGetShareStatus(Number(uid)).then(res => {
        refereeAvatar.value = res.data?.referee_avatar ?? ''
        bonus.value = res.data?.bonus ?? 0
        status.value = res.data?.status ?? 0
      }).catch(err => {
        console.log('err', err)
      })
    }
  })

  return () => (
    payLoading.value ? (
      <x-mask class="fixed inset-0 z-dialog flex size-full items-center justify-center bg-[var(--mask-1)]">
        <SvgIcon name="payment/loading"
          class="size-8 animate-spin" />
      </x-mask>
    )
      : (
          <x-activity-recharge-success-page class="relative block min-h-screen bg-[#FF4277] pb-12">
            <Back class="bg-transparent pr-9" isWhite title="DramaWave" />
            <img src={isFriendSuccess.value ? friendSuccessBg : successBg} width={375} height={540} class="absolute inset-0 h-auto w-full object-cover" />
            <x-wrapper class="relative z-up-up block">
              {/* 标题倾斜 */}
              <x-header class="mt-4 line-clamp-2 block h-20 px-3 text-center text-4xl font-bold capitalize italic leading-10 text-white [text-shadow:_0px_3px_0px_rgb(212_0_51_/_1.00)]">{isFriendSuccess.value ? T.paymentPage.friendSuccess() : T.paymentPage.rechargeSuccessful()}</x-header>
              {isFriendSuccess.value && (
                <x-content-wrapper class="flex justify-center">
                  <x-content class="mt-3 w-auto rounded-[200px] bg-[#FF2559] px-4 py-1.5 text-base capitalize text-white shadow-[0px_-1px_0px_0px_rgba(255,255,255,0.50)]" innerHTML={replaceHelper(T.paymentPage.clainXNow(), '#fde047', true).replace(/xxx/i, bonus.value.toString())} />
                </x-content-wrapper>
              )}
              <x-content class="mt-200px flex flex-col px-3">
                <CommonShare
                  title={isFriendSuccess.value ? replaceHelper(T.paymentPage.getXCoins(), '#fde047', true).replace(/xxx/i, bonus.value.toString()) : replaceHelper(T.paymentPage.clickToShareTips(), '#fde047', true)}
                  showTips={!isShareSuccess.value}
                  showButton
                  buttonText={isShareSuccess.value ? T.paymentPage.getItNow() : T.paymentPage.clickToShare()}
                  selfSuccess
                  friendSuccess={isFriendSuccess.value}
                  inviteUserAvatar={refereeAvatar.value}
                  buttonDisabled={!isFriendSuccess.value && isShareSuccess.value}
                />
              </x-content>
              <CommonRules />
            </x-wrapper>
          </x-activity-recharge-success-page>
        )
  )
})

export default ActivityRechargeSuccessPage
