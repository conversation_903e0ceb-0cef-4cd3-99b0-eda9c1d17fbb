import { bindLoading, createComponent, getQuery, mc } from '@skynet/shared'
import { Button } from '@skynet/ui'
// import { inApp, jsBridge } from 'src/lib/jsbridge'
// import router from 'src/router'
import { onMounted, Ref, ref, watch } from 'vue'
import { useMyWalletStore } from './use-my-wallet-store'
import router from 'src/router'
import { background, coin, down, question, right, ticket } from './images/images'
import { useStretchScroll } from 'src/lib/use-stretch-scroll'
import { useLocale } from 'src/lib/use-locale'
import { openDialog } from '../common/dialog'
import { Switch } from '../common/switch/switch'
import { apiAutoUnlock } from './my-wallet-api'
import { track } from 'src/lib/track'
import { Back } from '../common/back/back'
import { deviceInfo } from 'src/lib/get-device-info'
import { authInfo } from 'src/lib/get-auth'
import { report } from '@skynet/client-track'
import { useLoadingStore } from '../common/loading/use-loading-store'

export const MyWalletPage = createComponent(null, () => {
  const isViewAll = ref(false)
  const language = getQuery('language', 'en')
  const { t } = useLocale(language)
  const autoUnlock = ref(false)
  const loading = ref(false)
  const purchasedTicketsRef = ref<HTMLElement>()
  const purchaseRewardTicketsRef = ref<HTMLElement>()
  const coinExchangeTicketsRef = ref<HTMLElement>()
  const adRewardTicketsRef = ref<HTMLElement>()
  const ticketRef = ref<HTMLElement>()
  const coinRef = ref<HTMLElement>()

  const { myWallet, getMyWallet, gettingMyWallet } = useMyWalletStore()
  const { pageLoading } = useLoadingStore()

  watch(() => gettingMyWallet.value, () => {
    if (!gettingMyWallet.value) {
      pageLoading.value = false
    }
  })

  const formatBalance = (type: 'ticket' | 'coin', balance?: number | string) => {
    if (!balance) return type === 'ticket' ? '0.0' : '0'
    if (typeof balance === 'string') return balance.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    const _balance = type === 'ticket' ? balance.toFixed(1) : balance.toFixed(0)
    return _balance.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  const balanceItem = (icon: string, type: 'ticket' | 'coin', domRef: Ref<HTMLElement | undefined>, value?: number | string, canExpand?: boolean) => {
    return (
      <div class={mc('flex items-center h-full gap-x-2 shrink-0 grow-0', myWallet.value?.show_coins ? 'w-[calc(50%-10px)]' : 'w-[60%]')} onClick={() => {
        if (!canExpand) return
        isViewAll.value = !isViewAll.value
      }}
      >
        <img src={icon} class="size-6 shrink-0" />
        <div class="text-[var(--text-1)] text-2xl font-bold break-all" ref={domRef}>{formatBalance(type, value) ?? (type === 'ticket' ? '0.0' : '0')}</div>
        {canExpand && (
          <img src={down} class={mc('size-4 mt-0.5 transition-transform duration-300 shrink-0', isViewAll.value ? 'rotate-180' : '')} />
        )}
      </div>
    )
  }

  const walletHistoryMenus = [{
    name: t('my_wallet.purchase_history'),
    path: '/my-wallet/purchase-history',
    trackName: 'purchase_history',
  }, {
    name: t('my_wallet.rewards_history'),
    path: '/coins/my-earnings',
    query: {
      tab: 'earnings',
      showBar: false,
      from: 'myWallet',
    },
    trackName: 'reward_history',
  }, {
    name: t('my_wallet.exchange_history'),
    path: '/coins/my-earnings',
    query: {
      tab: 'exchange',
      showBar: false,
      from: 'myWallet',
    },
    trackName: 'exchange_history',
  }, {
    name: t('my_wallet.consumption_records'),
    path: '/my-wallet/consumption-records',
    trackName: 'consumption_history',
  }]

  const renderWalletHistoryMenus = () => {
    return walletHistoryMenus.map(item => {
      return (!myWallet.value?.show_coins && ['reward_history', 'exchange_history'].includes(item.trackName)
        ? null
        : (
            <x-nav-item class="flex justify-between gap-3 h-11 py-3 items-center" onClick={() => {
            // @ts-expect-error never mind
              void router.push({ path: item.path, query: { ...item.query, ...router.currentRoute.value.query } })
              track('wallet', item.trackName, 'click')
            }}
            >
              <span>{item.name}</span>
              <img src={right} class="w-4 h-4" />
            </x-nav-item>
          )
      )
    })
  }

  const openHelpDialog = () => {
    const closeDialog = openDialog({
      title: t('my_wallet.help_dialog.title'),
      closeVisible: false,
      mainClass: '',
      body: (
        <x-explain-dialog>
          <x-explain class="block mt-1 px-[26px] h-[200px] font-normal text-[var(--text-1)] text-sm break-words overflow-hidden">
            <x-wrapper id="explain-wrapper" class="block pb-7" style="-webkit-overflow-scrolling: touch;">
              <p>{t('my_wallet.help_dialog.content1')}</p>
              <p>{t('my_wallet.help_dialog.content2')}</p>
              <p>{t('my_wallet.help_dialog.content3')}</p>
              <p>{t('my_wallet.help_dialog.content4')}</p>
              <p>{t('my_wallet.help_dialog.content5')}</p>
              <p>{t('my_wallet.help_dialog.content6')}</p>
            </x-wrapper>
          </x-explain>
          <x-footer class="relative flex flex-col bg-white">
            <x-mask class="-top-[38px] absolute bg-gradient-to-b from-[rgba(255,_255,_255,_0)] to-[rgba(255,_255,_255,_1)] w-full h-10" />
            <x-got-it>
              <Button class="bg-[var(--brand-7)] mx-4 mt-4 mb-6 no-tap-color border-none rounded-lg w-[calc(100%-2rem)] h-11 text-[var(--text-5)] text-base outline-none" onClick={() => closeDialog()}>{t('coins.earnings.gotIt')}</Button>
            </x-got-it>
          </x-footer>
        </x-explain-dialog>
      ),
    })
    setTimeout(() => {
      useStretchScroll(document.getElementById('explain-wrapper') as HTMLElement)
    }, 200)
  }

  watch(() => myWallet.value?.auto_unlock, () => {
    console.log('myWallet.value?.auto_unlock', myWallet.value?.auto_unlock)
    autoUnlock.value = myWallet.value?.auto_unlock === 1 ?? false
  })

  onMounted(() => {
    void getMyWallet()
    track('wallet', 'page', 'show')
  })
  const isUpdateFontSize = ref(false)
  watch(() => [coinRef.value, ticketRef.value, isUpdateFontSize.value], () => {
    if (coinRef.value && ticketRef.value) {
      isUpdateFontSize.value = false
      const coinRefLineHeight = parseFloat(getComputedStyle(coinRef.value).lineHeight)
      const coinRefFontSize = getComputedStyle(coinRef.value).fontSize
      const ticketRefLineHeight = parseFloat(getComputedStyle(ticketRef.value).lineHeight)
      const ticketRefFontSize = getComputedStyle(ticketRef.value).fontSize
      let fitFontSize = 24
      if (coinRef.value.scrollHeight > coinRefLineHeight) {
        fitFontSize = coinRefFontSize ? parseInt(coinRefFontSize) - 2 : 14
        coinRef.value.style.fontSize = fitFontSize + 'px'
        ticketRef.value.style.fontSize = fitFontSize + 'px'
        isUpdateFontSize.value = true
      }
      if (ticketRef.value.scrollHeight > ticketRefLineHeight) {
        fitFontSize = ticketRefFontSize ? parseInt(ticketRefFontSize) - 2 : 14
        coinRef.value.style.fontSize = fitFontSize + 'px'
        ticketRef.value.style.fontSize = fitFontSize + 'px'
        isUpdateFontSize.value = true
      }
    }
  })

  return () => (
    <div class="flex flex-col px-3 bg-[var(--surface-4)] size-full">
      <Back title={t('my_wallet.title')} isCloseWebview={true} class="-mx-3 bg-transparent shrink-0" />
      <x-account-balance class="relative bg-white border-none rounded-lg no-tap-color py-5 px-3 mt-2">
        <img src={background} class="absolute top-0 left-0 w-full h-auto object-cover z-up" />
        <div class="relative z-up-up">
          <div class="text-[var(--text-1)] text-xs flex items-center">
            {t('my_wallet.account_balance')}
            <img src={question} class="size-3.5 ml-1" onClick={openHelpDialog} />
          </div>
          <div class="flex w-full gap-x-5 h-8 items-center mt-5 overflow-x-hidden">
            {balanceItem(ticket, 'ticket', ticketRef, myWallet.value?.ticket_total, true)}
            { myWallet.value?.show_coins ? balanceItem(coin, 'coin', coinRef, myWallet.value?.gold_total) : null }
          </div>
          <div class={mc('flex flex-col mt-3 text-[var(--text-2)] bg-white rounded-lg px-3 text-sm relative transition-all duration-300', isViewAll.value ? 'h-48 opacity-100' : 'h-0 opacity-0')}>
            <div class="w-2.5 h-2.5 origin-top-left rotate-45 bg-white rounded-[1px] absolute top-0 left-5 -translate-y-1/2" />
            {[
              { label: t('my_wallet.purchased_tickets'), value: myWallet.value?.ticket, ref: purchasedTicketsRef },
              { label: t('my_wallet.purchase_reward_tickets'), value: myWallet.value?.ticket_bonus, ref: purchaseRewardTicketsRef },
              { label: t('my_wallet.coin_exchange_tickets'), value: myWallet.value?.ticket_coin_exchange, ref: coinExchangeTicketsRef },
              { label: t('my_wallet.ad_reward_tickets'), value: myWallet.value?.ticket_ad_reward, ref: adRewardTicketsRef },
            ].map(item => (
              <div class="w-full h-12 py-2 gap-x-3 flex justify-between items-center">
                <span ref={item.ref} class="max-w-[231px] text-left text-sm break-all">{item.label}</span>
                <span class="font-bold flex-1 text-right truncate">{item.value ?? '0.0'}</span>
              </div>
            ))}
          </div>
          <Button class="w-full h-11 mt-2 rounded-lg justify-center text-base text-[var(--text-5)] items-center flex bg-[var(--brand-6)] border-none relative z-up-up" onClick={() => {
            report({
              event: `wallet_top_up_click`,
              device_hash: deviceInfo?.device_id ?? 'unknown',
              app_version: deviceInfo?.app_version ?? 'unknown',
              user_id: authInfo?.uid ? authInfo.uid : 'unknown',
              user_source: 'app',
              // @ts-expect-error never mind
              device_manufacturer: deviceInfo?.device_manufacturer ?? deviceInfo?.manufacturer ?? 'unknown',
              device_name: deviceInfo?.device_model ?? deviceInfo?.model ?? 'unknown',
              app_package: deviceInfo?.app_name ?? 'com.freereels.app',
              event_info: JSON.stringify({
                origin: 'wallet',
                entity: 'top_up',
                event: 'click',
              }),
            })
            void router.push({ path: '/top-up', query: router.currentRoute.value.query })
          }}
          >{t('my_wallet.top_up')}
          </Button>
        </div>
      </x-account-balance>
      <x-nav class="flex flex-col text-sm bg-white mt-4 rounded-lg py-2 px-3 gap-y-1 relative z-10 text-[var(--text-2)]">
        {renderWalletHistoryMenus()}
        <x-nav-item class="flex justify-between gap-3 h-11 py-3 items-center">
          <span>{t('my_wallet.auto_unlock')}</span>
          <Switch modelValue={autoUnlock.value} onUpdate:modelValue={async v => {
            if (loading.value) return
            track('wallet', 'auto_unlock', 'click', {
              action_type: v ? 1 : 0,
            })
            autoUnlock.value = v
            await bindLoading(apiAutoUnlock({ auto_unlock: v ? 1 : 0 }), loading)
          }}
          />
        </x-nav-item>
      </x-nav>

    </div>
  )
})

export default MyWalletPage
