// 多语言替换步骤
// 解析飞书文档
// 1. 打开飞书文档，在浏览器打开
// 2. 打开控制台，选择source，新建snippet，将bin/parse-feishu.mjs代码粘贴进去，单击下方运行即可生成对应的md文件，文件名即为filename
// 3. 将md文件复制到 bin/public/dramareels/rules/ 或者 bin/public/dramawave/rules/对应的语言中去
// 4. 运行node bin/convert-md.mjs，即可生成对应的html文件
function parseFeishu(data, filename) {
    const meta = data.meta;
    const clientVars = data.clientVars;
    const blockMap = clientVars.data.block_map;
    let order;
    const blockSequence = clientVars.data.block_sequence;
    let markdown = ``;

    for (let i = 0; i < blockSequence.length; i++) {
        const block = blockMap[blockSequence[i]];
        const type = block.data.type;
        if (type === 'callout' || type ==='page' || !block.data.text) continue;
        const attributes = block.data.text.apool.numToAttrib; // {0: ['bold', 'true'], 1: ['italic', 'true'], 2: ['underline', 'true']}
        const renderAttributes = block.data.text.initialAttributedTexts.attribs[0]; // "*0*1+18*0+m"
        const content = applyAttributes(block.data.text.initialAttributedTexts.text[0], attributes, renderAttributes);
        const parentId = block.parent_id;
        let mdContent = '';
        order = block.data.seq === 'auto' ? Number(order) + 1 : block.data.seq;
        

        switch (type) {
            case 'bullet':
                // 如果下一项不是bullet，则添加两个换行符
                if (blockSequence[i + 1] && blockMap[blockSequence[i + 1]].data.type !== 'bullet') {
                    mdContent = `- ${content}\n\n`;
                } else {
                    mdContent = `- ${content}\n`;
                }
                break;
            case 'ordered':
                // 如果下一项不是ordered，则添加两个换行符
                if (blockSequence[i + 1] && blockMap[blockSequence[i + 1]].data.type !== 'ordered') {
                    mdContent = `${order}. ${content}\n\n`;
                } else {
                    mdContent = `${order}. ${content}\n`;
                }
                break;
            case 'text':
                mdContent = `${content.replace(/<EMAIL>/ig, '<<EMAIL>>').replace(/<EMAIL>/ig, '<<EMAIL>>')}\n\n`;
                break;
            case 'heading1':
                mdContent = `## ${content}\n\n`;
                break;
            case 'heading2':
                mdContent = `### ${content}\n\n`;
                break;
            case 'heading3':
                mdContent = `#### ${content}\n\n`;
                break;
            case 'heading4':
                mdContent = `##### ${content}\n\n`;
                break;
            case 'heading5':
                mdContent = `###### ${content}\n\n`;
                break;
        }

        if (parentId) {
            // 处理嵌套内容
            const parentContent = markdown.split('\n');
            let lastIndex = parentContent.length - 1;
            while (lastIndex >= 0) {
                if (parentContent[lastIndex].includes(parentId)) {
                    // 在父节点后添加缩进的子内容
                    mdContent = '    ' + mdContent;
                    parentContent.splice(lastIndex + 1, 0, mdContent);
                    break;
                }
                lastIndex--;
            }
            markdown = parentContent.join('\n');
        } else {
            markdown += mdContent;
        }
    }
    // 免费版替换Freereels为FreeReels
    // const blob = new Blob([markdown.replaceAll(/Freereels/g, 'FreeReels')], { type: 'text/markdown' });
    const blob = new Blob([markdown.replaceAll(/Dramawave/g, 'DramaWave')], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}.md`;
    a.click();
}

function applyAttributes(content, attributes, renderAttributes) {
    // 首先判断attributes是否为空数组，如果是空数组，则直接返回content
    if (!attributes[1]) {
        return content;
    }
    // attributes是规则详细，例如[['bold', 'true'], ...]，['bold', 'true']表示字体加粗规则
    if (attributes[1][0] === 'bold' || attributes[1][0] === 'link' || attributes[1][0] === 'link-id') {
        let tempContent = ''; // 临时存储content
        // renderAttributes是一个字符串规则
        // 利用正则解析renderAttributes，并应用attributes的规则
        let length = 0;
        const regex = /(\*\d)+\+[0-9a-z]+/g;
        const matches = renderAttributes.match(regex);
        console.log(matches);
        // 将matches中的规则应用到content中
        // *0*1+18*0+m 代表从content起始位置往后n1个字符，使用attributes的规则0和1，n1是36进制表示的18，再从n1开始到n2个字符，使用attributes的规则0，n2是36进制表示的m
        // *0 *1 等等，代表多个规则，+18代表往后多少个字符应用这些规则，+18是36进制表示法
        for (let i = 0; i < matches.length; i++) {
            const match = matches[i];
            if (match.match(/\*0\*1/g)) {
                const count = parseInt(match.match(/\+[0-9a-z]+/g)[0].slice(1), 36)
                const subContent = content.slice(length, length + count);
                if (subContent.trim() !== '') {
                    const newContent = attributes[1][0] === 'bold' ? 
                        ` **${subContent.trim()}** ` 
                        : attributes[1][0] === 'link' ? 
                            ` [${subContent.trim()}](${decodeURIComponent(attributes[1][1])}) ` 
                            : attributes[1][0] === 'link-id' ? 
                            ` <${subContent}> ` 
                            : subContent;
                    tempContent += newContent;
                } else {
                    tempContent += subContent;
                }
            } else {
                tempContent += content.slice(length, length + parseInt(match.match(/\+[0-9a-z]+/g)[0].slice(1), 36));
            }
            length += parseInt(match.match(/\+[0-9a-z]+/g)[0].slice(1), 36);
        }
        return tempContent;
    }
    return content;
}

parseFeishu(window.DATA, 'result')
