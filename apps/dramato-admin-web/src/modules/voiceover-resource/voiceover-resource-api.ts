import { httpClient } from 'src/lib/http-client'

export const apiGetAiVoiceResource = (data: {
  series_resource_id: number
}) => httpClient.post<ApiResponse<Api.RoleMark.IAiVoiceResource>>('/aivoice/aivoice_resource_info', data)

export const apiGenerateAiVoice = (data: {
  series_resource_id: number
  serial_numbers: number[]
  languages: string[]
}) => httpClient.post<ApiResponse<null>>('/aivoice/aivoice_voice_generate', data)

export const apiAliTransVideo = (data: {
  series_resource_id: number
  serial_numbers: number[]
  lang: string
}) => httpClient.post<ApiResponse<null>>('/series_resource/ali_video_trans', data)

export const apiShortAISrt = (data: {
  series_resource_id: number
  serial_numbers: number[]
  languages: string[]
  no_ai_horizontal?: boolean
}) => httpClient.post<ApiResponse<null>>('/aivoice/aivoice_subtitle_shorten', data)

export const apiVoiceOptimize = (data: {
  series_resource_id: number // 剧资源id
  serial_numbers: number[] // 优化的集
}) => httpClient.post<ApiResponse<null>>('/aivoice/voice_optimize', data)
