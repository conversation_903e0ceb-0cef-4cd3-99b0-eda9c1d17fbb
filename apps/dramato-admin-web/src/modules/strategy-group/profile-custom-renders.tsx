import { FormItemObject } from '@skynet/ui/form/form-types'
import { VNodeChild } from 'vue'
import { ToggleRange } from './toggle-range'
import { ToggleRegExp } from './toggle-regexp'
import { ToggleString } from './toggle-string'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ProfileItemValue = [string, ...any[]]
type Render = (profileItem?: ProfileItem) => (
  { item, onInput, value }: { item: FormItemObject, onInput: (value: ProfileItemValue) => void, value: unknown }
) => VNodeChild
type ProfileItem = {
  item_code: string
  item_description: string
  item_name: string
  item_value: string
  item_child: ProfileItem[]
}

const renderToggleString: Render = profileItem => ({ item, onInput, value }) => {
  return (
    <div class="flex items-center gap-2">
      <ToggleString modelValue={value as ProfileItemValue} onUpdate:modelValue={v => onInput(v as ProfileItemValue)} inputClass="w-[18em]" />
    </div>
  )
}

const renderToggleRange: Render = profileItem => ({ item, onInput, value }) => {
  return (
    <div class="flex items-center gap-2">
      <ToggleRange modelValue={value as ProfileItemValue} onUpdate:modelValue={v => onInput(v as ProfileItemValue)} inputClass="w-[14ex]" />
      <p class="text-gray-500 text-sm"> 输入范围 0 到 99999,可带 2 位小数 </p>
    </div>
  )
}
const renderRegexp = (valueType: 'number' | 'string' = 'number'): Render => profileItem => ({ item, onInput: _onInput, value }) => {
  return (
    <div>
      <ToggleRegExp valueType={valueType} modelValue={value as ProfileItemValue} onUpdate:modelValue={v => _onInput(v as ProfileItemValue)} profileItem={profileItem} />
    </div>
  )
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const ProfileCustomRenderers: Record<string, Render> = {
  Campaign: renderToggleString,
  InstallTime: renderToggleString,
  Cpi: renderToggleRange,
  Ecpm: renderToggleRange,
  Cpm: renderToggleRange,
  AdValue: renderToggleRange, // 无用
  AdsEcpm: renderToggleRange,
  RecentThreeDaysViewingActivities: renderRegexp('number'),
  PlatformVersion: renderRegexp('string'),
}

export const profileDefaultValues: Record<string, ProfileItemValue> = {
  Campaign: ['invalid'],
  Cpi: ['invalid', [0, 0]],
  Ecpm: ['invalid', [0, 0]],
  Cpm: ['invalid', [0, 0]],
  InstallTime: ['invalid'],
  AdsEcpm: ['invalid', [0, 0]],
}
