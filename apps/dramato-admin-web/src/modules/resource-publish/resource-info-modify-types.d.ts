declare namespace Api {
  namespace InfoModify {
    type Request = {
      title?: string
      id?: number
      partner_name?: string
      create_start_time?: number // 录入开始时间
      create_end_time?: number // 录入结束时间
      series_key_or_title_list?: string | string[]
      resource_id_or_title_list?: string | string[]
    }
    type Response = ApiResponse<{ list: M.InfoModify.Resource[], total: number }>
  }
}

declare namespace M {
  namespace InfoModify {
    type Resource = {
      id: number // 资源id
      title: string // 资源名称
      title_progress: Progress[]
      desc_progress: Progress[]
      cover_progress: Progress[]
      preview_status?: 1 | 2
      release_round: number
      claim_name: string
      audit_status?: 0 | 1 | 2 | 99 | number
      audit_reason?: string
      audit_to_status: 0 | 1 | 2 | 99 | number
      audit_to_reason: string
      partner_name: string
    }

    type Progress = {
      language_code: string
      status: number // 1 已生成
    }
  }

  interface IColor {
    id: number
    name: string
    background: number
    button: number
    font: number
  }
}
