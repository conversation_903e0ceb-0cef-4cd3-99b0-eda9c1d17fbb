export const th = {
  // 通用
  common: {
    cancel: 'ยกเลิก',
  },
  // 分享页
  share: {
    watchNow: 'ดูตอนนี้',
    episodeDialog: {
      title: 'มาร่วมกับฉันและรับเหรียญพิเศษ!',
      description: 'ฉันติดละครเรื่องนี้แล้ว!',
      tips: 'คำแนะนำ',
      button: 'ชมละครเต็มเรื่อง',
      limit24: 'เวลาแบบจำกัด: 24h',
    },
    toast: {
      takeOffShelf: 'ไม่มีเนื้อหาที่พร้อมให้บริการ',
    },
  },
  'customer-service-center': {
    title: 'ศูนย์บริการลูกค้า',
    description: 'รายละเอียด',
    'description-placeholder': 'โปรดระบุรายละเอียดเพื่อช่วยให้เราเข้าใจปัญหา',
    'email-placeholder': 'กรอกอีเมลของคุณเพื่อรับการอัปเดตความคิดเห็น',
    'submit-feedback': 'ส่ง',
    'enter-feedback': 'ปัญหาอื่นๆ',
    'upload-pictures': 'อัปโหลดรูปภาพ',
    feedback: 'ความคิดเห็น',
    faq: {
      question1: 'ทำไมถึงขึ้นว่า \'โฆษณายังไม่พร้อม\' ?',
      answer1: 'บางครั้งอาจเกิดจากการเชื่อมต่อเซิร์ฟเวอร์หรือผู้ลงโฆษณาที่ไม่เสถียร เราแนะนำให้ลองใหม่อีกครั้งในภายหลัง และหลีกเลี่ยงการกด "ดูโฆษณา" บ่อยเกินไป ข้อจำกัดในการดูโฆษณาอาจเกิดจากนโยบายของผู้ให้บริการโฆษณาหรือข้อจำกัดในประเทศของคุณ เรากำลังทำงานร่วมกับผู้ให้บริการโฆษณาเพื่อปรับปรุงประสบการณ์การรับชมโฆษณาให้ดียิ่งขึ้น',
      question2: 'ทำไมถึงขึ้นว่า \'เครือข่ายไม่พร้อมใช้งาน\' สำหรับโฆษณา?',
      answer2: 'แนะนำให้ลองรีสตาร์ทแอปโดยปิดแอปแล้วเปิดใหม่อีกครั้ง หากโฆษณายังไม่โหลด ให้รอซักครู่แล้วลองอีกครั้ง และหลีกเลี่ยงการกดบ่อยเกินไป หากปัญหายังไม่หาย คุณสามารถติดต่อเราผ่านระบบฟีดแบค เรากำลังทำงานร่วมกับทีมโฆษณาเพื่อปรับปรุงประสบการณ์การรับชม ขอบคุณสำหรับความอดทน',
    },
  },
  toast: {
    'upload-failed': 'อัปโหลดไม่สำเร็จ โปรดอัปโหลดอีกครั้ง',
    'submit-success': 'ส่งสำเร็จ',
    'email-incorrect': 'รูปแบบอีเมลไม่ถูกต้อง',
  },
  not_found: {
    title: 'หน้าที่คุณต้องการไม่มีอยู่',
  },
  regulations: {
    howUseData: 'เราจะใช้ข้อมูลของคุณอย่างไร',
    content: `หากคุณเลือก "อนุญาต" ในคำขออนุญาตความโปร่งใสในการติดตามแอป เราจะใช้ข้อมูลและข้อมูลของคุณเพื่อปรับปรุงบริการของเราและมอบประสบการณ์ที่ดีที่สุดให้กับคุณ มิฉะนั้นเราจะรวบรวมข้อมูลเท่านั้นและจะไม่ดำเนินกิจกรรมการติดตามใด ๆ

หากคุณยินยอมให้ DramaWave และพันธมิตรและผู้ให้บริการของเราใช้ข้อมูลจากอุปกรณ์ของคุณเพื่อวิเคราะห์วิธีที่คุณใช้แอปพลิเคชัน ปรับแต่งคำแนะนำเนื้อหา และให้บริการที่เกี่ยวข้องกับข้อมูลเพิ่มเติม โปรดคลิก "ยอมรับและดำเนินการต่อ" เพื่อดำเนินการต่อ`,
    acceptAll: 'ยอมรับทั้งหมด',
    dataSettings: 'การตั้งค่าข้อมูล',
    next: 'ถัดไป',
    confirm: 'ยืนยัน',
    aboutYourPrivacy: 'เกี่ยวกับความเป็นส่วนตัวของคุณ',
    personalDataName: 'ข้อมูลส่วนบุคคล',
    personalDataDescription: 'เราใช้ข้อมูลนี้เพื่อจดจำตัวเลือกของคุณและให้คำแนะนำโฆษณาและเนื้อหาที่ปรับให้เหมาะสม การแจ้งเตือนและการเตือนความจำ ฯลฯ',
    advertisementDataName: 'ข้อมูลโฆษณา',
    advertisementDataDescription: 'คุกกี้ ตัวระบุอุปกรณ์ของคุณ หรือข้อมูลอื่น ๆ อาจถูกจัดเก็บหรือเข้าถึงได้บนอุปกรณ์ของคุณเพื่อวัตถุประสงค์ในการโฆษณาออนไลน์ ข้อมูลผู้ใช้อาจถูกแชร์กับพันธมิตรโฆษณาของเราด้วย',
    behaviorsDataName: 'ข้อมูลพฤติกรรม',
    behaviorsDataDescription: 'ข้อมูลที่เกี่ยวข้องกับการวิเคราะห์ เช่น คุกกี้หรือตัวระบุอุปกรณ์ อาจถูกจัดเก็บเพื่อวัดและวิเคราะห์การใช้งาน พฤติกรรม และประสิทธิภาพการโฆษณา',
    privacyAndTerms: 'นโยบายความเป็นส่วนตัวและข้อกำหนดการให้บริการ',
    referToPrivacy: 'โปรดดูนโยบายความเป็นส่วนตัว',
    referToTerms: 'โปรดดูข้อกำหนดการให้บริการ',
    agreeAndSave: 'ยอมรับและบันทึก',
  },
  coins: {
    earnings: {
      title: 'รายได้ของฉัน',
      total: 'เหรียญรวม',
      exchange: 'แลกเปลี่ยน',
      earningsHistory: 'ประวัติรายได้',
      exchangeHistory: 'ประวัติการแลก',
      noRecords: 'ไม่มีบันทึก',
      last50earnings: 'แสดงเฉพาะ 50 รายการรายได้ล่าสุด',
      last50exchanges: 'แสดงเฉพาะ 50 รายการแลกเปลี่ยนล่าสุด',
      benefits: 'สิทธิประโยชน์',
      gotIt: 'รับทราบ',
    },
    exchange: {
      title: 'โซนแลกเปลี่ยน',
      notOpen: 'ยังไม่เปิดให้บริการ กรุณารอติดตาม',
      receiveBtn: 'รับอย่างมีความสุข',
      earnCoins: 'รับเหรียญ',
      redeemRequiredXCoins: 'แลกรางวัลโดยใช้เหรียญ {X}',
      exchangeSuccess: 'แลกเปลี่ยนสำเร็จ',
      cardId: 'รหัสไอดี',
      exchangeFailed: 'การแลกล้มเหลว',
      transferFailedInsufficientStock: 'สินค้าหมดแล้ว กรุณามาใหม่พรุ่งนี้แต่เช้า.',
      gotItBtn: 'เข้าใจแล้ว',
      transferFailedRiskControl: 'บัญชีมีปัญหา, ไม่สามารถแลกได้ชั่วคราว.',
      tryAgainBtn: 'ลองอีกครั้งภายหลัง',
      transferConfirm: 'ยืนยันการโอน',
      paypalAccountEmail: 'อีเมลบัญชี Paypal',
      confirmPaypalAccountEmail: 'ยืนยันอีเมลบัญชี Paypal',
      transferX: 'โอน {X}',
      transferXConfirm: 'ยืนยันการโอน {X}',
      transferToYourAccountX: 'โอนเข้าบัญชีของคุณ {X}',
      confirmBtn: 'ยืนยัน',
      transferSuccessTitle: 'เรากำลังส่งเงินไปยังบัญชี Paypal ของคุณ!',
      transferSuccessContentX: 'เงินของคุณจะถึงภายใน 5 วันในบัญชีของ {X}.',
      copySuccess: 'คัดลอกสำเร็จ',
      enterEmailError: 'กรุณากรอกอีเมลที่ถูกต้อง',
      enterEmailValidError: 'ข้อมูลไม่ถูกต้อง กรุณากรอกใหม่',
      emailNotMatchError: 'ที่อยู่อีเมลไม่ตรงกัน กรุณากรอกใหม่',
    },
  },
  my_wallet: {
    title: 'กระเป๋าของฉัน',
    help_dialog: {
      title: 'กฎการใช้ตั๋ว',
      content1: `1. 1 ตั๋วที่ซื้อ = 1 ตั๋วรางวัลที่ซื้อ = 1 ตั๋วรางวัลโฆษณา = 1 ตั๋วแลกเหรียญ – ตั๋วเหล่านี้สามารถใช้ปลดล็อกตอนได้ทั้งหมด`,
      content2: `2. ตั๋วทั้งหมดปลดล็อกสิทธิ์ในการรับชมตอนที่ไม่มีวันหมดอายุ แต่ตั๋วรางวัลที่ซื้อจะหมดอายุหลังจากช่วงเวลาหนึ่ง`,
      content3: `3. ตั๋วที่ซื้อได้สามารถได้มาโดยการซื้อโดยตรง`,
      content4: `4. ตั๋วรางวัลที่ซื้อได้สามารถได้มาเฉพาะในช่วงโปรโมชั่นที่กำหนดผ่านการซื้อ`,
      content5: `5. ตั๋วรางวัลโฆษณาสามารถได้มาโดยการทำภารกิจดูโฆษณา`,
      content6: `6. ตั๋วแลกเหรียญสามารถได้มาโดยการแลกเหรียญ`,
    },
    account_balance: 'ยอดคงเหลือในบัญชี',
    purchased_tickets: 'ตั๋วที่ซื้อไว้',
    purchase_reward_tickets: 'ตั๋วรางวัลที่ซื้อไว้',
    coin_exchange_tickets: 'ตั๋วแลกเหรียญ',
    ad_reward_tickets: 'ตั๋วรางวัลโฆษณา',
    top_up: 'เติมเงิน',
    purchase_history: 'ประวัติการซื้อ',
    rewards_history: 'ประวัติการรับรางวัล',
    exchange_history: 'ประวัติการแลก',
    consumption_records: 'ประวัติการใช้จ่าย',
    auto_unlock: 'ตอนเกี่ยวกับการปลดล็อคอัตโนมัติ',
  },
  top_up: {
    exchange_title: 'แลกเหรียญ',
    purchase_title: 'ซื้อ',
    tickets: 'ตั๋ว',
    coins: 'เหรียญ',
    balance: 'ยอดคงเหลือ',
    title: 'ร้านค้า',
    tips: 'คำแนะนำ',
    tips_1: '1.แพลตฟอร์มของเรามีทั้งละครสั้นฟรีและละครแบบเข้าถึงก่อนพรีเมียม',
    tips_2: '2.เพื่อชมเนื้อหาแบบเข้าถึงก่อนล่วงหน้า, คุณต้องใช้ตั๋วในการแลก. ตั๋วเหล่านี้สามารถได้มาโดยการซื้อ, การแลกเหรียญ, หรือการดูโฆษณา',
    tips_3: '3.ตั๋วทั้งหมดปลดล็อกสิทธิ์ในการรับชมตอนที่ไม่มีวันหมดอายุ แต่ตั๋วรางวัลที่ซื้อจะหมดอายุหลังจากช่วงเวลาหนึ่ง',
    tips_4: '4.ตั๋วรางวัลที่ซื้อจะถูกใช้ก่อนเมื่อปลดล็อกตอน. หากยอดเงินไม่เพียงพอ, ตั๋วปกติของคุณจะถูกใช้อัตโนมัติ',
    tips_5: '5.หากการชำระเงินเติมเงินของคุณสำเร็จแต่ยอดเงินของคุณยังไม่ได้รับการอัปเดต, กรุณาลองรีเฟรชกระเป๋าเงินของคุณ',
    tips_6: '6.หากมีคำถามอื่นๆ กรุณาติดต่อเราผ่าน ‘โปรไฟล์’ -> ‘ศูนย์บริการลูกค้า’ -> ‘ข้อเสนอแนะ’”',
    exchange_dialog_title: 'คุณแน่ใจหรือว่าต้องการแลกเปลี่ยน?',
    redeem_required: 'ต้องแลก',
    exchange_success: 'เปลี่ยน xxx ตั๋วสำเร็จ',
    balance_not_enough: 'ยอดคงเหลือไม่เพียงพอ',
    exchange_failed: 'การแลกเปลี่ยนล้มเหลว',
  },
  my_wallet_in: {
    transaction_history: 'ประวัติการทำธุรกรรม',
    EPnum: 'ตอนที่{num}',
    Expired: 'หมดอายุ',
    AD: 'โฆษณา',
    Expires_on_num: 'หมดอายุวันที่ {num}',
    Thats_all_for_now: 'เท่านี้ก่อน',
    consumption_records: 'ประวัติการใช้จ่าย',
  },
  meal_check_in: {
    sleeping_link: 'รับเหรียญขณะนอนหลับ',
    rules_title: 'กฎกิจกรรม',
    benefits_title: 'Benefits',
    rules_1: '1. ทุกวันมีสี่ช่วงเวลาที่คุณสามารถเช็คอินมื้ออาหารของคุณได้: อาหารเช้า: 07:00-10:00; มื้อกลางวัน: 11:00-14:00; มื้อเย็น: 17:00-20:00; ขนมยามดึก: 21:00-24:00.',
    rules_2: '2. หากคุณพลาดเวลาเช็คอินที่กำหนดไว้ คุณสามารถดูวิดีโอเพื่อขอรับรางวัลได้.',
    rules_3: '3. รางวัลจะรีเซ็ตทุกวันเวลาเที่ยงคืน อย่าลืมขอรับรางวัลนะคะ.',
  },
  sleep_check_in: {
    earn_link: 'บันทึกมื้ออาหารเพื่อรับ',
    benefits_title: 'Benefits',
    rules_1: '1. ทุกคืนตั้งแต่เวลา 19:00 ถึง 24:00 สามารถเปิดโหมด \'นอนทำเงิน\' ได้',
    rules_2: '2. หลังจากเปิดโหมดแล้วในวันถัดไป เมื่อคุณนอนครบ 8 ชั่วโมงแล้ว คุณสามารถรับรางวัลจากการนอนระหว่างเวลา 08:00 ถึง 12:00',
    rules_3: '3. หากคุณพลาดเวลาในการรับรางวัล คุณสามารถดูวิดีโอระหว่างเวลา 12:00 ถึง 19:00 ของวันถัดไปเพื่อรับรางวัล',
  },
}
