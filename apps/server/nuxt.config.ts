import { join } from 'node:path'
const __dirname = import.meta.dirname || new URL('.', import.meta.url).pathname
// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  modules: [
    // '@nuxt/content',
    '@nuxt/eslint',
    '@unocss/nuxt',
    // '@nuxt/image',
    // '@nuxt/scripts',
    // '@nuxt/test-utils',
    // '@nuxt/ui'
    // '@artmizu/nuxt-prometheus'
  ],
  devtools: { enabled: true },
  app: {
    cdnURL: process.env.NUXT_CDN_BASE,
    head: {
      link: [
        {
          rel: 'apple-touch-icon',
          href: '/Logo.png',
        }
      ],
      script: [
        {
          innerHTML: `
            // 用于打点上报计算相对时间
            window.startReportTime = Date.now();
          `,
        }
      ]
    }
  },
  // css: ['~/assets/css/h5-global.css'],
  vue: {
    compilerOptions: {
      isCustomElement: isCustomElement,
    }
  },
  runtimeConfig: {
    public: {
      apiUrl: process.env.NUXT_PUBLIC_API_URL,
      cookieDomain: process.env.NUXT_PUBLIC_COOKIE_DOMAIN,
    }
  },
  routeRules: {
  },
  compatibilityDate: '2024-11-01',
  nitro: {
    serverAssets: [
      {
        baseName: 'templates',
        dir: join(__dirname, './templates'),
      }
    ]
  },
  vite: {
    vueJsx: {
      isCustomElement: isCustomElement,
    },
    build: {
      modulePreload: false,
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules')) {
              if (id.includes('shaka-player')) {
                return 'vendor_shaka-player'
              }
            }
          }
        }
      }
    }
  },
  typescript: {
    strict: false
  },
  hooks: {
    'build:manifest': manifest => {
      for (const key in manifest) {
        const file = manifest[key]
        file.preload = false
        file.prefetch = false
      }
    }
  },
  eslint: {
    config: {
      stylistic: true,
      typescript: true,
      formatters: true
    }
  },
  unocss: {
    nuxtLayers: true,
  },
})

function isCustomElement(tag: string) {
  return tag.startsWith('x-')
}
