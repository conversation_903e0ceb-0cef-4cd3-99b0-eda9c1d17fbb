import { createComponent, mc } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { transformNumber, Button, DateTime, SvgIcon } from '@skynet/ui'
import { set } from 'lodash-es'
import { useUserAssetManage } from './use-user-asset-manage'
type UserOptOptions = {
  props: {}
}
export const UserAssetManage = createComponent<UserOptOptions>({
  props: {},
}, props => {
  const {
    Form,
    FormOne,
    params,
    search,
    loading,
    userAssetForm,
    assetMsgList,
    selectedAssetMsg,
    onAssetTransfers,
  } = useUserAssetManage()
  const toggleCheckAll = (e: Event, item: { name: string, value: string }) => {
    const target = e.target as HTMLInputElement
    if (target.checked) {
      selectedAssetMsg.value.transfer_asset_type_list?.push(item.value)
    } else {
      selectedAssetMsg.value.transfer_asset_type_list = selectedAssetMsg.value.transfer_asset_type_list?.filter(i => i !== item.value)
    }
  }
  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>用户账号查询</li>
          </ul>
        ),
        form: () => (
          <Form
            submitText="查询"
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { is_single_user_query: 0 }
              userAssetForm.value = {}
            }}
            onSubmit={() => search()}
            data={params.value}
            items={[
              ['老账号ID', 'old_user_id', { type: 'number', placeholder: '用户ID' }, { transform: transformNumber }],
              ['新账号ID', 'new_user_id', { type: 'number', placeholder: '用户ID' }, { transform: transformNumber }],
            ]}
          />
        ),
        table: () => (
          <div>
            <div class={mc(userAssetForm.value.old_user_asset?.user_id && '!hidden', 'h-[400px] flex justify-center items-center')}>{loading.value ? <SvgIcon class="loading loading-spinner size-4" name="ic_loading" /> : '请输入用户ID查询后操作'}</div>
            <div class={mc('hidden', userAssetForm.value.old_user_asset?.user_id && 'block', 'pl-5')}>
              <h2 class="mb-2">用户信息</h2>
              <x-push-desc-list class="flex gap-10">
                <div class=" p-5 w-[500px] bg-gray-100 rounded-lg">
                  <h2 class="font-medium mb-2">老账号信息</h2>
                  <FormOne
                    class="grid grid-cols-4"
                    hasAction={false}
                    data={userAssetForm.value}
                    items={[
                      [['用户ID：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.old_user_asset?.user_id}</div>) } }, { class: 'flex flex-row col-span-2' }],
                        ['国家/地区代码：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.old_user_asset?.country_code}</div>) } }, { class: 'flex flex-row col-span-2' }]],
                      ['账号状态：', '', { type: 'custom', render: r => { return (<div>最后使用时间为 {userAssetForm.value.old_user_asset?.last_active_dt}</div>) } }, { class: 'flex flex-row  col-span-4' }],
                      ['端：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.old_user_asset?.platform == 1 ? 'ios' : userAssetForm.value.old_user_asset?.platform == 2 ? 'android' : '不知'}</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['订阅状态：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.old_user_asset?.subscription_status || '--'} (到期时间: <DateTime value={userAssetForm.value.old_user_asset?.subscription_expire_time ? userAssetForm.value.old_user_asset?.subscription_expire_time * 1000 : '--'} />)</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['金币：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.old_user_asset?.coins} coins</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['奖励币：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.old_user_asset?.bonus} bonus + {userAssetForm.value.old_user_asset?.rewards} rewards</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['兑换劵：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.old_user_asset?.coupon}</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['已解锁短剧集数：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.old_user_asset?.unlocked_episode}</div>) } }, { class: 'flex flex-row col-span-4' }],
                    ]}
                  />
                </div>
                <div class=" p-5 w-[500px] bg-gray-100 rounded-lg">
                  <h2 class="font-medium mb-2">新账号信息</h2>
                  <FormOne
                    class="grid grid-cols-4"
                    hasAction={false}
                    data={userAssetForm.value}
                    items={[
                      [['用户ID：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.new_user_asset?.user_id}</div>) } }, { class: 'flex flex-row col-span-2' }],
                        ['国家/地区代码：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.new_user_asset?.country_code}</div>) } }, { class: 'flex flex-row col-span-2' }]],
                      ['账号状态：', '', { type: 'custom', render: r => { return (<div>最后使用时间为 {userAssetForm.value.new_user_asset?.last_active_dt}</div>) } }, { class: 'flex flex-row  col-span-4' }],
                      ['端：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.new_user_asset?.platform == 1 ? 'ios' : userAssetForm.value.new_user_asset?.platform == 2 ? 'android' : '不知'}</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['订阅状态：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.new_user_asset?.subscription_status || '--'} (到期时间: <DateTime value={userAssetForm.value.new_user_asset?.subscription_expire_time ? userAssetForm.value.new_user_asset?.subscription_expire_time * 1000 : '--'} />)</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['金币：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.new_user_asset?.coins} coins</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['奖励币：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.new_user_asset?.bonus} bonus + {userAssetForm.value.new_user_asset?.rewards} rewards</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['兑换劵：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.new_user_asset?.coupon}</div>) } }, { class: 'flex flex-row col-span-4' }],
                      ['已解锁短剧集数：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.new_user_asset?.unlocked_episode}</div>) } }, { class: 'flex flex-row col-span-4' }],
                    ]}
                  />
                </div>
              </x-push-desc-list>
              <x-push-desc-list>
                <h2 class="mt-6">资产转移</h2>
                <div class="block text-gray-400 text-xs my-1">从老账号转移至新账号，请勾选要转移的资产，资产转移不会转移历史记录</div>
                <div class="relative p-5 w-[1040px] min-h-[240px] bg-gray-100 rounded-lg">
                  <div class="gap-3 mt-3 flex">
                    {
                      (assetMsgList.value).map(item => (
                        <label class="cursor-pointer m-2 flex items-center">
                          <input type="checkbox"
                            value={item.name}
                            checked={selectedAssetMsg.value.transfer_asset_type_list?.includes(item.value)}
                            onChange={(e: Event) => toggleCheckAll(e, item)}
                            class="tm-radio mr-1 cursor-pointer checkbox checkbox-primary checkbox-sm" />
                          {item.name}
                        </label>
                      ),
                      )
                    }
                  </div>
                  <div class="absolute bottom-4 right-4">
                    <div class="w-[110px] text-gray-400 text-xs text-center mb-3">资产转移会移除老账号的相应资产</div>
                    <Button class="btn btn-md btn-error text-white" onClick={onAssetTransfers}>执行资产转移</Button>
                  </div>
                </div>
              </x-push-desc-list>
            </div>
          </div>
        ),
      }}
    </NavFormTablePager>
  )
})
