* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

*::before {
  box-sizing: border-box;
}

*::after {
  box-sizing: border-box;
}

ol,
ul {
  list-style: none;
}

button,
input {
  font-size: inherit;
  font-family: inherit;
  color: inherit;
}

img {
  max-width: 100%;
}

body {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
}

  body {
    background-color: #0b0b0b;
    color: #fff;
  }

  @media (min-width: 1000px) {
    #app {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    #app>article {
      flex: 1;
      display: flex;
    }
  }


  x-wrapper {
    flex: 1;
    display: block;
    background-size: cover;
    background-position: center;
    position: relative;
    padding-top: 28px;
    display: block;
    padding-bottom: 28px;
    overflow: hidden;
  }

  @media (min-width: 1000px) {
    x-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  main {
    display: block;
    margin-left: 12px;
    margin-right: 12px;
  }

  @media (min-width: 1000px) {
    main {
      display: flex;
      max-width: 1000px;
      column-gap: 50px;
    }
  }

  x-cover {
    display: flex;
    justify-content: center;
    align-items: center;
    /* 宽高比 3:4 */
    aspect-ratio: 3 / 4;
    margin: 0 28px;
    position: relative;
    overflow: hidden;
  }



  @media (min-width: 1000px) {
    x-cover {
      width: 400px;
      flex-shrink: 0;
      margin: 0;
    }
  }

  x-cover>.cover {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.20);
    background-color: lightgray;
  }

  x-cover>.play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  x-summary {
    display: block;
  }

  @media (min-width: 1000px) {
    x-summary {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      justify-content: center;
      align-items: center;
    }
  }

  x-summary x-desc {
    overflow: hidden;
    line-height: 1.4em;
    color: #AAA;
    font-size: 14px;
  }

  x-summary x-desc.folded {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
    text-overflow: ellipsis;
    max-height: 7em;
  }

  x-summary x-desc.unfolded {
    max-height: none;
    display: block;
  }

  x-summary x-more,
  x-summary x-pick-up {
    display: block;
    text-align: right;
    font-size: 12px;
  }

  x-summary x-pick-up {
    display: none;
  }

  x-summary h1 {
    margin-top: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-Icon-text-1, #FDFBFC);
    font-size: 20px;
    font-weight: 510;
  }

  @media (min-width: 1000px) {
    x-summary h1 {
      font-size: 24px;
      font-size: 38px;

      white-space: normal;
    }
  }

  x-summary ul {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0 auto;
  }

  x-summary ul li {
    line-height: 1.25rem;
    border-radius: 0.675rem;
    background: #313131;
    padding: 0 1rem;
    font-size: 12px;
  }

  x-summary button {
    background-color: blue;
    color: white;
    min-height: 48px;
    min-width: 10em;
    border: none;
    border-radius: 24px;
    /* 渐变背景，从左往右，FE8C0E 到 F700D8 */
    background: linear-gradient(to right, #FE8C0E, #F700D8);
    margin-top: 32px;
    font-size: 20px;
    font-weight: 700;
    position: fixed;
    bottom: 80px;
    left: 12px;
    right: 12px;
  }

  @media (min-width: 1000px) {
    x-summary button {
      position: relative;
      bottom: 0;
      left: 0;
      right: 0;
      width: 100%;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.2);
    }

    100% {
      transform: scale(1);
    }
  }

  x-summary button img {
    position: absolute;
    top: 15px;
    right: 18px;
    color: white;
    animation: pulse 1s infinite;
  }



  x-mask {
    display: block;
    position: absolute;
    inset: 0;
    height: 100%;
    z-index: 2;
    background: linear-gradient(to bottom, #0b0b0b1a, #0b0b0bff);
  }

  @media (min-width: 1000px) {
    x-mask {
      background: linear-gradient(to bottom, #0b0b0b6a, #0b0b0bff);
    }
  }

  x-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1;
    overflow: hidden;
  }

  x-bg img {
    /* 模糊效果 */
    filter: blur(10px);
    margin: -10px;
    display: block;
    width: calc(100% + 20px);
    max-width: none;
  }

  x-top-nav {
    position: sticky;
    top: 0;
    z-index: 10;
    color: #fff;
    background-color: #0b0b0b;
    display: grid;
    grid-template-areas:
      'logo title button'
      'logo subtitle button'
    ;
    grid-template-columns: max-content 1fr max-content;
    grid-template-rows: 1fr max-content;
    justify-content: left;
    align-items: center;
    justify-items: start;
    padding: 12px 18px;
    row-gap: 0px;
    column-gap: 8px;
  }

  @media (min-width: 1000px) {
    x-top-nav {
      position: fixed;
      top: 10px;
      left: 0;
      right: 0;
      z-index: 10;
      max-width: 1000px;
      margin: 0 auto;
      padding-left: 0;
      padding-right: 0;
      background-color: transparent;
    }
  }

  x-top-nav .logo {
    grid-area: logo;
  }

  x-top-nav .title {
    grid-area: title;
  }

  @media (min-width: 1000px) {
    x-top-nav .title {
      width: 164px;
      height: 21px;
    }
  }

  x-top-nav .subtitle {
    grid-area: subtitle;
    color: #666;
    font-size: 12px;
  }

  @media (min-width: 1000px) {
    x-top-nav .subtitle {
      display: none;
    }
  }

  x-top-nav button {
    grid-area: button;
    display: flex;
    height: 38px;
    padding: 0px var(--number-16, 18px);
    justify-content: center;
    align-items: center;
    gap: 10px;
    border: none;
    border-radius: 19px;
    background: var(--brand-brand-6, #FC2763);
  }

  @media (min-width: 1000px) {
    x-top-nav button {
      display: none;
    }
  }
