import { createComponent } from '@skynet/shared'
import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ActivityBlock } from './activity-block'
import { promoteImages } from './promote-images/promote-images.ts'
import { useActivityStore } from './use-activity-store'
import { track } from 'src/lib/track'
import { BackButton } from '../common/button/back-button'
type PromotePageOptions = {
  props: {}
}

const imageToButtonMap: Record<number, number | null> = {
  0: null,
  1: 0,
  2: null,
}

export const PromotePage = createComponent<PromotePageOptions>({
  props: {},
}, () => {
  const { config, fetchConfig } = useActivityStore()
  const route = useRoute()
  const language = computed(() => ['en', '', undefined].includes(route.query.language as string) ? 'en' : null)

  onMounted(() => {
    track('activity_H5', 'page', 'show', {
      activity_id: route.query.event_id,
      user_id: route.query.user_id,
      from: route.query.from,
    })
  })

  watch(() => route.query.event_id as string, _eventId => {
    const eventId = Number(_eventId)
    if (eventId) {
      void fetchConfig(eventId)
    }
  }, { immediate: true })
  // 暂时只支持英语
  const images = computed(() => language.value === 'en' ? promoteImages.en : promoteImages.en)

  return () => (
    <x-activity-page class="block relative bg-[#0c0a0d]">
      <BackButton class="absolute left-0 z-up top-[max(env(safe-area-inset-top),_44px)]" />
      <x-block-list class="flex flex-col gap-0">
        {images.value.map((i, index) => (
          <ActivityBlock key={index} type="image" src={i} button={config.value[imageToButtonMap[index] ?? -1]} />
        ))}
      </x-block-list>
    </x-activity-page>
  )
})

export default PromotePage
