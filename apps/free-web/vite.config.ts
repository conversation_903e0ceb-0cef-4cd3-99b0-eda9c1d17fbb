import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { dirname, join } from 'node:path'
import { fileURLToPath } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
// @ts-expect-error ignore js
import { createSvgIconsPlugin } from '@skynet/vite-plugin-svg-icons'
import { plugin as mdPlugin, Mode } from 'vite-plugin-markdown'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const projectDir = __dirname
const project = (path: string) => join(projectDir, path)
const envDir = project('env')

const isCustomElement = (tag: string) => tag.startsWith('x-')
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, envDir)
  console.log('env: ', env)
  return {
    base: env.VITE_CONFIG_BASE,
    envDir, // 多页面
    build: {
      rollupOptions: {
        input: {
          main: project('index.html'),
          'top-up': project('top-up.html'),
        },
        output: {
          manualChunks: (id: string) => {
            if (['@vueuse/core', '@vueuse/integrations', 'axios', 'crypto-js', 'dayjs', 'github-markdown-css', 'js-cookie',
              'js-md5', 'lottie-web', 'vue', 'vue-i18n', 'vue-router'].some(i => id.indexOf(i) >= 0)) {
              return 'vue-deps'
            }
            if (id.indexOf('rules-map') >= 0) {
              return 'rules-map'
            }
          },
          // chunkFileNames: chunkInfo => {
          //   if (chunkInfo.name === 'vue-deps') {
          //     return `assets/[name]-${env.VITE_CONFIG_MODE}-new.js`
          //   } else {
          //     return 'assets/[name].[hash].js'
          //   }
          // },
        },
      },
    },
    plugins: [
      vue(),
      vueJsx({
        isCustomElement,
        defineComponentName: ['defineComponent', 'createComponent'],
      }),
      createSvgIconsPlugin({
        iconDirs: [project('src/icons/svg')],
        symbolId: '[name]',
      }),
      createSvgIconsPlugin({
        registerName: 'virtual:svg-no-colored-icons-register',
        iconDirs: [project('src/icons/no-colored-svg')],
        symbolId: 'n_[name]',
        svgoOptions: {
          plugins: [
            {
              name: 'removeAttrs',
              params: {
                attrs: ['fill'],
                preserveCurrentColor: true,
              },
            },
          ],
        },
        customDomId: '__svg__no__colored__icons__dom__',
      }),
      mdPlugin({ mode: [Mode.HTML, Mode.VUE] }),
    ],
    resolve: {
      alias: {
        src: project('src'),
      },
    },
  }
})
