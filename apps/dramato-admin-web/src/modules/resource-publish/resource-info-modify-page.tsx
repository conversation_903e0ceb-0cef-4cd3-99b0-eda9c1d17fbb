/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Pager, Button, transformTimestamp, transformNumber } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { apiGetInfoModifyList } from './resource-info-modify-api'
import { renderTitle, renderStatusDesc } from './util'
import { useResourceStore } from './use-resource-publish-store'
import { taskStatusOptions, auditStatusForQuery, releaseRounds } from './constant'

type InfoModifyPageOptions = {
  props: {}
}
export const InfoModifyPage = createComponent<InfoModifyPageOptions>({
  props: {},
}, props => {
  const {
    onUploadResourceInfo,
    getTaskRole,
    resourceRole,
    all_members,
    onDistributeTask,
    onGetTask,
  } = useResourceStore()
  const resourceType = 2
  const loading = ref(false)
  const list = ref<M.InfoModify.Resource[]>([])
  const defaultParams: M.IResourcePublishQueryParams = {
    title: '',
    id: undefined,
    partner_name: '',
    create_start_time: undefined, // 录入开始时间
    create_end_time: undefined, // 录入结束时间
    series_key_or_title_list: '',
    resource_id_or_title_list: '',
    claim_names: [],
    audit_status_list: [],
    task_status: 0
  }
  const form = ref<M.IResourcePublishQueryParams>(defaultParams)
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const Table = CreateTableOld<M.InfoModify.Resource>()
  const QueryForm = CreateForm<M.IResourcePublishQueryParams>()

  const getList = async () => {
    const params = {
      ...form.value,
      ...pageInfo.value,
    }
    loading.value = true
    list.value = []
    try {
      const res = await apiGetInfoModifyList(params)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const columns: TableColumnOld<M.InfoModify.Resource>[] = [
    ['资源ID', 'id', { class: 'w-[80px]' }],
    ['资源名称', row => renderTitle(row), { class: 'w-[190px]' }],
    ['认领人', 'claim_name', { class: 'w-[120px]'} ],
    ['合作方', 'partner_name', { class: 'w-[120px]' }],
    ['审核状态', row => {
      return renderStatusDesc(row)
    }, { class: 'w-[110px] text-center' }],
    ['标题进度', row => {
      return (
        <div class="text-center">
          <span>{row.title_progress.filter(t => t.status === 1).length}/{row.title_progress.length}</span>
        </div>
      )
    }, {
      class: 'text-center',
    }],
    ['简介进度', row => {
      return (
        <div class="text-center">
          <span>{row.desc_progress.filter(t => t.status === 1).length}/{row.desc_progress.length}</span>
        </div>
      )
    }, {
      class: 'text-center',
    }],
    ['封面进度', row => {
      return (
        <div class="text-center">
          <span>{row.cover_progress.filter(t => t.status === 1).length}/{row.cover_progress.length}</span>
        </div>
      )
    }, {
      class: 'text-center',
    }],
    ['操作', (row, index) => (
      <div class="flex justify-center gap-x-2">
        <Button
          class="btn btn-link btn-xs"
          onClick={() => {
            window.open(`${location.origin}/resource-publish/info-verify/information/${row.id}`)
          }}
        >
          多语言信息校验
        </Button>
        {
          resourceRole.value === 1
            ? (
                <Button
                  class="btn btn-link btn-xs"
                  onClick={() => {
                    void onDistributeTask({
                      type: resourceType,
                      resource_ids: [row.id],
                    }, getList)
                  }}
                >
                  分配任务
                </Button>
              )
            : null
        }
      </div>
    ), {
      class: 'w-[180px] text-center',
    },
    ],
  ]

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = { ...defaultParams, audit_status_list: [] }
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }

  onQuery()
  void getTaskRole({
    type: resourceType,
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>多语言信息校验</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              ['资源名称/ID：', 'resource_id_or_title_list', { type: 'text', placeholder: '请输入资源名称或者id' }],
              ['剧集名称/ID：', 'series_key_or_title_list', { type: 'text', placeholder: '请输入资源名称' }],
              ['合作方：', 'partner_name', { type: 'text', placeholder: '请输入合作方' }],
              ['认领人：', 'claim_names', { type: 'multi-select', search: true, options: all_members.value.map(m => {
                return {
                  value: m,
                  label: m,
                }
              }) }],
              ['认领状态：', 'task_status', { type: 'select', options: taskStatusOptions, autoInsertEmptyOption: false }, { transform: transformNumber }],
              ['审核状态', 'audit_status_list', { type: 'multi-select', maxlength: 1, options: auditStatusForQuery }],
              [
                ['录入时间-开始：', 'create_start_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
                ['录入时间-结束：', 'create_end_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
              ],
              ['发行轮次：', 'release_round', { type: 'select', placeholder: '请选择发行轮次', options: releaseRounds }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex justify-between items-center">
            <span>多语言信息列表</span>
            <div class="space-x-2">
              { resourceRole.value === 2
                ? (
                    <button class="btn btn-primary btn-sm" onClick={() => {
                      void onGetTask(resourceType, onQuery)
                    }}
                    >领取任务
                    </button>
                  )
                : null}
              <Button class="btn btn-primary btn-sm" onClick={() => onUploadResourceInfo(getList)}>导入资源信息</Button>
            </div>
          </x-table-actions>
        ),
        table: () => (
          <Table
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default InfoModifyPage
