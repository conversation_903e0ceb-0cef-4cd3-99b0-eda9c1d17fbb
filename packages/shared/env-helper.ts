export const openedInDev = import.meta.env.MODE === 'development'
export const openedInProd = import.meta.env.MODE === 'production'
export const openedInStaging = import.meta.env.MODE === 'staging'

export const getPlatform = () => {
  const platform = navigator.platform?.toLowerCase?.()
  if (platform.indexOf('win32') >= 0) return 'windows'
  if (platform.indexOf('mac') >= 0) return 'mac'
  if (platform.indexOf('linux') >= 0) return 'linux'
  return 'unknown'
}

declare global {
  interface ImportMetaEnv {
    MODE: 'development' | 'production' | 'staging' | string
  }
}

export const breakpoints = {
  phone: { min: 0, max: 719 },
  pad: { min: 720, max: 1023 },
  pc: { min: 1024, max: Infinity },
}
