import { ref } from 'vue'
import { getHotSearch, searchDramato, searchDramatoBackup } from './search-api'
import { showToast } from 'src/modules/common/toast/toast'
import { T } from './search.i18n'

const query = ref<string>('')
const keywordsList = ref<M.SearchKeywords[]>([])
const dramatoList = ref<M.Dramato[]>([])
const isSearching = ref(false)
const isSearchingDramato = ref(false)
const showKeywordsList = ref(false)
const showDramatoList = ref(false)
const hasMore = ref(false)
const next = ref('')
const searchedKeywords = ref<string[]>(JSON.parse(localStorage.getItem('drama.searchKeywords') ?? '[]'))

export const useSearch = () => {
  const setSearchKeywords = () => {
    const idx = searchedKeywords.value.findIndex(keyword => keyword === query.value)
    if (idx === -1) {
      searchedKeywords.value.push(query.value)
    } else {
      searchedKeywords.value.splice(idx, 1)
      searchedKeywords.value.unshift(query.value)
    }
    localStorage.setItem('drama.searchKeywords', JSON.stringify(searchedKeywords.value))
  }

  const clearSearchKeywords = () => {
    searchedKeywords.value = []
    localStorage.removeItem('drama.searchKeywords')
    showToast(T.search.deleteAllHistoryTipSuccess())
  }

  const withHighlight = (text?: string) => {
    return text?.replaceAll('{{', '<span class="text-[#fc2763]">').replaceAll('}}', '</span>')
  }

  const search = (_next?: string) => {
    if (query.value === '') return
    showKeywordsList.value = false
    showDramatoList.value = true
    isSearchingDramato.value = true

    setSearchKeywords()
    void searchDramato({
      scence: 'series_tag',
      next: _next ?? '',
      keyword: query.value,
      timestamp: Date.now().toString(),
    }).then(res => {
      if (res.data) {
        if (res.data.items?.length) {
        // new Set 只能做简单类型去重，所以需要手动去重
          dramatoList.value = [...res.data.items?.filter(item => !dramatoList.value.some(newItem => newItem.id === item.id)), ...dramatoList.value]
        }
        next.value = res.data.page_info.next
        hasMore.value = res.data.page_info.has_more
      }
      if (dramatoList.value.length) {
        isSearchingDramato.value = false
        return
      }
      void searchDramatoBackup({
        next: '',
      }).then(res => {
        if (res.data?.items?.length) {
          // new Set 只能做简单类型去重，所以需要手动去重
          dramatoList.value = [...res.data.items?.filter(item => !dramatoList.value.some(newItem => newItem.id === item.id)), ...dramatoList.value]
          hasMore.value = res.data.page_info.has_more
          next.value = res.data.page_info.next
        }
      }).finally(() => {
        isSearchingDramato.value = false
      })
    }).catch(e => {
      console.log(e)
      isSearchingDramato.value = false
    })
  }

  const chooseKeyword = (keyword: string) => {
    query.value = keyword
    search()
  }

  const hotDramatoList = ref<M.Dramato[]>([])

  const getHotDramatoList = () => {
    void getHotSearch().then(res => {
      hotDramatoList.value = res.data?.items || []
    })
  }

  return {
    query,
    keywordsList,
    dramatoList,
    isSearching,
    isSearchingDramato,
    showKeywordsList,
    showDramatoList,
    hotDramatoList,
    chooseKeyword,
    search,
    searchedKeywords,
    clearSearchKeywords,
    withHighlight,
    getHotDramatoList,
    hasMore,
    next,
  }
}
