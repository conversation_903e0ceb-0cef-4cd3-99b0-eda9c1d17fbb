import { createComponent, fn } from '@skynet/shared'
import { Button } from '../ui'
import { Fn } from '@vueuse/core'
type DialogFooterOptions = {
  props: {
    cancelText?: string
    okText?: string
  }
  emits: {
    cancel: Fn
    ok: Fn
  }
}
export const DialogFooter = createComponent<DialogFooterOptions>({
  props: {
    cancelText: '',
    okText: '',
  },
  emits: {
    cancel: fn,
    ok: fn,
  },
}, (props, { emit }) => {
  return () => (
    <div class="btn-group flex w-full items-center justify-end gap-3 absolute bottom-0 pb-4 right-0 px-5">
      <Button class="h-8 px-4 rounded-md border border-primary border-solid text-base font-medium text-primary bg-transparent leading-8" onClick={() => emit('cancel')}>
        {props.cancelText || '取消'}
      </Button>
      <Button class="h-8 px-4 rounded-md border border-primary border-solid text-base font-medium text-white bg-primary leading-8" onClick={() => emit('ok')}>
        {props.okText || '确定'}
      </Button>
    </div>
  )
})
