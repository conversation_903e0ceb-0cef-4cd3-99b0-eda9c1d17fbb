import { createComponent, mc } from '@skynet/shared'
import DefaultCover from 'src/h5_modules/images/cover-def.webp'
import icHistory from 'src/h5_modules/images/ic-history.webp'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { T } from '../my-list-i18n'
import { openLimitDialog } from 'src/h5_modules/watch-history/component/drama-card'

type DramaCardOptions = {
  props: {
    drama: M.Drama
    is_last_history?: boolean
    name: string
  }
}
export const DramaCard = createComponent<DramaCardOptions>({
  props: {
    drama: {},
    is_last_history: false,
    name: '',
  },
}, props => {
  const cover = ref<string>(props.drama.cover || '')
  const loadingCover = ref<boolean>(false)
  const router = useRouter()

  const onSeeMoreHistory = () => {
    // void router.push('/watch-history')
    // void router.push({ path: '/watch-history', query: { return_to: 'my-list' } })
    window.location.href = window.location.origin + '/watch-history?return_to=my-list'
  }

  const gotoPaly = () => {
    // if (props.drama.release_round === 1) {
    //   openLimitDialog(props.drama, 'my_list')
    // } else {
    // ?from_page=library&return_to=/my-list
    window.location.href = window.location.origin + '/series/' + props.drama.id + '/' + props.drama?.episode?.id + '?from_page=' + 'library&return_to=/my-list'
    // void router.push('/series/' + props.drama.id + '/' + props.drama?.episode?.id + '?from_page=' + props.name)
    // }
  }

  const playDrama = () => {
    if (props.is_last_history) {
      return
    }

    // go to play page
    gotoPaly()
  }

  return () => (
    <x-drama-card class="block h-auto overflow-hidden rounded-lg" onClick={playDrama}>
      <div class="min-h-37.25 h-37.25 relative block w-full overflow-hidden rounded-lg">
        <img
          class={mc('size-full object-cover rounded-lg', loadingCover.value ? 'opacity-0' : '')}
          src={props.drama.cover || ''}
          onLoad={() => loadingCover.value = false}
          onError={() => cover.value = DefaultCover}
        />
        {
          props.is_last_history && (
            <x-view-more onClick={onSeeMoreHistory} class="absolute left-0 top-0 flex size-full flex-col items-center justify-center gap-y-2 bg-[rgba(11,8,11,0.6)]">
              <x-img class="flex size-11 items-center justify-center rounded-full bg-[rgba(253,251,252,0.20)]">
                <img src={icHistory} class="size-8" />
              </x-img>
              <div class="text-3.5 text-center text-[#FDFBFC]">{T.myList.seeAll()}</div>
            </x-view-more>
          )
        }
      </div>
      {
        !props.is_last_history && (
          <x-drama-desc-wrap class="flex flex-col items-start justify-start gap-y-1 p-2 pl-0">
            <x-drama-card-title class="max-h-10.25 text-3.5 block w-full truncate break-words leading-4 text-[#fdfbfc]">{props.drama.name}</x-drama-card-title>
            <x-drama-card-title class="max-h-10.25 text-3 leading-3.5 block w-full truncate break-words text-[#797b7d]">EP.{props.drama.view_episode || 1} / EP.{props.drama.episode_count || 0}</x-drama-card-title>
          </x-drama-desc-wrap>
        )
      }
    </x-drama-card>
  )
})

export default DramaCard
