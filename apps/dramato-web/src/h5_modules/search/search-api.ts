import { encryptedHttpClient } from 'src/lib/encrypted-http-client'

export const searchKeywords = (keyword: string) => {
  return encryptedHttpClient.post<ApiResponse<{
    keywords: M.SearchKeywords[]
    page_info: PageInfo
  }>>('/h5-api/search/keywords', {
    keyword,
  })
}

export const searchDramato = (data: {
  scence: string
  next: string
  keyword: string
  timestamp: string
}) => {
  return encryptedHttpClient.post<ApiResponse<{
    items: M.Dramato[]
    page_info: PageInfo
  }>>('/h5-api/search/drama', data)
}

export const searchDramatoBackup = (data: {
  next: string
}) => {
  return encryptedHttpClient.post<ApiResponse<{
    items: M.Dramato[]
    page_info: PageInfo
  }>>('/h5-api/search/security', data)
}

export const getHotSearch = () => {
  return encryptedHttpClient.post<ApiResponse<{
    items: <PERSON><PERSON>[]
  }>>('/h5-api/search/hot-list')
}
