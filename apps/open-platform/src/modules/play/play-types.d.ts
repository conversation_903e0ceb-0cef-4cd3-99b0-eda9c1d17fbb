declare namespace M {
  interface Play {
    play_key: string
    create_user_id?: string
    title: string
    description: string
    count?: number
    cover: string
    label_ids: number[]
    play_path: string
    /**
     * 进度
     * * 0 剧本准备(草稿)
     * * 1 剧本初审
     * * 2 初审未通过
     * * 3 初审放弃
     * * 4 剧本签约
     * * 5 剧本二审
     * * 6 二审未通过
     * * 7 二审放弃
     * * 8 二次付费
     * * 9 剧本终审
     * * 10 终审未通过
     * * 11 终审放弃
     * * 12 完本
     */
    audit_status?: number
    audit_failed_reason: string
    audit_failed_reason_en: string
    created?: number
    updated?: number
  }
}
