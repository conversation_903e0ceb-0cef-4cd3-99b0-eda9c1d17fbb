import { httpClient } from 'src/lib/http-client'


interface cardItem {

}

export type activityInfo = {
  id: string
  name: string
  desc: string
  cover: string
  episode_count: number
  view_count: number
  follow_count: number
  finish_status: number
  view_episode: number
  following: boolean
  episode_price: number

  // activity_cards: 
}

export const apiActivityInfo = (id: string, language?: string) => {
  return httpClient.get<ApiResponse<{ activity_info: activityInfo }>>('dm-api/h5/activity/detail?id=' + id + (language ? '&language=' + language : ''))
}
