import { createComponent, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { coins, copy as copyIcon, dialogBg, spotifyCard } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { useClipboard } from '@vueuse/core'
import { showToast } from 'src/modules/common/toast/toast'
import { ref, watch } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { openDialog } from 'src/modules/common/dialog'
import { useRedeem } from '../use-redeem'
type GiftOptions = {
  props: {
    gift?: GiftType
  }
}
export const SpotifyGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      type: 'spotify',
    },
  },
}, props => {
  const { copy, copied } = useClipboard()
  const { t } = useLocale()
  const tipsRef = ref<HTMLDivElement>()

  watch(() => copied.value, () => {
    if (copied.value) {
      showToast('复制成功')
    }
  })
  const { commonTitle, reachLimit } = useRedeem(props.gift)

  const openExchangeSuccessDialog = () => {
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex min-h-[260px] flex-col bg-no-repeat pt-16 shadow">
          <x-body-main class="mx-4 flex flex-col gap-y-4">
            <x-title class="truncate text-center text-lg font-bold text-[#090609]">Exchange Succesffully !</x-title>
            <x-explain class="flex flex-col items-center text-center">
              <x-price class="mx-[6px] block max-w-[calc(100%-12px)] truncate text-[32px] font-bold text-[#fc2763]">Card ID:</x-price>
              <x-description class="flex w-full items-center justify-center text-wrap text-sm font-medium text-[#4d4e4f]">
                <span>1274-2948-DFHS-SJAD</span>
                <img src={copyIcon} class="no-tap-color ml-1 size-4 cursor-pointer" onClick={() => copy('1274-2948-DFHS-SJAD')} />
              </x-description>
            </x-explain>
            <Button class="no-tap-color mx-7 mb-6 h-11 w-[calc(100%-2.5rem)] rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={() => closeDialog()}>{t('coins.exchange.receiveBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const handleClick = () => {
    if (props.gift?.can_exchange) {
      if (reachLimit.value) {
        openExchangeDialog()
      } else {
        track('exchangezone', 'earncoins_button', 'click')
        window.location.href = 'dramawave://dramawave.app/rewards_task?enter_from=exchange_page'
      }
    }
  }

  const flag = ref(false)
  watch(() => [tipsRef.value, flag.value], () => {
    flag.value = false
    if (tipsRef.value) {
      const lineHeight = parseFloat(getComputedStyle(tipsRef.value).lineHeight)
      const fontSize = parseFloat(getComputedStyle(tipsRef.value).fontSize)
      if (fontSize <= 8) {
        return
      }
      const maxHeight = lineHeight * 2
      if (tipsRef.value.scrollHeight > maxHeight) {
        tipsRef.value.style.fontSize = `${fontSize - 2 <= 8 ? 8 : fontSize - 2}px`
        flag.value = true
      }
    }
  })

  const openExchangeDialog = () => {
    openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex min-h-[260px] flex-col bg-no-repeat pt-16 shadow">
          <x-body-main class="mx-4 flex flex-col gap-y-4">
            <x-title class="truncate text-center text-lg font-bold text-[#090609]">Amazon Amazon Amazon Amazon Amazon Amazon</x-title>
            <x-explain class="flex flex-col items-center text-center">
              <x-price class="mx-[6px] block max-w-[calc(100%-12px)] truncate text-[32px] font-bold text-[#fc2763]">$9999999999999999999999</x-price>
              <x-description class="line-clamp-2 w-full  text-wrap text-sm font-medium text-[#4d4e4f]">Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard</x-description>
            </x-explain>
            <x-content class="line-clamp-2 w-full text-center text-[#090609]">
              Redeem requierd 30000 coins Redeem requierd 30000 coinsrequierd requierd
            </x-content>
            <Button class="no-tap-color mx-7 mb-6 h-11 w-[calc(100%-2.5rem)] rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={openExchangeSuccessDialog}>{t('coins.earnings.exchange')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  return () => (
    <x-gift-item class="flex gap-x-3 p-3">
      <x-gift-item-left class={mc('flex max-w-[44px] flex-col gap-1', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-card class="box-border size-11 rounded-lg border border-solid border-[#ece9ec] px-1 py-3">
          <img src={props?.gift?.image_url ?? spotifyCard} class="h-auto w-full object-cover" />
        </x-card>
        <x-price class="block w-full break-all text-center text-xs font-bold text-[#4d4e4f]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class={mc('flex flex-1 flex-col gap-1 truncate', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-gift-title class="w-full truncate text-sm text-[#4d4e4f]">{props.gift.name}</x-gift-title>
        {
          props.gift?.can_exchange && (
            <x-gift-coins class="flex items-center text-sm">
              <img src={coins} width={16} height={16} class="mr-[2px] size-4 object-contain" />
              <x-gift-amount class="text-[#fc2763]">{props.gift.user_coins}</x-gift-amount>
              <x-separator class="px-[2px] text-[#a1a2a3]">/</x-separator>
              <x-gift-remains class="text-[#090609]">{props.gift.gift_coins}</x-gift-remains>
            </x-gift-coins>
          )
        }
        {
          props.gift?.can_exchange && (
            <x-gift-progress>
              <Progress class="mt-1 bg-[#ECE9EC]" value={props.gift.ratio} max={1} />
            </x-gift-progress>
          )
        }
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21 justify-center', props.gift?.button_tips ? 'gap-y-1' : '')}>
        <Button class={mc('min-h-8 w-full rounded-lg border-none px-1 pt-[2px] pb-[3px]  text-sm', !props.gift?.can_exchange ? 'bg-[#FFA0B6] !text-[#FDFBFC80]' : 'bg-[#fc2763] !text-white')} onClick={handleClick}>
          <p class="line-clamp-2 h-auto w-full text-wrap break-words text-center leading-4">
            {reachLimit.value ? t('coins.earnings.exchange') : t('coins.exchange.earnCoins')}
          </p>
        </Button>
        {props.gift?.button_tips && <x-tips class={mc('line-clamp-2 h-auto w-full break-words text-center text-[10px] leading-3 text-[#4d4e4f]', props.gift?.can_exchange ? '' : 'opacity-30')} ref={tipsRef}>{props.gift?.button_tips}</x-tips>}
      </x-gift-item-right>
    </x-gift-item>
  )
})
