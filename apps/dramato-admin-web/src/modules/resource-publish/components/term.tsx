/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, Icon, openDialog, showFailToast, showSuccessToast, SvgIcon } from '@skynet/ui'
import { ElTable, ElTableColumn } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { ref, watch } from 'vue'
import { TermEditDialog } from './term-edit-dialog'
import { apiUpdateOriginalTerminology } from 'src/modules/resource-publish/resource-publish-api'
import { useRoute } from 'vue-router'
type TermOptions = {
  props: {
    list: M.ITerminology[]
    loading: boolean
    tableStyle?: { [Record: string]: string }
  }
  emits: {
    select: (row: M.ITerminology) => void
    save: (list: M.ITerminology[]) => void
    insertRole: (row: M.ITerminology) => void
    fixTop: (row: M.ITerminology) => void
    refreshTerm: () => void
  }
}
export const Term = createComponent<TermOptions>({
  props: {
    list: [],
    loading: false,
    tableStyle: { width: '100%', height: '500px' },
  },
  emits: {
    select: (row: M.ITerminology) => {},
    save: (list: M.ITerminology[]) => {},
    insertRole: (row: M.ITerminology) => {},
    fixTop: (row: M.ITerminology) => {},
    refreshTerm: () => {},
  },
}, (props, { emit }) => {
  const route = useRoute()
  const list = ref<M.ITerminology[]>([])

  watch(() => props.list, newVal => {
    list.value = cloneDeep(newVal)
  }, {
    immediate: true,
  })

  return () => (
    <div class="space-y-4">
      <ElTable
        scrollbar-always-on
        stripe
        style={props.tableStyle}
        data={list.value || []}
      >
        <ElTableColumn
          prop="term"
          label="术语名称"
          fixed="left"
          minWidth={140}
          v-slots={{
            default: ({ row }: { row: any }) => {
              return row.character_id
                ? (
                    <Button class="btn btn-primary btn-sm" onClick={() => {
                      emit('insertRole', row)
                    }}>{row.term}</Button>
                  ) : <div class="px-4">{row.term}</div>
            },
          }}
        />
        <ElTableColumn
          prop="category"
          label="类型"
          minWidth={60}
          v-slots={{
            default: ({ row }: { row: any }) => {
              return <div>{row.category?.slice(0, 3) || '-'}</div>
            },
          }}
        />
        <ElTableColumn
          prop="official_name"
          label="官方名"
          minWidth={110}
        />
        <ElTableColumn
          prop="gender"
          label="性别"
          minWidth={60}
          v-slots={{
            default: ({ row }: { row: any }) => <div>{['未知', '男', '女'][row.gender || 0]}</div>,
          }}
        />
        <ElTableColumn
          prop="age"
          label="年龄"
          minWidth={100}
          v-slots={{
            default: ({ row }: { row: any }) => <div>{row.age || '-'}</div>,
          }}
        />
        <ElTableColumn
          prop="voice_labels"
          label="音色特征"
          minWidth={100}
          v-slots={{
            default: ({ row }: { row: any }) => <div>{row.voice_labels || '-'}</div>,
          }}
        />
        <ElTableColumn
          prop="role_labels"
          label="角色人设"
          minWidth={100}
          v-slots={{
            default: ({ row }: { row: any }) => <div>{row.role_labels || '-'}</div>,
          }}
        />
        <ElTableColumn
          prop="opr"
          label="操作"
          fixed="right"
          width={110}
          v-slots={{
            default: ({ row, $index }: { row: any, $index: number }) => {
              const ts = Date.now()
              return (
                <div class="flex justify-end space-x-2">
                  {
                    row.character_id ? (
                      <div class="flex justify-between items-center">
                        <div key={`${row.character_id}_${ts}_fixed`}>
                          {
                            row.isFixed ? (
                              <SvgIcon name="ic_cancel_fix_top" class="size-[20px] cursor-pointer" onClick={() => {
                                emit('fixTop', row)
                              }} />
                            )
                              : (
                                  <SvgIcon name="ic_fix_top" class="size-[20px] cursor-pointer" onClick={() => {
                                    emit('fixTop', row)
                                  }} />
                                )
                          }
                        </div>
                      </div>
                    ) : null
                  }
                  <SvgIcon class="size-[20px] cursor-pointer" name="ic_edit" onClick={() => {
                    const hideDialog = openDialog({
                      title: '编辑术语',
                      mainClass: 'pb-0 px-5',
                      body: () => (
                        <TermEditDialog
                          term={row}
                          onHideDialog={hideDialog}
                          onTermSave={() => {
                            hideDialog()
                            emit('refreshTerm')
                          }}
                        />
                      ),
                    })
                  }} />
                  <SvgIcon class="size-[20px] cursor-pointer" name="ic_del" onClick={() => {
                    const btnLoading = ref(false)
                    const hideDeleteDialog = openDialog({
                      title: '删除',
                      mainClass: 'pb-0 px-5',
                      body: () => (
                        <x-term-del-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <x-term-del-body>确认删除【{row.term}】吗？</x-term-del-body>
                          <x-term-del-footer class="flex w-full justify-end gap-x-[10px]">
                            <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                              try {
                                btnLoading.value = true
                                await apiUpdateOriginalTerminology({
                                  series_resource_id: +route.params.id,
                                  terms: [{
                                    ...row,
                                    delete: 1,
                                  }],
                                })
                                emit('refreshTerm')
                                showSuccessToast('操作成功')
                                hideDeleteDialog()
                              } catch (error: any) {
                                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                              } finally {
                                btnLoading.value = false
                              }
                            }}
                            >
                              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                              确定
                            </button>
                          </x-term-del-footer>
                        </x-term-del-confirm-dialog>
                      ),
                    })
                  }} />
                </div>
              )
            },
          }}
        />

      </ElTable>
      <div class="flex justify-end">
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const hideDialog = openDialog({
            title: '新增术语',
            mainClass: 'pb-0 px-5',
            body: () => (
              <TermEditDialog
                onHideDialog={hideDialog}
                onTermSave={() => {
                  hideDialog()
                  emit('refreshTerm')
                }}
              />
            ),
          })
        }}>新增</Button>
        {/* <Button class="btn btn-primary btn-xs" disabled={props.loading} onClick={() => {
          emit('save', list.value)
        }}>
          {props.loading ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
          保存角色
        </Button> */}
      </div>
    </div>
  )
})

export default Term
