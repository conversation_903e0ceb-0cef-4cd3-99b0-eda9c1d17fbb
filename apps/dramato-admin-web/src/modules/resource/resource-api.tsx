/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from 'src/lib/http-client'
import { filterIt, splitIt } from '@skynet/shared'

type IColumnValue = string | number

type ISourceItem = {
  path: string
  size: number
  duration: number
}

const toNumArr = (value: string[]) => {
  return value.map(v => +v)
}

type IVideo = {
  serial_number: number // 序号
  file_path: string
  file_size: number // 视频文件大小
  duration: number // 视频时长
}

type ITask = {
  series_resource_id: number
  serial_number: number
  job_type: string
  job_id: string
}

// 获取资源列表
export const apiGetResource = (data: M.IResourceQueryParams) =>
  httpClient.post<ApiResponse<{
    meta: string[]
    total: number
    list: IColumnValue[][]
  }>>('/series_resource/search', data)

export const apiGetResourceDetail = (data: { series_resource_id: number }) =>
  httpClient.post<ApiResponse<{
    title: string
    unlocked_episodes: number
    serialize_status: number
    count: number
    label_ids: string
    first_label_ids: string | string[]
    root_label_ids: string | string[]
    second_label_ids: string | string[]
  }>>('/series_resource/detail', data, {
    transformResponseData: {
      'data.first_label_ids': [splitIt([',']), filterIt(Boolean)],
      'data.root_label_ids': [splitIt([',']), filterIt(Boolean)],
      'data.second_label_ids': [splitIt([',']), filterIt(Boolean)],
    },
  })

// 新增、修改资源
export const apiUpdateResource = (data: M.ISourceInfo) =>
  httpClient.post<ApiResponse<null>>('/series_resource/create', data)

// 资源上传结果保存(视频、字幕)
export const apiSourceUpdate = (data: {
  series_resource_id: number
  type: number // 1 视频 2 字幕
  language_code: string
  data: ISourceItem[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/upload_save', data)

// 3. 源视频/合成视频同步
export const apiVideoSync = (data: {
  series_resource_id: number
  type: number // 1 源视频同步 2 合成视频同步
  language_code: string
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/upload_save', data)

// 5. 资源状态操作
export const apiUpdateResourceStatus = (data: {
  series_resource_id: number
  operate_status: number // 1 归档 2 删除
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/edit_status', data)

// 单集删除
export const apiEpisodeDelete = (data: {
  series_resource_id: number
  serial_number_list: number[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/episode_delete', data)

// 8. 字幕提取
export const apiSubtitleExtraction = (data: {
  series_resource_id: number
  serial_numbers: number[]
  type: 1 | 2 | number
  target_lang: string
  region: [number, number, number, number] // 提取字幕的坐标位置（left，top，weight，height）
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/subtitle_extraction', data)

// 8. 字幕提取v2
export const apiSubtitleExtractionV2 = (data: {
  series_resource_id: number
  serial_numbers: number[]
  type: 1 | 2 | number
  region: [number, number, number, number] // 提取字幕的坐标位置（left，top，weight，height）
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/subtitle_extraction', data)

// 9. 字幕抹除
export const apiSubtitleDel = (data: {
  series_resource_id: number
  serial_numbers: number[]
  regions: [number, number, number, number][] // 提取字幕的坐标位置（left，top，weight，height）
  job_way: 1 | 2 | number
  reuse_detext?: 0 | 1
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/subtitle_detext', data, { timeout: 1000 * 60 * 10 })

// 10. 字幕翻译
export const apiSubtitleTrans = (data: {
  series_resource_id: number
  serial_numbers: number[]
  src_lang: string
  target_lang?: string
  target_lang_list?: string[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/subtitle_trans', data)

export const apiAudioTrans = (data: {
  series_resource_id: number
  serial_numbers: number[]
  src_lang: string
  target_lang?: string
  target_lang_list?: string[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/audio_trans', data)

// 11. 字幕注入
export const apiSubtitleInject = (data: {
  series_resource_id: number
  serial_numbers: number[]
  region: number[] // 提取字幕的坐标位置（left，top，weight，height）
  src_lang?: string
  src_lang_list?: string[]
  inject_type?: 0 | 1 | number // 0 只压入字幕 1 字幕和音频同时压入
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/subtitle_inject', data)

// 8. 剧集详情表
export const apiSeriesResourceList = (data: M.IResourceSeriesParams) => {
  const x = (value: {
    list: M.IResourceDrama[]
    list_v2: M.IResourceDrama[]
    title: string
    count: number
    total: number
    use_new: boolean
    subtitle_shorten_tasks?: M.ISubtitleShortenTask[]
  }) => {
    let list = value.list
    if (value.use_new) {
      list = value.list_v2
    }
    const subtitle_shorten_tasks = value.subtitle_shorten_tasks || []
    value.list = list.map(item => {
      const tasks = subtitle_shorten_tasks.filter(task => task.serial_number === item.serial_number)
      if (tasks.length > 0) {
        tasks.forEach(task => {
          item[`${task.language}_shorten_tasks`] = task
        })
      }
      return item
    })
    return value
  }

  return httpClient.post<ApiResponse<{
    list: M.IResourceDrama[]
    list_v2: M.IResourceDrama[]
    title: string
    count: number
    total: number
    use_new: boolean
  }>>('/series_resource/episode_list', data, {
    transformResponseData: {
      data: [x],
    },
  })
}

export const apiSeriesResourceListV2 = (data: M.IResourceSeriesParams) => {
  const x = (value: {
    list: M.IResourceDrama[]
    list_v2: M.IResourceDrama[]
    title: string
    count: number
    total: number
    use_new: boolean
    audio_type: number // 配音类型 0 ：无配音  1： 配音剧 2: ai配音剧
  }) => {
    if (value.use_new) {
      value.list = value.list_v2
      return value
    }
    return value
  }
  return httpClient.post<ApiResponse<{
    list: M.IResourceDrama[]
    list_v2: M.IResourceDrama[]
    title: string
    count: number
    total: number
    use_new: boolean
    audio_type: number // 配音类型 0 ：无配音  1： 配音剧 2: ai配音剧
  }>>('/series_resource/material_cutter_download', data, {
    transformResponseData: {
      data: [x],
    },
  })
}

// 上传视频path保存
export const apiUploadSaveVideo = (data: {
  data: IVideo[]
  series_resource_id: number
  type: 1 | 2
}) =>
  httpClient.post<ApiResponse<{
    list: M.IResourceDrama[]
    title: string
    count: number
  }>>('/series_resource/upload_save_video', data)

// 上传字幕
export const apiUploadSaveSubtitle = (data: {
  data: { file_path: string, serial_number: number }[]
  series_resource_id: number
  language_code: string
  alter_type?: number
  audio_type?: number
}) =>
  httpClient.post<ApiResponse<{
    list: M.IResourceDrama[]
    title: string
    count: number
    alter_type: number // 1 是已经修改
  }>>('/series_resource/upload_save_subttle', data)

export const apiUploadSaveAudio = (data: {
  data: { file_path: string, serial_number: number }[]
  series_resource_id: number
  audio_type: 1 | 2 | 3 | number // 1 原始声音 2 背景声音 3 特效声音
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/upload_save_audio', data)

// 同步
export const apiResourceSync = (data: {
  series_resource_id: number
  episode_resouce_id: number[]// 单集id列表
  type: number // 1 源视频同步 2 合成视频同步 3.
  language_code?: string // 同步合成视频的时候需要这个字段，需要知道是同步哪个语言的合成视频
  language_codes?: string[]
  subtitle_languages?: string[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/video_sync', data)

// 获取任务列表
export const apiGetTaskList = (data: {
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<{
    tasks: ITask[]
  }>>('/series_resource/task_list', data)

// 获取任务详情
export const apiGetTaskItem = (data: ITask) =>
  httpClient.post<ApiResponse<{
    finished: boolean
    status: string
  }>>('/series_resource/task_refresh', data)

export const apiGetSeriesInfo = (data: { series_resource_id: number }) =>
  httpClient.post<ApiResponse<{
    list: M.IEpisodeSeriesInfo[]
  }>>('/series_resource/series_info', data)

export const apiUpdateSeriesInfo = (data: { series_resource_id: number, list: M.IEpisodeSeriesInfo[] }) =>
  httpClient.post<ApiResponse<{
    list: M.IEpisodeSeriesInfo[]
  }>>('/series_resource/series_update', data)

export const apiTransSeriesInfo = (data: {
  series_resource_id: number
  src_lang: string
  target_lang_list: string[]
  title: string
  description: string
}) =>
  httpClient.post<ApiResponse<{
    list: M.IEpisodeSeriesInfo[]
  }>>('/series_resource/series_info_trans', data)

export const apiGetLabelList = (data: {
  language_code: string
  series_resource_id?: number
  label_type?: 1 | 2 | 3
}) =>
  httpClient.post<ApiResponse<{
    list: M.ITag[]
  }>>('/series_resource/label_list', data)

export const apiBaiduDiskUpload = (data: M.IDiskInfo) => httpClient.post<ApiResponse<null>>('/series_resource/netdisk_upload', data)
export const apiBaiduDiskUploadProgress = (data: { series_resource_id: number }) => httpClient.post<ApiResponse<{
  list: M.IDiskInfoUploadProgress[]
}>>('/series_resource/netdisk_progress', data)

export const apiGetRecordList = (data: { series_resource_id: number }) => httpClient.post<ApiResponse<{
  list: M.IAlterSubtitle[]
}>>('/series_resource/operate_record_list', data)

export const apiEraseAudio = (data: {
  series_resource_id: number
  serial_numbers: number[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/audio_erase', data, { timeout: 1000 * 60 * 10 })

export const apiGetFileByDir = (data: {
  dir: string
}) =>
  httpClient.post<ApiResponse<{
    files: string[]
  }>>('/series_resource/netdisk/file/list', data, { timeout: 1000 * 60 * 10 })

export const apiSeparateAudio = (data: {
  series_resource_id: number
  serial_numbers: number[]
}) =>
  httpClient.post<ApiResponse<{
    files: string[]
  }>>('/series_resource/audio_separate', data, { timeout: 1000 * 60 * 10 })

export const apiUploadExcel = (data: {
  file: File
}) =>
  httpClient.post<ApiResponse<{
    files: string[]
  }>>('/series_resource/resource_batch_import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })

export const apiUploadBusinessExcel = (data: {
  file: File
}) =>
  httpClient.post<ApiResponse<{
    files: string[]
  }>>('/series_resource/business_batch_import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })

export const apiFixSubtitle = (data: {
  series_resource_id: number
  serial_numbers: number[]
  lang_list: string[]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/subtitle_fix', data)

// 1. 外挂音频转码（新增）
export const apiAudioTransCode = (data: {
  series_resource_id: number
  series_numbers: number[]
  audio_original_language: string // 音频默认语言
  external_languages: string[] // 外挂的语言
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/external_audio_transcode', data)
// 2. 外挂音频进度+预览（新增）
export const apiGetExternalTransList = (data: {
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<{
    resource_list: M.IExternalTransProgress[]
  }>>('/series_resource/external_trans_progress', data)

// 3. 外挂音频同步（现有，加字段）
export const apiExternalAudioSync = (data: {
  series_resource_id: number
  episode_resouce_id: number[]
  language_codes: string[]
  type: number // 2024.11.29新增： 外挂音频同步
  subtitle_languages: string[] // 2024.11.29新增： 同步哪些字幕的语言到上面的剧中
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/external_audio_sync', data)

export const apiExternalSubtitleRegion = (data: {
  series_resource_id: number
  series_numbers: number[]
  region: [number, number, number, number]
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/external_subtitle_region', data)

export const apiRetryTrans = (data: {
  series_resource_id: number
  serial_number: number
  language_code: string
  trans_type: 1 | 2 // 1: 字幕翻译 2:语音翻译
}) =>
  httpClient.post<ApiResponse<null>>('/series_resource/episode_trans_retry', data)

export const apiGetEpisodeBatchList = (data: {
  series_resource_id_list: number[]
  language_code_list: string[]
  type: 1 | 2 // 1 免费的 2 所有的
}) =>
  httpClient.post<ApiResponse<{
    list: M.IEpisodeBatchItem[]
  }>>('/series_resource_pri/episode_batch_list', data)
