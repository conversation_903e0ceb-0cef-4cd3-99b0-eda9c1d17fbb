import { createComponent, getQueries } from '@skynet/shared'
import { background, verify_success } from './images/images'
import { Button } from '@skynet/ui'
import { onMounted, onUnmounted, ref } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { apiCheckEmail, apiCheckEmailResult } from './bind-email-api'
import { showToast } from '../common/toast/toast'
import Loading from '../common/loading/loading'
import { isIOS } from '@vueuse/core'
export const BindEmailPage = createComponent(null, () => {
  const { language, from } = getQueries({
    language: 'en',
    from: 'push',
  })
  const { t } = useLocale(language)
  const email = ref('')
  const emailError = ref('')
  const hasSendEmail = ref(false)
  const verifySuccess = ref(false)
  const isVerifyingEmail = ref(false)
  const loading = ref(true)

  const validateEmail = () => {
    if (!email.value) return false
    // 判断邮箱的正则
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email.value)) {
      emailError.value = t('bind_email.email_error')
      return false
    }
    return true
  }

  let timer: number | null = null

  const verify = () => {
    if (!validateEmail()) return
    track('bindemail', 'activity_button', 'click', {
      from,
      button: 'verify',
      email: email.value,
    })
    void apiCheckEmail({
      email: email.value,
      from,
    }).then(() => {
      hasClickedReEnter.value = false
      hasSendEmail.value = true
      isVerifyingEmail.value = true
      createTimer()
    }).catch((error?: { response?: { data?: { message?: string } } }) => {
      if (error?.response?.data?.message) {
        showToast(error.response.data.message)
      }
    })
  }

  // 三秒轮询验证结果
  const createTimer = () => {
    timer = window.setInterval(() => {
      void apiCheckEmailResult().then(res => {
        if (hasClickedReEnter.value) return
        if (res.data?.state === 2) {
          track('bindemail', 'activity', 'success', {
            from,
            email: email.value,
          })
          hasSendEmail.value = true
          isVerifyingEmail.value = false
          verifySuccess.value = true
          timer && clearInterval(timer)
        } else if (res.data?.state === 1) {
          hasSendEmail.value = true
          isVerifyingEmail.value = true
          verifySuccess.value = false
        }
      })
    }, 3000)
  }

  onUnmounted(() => {
    btnTimer && clearTimeout(btnTimer)
    timer && clearInterval(timer)
    window.removeEventListener('resize', scrollIntoView)
  })

  const hasClickedReEnter = ref(false)

  const reEnter = () => {
    hasClickedReEnter.value = true
    track('bindemail', 'activity_button', 'click', {
      from,
      button: 're_enter',
      email: email.value,
    })
    isVerifyingEmail.value = false
    hasSendEmail.value = false
    verifySuccess.value = false
    timer && clearInterval(timer)
  }

  onMounted(() => {
    document.title = t('bind_email.title')
    track('bindemail', 'activity_page', 'show', {
      from,
    })
    window.addEventListener('resize', scrollIntoView)
    void apiCheckEmailResult().then(res => {
      // 如果邮箱已验证，则直接跳转
      if (res.data?.state === 2) {
        track('bindemail', 'activity', 'success', {
          from,
          email: email.value,
        })
        hasSendEmail.value = true
        isVerifyingEmail.value = false
        verifySuccess.value = true
      } else if (res.data?.state === 1) {
        hasSendEmail.value = true
        isVerifyingEmail.value = true
        verifySuccess.value = false
        createTimer()
      }
    }).finally(() => {
      loading.value = false
    })
  })

  const btnRef = ref<HTMLDivElement>()
  let btnTimer: number | null = null
  const scrollIntoView = () => {
    btnTimer = window.setTimeout(() => {
      btnRef.value?.scrollIntoView({
        behavior: 'smooth',
        block: isIOS ? 'center' : 'end',
        inline: 'nearest',
      })
    }, 500) // 需等待键盘动画完成
  }

  return () => (
    loading.value
      ? (
          <Loading />
        )
      : (
          <div class="max-w-[var(--phone-page-max-width)] pad:max-w-[var(--phone-page-max-width)] pc:max-w-[var(--phone-page-max-width)] mx-auto min-h-full relative bg-gradient-to-b from-[#ff3950] to-[#ff4850] pb-10">
            <img width={430} height={390} class="block w-full h-auto object-cover" src={background} />
            <x-title class="absolute top-7 left-0 z-up w-full px-15 text-center text-[#fff6dd] text-2xl font-bold line-clamp-3">{t('bind_email.title')}</x-title>
            <x-container class="relative pt-2 pb-5 px-4 mx-3 w-[calc(100%-24px)] bg-white rounded-lg flex-col justify-start items-center gap-3 flex">
              {
                verifySuccess.value && (
                  <x-verify-success class="w-full flex flex-col py-4 gap-y-2 items-center justify-center">
                    <img class="block w-15 h-15 object-cover" src={verify_success} />
                    <p class="text-center text-[#1cc15b] text-lg font-bold">{t('bind_email.verify_success')}</p>
                  </x-verify-success>
                )
              }
              <x-list class="flex flex-col gap-y-1 py-3 w-full text-[#434546] text-sm font-normal">
                {
                  [
                    t('bind_email.list_1'),
                    t('bind_email.list_2'),
                    t('bind_email.list_3'),
                  ].map(item => (
                    <x-list-item class="flex items-start gap-1"><div class="flex items-center justify-center h-full w-4 shrink-0">•</div> {item}</x-list-item>
                  ))
                }
              </x-list>
              {
                verifySuccess.value
                  ? null
                  : (
                      hasSendEmail.value
                        ? (
                            <>
                              <x-title class="block text-center text-[#0b080b] text-sm font-bold mb-5">{t('bind_email.verify_progress')}</x-title>
                              <Button
                                class="bg-[#fc2763] no-tap-color border-none rounded-lg w-full h-11 text-[#fff] text-base outline-none"
                                onClick={reEnter}
                              >{t('bind_email.re_enter')}
                              </Button>
                            </>
                          )
                        : (
                            <>
                              <x-email-input class="w-full text-sm block pb-5">
                                <x-form-label class="font-bold text-[#0b080b]">Email</x-form-label>
                                <input
                                  type="email"
                                  v-model={email.value}
                                  placeholder={t('bind_email.email_placeholder')}
                                  class="dramato-input py-3 pl-0 !bg-transparent text-[#090609] text-sm no-tap-color active:!bg-transparent focus:!bg-transparent "
                                  onBlur={validateEmail}
                                  onInput={() => emailError.value = ''}
                                  onFocus={scrollIntoView}
                                />
                                <x-line class="w-full h-[1px] bg-[#dddadd] block" />
                                {emailError.value && <x-error class="block w-full line-clamp-2 text-xs pt-1 text-[#ff3b30]">{emailError.value}</x-error>}
                              </x-email-input>
                              <Button
                                class={`bg-[#fc2763] no-tap-color border-none rounded-lg w-full h-11 text-[#fff] text-base outline-none ${email.value && !isVerifyingEmail.value ? 'opacity-100' : 'opacity-50'}`}
                                onClick={verify}
                              >{t('bind_email.verify')}
                              </Button>
                              <div ref={btnRef} class="h-[1px] -mt-3 w-full invisible" />
                            </>
                          )
                    )
              }
            </x-container>
          </div>
        )
  )
})

export default BindEmailPage
