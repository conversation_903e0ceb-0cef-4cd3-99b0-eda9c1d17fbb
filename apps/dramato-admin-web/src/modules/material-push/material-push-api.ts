import { httpClient } from 'src/lib/http-client'

export const apiListAvailableMaterial = (data: Api.ListAvailableMaterialRequest) => {
  return httpClient.post<ApiResponse<{ total: number, list: M.AvailableMaterial[] }>>('/material/available', data)
}

export const apiPushMaterial = (data: RPPick<M.AvailableMaterial, 'series_key' | 'app_id', 'third_key' | 'country' | 'listing_time'>) => {
  return httpClient.post<ApiResponse<{ success: boolean }>>('/material/push', data)
}

export const apiUpdateReleaseStatus = (data: RPick<M.AvailableMaterial, 'app_id' | 'series_key' | 'release_status'>) => {
  return httpClient.post<ApiResponse<{ success: boolean }>>('/material/release_status', data)
}

export const apiUpdateThirdKey = (data: RPick<M.AvailableMaterial, 'app_id' | 'series_key' | 'third_key'>) => {
  return httpClient.post<ApiResponse<{ success: boolean }>>('/material/third_key', data)
}

export const apiPushToMoboshort = (data: RPick<M.AvailableMaterial, 'app_id' | 'series_key'>) => {
  return httpClient.post<ApiResponse<{ success: boolean }>>('/material/moboshort_push', data)
}
