import { createComponent, mc } from '@skynet/shared'
import { useSeriesPackageAddFormStore } from './use-series-package-add-form-store'
import { set } from 'lodash-es'
import { transformNumber } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import { lang } from 'src/lib/constant'

const Countries = [
  {
    value: 'US',
    label: '美国',
  },
  {
    value: 'JP',
    label: '日本',
  },
  {
    value: 'PH',
    label: '菲律宾',
  },
  {
    value: 'ID',
    label: '印度尼西亚',
  },
  {
    value: 'TR',
    label: '土耳其',
  },
  {
    value: 'BR',
    label: '巴西',
  },
  {
    value: 'GB',
    label: '英国',
  },
  {
    value: 'TH',
    label: '泰国',
  },
  {
    value: 'DE',
    label: '德国',
  },
  {
    value: 'IT',
    label: '意大利',
  },
  {
    value: 'AU',
    label: '澳大利亚',
  },
  {
    value: 'FR',
    label: '法国',
  },
  {
    value: 'CA',
    label: '加拿大',
  },
  {
    value: 'MY',
    label: '马来西亚',
  },
  {
    value: 'KR',
    label: '韩国',
  },
  {
    value: 'VN',
    label: '越南',
  },
  {
    value: 'MX',
    label: '墨西哥',
  },
  {
    value: 'IN',
    label: '印度',
  },
  {
    value: 'ZA',
    label: '南非',
  },
  {
    value: 'SG',
    label: '新加坡',
  },
  {
    value: 'BE',
    label: '比利时',
  },
  {
    value: 'PT',
    label: '葡萄牙',
  },
  {
    value: 'NL',
    label: '荷兰',
  },
  {
    value: 'CL',
    label: '智利',
  },
  {
    value: 'ES',
    label: '西班牙',
  },
  {
    value: 'CH',
    label: '瑞士',
  },
  {
    value: 'AT',
    label: '奥地利',
  },
  {
    value: 'CO',
    label: '哥伦比亚',
  },
  {
    value: 'AE',
    label: '阿联酋',
  },
  {
    value: 'PR',
    label: '波多黎各',
  },
  {
    value: 'DK',
    label: '丹麦',
  },
]

type Step1FormOptions = {
  props: {}
}
export const Step1Form = createComponent<Step1FormOptions>({
  props: {},
}, props => {
  const {
    formData,
    Form,
    oneStepError,
  } = useSeriesPackageAddFormStore()

  return () => (
    <Form
      class="flex flex-1 flex-col gap-y-3"
      hasAction={false}
      error={oneStepError.value}
      onChange={(path, value) => {
        if (path === 'scene_type' && value === 3 && formData.value.import_type === 3) {
          formData.value.import_type = 1
        }
        set(formData.value || {}, path, value)
      }}
      items={[
        [
          requiredLabel('剧包名称'),
          'package_name',
          {
            type: 'textarea',
            placeholder: '请输入剧包名称',
            rows: 3,
            disabled: formData.value.needDisabled,
          },
          {
            class: mc('col-span-1'),
          },
        ],
        [
          requiredLabel('排序'),
          'priority',
          {
            type: 'number',
            disabled: formData.value.needDisabled,
          },
          {
            class: mc('col-span-1'),
            transform: transformNumber,
          },
        ],
        [
          requiredLabel('应用设备'),
          'user_platform',
          {
            type: 'radio',
            options: [
              { label: '全部', value: 0, disabled: formData.value.needDisabled },
              { label: 'iOS', value: 1, disabled: formData.value.needDisabled },
              { label: 'Android', value: 2, disabled: formData.value.needDisabled },
            ],
          },
        ],
        [
          requiredLabel('应用场景'),
          'scene_type',
          {
            type: 'radio',
            options: [
              { label: '商业化策略', value: 1, disabled: formData.value.needDisabled },
              { label: '首页内容', value: 3, disabled: formData.value.needDisabled },
              { label: '活动', value: 4, disabled: formData.value.needDisabled },
              { label: '运营位弹窗', value: 5, disabled: formData.value.needDisabled },
            ],
          },
        ],
        [
          requiredLabel('添加方式'),
          'import_type',
          {
            type: 'radio',
            options: formData.value.scene_type === 3 ? [
              { label: '手动导入', value: 1, disabled: formData.value.needDisabled },
              { label: '批量上传', value: 2, disabled: formData.value.needDisabled },
              // { label: '筛选导入', value: 3 },
              { label: '指定范围（动态更新）', value: 4, disabled: formData.value.needDisabled },
            ] : [
              { label: '手动导入', value: 1, disabled: formData.value.needDisabled },
              { label: '批量上传', value: 2, disabled: formData.value.needDisabled },
              { label: '筛选导入', value: 3, disabled: formData.value.needDisabled },
              { label: '指定范围（动态更新）', value: 4, disabled: formData.value.needDisabled },
            ],
          },
        ],
        !(formData.value.scene_type === 3 && [1, 2].includes(formData.value.import_type || 0)) && [
          '应用国家',
          'country_include',
          {
            type: 'multi-select',
            options: Countries,
            popoverWrapperClass: 'z-popover-in-dialog',
            search: true,
            disabled: formData.value.needDisabled,
          },
          {
            transform: [
              (display: string): string[] => display.split(/,|\n/g).map(item => item.trim()).filter(i => !!i),
              (raw?: unknown) => Array.isArray(raw) ? raw.join(',') : raw?.toString() ?? '',
            ] as const,
          },
        ],
        !(formData.value.scene_type === 3 && [1, 2].includes(formData.value.import_type || 0)) && [
          '指定排除范围',
          'country_exclude',
          {
            type: 'multi-select',
            options: Countries,
            popoverWrapperClass: 'z-popover-in-dialog',
            disabled: formData.value.needDisabled,
            search: true,
          },
          {
            transform: [
              (display: string): string[] => display.split(/,|\n/g).map(item => item.trim()).filter(i => !!i),
              (raw?: unknown) => Array.isArray(raw) ? raw.join(',') : raw?.toString() ?? '',
            ] as const,
          },
        ],
        formData.value.scene_type === 3 && [1, 2].includes(formData.value.import_type || 0) && [
          '发行语言',
          'language_version_code',
          {
            type: 'radio',
            options: [{ label: '全部', value: 'all', disabled: formData.value.needDisabled }, ...(lang.map(item => ({ ...item, disabled: formData.value.needDisabled })))],

          },
          { class: 'flex-wrap w-full mb-24' },
        ],
        [
          '备注',
          'remark',
          {
            type: 'textarea',
            placeholder: '请输入备注',
            rows: 3,
            disabled: formData.value.needDisabled,
          },
          {
            class: mc('col-span-1'),
          },
        ],
      ]}
      data={formData.value}
    />
  )
})

export default Step1Form
