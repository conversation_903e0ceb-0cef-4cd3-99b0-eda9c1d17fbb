<template>
  <div class="timeline-container" ref="containerRef">
    <div class="timeline-scroll">
      <v-stage :config="stageConfig" @mousedown="handleMouseDown" @mousemove="handleMouseMove" @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp" ref="stageRef">
        <v-layer>
          <v-group :config="groupConfig">
            <x-graduation :duration="duration" :width="contentWidth" />
            <x-cursor :position="cursorPosition" :height="stageSize.height" />
            <x-track :width="contentWidth" :height="80" :y="50">
              <x-audio :width="200" :height="60" :x="100" :minWidth="50" />
            </x-track>
          </v-group>
        </v-layer>
      </v-stage>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import XGraduation from './x-graduation.vue';
import XCursor from './x-cursor.vue';
import XTrack from './x-track.vue';
import XAudio from './x-audio.vue';

const containerRef = ref<HTMLElement>();
const stageRef = ref();

const stageSize = {
  width: 982,
  height: 204
};

// 内容实际宽度（每秒10像素）
const pixelsPerSecond = 10;
const duration = ref(300); // 5分钟
const contentWidth = computed(() => duration.value * pixelsPerSecond);

// 舞台配置
const stageConfig = computed(() => ({
  ...stageSize,
  draggable: true,
  dragBoundFunc: (pos: { x: number, y: number }) => ({
    x: Math.min(0, Math.max(stageSize.width - contentWidth.value, pos.x)),
    y: 0
  })
}));

const groupConfig = {
  x: 50,
  y: 0,
  draggable: false
};

// 光标位置
const cursorPosition = ref(0);

// 处理鼠标事件
let isDragging = false;
let lastX = 0;

const handleMouseDown = (e: any) => {
  const stage = stageRef.value.getStage();
  const point = stage.getPointerPosition();
  lastX = point.x;
  isDragging = true;
};

const handleMouseMove = (e: any) => {
  if (!isDragging) return;

  const stage = stageRef.value.getStage();
  const point = stage.getPointerPosition();
  const dx = point.x - lastX;

  const scrollContainer = containerRef.value?.querySelector('.timeline-scroll');
  if (scrollContainer) {
    scrollContainer.scrollLeft -= dx;
  }

  lastX = point.x;
};

const handleMouseUp = () => {
  isDragging = false;
};

// 清理事件监听
onUnmounted(() => {
  handleMouseUp();
});
</script>

<style scoped>
.timeline-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  user-select: none;
}

.timeline-scroll {
  width: 100%;
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
  scrollbar-color: #CBD5E1 #F1F5F9;
}

.timeline-scroll::-webkit-scrollbar {
  height: 8px;
}

.timeline-scroll::-webkit-scrollbar-track {
  background: #F1F5F9;
  border-radius: 4px;
}

.timeline-scroll::-webkit-scrollbar-thumb {
  background-color: #CBD5E1;
  border-radius: 4px;
}

.timeline-scroll::-webkit-scrollbar-thumb:hover {
  background-color: #94A3B8;
}
</style>
