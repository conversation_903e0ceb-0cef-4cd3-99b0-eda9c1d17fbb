<template>
  <x-transaction-history-page class="px-3 wallet text-[#cccacb] overflow-y-auto relative flex flex-col gap-y-2 bg-[#000] size-full">
    <Back 
      :is-white="true" 
      :title="T.walletPage.rewardsHistory.ref.value" 
      :is-close-webview="false"
      class="z-100 text-[#fdfbfc] font-bold w-full flex items-center sticky top-0 h-11 bg-[#000] shrink-0"
    />
    <div
      v-if="(!rewardList || !rewardList.items?.length) && !pageLoading"
      class="flex-1 flex mt-48% items-center justify-center"
    >
      <EmptyPage
        :image-size="160"
        :description="T.walletPage.noContentAvailable.ref.value"
      />
    </div>
    <template
      v-for="_ in 8"
      v-if="pageLoading"
      :key="_"
    >
      <van-skeleton>
        <template #template>
          <div class="w-full flex justify-between items-center mb-4">
            <div class="flex flex-col w-60%">
              <van-skeleton-paragraph
                :round="true"
                row-width="60%"
              />
              <van-skeleton-paragraph
                :round="true"
                row-width="35%"
              />
            </div>
            <div class="w-40% flex flex-col items-end ">
              <van-skeleton-paragraph
                :round="true"
                row-width="20%"
              />
              <van-skeleton-paragraph
                :round="true"
                row-width="40%"
              />
            </div>
          </div>
        </template>
      </van-skeleton>
    </template>
    <div
      v-if="rewardList && rewardList.items && rewardList.items.length"
      class="py-5 px-3 flex flex-col gap-3 text-[#cccacb] text-base"
    >
      <div
        v-for="item in rewardList.items"
        class="flex justify-between"
      >
        <div>
          <p class="mb-2">
            {{ item.title }}
          </p>
          <p class="text-[#434546] text-xs">
            {{ item.pay_finish_time ? dayjs.unix(item.pay_finish_time).format('YYYY-MM-DD') : 0 }}
          </p>
        </div>
        <div class="flex flex-col justify-end">
          <p class="text-right text-sm mb-2 bg-right-center pr-5 bg-[length:1.125rem] bg-no-repeat bg-[url('src/h5_modules/images/coins-dark.webp')]">
            {{ item.flow_type === 1 ? '+' : '-' }} {{ item.amount }}
          </p>
          <p class="text-[#434546] text-xs">
            {{ $t('walletPage.expiresOn', [formatTime(item.bonus_expire_time)]) }}
          </p>
        </div>
      </div>
      <p class="mt-10 mb-3 text-center  text-[#6e7071] text-sm">
        {{ T.walletPage.lastLine.ref.value }}
      </p>
    </div>
  </x-transaction-history-page>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {  Skeleton as VanSkeleton, SkeletonParagraph as VanSkeletonParagraph } from 'vant';
import { Back } from 'src/h5_modules/common/back/back'
import {apiRewardsList} from '../wallet-api'
import dayjs from 'dayjs'
import { T } from '../wallet-page.i18n'
import EmptyPage from 'src/h5_modules/common/empty-page/empty-page.vue'

const pageLoading = ref(true);
const rewardList = ref({
  // items: [
  // {
  //               "txn_id": "1833480822366470144",
  //               "title": "购买剧情",
  //               "amount": 1,
  //               "flow_type": 2,
  //               "pay_finish_time": 1725970867,
  //               "bonus_expire_time": 0,
  //               "business_data": "{\"amount\":1,\"series_key\":\"3fTpz6gHqf\",\"episode\":2}"
  //           },
  //           {
  //               "txn_id": "1833480242357145600",
  //               "title": "购买剧情",
  //               "amount": 1,
  //               "flow_type": 2,
  //               "pay_finish_time": 1725970728,
  //               "bonus_expire_time": 0,
  //               "business_data": "{\"amount\":1,\"series_key\":\"3fTpz6gHqf\",\"episode\":2}"
  //           }
  // ]
})

const getRewardsList = async () => {
  const res =  await apiRewardsList()
  pageLoading.value = false;
  if (res.data && res.data) {
    rewardList.value = res.data
  }
}

const formatTime = (time) => {
  return time ? dayjs.unix(time).format('YYYY-MM-DD') : 0 
}

onMounted(() => {
  getRewardsList();
})
</script>

<style scoped>
.van-skeleton-paragraph {
  height: 0.8rem;
  background-color: #242526;
}
</style>