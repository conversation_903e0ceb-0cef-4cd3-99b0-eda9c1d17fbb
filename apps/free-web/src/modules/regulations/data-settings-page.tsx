import { createComponent } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { DataSettingsItem } from './data-settings-item'
import './custom-markdown.css'
import router from 'src/router'
import { useLocale } from 'src/lib/use-locale'
import { inApp, jsBridge } from 'src/lib/jsbridge'
import { Back } from '../common/back/back'

export const DataSettingsPage = createComponent(null, props => {
  const language = router.currentRoute.value.query.language as string || 'en'
  const from = router.currentRoute.value.query.from as string
  const isFromRegulations = from === 'regulations'
  const { t } = useLocale(language)

  const dataSettings = ref({
    personalised: true,
    advertisement: true,
    behavioural: true,
  })

  onMounted(() => {
    if (window.localStorage.getItem('consent')) {
      const consent = JSON.parse(window.localStorage.getItem('consent') as string) as Consent.Consent
      console.log('consent', consent)
      dataSettings.value.personalised = consent.allow_ad_personalization_signals
      dataSettings.value.advertisement = consent.allow_ad_storage !== consent.allow_ad_user_data ? true : consent.allow_ad_storage
      dataSettings.value.behavioural = consent.allow_analytics_storage
      return
    }
    void jsBridge<Consent.Consent | null>('get_google_analytics_consent_status').then(consent => {
      console.log('consent', consent)
      if (consent) {
        dataSettings.value.personalised = consent.allow_ad_personalization_signals ?? true
        dataSettings.value.advertisement = consent.allow_ad_storage !== consent.allow_ad_user_data ? true : consent.allow_ad_storage ?? true
        dataSettings.value.behavioural = consent.allow_analytics_storage ?? true
      } else {
        dataSettings.value.personalised = true
        dataSettings.value.advertisement = true
        dataSettings.value.behavioural = true
      }
    })
  })

  return () => (
    <div class="size-full bg-[var(--surface-1)] text-[var(--text-1)]">
      <Back isCloseWebview={!isFromRegulations} />
      <div class="px-3 py-5 border-b-[1px] border-b-solid border-b-[var(--line-1)] text-center text-base leading-tight">{t('regulations.aboutYourPrivacy')}</div>
      {[
        {
          name: t('regulations.personalDataName'),
          desc: t('regulations.personalDataDescription'),
          key: 'personalised',
        },
        {
          name: t('regulations.advertisementDataName'),
          desc: t('regulations.advertisementDataDescription'),
          key: 'advertisement',
        },
        {
          name: t('regulations.behaviorsDataName'),
          desc: t('regulations.behaviorsDataDescription'),
          key: 'behavioural',
        },
      ].map(item => (
        <DataSettingsItem
          name={item.name}
          desc={item.desc}
          modelValue={dataSettings.value[item.key as keyof typeof dataSettings.value]}
          onUpdate:modelValue={v => dataSettings.value[item.key as keyof typeof dataSettings.value] = v}
        />
      ))}
      <x-bottom class="bottom-0 fixed z-1000 pc:w-[calc(var(--phone-page-max-width))] pad:w-[calc(var(--phone-page-max-width))] w-full bg-[var(--surface-1)] gap-2 p-3 flex flex-col justify-center items-center text-base share-bottom-8">
        <Button class="w-full h-11 rounded-lg justify-center text-[var(--text-5)] items-center flex bg-[var(--brand-6)] border-none" onClick={() => {
          if (isFromRegulations) {
            const consent = {
              allow_ad_personalization_signals: dataSettings.value.personalised,
              allow_ad_storage: dataSettings.value.advertisement,
              allow_ad_user_data: dataSettings.value.advertisement,
              allow_analytics_storage: dataSettings.value.behavioural,
            }
            window.localStorage.setItem('consent', JSON.stringify(consent))
            void router.push({ path: 'rules', query: { ...router.currentRoute.value.query } })
          } else {
            if (inApp) {
              const consent = {
                allow_ad_personalization_signals: dataSettings.value.personalised,
                allow_ad_storage: dataSettings.value.advertisement,
                allow_ad_user_data: dataSettings.value.advertisement,
                allow_analytics_storage: dataSettings.value.behavioural,
              }
              window.localStorage.setItem('consent', JSON.stringify(consent))
              void jsBridge('google_analytics_consent_status', { ...consent, close_page: true })
            }
          }
        }}
        >{ isFromRegulations ? t('regulations.next') : t('regulations.confirm')}
        </Button>
      </x-bottom>
    </div>
  )
})

export default DataSettingsPage
