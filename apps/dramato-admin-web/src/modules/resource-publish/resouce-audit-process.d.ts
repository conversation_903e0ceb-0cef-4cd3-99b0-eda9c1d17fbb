declare namespace Api {
  namespace ResourceAuditProcess {
    type Param = {
      page_index?: number
      page_size?: number
      series_key_or_title_list: string[] | string
      resource_id_or_title_list: string[] | string
    }
  }
}

declare namespace M {
  namespace IResourceAuditProcess {
    type LanguageTime = {
      language_code: string
      time: number | string
      series_key?: string
    }

    type LanguageUser = {
      language_code: string
      user: string
    }
    interface AuditItem {
      id: number
      title: string
      process: 1 | 2 | 3 | 4 // 1 资源录入 2 资源验收 3 资源发布 4 资源上架
      entering_time: number | string
      entering_user: string
      check_time: number | string
      check_user: string
      subtitle_check_time: LanguageTime[]
      subtitle_check_user: LanguageUser[]
      info_check_time: number | string // 信息验收时间
      info_check_user: string
      publish_source_check_time: LanguageTime[]
      publish_source_check_user: LanguageUser[]
      publish_compound_check_time: LanguageTime[]
      publish_compound_check_user: LanguageUser[]
      final_check_time: number | string // 终审验收时间
      final_check_user: string
      shelve_time: LanguageTime[]
      shelve_user: LanguageUser[]
      release_round: 1 | 2 // 发行轮次 1 首发 2二轮
      resource_type: 1 | 2 // 资源类型 1 本土 2 翻译
      duration: number
      count: number
    }
    type AuditList = AuditItem[] | []
  }
}
