<template>
  <x-cover class="z-99 fixed inset-0 size-full bg-[#0b080b]/60">
    <div class="cover-content text-base font-medium text-white">
      <p class="line-clamp-5 leading-tight [text-shadow:_0px_1px_8px_rgb(0_0_0_/_0.56)]">
        {{ T.productPanel.goToHome.ref.value }}
      </p>
      <div
        class="flex size-full h-11 items-center justify-center rounded-lg  bg-[#fc2763]"
        @click="goToHome"
      >
        {{ T.productPanel.go.ref.value }}
      </div>
    </div>
  </x-cover>
</template>
<script setup lang="ts">
// import { useRouter } from 'vue-router'
import {T} from './product-panel.i18n'
// const router = useRouter();
const emit = defineEmits(['close']);

const goToHome = () => {
  void emit('close');
  window.location.href = window.location.origin
}
</script>
<style scoped>
.cover-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% - 8.25rem); /* 66px * 2 */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}
</style>