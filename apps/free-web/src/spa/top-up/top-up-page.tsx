import { bindLoading, createComponent, getQueries, getQuery, mc, when } from '@skynet/shared'
import { background, balanceBg, close, coin, ticket } from './images/images'
import { computed, onMounted, ref, watch } from 'vue'
import { useTopUpStore } from './use-top-up-store'
import { jsBridge } from 'src/lib/jsbridge'
import { useStretchScroll } from 'src/lib/use-stretch-scroll'
import { useLocale } from 'src/lib/use-locale'
import { TransitionPresets, useTransition } from '@vueuse/core'
import { Button } from '@skynet/ui'
import { authInfo } from 'src/lib/get-auth'
import { deviceInfo } from 'src/lib/get-device-info'
import { apiExchange } from './top-up-api'
import { track } from 'src/lib/track'
import { currencyList } from 'src/lib/get-currency'
import dayjs from 'dayjs'
import { Back } from 'src/modules/common/back/back'
import { getCurrency } from 'src/lib/get-currency'
import { useMyWalletStore } from 'src/modules/my-wallet/use-my-wallet-store'
import { reportNow } from '@skynet/client-track'
import Loading from 'src/modules/common/loading/loading'

export const TopUpPage = createComponent(null, () => {
  const { halfScreen, language, series_key, episode_key, session_id, episode_price, from, video_id_rank } = getQueries({
    halfScreen: 'false',
    language: 'en',
    series_key: '',
    episode_key: '',
    session_id: '',
    episode_price: '',
    from: '',
    video_id_rank: '',
  })
  const isHalfScreen = halfScreen === 'true'
  const { t } = useLocale(language)
  const { myWallet, getMyWallet } = useMyWalletStore()
  const { exchangeList, rechargeList, getWalletStoreList, loading } = useTopUpStore()
  const activePurchase = ref<TopUp.RechargeProduct | null>(null)
  const activeExchange = ref<TopUp.ExchangeProduct | null>(null)
  const balanceRef = ref<HTMLSpanElement | null>(null)
  const ticketRef = ref<HTMLSpanElement | null>(null)
  const scrollRef = ref<HTMLDivElement | null>(null)
  const topUpY = ref(0)
  const dialogY = ref(100)
  const exchangeLoading = ref(false)
  const topUpTargetY = useTransition(topUpY, {
    duration: 500,
    transition: TransitionPresets.easeOutCubic,
  })
  const dialogTargetY = useTransition(dialogY, {
    duration: 500,
    transition: TransitionPresets.easeOutCubic,
    onFinished: () => {
      if (dialogTargetY.value === 0) {
        if (isHalfScreen) {
          track('video', 'exchange_confirm_pop', 'show', {
            video_id: episode_key,
            series_id: series_key,
            session_id,
            episode_price,
            from,
            video_id_rank,
            coin_balance: myWallet.value?.gold_total ?? 0,
            balance: myWallet.value?.ticket_total ?? 0.0,
            currency: currencyList?.[0]?.currency_code ?? 'USD',
            exchange_product_id: activeExchange.value?.product_id,
            product_amount: currentExchangeTicket.value,
            product_price: currentExchangeNeedCoins.value,
          })
        } else {
          track('store', 'exchange_confirm_pop', 'show', {
            exchange_product_id: activeExchange.value?.product_id,
            product_amount: currentExchangeTicket.value,
            product_price: currentExchangeNeedCoins.value,
          })
        }
      }
    },
  })

  const purchase = (item: TopUp.RechargeProduct) => {
    if (isHalfScreen) {
      track('video', 'payment_product', 'click', {
        video_id: episode_key,
        series_id: series_key,
        session_id,
        episode_price,
        coin_balance: myWallet.value?.gold_total ?? 0,
        balance: myWallet.value?.ticket_total ?? 0.0,
        currency: currencyList?.[0]?.currency_code ?? 'USD',
        product_id: item.product_id,
        price: item.formatted_price,
        from,
        video_id_rank,
      })
    } else {
      track('store', 'payment_panel', 'click', {
        coin_balance: myWallet.value?.gold_total ?? 0,
        balance: myWallet.value?.ticket_total ?? 0.0,
        currency: currencyList?.[0]?.currency_code ?? 'USD',
        product_id: item.product_id,
        price: item.formatted_price,
      })
    }
    activePurchase.value = item
    void jsBridge<{ result: string }>('doPurchase', { ...item, half_screen: isHalfScreen, series_key, episode_key, session_id, episode_price, from, video_id_rank }).then(res => {
      if (res.result == 'success') {
        console.log('purchase success test === success')
        getMyWallet()
        getWalletStoreList()
      }
    })
  }

  const openExchangeDialog = (item: TopUp.ExchangeProduct) => {
    if (isHalfScreen) {
      track('video', 'payment_product', 'click', {
        video_id: episode_key,
        series_id: series_key,
        session_id,
        episode_price,
        coin_balance: myWallet.value?.gold_total ?? 0,
        balance: myWallet.value?.ticket_total ?? 0.0,
        currency: currencyList?.[0]?.currency_code ?? 'USD',
        exchange_product_id: item.product_id,
        price: item.price,
        from,
        video_id_rank,
      })
    } else {
      track('store', 'payment_panel', 'click', {
        coin_balance: myWallet.value?.gold_total ?? 0,
        balance: myWallet.value?.ticket_total ?? 0.0,
        currency: currencyList?.[0]?.currency_code ?? 'USD',
        exchange_product_id: item.product_id,
        price: item.price,
      })
    }
    activeExchange.value = item
    topUpY.value = 100
    dialogY.value = 0
  }

  const closeExchangeDialog = () => {
    activeExchange.value = null
    topUpY.value = 0
    dialogY.value = 100
  }

  const format = (type: 'ticket' | 'coin', balance?: number | string) => {
    if (!balance) return type === 'ticket' ? '0.0' : '0'
    if (typeof balance === 'string') return balance.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    const _balance = type === 'ticket' ? balance.toFixed(1) : balance.toFixed(0)
    return _balance.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  const closeTopUp = () => {
    if (dialogTargetY.value === 0) return
    void jsBridge('dismiss')
  }

  const renderExchangeList = () => {
    return exchangeList.value?.length > 0
      ? (
          <x-exchange-wrapper class="block w-full relative z-up-up">
            <x-exchange-title class={mc('block w-full text-base text-[var(--text-1)]', isHalfScreen ? 'py-2' : 'pt-6 pb-3')}>{t('top_up.exchange_title')}</x-exchange-title>
            <x-exchange-list class="flex gap-3 flex-wrap">
              {
                exchangeList.value.map(item => (
                  <x-exchange-item class={mc('block w-[calc(50%-6px)] shrink-0 bg-[var(--fill-1)] rounded-lg border border-solid border-[var(--line-1)] px-4 pt-[15px] pb-[10px]', activeExchange.value?.product_id === item.product_id && 'border-[var(--line-4)] bg-[var(--brand-1)]')} onClick={() => openExchangeDialog(item)}>
                    <x-ticket-can-exchange class="flex items-center gap-x-1">
                      <img src={ticket} class="size-5" />
                      <x-ticket-can-exchange-num class="flex items-center gap-x-1 leading-[22px] truncate">
                        <span class="block text-lg font-bold text-[var(--text-1)] flex-1 truncate">{item.delivery_details?.quanity}</span>
                        {item.delivery_details?.bonus && item.delivery_details?.bonus !== '0.0' && <x-ticket-can-exchange-bonus class="text-[14px] font-normal text-[var(--brand-6)]">+{item.delivery_details.bonus}</x-ticket-can-exchange-bonus>}
                      </x-ticket-can-exchange-num>
                    </x-ticket-can-exchange>
                    <x-ticket-cost-coin class="flex items-center gap-x-1">
                      <img src={coin} class="size-3.5 ml-1.5" />
                      <x-ticket-cost-coin-num class="text-sm text-[var(--text-2)] truncate">{item.price}</x-ticket-cost-coin-num>
                      {/* {item.has_discount === 1 && <x-ticket-cost-coin-discount class="text-sm text-[var(--text-2)] truncate">{item.discount_price}</x-ticket-cost-coin-discount>} */}
                    </x-ticket-cost-coin>
                  </x-exchange-item>
                ))
              }
            </x-exchange-list>
          </x-exchange-wrapper>
        )
      : null
  }

  const renderRechargeList = () => {
    return rechargeList.value?.length > 0
      ? (
          <x-purchase-wrapper class="block w-full relative z-up-up">
            <x-purchase-title class={mc('block w-full text-base text-[var(--text-1)]', isHalfScreen ? 'py-2' : 'pt-6 pb-3')}>{t('top_up.purchase_title')}</x-purchase-title>
            <x-purchase-list class="flex gap-3 flex-wrap">
              {
                rechargeList.value.map(item => (
                  <x-purchase-item class={mc('block w-[calc(50%-6px)] shrink-0 bg-[var(--fill-1)] rounded-lg border border-solid border-[var(--line-1)] px-4 pt-[15px] pb-[10px] no-tap-color', activePurchase.value?.product_id === item.product_id ? 'border-[var(--line-4)] bg-[var(--brand-1)]' : '')} onClick={() => purchase(item)}>
                    <x-ticket-can-purchase class="flex items-center gap-x-1">
                      <img src={ticket} class="size-5" />
                      <x-ticket-can-purchase-num class="flex items-center gap-x-1 leading-[22px] truncate">
                        <span class="block text-lg font-bold text-[var(--text-1)] flex-1 truncate">{item.delivery_details?.quanity}</span>
                        {item.delivery_details?.bonus && item.delivery_details?.bonus !== '0.0' && <x-ticket-can-purchase-bonus class="text-[14px] font-normal text-[var(--brand-6)]">+{item.delivery_details.bonus}</x-ticket-can-purchase-bonus>}
                      </x-ticket-can-purchase-num>
                    </x-ticket-can-purchase>
                    <x-ticket-cost-currency class="flex items-center gap-x-1">
                      <x-place-holder class="inline-block size-5" />
                      <x-ticket-cost-currency-num class="text-sm text-[var(--text-2)] truncate">{item.formatted_price}</x-ticket-cost-currency-num>
                      {/* {item.has_discount === 1 && <x-ticket-cost-currency-discount class="text-sm text-[var(--text-2)] truncate">{item.discount_price}</x-ticket-cost-currency-discount>} */}
                    </x-ticket-cost-currency>
                  </x-purchase-item>
                ))
              }
            </x-purchase-list>
          </x-purchase-wrapper>
        )
      : null
  }

  watch(() => [exchangeList.value, rechargeList.value], () => {
    console.log('exchangeList', exchangeList.value)
    console.log('rechargeList', rechargeList.value)
    if (exchangeList.value || rechargeList.value) {
      rechargeList.value?.forEach(item => {
        if (isHalfScreen) {
          track('video', 'payment_panel', 'show', {
            video_id: episode_key,
            series_id: series_key,
            session_id,
            episode_price,
            coin_balance: myWallet.value?.gold_total ?? 0,
            balance: myWallet.value?.ticket_total ?? 0.0,
            currency: currencyList?.[0]?.currency_code ?? 'USD',
            product_id: item.product_id,
            from,
            video_id_rank,
          })
        } else {
          track('store', 'payment_panel', 'show', {
            coin_balance: myWallet.value?.gold_total ?? 0,
            balance: myWallet.value?.ticket_total ?? 0.0,
            currency: currencyList?.[0]?.currency_code ?? 'USD',
            product_id: item.product_id,
          })
        }
      })
      exchangeList.value?.forEach(item => {
        if (isHalfScreen) {
          track('video', 'payment_panel', 'show', {
            video_id: episode_key,
            series_id: series_key,
            session_id,
            episode_price,
            coin_balance: myWallet.value?.gold_total ?? 0,
            balance: myWallet.value?.ticket_total ?? 0.0,
            currency: currencyList?.[0]?.currency_code ?? 'USD',
            exchange_product_id: item.product_id,
          })
        } else {
          track('store', 'payment_panel', 'show', {
            coin_balance: myWallet.value?.gold_total ?? 0,
            balance: myWallet.value?.ticket_total ?? 0.0,
            currency: currencyList?.[0]?.currency_code ?? 'USD',
            exchange_product_id: item.product_id,
          })
        }
      })
    }
  })

  onMounted(async () => {
    await getCurrency()
    getMyWallet()
    getWalletStoreList()
  })

  // activePurchase和activeExchange互斥
  watch(() => activePurchase.value, () => {
    if (activeExchange.value && activePurchase.value) {
      activeExchange.value = null
    }
  })
  // activePurchase和activeExchange互斥
  watch(() => activeExchange.value, () => {
    if (activePurchase.value && activeExchange.value) {
      activePurchase.value = null
    }
  })

  watch(() => scrollRef.value, () => {
    if (scrollRef.value) {
      useStretchScroll(scrollRef.value)
    }
  })
  const isUpdateFontSize = ref(false)
  watch(() => [balanceRef.value, ticketRef.value, isUpdateFontSize.value], () => {
    if (balanceRef.value && ticketRef.value) {
      isUpdateFontSize.value = false
      const balanceLineHeight = parseFloat(getComputedStyle(balanceRef.value).lineHeight)
      const ticketLineHeight = parseFloat(getComputedStyle(ticketRef.value).lineHeight)
      const balanceFontSize = getComputedStyle(balanceRef.value).fontSize
      const ticketFontSize = getComputedStyle(ticketRef.value).fontSize
      let fontSize
      if (balanceRef.value.scrollHeight > balanceLineHeight) {
        fontSize = balanceFontSize ? parseInt(balanceFontSize) - 2 + 'px' : isHalfScreen ? '10px' : '22px'
      }
      if (ticketRef.value.scrollHeight > ticketLineHeight) {
        fontSize = ticketFontSize ? parseInt(ticketFontSize) - 2 + 'px' : isHalfScreen ? '10px' : '22px'
      }
      if (fontSize) {
        isUpdateFontSize.value = true
        balanceRef.value.style.fontSize = fontSize
        ticketRef.value.style.fontSize = fontSize
      }
    }
  })

  const currentExchangeTicket = computed(() => {
    return parseFloat(activeExchange.value?.delivery_details?.quanity ?? '0') + parseFloat(activeExchange.value?.delivery_details?.bonus ?? '0')
  })

  const currentExchangeNeedCoins = computed(() => {
    // return activeExchange.value?.has_discount === 1 ? activeExchange.value?.discount_price ?? 0 : activeExchange.value?.price ?? 0
    return activeExchange.value?.price ?? 0
  })

  const exchange = (item: TopUp.ExchangeProduct | null) => {
    let exchangeSuccessToast
    if (currentExchangeTicket.value === 1) {
      // 处理单复数
      exchangeSuccessToast = t('top_up.exchange_success').replace('xxx', currentExchangeTicket.value + '').replace('Tickets', 'Ticket')
    } else {
      exchangeSuccessToast = t('top_up.exchange_success').replace('xxx', currentExchangeTicket.value + '')
    }
    console.log('step1: exchange', isHalfScreen)
    if (isHalfScreen) {
      track('video', 'exchange_confirm_pop', 'click', {
        video_id: episode_key,
        series_id: series_key,
        session_id: session_id,
        from,
        video_id_rank,
        exchange_product_id: item?.product_id,
        product_amount: currentExchangeTicket.value,
        product_price: currentExchangeNeedCoins.value,
      })
    } else {
      track('store', 'exchange_confirm_pop', 'click', {
        exchange_product_id: item?.product_id,
        product_amount: currentExchangeTicket.value,
        product_price: currentExchangeNeedCoins.value,
      })
    }
    reportNow()
    if (!item) return
    void when(() => authInfo && deviceInfo).then(async () => {
      try {
        const res = await bindLoading(apiExchange({
          product_id: item.product_id,
          r_info: item.r_info ?? '',
          series_key,
          episode_key,
          appsflyer_id: deviceInfo?.appsflyer_id ?? '',
        }), exchangeLoading)
        console.log('step2: exchange', res)
        if (res.code === 200) {
          if (isHalfScreen) {
            // 客户端埋点，因为将要关闭webview，所以需要立即上报
            void jsBridge('track', {
              event: 'video_exchange_successful',
              upload_now: true,
              event_info: {
                origin: 'video',
                entity: 'exchange',
                event: 'successful',
                video_id: episode_key,
                series_id: series_key,
                session_id: session_id,
                episode_price,
                from,
                video_id_rank,
                order_id: res.data?.order_id,
                exchange_product_id: activeExchange.value?.product_id,
                timestamp: dayjs().format('YYYY/MM/DD HH/mm/ss'),
              },
            })
          } else {
            track('store', 'exchange', 'successful', {
              order_id: res.data?.order_id,
              exchange_product_id: activeExchange.value?.product_id,
              timestamp: dayjs().format('YYYY/MM/DD HH/mm/ss'),
            })
            reportNow()
          }
          getMyWallet()
          closeExchangeDialog()
          void jsBridge('toast', {
            toast: exchangeSuccessToast,
            action: 'exchange_success',
            series_key,
            episode_key,
            session_id: session_id,
            episode_price,
            order_id: res.data?.order_id,
            exchange_product_id: activeExchange.value?.product_id,
            timestamp: dayjs().format('YYYY/MM/DD HH/mm/ss'),
            half_screen: isHalfScreen,
          })
        }
      } catch (e: unknown) {
        // @ts-expect-error never mind
        if (e.response.data.code === 1000) {
          await jsBridge('toast', {
            toast: t('top_up.balance_not_enough'),
            action: 'exchange_fail',
            series_key,
            episode_key,
            half_screen: isHalfScreen,
          })
        } else {
          await jsBridge('toast', {
            toast: t('top_up.exchange_failed'),
            action: 'exchange_fail',
            series_key,
            episode_key,
            half_screen: isHalfScreen,
          })
        }
      }
    })
  }

  return () => (
    <div class="size-full relative overflow-hidden">
      {
        isHalfScreen
          ? (
              <>
                <div class="absolute size-full bg-[#090609]/50 z-up" onClick={closeTopUp} />
                <div class={mc('absolute bottom-0 left-0 w-full px-3 h-auto min-h-40 max-h-80vh bg-white rounded-t-xl overflow-hidden z-up-up', topUpTargetY.value === 100 ? 'invisible' : 'visible')}
                  style={{
                    transform: `translateY(${topUpTargetY.value}%)`,
                  }}
                >
                  <img src={background} class="w-full h-auto object-cover absolute top-0 left-0 z-up" />
                  <x-account-balance class="absolute w-[calc(100%-1.5rem)] z-1000 top-5 left-3 flex">
                    <div class="overflow-hidden w-[calc(100%-24px)] font-normal text-[var(--text-2)] flex items-center pb-3 gap-x-1">
                      <span>{t('top_up.balance')}:</span>
                      <div class="flex-1 flex items-center justify-start gap-x-1">
                        <img src={ticket} class="size-4" />
                        <span ref={ticketRef} class="text-sm break-all">{format('ticket', myWallet.value?.ticket_total ?? '0.0')}</span>
                        {
                          myWallet.value?.show_coins && (
                            <>
                              <x-separator class="w-4 h-2 flex items-center justify-center shrink-0">
                                <div class="w-0 h-full border-l border-l-solid border-l-[var(--line-2)]" />
                              </x-separator>
                              <img src={coin} class="size-4" />
                              <span ref={balanceRef} class="text-sm break-all">{format('coin', myWallet.value?.gold_total ?? 0)}</span>
                            </>
                          )
                        }
                      </div>
                    </div>
                    <img src={close} class="size-5 -mt-1 mr-1" onClick={closeTopUp} />
                  </x-account-balance>

                  <x-scroll-view-port class="block relative mt-13 w-full h-[calc(100%-3.25rem)] z-up-up overflow-hidden">
                    {
                      loading.value
                        ? <Loading />
                        : (
                            <x-scroll-wrapper class="block relative z-up-up share-bottom-5" ref={scrollRef}>
                              {
                                myWallet.value?.show_coins && renderExchangeList()
                              }
                              {
                                renderRechargeList()
                              }
                            </x-scroll-wrapper>
                          )
                    }
                  </x-scroll-view-port>
                </div>
                {
                  myWallet.value?.show_coins
                  && (
                    <x-exchange-dialog class={mc('absolute translate-y-full bottom-0 left-0 w-full z-up-up pt-[30px] px-8 bg-white rounded-t-xl overflow-hidden flex flex-col gap-y-5 text-[var(--text-1)] share-bottom-5', dialogTargetY.value === 100 ? 'invisible' : 'visible')}
                      style={{
                        transform: `translateY(${dialogTargetY.value}%)`,
                      }}
                    >
                      <x-exchange-title class="block w-full text-2xl font-bold  text-center">{t('top_up.exchange_dialog_title')}</x-exchange-title>
                      <x-ticket-can-redeem class="flex justify-between w-full text-base font-normal gap-x-3">
                        <div class="flex-1 text-wrap break-words">{t('top_up.tickets')}</div>
                        <div class="flex items-center justify-end gap-x-1 w-35 shrink-0">
                          <span class="text-wrap font-bold text-[var(--brand-6)] break-all text-left">{currentExchangeTicket.value}</span>
                          <img src={ticket} class="size-[18px] self-center" />
                        </div>
                      </x-ticket-can-redeem>
                      <x-redeem-need-coins class="flex justify-between w-full text-base font-normal gap-x-3">
                        <div>{t('top_up.redeem_required')}</div>
                        <div class="flex items-center justify-end gap-x-1 w-35 shrink-0">
                          <span class="text-wrap break-all text-left line-clamp-2">{currentExchangeNeedCoins.value}</span>
                          <img src={coin} class="size-[18px] self-center" />
                        </div>
                      </x-redeem-need-coins>
                      <Button class="w-full h-11 mt-[10px] no-tap-color border-none bg-[var(--brand-6)] text-white rounded-lg" disabled={exchangeLoading.value} onClick={() => exchange(activeExchange.value)}>{t('coins.earnings.exchange')}</Button>
                      <div class="w-full -mt-1 text-center text-base font-normal text-[var(--text-2)]" onClick={closeExchangeDialog}>{t('common.cancel')}</div>
                    </x-exchange-dialog>
                  )
                }
              </>
            )
          : (
              <x-wrapper class="flex flex-col size-full relative overflow-hidden">
                <Back title={t('top_up.title')} class="w-full bg-white shrink-0" />
                <div class="size-full flex flex-col items-center justify-start px-3 pt-3 overflow-y-auto share-bottom-5 bg-white">
                  <x-balance class="w-full h-24 shrink-0 flex justify-center items-center relative rounded-lg">
                    <img src={balanceBg} class="w-full h-full object-cover absolute top-0 left-0 z-up rounded-lg" />
                    <div class="w-full px-4 h-full flex items-center justify-between relative z-up-up">
                      <x-tickets class="flex flex-1 flex-col items-center justify-center">
                        <div ref={ticketRef} class="text-2xl font-bold text-[var(--text-1)] break-all">{format('ticket', myWallet.value?.ticket_total ?? 0)}</div>
                        <div class="text-xs font-normal text-[var(--text-3)]">{t('top_up.tickets')}</div>
                      </x-tickets>
                      {
                        myWallet.value?.show_coins && (
                          <>
                            <x-separator class="w-15 h-[35px] flex items-center justify-center shrink-0">
                              <div class="w-0 h-full border-l border-l-solid border-l-[var(--line-2)]" />
                            </x-separator>
                            <x-coins class="flex w-[calc(50%-30px)] flex-col items-center justify-center">
                              <div ref={balanceRef} class="text-2xl font-bold text-[var(--text-1)] break-all">{format('coin', myWallet.value?.gold_total ?? 0)}</div>
                              <div class="text-xs font-normal text-[var(--text-3)]">{t('top_up.coins')}</div>
                            </x-coins>
                          </>
                        )
                      }
                    </div>
                  </x-balance>
                  {
                    myWallet.value?.show_coins && renderExchangeList()
                  }
                  {
                    renderRechargeList()
                  }
                  <x-tips class="flex w-full mt-4 p-3 bg-[var(--fill-1)] rounded-lg flex-col items-start gap-y-2 justify-center text-sm text-left text-[var(--text-4)]">
                    <x-tips-title class="block w-full text-[var(--text-2)]">{t('top_up.tips')}:</x-tips-title>
                    <p>{t('top_up.tips_1')}</p>
                    <p>{t('top_up.tips_2')}</p>
                    <p>{t('top_up.tips_3')}</p>
                    <p>{t('top_up.tips_4')}</p>
                    <p>{t('top_up.tips_5')}</p>
                    <p>{t('top_up.tips_6')}</p>
                  </x-tips>

                  {dialogTargetY.value !== 100 && <div class="fixed size-screen bg-[#090609]/50 z-99" />}
                  {
                    myWallet.value?.show_coins && (
                      <x-exchange-dialog class="absolute translate-y-full bottom-0 left-0 w-full z-100 pt-[30px] px-8 bg-white rounded-t-xl overflow-hidden flex flex-col gap-y-5 text-[var(--text-1)] share-bottom-5"
                        style={{
                          transform: `translateY(${dialogTargetY.value}%)`,
                        }}
                      >
                        <x-exchange-title class="block w-full text-2xl font-bold  text-center">{t('top_up.exchange_dialog_title')}</x-exchange-title>
                        <x-ticket-can-redeem class="flex justify-between w-full text-base font-normal gap-x-3">
                          <div class="flex-1 text-wrap break-words">{t('top_up.tickets')}</div>
                          <div class="flex items-center justify-end gap-x-1 w-35 shrink-0">
                            <span class="text-wrap font-bold text-[var(--brand-6)] break-all text-left">{currentExchangeTicket.value}</span>
                            <img src={ticket} class="size-[18px] self-center" />
                          </div>
                        </x-ticket-can-redeem>
                        <x-redeem-need-coins class="flex justify-between w-full text-base font-normal gap-x-3">
                          <div>{t('top_up.redeem_required')}</div>
                          <div class="flex items-center justify-end gap-x-1 w-35 shrink-0">
                            <span class="text-wrap break-all text-left line-clamp-2">{currentExchangeNeedCoins.value}</span>
                            <img src={coin} class="size-[18px] self-center" />
                          </div>
                        </x-redeem-need-coins>
                        <Button class="w-full h-11 mt-[10px] no-tap-color border-none bg-[var(--brand-6)] text-white rounded-lg" disabled={exchangeLoading.value} onClick={() => exchange(activeExchange.value)}>{t('coins.earnings.exchange')}</Button>
                        <div class="w-full -mt-1 text-center text-base font-normal text-[var(--text-2)]" onClick={closeExchangeDialog}>{t('common.cancel')}</div>
                      </x-exchange-dialog>
                    )
                  }
                </div>
              </x-wrapper>
            )
      }
    </div>
  )
})

export default TopUpPage
