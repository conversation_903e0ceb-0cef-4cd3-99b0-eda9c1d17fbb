#!/bin/bash
set -e
export LANG=en_US.UTF-8

MODE=$1

# define vars
TEST_SERVERS=(na-test-dramato-server-1)
PROD_SERVERS=(sg-prod-dramato-nodeserver-1 sg-prod-dramato-nodeserver-2 na-prod-dramato-nodeserver-1 na-prod-dramato-nodeserver-2)
TEST_DIR="/home/<USER>/node-server"
PROD_DIR="/home/<USER>/node-server"

# check params
if [[ $# != 1 ]]; then
  echo "Usage: $0 [env:test|prod]"
  exit 0
fi

COMMIT_HASH=$(git log -1 --pretty=format:'%h')
# get base path
CURR_DIR=$(dirname "$0")
OUTPUT_DIR="$CURR_DIR/output"

# parse parameters
ENV_NAME=$(echo "$1" | tr '[:upper:]' '[:lower:]')

if [[ "$ENV_NAME" == "prod" ]]; then
  SERVERS=("${PROD_SERVERS[@]}")
  DIR=$PROD_DIR
else
  SERVERS=("${TEST_SERVERS[@]}")
  DIR=$TEST_DIR
fi

pwd
ls -la

for SERVER in "${SERVERS[@]}"; do
  echo "--------------------------------"
  echo "Deploying to $SERVER"
  echo "--------------------------------"
  ssh worker@$SERVER "mkdir -p $DIR/output/"
  rsync -az --delete $OUTPUT_DIR/ worker@$SERVER:$DIR/output/

  ssh worker@$SERVER "docker pull hub.tianmai.cn/dramato-node-server/${MODE}-server-dramato:$COMMIT_HASH"
  ssh worker@$SERVER "docker rm server-dramato-backup || echo no container 'server-dramato-backup' to remove"
  ssh worker@$SERVER "docker stop server-dramato && docker rename server-dramato server-dramato-backup || echo no container 'server-dramato' to stop"
  ssh worker@$SERVER "docker run --restart=always -dit --name server-dramato --network host hub.tianmai.cn/dramato-node-server/${MODE}-server-dramato:$COMMIT_HASH"
  if [[ "$SERVER" == "${SERVERS[0]}" ]]; then
    ssh worker@$SERVER "ossutil sync -f $DIR/output/public/ oss://us-dramato-prod/frontend_server/dramato_${MODE}"
  fi
done

set +e
echo 'success'
