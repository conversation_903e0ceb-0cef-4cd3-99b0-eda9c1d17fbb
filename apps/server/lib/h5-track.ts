import { report } from '@skynet/client-track'
import { getUser } from '~/modules/common/user'
import { useLocale } from '~/modules/i18n/i18n'
import { get_k_device_hash_from_cookie } from '~/lib/device-id.ts'

/**
 * H5 埋点
 * @example h5Track('home', 'page', 'show', {id: 'xxx'})
 */
export const h5Track = (origin: string, entity: string, event: string, extraEventInfo: object = {}, extra: object = {}) => {
  const user = getUser()
  report({
    event: `${origin}_${entity}_${event}`,
    device_hash: get_k_device_hash_from_cookie() ?? 'unknown',
    app_version: '',
    user_id: `${user.user_id ?? 'unknown'}`,
    user_source: 'H5',
    client_country: useLocale().currentLocale.value.country,
    ...extra,
    event_info: JSON.stringify({
      origin,
      entity,
      event,
      event_type: event,
      client_language: navigator.language,
      screen_height: window.screen.height,
      screen_width: window.screen.width,
      time: new Date().getTime(),
      user_name: user.name ?? 'unknown',
      ...extraEventInfo,
    }),
  })
}
/* 广告系列相关业务埋点 */
export const h5AdTrack = (origin: string, entity: string, event: string, extra?: object) => {
  const user = getUser()
  report({
    event,
    device_hash: get_k_device_hash_from_cookie() ?? 'unknown',
    app_version: '',
    user_id: `${user.user_id ?? 'unknown'}`,
    user_source: 'H5',
    client_country: useLocale().currentLocale.value.country,
    event_info: JSON.stringify({
      origin,
      entity,
      event,
      event_type: '',
      client_language: navigator.language,
      screen_height: window.screen.height,
      screen_width: window.screen.width,
      time: new Date().getTime(),
      user_name: user.name ?? 'unknown',
      ...extra,
    }),
  })
}
