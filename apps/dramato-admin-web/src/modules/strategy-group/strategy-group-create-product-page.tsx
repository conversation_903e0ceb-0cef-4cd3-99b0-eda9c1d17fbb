/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Button, CheckboxGroup, CreateTableOld, DialogFooter, Money, openDialog, RadioGroup, showFailToast, TableColumnOld } from '@skynet/ui'
import { set } from 'lodash-es'
import { memberAttr } from 'src/lib/constant'
import { requiredLabel } from 'src/lib/required-label'
import { useStrategyGroupStore } from 'src/modules/strategy-group/strategy-group-store'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { MemberPage } from '../member/member-page'
import RechargeItemPage from '../recharge-item-management/recharge-item-page'
import RechargeLevelPage from '../recharge-level/recharge-level-page'
import { apiBatchGetProduct, apiSaveStrategyGroup } from './strategy-group-api'

type StrategyGroupCreateProductPageOptions = {
  props: {}
}

export const StrategyGroupCreateProductPage = createComponent<StrategyGroupCreateProductPageOptions>({
  props: {},
}, props => {
  // const { formData, Form, stepTwoError, validateStepTwo, resetTaskFormData } = useStrategyGroupStore()
  const { formData, Form, resetTaskFormData, stepThreeError, validateAllSteps, stepOneError, stepTwoError, needShowSaveDragDialog } = useStrategyGroupStore()

  const curTab = ref([0, 1].includes(+formData.value.user_platform) ? 'ios_config' : 'android_config')

  const router = useRouter()
  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')

  const LevelOptionTable = CreateTableOld<M.NSStrategyGroup.LevelOption>()
  const checkedRechargeItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const checkedRechargeItemItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const checkedMemberItem = ref<M.NSStrategyGroup.LevelOption[]>([])

  const rechargeColumns: TableColumnOld<M.NSStrategyGroup.LevelOption>[] = [
    ['档位顺序', (row, idx) => (
      <label class={mc('h-8')}>
        <select
          class="select select-bordered select-sm "
          value={row.priority}
          disabled={isViewMode.value}
          onInput={(e: Event) => {
            const priority = Number((e.target as HTMLInputElement).value) || 0
            if (!formData.value.product_config) {
              return
            }
            formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].priority = priority
          }}
        >
          {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((option: number) => (
            <option value={option}>{option}</option>
          ))}
        </select>
      </label>
    ), { class: 'w-[100px]' }],
    ['档位ID', 'id', { class: 'w-[120px]' }],
    ['展示赠送%', (row, idx) => (
      <RadioGroup
        modelValue={row?.show_bonus}
        onUpdate:modelValue={v => {
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].show_bonus = v as number
        }}
        options={[
          { label: '是', value: 1, disabled: isViewMode.value },
          { label: '否', value: 0, disabled: isViewMode.value },
        ]}
      />
    ), { class: 'w-[200px]' }],
    ['自定义输入', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[100px] items-center gap-1"
        value={row?.slogan}
        disabled
        onInput={e => {
          const slogan = (e.target as HTMLInputElement).value || ''
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].slogan = slogan
        }}
      />
    ), { class: 'w-[120px]' }],
    ['档位名称', 'title', { class: 'w-[200px]' }],
    ['类型', row => row?.first_recharge ? (row.first_recharge === 1 ? '首充档位' : '再充值') : '常规档位', { class: 'w-[200px]' }],
    ['档位描述', (row, idx) => (
      <CheckboxGroup
        modelValue={row.props || []}
        onUpdate:modelValue={v => {
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option.forEach(u => u.props = [])
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option[idx].props = (v || []) as string[]
        }}
        options={memberAttr.map(i => ({ ...i, disabled: isViewMode.value }))}
      />
    ), { class: 'w-[200px]' }],
    ['现价/元', row => <Money modalValue={row.discount_price} />, { class: 'w-[100px]' }],
    ['售卖金币数', row => row?.delivery_details?.quanity || 0, { class: 'w-[100px]' }],
    ['赠送金币数', row => row?.delivery_details?.bonus || 0, { class: 'w-[100px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              showImportRechargeLevelDialog(row.id, row.priority)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (!formData.value.product_config) {
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option.splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  const packItemTypeList = [
    { label: '解锁礼包', value: 1 },
    { label: '整剧打包解锁', value: 5 },
  ]
  const rechargeItemColumns: TableColumnOld<M.NSStrategyGroup.LevelOption>[] = [
    ['道具ID', 'id', { class: 'w-[70px]' }],
    ['道具说明', 'description', { class: 'w-[200px]' }],
    ['类型', row => {
      return packItemTypeList.find(item => item.value === row.package_type)?.label
    }, { class: 'w-[160px]' }],
    ['解锁集数', row => {
      return row.package_type == 1 ? row.unlock_num : '全剧'
    }, { class: 'w-[100px]' }],
    ['折扣', row => {
      return row.discount_rate + '%'
    }, { class: 'w-[100px]' }],
    ['默认充值档位（档位ID/价格/金币+赠送）', row => {
      return row.package_type == 1 ? row.default_product && row.default_product.id + ' / $' + (row.default_product.price / 100).toFixed(2) + ' / ' + row.default_product.delivery_details.quanity + ' + ' + (row.default_product.delivery_details.bonus || 0) : row.product_items && row.product_items[0].id + ' / $' + (row.product_items[0].price / 100).toFixed(2) + ' / ' + row.product_items[0].delivery_details.quanity + ' + ' + (row.product_items[0].delivery_details.bonus || 0)
    }, { class: 'w-[230px]' }],
    ['更多充值档位（数）', row => {
      return row.package_type == 1 ? row.product_ids?.length : row.episode_products!.length
    }, { class: 'w-[160px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              showImportRechargeItemDialog(row.id, row.priority)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (!formData.value.product_config) {
                return
              }
              if (isArray(formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option)) {
                formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option.splice(idx, 1)
              } else {
                formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option = []
              }
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  const memberColumns: TableColumnOld<M.NSStrategyGroup.LevelOption>[] = [
    ['档位顺序', (row, idx) => (
      <label class={mc('h-8')}>
        <select
          class="select select-bordered select-sm "
          value={row.priority}
          disabled={isViewMode.value}
          onInput={(e: Event) => {
            const priority = Number((e.target as HTMLInputElement).value) || 0
            if (!formData.value.product_config) {
              return
            }
            formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].priority = priority
          }}
        >
          {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((option: number) => (
            <option value={option}>{option}</option>
          ))}
        </select>
      </label>
    ), { class: 'w-[100px]' }],
    ['档位ID', 'id', { class: 'w-[120px]' }],
    ['档位名称', 'title', { class: 'w-[200px]' }],
    ['档位描述', (row, idx) => (
      <CheckboxGroup
        modelValue={row.props || []}
        onUpdate:modelValue={v => {
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option.forEach(u => u.props = [])
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].props = (v || []) as string[]
        }}
        options={memberAttr.map(i => ({ ...i, disabled: isViewMode.value }))}
      />
    ), { class: 'w-[200px]' }],
    ['类型', row => row?.first_recharge ? (row.first_recharge === 1 ? '首充' : '再订阅') : '常规档位', { class: 'w-[200px]' }],
    ['提示文案', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[300px] items-center gap-1"
        value={row?.tips}
        disabled
        onInput={e => {
          const tips = (e.target as HTMLInputElement).value || ''
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].tips = tips
        }}
      />
    ), { class: 'w-[320px]' }],
    ['角标文案', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[300px] items-center gap-1"
        value={row?.slogan}
        disabled
        onInput={e => {
          const slogan = (e.target as HTMLInputElement).value || ''
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].slogan = slogan
        }}
      />
    ), { class: 'w-[320px]' }],
    ['权益说明', (row, idx) => (
      <input
        class="input input-bordered flex h-8 w-[300px] items-center gap-1"
        value={row?.description}
        disabled
        onInput={e => {
          const description = (e.target as HTMLInputElement).value || ''
          if (!formData.value.product_config) {
            return
          }
          formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option[idx].description = description
        }}
      />
    ), { class: 'w-[320px]' }],
    ['原价/元', row => <Money modalValue={row.price} />, { class: 'w-[100px]' }],
    ['现价/元', row => <Money modalValue={row.discount_price} />, { class: 'w-[100px]' }],
    // ['售卖金币数', row => row?.delivery_details?.quanity || 0, { class: 'w-[100px]' }],
    // ['赠送金币数', row => row?.delivery_details?.bonus || 0, { class: 'w-[100px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap gap-x-2">
          <Button
            class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              showImportMemberLevelDialog(row.id, row.priority)
            }}
          >
            更新
          </Button>
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (!formData.value.product_config) {
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option.splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[160px]' },
    ],
  ]

  const showImportRechargeItemDialog = (curr_row_id?: number, priority?: number) => {
    const itemPckOpt = formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option
    if (!itemPckOpt) {
      checkedRechargeItemItem.value = []
    }
    if (!curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option) {
      if (isArray(itemPckOpt)) {
        checkedRechargeItemItem.value = itemPckOpt
      } else {
        checkedRechargeItemItem.value = [itemPckOpt]
      }
    }
    const hide = openDialog({
      title: '导入充值道具方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-recharge-level class="relative">
          <RechargeItemPage
            hasNav={false}
            hasActions={false}
            hasCheckItem
            hasPriority={false}
            appIdSelectDisabled
            platform={curTab.value === 'ios_config' ? 1 : 2}
            checkedItem={checkedRechargeItemItem.value}
            onAdd={item => {
              if (curr_row_id) {
                checkedRechargeItemItem.value = []
              }
              if (checkedRechargeItemItem.value?.length > 1) {
                showFailToast('仅支持配置1个道具方案')
                return
              }
              checkedRechargeItemItem.value?.push({ ...item, product_id: item?.id?.toString() } as M.NSStrategyGroup.LevelOption)
            }}
            onRemove={item => {
              checkedRechargeItemItem.value = checkedRechargeItemItem.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.product_config) return
              if (checkedRechargeItemItem.value?.length > 1) {
                showFailToast('仅支持配置1个道具方案')
                return
              }
              if (curr_row_id) {
                const list = formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option || []
                if (isArray(list)) {
                  const index = list.findIndex(i => i.id === curr_row_id)
                  if (index === -1) {
                    return
                  }
                  (formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option || [])[index] = { ...JSON.parse(JSON.stringify(checkedRechargeItemItem.value[0])) }
                } else {
                  formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option = [{ ...JSON.parse(JSON.stringify(checkedRechargeItemItem.value[0])) }]
                }
                hide()
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option = [
                ...(JSON.parse(JSON.stringify(checkedRechargeItemItem.value)) || []),
              ]
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-800px',
      beforeClose: () => {
        checkedRechargeItemItem.value = []
      },
    })
  }

  const showImportRechargeLevelDialog = (curr_row_id?: number, priority?: number) => {
    if (!curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option) {
      checkedRechargeItem.value = [...formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option]
    }
    if (curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option) {
      checkedRechargeItem.value = formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option.filter(item => item.id === curr_row_id)
    }
    const hide = openDialog({
      title: '导入充值档位方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-recharge-level class="relative">
          <RechargeLevelPage
            hasNav={false}
            hasActions={false}
            hasCheckItem
            hasPriority={false}
            appIdSelectDisabled
            platform={curTab.value === 'ios_config' ? 1 : 2}
            checkedItem={checkedRechargeItem.value}
            onAdd={item => {
              if (curr_row_id) {
                checkedRechargeItem.value = []
              }
              checkedRechargeItem.value?.push({ ...item, product_id: item?.id?.toString() } as M.NSStrategyGroup.LevelOption)
            }}
            onRemove={item => {
              checkedRechargeItem.value = checkedRechargeItem.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.product_config) return
              if (curr_row_id) {
                const list = formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option || []
                const index = list.findIndex(i => i.id === curr_row_id)
                if (index === -1) {
                  return
                }
                (formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option || [])[index] = { ...checkedRechargeItem.value[0], priority }
                hide()
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option = [
                ...(checkedRechargeItem.value || []),
              ]
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-800px',
      beforeClose: () => {
        checkedRechargeItem.value = []
      },
    })
  }

  const showImportMemberLevelDialog = (curr_row_id?: number, priority?: number) => {
    if (formData.value.product_config && formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option) {
      checkedMemberItem.value = [...formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option]
    }
    if (curr_row_id && formData.value.product_config && formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option) {
      checkedMemberItem.value = formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option.filter(item => item.id === curr_row_id)
    }
    const hide = openDialog({
      title: '导入订阅方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-member-level>
          <MemberPage
            hasNav={false}
            hasCheckItem
            hasActions={false}
            hasPriority={false}
            appIdSelectDisabled
            platform={curTab.value === 'ios_config' ? 1 : 2}
            checkedItem={checkedMemberItem.value as any}
            onAdd={item => {
              if (curr_row_id) {
                checkedMemberItem.value = []
              }
              checkedMemberItem.value?.push({ ...item, product_id: item?.id?.toString(), props: [] } as M.NSStrategyGroup.LevelOption)
            }}
            onRemove={item => {
              checkedMemberItem.value = checkedMemberItem.value?.filter(i => i.id !== item.id)
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.product_config) {
                return
              }
              if (curr_row_id) {
                const list = formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option || []
                const index = list.findIndex(i => i.id === curr_row_id)
                if (index === -1) {
                  return
                }
                (formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option || [])[index] = { ...checkedMemberItem.value[0], priority }
                hide()
                return
              }
              formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option = [
                ...(checkedMemberItem.value || []),
              ]
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-member-level>

      ),
      customClass: '!w-800px',
      beforeClose: () => {
        checkedMemberItem.value = []
      },
    })
  }

  const currentRechargeItemList = computed(() => {
    if (!formData.value.product_config) {
      checkedRechargeItemItem.value = []
      return []
    }

    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab]) {
      checkedRechargeItemItem.value = []
      return []
    }
    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option) {
      checkedRechargeItemItem.value = []
      return []
    }
    if (isArray(formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option)) {
      checkedRechargeItemItem.value = formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option
      return formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option
    } else {
      checkedRechargeItemItem.value = [{ ...JSON.parse(JSON.stringify(formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option)) }]
      return [{ ...JSON.parse(JSON.stringify(formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].item_package_option)) }]
    }
  })

  const currentRechargeList = computed(() => {
    if (!formData.value.product_config) {
      return []
    }

    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab]) {
      return []
    }
    return formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].recharge_option.sort((a, b) => (a.priority || 0) - (b.priority || 0))
  })

  const currentMemberList = computed(() => {
    if (!formData.value.product_config) {
      return []
    }

    if (!formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab]) {
      return []
    }
    return formData.value.product_config[curTab.value as M.NSStrategyGroup.Tab].member_vip_option.sort((a, b) => (a.priority || 0) - (b.priority || 0))
  })

  const refreshLoading = ref(false)
  const iosMemberIds = computed(() => formData.value.product_config?.ios_config?.member_vip_option?.map(item => item.id).filter(a => a !== undefined) || [])
  const androidMemberIds = computed(() => formData.value.product_config?.android_config?.member_vip_option?.map(item => item.id).filter(a => a !== undefined) || [])
  const iosRechargeIds = computed(() => formData.value.product_config?.ios_config?.recharge_option?.map(item => item.id).filter(a => a !== undefined) || [])
  const androidRechargeIds = computed(() => formData.value.product_config?.android_config?.recharge_option?.map(item => item.id).filter(a => a !== undefined) || [])

  const updateProductItem = (item: M.RechargeLevel, config: 'ios_config' | 'android_config', type: 'recharge_option' | 'member_vip_option') => {
    if (!formData.value.product_config) return
    const targetIndex = formData.value.product_config[config][type].findIndex((i: any) => i.id === item.id)
    if (targetIndex > -1) {
      formData.value.product_config[config][type][targetIndex] = {
        priority: formData.value.product_config[config][type][targetIndex].priority,
        title: item.title,
        discount_price: item.discount_price,
        delivery_details: item.delivery_details,
        currency: item.currency,
        price: item.price!,
        first_recharge: item.first_recharge!,
        product_id: item.id + '',
        id: item.id,
      }
    }
  }
  const onRefresh = () => {
    if (refreshLoading.value) return
    refreshLoading.value = true
    void apiBatchGetProduct([...iosRechargeIds.value, ...iosMemberIds.value, ...androidRechargeIds.value, ...androidMemberIds.value]).then(response => {
      if (!response.data) return
      response.data.items.forEach(item => {
        if (item.id && iosRechargeIds.value.includes(item.id)) {
          updateProductItem(item, 'ios_config', 'recharge_option')
        }
        if (item.id && iosMemberIds.value.includes(item.id)) {
          updateProductItem(item, 'ios_config', 'member_vip_option')
        }
        if (item.id && androidRechargeIds.value.includes(item.id)) {
          updateProductItem(item, 'android_config', 'recharge_option')
        }
        if (item.id && androidMemberIds.value.includes(item.id)) {
          updateProductItem(item, 'android_config', 'member_vip_option')
        }
      })
    }).finally(() => {
      refreshLoading.value = false
    })
  }

  // 判断是否为数组
  const isArray = (value: unknown): value is unknown[] => {
    return Array.isArray(value)
  }

  // 判断是否为对象
  const isObject = (value: unknown): value is Record<string, unknown> => {
    return value !== null && typeof value === 'object' && !Array.isArray(value)
  }

  const onSubmit = async () => {
    if (!validateAllSteps()) {
      openDialog({
        title: '出错提示',
        body: (
          <div>请检查表单<br />
            {JSON.stringify(stepOneError.value)}
            {JSON.stringify(stepTwoError.value)}
            {JSON.stringify(stepThreeError.value)}
          </div>
        ),
      })
      return
    }
    if (formData.value.user_config?.active_users_config?.start_days && formData.value.user_config?.active_users_config?.end_days) {
      if (formData.value.user_config?.active_users_config?.start_days > formData.value.user_config?.active_users_config?.end_days) {
        // 交换起止时间
        const temp = formData.value.user_config.active_users_config.start_days
        formData.value.user_config.active_users_config.start_days = formData.value.user_config.active_users_config.end_days
        formData.value.user_config.active_users_config.end_days = temp
      }
    }
    let response = {}
    const newFromData = JSON.parse(JSON.stringify(formData.value))
    if (newFromData.product_config) {
      Object.keys(newFromData.product_config).forEach(key => {
        // 判断数组还是对象
        if (isObject(newFromData.product_config[key].item_package_option)) {
          newFromData.product_config[key].item_package_option = { id: newFromData.product_config[key].item_package_option.id }
        } else if (isArray(newFromData.product_config[key].item_package_option) && newFromData.product_config[key].item_package_option.length > 0) {
          newFromData.product_config[key].item_package_option = { id: newFromData.product_config[key].item_package_option[0].id }
        } else {
          newFromData.product_config[key].item_package_option = null
        }
      })
    }
    try {
      response = await apiSaveStrategyGroup({
        ...newFromData,
        save_type: 1,
      })
    } catch (error) {
    }
    if (!response.data) return
    needShowSaveDragDialog.value = false
    formData.value = response.data
    setTimeout(() => {
      void router.push({ path: route.path, query: { ...route.query, has_edit: 'false' } }).then(() => {
        const closeDialog = openDialog({
          title: '提示',
          body: (
            <div>保存成功
              <DialogFooter okText="返回列表页" onOk={() => {
                closeDialog()
                void router.push('/strategy-group')
              }} cancelText="留在当前页" onCancel={() => closeDialog()}
              />
            </div>
          ),
        })
      })
    })
  }

  return () => (
    <div class="flex flex-1 flex-col overflow-hidden p-8">
      {/* <Form
        class="flex flex-col gap-0"
        data={formData.value}
        error={stepTwoError.value}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        hasAction={false}
        items={[
          [requiredLabel('承接模式/支付面板'), 'panel_type', {
            type: 'radio',
            options: [
              { label: 'IAP优先', value: 0, disabled: isViewMode.value },
              { label: 'IAA优先', value: 1, disabled: isViewMode.value },
              { label: 'IAP优先（三合一面板）', value: 2, disabled: isViewMode.value },
              { label: 'IAA优先（三合一面板）', value: 3, disabled: isViewMode.value },
            ],
            disabled: isViewMode.value,
          }],
          (formData.value.panel_type === 2 || formData.value.panel_type === 3)
            ? [
                '支付面板个性化',
                'panel_config.custom_panel_config',
                {
                  type: 'checkbox-group',
                  options: [
                    { label: '订阅区展示', value: 'show_membership', disabled: isViewMode.value },
                    { label: '充值区展示', value: 'show_recharge', disabled: isViewMode.value },
                    { label: '激励广告展示', value: 'show_ad', disabled: isViewMode.value },
                  ],
                  disabled: isViewMode.value,
                },
              ]
            : [],
          (formData.value.panel_config?.custom_panel_config?.find(v => v === 'show_ad') && (formData.value.panel_type === 2 || formData.value.panel_type === 3))
            ? ['激励广告按钮', 'panel_config.highlight_ad', {
                type: 'radio',
                options: [
                  { label: '默认样式（非强化）', value: false, disabled: isViewMode.value },
                  { label: '强化展示', value: true, disabled: isViewMode.value },
                ],
                disabled: isViewMode.value,
              }]
            : [],
        ]}
      /> */}
      <div role="tablist" class="tab-sm tabs-boxed tabs mb-4 flex gap-x-2 bg-white">
        {
          [
            [0, 1].includes(+formData.value.user_platform) ? 'ios_config' : '',
            [0, 2].includes(+formData.value.user_platform) ? 'android_config' : '',
          ].filter(i => !!i).map(item => (
            <span role="tab" onClick={() => curTab.value = item}
              class={mc('tab bg-gray-300 text-black', curTab.value === item ? 'tab-active' : '')}
            >
              {{ ios_config: 'iOS配置', android_config: 'Android配置' }[item]}
            </span>
          ))
        }
        <div class="flex flex-1 justify-end">
          <Button class="btn btn-outline  btn-sm justify-self-end" disabled={refreshLoading.value} onClick={onRefresh}>刷新</Button>
        </div>

      </div>
      <Form
        class="flex flex-col gap-4"
        data={formData.value}
        error={stepTwoError.value}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        onReset={() => {
          resetTaskFormData(false)
        }}
        onSubmit={onSubmit}
        items={[
          [requiredLabel('充值档位方案-高亮显示只能勾选一个档位'), `product_config.${curTab.value}.recharge_option`, {
            type: 'custom',
            render: ({ item, value }) => {
              return (
                <section class="border-1 rounded-lg border border-solid p-4">
                  <x-table-actions class="flex items-center justify-between">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportRechargeLevelDialog()}>导入配置</Button>
                    </div>
                  </x-table-actions>
                  <hr class="my-4" />
                  {/* {JSON.stringify(currentRechargeList.value)} */}
                  <LevelOptionTable
                    list={currentRechargeList.value}
                    columns={rechargeColumns}
                    class="tm-table-fix-last-column"
                  />
                </section>
              )
            },
          }, { class: 'w-full' }],
          ['道具（充值）方案-仅支持配置1个。带道具时，订阅方案建议不超过2个，以避免支付面板UI内容太多。非必填，若不需要展示，请删除为空', `product_config.${curTab.value}.item_package_option`, {
            type: 'custom',
            render: ({ item, value }) => {
              return (
                <section class="border-1 rounded-lg border border-solid p-4">
                  <x-table-actions class="flex items-center justify-between">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportRechargeItemDialog()}>导入配置</Button>
                    </div>
                  </x-table-actions>
                  <hr class="my-4" />
                  {/* {JSON.stringify(currentRechargeItemList.value)} */}
                  <LevelOptionTable
                    list={currentRechargeItemList.value || []}
                    columns={rechargeItemColumns}
                    class="tm-table-fix-last-column"
                  />
                </section>
              )
            },
          }, { class: 'w-full' }],
          [requiredLabel('订阅方案-高亮显示只能勾选一个档位'), `product_config.${curTab.value}.member_vip_option`, {
            type: 'custom',
            render: ({ item, value }) => {
              return (
                <section class="border-1 rounded-lg border border-solid p-4">
                  <x-table-actions class="flex items-center justify-between">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportMemberLevelDialog()}>导入配置</Button>
                    </div>
                  </x-table-actions>
                  <hr class="my-4" />
                  {/* {JSON.stringify(currentMemberList.value)} */}
                  <LevelOptionTable
                    list={currentMemberList.value}
                    columns={memberColumns}
                    class="tm-table-fix-last-column"
                  />
                </section>
              )
            },
          }, { class: 'w-full' }],
        ]}
        actions={() => (
          <div class="flex items-center justify-between gap-x-4">
            <Button class="btn btn-sm" type="button" onClick={() => {
              void router.push({ path: `/strategy-group/create/target`, query: { ...route.query } })
            }}
            >上一步
            </Button>
            {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button>}
            <Button class="btn btn-primary btn-sm" type="submit">保存</Button>
          </div>
        )}
      />

    </div>
  )
})
export default StrategyGroupCreateProductPage
