import { createComponent, fn, required } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import EpisodeSelect from './episode-select/episode-select.vue'
import FollowButton from './follow-button'
import { Fn } from '@vueuse/core'

type SeriesMenuBarOptions = {
  props: {
    seriesId: string
  }
  emits: {
    unlock: (episode_id: string) => void
    click: Fn
  }
}
export const SeriesMenuBar = createComponent<SeriesMenuBarOptions>({
  props: {
    seriesId: required,
  },
  emits: {
    unlock: fn,
    click: fn,
  },
}, (props, { emit }) => {
  return () => (
    <MergeClass tag="x-series-menu-bar" baseClass="fixed z-series-menu bottom-28 right-2.5 flex flex-col gap-7 "
      onClick={() => emit('click')}>
      {/* 其他控件 */}
      <FollowButton seriesId={props.seriesId} />
      <EpisodeSelect onUnlock={episode_id => emit('unlock', episode_id)} />
    </MergeClass>
  )
})

export default SeriesMenuBar
