/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { <PERSON>ton, CreateForm, Pager, showFailToast, transformTimestamp } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { ElTable, ElTableColumn, ElDatePicker } from 'element-plus'
import { apiGetGroupStatistic } from './resource-user-statistic-api'

type ResourceGroupStatisticOptions = {
  props: {}
}

export const ResourceGroupStatistic = createComponent<ResourceGroupStatisticOptions>({
  props: {},
}, props => {
  const loading = ref(false)
  const list = ref<Api.ResourceUserStatistic.List>([])
  const defaultParams: Api.ResourceUserStatistic.Param = {
    start_time: 0,
    end_time: 0,
    username: '',
  }
  const form = ref<Api.ResourceUserStatistic.Param>({ ...defaultParams })
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const QueryForm = CreateForm<Api.ResourceUserStatistic.Param>()

  const getList = async () => {
    if (form.value.start_time > form.value.end_time) {
      showFailToast('结束时间必须小于开始时间')
      return
    }
    const params = {
      ...form.value,
      ...pageInfo.value,
      start_time: form.value.start_time || 0,
      end_time: form.value.end_time || 0,
    }
    loading.value = true
    list.value = []
    try {
      const res = await apiGetGroupStatistic(params)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const columns = [
    { prop: 'username', label: '分组', minWidth: 180 },
    { prop: 'cal_time', label: '时间', minWidth: 180 },
    { prop: 'check_count_first_round', label: '审核量-首发', minWidth: 180 },
    { prop: 'rejected_count_first_round', label: '驳回数-首发', minWidth: 180 },
    { prop: 'pass_count_first_round', label: '通过数-首发', minWidth: 180 },
    { prop: 'check_count_second_round', label: '审核量-二轮', minWidth: 180 },
    { prop: 'rejected_count_second_round', label: '驳回数-二轮', minWidth: 180 },
    { prop: 'pass_count_second_round', label: '通过数-二轮', minWidth: 180 },
    { prop: 'rejected_count', label: '被驳回数', minWidth: 180 },
  ]

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = { ...defaultParams }
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }
  onQuery()

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>各组汇总</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              [
                ['开始时间：', 'start_time', {
                  type: 'custom',
                  render: () => {
                    return (
                      <ElDatePicker
                        v-model={form.value.start_time}
                        type="date"
                        placeholder="选择日期"
                      />
                    )
                  },
                }],
                ['结束时间：', 'end_time', {
                  type: 'custom',
                  render: () => {
                    return (
                      <ElDatePicker
                        v-model={form.value.end_time}
                        type="date"
                        placeholder="选择日期"
                      />
                    )
                  },
                }],
              ],
            ]}
          />
        ),
        table: () => (
          <ElTable
            data={list.value || []}
            v-loading={loading.value}
          >
            {columns.map(col => {
              if (col.render) {
                return (
                  <ElTableColumn key={col.prop} prop={col.prop} label={col.label} width={col.width} v-slots={{
                    default: ({ row }: { row: any }) => col.render({ row }),
                  }}
                  />
                )
              } else {
                return (
                  <ElTableColumn key={col.prop} prop={col.prop} label={col.label} width={col.width} fixed={col.fixed} />
                )
              }
            })}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ResourceGroupStatistic
