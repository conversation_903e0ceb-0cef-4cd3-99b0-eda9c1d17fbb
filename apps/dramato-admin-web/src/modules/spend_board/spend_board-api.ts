import { httpClient } from 'src/lib/http-client'

export const apiGetSpendBoardList = (data: M.SpendBoard.params) =>
  httpClient.post<ApiResponse<M.SpendBoard.ListResponse>>('/material/spend_board', data)

export const apiEditSpendBoard = (data: {
  column_type: 1 | 2 | 3 | number // . 1 comment  2:business_rating 3: content_rating
  content: string
  series_resource_id: string
}) =>
  httpClient.post<ApiResponse<boolean>>('/material/spend_board_edit', data)

export const apiJuDanSpendBoard = (data: {
  deploy_task_time: number
  series_resource_id: string
}) =>
  httpClient.post<ApiResponse<boolean>>('/material/spend_board_judan_edit', data)

export const apiEditSpendBoardTaskStatus = (data: {
  recommends: {
    lang: string
    deploy_task_time: number
    recommend: number // 任务状态 0 空 1 待认领 2 已认领
  }[]
  series_resource_id: string
}) =>
  httpClient.post<ApiResponse<boolean>>('/material/spend_board_recommend', data)

export const apiAddEditTaskListStatus = (data: {
  tasks: {
    lang: string
    deploy_task_time: number
    team: 1 | 2
    comment: string
  }[]
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<boolean>>('/material/task_list_task_addedit', data)

export const apiDeleteTaskListStatus = (data: {
  lang: string
  audio_type: number
  team: 1 | 2
  series_resource_id: number
}) =>
  httpClient.post<ApiResponse<boolean>>('/material/task_list_task_delete', data)

export const apiAddScore = (data: {
  editor_rating: string
  series_resource_id: number
  deploy_task_doc: string
}) =>
  httpClient.post<ApiResponse<boolean>>('/material/task_list_info_add', data)

export const apiAddSeriesTaskListBatchAdd = (data: {
  team: 1 | 2
  comment: string
  deploy_task_time: number
  tasks: {
    series_resource_id: number
    lang: string
  }[]
}) =>
  httpClient.post<ApiResponse<boolean>>('/material/task_list_task_addedit_batch', data)
