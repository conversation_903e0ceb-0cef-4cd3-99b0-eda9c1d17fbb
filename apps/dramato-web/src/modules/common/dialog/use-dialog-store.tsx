import { createCachedFn, Key } from '@skynet/shared'
import { Dialog, type DialogProps } from './dialog'
import { ref } from 'vue'

export const useDialogStore = createCachedFn((id: Key) => {
  const dialogStack = ref<DialogProps[]>([])
  /**
   * 压入 dialog
   * @returns dialog 的 index
   */
  const addDialog = (props: DialogProps) => {
    const length = dialogStack.value.push(props)
    return length - 1
  }

  const removeDialog = (index = -1) => {
    if (index === -1) {
      dialogStack.value.pop()
    } else {
      dialogStack.value.splice(index)
    }
  }

  const renderDialogs = () => {
    return dialogStack.value.map((dialog, index) => (
      <Dialog {...dialog} visible key={index}
        onClose={() => {
          dialog.beforeClose && dialog.beforeClose()
          removeDialog(index)
          if (dialog.showParentWhenChildClose && dialogStack.value.length > 0) {
            dialogStack.value[dialogStack.value.length - 1].visible = true
          }
        }}
      />
    ))
  }
  return {
    addDialog,
    removeDialog,
    dialogStack,
    renderDialogs,
  }
})
