/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, Icon, showSuccessToast, showFailToast, openDialog, Tooltip } from '@skynet/ui'
import { TranslateForm } from './components/translate-form'
import { SeriesTable } from './components/series-table'
import { useResourceStore } from './use-resource-publish-store'
import { useRoute, RouterLink } from 'vue-router'
import { onMounted, ref, watch, computed } from 'vue'
import { Wrapper } from 'src/layouts/wrapper'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { apiSaveLabel, apiChangeAudit, apiGetResourcePublishDetail, apiUploadCoverUrl } from './resource-publish-api'
import { apiGetColors } from './resource-info-modify-api'
import { loadResource } from 'src/lib/dynamic-load'
import { ShowStatus } from './components/show-status'
import UploadImagePreview from './components/upload-image-preview'
import { Uploader } from '../common/uploader/uploader'
import { ElCascader, ElTooltip } from 'element-plus'
import { LABEL_OPTIONS } from './constant'
import { requiredLabel } from 'src/lib/required-label'
import { langKey, langValue } from '../resource/constant'

type ResourceInfoPageOptions = {
  props: {}
}
export const ResourceInfoPage = createComponent<ResourceInfoPageOptions>({
  props: {},
}, props => {
  const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()
  const labelLoading = ref(true)
  const colorList = ref<M.IColor[]>([])
  const rootLabelIds = ref<number[]>([])
  const firstLabelIds = ref<number[]>([])
  const secondLabelIds = ref<number[] | [number, number][]>([])
  const pureCover = ref<string>()
  const rootLabelEdit = ref(false)
  const childLabelEdit = ref(false)
  const secondLabelEdit = ref(false)
  const btnLoading = ref(false)
  const audit_process = ref<M.IResourceAuditProcessItem[]>([])
  const detailLoading = ref(true)
  const isUploading = ref(false)
  const options = {
    multiple: true,
    expandTrigger: 'hover' as const,
    checkStrictly: true,
    disabled: (data: any) => {
      return data.children && data.children.length > 0
    },
  }
  const seriesInfoCommitLoading = ref(false)
  const curColor = ref<number>()
  const sortableList1 = ref<HTMLElement>()
  const errors = ref<string>('')
  const checkValidate = ref(false)
  const descriptionChange = ref(false)

  const route = useRoute()
  const { getEpisodeSeriesInfoList, episodeSeriesInfoList, updateEpisodeSeriesInfoList, onUploadResourceInfo, getLabelList, firstLabels, secondLabels, rootLabels, allLabels } = useResourceStore()
  // 1 资源验收 2 多语言信息 3 多语言字幕 4 终审发布 5 资源录入
  const isFinalPage = computed(() => {
    return route.fullPath.includes('/resource-publish/info-verify/information')
  })

  const currentStatus = computed(() => {
    return audit_process.value?.find(item => item.page_type === 2) as M.IResourceAuditProcessItem
  })

  // 非审核通过的有通过按钮
  const hasPassButton = computed(() => {
    const obj = audit_process.value?.find(item => item.page_type === 2)
    return obj?.status === 0
  })

  const getResourceDetail = async () => {
    const res = await apiGetResourcePublishDetail({
      id: +route.params.id,
    })
    audit_process.value = res?.data?.audit_process || []
    detailLoading.value = false
    rootLabelIds.value = (res.data?.root_label_ids as number[]) || []
    firstLabelIds.value = (res.data?.first_label_ids as number[]) || []
    secondLabelIds.value = (res.data?.second_label_ids as number[]) || []
    pureCover.value = res.data?.pure_cover || ''
  }

  const saveSeriesInfo = async () => {
    seriesInfoCommitLoading.value = true
    try {
      if (checkValidate.value) {
        const m = {
          3: 0,
          4: 0,
          5: 0,
        }
        if (rootLabelIds.value.length === 0) {
          showFailToast('请选择一级标签')
          return
        }
        if (firstLabelIds.value.length === 0) {
          showFailToast('请选择二级标签')
          return
        }
        if (secondLabelIds.value.length === 0) {
          showFailToast('请选择三级标签')
          return
        }
        /**
         * @description
         * 通过cascader操作过的 [number, number] [标签类型， 标签id]
         * 未操作的  number[] 标签id[]
         */
        secondLabelIds.value.forEach(item => {
          if (Array.isArray(item) && item.length === 2) {
            m[item[0] as 3 | 4 | 5]++
          } else if (typeof item === 'number') {
            const type = allLabels.value.find(label => label.label_id === item)?.content_type
            m[type as 3 | 4 | 5]++
          }
        })
        if (m[3] === 0) {
          showFailToast('请选择情节类型标签')
          return
        }
        if (m[4] === 0) {
          showFailToast('请选择角色类型标签')
          return
        }
        if (m[5] === 0) {
          showFailToast('请选择背景类型标签')
          return
        }
        if (m[5] > 1) {
          showFailToast('背景类型标签只能选择一个')
          return
        }
      }
      if (!descriptionValidate()) return
      await apiSaveLabel({
        id: +route.params.id,
        root_label_ids: rootLabelIds.value,
        first_label_ids: firstLabelIds.value,
        second_label_ids: secondLabelIds.value as number[],
      })
      const res = await updateEpisodeSeriesInfoList({
        series_resource_id: +route.params.id,
        list: episodeSeriesInfoList.value,
      }) as any
      if (res?.code !== 200) {
        return
      }
      showSuccessToast('保存成功')
    } catch (error: any) {
      showFailToast(error.response.data.err_msg || '操作失败')
    } finally {
      seriesInfoCommitLoading.value = false
    }
  }

  const getColorLit = async () => {
    const res = await apiGetColors()
    colorList.value = res.data?.list || []
  }

  watch(() => sortableList1.value, newVal => {
    if (!newVal) return
    new window.Sortable(sortableList1.value!, {
      animation: 150,
      onEnd(event: any) {
        // 更新 items 顺序
        const [movedItem] = secondLabelIds.value.splice(event.oldIndex, 1)
        secondLabelIds.value.splice(event.newIndex, 0, movedItem)
      },
    })
  })

  onMounted(async () => {
    try {
      labelLoading.value = true
      void getResourceDetail()
      void getEpisodeSeriesInfoList({
        series_resource_id: +route.params.id,
      })
      void getColorLit()
      await loadResource('https://static-v1.mydramawave.com/frontend_static/vendor/sortable.min.js', 'js')
      void getLabelList()
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      labelLoading.value = false
    }
  })

  const descriptionValidate = () => {
    if (descriptionChange.value) {
      const _list = episodeSeriesInfoList.value.filter(item => item.description && item.description.length > 300)

      if (_list.length > 0) {
        showFailToast(`${_list.map(item => langValue[langKey.findIndex(key => item.language_code === key)]).join(',')}简介超过300个字符`)
        return false
      }
    }

    return true
  }

  const onPass = () => {
    if (!descriptionValidate()) return
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-title class="text-[var(--text-2)]">确认通过</x-title>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              btnLoading.value = true
              try {
                await apiChangeAudit({
                  id: +route.params.id,
                  page_type: 2,
                  ope_type: 2,
                })
                showSuccessToast('操作成功')
                void getResourceDetail()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                btnLoading.value = false
                hideDialog()
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  return () => (
    <Wrapper class="relative">
      <section class="breadcrumbs text-sm">
        <ul>
          <li><RouterLink to="/resource-publish/info-verify">信息校验</RouterLink></li>
          <li>资源信息</li>
        </ul>
      </section>
      <div class="relative w-full overflow-hidden">
        <div>
          <ShowStatus data={currentStatus.value} />
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex flex-col gap-y-1">
            <div class="h-[24px]">
              {requiredLabel('一级标签')}
            </div>
            {
              rootLabelEdit.value
                ? (
                    <div class="flex flex-wrap items-center space-x-2">
                      <FormMultiSelect
                        search
                        maxlength={1}
                        popoverWrapperClass="z-popover-in-dialog"
                        modelValue={rootLabelIds.value}
                        onUpdate:modelValue={e => rootLabelIds.value = e.map(o => +o)}
                        options={rootLabels.value?.map(i => {
                          return { label: i.content, value: '' + i.label_id }
                        })}
                      />
                      <Button class="btn btn-xs btn-primary" onClick={() => rootLabelEdit.value = false}>确认</Button>
                    </div>
                  )
                : (
                    <div class="flex flex-wrap items-center space-x-1">
                      {
                        rootLabelIds.value && rootLabelIds.value.length > 0
                          ? rootLabelIds.value.map(id => {
                            const curLabel = rootLabels.value.find(label => label.label_id === +id)
                            return <div class="badge badge-outline">{curLabel?.content}</div>
                          })
                          : <div class="badge badge-outline">暂无</div>
                      }
                      <Button class="btn btn-xs btn-primary" onClick={() => {
                        childLabelEdit.value = false
                        rootLabelEdit.value = true
                        secondLabelEdit.value = false
                      }}
                      >编辑
                      </Button>
                    </div>
                  )
            }
            <div class="h-[16px]" />
          </div>
          <div class="flex flex-col gap-y-1">
            <div class="h-[24px]">
              {requiredLabel('二级标签')}
            </div>
            <div class="flex space-x-2">
              {
                labelLoading.value
                  ? '加载中...'
                  : childLabelEdit.value
                    ? (
                        <div class="flex items-center space-x-2">
                          <FormMultiSelect
                            search
                            maxlength={1}
                            modelValue={firstLabelIds.value}
                            onUpdate:modelValue={e => firstLabelIds.value = e.map(o => +o)}
                            options={firstLabels.value?.map(i => {
                              return { label: i.content, value: '' + i.label_id, tooltip: i.meaning }
                            })}
                          />
                          <Button class="btn btn-xs btn-primary" onClick={() => childLabelEdit.value = false}>确认</Button>
                        </div>

                      )
                    : (
                        <div class="flex flex-wrap items-center space-x-1">
                          <div class="flex space-x-1 ">
                            {
                              firstLabelIds.value && firstLabelIds.value.length > 0
                                ? firstLabelIds.value.map(id => {
                                  const curLabel = firstLabels.value.find(label => label.label_id === +id)
                                  return (
                                    <Tooltip key={id} popContent={() => curLabel?.meaning}>
                                      <div class="badge badge-outline">{curLabel?.content}</div>
                                    </Tooltip>
                                  )
                                })
                                : <div class="badge badge-outline">暂无</div>
                            }
                          </div>
                          <Button class="btn btn-xs btn-primary" onClick={() => {
                            rootLabelEdit.value = false
                            childLabelEdit.value = true
                            secondLabelEdit.value = false
                          }}
                          >编辑
                          </Button>
                        </div>
                      )
              }
            </div>
            <div class="h-[16px]" />
          </div>
          <div class="flex flex-col gap-y-1">
            <div class="h-[24px]">
              {requiredLabel('三级标签')}
            </div>
            <div class="flex space-x-2">
              {
                labelLoading.value
                  ? '加载中...'
                  : secondLabelEdit.value
                    ? (
                        <div class="space-x-2">
                          <ElCascader
                            collapse-tags
                            modelValue={secondLabelIds.value}
                            placeholder="请选择"
                            collapse-tags-tooltip
                            options={secondLabels.value}
                            clearable
                            filterable
                            props={options}
                            onChange={(e: any) => {
                              checkValidate.value = true
                              secondLabelIds.value = e
                            }}
                            v-slots={{
                              default: ({ node, data }: any) => {
                                if (node.isLeaf) {
                                  return (
                                    <ElTooltip
                                      content={data.meaning}
                                      placement="top"
                                      effect="light"
                                      popperClass="!max-w-[400px]"
                                    >
                                      <span>{data.label}</span>
                                    </ElTooltip>
                                  )
                                }
                                return <span>{data.label}</span>
                              },
                            }}
                          />
                          <Button class="btn btn-xs btn-primary" onClick={() => {
                            errors.value = ''
                            if (checkValidate.value) {
                              const m = {
                                3: 0,
                                4: 0,
                                5: 0,
                              }
                              secondLabelIds.value.forEach(item => {
                                if (Array.isArray(item) && item.length === 2) {
                                  m[item[0] as 3 | 4 | 5]++
                                } else if (typeof item === 'number') {
                                  const type = allLabels.value.find(label => label.label_id === item)?.content_type
                                  m[type as 3 | 4 | 5]++
                                }
                              })
                              if (m[3] === 0) {
                                errors.value = '请选择情节类型标签'
                                return
                              }
                              if (m[4] === 0) {
                                errors.value = '请选择角色类型标签'
                                return
                              }
                              if (m[5] === 0) {
                                errors.value = '请选择背景类型标签'
                                return
                              }
                              if (m[5] > 1) {
                                errors.value = '背景类型标签只能选择一个'
                                return
                              }
                            }
                            secondLabelEdit.value = false
                          }}>确认</Button>
                        </div>
                      )
                    : (
                        <div class="flex items-center space-x-1 max-w-[320px]">
                          <div class="flex flex-wrap gap-1" ref={sortableList1}>
                            {
                              secondLabelIds.value && secondLabelIds.value.length > 0
                                ? (secondLabelIds.value as any[]).map(item => {
                                    if (Array.isArray(item) && item.length === 2) {
                                      const curLabel = allLabels.value.find(label => label.label_id === +item[1])
                                      return (
                                        <Tooltip popWrapperClass="w-[200px]" key={curLabel?.label_id} popContent={() => curLabel?.meaning}>
                                          <div class="badge badge-outline cursor-move">{LABEL_OPTIONS.find(o => o.value === curLabel?.content_type)?.label}/{curLabel?.content}</div>
                                        </Tooltip>
                                      )
                                    } else if (typeof item === 'number') {
                                      const curLabel = allLabels.value.find(label => label.label_id === +item)
                                      return (
                                        <Tooltip popWrapperClass="w-[200px]" key={item} popContent={() => curLabel?.meaning}>
                                          <div class="badge badge-outline cursor-move">{LABEL_OPTIONS.find(o => o.value === curLabel?.content_type)?.label}/{curLabel?.content}</div>
                                        </Tooltip>
                                      )
                                    }
                                  })
                                : <div class="badge badge-outline">暂无</div>
                            }
                          </div>
                          <Button class="btn btn-xs btn-primary" onClick={() => {
                            rootLabelEdit.value = false
                            childLabelEdit.value = false
                            secondLabelEdit.value = true
                          }}
                          >编辑
                          </Button>
                        </div>
                      )
              }
            </div>
            <div class="h-[16px]">
              <span class="text-xs text-[var(--text-2)]">情节（多选）、角色（多选）、背景（单选）</span>
            </div>
            {errors.value ? <div class="text-xs text-red-500">{errors.value}</div> : null}
          </div>
          <div>
            <div class="h-[24px]">全语言配色方案：</div>
            <select
              class="select-bordered select-sm select w-[160px]"
              value={curColor.value}
              onInput={(e: Event) => {
                episodeSeriesInfoList.value.forEach(item => {
                  item.color_style = +((e.target as HTMLInputElement).value || '')
                })
                curColor.value = +((e.target as HTMLInputElement).value || '')
              }}
            >
              {colorList.value?.map((option: M.IColor) => (
                <option value={option.id}>{option.name}</option>
              ))}
            </select>
            <div class="h-[16px]" />
          </div>
          <div>
            <div class="h-[24px]">纯净版海报：</div>
            <Uploader
              maxsize={2 * 1024 * 1024}
              accept="jpg,png,jpeg,gif"
              dimension={[3, 4]}
              multiple={false}
              ossKeyType="resource"
              class="size-[120px] cursor-pointer overflow-hidden rounded-md border border-dashed"
              onUploadSuccess={async d => {
                try {
                  isUploading.value = true
                  await apiUploadCoverUrl({
                    id: +route.params.id,
                    path: d.temp_path!,
                    type: 5,
                  })
                  isUploading.value = false
                  showSuccessToast('上传成功！')
                } catch (error: any) {
                  isUploading.value = false
                  showFailToast(error.response.data.message || '操作失败')
                }
                pureCover.value = d.temp_path as string
              }}
              isImage={false}
            >
              {pureCover.value
                ? (
                    <UploadImagePreview image={
                      pureCover.value.indexOf('http') > -1 ? pureCover.value : `https://img.tianmai.cn/${pureCover.value}`
                    } onDelete={() => {
                      const hideDialog = openDialog({
                        title: '提示',
                        mainClass: 'pb-0 px-5',
                        body: () => (
                          <x-image-confirm-dialog class="flex flex-col gap-y-[25px]">
                            <x-image-body>是否删除图片</x-image-body>
                            <x-image-footer class="flex w-full justify-end gap-x-[10px]">
                              <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                              <button class="btn btn-primary btn-sm" onClick={() => {
                                pureCover.value = ''
                                hideDialog()
                              }}
                              >确定
                              </button>
                            </x-image-footer>
                          </x-image-confirm-dialog>
                        ),
                      })
                    }}
                    />
                  )
                : isUploading.value
                  ? <span class="flex size-full items-center justify-center">图片上传中……</span>
                  : <span class="flex size-full items-center justify-center">上传封面, 3:4</span>}
            </Uploader>
            <div class="h-[16px]" />
          </div>
        </div>
        <div class="flex justify-end space-x-4">
          <Button class="btn btn-primary btn-sm" onClick={(() => {
            const hideDialog = openDialog({
              title: '提示',
              mainClass: 'pb-0 px-5',
              body: () => (
                <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                  <TranslateForm series_resource_id={+route.params.id} onFinish={() => {
                    hideDialog()
                  }}
                  />
                </x-status-confirm-dialog>
              ),
            })
          })}
          >重新翻译信息
          </Button>
          <Button class="btn btn-primary btn-sm" onClick={() => onUploadResourceInfo(() => {
            void getEpisodeSeriesInfoList({
              series_resource_id: +route.params.id,
            })
          })}
          >导入资源信息
          </Button>
        </div>
        <div class="flex w-full flex-col gap-x-2 overflow-x-auto pb-16">
          {
            episodeSeriesInfoList.value && episodeSeriesInfoList.value.length > 0
              ? (
                  <SeriesTable
                    colors={colorList.value}
                    list={episodeSeriesInfoList.value}
                    onDescriptionChange={() => {
                      descriptionChange.value = true
                    }}
                  />
                ) : null
          }
        </div>
        <div class="fixed bottom-0 right-8 z-[5] w-[calc(100vw-240px)] space-x-4 bg-white py-4 text-right">
          <RouterLink to="/resource-publish/info-verify">
            <Button class="btn btn-sm">
              返回
            </Button>
          </RouterLink>
          {
            !detailLoading.value && isFinalPage.value && hasPassButton.value
              ? (
                  <Button class="btn btn-sm btn-primary" disabled={btnLoading.value} onClick={onPass}>
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-3" /> : null}
                    完成校验
                  </Button>
                )
              : null
          }
          <Button class="btn btn-sm btn-primary" disabled={seriesInfoCommitLoading.value} onClick={saveSeriesInfo}>
            {seriesInfoCommitLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-3" /> : null}
            提交到线上
          </Button>
        </div>
      </div>
    </Wrapper>
  )
})

export default ResourceInfoPage
