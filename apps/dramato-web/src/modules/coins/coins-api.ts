import { httpClient } from 'src/lib/http-client'
import { MyEarnings } from './my-earnings/my-earnings'
import { CoinsGift, ExchangeDetails, GiftType, MyExchange } from './exchange/exchange'

export const apiGetMyEarnings = (data: {
  page: number
  page_size: number
  last_id?: number
}) => {
  return httpClient.get<ApiResponse<{
    gold_flow_list: MyEarnings[]
    last_id: number
    has_more: boolean
  }>>('dm-api/welfare/gold-flow', data)
}

export const apiGetMyExchange = (data: {
  page: number
  page_size: number
  last_id?: number
}) => {
  return httpClient.get<ApiResponse<{
    list: MyExchange[]
    last_id: number
    has_more: boolean
  }>>('dm-api/welfare/exchange/history_list', data)
}

export const apiGetCoins = () => {
  return httpClient.get<ApiResponse<{ coins: { amount: number } }>>('dm-api/welfare/wallet')
}

export const apiGetBroadcast = () => {
  return httpClient.get<ApiResponse<{ list: string[][] }>>('dm-api/welfare/exchange/broadcast/list')
}

export const apiGetExchangeDetails = () => {
  return httpClient.get<ApiResponse<ExchangeDetails>>('dm-api/welfare/exchange/detail')
}

export const apiGetExchangeCoinsList = () => {
  return httpClient.get<ApiResponse<{ reward_list: CoinsGift[] }>>('dm-api/welfare/exchange/list')
}

export const apiExchangeGift = (data: {
  id: string
  type: string
}) => {
  return httpClient.post<ApiResponse<{ gift_card_secret?: string }>>('dm-api/welfare/exchange/gift', data)
}

export const apiRedeemGift = (data: {
  id: string
  type: string
  account?: string
  full_name?: string
  mobile?: string
  ifsc?: string
  email?: string
  address?: string
}) => {
  return httpClient.post<ApiResponse>('dm-api/welfare/exchange/transfer', data)
}

export const apiExchangeCoins = (data: {
  type: string
  check_enough?: boolean
}) => {
  return httpClient.post<ApiResponse>('dm-api/welfare/exchange/coins', data)
}

export const apiGetLastRedeemAccount = (type: string) => {
  return httpClient.post<ApiResponse<{
    account?: string
    type: 'ovo' | 'brazil' | 'india' | 'moxico' | 'philippines' | 'vietnam' | 'malaysia' | 'tailandia' | string
    id?: string
    full_name?: string
    mobile?: string
    ifsc?: string
    email?: string
    address?: string
    tax_id?: string
  }>>('dm-api/welfare/exchange/get_account', { type })
}
