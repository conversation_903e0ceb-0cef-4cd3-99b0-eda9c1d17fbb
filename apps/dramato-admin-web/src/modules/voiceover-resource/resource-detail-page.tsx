/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { langKey, langValue } from 'src/modules/resource/constant'
import { useResourceDetailStore } from './use-resource-detail-store'
import { M3u8Player } from '../resource/components/m3u8-player'
import Subtitle from '../common/subtitle/subtitle1'
import RoleTimbre from './role-timbre'
import { Button, Checkbox, Icon, showFailToast, showSuccessToast } from '@skynet/ui'
import { apiSaveSubtitleContent, apiGetSubtitleSyllableCount } from '../role-mark/role-mark-api'
import { getSubtitleContent } from 'src/modules/common/subtitle/until'

type ResourceDetailPageOptions = {
  props: {}
}
export const ResourceDetailPage = createComponent<ResourceDetailPageOptions>({
  props: {},
}, props => {
  const route = useRoute()
  const router = useRouter()
  const { defaultLanguageCode, getAnnotations, setSeriesId, getRealSubtitle, getList, list, loading,  getDetail, currentResource, characters, annotations, getCharacters, emotions, effects, emotions_cn, effects_cn, origin_syllable_list, shorten_syllable_list } = useResourceDetailStore()
  setSeriesId(+route.params.id)

  // 如果不存在 默认用英语
  const original_language = route.query.language || 'en'
  const reference_language = route.query.ref || defaultLanguageCode.value || 'en'
  // 如果不存在 默认第一集
  const serial_number = ref<number>(+(route.query.number || 1))
  const langIndex = langKey.findIndex(r => r === original_language)
  const refIndex = langKey.findIndex(r => r === reference_language)
  const btnLoading = ref(false)
  const resourceLoading = ref(false)
  const isInit = ref(false)
  // 当前选中行
  const currentRow = ref<Api.VoiceoverResource.IMultipleResource>({})

  // 剧集原语言
  const referenceLangIndex = ref(refIndex)
  const videoRef = ref()
  const currentTime = ref(0)
  // 右侧展示语言
  const originalLangIndex = ref(langIndex)
  // 左侧
  const originalRef = ref()
  const referenceRef = ref()

  const isAsync = ref(true)

  const lineNotMatch = ref(false)

  const videoType = ref<'pure_video_path' | 'ora_video_path'>('pure_video_path')
  const videoUrl = ref(currentRow.value[`${defaultLanguageCode.value}_${videoType.value}`])

  const reference = ref<{
    code: string
    language: string
  }>({
    code: '',
    language: '',
  })
  const original = ref<{
    code: string
    language: string
  }>({
    language: langKey[langIndex],
    code: '',
  })
  const subtitles = ref<{ language: string, type: string, subtitle: string }[]>([])

  // 左侧语言、右侧语言及技术发生变化
  const setRouter = (params: { [Record: string]: string | number }) => {
    const query = {
      ...route.query,
      ...params,
    }
    void router.replace({
      path: location.pathname,
      query,
    })
  }

  watch(() => currentRow.value.num, async () => {
    resourceLoading.value = true
    currentTime.value = 0
    lineNotMatch.value = false
    if (currentRow.value.num) {
      setRouter({
        number: currentRow.value.num,
      })
    }
    videoUrl.value = currentRow.value[`${defaultLanguageCode.value}_${videoType.value}`]
    if (typeof currentRow.value.num === 'number') await getAnnotations(currentRow.value.num, langKey[originalLangIndex.value])
    if (referenceLangIndex.value < 0 && originalLangIndex.value < 0) return
    const code1 = await getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[referenceLangIndex.value]}_srt_path`]))
    const code2 = await getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_srt_path`]))
    // 左侧对照永远选择 中文
    reference.value = {
      code: code1,
      language: langKey[referenceLangIndex.value],
    }
    original.value = {
      language: langKey[originalLangIndex.value],
      code: code2,
    }
    subtitles.value = [
      {
        language: original.value.language,
        type: 'normal',
        subtitle: getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_srt_path`]),
      },
    ]
    // 可选的角色 suggestion
    resourceLoading.value = false
  })

  watch(() => videoRef.value, newVal => {
    if (newVal) {
      videoRef.value.on('timeupdate', () => {
        if (!resourceLoading.value) currentTime.value = videoRef.value.getCurrentTime() || 0
      })
    }
  })

  watch(() => videoType.value, () => {
    if (videoType.value === 'ora_video_path') {
      videoUrl.value = currentRow.value[`${defaultLanguageCode.value}_ora_video_path`]
    } else if (videoType.value === 'pure_video_path') {
      videoUrl.value = currentRow.value[`${defaultLanguageCode.value}_pure_video_path`]
    }
  })

  watch(() => referenceLangIndex.value, async () => {
    resourceLoading.value = true
    lineNotMatch.value = false
    const code = await getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[referenceLangIndex.value]}_srt_path`]))
    reference.value = {
      code,
      language: langKey[referenceLangIndex.value],
    }

    resourceLoading.value = false
  })

  watch(() => originalLangIndex.value, async () => {
    resourceLoading.value = true
    lineNotMatch.value = false
    await getAnnotations(currentRow.value.num, langKey[originalLangIndex.value])
    const code = await getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_srt_path`]))

    original.value = {
      code,
      language: langKey[originalLangIndex.value],
    }

    resourceLoading.value = false
  })

  const getFormatSubtitle = (list: Api.RoleMark.ISubtitleLine[]) => {
    return list.map(item => {
      return `${item.order}\n${item.time}\n${item.content}\n`
    }).join('\n')
  }

  onMounted(async () => {
    isInit.value = false
    void getCharacters()
    void getDetail()
    await getList()
    referenceLangIndex.value = langKey.findIndex(key => key === reference_language)
    currentRow.value = list.value.find(item => item.num === serial_number.value) as Api.VoiceoverResource.IMultipleResource
    const subtitleLang = langKey[referenceLangIndex.value]

    reference.value = {
      code: '',
      language: subtitleLang,
    }

    original.value = {
      language: langKey[originalLangIndex.value],
      code: ''
    }

    subtitles.value = [
      {
        language: original.value.language,
        type: 'normal',
        subtitle: getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_srt_path`]),
      },
    ]
    isInit.value = true
  })

  return () => (
    <x-resource-detail-page class="block">
      {
        isInit.value ? (
          <div class="px-4 flex flex-col h-[100vh] max-w-[1400px] mx-auto">
            <x-nav class="py-4 flex items-center space-x-4">
              <a class="btn btn-sm md:btn-md gap-2 lg:gap-3" href={`/resource-publish/voice-over/detail/${+route.params.id}`}>
                <svg class="h-6 w-6 fill-current md:h-8 md:w-8 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                  <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z" />
                </svg>
                <div class="flex flex-col items-start gap-0.5 leading-[1.1]">
                  <span>返回</span>
                </div>
              </a>
              {currentResource.value?.title ? (
                <>
                  <div>资源名称：<span class="text-[var(--text-1)] font-bold">{currentResource.value?.title}</span></div>
                  <div>资源ID：<span class="text-[var(--text-1)] font-bold">{currentResource.value?.id}</span></div>
                  <div>原视频语言：<span class="text-[var(--text-1)] font-bold">{langValue[langKey.findIndex(key => currentResource.value?.upload_subtitle_language_code && key === currentResource.value?.upload_subtitle_language_code[0])]}</span></div>
                  <span class="text-red-500 text-sm">{lineNotMatch.value ? '字幕行数对不上' : ''}</span>
                </>
              ) : null}
            </x-nav>
            <div class="flex flex-1 flex-row overflow-auto space-x-2">
              <x-video-area class="block">
                { videoUrl.value
                  ? (
                      <M3u8Player
                        subtitles={subtitles.value}
                        currentLanguage={langKey[originalLangIndex.value]}
                        url={getRealSubtitle(videoUrl.value)}
                        onPlayerReady={(e: any) => {
                          videoRef.value = e
                        }}
                      />
                    )
                  : <div class="flex h-[550px] w-[309px] items-center justify-center text-[var(--text-2)]">暂无视频</div> }
              </x-video-area>
              <x-subtitle-area class="flex h-full min-w-[820px] flex-1 overflow-auto">
                <x-reference class="w-[60%]">
                  <select value={reference.value.language} class="select select-bordered select-xs w-[100px]" onChange={(e: any) => {
                    const value = e.target.value as string
                    referenceLangIndex.value = langKey.findIndex(key => key === value)
                    setRouter({
                      ref: value,
                    })
                  }}
                  >
                    {
                      langKey.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
                    }
                  </select>
                  <Subtitle
                    v-loading={resourceLoading.value}
                    ref={referenceRef}
                    class="h-full"
                    code={reference.value.code}
                    onVideoProgress={(time: number) => {
                      if (videoRef.value) videoRef.value?.seek(time)
                    }}
                    showContentText={false}
                    emotions={emotions.value}
                    effects={effects.value}
                    emotions_cn={emotions_cn.value}
                    effects_cn={effects_cn.value}
                    rolePick={false}
                    syllable_list={reference.value.language === defaultLanguageCode.value ? origin_syllable_list.value : []}
                    showCharacters={true}
                    characters={characters.value}
                    annotations={annotations.value}
                    isEditable={false}
                    subtitleHeightFixed={true}
                    rtl={reference.value.language === 'ar'}
                  />
                </x-reference>
                <x-original class="w-[40%]">
                  <select value={original.value.language} class="select select-bordered select-xs w-[100px]" onChange={(e: any) => {
                    const value = e.target.value as string
                    originalLangIndex.value = langKey.findIndex(key => key === value)
                    setRouter({
                      language: value,
                    })
                  }}
                  >
                    {
                      langKey.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
                    }
                  </select>
                  <Subtitle
                    v-loading={resourceLoading.value}
                    ref={originalRef}
                    class="h-full"
                    code={original.value.code}
                    currentTime={currentTime.value}
                    onVideoProgress={(time: number) => {
                      if (videoRef.value) videoRef.value?.seek(time)
                    }}
                    showCharacters={false}
                    syllable_list={shorten_syllable_list.value}
                    characters={[]}
                    annotations={[]}
                    isEditable={true}
                    rolePick={false}
                    isAsyncScroll={isAsync.value}
                    subtitleHeightFixed={true}
                    rtl={original.value.language === 'ar'}
                  />
                </x-original>
              </x-subtitle-area>
              <RoleTimbre lang={langKey[originalLangIndex.value]} onChangeEpisodeNum={num => {
                const index = list.value.findIndex(item => item.num === num)
                currentRow.value = list.value[index]
              }} />
            </div>
            <x-subtitle-operation>
              <div class="py-4 flex items-center justify-between pt-4">
                <div class="flex">
                  <Button class="btn btn-outline btn-xs" disabled={(currentRow.value?.num) === (list.value[0].num) || resourceLoading.value || btnLoading.value} onClick={() => {
                    const index = list.value.findIndex(item => item.num === currentRow.value.num)
                    currentRow.value = list.value[index - 1]
                  }}
                  >上一集
                  </Button>
                  <div class="px-4">{ currentRow.value.num }</div>
                  <Button class="btn btn-outline btn-xs" disabled={list.value[list.value.length - 1].num === currentRow.value.num || resourceLoading.value || btnLoading.value} onClick={() => {
                    const index = list.value.findIndex(item => item.num === currentRow.value.num)
                    currentRow.value = list.value[index + 1]
                  }}
                  >下一集
                  </Button>
                  <select value={videoType.value} class="select select-bordered select-xs ml-4 w-[100px]" onChange={(e: any) => {
                    const value = e.target.value
                    videoType.value = value
                  }}
                  >
                    <option value="pure_video_path">无字幕视频</option>
                    <option value="ora_video_path">原视频</option>
                  </select>
                  <label class="flex items-center ml-4 cursor-pointer">
                    <Checkbox
                      label=""
                      modelValue={isAsync.value}
                      onUpdate:modelValue={(value: boolean) => {
                        isAsync.value = value
                      }}
                    />
                    <span>同步滚动</span>
                  </label>
                </div>
                <div>
                  <Button class="btn btn-primary btn-sm" disabled={btnLoading.value || resourceLoading.value} onClick={async () => {
                    const _list = originalRef.value.getContent() as Api.RoleMark.ISubtitleLine[]
                    const referenceList = referenceRef.value.getContent() as Api.RoleMark.ISubtitleLine[]

                    const list = _list.map((item, index) => {
                      item.order = index + 1
                      return item
                    })

                    const arr = list.filter(item => !item.content || !item.time)
                    if (arr.length > 0) {
                      showFailToast(`字幕第${arr.map(item => item.order).join(',')}请完成字幕内容及时间的填写`)
                      return
                    }

                    if (_list.length !== referenceList.length) {
                      lineNotMatch.value = true
                    } else {
                      lineNotMatch.value = false
                    }

                    const formatCode = getFormatSubtitle(list)
                    btnLoading.value = true

                    try {
                      const res = await apiSaveSubtitleContent({
                        serial_number: currentRow.value?.num,
                        series_resource_id: +route.params.id,
                        language_code: original.value.language,
                        content: formatCode,
                        audio_type: 1,
                        alter_type: 1
                      })
                      currentRow.value[`${original.value.language}_srt_path`] = res.data?.subtitle_uri || ''
                      showSuccessToast('保存成功')

                      const diffList: { index: number, text: string }[] = []
                      for (let i = 0; i < list.length; i++) {
                        const iItem = list[i]
                        const iContent = iItem['content']
                        const shortCont = (iItem?.errs || []).find(err => err.canReplace)?.shorten_content || ''
                        const oldContent = iItem?.oldContent || ''
                        if (iContent !== shortCont && iContent !== oldContent) {
                          diffList.push({
                            index: iItem.order!,
                            text: iContent
                          })
                        }
                      }

                      try{
                        const res2 = await apiGetSubtitleSyllableCount({
                          series_resource_id: +route.params.id,
                          serial_number: currentRow.value.num,
                          language: original.value.language,
                          texts: diffList,
                        })

                        if(res2.data?.series_resource_id === +route.params.id && res2.data?.serial_number === currentRow.value.num && res2.data?.language === original.value.language) {
                          const newDiffList = res2.data?.texts
                          for (let j = 0; j < newDiffList.length; j++) {
                            for(let m = 0; m < list.length; m++) {
                              if(list[m].order === newDiffList[j].index && list[m].content === newDiffList[j].text) {
                                for(let k = 0; k < shorten_syllable_list.value.length; k++) {
                                  if(shorten_syllable_list.value[k].line_number === newDiffList[j].index) {
                                    shorten_syllable_list.value[k].content = newDiffList[j].text
                                    shorten_syllable_list.value[k].content_syllable_count = newDiffList[j].syllable_count
                                    break
                                  }
                                }
                                break
                              }
                            }
                          }
                        }

                        const code2 = await getSubtitleContent(getRealSubtitle(currentRow.value[`${original.value.language}_srt_path`]))
                        original.value.code = code2
                      } catch (error: any) {
                        console.log(error)
                      }
                    } catch (error: any) {
                      showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                    } finally {
                      btnLoading.value = false
                    }
                  }}>
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    保存字幕
                  </Button>
                </div>
              </div>
            </x-subtitle-operation>
          </div>
        ) : null
      }
    </x-resource-detail-page>
  )
})

export default ResourceDetailPage
