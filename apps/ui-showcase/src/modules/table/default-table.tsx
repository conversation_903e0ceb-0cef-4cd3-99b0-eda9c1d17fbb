import { createComponent } from '@skynet/shared'
import { CreateTable } from '@skynet/ui'
export const DefaultTable = createComponent(null, () => {
  type Data = { id: string, name: string, age: number, address: string, birth: string, hobby: string, favorite_color: string, favorite_food: string, favorite_movie: string, favorite_music: string, favorite_book: string, favorite_game: string, favorite_sport: string, favorite_actor: string, favorite_actress: string, favorite_director: string }
  const Table = CreateTable<Data>()
  return () => (
    <Table
      list={[
        { id: '1', name: '<PERSON>', age: 20, address: '123 Broadway Street, Manhattan, New York, NY 10013', birth: '1990-01-01', hobby: 'Reading', favorite_color: 'Red', favorite_food: 'Pizza', favorite_movie: 'The Lord of the Rings: The Fellowship of the Ring', favorite_music: '<PERSON>', favorite_book: 'The Great Gatsby', favorite_game: 'The Witcher 3', favorite_sport: 'Basketball', favorite_actor: '<PERSON>', favorite_actress: '<PERSON><PERSON>', favorite_director: '<PERSON>' },
        { id: '2', name: '<PERSON>', age: 21, address: '123 Main Street, Los Angeles, California, CA 90038', birth: '1991-01-01', hobby: 'Reading', favorite_color: 'Blue', favorite_food: 'Pasta', favorite_movie: 'The Dark Knight', favorite_music: 'Ed Sheeran', favorite_book: 'To Kill a Mockingbird', favorite_game: 'The Last of Us', favorite_sport: 'Tennis', favorite_actor: 'Leonardo DiCaprio', favorite_actress: 'Natalie Portman', favorite_director: 'Martin Scorsese' },
        { id: '3', name: 'Jim', age: 22, address: '123 Pine Street, Chicago, Illinois, IL 60601', birth: '1992-01-01', hobby: 'Reading', favorite_color: 'Green', favorite_food: 'Salad', favorite_movie: 'The Shawshank Redemption', favorite_music: 'Taylor Swift', favorite_book: 'The Catcher in the Rye', favorite_game: 'The Elder Scrolls V: Skyrim', favorite_sport: 'Golf', favorite_actor: 'Tom Cruise', favorite_actress: 'Scarlett Johansson', favorite_director: 'Christopher Nolan' },
        { id: '4', name: 'Jill', age: 23, address: '123 Maple Street, Seattle, Washington, WA 98101', birth: '1993-01-01', hobby: 'Reading', favorite_color: 'Yellow', favorite_food: 'Sushi', favorite_movie: 'The Godfather', favorite_music: 'Ariana Grande', favorite_book: 'The Hobbit', favorite_game: 'The Last of Us Part II', favorite_sport: 'Basketball', favorite_actor: 'Al Pacino', favorite_actress: 'Meryl Streep', favorite_director: 'Steven Spielberg' },
        { id: '5', name: 'Jack', age: 24, address: '123 Oak Street, Miami, Florida, FL 33131', birth: '1994-01-01', hobby: 'Reading', favorite_color: 'Orange', favorite_food: 'Burger', favorite_movie: 'The Dark Knight Rises', favorite_music: 'Beyonce', favorite_book: 'The Great Gatsby', favorite_game: 'The Witcher 3', favorite_sport: 'Basketball', favorite_actor: 'Tom Hanks', favorite_actress: 'Meryl Streep', favorite_director: 'Steven Spielberg' },
      ]}
      columns={[
        ['姓名', 'name', { class: 'sticky left-0 shadow-lg bg-white' }],
        ['年龄', 'age'],
        ['地址', 'address'],
        ['出生日期', 'birth'],
        ['爱好', 'hobby'],
        ['喜欢的颜色', 'favorite_color'],
        ['喜欢的食物', 'favorite_food'],
        ['喜欢的电影', 'favorite_movie', { class: 'w-[5em] truncate', tipVisible: true }],
        ['喜欢的音乐', 'favorite_music'],
        ['喜欢的书', 'favorite_book'],
        ['喜欢的游戏', 'favorite_game'],
        ['喜欢的运动', 'favorite_sport'],
        ['喜欢的演员', 'favorite_actor'],
        ['喜欢的女演员', 'favorite_actress'],
        ['喜欢的导演', 'favorite_director'],
        ['操作', (row, index) => <button class="border border-gray-300 rounded-md px-2 py-1">编辑</button>, { class: 'sticky right-0 shadow-lg bg-white' }],
      ]}
    />
  )
})

export default DefaultTable
