import { createComponent } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { apiSeriesInfo, SeriesInfo } from '../share-api'
import { ref } from 'vue'
import { click, dramabuzz, dramabuzzLogo, dramawave, dramawaveLogo } from '../image/image'
import { goToApp } from 'src/lib/go-to-app'
import { isIos } from 'src/lib/ua'
import { appName } from 'src/lib/constants'
import { useLocale } from 'src/lib/use-locale'
import { useRouter } from 'vue-router'

export const SeriesPage = createComponent(null, () => {
  const router = useRouter()
  const series = ref<SeriesInfo>()
  const seriesId = router.currentRoute.value.params.seriesId as string
  const language = router.currentRoute.value.query.language as string
  const loading = ref<boolean>(true)
  const width = ref<number>()
  const height = ref<number>()
  const { t } = useLocale(language)
  const deepLink = `dramawave://dramawave.app?redirect=${encodeURIComponent('/detail?id=' + seriesId)}`
  const universalLink = `https://${appName === 'dramabuzz' ? 'thedramabuzz' : 'mydramawave'}.com?redirect=${encodeURIComponent('/detail?id=' + seriesId)}`

  void apiSeriesInfo(seriesId, language).then(res => {
    if (!res.data) return
    if (res.data.series_info && res.data.series_info.cover) {
      const image = new Image()
      image.src = res.data.series_info.cover
      image.onload = () => {
        width.value = image.width
        height.value = image.height
        loading.value = false
        series.value = res.data!.series_info
      }
    }
  })
  const handleClick = () => {
    // 使用 JavaScript 超时机制，检测应用是否已安装，已安装跳转到app，未安装跳转到appStoreLink
    // 实现H5唤端功能，当IOS时，使用universalLink,当Android时使用 deepLink 唤端
    goToApp(isIos() ? universalLink : deepLink)
  }

  return () => (
    !loading.value && (
      <div class="relative size-full bg-[#0b080b]">
        <div class="absolute left-0 top-0 z-0 h-auto w-full">
          <img src={series.value?.cover} class="relative z-0 h-auto w-full object-cover" />
          <div class="z-1 absolute left-0 top-0 size-full bg-gradient-to-b from-[#0B080B1A] to-[#0b080b] backdrop-blur-[5px]" />
        </div>
        <div class="z-up py-26 relative flex w-full flex-col items-center px-3">
          <div class="absolute left-1/2 top-10 flex h-10 w-full -translate-x-1/2 items-center justify-center">
            <img src={appName === 'dramabuzz' ? dramabuzzLogo : dramawaveLogo} class="h-full w-auto object-cover" />
            <img src={appName === 'dramabuzz' ? dramabuzz : dramawave} class="h-4/5 w-auto object-cover" />
          </div>
          <div class="flex w-full flex-1 items-center justify-center">
            <img src={series.value?.cover} class="h-auto w-3/5 rounded-lg border border-solid border-[rgba(253,_251,_252,_0.2)] object-cover" width={width.value} height={height.value} />
          </div>
          <div class="flex w-full flex-col items-center gap-3">
            <div class="mt-8 text-center text-lg leading-snug text-[#fdfbfc]">{series.value?.name}</div>
            <div class="line-clamp-5 break-words text-sm font-normal text-[#cccacb]">{series.value?.desc}</div>
          </div>
          <Button class="pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] fixed bottom-12 left-1/2 h-11 w-[calc(100%-1.5rem)] -translate-x-1/2 rounded-lg border-none bg-[var(--brand-6)] text-base leading-tight text-[rgba(253,_251,_252,_0.9)]" onClick={handleClick}>
            {t('share.watchNow')}
            <img src={click} class="absolute -bottom-[10px] right-4 h-9 w-11 animate-[blink_.8s_infinite] object-cover" />
          </Button>
        </div>
      </div>
    )
  )
})

export default SeriesPage
