/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, r } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Pager, transformNumber, Button, openDialog, Checkbox, showSuccessToast, showFailToast, Icon } from '@skynet/ui'
import { ref, reactive, onMounted } from 'vue'
import { set } from 'lodash-es'
import { apiGetUserList, apiEditUser, apiResetQrCode } from './user-api'
import { useRoleStore } from './use-role-store'
import { RoleEditDialog } from './role-edit-dialog'
import { UserDialog } from './user-dialog'
import { keepError } from 'src/lib/http-client'
import { useMenu } from 'src/modules/menu/use-menu'

type UserPageOptions = {
  props: {}
}

export const UserPage = createComponent<UserPageOptions>({
  props: {},
}, props => {
  const { getUserRoles } = useMenu()
  const { roles, getRoleList, statusOptions, setCurrentUser, checked, isCheckAll, checkedAllChange, checkboxChange, resetChecked } = useRoleStore()
  const pageInfo = reactive({
    page_index: 1,
    page_size: 20,
  })
  const total = ref(0)
  const loading = ref(false)
  const list = ref<M.IRoleUser[]>()
  const form = ref<M.IRoleUserParams>({
    source: 0,
    open_roles: [],
    username: '',
  })
  const Table = CreateTableOld<M.IRoleUser>()
  const QueryForm = CreateForm<M.IRoleUserParams>()
  const columns: TableColumnOld<M.IRoleUser>[] = [
    [() => (
      <Checkbox
        label=""
        disabled={list.value?.length === 0}
        modelValue={isCheckAll.value}
        onUpdate:modelValue={(value: boolean) => {
          checkedAllChange(value, list.value || [])
        }}
      />
    ),
    (row: M.IRoleUser) => {
      return (
        <Checkbox
          label=""
          modelValue={checked.value.some(c => c.user_id === row.user_id)}
          onUpdate:modelValue={(value: boolean) => {
            checkboxChange(value, row, list.value || [])
          }}
        />
      )
    },
    { class: 'w-[60px]' },
    ],
    ['用户ID', 'user_id', { class: 'w-[100px]' }],
    ['用户名称', 'username', { class: 'w-[140px]' }],
    ['角色', row => {
      return <div>{row.open_roles?.map(i => roles.value.find(r => r.id === i)?.name).join(',') || '-'}</div>
    }, { class: 'w-[140px]' }],
    ['用户类型', row => {
      const statusStyle = row.source === 1 ? 'badge badge-outline badge-primary' : 'badge badge-outline default'
      return <div class={`${statusStyle}`}>{ row.source === 1 ? '内部用户' : '外部用户'}</div>
    }, { class: 'w-[130px]' }],
    ['部门', 'department', { class: 'w-[120px]' }],
    ['状态', row => {
      const statusStyle = row.status === 1 ? 'badge badge-primary' : 'badge default'
      return <div class={`${statusStyle}`}>{ row.status === 1 ? '正常' : '已注销'}</div>
    }, { class: 'w-[130px]' }],
    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap justify-center">
            <Button class="btn btn-link btn-primary btn-sm" onClick={() => {
              const createDialog = openDialog({
                title: '编辑用户',
                mainClass: 'pb-0 px-5',
                body: () => <RoleEditDialog afterSave={afterSave} closeDialog={closeDialog} />,
              })

              function afterSave() {
                createDialog()
                void onQuery()
              }

              function closeDialog() {
                createDialog()
              }

              setCurrentUser({
                users: [row],
                open_roles: row.open_roles || [],
              })
            }}
            >
              编辑用户
            </Button>
            {row.status === 1
              ? (
                  <Button class="btn btn-link btn-primary btn-sm" onClick={() => onClickDeleteAccount(row)}
                  >注销用户
                  </Button>
                )
              : (
                  <Button class="btn btn-link btn-primary btn-sm" onClick={() => onClickRestoreAccount(row)}>
                    恢复用户
                  </Button>
                )}
            {
              row.source === 2 && getUserRoles()?.includes(1) ? (
                <Button class="btn btn-link btn-primary btn-sm" onClick={() => onResetQrCode(row)}
                >重置二维码
                </Button>
              ) : null
            }
          </div>

        )
      },
      { class: 'w-[180px] text-center' },
    ],
  ]

  async function onClickRestoreAccount(row: M.IRoleUser) {
    await apiEditUser({
      username: row.username,
      source: row.source,
      operate_type: 4,
      open_roles: row.open_roles,
    }).catch(keepError((error: any) => {
      showFailToast(error.response.data.message || '操作失败')
    }))
    void onQuery()
    showSuccessToast('操作成功')
  }

  function onClickDeleteAccount(row: M.IRoleUser) {
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>是否注销【{row.username}】</x-status-body>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" onClick={async () => {
              try {
                await apiEditUser({
                  username: row.username,
                  source: row.source,
                  operate_type: 3,
                  open_roles: row.open_roles,
                })
                void onQuery()
                showSuccessToast('操作成功')
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                hideDialog()
              }
            }}
            >确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const onResetQrCode = (row: M.IRoleUser) => {
    console.log(row, 'row')
    const btnLoading = ref(false)
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>确认重置二维码</x-status-body>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              try {
                await apiResetQrCode({
                  username: row.username,
                })
                showSuccessToast('操作成功')
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
              } finally {
                hideDialog()
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const getList = async () => {
    loading.value = true
    try {
      const { data } = await apiGetUserList({
        ...form.value,
        ...pageInfo,
      })
      list.value = data?.list
      total.value = data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const onQuery = async () => {
    pageInfo.page_index = 1
    resetChecked()
    await getList()
  }

  const onReset = async () => {
    pageInfo.page_index = 1
    form.value = {
      source: 0,
      open_roles: [],
      username: '',
    }
    await onQuery()
  }

  const onPageChange = async () => {
    await getList()
  }
  const onPageSizeChange = async (n: number) => {
    pageInfo.page_size = n
    await onQuery()
  }

  onMounted(async () => {
    await getRoleList()
    await getList()
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>用户管理</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              { label: '用户类型：', path: 'source', input: { type: 'select', options: statusOptions, autoInsertEmptyOption: false }, transform: transformNumber },
              { label: '角色：', path: 'role', input: { type: 'select', options: [
                {
                  value: 0,
                  label: '全部',
                },
                ...roles.value.map(roleObj => {
                  return {
                    value: roleObj.id,
                    label: roleObj.name,
                  }
                })], autoInsertEmptyOption: false }, transform: transformNumber },
              { label: '用户名称：', path: 'username', input: { type: 'text', placeholder: '请输入用户名称' } },
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex w-full items-center justify-end">
            <x-table-actions-right class="space-x-2">
              <Button class="btn btn-primary btn-sm" onClick={() => {
                setCurrentUser({})
                const createDialog = openDialog({
                  title: '新建用户',
                  mainClass: 'pb-0 px-5',
                  body: () => <UserDialog afterSave={afterSave} closeDialog={closeDialog} />,
                })
                function closeDialog() {
                  createDialog()
                }
                function afterSave() {
                  createDialog()
                  void onQuery()
                }
              }}
              >
                新建用户
              </Button>
              <Button disabled={checked.value.length === 0} class="btn btn-primary btn-sm" onClick={() => {
                setCurrentUser({
                  users: [...checked.value],
                  open_roles: checked.value[0].open_roles,
                })
                const createDialog = openDialog({
                  title: '修改角色',
                  mainClass: 'pb-0 px-5',
                  body: () => <RoleEditDialog afterSave={afterSave} closeDialog={closeDialog} />,
                })
                function closeDialog() {
                  createDialog()
                }
                function afterSave() {
                  createDialog()
                  void onQuery()
                }
              }}
              >批量修改角色
              </Button>
            </x-table-actions-right>
          </x-table-actions>
        ),
        table: () => (
          <Table
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.page_index} v-model:size={pageInfo.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default UserPage
