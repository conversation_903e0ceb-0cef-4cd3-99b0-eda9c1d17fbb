import { bindLoading, createComponent, fn, useValidator } from '@skynet/shared'
import { Button, CreateForm, Icon, openDialog, transformInteger } from '@skynet/ui'
import { FormItemGroup } from '@skynet/ui/form/form-types'
import { Fn } from '@vueuse/core'
import dayjs from 'dayjs'
import { cloneDeep, debounce, pick, set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, ref, watch } from 'vue'
import { z } from 'zod'
import { useAppAndLangOptions } from '../options/use-app-options'
import { apiGetHomeConfig, apiGetHomeConfigCategoryList, apiSaveHomeConfig, homeConfigApi } from './home-config-api'
import { useCreateHomeConfigStore } from './use-create-home-config-store'
type HomeConfigDialogOptions = {
  props: {
    configId?: M.HomeItemConfig['id'] | null
  }
  emits: {
    save: Fn
    cancel: Fn
  }
}
export const HomeConfigDialog = createComponent<HomeConfigDialogOptions>({
  props: {
    // type: 'add',
    configId: null,
  },
  emits: {
    save: fn,
    cancel: fn,
  },
}, (props, { emit }) => {
  const Form = CreateForm<M.HomeItemConfig>()
  const loading = ref<boolean>(false)
  const categoryList = ref<M.ContentTemplate[]>([])
  const store = useCreateHomeConfigStore()
  const formData = props.configId ? ref(store.defaultFormData) : store.formData

  const { appOptions, languageOptions } = useAppAndLangOptions(() => formData.value.app_id)
  const rules = z.object({
    name: z.string().min(1, {
      message: '请输入模块名称',
    }),
    app_id: z.number().min(1, {
      message: '请选择应用',
    }),
    language: z.string().min(1, {
      message: '请选择语言',
    }),
    type: z.number().min(1, {
      message: '请选择内容类型',
    }),
    category_id: z.number().min(1, {
      message: '请选择内容ID',
    }).optional(),
  })
  const keyword = ref<string>('')

  watch(() => formData.value.category_ids, () => {
    formData.value.category_id = formData.value.category_ids?.[0]
  })

  const searching = ref<boolean>(false)

  watch(() => [pick(formData.value, ['app_id', 'language', 'type']), keyword.value], debounce(() => {
    if (!formData.value.app_id || !formData.value.language || !formData.value.type) return
    const params = {
      ...pick(formData.value, ['app_id', 'language', 'type']),
      search_key: keyword.value,
    }
    params.app_id = Number(params.app_id)
    void bindLoading(apiGetHomeConfigCategoryList(params), loading).then(res => {
      if (!res.data) return
      categoryList.value = res.data.list
      if (categoryList.value.length > 0 && categoryList.value.findIndex(item => item.id === formData.value.category_id) > -1) {
        return
      }
      formData.value.category_ids = []
    })
  }, 300))

  const loadingConfig = ref<boolean>(false)
  onMounted(async () => {
    if (props.configId) {
      const res = await bindLoading(apiGetHomeConfig(props.configId), loadingConfig)
      if (!res.data) return
      formData.value = res.data.module_info
      if (formData.value.app_id) {
        formData.value.app_id = Number(formData.value.app_id)
      }
      formData.value.category_ids = formData.value.category_id ? [formData.value.category_id] : []
      formData.value.crowd_info = formData.value.crowd_info ? formData.value.crowd_info : 0
    }
  })

  const { error, validateAll } = useValidator(formData, rules)

  const confirmSave = () => {
    // 榜单需要判断短剧数量是否超过6个
    if (formData.value.type === 3 && formData.value.category_id) {
      const len = categoryList.value.find(item => item.id === formData.value.category_id)?.content_list_cnt
      if (len && len < 6) {
        const close = openDialog({
          customClass: 'pb-0',
          title: '提示',
          body: (
            <x-dialog class="flex flex-col gap-y-[25px]">
              <x-dialog-body>榜单内容小于6个短剧，App侧会有空缺，请确认是否继续？</x-dialog-body>
              <x-dialog-footer class="w-full flex justify-end gap-x-[10px]">
                <Button class="btn btn-default btn-sm" onClick={() => close()}>取消</Button>
                <Button class="btn btn-primary btn-sm" onClick={save}>确定</Button>
              </x-dialog-footer>
            </x-dialog>
          ),
        })
      }
    } else {
      save()
    }
  }

  const save = () => {
    if (validateAll()) {
      loading.value = true
      formData.value.created = formData.value.created ? dayjs(formData.value.created).unix() : undefined
      formData.value.updated = formData.value.updated ? dayjs(formData.value.updated).unix() : undefined
      // form.value.timing_time = form.value.timing_time ? dayjs(form.value.timing_time).unix() : undefined
      const params = cloneDeep(formData.value)
      delete params.category_ids
      delete params.created
      delete params.updated
      delete params.u_user_name
      delete params.c_user_name
      delete params.timing_time
      // save
      void apiSaveHomeConfig(params).then(() => {
        loading.value = false
        emit('save')
      }).finally(() => {
        loading.value = false
      })
    }
  }

  const cancel = () => {
    emit('cancel')
  }
  const keyOptions = ref<Array<{ label: string, value: string }>>([])
  onMounted(async () => {
    const response = await homeConfigApi.getTabContentKeys()
    if (!response.data) return
    keyOptions.value = response.data.list
  })

  return () => (
    <x-home-config-dialog class="block">
      {loadingConfig.value
        ? <x-loading class="block p-4">加载中...</x-loading>
        : (
            <>
              <x-home-config-dialog-body class="flex-1 flex flex-col overflow-y-auto px-1">
                <Form
                  class="w-full flex flex-nowrap flex-col"
                  data={formData.value}
                  onChange={(path, value) => {
                    set(formData.value, path, value)
                  }}
                  hasAction={false}
                  error={error.value}
                  items={[
                    [requiredLabel('模块名称'), 'name', { type: 'text' }],
                    [
                      'grid grid-cols-3 gap-x-4',
                      [requiredLabel('数据源key'), 'key', { type: 'select', options: keyOptions.value }],
                      [requiredLabel('是否展示模块名称'), 'show_title', { type: 'radio', options: [
                        { value: 1, label: '展示' },
                        { value: 2, label: '不展示' },
                      ] }, { transform: transformInteger }],
                      [requiredLabel('人群适配'), 'crowd_info', { type: 'select', options: [
                        { value: 0, label: '全部用户' },
                        { value: 1, label: '新用户' },
                        { value: 2, label: '老用户' },
                      ] }, { transform: transformInteger }],
                    ],
                    [
                      'grid grid-cols-3 gap-x-4',
                      [requiredLabel('选择生效应用'), 'app_id', { type: 'select', options: appOptions.value }, { transform: transformInteger }],
                      [requiredLabel('语言'), 'language', { type: 'select', options: languageOptions.value }],
                      [requiredLabel('内容类型'), 'type', { type: 'select', options: [
                        { value: 1, label: '横排专栏' },
                        { value: 2, label: '竖排专栏' },
                        { value: 3, label: '榜单' },
                        { value: 4, label: '3纵列' },
                      ] }, { transform: transformInteger }],
                    ],
                    [
                      <span>内容名称<small class="text-gray-500 ml-2">先选生效应用、语言、内容类型</small></span>,
                      'category_ids', {
                        type: 'multi-select',
                        disabled: !formData.value.app_id || !formData.value.language || !formData.value.type,
                        maxlength: 1,
                        popoverWrapperClass: 'z-popover-in-dialog',
                        search: { debounce: 20, placeholder: '输入名称搜索' },
                        options: searching.value
                          ? () => <x-loading class="block p-4">搜索中...</x-loading>
                          : categoryList.value.map(item => ({
                            value: item.id!,
                            label: item.name,
                          })),
                        'onUpdate:keyword': (value: string) => {
                          keyword.value = value
                        },
                      }],
                    ...(props.configId !== null
                      ? [
                          [
                            ['创建人', 'c_user_name', { type: 'custom', render: () => (
                              <x-create-user-name>
                                {formData.value.c_user_name}
                              </x-create-user-name>
                            ) }],
                            ['创建时间', 'created', { type: 'custom', render: () => <x-created>{formData.value.created ? dayjs.unix(+formData.value.created).format('YYYY-MM-DD HH:mm:ss') : '--'}</x-created> }],
                          ], [
                            ['更新人', 'u_user_name', { type: 'custom', render: () => (
                              <x-update-user-name>
                                {formData.value.u_user_name}
                              </x-update-user-name>
                            ) }],
                            ['更新时间', 'updated', { type: 'custom', render: () => <x-updated>{formData.value.updated ? dayjs.unix(+formData.value.updated).format('YYYY-MM-DD HH:mm:ss') : '--'}</x-updated> }],
                          ],
                        ]
                      : []) as FormItemGroup[],
                  ]}
                />
              </x-home-config-dialog-body>
              <x-home-config-dialog-footer class="flex justify-end gap-x-2">
                <Button class="btn btn-sm btn-default" onClick={cancel}>取消</Button>
                <Button class="btn btn-sm btn-primary" disabled={loading.value} onClick={confirmSave}>
                  {loading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                  提交
                </Button>
              </x-home-config-dialog-footer>
            </>
          )}

    </x-home-config-dialog>
  )
})
