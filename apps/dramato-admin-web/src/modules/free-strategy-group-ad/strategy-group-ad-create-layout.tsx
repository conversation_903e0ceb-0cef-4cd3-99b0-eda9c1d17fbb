import { createComponent, getQueriesWithParser, queryParsers } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { computed, FunctionalComponent, onUnmounted, ref, watch } from 'vue'
import { RouterLink, RouterView, useRoute, useRouter } from 'vue-router'
import { useStrategyGroupAdStore } from './strategy-group-ad-store'
import { apiGetStrategyGroupAdsDetails } from './strategy-group-ad-api'
// import router from 'src/router'
import { useListParamsStore } from './use-list-params-store'

const Item: FunctionalComponent<{ number: number }, { click: (e: Event) => void }> = (props, { slots, emit }) => (
  <MergeClass tag="x-item" baseClass="flex items-center gap-2" onClick={e => emit('click', e)}>
    <span class="size-8 bg-blue-500 rounded-full flex items-center justify-center text-white
      [.router-link-exact-active_&]:bg-red-600"
    >
      {props.number}
    </span>

    {slots.default?.()}
  </MergeClass>
)
export const StrategyGroupCreateLayout = createComponent(null, props => {
  const { formData, validateAllSteps, stepStatusList, validateAllStepsForIap, stepErrorForSolution } = useStrategyGroupAdStore()
  const isDataReady = ref<boolean>(false)

  watch(() => formData.value, () => {
    if (isDataReady.value) {
      void router.push({ path: router.currentRoute.value.path, query: { ...router.currentRoute.value.query, has_edit: 'true' } })
    }
  }, {
    deep: true,
  })

  const { id } = getQueriesWithParser({ id: queryParsers.string('') })
  const route = useRoute()
  const router = useRouter()

  watch(() => route.query.id,
    async v => {
      if (v) {
        isDataReady.value = false
        const res = await apiGetStrategyGroupAdsDetails({ id })
        if (!res.data) return
        formData.value = {
          ...res.data,
          scene_type: (Object.keys(res.data.ad_config || {}) || []).filter(k => {
            return res.data.ad_config[k] && res.data.ad_config[k].length > 0
          }),
        }
        setTimeout(() => {
          isDataReady.value = true
        })
      } else {
        isDataReady.value = true
        // resetFormData()
      }
    }, { immediate: true })

  const navList = route.path.includes('iaa')
    ? [
        ['目标条件', 'target'],
        ['商品配置', 'product'],
        ['任务计划', 'plan'],
      ]
    : [
        ['目标条件', 'target'],
        ['方案配置', 'solution'],
        ['任务计划', 'plan'],
      ]

  const isIAP = computed(() => route.path.includes('iap'))

  const onClickNav = (i: number) => (e: Event) => {
    isIAP.value ? validateAllStepsForIap() : validateAllSteps()
    console.log(stepErrorForSolution.value)
    if (i === 0) return
    if (stepStatusList.value[i - 1] !== 'valid') {
      e.stopPropagation()
      e.preventDefault()
    }
  }

  onUnmounted(() => {
    isDataReady.value = false
  })

  return () => (
    <x-strategy-group-create-page class="block my-4">
      <header class="flex justify-start items-center p-4 gap-4 sticky top-top-bar left-0 z-up bg-white border-b">
        <div class="breadcrumbs text-sm">
          <ul>
            <li>
              <span
                class="cursor-pointer"
                onClick={() => {
                  const { listParams, initListParams } = useListParamsStore()
                  listParams.value = { ...initListParams }
                  void router.push(`/free-ad-strategy-group/${isIAP.value ? 'iap' : 'iaa'}`)
                }}
              >
                {isIAP.value ? '广告' : 'IAA策略组'}
              </span>
            </li>
            <li>{isIAP.value ? route.query.id ? '编辑策略组' : '新建策略组' : route.query.id ? '编辑方案' : '新建方案'}</li>
          </ul>
        </div>
      </header>
      <div class="relative flex divide-x items-start">
        <aside class="w-64 bg-white grow-0 shrink-0 sticky top-[calc(var(--top-bar-height)_+_64px)] left-0
          flex justify-center items-start min-h-40 py-20"
        >
          <x-list class="block">
            {navList.map(([name, path], i) => (
              [
                i !== 0 && <hr class="border-t-0 border-b-0 border-l-0 border-r border-solid border-gray-500 w-0 h-10 ml-4" />,
                <RouterLink to={{ path, query: { ...route.query } }}>
                  <Item number={i + 1} onClick={onClickNav(i)}>
                    {name}
                  </Item>
                </RouterLink>]
            )).flat()}
          </x-list>
        </aside>
        <main class="bg-white  grow-1 shrink-1 w-full">
          {/* {JSON.stringify(formData.value)} */}
          <RouterView />
        </main>
      </div>

    </x-strategy-group-create-page>
  )
})

export default StrategyGroupCreateLayout
