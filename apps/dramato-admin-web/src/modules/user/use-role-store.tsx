import { apiGetRoleList, apiUserMenu } from './user-api'
import { ref } from 'vue'

const roles = ref<M.ISimpleRole[]>([])
const currentUser = ref<M.ICurrentUser>()
const checked = ref<M.IRoleUser[]>([])
const isCheckAll = ref(false)

const getRoleList = async () => {
  const res = await apiGetRoleList()
  roles.value = res.data?.list || []
}

const setCurrentUser = (user: M.ICurrentUser) => {
  currentUser.value = user
}

const statusOptions = [{
  label: '全部',
  value: 0,
}, {
  label: '内部用户',
  value: 1,
}, {
  label: '外部用户',
  value: 2,
}]

const checkedAllChange = (value: boolean, list: M.IRoleUser[]) => {
  isCheckAll.value = value
  if (isCheckAll.value) {
    checked.value = [...list]
  } else {
    checked.value = []
  }
}

const checkboxChange = (value: boolean, user: <PERSON><PERSON>ole<PERSON>, list: <PERSON><PERSON>IRoleUser[]) => {
  if (value) {
    if (!checked.value.some(row => row.user_id === user.user_id)) checked.value.push(user)
    if (list.length === checked.value.length) {
      isCheckAll.value = true
    }
  } else {
    const rowIndex = checked.value.findIndex(row => row.user_id === user.user_id)
    if (rowIndex !== -1) {
      checked.value.splice(rowIndex, 1)
      isCheckAll.value = false
    }
  }
}

const resetChecked = () => {
  checked.value = []
  isCheckAll.value = false
}

const toTree = (data: M.IUserMenuItem[]) => {
  const map: Record<number, M.IUserMenuItem> = {}
  const result: M.IUserMenuItem[] = []
  data.forEach(item => {
    map[item.id] = item
  })
  data.forEach(item => {
    const parent = map[item.parent_id]
    if (parent) {
      if (!parent.children) {
        parent.children = []
      }
      parent.children.push(item)
    } else {
      result.push(item)
    }
  })
  return result
}

const getUserMenu = async (roleIds: number[]) => {
  const res = await apiUserMenu({ role_ids: roleIds })
  const tempMenu = toTree(res.data?.list || [])
  return [
    {
      parent_id: -1,
      id: 0,
      path: '',
      title: '根目录',
      children: tempMenu,
    },
  ]
}

export const useRoleStore = () => {
  return {
    roles,
    getRoleList,
    statusOptions,
    currentUser,
    setCurrentUser,
    checked,
    isCheckAll,
    checkedAllChange,
    checkboxChange,
    resetChecked,
    getUserMenu,
  }
}
