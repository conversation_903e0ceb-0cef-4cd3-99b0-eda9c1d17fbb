import { h, computed, ref, watch, onMounted } from 'vue'
import { closeDialog, showDialog } from 'vant'
import { useSeriesItem } from './use-series-item'
import { T as TL } from '~/modules/i18n/login.i18n'
import { getUser } from '../common/user'
import { useMyWallet } from '../wallet/use-my-wallet'
import AnonymousDialog from '~/modules/payment/anonymous-dialog.vue'
import ReleaseRoundCover from '~/modules/series/release-round-cover.vue'
import { h5Track } from '~/lib/h5-track'
import { useRoute, useRouter } from 'vue-router'
import { useStatistics } from '../statistics/statistics'
import { closeCurrentLimitDialog, openLimitDialog } from '~/modules/watch-history/component/drama-card'

export function useSecondCover(series_id: string, onClickUnlock: (episodeId?: string) => void) {
  const { seriesItem, currentEpisodeId, getCurrentEpisode, unlockOverlayVisible, productPanelVisible, anonymousUserLoginCheck } = useSeriesItem(series_id)
  const { sessionId } = useStatistics()
  // const route = useRoute()
  const searchParams = new URLSearchParams(window.location.search);
  const from_page = computed(() => searchParams.get('from_page') ?? '')
  const currentEpisode = computed(() => getCurrentEpisode())
  const secondCoverVisible = ref(false)
  const { wallet } = useMyWallet()
  const me = getUser()
  // const router = useRouter()
  // onMounted(() => {
  //   void fetchWallet()
  // })
  watch(() => currentEpisode.value, async episode => {
    secondCoverVisible.value = false
    if (episode) {
      // 未登录，且没有足够的钱来解锁
      if (episode?.unlock !== true) {
        if (me.user_type === 0 && (wallet.value?.cash_balance || 0) + (wallet.value?.bonus_balance || 0) < episode.episode_price) {
          productPanelVisible.value = true
          return
        }
        // 如果未登录，则弹窗, 之前弹过，还要弹
        if (!anonymousUserLoginCheck.value) {
          const result = await checkLoginStatus() // 点击取消，会继续执行后面的代码
          if (!result) return
        }
      }
      if (checkFirst()) return
      if (episode?.unlock !== true) {
        if (wallet.value?.auto_unlock === 1) {
        // 尝试自动解锁
          void onClickUnlock()
        } else {
          unlockOverlayVisible.value = true
        }
      }
    }
  }, { immediate: true })

  /**
   * 首发剧二次弹窗
   */
  function renderSecondCover() {
    return secondCoverVisible.value && (
      <ReleaseRoundCover onClose={() => {
        secondCoverVisible.value = false
        h5Track('video', 'second_block', 'click', {
          video_id: currentEpisodeId.value,
          series_id: series_id,
          from: from_page.value,
          pop_type: 4,
          pop_scene: 'video',
          session_id: sessionId.value,
          video_type: 'charge',
        })
      }} />
    )
  }

  return {
    renderSecondCover,
  }

  async function checkLoginStatus() {
    return new Promise((resolve, reject) => {
      if (me.user_type !== 0) return resolve(true) // 已登录
      if (anonymousUserLoginCheck.value) return resolve(true) // 已弹过
      anonymousUserLoginCheck.value = true
      const video_id = currentEpisodeId.value
      const video_type = seriesItem.value?.free ? 'free' : 'charge'
      const pop_scene = 'video'

      h5Track('hint', 'pop', 'show', {
        event: 'hint_pop_show',
        from: from_page.value,
        pop_type: 1, // 支付后弹窗
        pop_scene: pop_scene,
        series_id: series_id,
        video_id: video_id,
        video_type: video_type,
        session_id: sessionId.value,
      })
      void showDialog({
        message: () => h(AnonymousDialog, {
          title: TL.login.secureYou.ref.value,
          message: TL.login.bindAccount.ref.value,
          confirmText: TL.login.goToSign.ref.value,
          cancelText: TL.login.maybeLater.ref.value,
          onClose: () => {
            h5Track('hint', 'pop_close', 'click', {
              event: 'hint_pop_close_click',
              from: from_page.value,
              pop_type: 1, // 支付后弹窗
              pop_scene: pop_scene,
              series_id: series_id,
              video_id: video_id,
              video_type: video_type,
              session_id: sessionId.value,
            })
            closeDialog()
            resolve(true)
            // location.href = location.origin + decodeHelper(from?.toString() || '')
          },
          onConfirm: () => {
            h5Track('hint', 'pop_sign_in', 'click', {
              event: 'hint_pop_sign_in_click',
              from: from_page.value,
              pop_type: 1, // 支付后弹窗
              pop_scene: pop_scene,
              series_id: series_id,
              video_id: video_id,
              video_type: video_type,
              session_id: sessionId.value,
            })
            window.location.href = `/login?from=${window.location.pathname}&series_id=${series_id}&video_id=${video_id}&video_type=${video_type}&pop_scene=${pop_scene}`
            // void router.push({ path: '/login', query: { from: route.path, series_id: series_id, video_id, video_type, pop_scene } })
            closeDialog()
            reject(false)
          },
        }),
        showConfirmButton: false,
        className: 'p-0',
      })
    })
  }

  function checkFirst() {
    const episode_price = (currentEpisode.value?.episode_price || 0)
    const walletCoins = (wallet.value?.cash_balance || 0) + (wallet.value?.bonus_balance || 0)
    const isFirst = seriesItem.value?.release_round === 1
    const vipVideType = currentEpisode?.value?.video_type === 'charge'
    const isVip = wallet?.value?.vip_level > 0
    const unlock = currentEpisode.value?.unlock
    const h5_available = currentEpisode?.value?.h5_available
    closeCurrentLimitDialog()

    // 首播剧 && （付费剧集 && 余额 >= 该集付费金额 || vip）
    if (isFirst && seriesItem.value
      && ((isVip && vipVideType && !h5_available) || (!unlock && !h5_available && walletCoins && episode_price >= 50 && walletCoins >= episode_price))
    ) {
      openLimitDialog(seriesItem.value, from_page.value, sessionId.value, () => {
        secondCoverVisible.value = true
        h5Track('video', 'first_block', 'close', {
          video_id: currentEpisodeId.value,
          series_id: series_id,
          from: from_page.value,
          pop_type: 3,
          session_id: sessionId.value,
          pop_scene: 'video',
          video_type: 'charge',
        })
        h5Track('video', 'second_block', 'show', {
          video_id: currentEpisodeId.value,
          series_id: series_id,
          from: from_page.value,
          pop_type: 4,
          session_id: sessionId.value,
          pop_scene: 'video',
          video_type: 'charge',
        })
      })
      return true
    }
    return false
  }
}
