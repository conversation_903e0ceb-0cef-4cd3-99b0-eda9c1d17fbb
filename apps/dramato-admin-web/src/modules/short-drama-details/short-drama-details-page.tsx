import { createComponent, mc } from '@skynet/shared'
import { Wrapper } from 'src/layouts/wrapper'
import { useRoute } from 'vue-router'
import { listingStatusList } from '../short-drama/short-drama-api'
import { Icon } from '@skynet/ui/icon/icon'
import router from 'src/router'
import { Button, DateTime, Input, showAlert } from '@skynet/ui'
import { useShortDramaStore } from '../short-drama/short-drama-store'
import dayjs from 'dayjs'
export const ShortDramaDetails = createComponent(null, () => {
  const { params: { drama_id }, query: { type } } = useRoute()
  const { dramaDetails, listStatus, hasTime, time, startPayingEpisodes, episodesPrice, freeEndTime, freeStartTime, isFree, loading, platform_sync_list, unshelve_reason, formatTime, getDetails, updateDramasListingStatus, updateDramasPrice, resetStore } = useShortDramaStore()

  getDetails(drama_id as unknown as number)

  const back = () => {
    resetStore()
    void router.push('/short-drama')
  }

  const save = () => {
    if (!dramaDetails.value) return
    if (type === 'listing') {
      const app_id = dramaDetails.value?.app_id || ''
      updateDramasListingStatus(app_id, false, true, fails => {
        if (fails && fails.length > 0) {
          showAlert(fails[0].msg, 'error')
        } else {
          showAlert('保存成功')
          resetStore()
          void router.push('/short-drama')
        }
      })
    } else if (type === 'price') {
      updateDramasPrice(false, true, fails => {
        if (fails && fails.length > 0) {
          showAlert(fails[0].msg, 'error')
        } else {
          showAlert('保存成功')
          resetStore()
          void router.push('/short-drama')
        }
      })
    }
  }

  return () => (
    <Wrapper>
      {
        dramaDetails.value
        && (
          <>
            <div class="flex w-full items-center gap-2 border-b border-gray-200 pb-4">
              <Icon class="size-5 cursor-pointer" name="material-symbols-light:arrow-back-ios" onClick={back} />
              <div class="text-xl  text-gray-900">{dramaDetails.value.app_name + ' > ' + dramaDetails.value.language_version_name + ' > ' + dramaDetails.value.series_key + ' - ' + dramaDetails.value.title}</div>
            </div>
            <div class="flex w-full flex-col gap-4">
              <div class="w-full">
                <div class="flex w-full items-center gap-2 text-base font-bold">
                  <Icon name="uis:web-section-alt" />
                  基本信息
                </div>
                <div class="flex w-full gap-8 px-10 py-4">
                  <div class="flex w-3/5 flex-col gap-4">
                    <div class="flex items-center justify-between">
                      <div class="flex w-1/2 gap-2">
                        <div class="shrink-0">应用名称：</div>
                        <div class="truncate">{dramaDetails.value.app_name}</div>
                      </div>
                      <div class="flex w-1/2 gap-2">
                        <div>发行语言：</div>
                        <div>{dramaDetails.value.language_version_name}</div>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex w-1/2 gap-2">
                        <div>短剧ID：</div>
                        <div>{dramaDetails.value.series_key}</div>
                      </div>
                      <div class="flex w-1/2 gap-2">
                        <div class="shrink-0">短剧名称：</div>
                        <div class="truncate">{dramaDetails.value.title}</div>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex gap-2">
                        <div>集数：</div>
                        <div>{dramaDetails.value.episodes_number}</div>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex gap-2">
                        <div class="shrink-0">简介：</div>
                        <div>{dramaDetails.value.description}</div>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex gap-2">
                        <div class="shrink-0">内容标签：</div>
                        <div>{dramaDetails.value.labels}</div>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex gap-2">
                        <div>连载状态：</div>
                        <div>{dramaDetails.value.serialize_status === 1 ? '连载中' : '已完结'}</div>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex w-1/2 gap-2">
                        <div>授权时间：</div>
                        <div>{formatTime(dramaDetails.value.publish_time)}</div>
                      </div>
                      <div class="flex w-1/2 gap-2">
                        <div>授权人：</div>
                        <div>{dramaDetails.value.publish_user_name}</div>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex w-1/2 gap-2">
                        <div>更新时间：</div>
                        <div>{formatTime(dramaDetails.value.updated_time)}</div>
                      </div>
                      <div class="flex w-1/2 gap-2">
                        <div>更新人：</div>
                        <div>{dramaDetails.value.operator_name}</div>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex w-1/2 gap-2">
                        <div>资源类型：</div>
                        <div>{dramaDetails.value.resource_type === 0 ? '- 空 -' : dramaDetails.value.resource_type === 1 ? '本土' : '翻译'}</div>
                      </div>
                    </div>
                  </div>
                  <img src={dramaDetails.value.cover_url} class="h-auto w-[260px] object-cover" />
                </div>
              </div>
              <div>
                <div class="flex w-full items-center gap-2 text-base font-bold">
                  <Icon name="uis:web-section-alt" />
                  上架信息
                </div>
                <div class="flex w-full gap-8 px-10 py-4">
                  <label class="flex items-center gap-2">
                    <span class="shrink-0">上架状态：</span>
                    {
                      type === 'listing'
                        ? (
                            <select
                              class="select select-bordered select-sm w-full max-w-xs"
                              v-model={listStatus.value}
                            >
                              {listingStatusList.map(option => (
                                <option value={option.code} disabled={option.code === 1}>{option.name}</option>
                              ))}
                            </select>
                          )
                        : listingStatusList.find(item => dramaDetails.value && item.code === dramaDetails.value.listing_status)?.name
                    }
                  </label>
                  {
                    (listStatus.value === 2 || listStatus.value === 4) && (
                      <>
                        <label class="flex items-center gap-2">
                          <input class="checkbox checkbox-sm checkbox-primary" type="checkbox" v-model={hasTime.value} disabled={type !== 'listing'} />
                          <span class="shrink-0">{listStatus.value === 2 ? '定时上架' : '定时下架'}</span>
                        </label>
                        {
                          type === 'listing'
                            ? <Input inputClass="input input-bordered input-sm w-full max-w-xs" format="YYYY-MM-DDTHH:mm:ss+08:00" type="datetime" v-model={time.value} disabled={type !== 'listing' || !hasTime.value} />
                            : formatTime(listStatus.value === 2 ? dramaDetails.value.listing_time : dramaDetails.value.removal_time)
                        }
                      </>
                    )
                  }
                </div>
                {type === 'listing' && import.meta.env.VITE_DRAMA_WAVE_APP_ID.split(',').includes('' + dramaDetails.value.app_id)
                  ? (
                      <div class="px-10 pb-4">
                        <label class="flex items-center gap-2">
                          <span class="shrink-0">双端同步：</span>
                          <input class="checkbox checkbox-sm checkbox-primary" type="checkbox" v-model={platform_sync_list.value} />
                        </label>
                      </div>
                    )
                  : null}
                {
                  type === 'listing' && listStatus.value === -1
                    ? (
                        <div class="px-10 pb-4">
                          <label class="flex items-start gap-2">
                            <span class="shrink-0">下架原因：</span>
                            <textarea class="textarea textarea-bordered textarea-sm w-full max-w-xs" placeholder="请输入下架原因（必填）" v-model={unshelve_reason.value} />
                          </label>
                        </div>
                      )
                    : null
                }
              </div>
              <div class="flex flex-col gap-y-4">
                <div class="flex w-full items-center gap-2 text-base font-bold">
                  <Icon name="uis:web-section-alt" />
                  定价配置
                </div>
                <div class="flex w-full items-center gap-8 px-10">
                  <div class="flex items-center gap-x-2">
                    <div class="shrink-0">免费上线时间：</div>
                    <DateTime value={(dramaDetails.value.free_online_time || 0) * 1000} format="YYYY-MM-DD HH:mm:ss" />
                  </div>
                </div>
                <div class="flex w-full items-center gap-8 px-10">
                  <div class="flex items-center gap-x-2">
                    <div class="shrink-0">免费时间段：</div>
                    {
                      type === 'price'
                        ? (
                            <>
                              <input
                                step={1}
                                class="input input-bordered input-sm w-full max-w-xs"
                                type="datetime-local"
                                value={freeStartTime.value ? dayjs(freeStartTime.value * 1000).format('YYYY-MM-DD HH:mm:ss') : 0}
                                onInput={(event: Event) => freeStartTime.value = dayjs((event.target as HTMLInputElement).value).unix()}
                              />
                              ~
                              <input
                                step={1}
                                min={freeStartTime.value ? dayjs(freeStartTime.value * 1000).format('YYYY-MM-DD HH:mm:ss') : dayjs().format('YYYY-MM-DD HH:mm:ss')}
                                class="input input-bordered input-sm w-full max-w-xs"
                                type="datetime-local"
                                value={freeEndTime.value ? dayjs(freeEndTime.value * 1000).format('YYYY-MM-DD HH:mm:ss') : 0}
                                onInput={(event: Event) => freeEndTime.value = dayjs((event.target as HTMLInputElement).value).unix()}
                              />
                            </>
                          )
                        : (
                            <>
                              <DateTime value={(freeStartTime.value || 0) * 1000} format="YYYY-MM-DD HH:mm:ss" />
                              ~
                              <DateTime value={(freeEndTime.value || 0) * 1000} format="YYYY-MM-DD HH:mm:ss" />
                            </>
                          )
                    }
                    {
                      isFree.value ? <div class="ml-2 shrink-0 bg-green-500 p-1 text-white">生效中</div> : <div class="ml-2 shrink-0 bg-gray-500 p-1 text-white">未生效</div>
                    }
                  </div>
                </div>
                <div class={mc('flex gap-8 px-10 w-full', isFree.value ? 'opacity-40' : '')}>
                  <label class="flex items-center">
                    <span class="mr-2 shrink-0">开始付费集数：</span>
                    {
                      type === 'price'
                        ? (
                            <Input
                              type="number"
                              class="w-[200px]"
                              inputClass="input input-bordered input-sm w-full max-w-xs"
                              v-model={startPayingEpisodes.value}
                            />
                          )
                        : dramaDetails.value.start_paying_episodes
                    }
                  </label>
                  <label class={mc('flex items-center', isFree.value ? 'opacity-40' : '')}>
                    <span class="mr-2 shrink-0">单集定价：</span>
                    {
                      type === 'price'
                        ? (
                            <Input
                              type="number"
                              class="w-[200px]"
                              inputClass="input input-bordered input-sm w-full max-w-xs pr-16"
                              v-model={episodesPrice.value}
                              placeholder="1-10000的整数"
                              min={1}
                              max={10000}
                              v-slots={{
                                suffix: () => <span class="text-gray-800">金币/集</span>,
                              }}
                            />
                          )
                        : parseInt(dramaDetails.value.episodes_price + '') + '金币/集'
                    }
                  </label>
                </div>
                {type === 'price' && import.meta.env.VITE_DRAMA_WAVE_APP_ID.split(',').includes('' + dramaDetails.value.app_id)
                  ? (
                      <div class="px-10">
                        <label class="flex items-center gap-2">
                          <span class="shrink-0">双端同步：</span>
                          <input class="checkbox checkbox-sm checkbox-primary" type="checkbox" v-model={platform_sync_list.value} />
                        </label>
                      </div>
                    )
                  : null}
              </div>
            </div>
          </>
        )
      }
      <div class="flex items-center justify-end gap-4 px-10">
        <Button class="btn btn-default btn-sm" onClick={back}>返回</Button>
        <Button class="btn btn-primary btn-sm" disabled={loading.value} onClick={save}>
          {
            loading.value && <Icon class="size-5" name="line-md:loading-twotone-loop" />
          }
          保存
        </Button>
      </div>
    </Wrapper>
  )
})

export default ShortDramaDetails
