import { httpClient } from 'src/lib/http-client'

export type Page = {
  page_index?: number
  page_size?: number
}

export const playApi = {
  getList: async ({ page_index = 1, page_size = 10 }: Page) => {
    const { data } = await httpClient.post<ApiResponse<{ list: M.Play[], total: number }>>('/open_platform/play/list', { page_index, page_size })
    return data
  },
  getItem: async (play_key: string) => {
    const { data } = await httpClient.post<ApiResponse<M.Play>>('/open_platform/play/detail', { play_key }, {
      transformResponseData: {
        'data.label_ids': v => v.split(',').map((s: string) => Number(s)),
      },
    })
    return data
  },
  createOrUpdate: (data: M.Play) => {
    return httpClient.post<ApiResponse<M.Play>>('/open_platform/play/create_or_update', data, {
      transformRequestData: {
        label_ids: v => v.join(','),
      },
      transformResponseData: {
        'data.label_ids': v => v.split(',').map((s: string) => Number(s)),
      },
    }).then(res => res.data)
  },
  submitForAudit: (play_key: string) => {
    return httpClient.post('/open_platform/play/audit', { play_key, audit_operate: 1 })
  },
}
