declare namespace M {
  interface PushNotificationSearchOption {
    app_id?: number
    state?: number // 任务状态，1：待发送，20：发送中，40：已完成
    search_val?: string
    page_info?: {
      page_index?: number
      page_size?: number// 选填参数
    }
  }

  interface PushNotification {
    id?: number
    title?: string
    content?: string
    target_app_names?: string// 应用名称
    start_ts?: number// 开始时间戳
    end_ts?: number// 结束时间戳
    state?: number // 任务状态，1：待发送，20：发送中，40：已完成
    send_cnt?: number // 发送条数
    created?: number // 创建时间戳
    creator?: string
    updated?: number // 修改时间戳
    updator?: string
    link?: string
    image_url?: string
    priority?: number// 优先级：1：紧急，2：高，3：中，4：低，5：无
    target_app_ids?: string | number[]
    ageing_type?: number// 时效类型，1：立即发送，2：定时发送
    timed_ts?: number | string // 定时发送时间，北京时间时间戳
    target_user_type?: number// 目标用户类型，1：全部用户，2：指定用户token列表
    user_identify_val?: string // token列表,逗号分割
    android_notify_channel?: string// 安卓通知渠道，短剧通知:Drama,运营通知:Operation

    horizontal_image_url?: string// 横图/大图URL
    has_notify_btn?: boolean// 是否有按钮
    notify_btn_bg_color?: string// 按钮背景色
    last_n_day_register_start?: number// 最近几天注册
    last_n_day_register_end?: number// 最近几天注册结束天数

    tips?: string
    tips_style?: string // 如果tips不为空，取值范围"purple", "blue", "green", "orange", "red"
    has_notify_btn2?: boolean
    notify_btn2_text?: string // 如果has_notify_btn2=true，不能为空
    notify_btn2_deep_link?: string
  }

  interface PushNotificationListRequestResponse {
    total: number
    list: PushNotification[]
  }
}
