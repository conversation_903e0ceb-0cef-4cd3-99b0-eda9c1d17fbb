import { createComponent } from '@skynet/shared'
import { RouterView } from 'vue-router'
import { useDialogStore, usePopoverStore } from '@skynet/ui'
import { useAlertStore } from '@skynet/ui/alert/use-alert-store'
import { usePopover2Store } from '@skynet/ui/popover/use-popover-2-store'
import { Component, KeepAlive } from 'vue'

type RouterViewSlot = {
  Component: Component
}
export const App = createComponent(null, () => {
  const { renderDialogs } = useDialogStore()
  const { renderPopovers } = usePopoverStore()
  const { renderPopovers: renderPopovers2 } = usePopover2Store()
  const { renderAlerts } = useAlertStore()
  return () => (
    <x-app class="block">
      <RouterView>
        {{
          default: ({ Component }: RouterViewSlot) => Component ? <KeepAlive>{Component}</KeepAlive> : null,
        }}
      </RouterView>
      {/* 所有动态组件都挂载到 mountRoot 中 */}
      <div class="" id="mountRoot">
        { renderDialogs() }
        { renderPopovers() }
        { renderPopovers2() }
        <div class="fixed space-y-4 top-[100px] z-alert left-1/2 transform -translate-x-1/2 items-center justify-center flex-col flex">
          {renderAlerts()}
        </div>
      </div>

    </x-app>
  )
})
