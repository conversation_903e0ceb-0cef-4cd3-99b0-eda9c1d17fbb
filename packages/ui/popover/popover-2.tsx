import { autoPlacement, autoUpdate, computePosition, offset, Placement } from '@floating-ui/dom'
import { createComponent, fn, mc, required, SlotFn } from '@skynet/shared'
import { Fn, unrefElement } from '@vueuse/core'
import { CSSProperties, ref, watch, useId } from 'vue'
import { MergeClass } from '../ui'

export type Popover2Options = {
  props: {
    id: number
    visible?: boolean
    triggerType?: 'click' | 'hover' | 'focus' | 'manual'
    triggerElement: () => (HTMLElement | null)
    /**
     * 相对偏移的元素。
     * 如果不传，则使用 triggerElement
     */
    offsetElement?: null | (() => HTMLElement | null)
    placement?: Placement // 弹窗位置
    /**
     * 如果空间不足，允许的位置
     */
    allowedPlacements?: Placement[]
    /**
     * 弹出内容的定位方式
     */
    popPositionStyle?: 'fixed' | 'absolute'
    offset?: number // 弹窗偏移量
    content?: SlotFn | null
  }
  slots: {
    default: SlotFn
  }
  emits: {
    remove: Fn
    mouseEnter: Fn
    mouseLeave: Fn
  }
}

export const Popover2 = createComponent<Popover2Options>({
  props: {
    id: required,
    triggerType: 'hover',
    triggerElement: required,
    offsetElement: null,
    popPositionStyle: 'fixed',
    content: null,
    placement: 'bottom',
    allowedPlacements: [],
    offset: 20,
    visible: false,
  },
  emits: {
    remove: fn,
    mouseEnter: fn,
    mouseLeave: fn,
  },
}, (props, { emit, slots }) => {
  const popoverRef = ref()
  const style = ref<CSSProperties>({})

  const update = () => {
    const trigger = props.triggerElement?.()
    if (!trigger) return
    const wrapperEl = unrefElement(popoverRef)
    const el = props.offsetElement?.() ?? trigger
    if (!wrapperEl) return
    void computePosition(el, wrapperEl, {
      strategy: 'fixed',
      placement: props.placement ?? 'bottom',
      middleware: [
        autoPlacement({ allowedPlacements: props.allowedPlacements }),
        offset(props.offset),
      ].filter(Boolean),
    }).then(({ x, y, placement, middlewareData }) => {
      Object.assign(style.value, {
        transform: `translate(${x}px, ${y}px)`,
      })
    })
  }
  watch(() => props.visible, visible => {
    if (!visible) return
    update()
  })

  watch(() => props.triggerElement?.(), trigger => {
    if (!trigger) return
    const wrapperEl = unrefElement(popoverRef)
    if (!wrapperEl) return
    autoUpdate(props.offsetElement?.() ?? trigger, wrapperEl, update, { animationFrame: false })
  }, { immediate: true })

  return () => {
    const content = props.content?.() ?? slots.default?.()
    if (!content) return null
    return (
      <MergeClass tag="x-popover" ref={popoverRef} style={style.value} baseClass={
        mc('left-0 top-0 drop-shadow z-popover border border-gray-300 bg-white px-2 py-1 rounded-md', props.popPositionStyle, props.visible ? 'block' : 'hidden', `v-popover-${props.id}`)
      } onMouseenter={() => emit('mouseEnter')} onMouseleave={() => emit('mouseLeave')}
      >
        {content}
      </MergeClass>
    )
  }
})
