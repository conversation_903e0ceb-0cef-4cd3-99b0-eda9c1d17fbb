/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { onMounted, onUnmounted, watch } from 'vue'
import { defaultClipValue } from '../constant'
import { ref } from 'vue'
import { useStorage } from '@vueuse/core'
import { getSubtitleContent } from 'src/modules/common/subtitle/until'

type ISubtitle = {
  kind: string
  default: boolean
  type: string
  label: string
  src: string
  srclang: string
}

type M3u8PlayerOptions = {
  props: {
    url: string
    subtitles?: {
      language: string
      type: string
      subtitle: string
    }[]
    region?: [number, number, number, number]
    currentLanguage?: string
    id?: string
    showSubtitle?: boolean
    height?: string
    hideControlBar?: boolean
    width?: string
    useStorageSetting?: boolean
  }
  emits: {
    playerReady?: () => void
  }
}

export const M3u8Player = createComponent<M3u8PlayerOptions>({
  props: {
    url: '',
    subtitles: [],
    region: defaultClipValue,
    currentLanguage: '',
    id: 'J_prismPlayer',
    showSubtitle: true,
    height: '550px',
    width: '309px',
    hideControlBar: false,
    useStorageSetting: true,
  },
  emits: {
    playerReady: () => {},
  },
}, (props, { emit }) => {
  const state = useStorage('player-store', {
    speed: 1,
    volume: 0.8
  })

  const width = 309
  let player: any = null
  const subtitles = ref<ISubtitle[]>([])

  function srtToVtt(srtContent: string) {
    const vttContent = 'WEBVTT\n\n'
    const srtLines = srtContent.split('\n')
    let vtt = vttContent
    let subtitleBlock = ''

    for (let i = 0; i < srtLines.length; i++) {
      const timeMatch = srtLines[i].match(/(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})/)
      // 数字下是时间轴的才是序号
      if (srtLines[i].match(/^\d+$/) && i < srtLines.length - 1 && srtLines[i + 1].match(/(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})/)) {
        // Skip subtitle index
        continue
      }

      if (timeMatch) {
        const startTime = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}.${timeMatch[4]}`
        const endTime = `${timeMatch[5]}:${timeMatch[6]}:${timeMatch[7]}.${timeMatch[8]}`
        subtitleBlock += `${startTime} --> ${endTime}\n`
      } else if (srtLines[i].trim()) {
        subtitleBlock += `${srtLines[i].trim()}\n`
      } else {
        vtt += subtitleBlock + '\n'
        subtitleBlock = ''
      }
    }

    // Append the last subtitle block if it exists
    if (subtitleBlock.trim()) {
      vtt += subtitleBlock + '\n'
    }

    return vtt
  }

  async function loadAndConvertSrtToVtt(url: string) {
    const srtContent = await getSubtitleContent(url)
    return srtToVtt(srtContent)
  }

  const getPlayer = () => {
    // 先销毁现有的播放器实例
    if (player) {
      player?.dispose && typeof player?.dispose === 'function' && player?.dispose()
      player = null
    }

    // 创建新的播放器实例
    player = new (window as any).Aliplayer({
      id: props.id,
      width: props.width,
      height: props.height,
      source: props.url,
      language: 'zh-cn',
      enableH265: true,
      definition: 'FD,LD,HD',
      defaultDefinition: 'FD',
      useH5Prism: true,
      extraInfo:{
        crossOrigin:"anonymous"
      },
      skinLayoutIgnore: props.hideControlBar ? [
        'bigPlayButton', // 隐藏大播放按钮
        'controlBar', // 隐藏控制条上的全屏按钮（通过点运算符进行子组件选择）
        'H5Loading'
      ] : [],
      extLanguageTexts: {
        'zh-cn': {
          HD: '1080p',
          LD: '720p',
          FD: '540p',
        },
      },
    }, (e: any) => {
      player = e
      player.on('ready', () => {
        emit('playerReady', player)
      })

      if (props.useStorageSetting) {
        player.on('settingSelected', (e: any) => {
          state.value.speed = e.paramData.key
        })
        player.on('timeupdate', (e: any) => {
          const v = player.getVolume()
          if (v !== state.value.volume) {
            state.value.volume = v
          }
        })
        player.setSpeed(state.value.speed)
        player.setVolume(state.value.volume)
      }
    })
    return player
  }

  const setSubtitles = async () => {
    if (!props.showSubtitle) return
    const ps = props.subtitles.filter(row => row.subtitle && row.subtitle.indexOf('http') === 0).map(async row => {
      const vttContent = await loadAndConvertSrtToVtt(row.subtitle)
      const blob = new Blob([vttContent], { type: 'text/vtt' })
      const vttUrl = URL.createObjectURL(blob)
      return vttUrl
    })
    await Promise.all(ps).then(urls => {
      subtitles.value = props.subtitles.map((row, index) => {
        // fix: * 用来修复 aliplayer switch不能传id
        return { kind: 'subtitles', default: props.currentLanguage ? ' ' + props.currentLanguage === ' ' + row.language : index === 0, type: 'srt', label: ' ' + row.language, src: urls[index], srclang: ' ' + row.language }
      })
      player.setTextTracks(subtitles.value)
      if (props.currentLanguage && player?._ccService && player?._ccService.switch && typeof player?._ccService.switch === 'function') player?._ccService.switch(' ' + props.currentLanguage)
    })
  }

  onUnmounted(() => {
    try {
      player?.dispose && typeof player?.dispose === 'function' && player?.dispose()
      player = null
    } catch (error) {
      console.log(error, 'error')
    }
  })

  watch(() => [props.url], async () => {
    player = await getPlayer()
    void setSubtitles()
  })

  watch(() => [props.subtitles], () => {
    void setSubtitles()
  })

  watch(() => props.currentLanguage, (newVal: string) => {
    if (!props.showSubtitle) return
    if (newVal && player?._ccService && player?._ccService.switch && typeof player?._ccService.switch === 'function') player?._ccService.switch(' ' + newVal)
  })

  watch(() => props.showSubtitle, () => {
    if (props.showSubtitle) {
      player?._ccService.open()
      void setSubtitles()
    } else {
      player?._ccService.close()
    }
  })

  onMounted(async () => {
    player = await getPlayer()
    void setSubtitles()
  })

  return () => (
    <div id={props.id} class="overflow-hidden" />
  )
})
