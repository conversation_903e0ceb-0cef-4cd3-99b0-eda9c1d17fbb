declare namespace Api {
  type ListAvailableMaterialRequest = RPPick<M.AvailableMaterial,
    'app_id',
  'drama_status' | 'series_status' | 'release_status',
  > & {
    language?: string[]
    drama_key?: string
    pushed?: number | 0 | 1 // 0 no 1 yes
    page_info?: {
      page_index?: number
      page_size?: number
    }
    series_resource_id?: string
  }
}
declare namespace M {
  type AvailableMaterial = {
    app_id: number | number[]
    app_name: string
    series_key: string
    title: string
    cover: string
    desc: string
    episode_num: number
    upload_num: number
    // 状态 1 草稿 2 已发布 3 已归档 4 已删除
    series_status: number | 1 | 2 | 3 | 4
    // 更新：0-默认 1-未上架 2-已上架. 如果不是第三方剧，填0
    drama_status: number | -1 | 1 | 2 | 3 | 4
    // 投放开关 0: 关闭 1:开启
    release_status: number | 0 | 1
    // 第三方剧集key
    third_key?: string
    created: number
    updated: number
    pushed: 0 | 1
    single_tip: string
    summary_tip: string
    country?: string
    language?: string
    // 上架时间 如果不是第三方剧，填0
    listing_time?: number
    moboshort_pushed?: 0 | 1 // moboshort是否已推送 0 未推送 1已推送
  }
}
