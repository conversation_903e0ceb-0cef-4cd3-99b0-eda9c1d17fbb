import { createComponent, fn } from '@skynet/shared'
import { But<PERSON> } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { ElDialog, ElButton } from 'element-plus'
import { useI18n } from 'vue-i18n'
type AuditOptions = {
  emits: {
    confirm: Fn
  }
}

export const Audit = createComponent<AuditOptions>({
  emits: {
    confirm: fn,
  },
}, (props, { emit }) => {
  const onOk = () => {
    emit('confirm')
  }
  const { t } = useI18n()
  return () => (
    <x-audit class="block">
      <span class="py-5 text-sm text-[#485568]">{t('completeCertificationBeforeSubmission')}</span>
      <div class="dialog-footer mt-5 text-end">
        <Button class="btn py-[11px]" onClick={onOk}>
          {t('toCertify')}
        </Button>
      </div>
    </x-audit>
  )
})

export default Audit
