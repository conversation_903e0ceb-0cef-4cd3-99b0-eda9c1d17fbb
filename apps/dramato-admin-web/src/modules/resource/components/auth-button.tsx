import { createComponent, SlotFn, Renderable } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { useMenu } from 'src/modules/menu/use-menu'

type AuthButtonOptions = {
  props: {
    blackRoleList?: string[]
    disabled?: boolean
    loading?: boolean
    prefix?: Renderable | null
    suffix?: Renderable | null
    class?: string
    onClick?: () => void
  }
  slots: {
    default: SlotFn
  }
}
export const AuthButton = createComponent<AuthButtonOptions>({
  props: {
    blackRoleList: [],
    disabled: false,
    loading: false,
    prefix: null,
    suffix: null,
    class: '',
    onClick: () => {}
  },
}, (props, { slots }) => {
  const { getRole } = useMenu()
  const role = getRole() as string
  return () => (
    <>
      {
        // 角色黑名单列表，对应role不展示该按钮 如果不传则都展示，如果传了，就判断黑名单是否含有该role，不存在则展示
        props.blackRoleList.length === 0 || !props.blackRoleList.includes(role) ? <Button {...props}>{slots.default ? slots.default() : null}</Button> : null
      }
    </>
  )
})
