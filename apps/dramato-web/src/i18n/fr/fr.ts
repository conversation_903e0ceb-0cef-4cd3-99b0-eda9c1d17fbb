export const fr = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: 'Choix Populaires',
    },
  },
  myList: {
    watchHistory: `Regarder l'Historique`,
    myList: 'Ma liste',
    seeAll: 'Tout Voir',
    noData: 'Aucun résultat apparenté',
  },
  // 分享页
  share: {
    watchNow: 'Regardez Maintenant',
    episodeDialog: {
      title: 'Rejoins-moi et obtient des pièces supplémentaires !',
      description: 'Je suis accro à cette série !',
      tips: 'Recommandation',
      button: 'Regarder le drama complet',
      limit24: 'Durée limitée: 24h',
    },
    toast: {
      takeOffShelf: 'Aucun Contenu Disponible',
    },
  },
  'customer-service-center': {
    title: 'Centre de service client',
    description: 'Description',
    'description-placeholder': 'Veuillez fournir des détails pour nous aider à comprendre le problème',
    'email-placeholder': 'Veuillez saisir votre adresse email pour recevoir les mises à jour du feedback',
    'submit-feedback': 'Soumettre',
    'enter-feedback': 'Autres commentaires',
    'upload-pictures': 'Téléverser des images',
    feedback: 'Feedback',
    'problem-type': 'Type de problème',
    'problem-type-placeholder': 'Veuillez sélectionner',
    'payment-issue-placeholder': `Veuillez fournir votre identifiant et votre reçu de commande:Google : https://pay.google.com/ (le numéro de commande commence par GPA...) iOS : App store - Apple ID - historique des achats (Numéro d'ordre)`,
    'content-issue-placeholder': `Veuillez indiquer le titre de la série, les numéros d'épisodes et les langues de sous-titres manquantes.`,
    'ads-issue-placeholder': `Veuillez indiquer le modèle de votre appareil, le pays et les paramètres linguistiques.`,
    'technical-issue-placeholder': `Veuillez indiquer le modèle de votre appareil, le pays et les paramètres linguistiques.`,
    'other-issue-placeholder': 'Veuillez fournir plus de détails, y compris le modèle de votre appareil et le pays.',
    'i-want-to-give-feedback': 'Je voudrais donner mes commentaires',
    'are-you-satisfied-with-this-reply': 'Êtes-vous satisfait(e) de cette réponse ?',
    unsatisfied: 'Insatisfait(e)',
    satisfied: 'Satisfait(e)',
    myFeedback: 'Mon avis',
    'reply-to-feedback': 'Répondre aux commentaires',
    'description-min-length': 'Décrivez le problème (6 caractères minimum)',
    'problem-type-not-choose': 'Veuillez sélectionner le type de feedback',
    'description-empty': 'Veuillez remplir ce champ.',
  },
  terminate: {
    question: 'Comment gérer mon abonnement ?',
    desc: 'Cliquez sur le bouton ci-dessous pour accéder à la page de gestion de contrat. Vous pouvez gérer votre abonnement en utilisant le commutateur de contrat :',
    title: 'Gérer mon contrat',
    expiration: `Date d'expiration`,
    manageDesc: 'Relation contractuelle',
  },
  toast: {
    'upload-failed': 'Téléversement échoué, veuillez réessayer',
    'submit-success': 'Soumis avec succès',
    'email-incorrect': 'Le format de l\'email est incorrect',
  },
  not_found: {
    title: 'La page que vous recherchez n\'existe pas',
  },
  regulations: {
    howUseData: 'Comment nous utilisons vos données',
    content: `Si vous sélectionnez "Autoriser" dans la demande d'autorisation de transparence du suivi des applications, nous utiliserons vos informations et données pour optimiser nos services et vous offrir la meilleure expérience. Sinon, nous ne ferons que collecter les données sans effectuer d'activités de suivi.

Si vous acceptez que DramaWave et nos partenaires et fournisseurs utilisent les données de votre appareil pour analyser votre utilisation de l'application, personnaliser les recommandations de contenu et fournir plus de services liés à l'information, veuillez cliquer sur "Accepter et continuer" pour continuer.`,
    acceptAll: 'Tout accepter',
    dataSettings: 'Paramètres des données',
    next: 'Suivant',
    cancel: 'Annuler',
    aboutYourPrivacy: 'À propos de votre confidentialité',
    personalDataName: 'Données personnalisées',
    personalDataDescription: 'Nous utilisons ces informations pour mémoriser vos choix et fournir des publicités et des recommandations de contenu personnalisées, des notifications et des rappels, etc.',
    advertisementDataName: 'Données publicitaires',
    advertisementDataDescription: 'Des cookies, des identifiants de votre appareil ou d\'autres informations peuvent être stockés ou consultés sur votre appareil à des fins de publicité en ligne. Les données utilisateur peuvent également être partagées avec nos partenaires publicitaires.',
    behaviorsDataName: 'Données comportementales',
    behaviorsDataDescription: 'Les données liées à l\'analyse, telles que les cookies ou les identifiants d\'appareil, peuvent être stockées pour mesurer et analyser l\'utilisation, le comportement et les performances publicitaires.',
    privacyAndTerms: 'Politique de confidentialité et conditions d\'utilisation',
    referToPrivacy: 'Veuillez consulter la politique de confidentialité',
    referToTerms: 'Veuillez consulter les conditions d\'utilisation',
    agreeAndSave: 'Accepter et enregistrer',
  },
  coins: {
    earnings: {
      title: 'Mes gains',
      total: 'Total des diamants',
      exchange: 'Échange',
      earningsHistory: 'Historique des gains',
      exchangeHistory: 'Historique des échanges',
      noRecords: 'Aucun enregistrement',
      last50earnings: 'Seuls les 50 derniers gains sont affichés',
      last50exchanges: 'Seuls les 50 derniers échanges sont affichés',
      benefits: 'Avantages',
      gotIt: 'D\'accord',
    },
    exchange: {
      title: 'Zone d\'échange',
      notOpen: 'Pas encore ouvert, restez à suivre',
      receiveBtn: 'Recevoir avec plaisir',
      earnCoins: 'Gagner des diamants',
      redeemRequiredXCoins: 'Remise des {X} diamants demandées',
      exchangeSuccess: 'Échanger avec succès',
      cardId: 'ID de carte',
      exchangeFailed: 'Erreur d\'échange',
      transferFailedInsufficientStock: 'Stock insuffisant. Veuillez venir tôt demain.',
      gotItBtn: 'D\'accord',
      transferFailedRiskControl: 'Compte anormal, incapable d\'échanger temporairement.',
      tryAgainBtn: 'Réessayer plus tard',
      transferConfirm: 'Confirmation du transfert',
      paypalAccountEmail: 'Email du compte PayPal',
      confirmPaypalAccountEmail: 'Confirmez l\'email du compte PayPal',
      transferX: 'Transfert de {X}',
      transferXConfirm: 'Transfert de {X} confirmé',
      transferToYourAccountX: 'Transfert sur votre compte {X}',
      confirmBtn: 'Confirmer',
      redeemFailed: 'Rachat échoué',
      redeemFailedContent: 'Il y a eu une erreur. Veuillez réessayer plus tard. ',
      transferSuccessTitle: 'Nous envoyons l\'argent!',
      transferSuccessContent: 'Votre argent arrivera dans un délai de 5 minutes à 72 heures.',
      copySuccess: 'Succès de copie',
      enterEmailError: 'Veuillez saisir une adresse e-mail valide',
      enterEmailValidError: 'Erreur de saisie, veuillez ressaisir',
      emailNotMatchError: 'L\'adresse e-mail ne correspond pas, veuillez la saisir à nouveau.',
    },
  },
  meal_check_in: {
    sleeping_link: 'Earn Coins While Sleeping',
    rules_title: 'Règles de l\'activité',
    benefits_title: 'Benefits',
    rules_1: '1. Chaque jour, vous avez quatre créneaux horaires pour enregistrer vos repas : Petit-déjeuner : 07:00-10:00; Déjeuner : 11:00-14:00; Dîner : 17:00-20:00; Collation de minuit : 21:00-24:00.',
    rules_2: '2. Si vous manquez l\'heure d\'enregistrement correspondante, vous pouvez regarder une vidéo pour récupérer la récompense.',
    rules_3: '3. Les récompenses se réinitialisent tous les jours à minuit, alors n\'oubliez pas de les réclamer.',
  },
  sleep_check_in: {
    earn_link: 'Enregistrer les repas pour gagner',
    benefits_title: 'Benefits',
    rules_1:
      '1. Il est possible d\'activer le mode \'Dormir pour gagner\' chaque nuit de 19 h à 00 h.',
    rules_2:
      '2. Après activation, le jour suivant, après avoir dormi 8 heures, vous pouvez réclamer la récompense de sommeil entre 8 h et 12 h.',
    rules_3:
      '3. Si vous manquez l\'heure pour réclamer la récompense, vous pouvez regarder une vidéo entre 12 h et 19 h le jour suivant pour réclamer à nouveau.',
  },
  bind_email: {
    title: 'Saisissez votre adresse e-mail',
    list_1: 'En saisissant votre adresse e-mail, vous recevrez des pièces exclusives',
    list_2: 'Recevoir des notifications sur les événements à venir et les offres spéciales',
    list_3: 'Ainsi que découvrir en avant-première les nouveaux épisodes',
    email_placeholder: 'Veuillez entrer votre adresse e-mail',
    verify: 'Vérifier',
    email_error: 'Veuillez saisir une adresse e-mail valide',
    verify_progress: 'L\'Email en cours de validation',
    verify_success: 'Réclamation réussie !',
    re_enter: 'Réessayer',
    email_repeat: 'Cet e-mail a déjà participé à l\'événement !',
  },
  infinityScroll: {
    end: 'La Fin',
    loading: 'Chargement...',
  },
}
