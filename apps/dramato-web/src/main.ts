import { reportNowViaBeacon } from '@skynet/client-track'
import 'src/init/init.ts'
import { createApp } from 'vue'
import { App } from './app.tsx'
import router from './router.tsx'
import { h5Router } from './h5-router.tsx'

const app = createApp(App)
app.use(isH5Domain() ? h5Router : router)
app.mount('#app')

window.addEventListener('unhandledrejection', event => {
  console.log(event.reason ?? event)
  event.preventDefault()
})
window.onerror = (error: unknown) => {
  console.log('error')
  console.log(error)
  return true
}

window.addEventListener('beforeunload', reportNowViaBeacon)

function isH5Domain() {
  const domain = window.location.hostname
  return domain === 'm-test.mydramawave.com' || domain === 'm.mydramawave.com'
}
