{"name": "@skynet/ui", "version": "1.0.0", "description": "", "main": "ui.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "peerDependencies": {"@skynet/shared": "workspace:*", "@types/markdown-it": "catalog:", "@vueuse/core": "catalog:", "choices.js": "catalog:", "dayjs": "catalog:", "echarts": "catalog:", "github-markdown-css": "catalog:", "markdown-it": "catalog:", "vue": "catalog:", "@tweenjs/tween.js": "catalog:"}, "devDependencies": {"@iconify/vue": "catalog:", "@skynet/preset": "workspace:*", "@types/lodash-es": "catalog:"}, "dependencies": {"@floating-ui/dom": "^1.6.10", "flatpickr": "^4.6.13", "lodash-es": "catalog:", "vue-multiselect": "catalog:"}}