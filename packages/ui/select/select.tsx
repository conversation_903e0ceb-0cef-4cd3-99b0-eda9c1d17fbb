import { createComponent, fn, JsonSimpleValue } from '@skynet/shared'
import Choices from 'choices.js'
import { nextTick, onUnmounted, ref, watchEffect } from 'vue'
import './choices.min.css'

export type Choice = {
  label: string
  value: JsonSimpleValue
  disabled?: boolean
  selected?: boolean
  choices?: Choice[]
}

export type SelectProps = {
  modelValue?: JsonSimpleValue
  placeholder?: string
  choices?: Choice[]
  multiple?: boolean
  clearable?: boolean
  disabled?: boolean
}

export type SelectOptions = {
  props: SelectProps
  emits: {
    'update:modelValue': (value: SelectProps['modelValue']) => void
  }
}

export const selectDefaultProps: Required<SelectProps> = {
  modelValue: '',
  choices: [],
  placeholder: '',
  multiple: false,
  clearable: false,
  disabled: false,
}

export const Select = createComponent<SelectOptions>({
  props: selectDefaultProps,
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const selectRef = ref<HTMLSelectElement>()
  let choicesInstance: Choices | null = null

  watchEffect(async () => {
    if (props.choices.length > 0) {
      if (!choicesInstance) {
        await nextTick(async () => {
          choicesInstance = await new Choices(selectRef.value, {
            allowHTML: true,
            placeholder: !!props.placeholder,
            placeholderValue: props.placeholder,
            noResultsText: '没有找到匹配的选项',
            noChoicesText: '没有选项',
            itemSelectText: '',
            classNames: {
              containerOuter: ['choices'],
              containerInner: ['tm-select'],
              input: ['tm-choices__input'],
              inputCloned: ['choices__input--cloned'],
              list: ['choices__list'],
              listItems: ['flex flex-row items-center gap-2'],
              listSingle: [''],
              listDropdown: ['choices__list--dropdown'],
              item: ['choices__item'],
              itemSelectable: ['choices__item--selectable'],
              itemDisabled: ['choices__item--disabled'],
              itemChoice: ['choices__item--choice'],
              description: ['choices__description'],
              placeholder: ['choices__placeholder'],
              group: ['choices__group'],
              groupHeading: ['choices__heading'],
              button: ['choices__button'],
              activeState: ['is-active'],
              focusState: ['is-focused'],
              openState: ['is-open'],
              disabledState: ['is-disabled'],
              highlightedState: ['is-highlighted'],
              selectedState: ['is-selected'],
              flippedState: ['is-flipped'],
              loadingState: ['is-loading'],
              notice: ['choices__notice'],
              addChoice: ['choices__item--selectable', 'add-choice'],
              noResults: ['has-no-results'],
              noChoices: ['has-no-choices'],
            },
          }).setChoices(props.choices, 'value', 'label', true)
        })
      }
    }
  })

  onUnmounted(() => {
    choicesInstance?.destroy()
  })

  return () => (
    <>
      <select
        class="tm-select"
        ref={selectRef}
        value={props.modelValue}
        placeholder={props.placeholder}
        multiple={props.multiple}
        // @ts-expect-error never mind
        onChange={(e: { target: { value: JsonSimpleValue } }) => {
          emit('update:modelValue', e.target?.value)
        }}
      />
    </>
  )
})
