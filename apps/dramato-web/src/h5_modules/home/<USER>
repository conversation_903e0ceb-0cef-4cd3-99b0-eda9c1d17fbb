import { ref } from 'vue'
import { defineStore } from 'pinia'
import { HomeModuleType } from './home-page-constant'
import { HomePageApi } from './home-page-api'
// import { useSearchAndTabsLayoutStore } from '../layout/use-search-and-tabs-layout-store'

export const useHomePageStore = defineStore('homePageStore', () => {
  const needInitPage = ref(true)
  const needChangeBgColor = ref(false)
  const skipFetch = ref(false)

  /** 首页-Tab内容页 */
  const homeModuleList = ref<M.HomepageModule[]>([
    {
      type: 'column_vertical_three', // 三列推荐
      module_name: 'column_vertical_three', // 三列推荐
      module_desc: '',
      module_key: '',
      scene_source: '/home/<USER>/banner', // 场景来源
      show_title: true,
      items: [],
    },
  ])
  const homeModuleListPageInfo = ref<PageInfo>({ next: '', has_more: false })

  const threeColumnRecommend = ref<M.HomepageModule>({})

  /** 首页- tab-Feed */
  const dramaFeeds = ref<M.Drama[]>([])
  const dramaFeedsNext = ref<string>('offset=0')
  const hasMoreDramaFeeds = ref<boolean>(true)
  const loadingDramaFeeds = ref<boolean>(false)

  /** 获取首页tab */
  const homeTabs = ref<M.Tab[]>([])
  const getHomeTab = async () => {
    const res = await HomePageApi.getTabList()
    homeTabs.value = res.data?.list || []
    return homeTabs.value
  }

  /** 获取首页-Tab内容页  */
  const getHomeModuleList = async (d: Api.GetHomeModuleList.Query) => {
    const res = await HomePageApi.getModuleList(d)
    homeModuleList.value = res.data?.items || []
    getThreeColumnRecommend()
    getDramaFeedsByModuleKey()
    homeModuleListPageInfo.value = res.data?.page_info || { next: '', has_more: false }
    dramaFeedsNext.value = res.data?.page_info?.next || 'offset=0'
    needInitPage.value = false
    loadingDramaFeeds.value = false
    hasMoreDramaFeeds.value = true
  }

  /** 获取三列推荐 */
  const getThreeColumnRecommend = () => {
    threeColumnRecommend.value = homeModuleList.value.find(item => item.type === HomeModuleType.ColumnVerticalThree) || {}
  }

  /** 获取首页- tab-Feed */
  const getDramaFeedsByModuleKey = () => {
    dramaFeeds.value = (homeModuleList.value.find(item => item.type === HomeModuleType.Recommend)?.items || []).filter(item => !!item.episode_info)
  }

  /** 获取首页- tab-Feed */
  const getDramaFeeds = async (_next?: string) => {
    const feedsModule = homeModuleList.value.find(item => item.type === HomeModuleType.Recommend)
    if (!homeModuleListPageInfo.value.has_more) return
    if (!hasMoreDramaFeeds.value) return
    if (loadingDramaFeeds.value) return
    loadingDramaFeeds.value = true
    const res = await HomePageApi.getFeeds({ module_key: feedsModule?.module_key || '', next: _next || dramaFeedsNext.value })
    const list = (res.data?.items || []).filter(item => !!item.episode_info)
    dramaFeeds.value.push(...list)
    dramaFeedsNext.value = res.data?.page_info.next || ''
    hasMoreDramaFeeds.value = res.data?.page_info.has_more || false
    if (list.length === 0) {
      hasMoreDramaFeeds.value = false
    }
    loadingDramaFeeds.value = false
  }

  /** 滚动加载实现 */
  // const onScroll = () => {
  //   const { mainRef } = useSearchAndTabsLayoutStore()
  //   if (!hasMoreDramaFeeds.value) {
  //     return
  //   }
  //   if (mainRef.value?.scrollTop + 50 >= mainRef.value?.scrollHeight - mainRef.value?.offsetHeight) {
  //     void getDramaFeeds()
  //     console.log('load more feeds')
  //   }
  // }

  // const onListenerScroll = () => useSearchAndTabsLayoutStore().mainRef.value?.addEventListener('scroll', onScroll)

  // const removeListenerScroll = () => useSearchAndTabsLayoutStore().mainRef.value?.removeEventListener('scroll', onScroll)
  return {
    homeTabs,
    homeModuleList,
    dramaFeeds,
    threeColumnRecommend,
    getHomeTab,
    getHomeModuleList,
    getThreeColumnRecommend,
    getDramaFeeds,
    // onListenerScroll,
    // removeListenerScroll,
    loadingDramaFeeds,
    hasMoreDramaFeeds,
    dramaFeedsNext,
    needInitPage,
    skipFetch,
    needChangeBgColor,
  }
})
