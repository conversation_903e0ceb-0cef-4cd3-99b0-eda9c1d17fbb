/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Button, CreateForm, Icon, openDialog, Pager, showFailToast, showSuccessToast, SvgIcon, transformNumber } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { set } from 'lodash-es'
import { useTimbre } from './use-timbre-store'
import { ElTable, ElTableColumn } from 'element-plus'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { apiCreateTimbre } from './timbre-api'
import { Uploader } from '../common/uploader/uploader'
import { langKey, langValue } from 'src/modules/resource/constant'
type TimbrePageOptions = {
  props: {}
}
export const TimbrePage = createComponent<TimbrePageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, onReset, Form, params, columns, total, onPageChange, onPageSizeChange, sourceOptions, cdnUrl } = useTimbre()
  const tableRef = ref<InstanceType<typeof ElTable>>()

  onMounted(() => {
    void onQuery()
  })

  return () => (
    <x-timbre-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>音色管理</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={() => onQuery()}
            onReset={() => {
              tableRef.value?.clearSort()
              onReset()
            }}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['音色名称', 'title', { type: 'text', placeholder: '可搜索人设、音色特征' }],
              ['语言', 'language', { type: 'select', placeholder: '请选择语言', options: langKey.map((key, index) => {
                return {
                  label: langValue[index],
                  value: key
                }
              }) }],
              ['性别', 'gender', { type: 'select', placeholder: '请选择性别', options: [
                {
                  value: -1,
                  label: '全部'
                },
                {
                  value: 0,
                  label: '未知'
                },
                {
                  value: 1,
                  label: '男'
                },
                {
                  value: 2,
                  label: '女'
                },
              ], autoInsertEmptyOption: false }, {
                transform: transformNumber
              }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-end">
            <Button class="btn btn-primary btn-sm" onClick={() => {
              const btnLoading = ref(false)
              const timbreItem = ref<Api.Timbre.TimbreItem>({})
              const TimbreForm = CreateForm<Api.Timbre.TimbreItem>()
              const rules = z.object({
                title: z.string().min(1, '请输入音色名称'),
                desc: z.string().min(1, '请输入音色描述'),
                sample_path: z.string().min(1, '请上传样本路径'),
                source: z.string().min(1, '请选择音色来源'),
              })
              const { error, validateAll } = useValidator(timbreItem, rules)

              const hideDialog = openDialog({
                title: '新增音色',
                body: () => (
                  <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                    <TimbreForm
                      class="flex w-full flex-col gap-y-4"
                      hasAction={false}
                      data={timbreItem.value}
                      error={error.value}
                      onChange={(path, value) => {
                        set(timbreItem.value, path, value)
                      }}
                      items={[
                        [requiredLabel('音色名称'), 'title', { type: 'text', placeholder: '请输入音色名称' }],
                        [requiredLabel('音色描述'), 'desc', { type: 'textarea', placeholder: '请输入音色描述' }],
                        [requiredLabel('音色样本'), 'sample_path', {
                          type: 'custom',
                          render: () => (
                            <x-upload-cover class="grid w-[200px] h-[220px] gap-y-2">
                              <Uploader
                                accept="mp4,mp3,wav"
                                ossKeyType="resource"
                                isImage={false}
                                showFileList={false}
                                maxsize={1024 * 1024 * 1024 * 2}
                                onUploadSuccess={file => {
                                  set(timbreItem.value, 'sample_path', file.temp_path || '')
                                }}
                              >
                                <x-uploader-wrapper class="flex size-[200px] cursor-pointer flex-col items-center justify-center  gap-y-2 overflow-hidden rounded-md border border-dashed">
                                  {
                                    timbreItem.value.sample_path ? (
                                      <div class="size-[200px] border border-dashed rounded-md flex justify-center items-center">
                                        <SvgIcon name="ic_audio_wave" class="size-10" />
                                      </div>
                                    ) : (
                                      <p>上传音频</p>
                                    )
                                  }
                                </x-uploader-wrapper>
                              </Uploader>
                            </x-upload-cover>
                          ),
                        }, {
                          hint: timbreItem.value.sample_path ? <audio src={`${cdnUrl}/${timbreItem.value.sample_path}`} controls /> : null
                        }],
                        [requiredLabel('音色来源'), 'source', {
                          type: 'select',
                          placeholder: '请输入音色来源',
                          options: sourceOptions
                        }],
                      ]}
                    />
                    <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                      <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                      <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                        try {
                          if (!validateAll()) return
                          btnLoading.value = true
                          await apiCreateTimbre(timbreItem.value)
                          showSuccessToast('操作成功')
                          onQuery()
                          hideDialog()
                        } catch (error: any) {
                          showFailToast(error.response.data.err_msg || '操作失败')
                          return
                        } finally {
                          btnLoading.value = false
                        }
                      }}
                      >
                        {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                        确定
                      </button>
                    </x-status-footer>
                  </x-status-confirm-dialog>
                ),
              })
            }}>
              新增音色
            </Button>
          </div>
        ),
        table: () => (
          <ElTable
            ref={tableRef}
            v-loading={loading.value}
            data={list.value || []}
          >
            {columns.value.map(col => {
              const { render, ...rest } = col
              return (
                <ElTableColumn
                  key={col.prop}
                  {...rest}
                  v-slots={render ? {
                    default: ({ row }: { row: any }) => render({ row }),
                  } : undefined}
                />
              )
            })}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_index} v-model:size={params.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </x-timbre-page>
  )
})

export default TimbrePage
