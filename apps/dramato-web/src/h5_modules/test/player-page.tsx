import { createComponent } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import shaka from 'shaka-player/dist/shaka-player.compiled'
import { useRoute } from 'vue-router'
type PlayerPageOptions = {
  props: {}
}
export const PlayerPage = createComponent<PlayerPageOptions>({
  props: {},
}, props => {
  let player: shaka.Player | null = null
  const videoRef = ref<HTMLVideoElement | null>(null)
  const route = useRoute()
  onMounted(() => {
    void initPlayer()
  })
  return () => (
    <x-player-page class="block">
      <div class="flex justify-center items-center">
        <button class="p-2 m-2 bg-red-500" onClick={onClick}>Play</button>
      </div>
      <video ref={videoRef} class="w-full h-[calc(100vh-60px)]" autoplay controls={false}
        playsinline webkit-playsinline tab-index="-1"
        poster="https://static-v1.mydramawave.com/frontend_static/common-images/black.png"
      />
    </x-player-page>
  )

  function onClick() {
    const video = videoRef.value
    if (!video) return
    if (video.paused) {
      void video.play()
    } else {
      video.pause()
    }
  }

  async function initPlayer() {
    if (!videoRef.value) return
    shaka.polyfill.installAll()
    if (!shaka.Player.isBrowserSupported()) {
      return window.alert('Browser not supported!')
    }
    player = new shaka.Player()
    const url = route.query.url?.toString()
    if (url && player) {
      player.addEventListener('error', event => {
        console.log('shaka error')
        console.log(event)
      })
      await player.attach(videoRef.value)
      await player.load(url).then(() => {
        if (!player) return
        player.configure({ abr: { enabled: false } })
        // 获取所有 video tracks
        const tracks = player.getVariantTracks()

        // 找出最小分辨率的 track
        let minTrack = tracks[0]
        for (const track of tracks) {
          if ((track.height && minTrack.height) ? track.height < minTrack.height
            : (track.height === minTrack.height && track.bandwidth < minTrack.bandwidth)) {
            minTrack = track
          }
        }
        // 选择这个最小分辨率的 track
        player.selectVariantTrack(minTrack, /* clearBuffer= */ true)
      })
    }
  }
})

export default PlayerPage
