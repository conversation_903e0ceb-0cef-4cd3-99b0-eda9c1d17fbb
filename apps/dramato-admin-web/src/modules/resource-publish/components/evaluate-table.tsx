/* eslint-disable @typescript-eslint/no-explicit-any */
import { Tooltip } from '@skynet/ui'
import { ElTable, ElTableColumn, ElSelect, ElOption } from 'element-plus'
import { ref, computed } from 'vue'

interface ScoreTableProps {
  data: M.IRatingDetail[]
  onScoreChange?: (data: M.IRatingDetail[], total: number, rating: string) => void
  disabled?: boolean
}

interface TableColumn {
  property: string
  [key: string]: any
}

export const EvaluateTable = (props: ScoreTableProps) => {
  const tableData = ref<M.IRatingDetail[]>(props.data)
  const totalScore = computed(() => {
    return tableData.value.reduce((sum, item) => sum + (((item.score * item.weight) / 100) || 0), 0)
  })

  const rating = computed(() => {
    const score = totalScore.value
    if (score >= 5) return 'S'
    if (score >= 4) return 'A'
    if (score >= 3) return 'B'
    return 'C'
  })

  const handleScoreChange = (value: number, row: M.IRatingDetail) => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      const newData = [...tableData.value]
      newData[index] = { ...row, score: value }
      tableData.value = newData

      props.onScoreChange?.(newData, totalScore.value, rating.value)
    }
  }

  const spanMethod = ({ row, column, rowIndex }: {
    row: M.IRatingDetail
    column: TableColumn
    rowIndex: number
  }) => {
    if (column.property === 'category') {
      const prevRow = tableData.value[rowIndex - 1]
      if (prevRow && prevRow.category === row.category) {
        return { rowspan: 0, colspan: 0 }
      }

      let count = 1
      for (let i = rowIndex + 1; i < tableData.value.length; i++) {
        if (tableData.value[i].category === row.category) {
          count++
        } else {
          break
        }
      }
      return { rowspan: count, colspan: 1 }
    }
  }

  return (
    <div class="w-full">
      <ElTable
        data={[...tableData.value, { id: 'total', category: '总分', criteria: '', score: totalScore.value }]}
        border={true}
        spanMethod={spanMethod}
        class="w-full"
      >
        <ElTableColumn
          prop="criteria"
          label="评估项目"
          minWidth="300"
          v-slots={{
            default: ({ row }: { row: M.IRatingDetail }) => (
              row.id === 'total'
                ? (
                    <div class="font-bold">
                      {rating.value}
                    </div>
                  )
                : row.desc
                  ? (
                      <Tooltip
                        popContent={() => row.desc}
                        popWrapperClass="z-popover-in-dialog !max-w-[400px]"
                      >
                        <span>{row.criteria}</span>
                      </Tooltip>
                    )
                  : (
                      <span>{row.criteria}</span>
                    )
            ),
          }}
        />

        <ElTableColumn
          prop="score"
          label="评分"
          width="120"
          v-slots={{
            default: ({ row }: { row: M.IRatingDetail }) => row.id === 'total'
              ?  <></>
              : (
                  <ElSelect
                    v-model={row.score}
                    disabled={props.disabled}
                    placeholder="请选择"
                    onChange={(value: number) => handleScoreChange(value, row)}
                  >
                    {[1, 2, 3, 4, 5].map(score => (
                      <ElOption
                        key={score}
                        label={score}
                        value={score}
                      />
                    ))}
                  </ElSelect>
                ),
          }}
        />
      </ElTable>
    </div>
  )
}
