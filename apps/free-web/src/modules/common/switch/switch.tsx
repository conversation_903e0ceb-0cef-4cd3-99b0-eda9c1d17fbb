import { ClassName, createComponent, fn, mc } from '@skynet/shared'
type SwitchOptions = {
  props: {
    class?: ClassName
    modelValue: boolean
    disabled?: boolean
  }
  emits: {
    'update:modelValue': (value: boolean) => void
  }
}
export const Switch = createComponent<SwitchOptions>({
  inheritAttrs: false,
  props: {
    class: '',
    modelValue: false,
    disabled: false,
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const onClick = () => {
    emit('update:modelValue', !props.modelValue)
  }
  return () => (
    <button disabled={props.disabled} class={mc(`flex w-11 h-6 shrink-0 cursor-pointer items-center border-none no-tap-color rounded-full 
      transition-colors focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 `, props.class, [
      props.modelValue && 'bg-[var(--switch-on)]',
      !props.modelValue && 'bg-[var(--switch-off)]',
    ])} onClick={onClick}
    >
      <span class={['pointer-events-none h-5 w-5 flex items-center justify-center rounded-full bg-[var(--white)] shadow-lg ring-0 transition-transform text-xs',
        props.modelValue && 'translate-x-[22px]',
        !props.modelValue && 'translate-x-[2px]',
      ]}
      />
    </button>
  )
})
