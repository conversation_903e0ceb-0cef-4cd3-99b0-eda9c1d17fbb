import CryptoJS from 'crypto-js'

/**
 * 加密函数，对应后端的 Encrypt 函数
 * @param {string} plaintext 需要加密的文本
 * @param {string} key 加密密钥，默认为 "2r36789f45q01ae5"
 * @returns {string} 加密后的 base64 字符串
 */
export function encrypt(plaintext: string, key: string = '2r36789f45q01ae5'): string {
  // 生成随机的 IV (初始化向量)
  const iv = CryptoJS.lib.WordArray.random(16)

  // 使用 AES-CBC 模式加密
  const encrypted = CryptoJS.AES.encrypt(plaintext, CryptoJS.enc.Utf8.parse(key), {
    iv: iv,
    padding: CryptoJS.pad.Pkcs7,
    mode: CryptoJS.mode.CBC,
  })

  // 将 IV 和加密后的内容拼接后进行 base64 编码
  const ivAndEncrypted = iv.concat(encrypted.ciphertext)
  return CryptoJS.enc.Base64.stringify(ivAndEncrypted)
}

/**
 * 解密函数，对应后端的 Decrypt 函数
 * @param {string} cryptoText 加密后的 base64 字符串
 * @param {string} key 解密密钥，默认为 "2r36789f45q01ae5"
 * @returns {string} 解密后的明文
 */
export function decrypt(cryptoText: string, key: string = '2r36789f45q01ae5'): string {
  try {
    // 解码 base64 字符串
    const ciphertext = CryptoJS.enc.Base64.parse(cryptoText)

    // 提取 IV (前 16 字节)
    const iv = CryptoJS.lib.WordArray.create(
      ciphertext.words.slice(0, 4),
      16,
    )

    // 提取加密内容 (剩余部分)
    const encryptedContent = CryptoJS.lib.WordArray.create(
      ciphertext.words.slice(4),
      ciphertext.sigBytes - 16,
    )

    // 创建解密对象
    const decrypted = CryptoJS.AES.decrypt(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      { ciphertext: encryptedContent } as any,
      CryptoJS.enc.Utf8.parse(key),
      {
        iv: iv,
        padding: CryptoJS.pad.Pkcs7,
        mode: CryptoJS.mode.CBC,
      },
    )

    // 转换为 UTF-8 字符串
    return decrypted.toString(CryptoJS.enc.Utf8)
  } catch (error) {
    console.error('Decryption failed:', error)
    throw error
  }
}
