/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetUserStrategyLayerList } from '../ab-test-new/ab-test-new-api'

export const useUserStrategyLayer = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    isUpdating,
  }
}

const Form = CreateForm<M.UserStrategyLayer.params>()
const params = ref<M.UserStrategyLayer.params>({
  // platform: 'ios',
  // persona_type: 1,
  query_condition: {
    CampaignType: [],
    UserType: [],
    Region: [],
    AdValueLevel: [],
    HasFirstRecharge: [],
    Device: [],
  },
})

const Table = CreateTableOld<M.UserStrategyLayer.ListItem>()
const list = ref<M.UserStrategyLayer.ListItem[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(1)
const isUpdating = ref(false)

// const currentAd = ref<M.UserStrategyLayer.CreateAd>({})

const search = async (_page?: number) => {
  _page = _page || page.value + 1
  loading.value = true
  const res = await apiGetUserStrategyLayerList({
    ...params.value,
    page_info: {
      offset: (_page - 1) * pageSize.value, size: pageSize.value,
    },
  })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.page_info.total || 0
  page.value = _page
}
