export const isEmptyArray = (arr?: unknown[] | null) => {
  return arr === null || arr === undefined || arr.length === 0
}

export const notEmptyArray = (arr?: unknown[] | null) => !isEmptyArray(arr)

export const mapToOptions = (map: Record<string, string>, sortFn?: (a: string, b: string) => number) => {
  return Object.entries(map)
    .sort(([a], [b]) => sortFn ? sortFn(a, b) : a.localeCompare(b))
    .map(([value, label]) => ({ value, label }))
}
