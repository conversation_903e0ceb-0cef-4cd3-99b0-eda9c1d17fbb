import { createComponent } from '@skynet/shared'
import { Input } from '@skynet/ui'
import { ref } from 'vue'

export const InputExamplesDisabled = createComponent(null, () => {
  const name = ref<string>('')
  const age = ref<number>()
  const gender = ref<string>()
  const yesOrNo = ref<boolean>()
  const description = ref<string>()
  const time = ref<string>()
  const format = ref<string>('YYYY-MM-DD')

  const options = [{

    label: '男',
    value: 'male',

  }, {

    label: '女',
    value: 'female',

  }]
  return () => (
    <div class="space-y-2 p-2 markdown-body">
      <p>default:</p>
      <Input
        disabled
        placeholder="请输入姓名"
        class="w-80 h-10 inline-block"
        v-model={name.value}
      />
      <p>number:</p>
      <Input
        disabled
        placeholder="请输入年龄"
        class="w-80 h-10 inline-block"
        type="number"
        v-model={age.value}
        min={1}
        max={200}
      />
      <p>radio:</p>
      <Input
        disabled
        type="radio"
        class="w-80 h-10 inline-block"
        options={options}
        v-model={gender.value}
      />

      <p>checkbox:</p>
      <Input
        disabled
        type="checkbox"
        class="inline-block"
        v-model={yesOrNo.value}
      />
      {yesOrNo.value && <span class="ml-1">yes</span>}
      {!yesOrNo.value && <span class="ml-1">no</span>}

      <p>textarea:</p>
      <Input
        disabled
        rows={5}
        type="textarea"
        v-model={description.value}
      />

      <p>date:</p>
      <Input
        disabled
        type="date"
        format={format.value}
        v-model={time.value}
      />
      <Input
        disabled
        type="text"
        v-model={format.value}
      />
      <p>
        format:
        {format.value}
      </p>
      <p>
        time:
        {time.value}
      </p>
    </div>
  )
})

export default InputExamplesDisabled
