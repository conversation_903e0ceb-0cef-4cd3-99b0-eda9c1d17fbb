{"name": "@skynet/preset", "version": "1.0.0", "description": "", "type": "module", "main": "./preset.mjs", "types": "./preset.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "exports": {".": "./preset.mjs", "./uno": {"import": "./uno/uno.mjs", "types": "./uno/uno.d.ts"}, "./eslint": "./eslint.mjs"}, "keywords": [], "author": "", "license": "ISC", "peerDependencies": {"unocss": "catalog:"}, "devDependencies": {"@eslint/js": "9.6.0", "@stylistic/eslint-plugin": "2.3.0", "@stylistic/eslint-plugin-jsx": "2.3.0", "eslint": "^8.57.0", "eslint-plugin-json": "4.0.0", "eslint-plugin-tailwindcss": "catalog:", "globals": "catalog:", "typescript": "catalog:", "eslint-plugin-vue": "catalog:", "typescript-eslint": "7.16.0"}}