/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, Tooltip } from '@skynet/ui'
import { ElTable, ElTableColumn } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { langValue, langKey } from 'src/modules/resource/constant'
import { computed } from 'vue'

type TermTableOptions = {
  props: {
    list: M.ITerminology[]
    isTrans?: boolean
    language?: string
    height?: string
  }
  emits: {
    change: (term: { list: M.ITerminology[], item: M.ITerminology }) => void
    delete: (id: number) => void
  }
}
export const TermTable = createComponent<TermTableOptions>({
  props: {
    list: [],
    isTrans: false,
    language: '',
    height: '500px'
  },
  emits: {
    change: (term: { list: M.ITerminology[], item: M.ITerminology }) => {},
    delete: (id: number) => {},
  },
}, (props, { emit }) => {
  const updateData = (newTerm: M.ITerminology, scope: { row: M.ITerminology }) => {
    const index = props.list.findIndex(item => item.term_id === scope.row.term_id)
    const newList = cloneDeep(props.list)
    newList.splice(index, 1, newTerm)
    emit('change', {
      list: newList,
      item: newTerm,
    })
  }

  const columns = computed(() => [
    props.language ? { label: `${langValue[langKey.findIndex(item => item === props.language)] || '简体中文'}术语`, minWidth: 120, render: (scope: { row: M.ITerminology }) => {
      return (
        <input type="text" key={`translation_${scope.row.term_id}`} disabled={scope.row.delete === 1} value={scope.row.translation} class="input input-xs input-bordered w-full" onChange={e => {
          const value = (e.target as HTMLInputElement).value
          const newTerm = {
            ...scope.row,
            translation: value,
            update: true,
            delete: 0,
            tar_lang: props.language,
          } as M.ITerminology
          updateData(newTerm, scope)
        }} />
      )
    } } : null,
    !!props.language ? {
      label: '源语言术语', minWidth: 120, prop: 'term' } : { label: '源语言术语', minWidth: 120, render: (scope: { row: M.ITerminology }) => {
      return (
        <input type="text" key={`term_${scope.row.term_id}`} disabled={scope.row.delete === 1} value={scope.row.term} class="input input-xs input-bordered w-full" onChange={e => {
          const value = (e.target as HTMLInputElement).value
          const newTerm = {
            ...scope.row,
            term: value,
            update: true,
            delete: 0,
            tar_lang: props.language,
          } as M.ITerminology
          updateData(newTerm, scope)
        }} />
      )
    } },

    { prop: 'category', label: '类型', minWidth: 100, render: (scope: { row: M.ITerminology }) => {
      return (
        <Tooltip popWrapperClass="z-popover-in-dialog" popContent={() => scope.row.category}>
          <input type="text" v-model={scope.row.category} key={`category_${scope.row.term_id}`} disabled={!scope.row.isNew} class="input input-xs input-bordered w-full" onInput={e => {
            const value = (e.target as HTMLInputElement).value
            if (value === 'character') {
              scope.row.official_name = ''
            }
            const newTerm = {
              ...scope.row,
              category: value,
            } as M.ITerminology
            updateData(newTerm, scope)
          }} />
        </Tooltip>

      )
    } },

    { prop: 'official_name', label: 'official_name', minWidth: 120, render: (scope: { row: M.ITerminology }) => {
      return (scope.row.isNew && scope.row.category === 'character') ? (
        <input type="text" key={`official_name_${scope.row.term_id}`} class="input input-xs input-bordered w-full" onChange={e => {
          const value = (e.target as HTMLInputElement).value
          const newTerm = {
            ...scope.row,
            official_name: value,
          } as M.ITerminology
          updateData(newTerm, scope)
        }} />
      ) : <div key={`official_name_${scope.row.term_id}_${scope.row.official_name}`}>{scope.row.official_name}</div>
    } },
    !props.isTrans ? { prop: 'operation', label: '操作', minWidth: 80, render: (scope: { row: M.ITerminology }) => {
      return !scope.row.isNew
        ? (
            <>
              {scope.row.delete === 1 ? (
                <Button class="btn btn-link btn-xs" onClick={() => {
                  emit('change', {
                    list: props.list,
                    item: {
                      ...scope.row,
                      delete: scope.row.update ? 0 : undefined,
                    },
                  })
                }}>恢复</Button>
              )
                : (
                    <Button class="btn btn-link btn-xs" onClick={() => {
                      emit('change', {
                        list: props.list,
                        item: {
                          ...scope.row,
                          delete: 1,
                        },
                      })
                    }}>删除</Button>
                  )}
            </>
          )
        : (
            <>
              <Button class="btn btn-link btn-xs" onClick={() => {
                emit('delete', scope.row.term_id || 0)
              }}>删除</Button>
            </>
          )
    } } : null,
  ])

  return () => (
    <ElTable
      scrollbar-always-on
      style={{ width: '100%', height: props.height }}
      data={props.list || []}
    >
      {columns.value.map(col => {
        if (!col) return null
        const { render, ...rest } = col

        return (
          <ElTableColumn
            key={col.prop}
            {...rest}
            v-slots={render ? {
              default: ({ row }: { row: any }) => render({ row }),
            } : undefined}
          />
        )
      })}
    </ElTable>
  )
})

export default TermTable
