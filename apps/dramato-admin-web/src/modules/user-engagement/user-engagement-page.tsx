import { createComponent, mc } from '@skynet/shared'
import { Button, Pager, DateTime, transformInteger, transformNumber, openDialog, showAlert } from '@skynet/ui'
import { set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { useUserEngagement } from './use-user-engagement'
import { apiNewUserStrategyOption } from './user-engagement-api'
type userEngagementPageOptions = {
  props: {}
}
export const userEngagementPage = createComponent<userEngagementPageOptions>({
  props: {},
}, props => {
  const { Form, listParams, Table, total, list, loading, search, onPageChange, onPageSizeChange, copyBtn } = useUserEngagement()
  const router = useRouter()

  onMounted(() => {
    void search()
  })
  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>新用户承接策略</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              listParams.value = { next: 'page=1&page_size=10' }
              void search()
            }}
            onSubmit={() => {
              set(listParams.value, 'next', 'page=1&page_size=10')
              void search()
            }}
            data={listParams.value} items={[
              ['策略ID', 'id', { type: 'number' }, {
                transform: transformNumber,
              }],
              ['策略名称', 'name', { type: 'text' }],
              ['状态', 'status', {
                type: 'select',
                options: [
                  { label: '草稿', value: 1 },
                  { label: '上架', value: 2 },
                  { label: '下架', value: 3 },
                ],
              }, { transform: transformInteger }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2" />
            <x-hide-when-in-dialog>
              <Button class="btn btn-primary btn-sm" onClick={
                () => {
                  // resetFormData()
                  void router.push('/new-user-engagement/create')
                }
              }
              >新建用户承接策略
              </Button>
            </x-hide-when-in-dialog>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['策略组ID', 'id', { class: 'w-[100px]' }],
            ['策略组名称', 'name', { class: 'w-[200px]' }],
            ['是否AB测试', row => (
              <span>{[
                { label: '否', value: 0 },
                { label: '是', value: 1 },
              ].find(item => item.value === row.is_abtest)?.label ?? '否'}
              </span>
            ), { class: 'w-[80px]' }],
            ['平台', row => (
              <span>{[
                { label: 'all', value: 0 },
                { label: 'IOS', value: 1 },
                { label: 'Android', value: 2 },
              ].find(item => item.value === row.platform)?.label ?? 'all'}
              </span>
            ), { class: 'w-[80px]' }],
            ['上架状态', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { label: '草稿', value: 1 },
                { label: '上架', value: 2 },
                { label: '下架', value: 3 },
              ].find(item => item.value === row.status)?.label ?? '草稿'}
              </span>
            ), { class: 'w-[80px]' }],
            ['创建时间', 'create_time', { class: 'w-[150px]' }],
            ['上架时间', 'list_time', { class: 'w-[150px]' }],
            ['修改人', row => row?.operator_name, { class: 'w-[100px]' }],
            [
              <span class="px-3">操作</span>,
              row => (
                <div class="flex gap-x-2">
                  <Button
                    class="btn btn-outline btn-xs"
                    onClick={() => {
                      if (!row.id) return
                      void apiNewUserStrategyOption({
                        id: row.id,
                        option: row.status == 2 ? 2 : 1,
                      })
                        .then(() => {
                          void search()
                        })
                        .catch(error => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                  >
                    {row.status == 2 ? '下架' : '上架'}
                  </Button>
                  <RouterLink to={`/new-user-engagement/create?id=${row.id}`} class={mc('btn btn-outline btn-xs', [1, 3].includes(row.status || 0) ? '' : 'hidden')}>修改</RouterLink>
                  <RouterLink to={`/new-user-engagement/create?id=${row.id}&mode=view`} class={mc('btn btn-outline btn-xs', row.status === 2 ? '' : 'hidden')}>查看</RouterLink>
                  <Button
                    class={mc('btn btn-outline btn-xs', row.status == 2 ? 'hidden' : '')}
                    onClick={() => {
                      const showTipsDialog = () => {
                        const hideDialog = openDialog({
                          title: '',
                          mainClass: 'pb-0 px-5',
                          body: (
                            <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                              <x-status-body>是否确认删除新用户承接策略【{row.name}】?</x-status-body>
                              <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                                <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                <button class="btn btn-primary btn-sm" onClick={() => {
                                  void apiNewUserStrategyOption({
                                    id: row.id || 0,
                                    option: 3,

                                  })
                                    .then(() => {
                                      void search()
                                    })
                                    .catch(error => {
                                      showAlert(error.response.data.message, 'error')
                                    })
                                  hideDialog()
                                }}
                                >确定
                                </button>
                              </x-status-footer>
                            </x-status-confirm-dialog>
                          ),
                        })
                      }

                      showTipsDialog()
                    }}
                  >
                    删除
                  </Button>
                  <Button class="btn-outline btn btn-xs" onClick={() => copyBtn(row)}>
                    复制
                  </Button>
                </div>
              ), {
                class: 'w-[200px]',
              },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (total.value
          ? (
              <Pager class="justify-end"
                page={listParams.value.page_info?.page_index || 1}
                size={listParams.value.page_info?.page_size || 10}
                total={total.value || 0}
                onUpdate:page={onPageChange}
                onUpdate:size={onPageSizeChange}
              />
            )
          : null),

      }}
    </NavFormTablePager>
  )
})

export default userEngagementPage
