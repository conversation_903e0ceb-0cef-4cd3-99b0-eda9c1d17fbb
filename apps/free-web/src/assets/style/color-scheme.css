@media (prefers-color-scheme: dark) {

  :root {
    --black: #0B080B;
    --white: #FDFBFC;
    --grey-1: #FAF7FA;
    --grey-2: #F3F0F3;
    --grey-3: #ECE9EC;
    --grey-4: #CCCACB;
    --grey-5: #A1A0A3;
    --grey-6: #797B7D;
    --grey-7: #6E7071;
    --grey-8: #5F6162;
    --grey-9: #434546;
    --grey-10: #2E2F30;
    --grey-11: #292A2B;
    --grey-12: #242526;
    --grey-13: #1D1D1E;

    /* 标题、主要文字 */
    --text-1: #090609;
    /* 次强文字 */
    --text-2: #4d4e4f;
    /* 辅助文字 */
    --text-3: #626466;
    /* 禁用文字 */
    --text-4: #a1a2a3;
    /* 图片上主要文字 */
    --text-5: #fff;
    /* 图片上次要文字 */
    --text-6: #FFFFFFCC;
    /* 图片上辅助文字 */
    --text-7: #FDFBFCB3;
    /* 图片上禁用文字 */
    --text-8: rgba(253, 251, 252, 0.5);
    /* 浅色背景下深色文字 */
    --text-9: #0B080B;
    /* VIP文字 */
    --text-10: #5A280A;
    /* Surface */
    /* 基础背景 */
    --surface-1: #FDFBFC;
    /* 弹窗 */
    --surface-2: #F3F0F3;
    /* 弹窗+1 */
    --surface-3: #FDFBFC;
    --surface-4: #f7f4f7;
    /* toast */
    --surface-5: #3d3e3f;

    /* 背景色 */
    /* 填充色-浅 */
    --fill-1: #faf8fa;
    /* 填充色-一般 */
    --fill-2: #f7f4f7;
    /* 填充色-深 */
    --fill-3: #CCCACB;
    /* 填充色-白色 */
    --fill-4: #FDFBFC;
    --fill-5: #fff;

    /* 线 */
    /* 线条色-浅 */
    --line-1: #ece9ec;
    /* 线条色-一般 */
    --line-2: #CCCACB;
    /* 线条色-浅棕 */
    --line-3: #CCCACB;
    --line-4: #fb3a3a;

    /* system */
    /* 蓝色-链接、可点击 */
    --blue-6: #0766FF;
    /* 红色-警示 */
    --red-6: #FF2351;
    /* 黄色-等待结果 */
    --yellow-6: #FCCF16;
    /* 绿色-通过结果 */
    --green-6: #34c759;

    /* brand */
    --brand-1: #ffeaea;
    --brand-3: #ff7b81;
    --brand-6: #fe2333;
    --brand-7: #e61d2e;

    /* mask */
    --mask: #0B080B80;

    /* pay */
    /* 支付背景色 */
    --pay-1: #FFE5E5;
    /* 支付价格点缀色 */
    --pay-2: #FFE5E5;
    /* 支付选中态背景色 */
    --pay-3: #FFCBCB;
    --pay-4: #FFD5A8;
    --pay-5: #FB3A3A;

    /* gradient */
    --gradient-1: #FFB800;
    --gradient-2: #FF2D3F;
    --gradient-3: #F418EF;

    --switch-on: #34c759;
    --switch-off: #ece9ec;
  }
}

@media (prefers-color-scheme: light) {
  :root {

    --black: #0B080B;
    --white: #FDFBFC;
    --grey-1: #FAF7FA;
    --grey-2: #F3F0F3;
    --grey-3: #ECE9EC;
    --grey-4: #CCCACB;
    --grey-5: #A1A0A3;
    --grey-6: #797B7D;
    --grey-7: #6E7071;
    --grey-8: #5F6162;
    --grey-9: #434546;
    --grey-10: #2E2F30;
    --grey-11: #292A2B;
    --grey-12: #242526;
    --grey-13: #1D1D1E;

    /* 标题、主要文字 */
    --text-1: #090609;
    /* 次强文字 */
    --text-2: #4d4e4f;
    /* 辅助文字 */
    --text-3: #626466;
    /* 禁用文字 */
    --text-4: #a1a2a3;
    /* 图片上主要文字 */
    --text-5: #fff;
    /* 图片上次要文字 */
    --text-6: #FFFFFFCC;
    /* 图片上辅助文字 */
    --text-7: #FDFBFCB3;
    /* 图片上禁用文字 */
    --text-8: rgba(253, 251, 252, 0.5);
    /* 浅色背景下深色文字 */
    --text-9: #0B080B;
    /* VIP文字 */
    --text-10: #5A280A;
    /* Surface */
    /* 基础背景 */
    --surface-1: #FDFBFC;
    /* 弹窗 */
    --surface-2: #F3F0F3;
    /* 弹窗+1 */
    --surface-3: #FDFBFC;
    --surface-4: #f7f4f7;
    /* toast */
    --surface-5: #3d3e3f;

    /* 背景色 */
    /* 填充色-浅 */
    --fill-1: #faf8fa;
    /* 填充色-一般 */
    --fill-2: #f7f4f7;
    /* 填充色-深 */
    --fill-3: #CCCACB;
    /* 填充色-白色 */
    --fill-4: #FDFBFC;
    --fill-5: #fff;

    /* 线 */
    /* 线条色-浅 */
    --line-1: #ece9ec;
    /* 线条色-一般 */
    --line-2: #CCCACB;
    /* 线条色-浅棕 */
    --line-3: #CCCACB;
    --line-4: #fb3a3a;

    /* system */
    /* 蓝色-链接、可点击 */
    --blue-6: #0766FF;
    /* 红色-警示 */
    --red-6: #FF3B30;
    /* 黄色-等待结果 */
    --yellow-6: #FCCF16;
    /* 绿色-通过结果 */
    --green-6: #34c759;

    /* brand */
    --brand-1: #ffeaea;
    --brand-3: #ff7b81;
    --brand-6: #fe2333;
    --brand-7: #e61d2e;

    /* mask */
    --mask: #09060980;

    /* pay */
    /* 支付背景色 */
    --pay-1: #FFE5E5;
    /* 支付价格点缀色 */
    --pay-2: #FFE5E5;
    /* 支付选中态背景色 */
    --pay-3: #FFCBCB;
    --pay-4: #FFD5A8;
    --pay-5: #FB3A3A;

    /* gradient */
    --gradient-1: #FFB800;
    --gradient-2: #FF2D3F;
    --gradient-3: #F418EF;

    --switch-on: #34c759;
    --switch-off: #ece9ec;
  }
}
