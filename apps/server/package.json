{"name": "@skynet/server", "private": true, "type": "module", "scripts": {"deploy:test": "bin/deploy.sh test", "deploy:prod": "bin/deploy.sh prod", "build": "nuxt build", "build:test": "nuxt build --mode test --dotenv .env.staging", "build:prod": "nuxt build --mode production --dotenv .env.production", "dev": "nuxt dev --host 0.0.0.0", "generate": "nuxt generate", "preview": "node .output/server/index.mjs", "postinstall": "nuxt prepare", "serve": "NITRO_PRESET=node_cluster node .output/server/index.mjs", "deploy:cdn": "yes | ossutil cp -r .output/public/ oss://us-dramato-prod/frontend_server/dramato/ && ossutil ls oss://us-dramato-prod/frontend_server/dramato/", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@airwallex/components-sdk": "^1.18.0", "@artmizu/nuxt-prometheus": "^2.5.0", "@better-scroll/core": "^2.5.1", "@nuxt/content": "3.5.1", "@nuxt/eslint": "1.3.0", "@nuxt/image": "1.10.0", "@nuxt/kit": "^3.17.3", "@nuxt/scripts": "0.11.6", "@nuxt/test-utils": "3.17.2", "@nuxt/ui": "3.1.0", "@skynet/client-track": "workspace:*", "@skynet/preset": "workspace:*", "@skynet/shared": "workspace:*", "@skynet/ui": "workspace:*", "@tailwindcss/vite": "catalog:", "@tweenjs/tween.js": "catalog:", "@unhead/vue": "^2.0.3", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "axios": "catalog:", "crypto-js": "catalog:", "dayjs": "catalog:", "eslint": "catalog:", "eta": "3.5.0", "fs-extra": "11.2.0", "github-markdown-css": "5.6.1", "imagemin": "^9.0.0", "imagemin-webp": "^8.0.0", "js-cookie": "catalog:", "js-md5": "catalog:", "lodash-es": "catalog:", "lottie-web": "^5.12.2", "marked": "^15.0.1", "md-editor-v3": "^5.0.1", "nuxt": "3.17.3", "pinia": "^3.0.1", "prom-client": "15.1.3", "shaka-player": "catalog:", "tailwindcss": "catalog:", "tcplayer.js": "5.3.2", "typescript": "catalog:", "vant": "4.9.17", "vconsole": "catalog:", "vh-check": "catalog:", "vite-bundle-analyzer": "catalog:", "vue": "catalog:", "vue-i18n": "^10.0.1", "vue-router": "catalog:", "zod": "catalog:"}, "devDependencies": {"@nuxt/typescript-build": "3.0.2", "@types/fs-extra": "11.0.4", "@unocss/nuxt": "66.1.2", "@vitejs/plugin-vue": "5.2.4", "@vitejs/plugin-vue-jsx": "4.1.2", "@vue/runtime-dom": "3.5.14", "eslint-plugin-format": "catalog:", "unocss": "66.1.2"}}