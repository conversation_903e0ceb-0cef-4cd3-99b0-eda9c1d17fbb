import { createComponent, getQuery, mc } from '@skynet/shared'
import { TransitionPresets, useTransition } from '@vueuse/core'
import { preloadImages } from 'src/lib/preload-images'
import { track } from 'src/lib/track'
import { useLocale } from 'src/lib/use-locale'
import { Back } from 'src/modules/common/back/back'
import Loading from 'src/modules/common/loading/loading'
import { showToast } from 'src/modules/common/toast/toast'
import { onMounted, onUnmounted, ref, watch, watchEffect } from 'vue'
import { useCoinsStore } from '../coins-store'
import { amazonCard, amazonDialog, background, coins, dialogBg, horn, mercadoPagoCard, ovoCard, ovoDialog, paypalCard, paypalDialog, philippinesCard, spotifyCard, thailandCard, vietnamCard, pixCard, pixDialog, philippinesDialog, malaysiaCard, malaysiaDialog, thailandDialog, vietnamDialog, indiaCard, successDialog, redeemDialog, exchangeCoins } from '../images/images'
import { ExchangeCard } from './exchange'
import { AmazonGift } from './gifts/amazon-gift'
import { PaypalGift } from './gifts/paypal-gift'
import { useBroadcast } from './use-broadcast'
import { SpotifyGift } from './gifts/spotify-gift'
// import { MercadoPagoGift } from './gifts/mercado-pago-gift'
import { OvoGift } from './gifts/ovo-gift'
import { Button } from '@skynet/ui'
import { openDialog } from 'src/modules/common/dialog'
import { useBetterScroll } from 'src/lib/use-better-scroll'
import { IndiaGift } from './gifts/india-gift'
import { MexicoGift } from './gifts/mexico-gift'
import { PhilippinesGift } from './gifts/philippines-gift'
import { VietnamGift } from './gifts/vietnam-gift'
import { MalaysiaGift } from './gifts/malaysia-gift'
import { ThailandGift } from './gifts/thailand-gift'
import { PixGift } from './gifts/pix-gift'
import { CoinsGift } from './gifts/coins-gift'
import { useRouter } from 'vue-router'
export const ExchangePage = createComponent(null, props => {
  const router = useRouter()
  const from = router.currentRoute.value.query.from
  const isFromMyEarnings = from === 'myearnings'
  const broadcasts = ref<string[]>([])
  const activeCard = ref<string>()

  preloadImages([horn, dialogBg])

  const { getBroadcast, broadcast, broadcastPool } = useBroadcast()
  const wrapper = ref<HTMLDivElement>()
  const { t } = useLocale(getQuery('language', 'en'))

  const { getExchangeDetails, exchangePageParams, pageLoading, getLastRedeemAccount } = useCoinsStore()

  const decodeString = (str: string) => {
    return decodeURIComponent(str).replace(/<red>(.*?)<\/red>/g, `<span class="text-[#e61d2e]">$1</span>`)
  }

  let interval: number | undefined = undefined

  const y = ref(100)

  const targetY = useTransition(y, {
    duration: 500,
    transition: TransitionPresets.easeOutCubic,
  })

  const handleClickCard = (item: ExchangeCard) => {
    track('exchangezone', 'brand', 'click')
    if (item.can_choose) {
      activeCard.value = item.name
    } else {
      showToast(t('coins.exchange.notOpen'))
    }
  }

  watch(() => exchangePageParams.value, () => {
    if (exchangePageParams.value) {
      if (exchangePageParams.value.exchange_list) {
        exchangePageParams.value.exchange_list.forEach(item => {
          switch (item.name) {
            case 'amazon':
              preloadImages([amazonCard, amazonDialog])
              break
            case 'paypal':
              preloadImages([paypalCard, paypalDialog])
              break
            case 'spotify':
              preloadImages([spotifyCard])
              break
            case 'brazil':
              preloadImages([pixCard, pixDialog])
              break
            case 'ovo':
              preloadImages([ovoCard, ovoDialog])
              break
            case 'india':
              preloadImages([indiaCard])
              break
            case 'mexico':
              preloadImages([mercadoPagoCard, successDialog, redeemDialog])
              break
            case 'philippines':
              preloadImages([philippinesCard, philippinesDialog])
              break
            case 'vietnam':
              preloadImages([vietnamCard, vietnamDialog])
              break
            case 'malaysia':
              preloadImages([malaysiaCard, malaysiaDialog])
              break
            case 'thailand':
              preloadImages([thailandCard, thailandDialog])
              break
            default:
              break
          }
        })
        if (exchangePageParams.value.exchange_list.filter(item => item.can_choose).length > 0) {
          activeCard.value = exchangePageParams.value.exchange_list.filter(item => item.can_choose)[0].name
        }
      }
    }
  })

  watchEffect(() => {
    if (broadcastPool.value.length > 0 && !interval) {
      interval = window.setInterval(() => {
        const b = broadcast()
        if (b) {
          y.value -= 100
          broadcasts.value.push(decodeString(b))
        }
      }, 5000)
    }
  })

  const scrollY = ref(0)
  const handleScroll = () => {
    scrollY.value = window.scrollY
  }

  const explainWrapper = ref<HTMLDivElement>()

  const { init: initExplain, bsInstance: explainBsInstance } = useBetterScroll(explainWrapper)

  const openHelpDialog = () => {
    const closeDialog = openDialog({
      title: 'Aturan Penukaran Hadiah',
      customClass: 'bg-[#FDFBFC] rounded-xl shadow',
      body: () => (
        <x-explain-dialog>
          <x-explain ref={explainWrapper} class="mt-1 block h-[200px] overflow-hidden break-words px-[26px] text-sm font-normal text-[#090609]">
            <x-wrapper class="block pb-7" style="-webkit-overflow-scrolling: touch;">
              <p>1. Platform ini menyediakan fitur penarikan tunai, yang dapat ditarik ke akun pembayaran Anda (seperti Paypal, OVO, dll, sesuai dengan tampilan halaman yang sebenarnya).</p>
              <p>2. Platform ini menyediakan fitur pertukaran kartu hadiah, yang akan diberikan dalam bentuk kode kartu ke akun FreeReels Anda (seperti kartu hadiah Amazon, Google Play Store, dll, sesuai dengan tampilan halaman yang sebenarnya).</p>
              <p>3. Penarikan tunai dan kartu hadiah dibagi menjadi berbagai tingkatan berdasarkan nilai hadiah. Ketika koin Anda memenuhi persyaratan penarikan, Anda dapat melakukan penarikan. Setiap pengguna hanya dapat melakukan penarikan/pertukaran sekali dalam sehari, dengan jumlah total tidak terbatas. Beberapa tingkatan adalah keuntungan satu kali, hanya untuk pengguna baru. Untuk memberikan pengalaman penarikan yang lebih baik, FreeReels akan secara berkala memberikan kuota penarikan sementara atau kuota yang dapat ditarik setelah memenuhi persyaratan tertentu kepada sebagian atau seluruh pengguna (baru). Jika pengguna memperoleh jenis kuota ini, jumlah penggunaan terbatas. Persyaratan spesifik, kuota, dan batasan jumlah penggunaan dapat dilihat pada tampilan halaman.</p>
              <p>4. Ketika pengguna memilih untuk menarik tunai, akan dikenakan biaya transfer yang harus ditanggung oleh pengguna. Jumlah tunai yang diterima akhirnya adalah jumlah setelah biaya transfer dipotong.</p>
              <p>5. Ketika pengguna memilih untuk menarik tunai, Anda perlu memasukkan akun platform pembayaran pihak ketiga Anda di FreeReels. Pastikan Anda memasukkan dengan benar untuk menghindari kegagalan penarikan.</p>
              <p>6. Ketika pengguna memilih untuk menarik tunai, biasanya akan diterima dalam waktu 3-5 hari (jika bertepatan dengan puncak penarikan atau hari libur, waktu mungkin akan diperpanjang). Selama puncak aktivitas, karena kemacetan jaringan, pengguna mungkin tidak dapat melakukan penarikan dalam waktu singkat. Platform akan berusaha keras untuk memulihkan fungsi penarikan secepat mungkin. Namun, platform tidak bertanggung jawab atas hal ini.</p>
              <p>7. Penarikan tunai dan pertukaran kartu hadiah memiliki batas total stok harian. Kami berkomitmen bahwa kegiatan ini adalah nyata dan efektif, tetapi jumlah stok setiap wilayah akan disesuaikan sesuai dengan tahap kegiatan lokal. Misalnya, pada awal peluncuran, platform akan mengatur selama beberapa waktu untuk memastikan bahwa hadiah dapat diberikan kepada pengguna yang sebenarnya. Seiring dengan matangnya kegiatan di wilayah tersebut, platform akan berusaha agar setiap pengguna mendapatkan hadiah yang seharusnya. Namun, platform tidak bertanggung jawab atas hal ini.</p>
              <p>8. Jika Anda tidak masuk ke halaman manfaat selama 30 hari berturut-turut, atau tidak melakukan penarikan atau pertukaran kartu hadiah dalam periode kegiatan ini (satu saja), maka semua manfaat yang telah diberikan platform kepada Anda sebelumnya akan kedaluwarsa. Kegagalan untuk menarik atau menukarkan kartu hadiah dalam waktu yang ditentukan dianggap sebagai penghapusan hak Anda untuk menarik atau menukarkan kartu hadiah. Saldo hadiah dalam akun Anda akan direset menjadi nol, dan platform tidak akan dan tidak berkewajiban memberikan kompensasi dalam bentuk apa pun.</p>
              <p>9. Kami menggunakan teknologi kecerdasan buatan canggih untuk menganalisis perilaku Anda. Untuk melindungi keamanan akun dan aset Anda selama proses penarikan/pertukaran hadiah, platform berhak untuk meninjau pesanan Anda, membatasi jumlah penarikan/jumlah akun, dan meningkatkan langkah-langkah keamanan kapan saja (termasuk tetapi tidak terbatas pada verifikasi SMS, verifikasi identitas, pengenalan wajah, dll). Jika Anda gagal melalui verifikasi keamanan, Anda tidak akan dapat melakukan penarikan/pertukaran hadiah. Jika ditemukan tindakan palsu atau kecurangan lainnya, kami berhak untuk menghentikan Anda dari menggunakan platform (mengisi kode undangan, mengumpulkan koin, menarik tunai, mendapatkan hadiah) serta mencabut hadiah yang telah Anda peroleh. Pengguna harus menanggung konsekuensi negatif dari tidak dapat menarik/pertukaran hadiah, dan platform tidak bertanggung jawab atas hal ini.</p>
              <p>10. Hadiah atau keuntungan yang diperoleh pengguna melalui platform mungkin memerlukan potongan pajak atau pelaporan pajak oleh platform. Untuk memenuhi kewajiban hukum ini, platform perlu mengumpulkan dan memberikan informasi terkait pajak kepada otoritas pajak sesuai dengan persyaratan yang berlaku, termasuk informasi identitas pengguna, jumlah keuntungan, dan lainnya. Jika pengguna gagal memberikan informasi atau memberikan informasi yang salah, dapat menyebabkan kesalahan dalam pelaporan pajak. Jika platform tidak dapat memprosesnya, pengguna harus melakukan pelaporan pajak secara mandiri, dan konsekuensi negatif lainnya akan ditanggung oleh pengguna sendiri.</p>
              <p>11. Dalam batas-batas hukum dan peraturan yang berlaku, platform berhak untuk mengubah atau menyesuaikan aturan ini. Perubahan atau penyesuaian yang terkait akan dipublikasikan di halaman aturan dan akan berlaku segera setelah dipublikasikan. Jika pengguna terus berpartisipasi dalam kegiatan, maka dianggap bahwa pengguna menyetujui dan menerima aturan yang telah diubah atau disesuaikan. Jika pengguna menolak perubahan atau penyesuaian aturan, maka pengguna dapat memilih untuk tidak berpartisipasi dalam kegiatan yang telah diubah.</p>
            </x-wrapper>
          </x-explain>
          <x-footer class="relative flex flex-col bg-white">
            <x-mask class="absolute -top-[38px] h-10 w-full bg-gradient-to-b from-[rgba(255,_255,_255,_0)] to-[rgba(255,_255,_255,_1)]" />
            <x-got-it>
              <Button class="no-tap-color mx-4 mb-6 mt-4 h-11 w-[calc(100%-2rem)] rounded-lg border-none bg-[#fc2763] text-base text-white outline-none" onClick={() => {
                closeDialog()
                explainBsInstance.value?.destroy()
              }}
              >{t('coins.exchange.gotItBtn')}
              </Button>
            </x-got-it>
          </x-footer>
        </x-explain-dialog>
      ),
    })
    initExplain()
  }

  watch(() => activeCard.value, () => {
    const type = exchangePageParams.value?.exchange_list
      ?.filter(item => item.name === activeCard.value)[0]
      ?.gift_list[0]?.type
    if (type && type !== 'coins') {
      void getLastRedeemAccount(type)
    }
  })

  onMounted(() => {
    document.title = t('coins.exchange.title')
    track('exchangezone', 'page', 'show', {
      enter_from: from,
    })
    window.addEventListener('scroll', handleScroll)
    void getBroadcast()
    void getExchangeDetails()
  })

  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
    if (interval) {
      window.clearInterval(interval)
    }
  })

  return () => (
    pageLoading.value
      ? (
          <Loading />
        )
      : (
          <x-my-exchange-page class="share-bottom-8 pad:max-w-[var(--phone-page-max-width)] pc:max-w-[var(--phone-page-max-width)] relative mx-auto flex min-h-full w-full max-w-[var(--phone-page-max-width)] flex-col gap-y-4 bg-[#f7f4f7] text-white" ref={wrapper}>
            <img src={background} class="absolute left-0 top-0 z-0 h-auto w-full" />
            <Back title={t('coins.exchange.title')} isWhite={true} isCloseWebview={isFromMyEarnings ? false : true} class={mc('bg-transparent shrink-0 sticky', scrollY.value > 44 ? 'bg-[#fc2763]' : '')}>
              { exchangePageParams.value?.show_help_dialog && <p class="z-up-up absolute right-3 text-sm text-white" onClick={openHelpDialog}>Aturan</p>}
            </Back>
            <x-rules class="z-up relative flex items-center gap-x-[2px] px-5 text-base font-bold">
              <img src={coins} width={16} height={16} class="mb-px size-[18px] object-contain" />
              <span>{exchangePageParams.value?.coin_info?.split('≈')[0]}</span>
              <span>≈</span>
              <span>{exchangePageParams.value?.coin_info?.split('≈')[1]}</span>
            </x-rules>
            <x-broadcast class="z-up relative mx-[12px] h-[28px] rounded-lg bg-[#FDFBFC] py-[6px] pl-[44px] text-[12px] font-normal leading-[16px] text-[#090609]">
              <img src={horn} class="absolute -top-[4px] left-[8px] size-[28px]" />
              <x-wrapper class="relative block h-full overflow-hidden">
                <x-broadcast-list
                  class="relative block size-full" style={{
                    transform: `translateY(${targetY.value}%)`,
                  }}
                >
                  {
                    broadcasts.value?.map(broadcast => (
                      <x-broadcast-item innerHTML={broadcast ?? ''} class="m-0 block truncate p-0" />
                    ),
                    )
                  }
                </x-broadcast-list>
              </x-wrapper>
            </x-broadcast>
            <x-exchange-area class="z-up relative mx-3 -mt-1 flex w-[calc(100%-1.5rem)] flex-col rounded-lg bg-white">
              <x-title class="block p-3 text-lg font-bold text-[#090609]">{t('coins.earnings.exchange')}</x-title>
              <x-cards class="flex items-center justify-start gap-x-3 px-3 pb-3 pt-2">
                {
                  exchangePageParams.value?.exchange_list?.map(item => {
                    return (
                      <x-card onClick={() => handleClickCard(item)} class={mc('box-border h-[68px] flex justify-center items-center w-[calc(33.333%-0.5rem)] shrink-0 grow-0 rounded-lg px-[10px] py-[14px] outline outline-1 outline-offset-[-1px] outline-[#FFD5A8]', activeCard.value === item.name ? '!outline-2 outline-offset-[-2px] outline-[#FB3A3A]' : '')}>
                        <img class={mc('object-cover w-full h-auto bg-white rounded-lg', item.can_choose ? '' : 'opacity-20')} src={item.name === 'Coins' ? exchangeCoins : item.image_url} />
                      </x-card>
                    )
                  })
                }
              </x-cards>
              <x-gift-list class="flex flex-col pb-3">
                {
                  exchangePageParams.value?.exchange_list
                    ?.filter(item => item.name === activeCard.value)[0]
                    ?.gift_list?.map(
                      item => item.type === 'amazon'
                        ? <AmazonGift gift={item} />
                        : item.type === 'paypal'
                          ? <PaypalGift gift={item} />
                          : item.type === 'spotify'
                            ? <SpotifyGift gift={item} />
                            : item.type === 'brazil'
                              ? <PixGift gift={item} />
                              : item.type === 'ovo'
                                ? <OvoGift gift={item} />
                                : item.type === 'india'
                                  ? <IndiaGift gift={item} />
                                  : item.type === 'mexico'
                                    ? <MexicoGift gift={item} />
                                    : item.type === 'philippines'
                                      ? <PhilippinesGift gift={item} />
                                      : item.type === 'vietnam'
                                        ? <VietnamGift gift={item} />
                                        : item.type === 'malaysia'
                                          ? <MalaysiaGift gift={item} />
                                          : item.type === 'thailand'
                                            ? <ThailandGift gift={item} />
                                            : item.type === 'coins'
                                              ? <CoinsGift gift={item} />
                                              : null)
                }
              </x-gift-list>
            </x-exchange-area>
          </x-my-exchange-page>
        )
  )
})

export default ExchangePage
