import { useLocale } from 'src/h5_modules/i18n/i18n'
import { openedInDev } from '@skynet/shared/env-helper'

const { currentLocale } = useLocale()
export const gotoRules = (type: 'terms' | 'privacy') => {
  const preLan = currentLocale.value?.language?.split('-')[0]
  let language = currentLocale.value.language
  let country = ''

  if (language === 'es-ES') {
    language = 'es-MX'
  }
  if (preLan === 'es') {
    country = 'MX'
  } else {
    country = currentLocale.value?.language?.split('-')[1]
  }
  if (openedInDev) {
    window.location.href = window.location.origin + (`/dramawave/rules/${language}/` + type + '.html')
  } else {
    window.location.href = window.location.origin + ('/rules/' + type + `.html?language=${preLan}&country_code=${country}`)
  }
}
