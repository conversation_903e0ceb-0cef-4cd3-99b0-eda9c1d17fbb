import { ref } from 'vue'
import { bindLoading, createCachedFn } from '@skynet/shared'
import { apiWalletMy } from '../profile/profile-api'
import type { WalletDataInfo } from '../profile/profile'

const fetchingWallet = ref(false)
const fetchWallet = async () => {
  const response = await bindLoading(apiWalletMy(), fetchingWallet)
  if (!response?.data) return
  wallet.value = response.data
}
const wallet = ref<WalletDataInfo>()
export const useMyWallet = createCachedFn(() => {
  return {
    fetchWallet,
    wallet,
  }
})
