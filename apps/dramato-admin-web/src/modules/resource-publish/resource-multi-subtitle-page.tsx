/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Pager, Button, transformTimestamp, transformNumber, transformNumberArray, Tooltip } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { apiGetMultiSubtitleList } from './resource-multi-subtitle-api'
import { useRouter } from 'vue-router'
import { renderTitle, renderStatusDesc } from './util'
import { useResourceStore } from './use-resource-publish-store'
import { taskStatusOptions, auditStatusForQuery, releaseRounds } from './constant'
import { langKey, langValue } from 'src/modules/resource/constant'
import { useImagePreviewStore } from '@skynet/ui/image/use-preview-store'
import { formatSecondsToString } from './util'

type MultiSubtitlePageOptions = {
  props: {}
}
export const MultiSubtitlePage = createComponent<MultiSubtitlePageOptions>({
  props: {},
}, props => {
  const {
    getTaskRole,
    resourceRole,
    all_members,
    onDistributeTask,
    onGetTask,
  } = useResourceStore()
  const resourceType = 3
  const router = useRouter()
  const loading = ref(false)
  const { showImagePreviewDialog } = useImagePreviewStore()
  const list = ref<M.MultiSubtitle.Resource[]>([])
  const defaultParams: M.IResourcePublishQueryParams = {
    title: '',
    id: undefined,
    create_start_time: undefined, // 录入开始时间
    create_end_time: undefined, // 录入结束时间
    series_key_or_title_list: '',
    resource_id_or_title_list: '',
    claim_names: [],
    task_status: 0,
    audit_status_list: [],
    partner_name: '',
  }
  const form = ref<M.IResourcePublishQueryParams>(defaultParams)
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const Table = CreateTableOld<M.MultiSubtitle.Resource>()
  const QueryForm = CreateForm<M.IResourcePublishQueryParams>()

  const getList = async () => {
    const params = {
      ...form.value,
      ...pageInfo.value,
    }
    loading.value = true
    list.value = []
    try {
      const res = await apiGetMultiSubtitleList(params)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const columns: TableColumnOld<M.MultiSubtitle.Resource>[] = [
    ['资源ID', row => (
      <div>
        <div>
          {row.id}
        </div>
        {row.my === 1 ? <div class="text-primary">【我的】</div> : null}
      </div>
    ), { class: 'w-[140px]' }],
    ['资源名称', row => renderTitle(row), { class: 'w-[220px]' }],
    ['总时长', row => {
      return  <span>{row.count}集 / { !row.duration ? '-' : formatSecondsToString(row.duration) }</span>
    }, { class: 'w-[140px]' }],
    ['认领人', 'claim_name', { class: 'w-[120px]' }],
    ['合作方', 'partner_name', { class: 'w-[140px]' }],
    ['多语言生成状态', row => {
      const checkedLang = row.multi_subtitle_progress.filter(t => t.status === 1)
      let hasChecked = ''
      let notChecked = ''
      if (checkedLang.length === row.multi_subtitle_progress.length) {
        hasChecked = '全部语言通过'
      } else {
        hasChecked = checkedLang.length > 0 ? `已通过校验：${checkedLang.map(row => langValue[langKey.findIndex(i => i === row.language_code)]).join(',')}` : ''
        notChecked = `未通过校验：${row.multi_subtitle_progress.filter(t => t.status !== 1).map(row => langValue[langKey.findIndex(i => i === row.language_code)]).join(',')}`
      }
      return (
        <div class="text-center">
          <Tooltip popContent={() => hasChecked ? <div> <div>{hasChecked}</div> <div>{notChecked}</div> </div> : null}>
            <span>{checkedLang.length}/{row.multi_subtitle_progress.length}</span>
          </Tooltip>
        </div>
      )
    }, {
      class: 'text-center w-[100px]',
    }],
    ['多语言校验状态', row => {
      const checkedLang = row.multi_subtitle_audit_progress.filter(t => t.status === 2)
      let hasChecked = ''
      let notChecked = ''
      if (checkedLang.length === row.multi_subtitle_audit_progress.length) {
        hasChecked = '全部语言通过'
      } else {
        hasChecked = checkedLang.length > 0 ? `已通过校验：${checkedLang.map(row => langValue[langKey.findIndex(i => i === row.language_code)]).join(',')}` : ''
        notChecked = `未通过校验：${row.multi_subtitle_audit_progress.filter(t => t.status !== 2).map(row => langValue[langKey.findIndex(i => i === row.language_code)]).join(',')}`
      }
      return (
        <div class="text-center">
          <Tooltip popContent={() => hasChecked ? <div> <div>{hasChecked}</div> <div>{notChecked}</div> </div> : null}>
            <span>{checkedLang.length}/{row.multi_subtitle_audit_progress.length}</span>
          </Tooltip>
        </div>
      )
    }, { class: 'w-[110px] text-center' }],

    ['英语审核状态', row => {
      if (row.audit_status === 1) {
        return (
          <div class="tooltip flex items-center gap-x-1" data-tip={row.audit_reason}>
            <div class="tooltip line-clamp-1 flex-1 overflow-hidden text-red-500">
              <span class="text-[var(--text-3] text-xs">审核驳回：{row.audit_reason}</span>
            </div>
            {row.reject_image_list && row.reject_image_list.length > 0 ? (
              <div class="size-[60px]">
                <img class="size-full cursor-pointer object-contain" src={row.reject_image_list?.[0]} alt="" onClick={() => {
                  showImagePreviewDialog({
                    imageList: row.reject_image_list?.map(imageUrl => ({
                      src: imageUrl,
                      width: 2000,
                    })) || [],
                    canEscClose: true,
                  })
                }} />
              </div>
            ) : null}
          </div>
        )
      } else {
        return renderStatusDesc(row, true)
      }
    }, { class: 'text-center w-[240px]' }],
    ['英语审核进度', row => {
      const en_audit_process = row.en_audit_process
      if (en_audit_process) {
        return <div>{en_audit_process.finish_count}/{en_audit_process.total_count}</div>
      } else {
        return '-'
      }
    }, { class: 'w-[110px] text-center' }],
    ['操作', (row, index) => (
      <div class="flex justify-center gap-x-2">
        <Button
          class="btn btn-link btn-xs"
          onClick={() => {
            void router.push(`/resource-publish/multi-subtitle/detail/${row.id}`)
          }}
        >
          字幕校验
        </Button>
        {
          resourceRole.value === 1
            ? (
                <Button
                  class="btn btn-link btn-xs"
                  onClick={() => {
                    void onDistributeTask({
                      type: resourceType,
                      resource_ids: [row.id],
                    }, getList)
                  }}
                >
                  分配任务
                </Button>
              )
            : null
        }
      </div>
    ), {
      class: 'w-[150px] text-center',
    },
    ],
  ]

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = { ...defaultParams, audit_status_list: [] }
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }

  onQuery()
  void getTaskRole({
    type: resourceType,
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>多语言字幕校验</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              ['资源名称/ID：', 'resource_id_or_title_list', { type: 'text', placeholder: '请输入资源名称或者id' }],
              ['剧集名称/ID：', 'series_key_or_title_list', { type: 'text', placeholder: '请输入资源名称' }],
              ['认领人：', 'claim_names', { type: 'multi-select', search: true, options: all_members.value.map(m => {
                return {
                  value: m,
                  label: m,
                }
              }) }],
              ['合作方：', 'partner_name', { type: 'text', placeholder: '请输入合作方' }],
              ['认领状态：', 'task_status', { type: 'select', options: taskStatusOptions, autoInsertEmptyOption: false }, { transform: transformNumber }],
              ['审核状态：', 'audit_status_list', { type: 'multi-select', maxlength: 1, options: auditStatusForQuery }],
              [
                ['录入时间-开始：', 'create_start_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
                ['录入时间-结束：', 'create_end_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
              ],
              ['发行轮次：', 'release_round', { type: 'select', placeholder: '请选择发行轮次', options: releaseRounds }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex items-center justify-between">
            <span>多语言字幕校验列表</span>
            <div class="space-x-2">
              { resourceRole.value === 2
                ? (
                    <button class="btn btn-primary btn-sm" onClick={() => {
                      void onGetTask(resourceType, onQuery)
                    }}
                    >领取任务
                    </button>
                  )
                : null}
            </div>
          </x-table-actions>
        ),
        table: () => (
          <Table
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default MultiSubtitlePage
