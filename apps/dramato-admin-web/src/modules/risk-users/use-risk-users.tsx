/* eslint-disable @typescript-eslint/no-explicit-any */
import { openDialog } from '@skynet/ui'
import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { apiGetActivityList, apiSaveAudit } from './risk-users-api'
import { createCachedFn } from '@skynet/shared'

const initFromData = {
  id: undefined,    // id
  user_id: "",         // 用户ID
  device_id: "",   // 设备ID
  start_time: "",  // 开始时间
  end_time: "",    // 结束时间
  status: 0,       // 状态
}

const form = ref<M.RiskUsersItem>(cloneDeep(initFromData))

const config = ref<M.Config>({
  lang_items: {
    'de-DE': 'German',
    'en-US': 'English',
    'es-MX': 'Spanish',
    'fr-FR': 'French',
    'id-ID': 'Indonesian',
    'it-IT': 'Italian',
    'ja-JP': 'Japanese',
    'ko-KR': 'Korean',
    'pt-PT': 'Portuguese',
    'ru-RU': 'Russian',
    'th-TH': 'Thai',
    'tl-PH': 'Filipino',
    'tr-TR': 'Turkish',
    'vi-VN': 'Vietnamese',
    'zh-CN': '简体中文',
    'zh-TW': '繁体中文',
  },
})


export const useRiskUsers = createCachedFn(() => {
  const searchForm = ref<M.RiskUsersSearchOption>({
    user_id: undefined,  // 用户id
    device_id: undefined,  // 设备id
    page_index: 1,
    page_size: 20
  })

  const list = ref<M.RiskUsersItem[]>([])
  const total = ref<number>(10)
  const applicationList = ref<Array<{ label: string, value: number, platform: number, language: string[] }>>([])
  const closeEditPushNotificationModal = ref(() => { })
  const loading = ref(false)
  const isUpdating = ref(false)

  const InitPushNotificationOption: M.PushNotification = {
    priority: 3,
    user_identify_val: '',
    notify_btn_bg_color: '#FC2763',
  }

  const currentNotification = ref<M.PushNotification>(InitPushNotificationOption)

  const getList = async () => {
    loading.value = true
    const newSearchForm = {
      ...searchForm.value,
      user_id: Number(searchForm.value.user_id) || undefined
    }
    const rs = await apiGetActivityList(newSearchForm)
    list.value = rs.data?.risk_user_info || []
    total.value = rs.data?.total || 0
    loading.value = false
  }

  const onSearchRiskUsersList = (isFirst?: boolean) => {
    if (isFirst) {
      searchForm.value.page_index = 1
      searchForm.value.page_size = 20
    }
    void getList()
  }

  const saveAudit = async (data: M.AuditItem) => {
    const rs = await apiSaveAudit(data)
    return rs
  }

  const onPageChange = (page_index: number) => {
    searchForm.value.page_index = page_index
    onSearchRiskUsersList()
  }

  const onPageSizeChange = (page_size: number) => {
    searchForm.value.page_size = page_size
    searchForm.value.page_index = 1
    onSearchRiskUsersList()
  }

  const onReset = () => {
    searchForm.value.user_id = undefined
    searchForm.value.device_id = undefined
    searchForm.value.page_index = 1
    searchForm.value.page_size = 20
    onSearchRiskUsersList(true)
  }

  const pushConfig = ref<M.PushConfigData>()


  const getLangCode = (lang: string) => {
    return Object.keys(config.value.lang_items).find(key => config.value.lang_items[key] === lang)
  }

  const appTypeOptions = [
    { label: '全部', value: 3 },
    { label: '安卓', value: 1 },
    { label: 'IOS', value: 2 },
  ]
  const activityTypeOptions = [
    { label: '全部', value: 0 },
    { label: '评论活动', value: 1 }
  ]

  const activityPageOptions = [
    { label: '评论页面', value: 1 }
  ]
  const activityStatusOptions = [
    { label: '不限', value: 0 },
    { label: '待发布', value: 1 },
    { label: '已发布', value: 2 },
    { label: '下架', value: 3 }
  ]

  return {
    searchForm,
    form,
    initFromData,
    list,
    total,
    closeEditPushNotificationModal,
    InitPushNotificationOption,
    loading,
    onPageChange,
    onPageSizeChange,
    onReset,
    onSearchRiskUsersList,
    currentNotification,
    applicationList,
    isUpdating,
    config,
    pushConfig,
    getLangCode,
    appTypeOptions,
    activityTypeOptions,
    activityPageOptions,
    activityStatusOptions,
    saveAudit
  }
})
