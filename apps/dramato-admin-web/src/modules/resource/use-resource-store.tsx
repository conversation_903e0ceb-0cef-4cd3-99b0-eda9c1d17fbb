/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { openDialog, showFailToast, Button, showSuccessToast, showToast, SvgIcon, Icon, Input, CreateForm, CreateTableOld, TableColumnOld, Tooltip } from '@skynet/ui'
import { Uploader, type UploadImage } from '../common/uploader/uploader'
import { apiUploadSaveVideo, apiUploadSaveSubtitle, apiGetSeriesInfo, apiUploadSaveAudio, apiUpdateSeriesInfo, apiBaiduDiskUploadProgress } from './resource-api'
import { langValue, langKey, langKeyForCol } from './constant'
import { apiGetLabelList, apiBaiduDiskUpload, apiUploadExcel, apiUploadBusinessExcel, apiGetExternalTransList } from './resource-api'
import { FileList } from './components/file-list'
import { set, get } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'

const notFinished = ['trans_failed', 'failed', '']
const diskUploadProgress: { [record: string]: string } = {
  source: '带字幕视频',
  clear: '不带字幕视频',
  cover: '封面',
  subtitle: '字幕',
}

langKeyForCol.forEach((lang, index) => {
  diskUploadProgress[`${lang}_subtitle`] = `${langValue[index]}字幕`
})
const sync_status_colors = ['', 'bg-primary text-white', 'bg-primary text-white', 'bg-green-500 text-white', 'bg-red-500 text-white']
const sync_status_map: { [record: number]: string } = {
  1: '同步中',
  2: '同步中',
  3: '已同步',
  4: '失败',
}

type IMissingCol = { [record: string]: { missingNum?: number[], nums: number[], moreNums?: number[] } }
const tags = ref<M.ITag[]>([])
const uploadSyncList = ref<M.IDiskInfoUploadProgress[]>([])
const uploadSyncLoading = ref(false)
const taskLoading = ref(false)
const tipColumns = ref<IMissingCol>({})
// 资源详情内，平铺的剧集信息
const episodeSeriesInfoList = ref<M.IEpisodeSeriesInfo[]>([])
const externalTransProgress = ref<M.IExternalTransProgress[]>([])

const getEpisodeSeriesInfoList = async (params: { series_resource_id: number }) => {
  // fix: 监听失效
  episodeSeriesInfoList.value = []
  const res = await apiGetSeriesInfo(params)
  const list = res.data?.list
  episodeSeriesInfoList.value = langKey.map(langCode => {
    const item = list?.find(row => row.language_code === langCode)
    if (item) {
      return item
    } else {
      return {
        cover: '',
        title: '',
        description: '',
        is_sync: 0,
        language_code: langCode,
        push_content_list: [],
        horizontal_cover: '',
      }
    }
  })
}

const updateEpisodeSeriesInfoList = async (data: { series_resource_id: number, list: M.IEpisodeSeriesInfo[] }) => {
  try {
    await apiUpdateSeriesInfo(data)
  } catch (error: any) {
    showFailToast(error.response.data.message || '创建失败')
  }
}

type IVideoInfo = {
  temp_path: string
  duration: number
  file_path: string
  file_size: number
  serial_number: number
}

const checkVideos = (files: File[]) => {
  return Promise.all(files.map(file => {
    return new Promise(resolve => {
      const videoElement = document.createElement('video')
      const videoURL = URL.createObjectURL(file)
      videoElement.src = videoURL
      // 确保视频已加载元数据，否则 videoWidth 和 videoHeight 可能为 0
      videoElement.addEventListener('loadedmetadata', () => {
        const width = videoElement.videoWidth
        const height = videoElement.videoHeight
        console.log(`视频清晰度: ${width}x${height}`)
        resolve({
          fileName: file.name,
          validate: Math.min(width, height) >= 1000,
        })
      })
    })
  }))
}

const refreshSyncList = async (series_resource_id: number) => {
  uploadSyncLoading.value = true
  try {
    const res = await apiBaiduDiskUploadProgress({
      series_resource_id: series_resource_id,
    })
    uploadSyncList.value = res.data?.list || []
  } catch (error: any) {
    showFailToast(error.response.data.msg || error.response.data.message || '操作失败')
  } finally {
    uploadSyncLoading.value = false
  }
}

const onDiskUpload = (series_resource_id: number, refresh: () => void) => {
  const Form = CreateForm<M.IDiskInfo>()
  const form = ref<M.IDiskInfo>({
    series_resource_id: series_resource_id,
    pure_video_path: '',
    origin_video_path: '',
    subtitle_path: '',
    subtitle_language_code: '',
    cover_path: '',
  })

  const options = langKey.map((n, index) => {
    return { label: langValue[index], value: n }
  })

  const showFileList = (path: string, chooseFileExts: string[] = []) => {
    const dir = ref(get(form.value, path, '/'))
    const closeDialog = openDialog({
      showParentWhenChildClose: true,
      title: '网盘导入',
      mainClass: '!p-4 !pb-0 !pt-0',
      body: () => (
        <FileList
          dir={dir.value}
          onClose={() => {
            closeDialog()
          }}
          chooseFileExts={chooseFileExts}
          onSave={(str: string) => {
            set(form.value, path, str)
            closeDialog()
          }}
          onDirChange={(e: string) => dir.value = e}
        />
      ),
    })
  }

  const closeDialog = openDialog({
    title: '网盘资源上传',
    mainClass: '!p-4 !pb-0',
    hideParentWhenChildOpen: true,
    body: () => (
      <>
        <Form
          class="grid grid-cols-4 gap-y-3"
          data={form.value}
          hasAction={false}
          onChange={(path, value: any) => {
            set(form.value, path, value)
          }}
          items={[
            { label: '含字幕视频地址', class: 'col-span-4', path: 'origin_video_path', input: { type: 'custom', render: () => {
              return (
                <div class="flex w-full items-center gap-x-4">
                  <div class="input input-bordered flex h-8 flex-1 items-center gap-1">
                    <input type="text" class="tm-input block w-full" value={form.value.origin_video_path} disabled={true} placeholder="请选择目录" />
                  </div>
                  <SvgIcon class="cursor-pointer" name="ic_dir" onClick={() => showFileList('origin_video_path')} />
                </div>
              )
            } } },
            { label: '不含字幕视频地址', class: 'col-span-4', path: 'pure_video_path', input: { type: 'custom', render: () => {
              return (
                <div class="flex w-full items-center gap-x-4">
                  <div class="input input-bordered flex h-8 flex-1 items-center gap-1">
                    <input type="text" class="tm-input block w-full" disabled={true} value={form.value.pure_video_path} placeholder="请选择目录" />
                  </div>
                  <SvgIcon class="cursor-pointer" name="ic_dir" onClick={() => showFileList('pure_video_path')} />
                </div>
              )
            } } },
            { label: '字幕语言', class: 'col-span-1', path: 'subtitle_language_code', input: { type: 'select', options: options } },
            { label: '字幕目录地址', class: 'col-span-3', path: 'subtitle_path', input: { type: 'custom', render: () => {
              return (
                <div class="flex w-full items-center gap-x-4">
                  <div class="input input-bordered flex h-8 flex-1 items-center gap-1">
                    <input type="text" class="tm-input block w-full" value={form.value.subtitle_path} disabled={true} placeholder="请选择目录" />
                  </div>
                  <SvgIcon class="cursor-pointer" name="ic_dir" onClick={() => showFileList('subtitle_path')} />
                </div>
              )
            } } },
            { label: '封面图地址', class: 'col-span-4', path: 'cover_path', input: { type: 'custom', render: () => {
              return (
                <div class="flex w-full items-center gap-x-4">
                  <div class="input input-bordered flex h-8 flex-1 items-center gap-1">
                    <input type="text" class="tm-input block w-full" value={form.value.cover_path} disabled={true} placeholder="请选择封面文件" />
                  </div>
                  <SvgIcon class="cursor-pointer" name="ic_dir" onClick={() => showFileList('cover_path', ['jpg', 'png', 'jpeg'])} />
                </div>
              )
            } } },
          ]}
        />
        <Button class="btn btn-primary btn-sm float-right mt-4" disabled={taskLoading.value} onClick={async () => {
          try {
            if (!form.value.pure_video_path
              && !form.value.origin_video_path
              && !form.value.subtitle_path
              && !form.value.cover_path) {
              showFailToast('请输入上传地址')
              return
            }
            if (form.value.subtitle_path && !form.value.subtitle_language_code) {
              showFailToast('请选择字幕语言')
              return
            }
            taskLoading.value = true
            await apiBaiduDiskUpload(form.value)
            void refreshSyncList(series_resource_id)
            showSuccessToast('操作成功！')
            closeDialog()
          } catch (error: any) {
            showFailToast(error.response.data.msg || error.response.data.message || '操作失败')
          } finally {
            taskLoading.value = false
          }
        }}
        >
          {taskLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
          提交下载任务
        </Button>
      </>
    ),
  })
}

const showSyncSummary = (list: M.IDiskInfoUploadProgress[], series_resource_id: number, refresh?: () => void) => {
  const isSyncList = list.filter(row => [1, 2].includes(row.sync_status))
  const isFailed = list.filter(row => [4].includes(row.sync_status))
  return (
    isSyncList.length > 0 || isFailed.length > 0
      ? (
          <div class="flex items-center gap-x-1 text-xs text-[var(--text-3)]">
            <SvgIcon class={`cursor-pointer ${uploadSyncLoading.value ? 'animate-spin' : ''}`} name="ic_refresh" onClick={() => {
              refresh && refresh()
              void refreshSyncList(series_resource_id)
            }}
            />
            { isSyncList.length > 0 ? <span class="text-blue-500">{isSyncList.length}个任务同步中</span> : '' }
            { isFailed.length > 0 ? <span class="text-red-500">{isFailed.length}个任务失败</span> : '' }
            <Button class="btn btn-xs btn-link" onClick={() => {
              openDialog({
                title: '同步详情',
                body: () => showUploadProgress(list),
                customClass: '!w-[500px] !max-h-[70vh]',
              })
            }}
            >查看详情
            </Button>
          </div>
        )
      : null
  )
}

const showUploadProgress = (list: M.IDiskInfoUploadProgress[]) => {
  const notFinishList = list.filter(row => row.sync_status !== 3)
  const typeSet = new Set()
  list.forEach(row => typeSet.add(row.netdisk_type))
  const typeArr = Array.from(typeSet) as string[]
  const result: { [record: string]: M.IDiskInfoUploadProgress[] } = {}
  list.forEach(row => {
    if (!result[row.netdisk_type]) result[row.netdisk_type] = []
    result[row.netdisk_type].push(row)
  })
  const Table = CreateTableOld<M.IDiskInfoUploadProgress>()
  const columns: TableColumnOld<M.IDiskInfoUploadProgress>[] = [
    ['集数', 'serial_number', { class: 'w-[30px]' }],
    ['资源类型', (row: M.IDiskInfoUploadProgress) => {
      if (row.netdisk_type && diskUploadProgress[row.netdisk_type]) {
        return diskUploadProgress[row.netdisk_type]
      }
    }, { class: 'w-[55px]' }],
    ['同步状态', row => {
      return (
        <div class={`badge ${sync_status_colors[row.sync_status]} gap-2`}>
          { sync_status_map[row.sync_status] }
        </div>
      )
    }, { class: 'w-[65px]' }],
    ['报错信息', row => {
      return row.sync_status === 4 ? row.err_msg : ''
    }],
  ]
  return (
    <>
      {
        typeArr.map(type => {
          const _list = result[type]
          const _successList = _list.filter(row => row.sync_status === 3)
          return <div class="text-sm text-[var(--text-2)]">{diskUploadProgress[type]}: {_successList.length}/{_list.length}</div>
        })
      }
      <Table list={notFinishList} columns={columns} />
    </>
  )
}

const onUploadVideo = (series_resource_id: number, refresh: () => void) => {
  const promises = ref<Promise<unknown>[]>([])
  const tempFiles: File[] = []
  const hasUploadFiles: UploadImage[] = []
  const uploadType = ref<1 | 2>(1)

  const closeDialog = openDialog({
    title: '上传视频',
    mainClass: '!p-4 !pb-0',
    body: () => (
      <>
        <Input
          type="radio"
          class="tm-radio pb-4"
          options={[
            {
              value: 1,
              label: '含字幕',
            },
            {
              value: 2,
              label: '无字幕',
            },
          ]}
          modelValue={uploadType.value}
          onUpdate:modelValue={value => uploadType.value = value as 1 | 2}
        />
        <Uploader
          accept="mp4,mkv,avi,mov"
          isImage={false}
          ossKeyType="resource"
          multiple
          beforeUpload={async ({ files }) => {
            const responses = await checkVideos(files) as { fileName: string, validate: boolean }[]
            const unValidVideos = responses.filter(res => !res.validate)
            if (unValidVideos.length > 0) {
              showToast({
                message: `${unValidVideos.map(row => row.fileName).join(',')}, 视频清晰度没有达到1080或分辨率不正确`,
                duration: 5000,
                icon: 'fail',
              })
              return false
            }
            files.forEach(file => tempFiles.push(file))
          }}
          onUploadSuccess={file => {
            if (hasUploadFiles.some(f => f?.file?.name === file.file?.name)) return
            hasUploadFiles.push(file)

            const ps = new Promise(resolve => {
              const video = document.createElement('video')
              video.preload = 'metadata'
              video.src = file.url as string
              video.onloadedmetadata = () => {
                const width = video.videoWidth
                const height = video.videoHeight
                const resolution = `${width}x${height}`
                const videoInfo = {
                  temp_path: file.temp_path || '',
                  duration: Math.round(video.duration),
                  file_path: file.temp_path || '',
                  file_size: file.file?.size || 0,
                  serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
                  file_resolution: resolution,
                }
                resolve(videoInfo)
              }
            })
            promises.value.push(ps)
          }}
        >
          <x-uploader-wrapper class="flex h-[120px] w-full cursor-pointer flex-col items-center  justify-center overflow-hidden rounded-md border border-dashed">
            <div>上传视频, 支持MP4, MKV, AVI，MOV格式</div>
          </x-uploader-wrapper>
        </Uploader>
        <Button class="btn btn-primary btn-sm float-right mt-4" disabled={promises.value.length === 0 || tempFiles.length !== promises.value.length} onClick={async () => {
          try {
            const videoInfos = await Promise.all(promises.value) as IVideoInfo[]
            await apiUploadSaveVideo({
              data: videoInfos,
              series_resource_id,
              type: uploadType.value,
            })
            showSuccessToast('操作成功！')
            refresh && refresh()
            closeDialog()
          } catch (error: any) {
            showFailToast(error.response.data.message || '操作失败')
          }
        }}
        >保存
        </Button>
      </>
    ),
  })
}

const onPreviewVideo = (videoUrl: string, remark?: string) => {
  openDialog({
    title: () => (
      <div class="flex items-center space-x-2">
        <span>预览</span>
        {remark ? (
          <Tooltip placement="bottom-start" popWrapperClass="z-popover-in-dialog max-w-[400px]" popContent={() => (
            <pre class="whitespace-pre-wrap break-words">备注：{remark}</pre>
          )}>
            <div class="line-clamp-1 w-[400px] flex-1 text-sm text-[var(--text-2)]">
              备注：{remark}
            </div>
          </Tooltip>
        ) : null}
      </div>
    ),
    body: <video class="max-h-[550px] w-[350px]" controlslist="nodownload" src={videoUrl} controls />,
    customClass: '!w-[390px]',
  })
}

const onUploadAudio = (series_resource_id: number, refresh: () => void) => {
  const audio_type = ref< 1 | 2 | 3 | number>(1)
  const tempFiles: File[] = []
  const hasUploadFiles = ref<UploadImage[]>([])

  const closeDialog = openDialog({
    title: '上传音频',
    mainClass: '!p-4 !pb-0',
    body: () => (
      <>
        <div class="flex items-center gap-x-2 pb-4">
          {requiredLabel('音频类型:')}
          <select class="select select-bordered select-sm" value={audio_type.value} onChange={e => {
            const target = e.target as HTMLSelectElement
            const value = target.value
            audio_type.value = +value
          }}
          >
            <option value={1}>原始声音</option>
            <option value={2}>背景声音</option>
            <option value={3}>特效声音</option>
          </select>
        </div>
        <Uploader
          accept="mp4,mp3,wav"
          ossKeyType="resource"
          isImage={false}
          maxsize={1024 * 1024 * 1024 * 2}
          multiple
          beforeUpload={({ files }) => {
            files.forEach(file => tempFiles.push(file))
          }}
          onUploadSuccess={file => {
            if (hasUploadFiles.value.some(f => f?.file?.name === file.file?.name)) return
            hasUploadFiles.value.push(file)
          }}
        >
          <x-uploader-wrapper class="flex h-[120px] w-full cursor-pointer flex-col items-center justify-center  gap-y-2 overflow-hidden rounded-md border border-dashed">
            <p>上传音频</p>
            <p>上传文件需要确保音频文件名和视频对应,支持mp4、mp3、wav文件</p>
          </x-uploader-wrapper>
        </Uploader>
        <Button class="btn btn-primary btn-sm float-right mt-4" disabled={hasUploadFiles.value.length === 0 || tempFiles.length !== hasUploadFiles.value.length} onClick={async () => {
          try {
            const subtitleInfos = hasUploadFiles.value.map(file => {
              return {
                file_path: file.temp_path || '',
                serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
              }
            })
            await apiUploadSaveAudio({
              data: subtitleInfos,
              series_resource_id,
              audio_type: audio_type.value,
            })
            refresh && refresh()
            showSuccessToast('保存成功')
            closeDialog()
          } catch (error: any) {
            showFailToast(error.response.data.message || '操作失败')
          }
        }}
        >保存
        </Button>

      </>
    ),
  })
}

const onUploadCaption = (series_resource_id: number, refresh: () => void) => {
  const language_code = ref('zh-CN')
  const tempFiles: File[] = []
  const hasUploadFiles = ref<UploadImage[]>([])

  const closeDialog = openDialog({
    title: '上传字幕',
    mainClass: '!p-4 !pb-0',
    body: () => (
      <>
        <div class="flex items-center gap-x-2 pb-4">
          字幕语言:
          <select class="select select-bordered select-sm" value={language_code.value} onChange={e => {
            const target = e.target as HTMLSelectElement
            const value = target.value
            language_code.value = value
          }}
          >
            {
              langKey.map((n, index) => <option value={n}>{langValue[index]}</option>)
            }
            <option value="airole">角色标注</option>
          </select>
        </div>
        <Uploader
          accept="vtt,srt"
          ossKeyType="resource"
          isImage={false}
          maxsize={1024 * 1024 * 1}
          multiple
          beforeUpload={({ files }) => {
            files.forEach(file => tempFiles.push(file))
          }}
          // checkFile={checkFile}
          onUploadSuccess={file => {
            if (hasUploadFiles.value.some(f => f?.file?.name === file.file?.name)) return
            hasUploadFiles.value.push(file)
          }}
        >
          <x-uploader-wrapper class="flex h-[120px] w-full cursor-pointer flex-col items-center justify-center  gap-y-2 overflow-hidden rounded-md border border-dashed">
            <p>上传字幕, 支持VTT, SRT格式</p>
            <p>上传文件需要确保字幕文件名和视频对应</p>
            <p class="text-xs text-[var(--error-3)]">* 请先选择字幕语言，再上传字幕</p>
          </x-uploader-wrapper>
        </Uploader>
        <Button class="btn btn-primary btn-sm float-right mt-4" disabled={hasUploadFiles.value.length === 0 || tempFiles.length !== hasUploadFiles.value.length} onClick={async () => {
          try {
            const subtitleInfos = hasUploadFiles.value.map(file => {
              return {
                file_path: file.temp_path || '',
                serial_number: Number((file.file?.name || '').match(/\d+/g)?.[0] || 1),
              }
            })
            await apiUploadSaveSubtitle({
              data: subtitleInfos,
              series_resource_id,
              language_code: language_code.value,
            })
            refresh && refresh()
            showSuccessToast('保存成功')
            closeDialog()
          } catch (error: any) {
            showFailToast(error.response.data.message || '操作失败')
          }
        }}
        >保存
        </Button>

      </>
    ),
  })
}

const getVideoPlayerInfo = async (file: File) => {
  return new Promise(resolve => {
    const videoElement = document.createElement('video')
    videoElement.src = URL.createObjectURL(file)
    videoElement.addEventListener('loadedmetadata', function () {
      resolve({
        duration: videoElement.duration,
        width: videoElement.videoWidth,
        height: videoElement.videoHeight,
      })
    })
  })
}
/**
 *
 * @param list  补全后的
 * @param count 是资源完整集数
 * @param total 接口返回集数
 */
export const setMissingData = (list: M.IResourceDrama[], count: number, total: number) => {
  const _columns: IMissingCol = {
    origin_path: {
      missingNum: [],
      nums: [],
      moreNums: [],
    },
    pure_path: {
      missingNum: [],
      nums: [],
    },
  }
  langKeyForCol.map(lang => {
    _columns[`${lang}_episode_path`] = { missingNum: [], nums: [] }
    _columns[`${lang}_subtitle_path`] = { missingNum: [], nums: [] }
  })

  const keys = Object.keys(_columns)

  list.forEach(row => {
    keys.forEach(key => {
      // 判断是否是 统计字段
      if ((row[key] !== null || row[key] !== undefined) && !notFinished.includes(row[key])) {
        _columns[key].nums.push(row.serial_number)
      }
    })
  })

  keys.forEach(key => {
    // 多集数
    if (key === 'origin_path') {
      const fakeNumList = new Array(count).fill(0).map((_, i) => i + 1)
      _columns[key].moreNums = _columns[key]?.nums.filter(n => !fakeNumList.includes(n))
    }
    _columns[key].missingNum = findMissingNumberFromNumberArray(count, _columns[key]?.nums)
  })
  tipColumns.value = _columns
}

export const showColumnHeader = (k: string, title: string, isLang?: boolean, fn?: (arr: number[]) => void, onTranslateFn?: (langCode: string) => void) => {
  if (isLang) {
    // episodeSeriesInfoList.value
    const videoKey = `${k}_episode_path`
    const subtitleKey = `${k}_subtitle_path`

    const videoMissingNum = tipColumns.value[videoKey].missingNum
    const subtitleMissingNum = tipColumns.value[subtitleKey].missingNum

    return (
      <div class="flex items-center justify-center space-x-1">
        <span>{title}</span>
        <div class="flex flex-col items-center">
          {tipColumns.value[videoKey].nums.length !== 0 && videoMissingNum && videoMissingNum.length > 0
            ? (
                <div class="tooltip tooltip-bottom" data-tip={`视频缺少${videoMissingNum?.join(',')}集 ${fn ? ' 点击选中' : ''}`} onClick={() => fn && fn(videoMissingNum)}>
                  <SvgIcon class="size-3 cursor-pointer" name="ic_video" />
                </div>
              )
            : null}
          {tipColumns.value[subtitleKey].nums.length !== 0 && subtitleMissingNum && subtitleMissingNum.length > 0
            ? (
                <div class="tooltip tooltip-bottom" data-tip={`字幕缺少${subtitleMissingNum?.join(',')}集 ${fn ? ' 点击选中' : ''}`} onClick={() => fn && fn(subtitleMissingNum)}>
                  <SvgIcon class="size-3 cursor-pointer" name="ic_text" />
                </div>
              )
            : null}
        </div>
        {onTranslateFn ? (
          <div>
            <span class="badge badge-primary badge-xs cursor-pointer" onClick={() => {
              onTranslateFn && onTranslateFn(k)
            }}>翻译</span>
          </div>
        ) : null}
      </div>
    )
  } else {
    const missingNum = tipColumns.value[k].missingNum
    const moreNums = tipColumns.value[k].moreNums
    const moreTip = moreNums && moreNums.length > 0 ? `多出集${moreNums?.join(',')}集` : ''
    return (
      <div class="flex items-center justify-center space-x-1">
        <span>{title}</span>
        {(tipColumns.value[k].nums.length !== 0 && missingNum && missingNum.length > 0) || moreTip
          ? (
              <div class="tooltip tooltip-bottom" data-tip={`缺少${missingNum?.join(',')}集 ${moreTip} ${fn ? ' 点击选中' : ''}`} onClick={() => fn && fn(missingNum || [])}>
                <SvgIcon class="size-3  cursor-pointer" name="ic_video" />
              </div>
            )
          : null}
      </div>
    )
  }
}

function findMissingNumbers(num: number, arr: M.IResourceDrama[]) {
  const result = []
  for (let i = 1; i <= num; i++) {
    if (!arr.some(row => row.serial_number === i)) {
      result.push(i)
    }
  }
  return result
}

export function findMissingNumberFromNumberArray(num: number, arr: number[]) {
  const result = []
  for (let i = 1; i <= num; i++) {
    if (!arr.some(n => n === i)) {
      result.push(i)
    }
  }
  return result
}

// 补充缺少的row
function makeUpList(serialNumberArr: number[], o: M.IResourceDrama) {
  const keys = Object.keys(o)
  const emptyObj: { [record: string]: string | number } = {}
  keys.forEach(key => emptyObj[key] = '')
  return serialNumberArr.map(serialNumber => {
    return {
      ...emptyObj,
      serial_number: serialNumber,
    }
  })
}

const getTags = async (language_code: string, series_resource_id?: number) => {
  const rs = await apiGetLabelList({
    language_code,
    series_resource_id,
  })
  tags.value = rs.data?.list.map(row => {
    return {
      ...row,
      content: row.type ? row.type + ' - ' + row.content : row.content,
    }
  }) || []
}
// 上传商务资源
const onUploadBusinessExcel = (refresh: () => void) => {
  const uploading = ref(false)
  const inputRef = ref<HTMLInputElement>()
  const fileChange = async (e: Event) => {
    const files = (e.target as HTMLInputElement)?.files || []
    const fileList = Array.from(files)
    if (fileList && fileList.length > 0) {
      const file = fileList[0]
      const ext = file.name.split('.').pop()?.toLowerCase() || ''
      if (ext !== 'csv') {
        showFailToast('仅支持上传csv文件');
        (inputRef.value as HTMLInputElement).value = ''
        return
      }
      try {
        uploading.value = true
        await apiUploadBusinessExcel({ file })
        showSuccessToast('操作成功！')
        createDialog()
        refresh()
      } catch (error: any) {
        showFailToast(error.response.data.message || '操作失败！');
        (inputRef.value as HTMLInputElement).value = ''
      } finally {
        uploading.value = false
      }
    }
  }
  const createDialog = openDialog({
    title: '批量新增',
    mainClass: 'pb-0 px-5',
    body: () => (
      <div>
        <div>
          上传商务信息：
          <a class="link" target="_blank" href="https://rg975ojk5z.feishu.cn/wiki/Cw4fw3lYViNi9SkS79oc0ll5nuh?sheet=394927">下载模板</a>
        </div>
        <div class="mt-4">
          <input ref={inputRef} disabled={uploading.value} class="file-input file-input-xs file-input-primary w-full max-w-xs" type="file" multiple={false} accept="csv" onChange={fileChange} />
          <div class="mt-2 text-xs">{requiredLabel('仅支持上传.csv文件')}</div>
        </div>
      </div>
    ),
  })
}

const onUploadResourceExcel = (refresh: () => void) => {
  const uploading = ref(false)
  const inputRef = ref<HTMLInputElement>()
  const fileChange = async (e: Event) => {
    const files = (e.target as HTMLInputElement)?.files || []
    const fileList = Array.from(files)
    if (fileList && fileList.length > 0) {
      const file = fileList[0]
      const ext = file.name.split('.').pop()?.toLowerCase() || ''
      if (ext !== 'csv') {
        showFailToast('仅支持上传csv文件');
        (inputRef.value as HTMLInputElement).value = ''
        return
      }
      try {
        uploading.value = true
        await apiUploadExcel({ file })
        showSuccessToast('操作成功！')
        createDialog()
        refresh()
      } catch (error: any) {
        showFailToast(error.response.data.message || '操作失败！');
        (inputRef.value as HTMLInputElement).value = ''
      } finally {
        uploading.value = false
      }
    }
  }
  const createDialog = openDialog({
    title: '批量新增',
    mainClass: 'pb-0 px-5',
    body: () => (
      <div>
        <div>
          上传资源信息：
          <a class="link" target="_blank" href="https://rg975ojk5z.feishu.cn/wiki/HsoQwgN6UiehCakYBfKcjqHNnEk?sheet=6d0cbb">下载模板</a>
        </div>
        <div class="mt-4">
          <input ref={inputRef} disabled={uploading.value} class="file-input file-input-xs file-input-primary w-full max-w-xs" type="file" multiple={false} accept="csv" onChange={fileChange} />
          <div class="mt-2 text-xs">{requiredLabel('仅支持上传.csv文件')}</div>
        </div>
      </div>
    ),
  })
}

const getTranscodeVideos = async (id: number) => {
  const res = await apiGetExternalTransList({
    series_resource_id: id,
  })
  externalTransProgress.value = res.data?.resource_list || []
}

export const useResourceStore = () => {
  return {
    onUploadVideo,
    onDiskUpload,
    onUploadCaption,
    onPreviewVideo,
    getVideoPlayerInfo,
    getEpisodeSeriesInfoList,
    episodeSeriesInfoList,
    updateEpisodeSeriesInfoList,
    setMissingData,
    showColumnHeader,
    findMissingNumbers,
    makeUpList,
    getTags,
    tags,
    // 网盘进度相关
    uploadSyncList,
    refreshSyncList,
    showSyncSummary,
    uploadSyncLoading,
    onUploadAudio,
    onUploadResourceExcel,
    onUploadBusinessExcel,
    getTranscodeVideos,
    externalTransProgress,
  }
}
