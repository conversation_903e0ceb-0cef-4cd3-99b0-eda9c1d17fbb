import { ClassName, OptionsToProps } from '@skynet/shared'
import { HTMLAttributes, InputHTMLAttributes, VNode, VNodeChild } from 'vue'
import { CheckboxGroupOptions, CheckboxOptions } from '../ui'
import type { FormMultiSelectOptions } from './form-multi-select'

type X = OptionsToProps<CheckboxOptions>
export type SimpleValue = string | number | boolean
export type InputOption<D = SimpleValue> = { value: D, label: string, disabled?: boolean, tooltip?: VNodeChild | (() => VNodeChild) }
export type InputRender = (params: { onInput: (value: unknown) => void, item: FormItemObject, value: unknown, error: string | undefined }) => VNodeChild
export type FormItemObject = {
  label: VNodeChild | (() => VNodeChild)
  path: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  transform?: readonly [(raw: any, item: FormItemObject) => any, (display: any, item: FormItemObject) => any]
  class?: ClassName
  hint?: VNodeChild | (() => VNodeChild)
  errorVisible?: boolean
  input: (
    | { type: 'color', hasAlpha?: boolean }
    | { type: 'text' | 'number' | 'password' }
    | { type: 'money' }
    | { type: 'custom', render: InputRender }
    | { type: 'textarea', rows?: number }
    | { type: 'radio' | 'radio-group', options: InputOption[] }
    | ({ type: 'checkbox', label?: VNodeChild | (() => VNodeChild) } & OptionsToProps<CheckboxOptions>)
    | ({ type: 'checkbox-group' } & CheckboxGroupOptions['props'])
    | { type: 'select', autoInsertEmptyOption?: boolean, options: InputOption[] }
    | { type: 'datetime' | 'date', rawFormat?: string, displayFormat?: string }
    | { type: 'datetime2' | 'date2', rawFormat?: string, displayFormat?: string }
    | { type: 'image' }
    | {
      type: 'multi-select'
      options: InputOption[] | (() => VNode)
      search?: FormMultiSelectOptions['props']['search']
      popoverWrapperClass?: ClassName
      'onUpdate:keyword'?: (keyword: string) => void
      disabledItemClass?: ClassName
      itemClass?: ClassName
      onClear?: () => void
    }
  ) & {
    prefix?: VNodeChild | (() => VNodeChild)
    suffix?: VNodeChild | (() => VNodeChild)
    disabled?: boolean
    wrapperClass?: ClassName
    transform?: never
  } & InputHTMLAttributes & HTMLAttributes & { disabled?: boolean }
}
export type FormItemArray =
  | [FormItemObject['label'], FormItemObject['path'], FormItemObject['input'], Omit<FormItemObject, 'label' | 'path' | 'input'>]
  | [FormItemObject['label'], FormItemObject['path'], FormItemObject['input']]
export type FormItem = FormItemObject | FormItemArray | FormItemGroup | (() => VNode)
export type FormItemGroup = [string, ...FormItem[]] | FormItem[]
export type FormItems = Array<null | false | FormItem>
export type FormOptions<D extends object> = {
  props: {
    data: D
    /**
     * 数据是否准备就绪，reset 时会使用就绪的数据
     */
    dataReady?: boolean | number
    error?: ValidationError | null
    items: FormItems
    actions?: VNodeChild | (() => VNodeChild) | null
    class?: ClassName
    actionClass?: ClassName
    labelClass?: ClassName
    buttonWrapperClass?: ClassName
    hasAction?: boolean
    submitText?: string
    resetText?: string
    hideEmptyError?: boolean
  }
  emits: {
    change: (path: string, value: unknown) => void
    submit: (e: Event) => void
    reset: (e: Event, data: D) => void
  }
}
export type FormItemOptions = {
  props: {
    data: object
    item: FormItemObject | FormItemArray
    labelClass?: ClassName
    error?: ValidationError | null
    hideEmptyError?: boolean
  }
  emits: {
    change: (path: string, value: unknown) => void
  }
}

export type ValidationErrorItem = { message: string }
export type ValidationError = Record<string, ValidationErrorItem[]>
