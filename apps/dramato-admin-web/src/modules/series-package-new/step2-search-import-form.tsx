/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useSeriesPackageAddFormStore } from './use-series-package-add-form-store'
import { cloneDeep, set } from 'lodash-es'
import { Button, CreateTableOld, DateTime, openDialog, TableColumnOld } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import ShortDrama from '../short-drama/short-drama-page'
import { ref } from 'vue'
import { listingStatusList } from '../short-drama/short-drama-api'

type Step2SearchImportFormOptions = {
  props: {}
}
export const Step2SearchImportForm = createComponent<Step2SearchImportFormOptions>({
  props: {},
}, props => {
  const {
    formData,
    Form,
    curTab,
    twoStepError,
    appId,
    checkedEpisodeItem,
  } = useSeriesPackageAddFormStore()
  const Table = CreateTableOld<M.Drama>()

  const EpisodeColumns: TableColumnOld<M.Drama>[] = [
    ['剧集ID', 'series_key', { class: 'w-[120px]' }],
    ['剧名', 'title', { class: 'w-[320px]' }],
    ['上架状态', row => listingStatusList.find(item => item.code === row.listing_status)?.name, { class: 'w-[80px]' }],
    ['上架时间', row => <DateTime value={row?.listing_time ? row?.listing_time * 1000 : 0} />, { class: 'w-[150px]' }],
    ['集数', 'episodes_number', { class: 'w-[120px]' }],
    ['短剧类型', row => ['', '本土', '翻译'][+(row.resource_type || 0)], { class: 'w-[80px]' }],
    ['原起始付费集', 'start_paying_episodes', { class: 'w-[120px]' }],
    ['单集定价', 'episodes_price', { class: 'w-[120px]' }],
    ['生效端', row => ['', 'ios', 'android'][+(row?.app_platform || 0)], { class: 'w-[80px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-outline btn-xs" disabled={formData.value.needDisabled}
            onClick={() => {
              const key = curTab.value === 'ios_config' ? 'ios_list' : 'android_list'
              if (!formData.value.package_config) {
                formData.value.package_config = {}
              }
              if (!formData.value.package_config.package_type_3_config) {
                formData.value.package_config.package_type_3_config = {}
              }
              if (!formData.value.package_config.package_type_3_config[key]) {
                formData.value.package_config.package_type_3_config[key] = []
              }
              formData.value.package_config.package_type_3_config[key].splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[80px]' },
    ],
  ]

  const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[1300px] h-[80vh] overflow-hidden px-4'

  const showImportDramaDialog = () => {
    const key = curTab.value === 'ios_config' ? 'ios_list' : 'android_list'
    if (formData.value.needDisabled) {
      return
    }
    if (!formData.value.package_config) {
      formData.value.package_config = {}
    }
    if (!formData.value.package_config.package_type_3_config) {
      formData.value.package_config.package_type_3_config = {}
    }
    if (!formData.value.package_config.package_type_3_config[key]) {
      formData.value.package_config.package_type_3_config[key] = []
    }
    checkedEpisodeItem.value = [...formData.value.package_config.package_type_3_config[key]] as any
    const closeDialog = openDialog({
      title: '导入指定生效短剧',
      mainClass: dialogMainClass,
      body: () => (
        <x-import-recharge-level class="gxp-y-4 flex flex-1 flex-col overflow-hidden">
          <x-episode-list class="flex-1  overflow-y-auto">
            <ShortDrama
              hasNav={false}
              hasActions={false}
              appId={appId.value}
              checkedItems={checkedEpisodeItem.value}
              onAdd={item => {
                checkedEpisodeItem.value?.push({ ...item, drama_id: item.drama_id })
              }}
              onRemove={item => {
                checkedEpisodeItem.value = checkedEpisodeItem.value?.filter(i => i.drama_id !== item.drama_id)
              }}
            />
          </x-episode-list>
          <footer class="flex w-full justify-end gap-x-2 pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              const key = curTab.value === 'ios_config' ? 'ios_list' : 'android_list'
              if (!formData.value.package_config) {
                formData.value.package_config = {}
              }
              if (!formData.value.package_config.package_type_3_config) {
                formData.value.package_config.package_type_3_config = {}
              }
              if (!formData.value.package_config.package_type_3_config[key]) {
                formData.value.package_config.package_type_3_config[key] = []
              }
              const oleList = cloneDeep(formData.value.package_config.package_type_3_config[key])
              formData.value.package_config.package_type_3_config[key] = checkedEpisodeItem.value.map(i => ({
                drama_id: i.drama_id || 0,
                series_key: i.series_key || '',
                app_platform: i.app_platform, // 1 IOS 2 Android
                title: i.title || '',
                listing_status: i.listing_status || 1, // 上架状态： 1 未上架 2 待上架 3 已上架 4 待下架 -1 已下架
                listing_time: i.listing_time || 0,
                episodes_number: i.episodes_number || 0,
                start_paying_episodes: i.start_paying_episodes || 0,
                // new_start_paying_episodes: oleList.find(j => j.drama_id === i.drama_id)?.new_start_paying_episodes || 2,
                episodes_price: i.episodes_price || 0,
                resource_type: i.resource_type,
              })) as any
              closeDialog()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-1300px overflow-hidden',
      beforeClose: () => {
        checkedEpisodeItem.value = []
      },
    })
  }

  return () => (
    <Form
      class="flex flex-1 flex-col gap-y-3"
      hasAction={false}
      error={twoStepError.value}
      onChange={(path, value) => {
        set(formData.value || {}, path, value)
      }}
      items={[
        [requiredLabel('指定生效短剧'), `package_config.package_type_3_config.${curTab.value === 'ios_config' ? 'ios_list' : 'android_list'}`, {
          type: 'custom',
          render: ({ item, value }) => {
            return (
              <section class="border-1 rounded-lg border border-solid p-4">
                <x-table-actions class="flex items-center justify-between">
                  <div class="space-x-2">
                    <Button class="btn btn-primary btn-sm" disabled={formData.value.needDisabled} onClick={() => showImportDramaDialog()}>导入配置
                    </Button>
                  </div>
                </x-table-actions>
                <hr class="my-4" />
                <Table
                  list={formData.value.package_config.package_type_3_config[curTab.value === 'ios_config' ? 'ios_list' : 'android_list'] || []}
                  columns={EpisodeColumns}
                  class="tm-table-fix-last-column"
                />
              </section>
            )
          },
        }, { class: 'w-full' }],
      ]}
      data={formData.value}
    />
  )
})

export default Step2SearchImportForm
