import { createComponent } from '@skynet/shared'
import play from '../image/play.png'
import free from '../image/free.png'
type BannerLayoutOptions = {
  props: {
    title: string
    list: M.DramaItem[]
  }
}
export const BannerLayout = createComponent<BannerLayoutOptions>({
  props: {
    title: '',
    list: [],
  },
}, props => {
  const dramaItem = (drama: M.DramaItem) => {
    return (
      <div class="size-full relative">
        {drama.free && <img src={free} class="absolute right-1 top-1 w-40px h-auto object-cover shadow-sm" />}
        <img class="size-full object-cover rounded-md" src={drama.cover} />
      </div>
    )
  }
  return () => (
    props.list && (
      <x-banner class="w-full h-[380px] overflow-hidden flex flex-row items-center justify-center relative gap-x-2">
        <x-banner-last-item class="shrink-0 size-3/4 opacity-60">
          {dramaItem(props.list[props.list.length - 1])}
        </x-banner-last-item>
        <x-banner-first-item class="shrink-0 w-3/4 h-full relative">
          {dramaItem(props.list[0])}
          <x-play-button class="absolute bottom-4 left-1/2 -translate-x-1/2 w-[135px] h-[38px]">
            <img src={play} class="size-full" />
          </x-play-button>
        </x-banner-first-item>
        <x-banner-second-item class="shrink-0 size-3/4 opacity-60">
          {dramaItem(props.list[1])}
        </x-banner-second-item>
      </x-banner>
    )
  )
})
