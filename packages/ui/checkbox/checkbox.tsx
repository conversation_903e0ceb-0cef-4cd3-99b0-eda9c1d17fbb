import { createComponent, createStringId, fn, tryCall } from '@skynet/shared'
import { MergeClass } from '../ui'
import { VNodeChild } from 'vue'
export * from './checkbox-group.tsx'

export type CheckboxOptions = {
  props: {
    modelValue?: boolean
    disabled?: boolean
    label?: VNodeChild | (() => VNodeChild)
    suffix?: VNodeChild | (() => VNodeChild)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    trueValue?: true | any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    falseValue?: false | any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    valueTest?: null | ((value: any) => boolean)
    indeterminate?: boolean
  }
  emits: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    'update:modelValue': (value: any, e: Event) => void
  }
}

export const Checkbox = createComponent<CheckboxOptions>({
  props: {
    modelValue: false,
    disabled: false,
    label: '',
    suffix: null,
    trueValue: true,
    falseValue: false,
    valueTest: null,
    indeterminate: false,
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const id = createStringId('checkbox')
  const onChange = (e: Event) => {
    const target = e.target as HTMLInputElement
    const value = target.checked ? props.trueValue : props.falseValue
    emit('update:modelValue', value, e)
  }
  return () => {
    const isTrue = props.valueTest ? props.valueTest(props.modelValue) : props.modelValue
    return (
      <MergeClass baseClass="flex flex-row">
        <div class="flex items-center space-x-2 cursor-pointer p-1">
          <input id={id} type="checkbox" disabled={props.disabled} checked={isTrue}
            indeterminate={props.indeterminate}
            onChange={onChange}
          />
          {props.label && <label for={id} class="whitespace-nowrap">{tryCall(props.label)}</label>}
          {tryCall(props.suffix)}
        </div>
      </MergeClass>
    )
  }
})
