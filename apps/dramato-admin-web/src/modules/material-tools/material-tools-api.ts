/* eslint-disable @typescript-eslint/no-explicit-any */
import { get_k_sso_token } from 'src/lib/device-id'
import { httpClient } from 'src/lib/http-client'

// 批量拆剧
export const apiMultiSplit = (data: M.MultiSplitItem) =>
  httpClient.post<ApiResponse>('/open_platform_in/user/multi_split', data)

// 更新状态
export const apiUpdateRoleMark = (data: M.SaveItem) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('POST', 'https://ms-2scj6hpg-100039220333-sw.gw.ap-beijing.ti.tencentcs.com/ms-2scj6hpg/efficiency/tool/client/role/annotation', true)

    // 允许发送凭证（cookies 和 Authorization 头）
    xhr.withCredentials = true

    // 设置 Authorization 头
    xhr.setRequestHeader('Authorization', 'iXx6UjRBVXmccrs')

    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          resolve(JSON.parse(xhr.responseText))
        } else {
          reject(xhr.responseText)
        }
      }
    }

    xhr.send(JSON.stringify(data))
  })
}

// 获取角色列表
export const apiGetRoleList = (data: M.GetRoleListItem) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('GET', 'https://ms-2scj6hpg-100039220333-sw.gw.ap-beijing.ti.tencentcs.com/ms-2scj6hpg/efficiency/tool/client/role/face?resource=' + data.resource, true)

    // 允许发送凭证（cookies 和 Authorization 头）
    xhr.withCredentials = true

    // 设置 Authorization 头
    xhr.setRequestHeader('Content-Type', 'application/form-data')
    xhr.setRequestHeader('Authorization', 'iXx6UjRBVXmccrs')

    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          resolve(JSON.parse(xhr.responseText))
        } else {
          reject(xhr.responseText)
        }
      }
    }

    xhr.send()
  })
}

export const apiSeriesResourceList = (data: M.IResourceSeriesParams) => {
  const x = (value: {
    list: M.IResourceDrama[]
    list_v2: M.IResourceDrama[]
    title: string
    count: number
    total: number
    use_new: boolean
  }) => {
    if (value.use_new) {
      value.list = value.list_v2
      return value
    }
    return value
  }
  return httpClient.post<ApiResponse<{
    list: M.IResourceDrama[]
    list_v2: M.IResourceDrama[]
    title: string
    count: number
    total: number
    use_new: boolean
  }>>('/series_resource/episode_list', data, {
    transformResponseData: {
      data: [x],
    },
  })
}

// 获取拆剧列表
export const apiGetSplitActingList = (data: M.GetSplitActingListItem) =>
  httpClient.get<ApiResponse>('/efficiency/tool/client/split/acting', data)
