<template>
  <div>
    <div class="my-4 px-4">{{
      {
        1: '人工',
        2: '剧集无关',
        3: '剧集半相关',
        4: '剧集相关'
      }[+route.params.type]
    }} - pushId：{{ route.params.pushId }}</div>
    <div class="my-4 p-4 bg-white rounded-lg shadow">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="语言">
          <el-input v-model="searchForm.language" clearable />
        </el-form-item>
        <el-form-item label="资源 ID" v-if="+route.params.type === 4">
          <el-input v-model="searchForm.resource_id" type="number" clearable />
        </el-form-item>
        <el-form-item label="剧 ID" v-if="+route.params.type === 4">
          <el-input v-model="searchForm.series_key" clearable />
        </el-form-item>
        <el-form-item label="发送状态">
          <el-select style="width: 240px" v-model="searchForm.state" placeholder="请选择" clearable>
            <el-option label="待发送" :value="1"></el-option>
            <el-option label="发送中" :value="20"></el-option>
            <el-option label="已完成" :value="40"></el-option>
            <el-option label="发送失败" :value="60"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="统计周期" v-if="+route.params.type !== 2">
          <el-date-picker v-model="searchForm.batch" type="datetime" value-format="YYYY-MM-DD HH"
            placeholder="Pick a date" clearable />
        </el-form-item>
        <el-form-item label="实际开始时间" v-if="+route.params.type === 2">
          <el-date-picker v-model="searchForm.exec_start_date_time" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="Pick a date" clearable />
        </el-form-item>
        <el-form-item label="实际结束时间" v-if="+route.params.type === 2">
          <el-date-picker v-model="searchForm.exec_end_date_time" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="Pick a date" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">筛选</el-button>
          <!-- <el-button @click="onReset">重置</el-button> -->
        </el-form-item>
      </el-form>
    </div>

    <div class="push-task-table p-4 bg-white rounded-lg shadow mb-4">
      <div class="flex justify-end mb-4 border-b-2">
        <el-button @click="handleBatchDownload"><el-icon>
            <Download class="mr-1" />
          </el-icon>批量下载</el-button>
      </div>
      <el-table @selection-change="handleSelectionChange" :data="list" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="rec_type" label="Push类型" width="100">
          <template #default="scope">
            {{ listTypeMap[scope.row.rec_type] }}
          </template>
        </el-table-column>
        <el-table-column prop="push_task_id" label="PushId" width="80" />
        <el-table-column prop="gen_type" label="AI/人工" width="80">
          <template #default="scope">
            {{ scope.row.gen_type === 1 ? '人工' : 'AI' }}
          </template>
        </el-table-column>
        <el-table-column prop="language" label="语言" width="80" />
        <el-table-column prop="resource_id" label="资源ID" width="80" v-if="+route.params.type === 4" />
        <el-table-column prop="series_key" label="剧ID" width="80" v-if="+route.params.type === 4" />
        <el-table-column prop="style_config.title" label="Push标题" min-width="120" show-overflow-tooltip />
        <!-- <el-table-column prop="style_config.body" label="Push内容" min-width="120" show-overflow-tooltip /> -->
        <el-table-column prop="style_config.body" label="Push内容" min-width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.gen_type !== 1 && +route.params.type === 3 ? '/' : scope.row.style_config.body }}
          </template>
        </el-table-column>
        <el-table-column prop="gen_id" label="GEN ID" width="80" />
        <el-table-column prop="prompt_id" label="Prompt" width="80" />
        <el-table-column prop="state" label="发送状态" width="100">
          <template #default="scope">
            {{ {
              1: '待发送',
              20: '发送中',
              40: '已发送',
              60: '已停用'
            }[+scope.row.state] }}
          </template>
        </el-table-column>

        <el-table-column prop="start_ts_date_time" label="实际开始时间" width="160" v-if="+route.params.type === 2">
          <!-- <template #default="scope">
            {{ dayjs(scope.row.start_ts_date_time * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template> -->
        </el-table-column>

        <el-table-column prop="end_ts_date_time" label="实际结束时间" width="160" v-if="+route.params.type === 2">
          <!-- <template #default="scope">
            {{ dayjs(scope.row.end_ts_date_time * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template> -->
        </el-table-column>

        <el-table-column prop="batch" label="统计周期" width="100" v-if="+route.params.type !== 2" />

        <el-table-column prop="send_success_rate" label="成功率" width="100" />
        <el-table-column prop="send_cnt" label="发出条数" width="100" />
        <el-table-column prop="arrive_rate" label="到达率" width="100" />
        <el-table-column prop="show_rate" label="曝光率" width="100" />
        <el-table-column prop="click_rate_pv" label="点击率PV" width="100" />
        <el-table-column prop="click_rate_uv" label="拉活率UV" width="100" />
        <el-table-column prop="active_rate" label="拉活率" width="100" />
      </el-table>

      <div class="pagination-container my-2">
        <el-pagination v-model:current-page="searchForm.page_info.page_index"
          v-model:page-size="searchForm.page_info.page_size" :total="total" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { Download } from '@element-plus/icons-vue'
import Preview from './notification-preview.vue'
import { useNotification } from './use-notification';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';
import { useRoute } from 'vue-router';

const { getConfig, onSearchNotifications, list, loading, searchForm, total, onDownload } = useNotification('batch-list')
const route = useRoute()

onMounted(() => {
  console.log('mounted', route.params)
  getConfig();
  searchForm.value.list_type = +route.params.type
  searchForm.value.id = +route.params.id
  searchForm.value.is_batch_detail = true
  onSearchNotifications(true)
})

const multipleSelection = ref<M.PushNotificationNew[]>([])

const onSubmit = () => {
  console.log('submit!')
  console.log(searchForm)
  onSearchNotifications();
}

//列表类型，必传，1:人工push，2：剧集无关，3：剧集半相关，4：剧集相关
const listTypeMap: Record<number, string> = {
  3: '继续观看',
  16: '高付费剧',
  17: '限免剧',
  18: '折扣剧',
  23: '签到提醒',
  19: '订阅到期次日',
  20: '订阅到期5天',
  21: '免费部分完播',
  22: '充值用户',
  24: '预约新剧',
  26: '相关剧'
}

const handleSelectionChange = (val: M.PushNotificationNew[]) => {
  multipleSelection.value = val
}

const handleBatchDownload = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要下载的数据')
    return
  }
  onDownload(multipleSelection.value.map(item => item.id), 2, +route.params.type)
  console.log('批量下载', multipleSelection.value)
}

// 处理页码改变
const handleCurrentChange = (val: number): void => {
  searchForm.value.page_info.page_index = val
  onSearchNotifications()
}

// 处理每页条数改变
const handleSizeChange = (val: number): void => {
  searchForm.value.page_info.page_size = val
  onSearchNotifications()
}
</script>

<style scoped></style>