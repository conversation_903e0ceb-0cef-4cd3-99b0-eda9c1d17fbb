import { httpClient } from 'src/lib/http-client'
import { transformRequestData } from '../custom-users-config/custom-users-config'

export const apiListStrategyGroupAds = (params: Api.AdStrategyGroup.Request.List) => {
  return httpClient.post<Api.AdStrategyGroup.Response.List>('/ads/strategy-group/list', params)
}

// save_type  1 未上架，2 草稿
export const apiSaveStrategyGroupAds = (params: Partial<M.StrategyGroup> & { save_type: 1 | 2 }) => {
  if (params.id === '') {
    delete params.id
  }
  if (typeof params.custom_series_ids === 'string') {
    if (params.custom_series_ids === '') {
      params.custom_series_ids = []
    } else {
      params.custom_series_ids = params.custom_series_ids.split(/[\s,，]+/)
        .filter(Boolean)
        .map(Number)
        .filter(num => !isNaN(num))
    }
  }
  return httpClient.post<ApiResponse<M.StrategyGroup>>('/ads/strategy-group/save', params)
}

export const apiGetStrategyGroupAdsDetails = (params: { id: string }) => {
  return httpClient.get<ApiResponse<M.StrategyGroup>>('/ads/strategy-group/detail', params, {
    transformResponseData: {
      // 不展示 0
      'data.last_lock_episode': [value => value < 1 ? undefined : value],
    },
  })
}

export const apiDeleteStrategyGroup = (params: { ids: number[] }) => {
  return httpClient.post<Api.StrategyGroup.Response.List>('/ads/strategy-group/delete', params)
}

/** operation 1 上架 2 下架 3 冻结 4 解除冻结  */
export const apiUpdateStrategyGroupAdsState = (params: { id: string, operation: number }) => {
  return httpClient.post<unknown>('/ads/strategy-group/status/update', params)
}

export const apiCheckStrategyGroup = (params: { platform: string, series_keys: string[], strategy_id: number }) => {
  return httpClient.post<ApiResponse<{
    series_keys: string[]
  }>>('/ads/strategy-group/check', params)
}
