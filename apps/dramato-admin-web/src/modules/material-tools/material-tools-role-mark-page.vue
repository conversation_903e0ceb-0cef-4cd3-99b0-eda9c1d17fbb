<template>
  <div class="pt-4">
    <div class="container-flex">
      <!-- 表格部分 -->
      <div class="table-container">
        <div class="header-flex">
          <h2>角色list</h2>
          <el-button type="primary" @click="handleAddRole">新建</el-button>
        </div>
        <div>
          <el-table @selection-change="handleSelectionChange" :data="list" max-height="80vh" border v-loading="loading">
            <el-table-column prop="auth_status drag-item" label="姓名" width="150">
              <template #default="scope">
                <span>{{ scope.row.name }}</span>
                <el-button type="text" size="small"
                  @click="handleEditField(scope.row, scope.$index, 'name')">编辑</el-button>
                <el-popconfirm title="确认删除此角色吗?" @confirm="handleDeleteRole(scope.$index)" confirm-button-text="确认"
                  cancel-button-text="取消">
                  <template #reference>
                    <el-button type="danger" link size="small">删除角色</el-button>
                  </template>
                </el-popconfirm>

              </template>
            </el-table-column>
            <el-table-column prop="auth_status" label="称呼" width="100">
              <template #default="scope">
                <span>{{ scope.row.nickname }}</span>
                <el-button type="text" size="small"
                  @click="handleEditField(scope.row, scope.$index, 'nickname')">编辑</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="auth_status" label="人物描述" width="200">
              <template #default="scope">
                <span>{{ scope.row.introduction }}</span>
                <el-button type="text" size="small"
                  @click="handleEditField(scope.row, scope.$index, 'introduction')">编辑</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="auth_status" label="人物关系" width="300">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.relation_list" :key="`relation-${index}-${item.name}`">
                  <span>{{ item.name }}:</span>
                  <span>{{ item.relation }}</span>
                </div>
                <el-button type="text" size="small"
                  @click="handleEditField(scope.row, scope.$index, 'relation_list')">编辑</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="auth_status" label="出现的集数" width="100">
              <template #default="scope">
                <span class="cursor-pointer ep-item" @click="jumpToVideo(item, 0)" v-for="(item, index) in scope.row.episode_list"
                  :key="`episode-${index}-${item}`">
                  {{ item }}<template v-if="index < scope.row.episode_list.length - 1">,</template>
                </span>
                <el-button type="text" size="small"
                  @click="handleEditField(scope.row, scope.$index, 'episode_list')">编辑</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="auth_status" label="人脸" width="200">
              <template #default="scope">
                <div class="episode-container">
                  <div class="drag-in-box" :data-row-index="scope.$index">
                    <div class="face-group faces-flex">
                      <div v-for="(face, index) in scope.row.faces" :key="`face-${index}-${face.value}`"
                        class="StackedListItem--isDraggable flex-wrap flex gap-1">
                        <!-- <div v-for="(faceItem, faceIndex) in face" :key="`face-${index}-${faceIndex}`"> -->
                          <el-popover :width="300" placement="top"
                            popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 20px;">
                            <template #reference>
                              <div class="flex items-center justify-between relative">
                                <img @click="handleGoVideo(face)" :src="face" alt=""
                                  class="w-[40px] h-[40px] rounded-md cursor-pointer">
                                  <el-icon style="position: absolute;right: -0.7rem;top: -0.7rem;" class="delete-icon text-[16px] bg-[rgba(255,255,255,0.3)] rounded-full px-0 py-0" @click="handleDeleteFace(scope.$index, face, index)">
                                    <Close />
                                  </el-icon>
                              </div>
                            </template>
                            <template #default>
                              <div>
                                <img :src="face" alt="" class="w-[400px] rounded-md">
                              </div>
                            </template>
                          </el-popover>
                        <!-- </div> -->
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 人脸列表容器 -->
      <div class="faces-container ">
        <h2>人脸list</h2>
        <div class="drag-from-box overflow-y-auto mt-2" style="max-height: 80vh;">
          <div v-for="(group, groupIndex) in faceGroups.faces" :key="`group-${groupIndex}`" class="face-group my-1"
            draggable="true">
            <!-- <div class="group-header">{{ group.info.name || '-' }}</div> -->
            <div class="group-content faces-flex">
              <!-- class="StackedListItem--isDraggable face-item-flex" -->
              <div class="StackedListItem--isDraggable"  v-for="(face, faceIndex) in group.list" :key="`face-${groupIndex}-${faceIndex}`"
                :data-group-index="groupIndex" :data-face-index="faceIndex">
                <el-popover :width="300" placement="top"
                  popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 20px;">
                  <template #reference>
                    <div class="relative">
                      <img :data-group-index="groupIndex" :data-face-index="faceIndex" @click="handleGoVideo(face)" :src="face" alt=""
                        class="w-[40px] h-[40px] rounded-md cursor-pointer">
                      <div class="w-full h-full absolute top-0 left-0 cursor-no-drop" v-if="checkFaceIsInList(face)" style="background: rgb(20 255 51 / 42%);"></div>
                    </div>
                  </template>
                  <template #default>
                    <div>
                      <img :src="face" alt="" class="w-[400px] rounded-md">
                    </div>
                  </template>
                </el-popover>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-[350px]" style="width: 350px;">
        <h2>播放器</h2>
        <el-form-item label="播放集数">
          <el-select @change="handleChangeVideo" v-model="currentPlayVideoNum" placeholder="请选择">
            <el-option v-for="(item, key) in videoList.length" :key="item" :label="'第' + item + '集'" :value="key"></el-option>
          </el-select>
        </el-form-item>
        <div class="w-[350px]">
          <video ref="player" v-if="showVideo" :src="videoUrl" controls
            class='w-[350px] min-h-[550px] max-h-[550px] bg-black '></video>
        </div>
        <div class="flex gap-3 mt-3 justify-center">
          <el-button type="primary" @click="videoPre">上一集</el-button>
          <el-button type="primary" @click="videoNext">下一集</el-button>
        </div>
        <div v-if="false" class="flex gap-3 " style="padding-top: 24px;">
          <div>
            <div v-if="!headUrl"
              class="w-[120px] h-[120px] rounded-md flex items-center justify-center border border-dashed border-1 border-gray-300 bg-[#fff]">
              截图头像</div>
            <img v-else class="w-[120px] h-[120px] rounded-md" src="" alt="">
          </div>
          <div>
            <div>角色姓名：</div>
            <el-select v-model="formData.default_plan" placeholder="请选择">
              <el-option v-for="item in defaultPlanOptions" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
            <div class="flex gap-3 mt-3">
              <el-button type="primary" @click="handleCaptureFrame">截图</el-button>
              <el-button type="primary" @click="handleUploadHead">保存</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex justify-end">
      <el-button type="warning" @click="handleBack">返回</el-button>
      <el-button type="danger" @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleSave(1)">保存</el-button>
      <el-button type="primary" @click="handleSave(2)">保存并继续拆剧</el-button>
    </div>

  </div>

  <el-dialog v-model="dialogVisible" title="Prompt方案" width="70%">
    <div>
      <el-form :model="formData" label-width="120px">
        <el-form-item label="默认方案">
          <el-select v-model="formData.default_plan" placeholder="请选择">
            <el-option v-for="item in defaultPlanOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
          <el-button type="primary" @click="handleDefaultPlan">确定</el-button>
        </el-form-item>
        <div>
          <el-form-item label="Prompt管理">
            <el-button type="primary" @click="handlePromptAdd">添加</el-button>
          </el-form-item>
        </div>
        <div>
          内容
        </div>
      </el-form>
      <div class="flex justify-end">
        <el-button type="primary" @click="handleSubmit">通过</el-button>
        <el-button type="danger" @click="handleReject">不通过</el-button>
      </div>
    </div>
  </el-dialog>
  <el-dialog v-model="dialogEditVisible" title="编辑" width="40%">
    <div class="overflow-y-auto" style="max-height: 50vh;" v-if="currentEditType === 'relation_list'">
      <div class="flex gap-2 mb-2 items-center" v-for="(item, index) in currentEditValue"
        :key="`relation-${index}-${item.name}`">
        <el-select style="width: 200px;" v-model="item.name" placeholder="请选择">
          <el-option v-for="item in list" :disabled="checkDisabled(item.name)" :key="item.name" :label="item.name"
            :value="item.name"></el-option>
        </el-select>
        <el-input v-model="item.relation" placeholder="请输入关系" />
        <el-button size="small" type="danger" @click="handleDeleteRelation(index)">删除</el-button>
      </div>
    </div>
    <div v-else>
      <el-input rows="5" type="textarea" v-model="currentEditValue" placeholder="" />
    </div>
    <div class="flex justify-end mt-4" v-if="currentEditType === 'relation_list'">
      <el-button type="primary" @click="handleEditAddEpisode">添加</el-button>
    </div>
    <div class="flex justify-end mt-4">
      <el-button type="danger" @click="handleCancelEdit">取消</el-button>
      <el-button type="primary" @click="handleSaveEdit">保存</el-button>
    </div>
  </el-dialog>
  <el-dialog v-model="cropperDialogVisible" title="裁剪" width="380px">
    <div>
      <AvatarCropper :url="currentVideoImgUrl" @hide="cropperDialogVisible = false" @success="handleCropperSuccess" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { Plus, Delete, Download, InfoFilled, Close } from '@element-plus/icons-vue'
import { useMaterialTools } from './use-material-tools';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute, useRouter } from 'vue-router'
import { Uploader } from '../common/uploader/uploader'
import { httpClient } from 'src/lib/http-client'
import { AvatarCropper } from '../episode-breakdown/vocal-generate-components/avatar-cropper'
import { apiUpdateRoleMark, apiGetRoleList, apiSeriesResourceList } from './material-tools-api'
import { EpisodeMaterialPlayer } from '../episode-clip-material/episode-material-player'

const {
  progressOptions,
  roleMarkOptions,
  materialStatusOptions,
  loading,
  searchForm,
  total,
  onReset,
  multiSplit
} = useMaterialTools()


const route = useRoute()
const router = useRouter()

const currentEditValue = ref('')
const currentEditType = ref('')
const currentEditRowData = ref({})
const currentEditIndex = ref(0)
const dialogEditVisible = ref(false)

const list = ref<M.RoleListItem[]>([])
const dialogVisible = ref(false)
const formData = ref({
  default_plan: 1,
})
const videoUrl = ref('')
const showVideo = ref(true)
const headUrl = ref('')
const currentVideoImgUrl = ref('')
const cropperDialogVisible = ref(false)
const player = ref<HTMLVideoElement | null>(null)

const videoList = ref<M.IResourceDrama[]>([])

const faceGroups2 = ref([
  {
    name: '组1',
    faces: [
      { label: '人脸1', value: 1 },
      { label: '人脸2', value: 2 },
    ]
  },
  {
    name: '组2',
    faces: [
      { label: '人脸3', value: 3 },
      { label: '人脸4', value: 4 },
    ]
  }
]);
const faceGroups = ref({})
const btnIsDisabled = ref(false)
const faceItemList = ref([])
const currentPlayVideoNum = ref(0)

const defaultPlanOptions = [
  { label: '方案1', value: 1 },
  { label: '方案2', value: 2 },
  { label: '方案3', value: 3 },
]
const splitActingStatus = ref(1)
const splitStatusOptions = [
  { label: '未开始', value: 0 },
  { label: '进行中', value: 1 },
  { label: '已完成', value: 2 },
  { label: '已失败', value: 3 },
]
const markStatusOptions = [
  { label: '未标注', value: 0 },
  { label: '已标注', value: 1 }
]
const handleSplitMark = (row: M.SplitActingItem) => {
  console.log('handleSplitMark', row)
}

const handleDefaultPlan = () => {
  console.log('handleDefaultPlan')
}

const handleCaptureFrame = () => {
  if (!player.value) {
    return
  }
  // // 获取画布元素
  const canvas: HTMLCanvasElement = document.createElement('canvas')
  canvas.width = 350
  canvas.height = 550
  // 获取画布上下文，这里使用2D上下文
  const ctx = canvas.getContext('2d')

  if (!ctx) {
    return
  }

  console.log(3)

  // 将视频当前播放到的帧绘制到画布上
  ctx?.drawImage(player.value, 0, 0, 350, 550)

  // 将画布内容转换为图片数据
  const imageData = canvas.toDataURL('image/png')

  void fetch(imageData).then(res => res.blob()).then(blob => {
    console.log('blob', blob)

    const file = blobToFile(blob)
    console.log('file', file)

    void httpClient.post<ApiResponse<{ cover?: string, image?: string, show_cover?: string, path?: string }>>('/vocal/role_upload', {
      file,
    }, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(rs => {
      headUrl.value = rs?.data?.cover || rs?.data?.image || rs?.data?.path || ''
      showCropper(headUrl.value || '')
    },
    )
  })
}

const blobToFile = (theBlob: Blob): File => {
  return new File([theBlob], 'xxx.png', {
    lastModified: new Date() as any,
    // lastModifiedDate: new Date() as any,
    type: theBlob.type,
  })
}
const handleCropperSuccess = (d) => {
  const file = blobToFile(d)
  console.log('file', file)

  void httpClient.post<ApiResponse<{ cover?: string, image?: string, show_cover?: string, path?: string }>>('/vocal/role_upload', {
    file,
  }, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }).then(rs => {

    void httpClient.post<ApiResponse<{ cover?: string, image?: string, show_cover?: string, path?: string }>>('/vocal/role_upload', {
      file,
    }, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(rs => {
      headUrl.value = rs?.data?.cover || rs?.data?.image || rs?.data?.path || ''
      showCropper(headUrl.value || '')
    },
    )
  },
  )
}
const videoPre = () => {
  console.log('videoPre', currentPlayVideoNum.value)
  if(currentPlayVideoNum.value <= 0) return
  currentPlayVideoNum.value -= 1
  handleChangeVideo(currentPlayVideoNum.value)
}
const videoNext = () => {
  console.log('videoNext', currentPlayVideoNum.value, videoList.value.length)
  if(currentPlayVideoNum.value >= videoList.value.length - 1) return
  currentPlayVideoNum.value += 1
  handleChangeVideo(currentPlayVideoNum.value)
}
const handleChangeVideo = (value: number) => {
  showVideo.value = false
  videoUrl.value = `https://bj-dramato-algo-server-prod.oss-cn-beijing.aliyuncs.com/video_clips/data/${route.params.id}/videos/ch_${route.params.id}_${value+1}.mp4`
  setTimeout(() => {
    showVideo.value = true
  }, 100)
}
const handleGoVideo = (faceItem: string) => {
  let videoArr = faceItem.split('face_images/')[1].split('/face_')[1].split('_')
  let videoNum = videoArr[1].split('s')[1]
  let second = videoArr[2].split('.')
  second.pop()
  jumpToVideo(videoNum, second.join('.'))
}
const jumpToVideo = (videoNum: any, second: any) => {
  showVideo.value = false

  currentPlayVideoNum.value = Number(videoNum) - 1
  videoUrl.value = `https://bj-dramato-algo-server-prod.oss-cn-beijing.aliyuncs.com/video_clips/data/${route.params.id}/videos/ch_${route.params.id}_${videoNum}.mp4`
  setTimeout(() => {
    showVideo.value = true
    setTimeout(() => {
      if (player.value) {
        player.value.currentTime = second
      }
    }, 100)
  }, 100)
}

const showCropper = (url: string) => {
  currentVideoImgUrl.value = url
  cropperDialogVisible.value = true
}

const handleUploadHead = () => {
  console.log('handleUploadHead')
}

const handlePreviewRole = (row: M.SplitActingItem) => {
  console.log('handlePreviewRole', row)
}

const promptPlanPop = () => {
  dialogVisible.value = true
}

const handlePromptAdd = () => {
  console.log('handlePromptAdd')
}

const handleSplitActing = (row: M.SplitActingItem) => {
  console.log('handleSplitActing', row)
}

const handlePreviewLanguage = (row: M.SplitActingItem) => {
  console.log('handlePreviewLanguage', row)
}

// 处理操作方法
const handlePreview = (row: M.SplitActingItem) => {
  formData.value = JSON.parse(JSON.stringify(row))
  dialogVisible.value = true
}

const handleEditField = (row: M.SplitActingItem, index: number, type: string) => {
  currentEditIndex.value = index
  currentEditType.value = type
  currentEditValue.value = JSON.parse(JSON.stringify(row[type]))
  currentEditRowData.value = row
  dialogEditVisible.value = true
}

const handleEditAddEpisode = () => {
  currentEditValue.value.push({ name: '', relation: '' })
  console.log('handleEditAddEpisode')
}

const handleDeleteRelation = (index: number) => {
  currentEditValue.value.splice(index, 1)
  console.log('handleDeleteRelation', index)
}

const handleDeleteRole = (index: number) => {
  list.value.splice(index, 1)
  console.log('handleDeleteRole', index)
}

const handleCancelEdit = () => {
  dialogEditVisible.value = false
}

const handleSaveEdit = () => {
  console.log('handleSaveEdit', currentEditIndex.value, currentEditType.value, currentEditValue.value)
  if (currentEditType.value === 'episode_list') {
    list.value[currentEditIndex.value][currentEditType.value] = currentEditValue.value.split(',')
  } else {
    list.value[currentEditIndex.value][currentEditType.value] = currentEditValue.value
  }
  dialogEditVisible.value = false
}

const checkDisabled = (name: string) => {
  return currentEditValue.value.some(item => item.name === name || currentEditRowData.value.name === name)
}

const checkFaceIsInList = (face: any) => {
  if(!face) return false
  return list.value.some(item => item.faces?.some(f => f === face))
}

const handleEditName = (row: M.SplitActingItem) => {
  console.log('handleEditName', row)
}

const handleEditDesc = (row: M.SplitActingItem) => {
  console.log('handleEditDesc', row)
}

const handleEditRelation = (row: M.SplitActingItem) => {
  console.log('handleEditRelation', row)
}

const handleAddRole = () => {
  console.log('handleAddRole')
  list.value.push({
    name: '',
    nickname: '',
    relation_list: [],
    introduction: '',
    episode_list: [],
    faces: [],
  })

  initDrag()
}

const handleBack = () => {
  console.log('handleBack')
  router.back()
}
const onSearchRoleList = () => {
  console.log('onSearchRoleList')
  apiGetRoleList({ resource: Number(route.params.id) }).then((res: any) => {
    console.log(res)
    list.value = JSON.parse(res.data.role_json)
    faceGroups.value = JSON.parse(res.data.face_role_json)
  })
  // list.value = roleList
}
const handleReset = () => {
  console.log('handleReset')
  if (!route.params.id) {
    return
  }
  if (btnIsDisabled.value) {
    return
  }
  btnIsDisabled.value = true
  apiUpdateRoleMark({
    series_resource_id: Number(route.params.id),
    status: 3,
  }).then((res: any) => {
    if (res.code === 200) {
      ElMessage.success('重置成功')
      onSearchRoleList()
    } else {
      ElMessage.error(res.msg)
    }
  }).finally(() => {
    btnIsDisabled.value = false
  })
}

const handleSaveAndContinue = () => {
  console.log('handleSaveAndContinue')
  if (!route.params.id) {
    return
  }
  if (btnIsDisabled.value) {
    return
  }
  if (checkIsSave()) {
    ElMessage.error('请完善所有信息')
    return
  }
  btnIsDisabled.value = true
  apiUpdateRoleMark({
    series_resource_id: Number(route.params.id),
    status: 2,
  }).then((res: any) => {
    if (res.code === 200) {
      ElMessage.success('保存成功')
      onSearchRoleList()
    } else {
      ElMessage.error(res.msg)
    }
  }).finally(() => {
    btnIsDisabled.value = false
  })
}

const checkIsSave = () => {
  let hasNotFull = true
  let fullNum = 0
  if (list.value.length < 5) {
    ElMessage.error('请至少添加5个角色')
    return false
  }
  for (let i in list.value) {
    if (!(!list.value[i].name || !list.value[i].introduction || !list.value[i].faces || list.value[i].faces.length === 0)) {
      fullNum += 1
    }
  }
  console.log('fullNum', fullNum)
  if(fullNum >= 5){
    hasNotFull = false
  }else{
    hasNotFull = true
  }
  return hasNotFull
}

const handleSave = (type: number) => {
  console.log('handleSave', type)
  if (!route.params.id) {
    return
  }
  if (btnIsDisabled.value) {
    return
  }
  btnIsDisabled.value = true
  let faceDataList = JSON.parse(JSON.stringify(list.value))
  let newFaceGroups = {faces: [...faceGroups.value.faces]}
  for (let i in faceDataList) {

    if (faceDataList[i].faces) {
      let inObj = {
        info: {
          name: faceDataList[i].name,
          intro: faceDataList[i].introduction,
        },
        list: faceDataList[i].faces
      }
      let hasFind = false
      let fineIndex = 0
      for(let j in newFaceGroups.faces){
        if(newFaceGroups.faces[j].info.name === inObj.info.name){
          hasFind = true
          fineIndex = j
          break
        }
      }
      if(!hasFind){
        newFaceGroups.faces.push(inObj)
      }else{
        newFaceGroups.faces[fineIndex].list = inObj.list
      }
    }
  }

  let upData = {
    series_resource_id: Number(route.params.id),
    role_modify: JSON.stringify(list.value),
    face_role_modify: JSON.stringify(newFaceGroups),
    status: 1,
  }
  // upData.role_modify = JSON.stringify([])
  // upData.face_role_modify = JSON.stringify([])
  console.log('upData', upData)
  apiUpdateRoleMark(upData).then((res: any) => {
    if (res.code === 200) {
      if (type === 1) {
        ElMessage.success('保存成功')
        btnIsDisabled.value = false
        onSearchRoleList()
      } else {

        if (checkIsSave()) {
          ElMessage.error('请完善所有信息')
          return
        }
        apiUpdateRoleMark({
          series_resource_id: Number(route.params.id),
          status: 2,
        }).then((res: any) => {
          if (res.code === 200) {
            ElMessage.success('保存成功')
            onSearchRoleList()
          } else {
            ElMessage.error(res.msg)
          }
        }).finally(() => {
          btnIsDisabled.value = false
        })
      }
    } else {
      ElMessage.error(res.msg)
    }
  }).finally(() => {
    btnIsDisabled.value = false
  })
}

const handleSubmit = () => {
  ElMessageBox.confirm('确定通过吗？', '提示', {
    confirmButtonText: '通过',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    console.log('handleSubmit')
  })
}

const handleReject = () => {
  ElMessageBox.prompt('请输入不通过原因', '提示', {
    confirmButtonText: '不通过',
    cancelButtonText: '取消',
    type: 'warning',
  }).then((res: any) => {
    console.log('res', res)
  })
}


const container1 = ref(null);
const container2 = ref(null);

// 测试数据
const items1 = ref([
  { id: 1, text: '项目 1' },
  { id: 2, text: '项目 2' },
  { id: 3, text: '项目 3' },
]);

const items2 = ref([
  { id: 4, text: '项目 4' },
  { id: 5, text: '项目 5' },
  { id: 6, text: '项目 6' },
]);

const Classes = {
  draggable: 'StackedListItem--isDraggable',
  capacity: 'draggable-container-parent--capacity',
};

const getVideoList = async () => {
  const id = route.params.id
  if (!id) {
    return false
  }
  try {
    const res = await apiSeriesResourceList({
      page_index: 1,
      page_size: 300,
      series_resource_id: Number(id),
    })
    console.log('res', res)
    videoList.value = res?.data?.list_v2 || [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]
  } catch (error) {
    console.log('err')
    videoList.value = [null,null,null,null,null,null,null,null,null,null,null,null,null,null,null]
  }
  
}

const initDrag = () => {
  setTimeout(() => {
    try {
      const fromContainer = document.querySelector('.drag-from-box');
      const dropContainers = document.querySelectorAll('.drag-in-box');

      if (!fromContainer || !dropContainers.length) {
        console.error('Containers not found, retrying...');
        setTimeout(initDrag, 1000);
        return;
      }

      // 为源容器中的组和项目添加拖拽事件
      const setupDraggable = (element: Element) => {
        if (element.hasAttribute('drag-initialized')) {
          return; // 如果已经初始化过，则跳过
        }
        
        element.setAttribute('drag-initialized', 'true');
        element.setAttribute('draggable', 'true');

        element.addEventListener('dragstart', (e) => {
          e.stopPropagation();
          console.log('Drag start');

          // 获取组索引
          let groupIndex;
          if (e.target.classList.contains('face-group')) {
            groupIndex = Array.from(e.target.parentElement.children)
              .filter(el => el.classList.contains('face-group'))
              .indexOf(e.target);
          } else {
            groupIndex = e.target.getAttribute('data-group-index');
          }
          const faceIndex = e.target.getAttribute('data-face-index');
          const isGroup = e.target.classList.contains('face-group');

          console.log('Dragging:', { groupIndex, faceIndex, isGroup });

          e.target.classList.add('dragging');
          e.dataTransfer.setData('text/plain', JSON.stringify({
            groupIndex,
            faceIndex,
            isGroup
          }));
        });

        element.addEventListener('dragend', (e) => {
          e.stopPropagation();
          console.log('Drag end');
          e.target.classList.remove('dragging');
        });
      };

      // 设置组和子项的拖拽
      const groups = fromContainer.querySelectorAll('.face-group');
      const items = fromContainer.querySelectorAll('.StackedListItem--isDraggable');
      
      groups.forEach(setupDraggable);
      items.forEach(setupDraggable);

      // 为目标容器添加放置事件
      dropContainers.forEach(container => {
        if (container.hasAttribute('drop-initialized')) {
          return; // 如果已经初始化过，则跳过
        }
        
        container.setAttribute('drop-initialized', 'true');
        
        container.addEventListener('dragover', (e) => {
          e.preventDefault();
          container.classList.add('drag-over');
        });

        container.addEventListener('dragleave', (e) => {
          container.classList.remove('drag-over');
        });

        container.addEventListener('drop', async (e) => {
          e.preventDefault();
          container.classList.remove('drag-over');

          try {
            const data = JSON.parse(e.dataTransfer.getData('text/plain'));
            const rowIndex = parseInt(container.getAttribute('data-row-index'));

            console.log('Drop data:', data);
            console.log('Row index:', rowIndex);

            if (!isNaN(rowIndex) && rowIndex >= 0) {
              // 确保数据存在且有效
              if (!list.value[rowIndex]) {
                console.error('Invalid row index:', rowIndex);
                return;
              }

              // 初始化 faces 数组（如果不存在）
              if (!list.value[rowIndex].faces) {
                list.value[rowIndex].faces = [];
              }

              if (data.isGroup === true) {
                console.log('Dropping group:', data.groupIndex);
                // 拖拽整个组
                const group = faceGroups.value.faces[data.groupIndex];
                let newArr = []
                for(let i in group.list){
                  if(list.value[rowIndex].faces.some(f => f === group.list[i])){
                    continue
                  }
                  newArr.push(group.list[i])
                }
                if (group && Array.isArray(group.list)) {
                  list.value[rowIndex].faces = list.value[rowIndex].faces.concat(newArr);
                }
              } else {
                console.log('Dropping single face');
                // 拖拽单个项目
                const group = faceGroups.value.faces[data.groupIndex];
                if (group && Array.isArray(group.list)) {
                  const face = group.list[data.faceIndex];
                  if (face && !list.value[rowIndex].faces.some(f => f === face)) {
                    list.value[rowIndex].faces.push(face);
                  }
                }
              }

              // 强制更新视图
              list.value = [...list.value];
              console.log('list.value', list.value)

              // 触发更新事件
              emit('facesUpdated', {
                rowIndex,
                faces: list.value[rowIndex].faces
              });
            }
          } catch (error) {
            console.error('Error processing drop:', error);
          }
        });
      });

    } catch (error) {
      console.error('Drag initialization error:', error);
    }
  }, 1500);
};

// 监听动态添加的元素
const observeNewItems = () => {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length) {
        mutation.addedNodes.forEach((node) => {
          if (node.classList && node.classList.contains('StackedListItem--isDraggable')) {
            node.setAttribute('draggable', 'true');
            node.addEventListener('dragstart', (e) => {
              e.target.classList.add('dragging');
              e.dataTransfer.setData('text/plain', e.target.textContent);
            });
            node.addEventListener('dragend', (e) => {
              e.target.classList.remove('dragging');
            });
          }
        });
      }
    });
  });

  const fromContainer = document.querySelector('.drag-from-box');
  if (fromContainer) {
    observer.observe(fromContainer, { childList: true, subtree: true });
  }
};

const initVideo = () => {
  showVideo.value = false
  videoUrl.value = `https://bj-dramato-algo-server-prod.oss-cn-beijing.aliyuncs.com/video_clips/data/${route.params.id}/videos/ch_${route.params.id}_${1}.mp4`
  setTimeout(() => {
    showVideo.value = true
  }, 100)
}

onMounted(() => {
  onSearchRoleList()
  void getVideoList()
  initVideo()
  setTimeout(() => {
    initDrag();
    observeNewItems();
    console.log('initDrag')
  }, 3000);
});

const multipleSelection = ref<M.SplitActingItem[]>([])

const onSubmit = () => {
  console.log('submit!')
  console.log(searchForm)
  onSearchRoleList();
}



const handleSelectionChange = (val: M.SplitActingItem[]) => {
  multipleSelection.value = val
}

// 处理页码改变
const handleCurrentChange = (val: number): void => {
  searchForm.value.page_index = val
  onSearchRoleList()
}

// 处理每页条数改变
const handleSizeChange = (val: number): void => {
  searchForm.value.page_size = val
  onSearchRoleList()
}

// 暴露方法供外部使用
const emit = defineEmits(['facesUpdated']);

// 添加删除方法
const handleDeleteFace = (rowIndex: number, face: any, index: number) => {
  if (!list.value[rowIndex].faces) return;

  // 找到要删除的索引
  const deleteIndex = index

  if (deleteIndex > -1) {
    // 从数组中删除
    list.value[rowIndex].faces.splice(deleteIndex, 1);

    // 强制更新视图
    list.value = [...list.value];

    // 触发更新事件
    emit('facesUpdated', {
      rowIndex,
      faces: list.value[rowIndex].faces
    });

    console.log(`Deleted face from row ${rowIndex}:`, face);
  }
};
</script>

<style scoped>
/* 容器布局 */
.container-flex {
  display: flex;
  gap: 20px;
  padding: 16px;
}

.faces-container {
  min-width: 300px;
}

.header-flex {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 16px;
}

/* 人脸组样式 */
.face-group {
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  cursor: move;
  min-height: 50px;
  display: flex;

}

.group-header {
  padding: 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  user-select: none;
}

.group-content {
  padding: 8px;
}

/* Flex 布局的人脸列表 */
.faces-flex {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px;
}

.face-item-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: move;
  user-select: none;
  width: 50px;
}

.face-label {
  flex: 1;
  margin-right: 8px;
}

/* 拖拽容器样式 */
.drag-in-box {
  min-height: 50px;
  padding: 8px;
  margin: 5px 0;
  background: #f5f5f5;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.drag-from-box {
  background: #f0f0f0;
  min-height: 100px;
  padding: 12px;
  border-radius: 4px;
}

/* 拖拽状态样式 */
.drag-over {
  background-color: rgba(0, 255, 0, 0.1) !important;
  border: 2px dashed #4CAF50 !important;
}

.dragging {
  opacity: 0.5;
}

.face-group.dragging {
  opacity: 0.5;
  border: 2px dashed #4CAF50;
}

/* 删除图标样式 */
.delete-icon {
  cursor: pointer;
  color: #909399;
  font-size: 24px;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.3s;
}

.delete-icon:hover {
  color: #F56C6C;
  background-color: rgba(245, 108, 108, 0.1);
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .face-item-flex {
    flex: 0 1 100%;
    /* 小屏幕时每行一个项目 */
  }
}

.StackedListItem--isDraggable {
  touch-action: none;
}
.ep-item:hover{
  color: #409EFF;
}
</style>