import { createComponent, fn, JsonSimpleValue, mc, SlotFn } from '@skynet/shared'
import { Fn } from '@vueuse/core'
import { SvgIcon, usePopover } from '../ui'
import dayjs from 'dayjs'
import { ref, VNodeChild } from 'vue'
import { Icon } from '../icon/icon'
export type InputProps = {
  type?: 'text' | 'textarea' | 'password' | 'number' | 'date' | 'checkbox' | 'radio' | 'time' | 'datetime'
  modelValue?: JsonSimpleValue
  class?: string // wrapper 的 class
  inputClass?: string // input 的 class
  rows?: number | null
  max?: number | null
  min?: number | null
  step?: number | null
  maxlength?: number | string | null// 同原生 maxlength 属性
  showWordLimit?: boolean // 是否显示统计字数, 只在 type 为 'text' 或 'textarea' 的时候生效
  error?: string
  disabled?: boolean
  clearable?: boolean
  placeholder?: string
  format?: string
  searchable?: boolean
  results?: (() => VNodeChild) | null // 搜索结果，是一个组件render出来的内容
  options?: { label: string, value: JsonSimpleValue }[]
  labels?: string[]
  limit?: number
  precision?: number
  popoverWrapperClass?: string
}
type InputOptions = {
  props: InputProps
  emits: {
    'update:modelValue': (value: InputProps['modelValue']) => void
    blur: Fn
    focus: Fn
    'remove:label': (index: number) => void
    keydown: (e: KeyboardEvent) => void
    click: Fn
  }
  slots: {
    suffix?: SlotFn
    prefix?: SlotFn
  }
}

export const inputDefaultProps: Required<InputProps> = {
  type: 'text',
  modelValue: '',
  class: '',
  inputClass: '',
  rows: null,
  max: null,
  min: null,
  step: null,
  maxlength: null, // 同原生 maxlength 属性
  showWordLimit: false, // 是否显示统计字数, 只在 type 为 'text' 或 'textarea' 的时候生效
  error: '',
  disabled: false,
  clearable: false,
  placeholder: '',
  format: 'YYYY-MM-DD',
  searchable: false,
  results: null,
  options: [],
  labels: [],
  limit: 1,
  precision: 0,
  popoverWrapperClass: '',
}
export const Input = createComponent<InputOptions>({
  props: inputDefaultProps,
  emits: {
    blur: fn,
    focus: fn,
    keydown: fn,
    'update:modelValue': fn,
    'remove:label': fn,
    click: fn,
  },
}, (props, { slots, emit }) => {
  const input = ref<HTMLElement | null>(null)
  const wrapper = ref<HTMLElement | null>(null)
  const withFormat = (value: string) => {
    if (props.format) {
      return dayjs(value).format(props.format)
    }
    return value
  }

  const handleBlur = () => {
    emit('blur')
  }

  const handleFocus = () => {
    emit('focus')
  }

  const handleKeydown = (e: KeyboardEvent) => {
    if (props.type === 'number' && props.precision === 0) {
      if (e.key === '+' || e.key === '-' || e.key === '.') {
        e.preventDefault()
      }
    }
    emit('keydown', e)
  }

  usePopover({
    triggerElement: input,
    offsetElement: wrapper,
    content: props.results,
    placement: 'bottom-start',
    class: 'overflow-visible',
    offset: 5,
    arrowVisible: false,
    triggerType: 'click',
    wrapperClass: props.popoverWrapperClass,
  })

  return () => (
    <div class={mc('relative flex gap-1', props.class)} ref={wrapper}>
      {
        props.clearable && props.modelValue !== '' && (
          <SvgIcon
            name="ic_close_fill"
            class="cursor-pointer w-5 h-5 absolute right-4 top-1/2 -translate-y-1/2"
            onClick={(_e: Event) => {
              emit('update:modelValue', '')
            }}
          />
        )
      }
      {
        props.showWordLimit && props.maxlength && (
          <div class="dramato-mobile-textarea-word-limit absolute right-3 bottom-3 text-xs">
            <div>
              {props.modelValue ? (props.modelValue as string).length : 0}
              /
              {props.maxlength}
            </div>
          </div>
        )
      }
      {
        slots.prefix && (
          <div class="absolute left-2 top-1/2 -translate-y-1/2">
            {slots.prefix()}
          </div>
        )
      }
      {
        props.labels.length > 0 && (
          <div class="flex items-center gap-2 shrink-0 max-w-[80%] h-full overflow-hidden">
            {
              props.limit > 0 && (
                <>
                  {props.labels.slice(0, props.limit).map((label, index) => (
                    <div class="rounded-md bg-gray-100 px-3 text-sm text-gray-900 relative max-w-1/3 truncate">
                      {label}
                      <Icon
                        class="absolute right-0 top-0 cursor-pointer"
                        name="ic:round-close"
                        onClick={() => {
                          emit('remove:label', index)
                        }}
                      />
                    </div>
                  ))}
                  {
                    props.labels.length - props.limit > 0
                    && <div class="rounded-md bg-gray-100 px-3 text-sm text-gray-900 relative shrink-0">+{props.labels.length - props.limit}...</div>
                  }
                </>
              )
            }
          </div>
        )
      }
      {
        props.type === 'textarea' && (
          <textarea
            class={mc('dramato-textarea', props.inputClass, props.showWordLimit && 'dramato-textarea-with-word-limit')}
            value={props.modelValue as string}
            rows={props.rows ?? undefined}
            maxlength={props.maxlength ?? undefined}
            placeholder={props.placeholder}
            disabled={props.disabled}
            onKeydown={handleKeydown}
            onBlur={handleBlur}
            onFocus={handleFocus}
            onInput={(e: Event) => emit('update:modelValue', (e.target as HTMLInputElement).value)}
          />
        )
      }

      {
        props.type === 'checkbox' && (
          <input
            class="tm-checkbox"
            type="checkbox"
            value={props.modelValue as string}
            disabled={props.disabled}
            // @ts-expect-error never mind
            onChange={(e: { target: { checked: JsonSimpleValue } }) => {
              emit('update:modelValue', e.target?.checked)
            }}
          />
        )
      }
      {
        (props.type === 'date' || props.type === 'time') && (
          <input
            class={mc('tm-input h-9', props.inputClass)}
            type={props.type}
            value={dayjs(props.modelValue as string).format('YYYY-MM-DD')}
            placeholder={props.placeholder}
            disabled={props.disabled}
            onKeydown={handleKeydown}
            onBlur={handleBlur}
            onFocus={handleFocus}
            onInput={(e: Event) => emit('update:modelValue', withFormat((e.target as HTMLInputElement).value))}
          />
        )
      }
      {
        (props.type === 'datetime') && (
          <input
            class={mc('tm-input', props.inputClass)}
            type="datetime-local"
            step={1}
            value={dayjs(props.modelValue as string).format('YYYY-MM-DD HH:mm:ss')}
            placeholder={props.placeholder}
            disabled={props.disabled}
            onKeydown={handleKeydown}
            onBlur={handleBlur}
            onFocus={handleFocus}
            onInput={(e: Event) => emit('update:modelValue', withFormat((e.target as HTMLInputElement).value))}
          />
        )
      }
      {
        props.type === 'number' && (
          <input
            class={mc('tm-input', props.inputClass)}
            type="number"
            value={parseFloat(props.modelValue as string).toFixed(props.precision ?? 0)}
            min={props.min ?? undefined}
            max={props.max ?? undefined}
            step={props.step ?? undefined}
            maxlength={props.maxlength ?? undefined}
            placeholder={props.placeholder}
            disabled={props.disabled}
            onKeydown={handleKeydown}
            onBlur={handleBlur}
            onFocus={handleFocus}
            onInput={(e: Event) => {
              const value = (e.target as HTMLInputElement).value
              if (value) {
                emit('update:modelValue', parseFloat(value))
              } else {
                emit('update:modelValue', undefined)
              }
            }}
          />
        )
      }
      {
        props.type === 'text' && (
          <input
            class={mc('dramato-input', props.inputClass)}
            type="text"
            ref={input}
            value={props.modelValue as string}
            maxlength={props.maxlength ?? undefined}
            placeholder={props.placeholder}
            disabled={props.disabled}
            onKeydown={handleKeydown}
            onBlur={handleBlur}
            onFocus={handleFocus}
            onInput={(e: Event) => emit('update:modelValue', (e.target as HTMLInputElement).value)}
          />
        )
      }
      {
        props.type === 'radio' && (
          <div class="space-x-4">
            {
              props.options.map(item => (
                <label class={props.disabled ? 'cursor-not-allowed' : 'cursor-pointer'}>
                  <input
                    class="tm-radio"
                    type="radio"
                    v-model={props.modelValue}
                    value={item.value as string}
                    disabled={props.disabled}
                    onInput={() => emit('update:modelValue', item.value)}
                  />
                  {item.label && (
                    <span
                      class="ml-1"
                      onClick={() => {
                        if (props.disabled) return
                        emit('update:modelValue', item.value)
                      }}
                    >
                      {item.label}
                    </span>
                  )}
                </label>
              ),
              )
            }
          </div>
        )
      }
      {
        slots.suffix && (
          <div class="absolute right-3 top-1/2 -translate-y-1/2">
            {slots.suffix()}
          </div>
        )
      }
    </div>
  )
})
