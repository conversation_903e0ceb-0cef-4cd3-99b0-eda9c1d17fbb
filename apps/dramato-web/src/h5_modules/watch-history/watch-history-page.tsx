import { createComponent, getQuery } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { Back } from 'src/h5_modules/common/back/back'
import EmptyPage from 'src/h5_modules/common/empty-page/empty-page.vue'
import { onMounted } from 'vue'
import { InfinityScroll } from '../common/infinity-scroll/infinity-scroll'
import { T } from '../my-list/my-list-i18n'
import DramaCard from './component/drama-card'
import { useWatchHistoryStore } from './use-watch-history-store'
type WatchHistoryPageOptions = {
  props: {}
}
export const WatchHistoryPage = createComponent<WatchHistoryPageOptions>({
  props: {},
}, props => {
  const {
    mainRef,
    getDramaFeeds,
    dramaFeeds,
    loadingDramaFeeds,
    hasMoreDramaFeeds,
    dramaFeedsNext,
    isFirstLoading,
  } = useWatchHistoryStore()

  onMounted(() => {
    dramaFeedsNext.value = 'offset=0'
    hasMoreDramaFeeds.value = true
    void getDramaFeeds(true)
  })

  return () => (
    <MergeClass tag="x-search-and-tabs-layout" baseClass="max-w-150 mx-auto h-[100vh] flex flex-col overflow-hidden relative bg-[#0B080B] text-[#fff]">
      <Back backTo={getQuery('return_to', '/my-list')} titleLeft={true} isWhite class="z-100 flex h-[53px] w-full items-center justify-start bg-[#0B080B] pb-3 pt-2" title={T.myList.watchHistory()} />
      <main class="flex-1 overflow-y-auto px-3 pb-3" ref={mainRef}>
        {
          isFirstLoading.value ? (
            <x-history-list class="flex flex-col gap-y-2">
              <x-history-list-content class="flex w-full flex-col gap-2 overflow-hidden">
                { [1, 2, 3, 4, 5, 6, 7, 8].map((drama, index) => (
                  <x-drama-card class="flex h-auto w-full items-center gap-x-3 overflow-hidden rounded-lg">
                    <div class="w-22.5 min-w-22.5 min-h-30 h-30 skeleton-animation relative block overflow-hidden rounded-lg" />
                    <x-drama-desc-wrap class="flex flex-1 flex-col items-start justify-start gap-y-1 overflow-hidden p-2 pl-0">
                      <x-drama-card-title class="text-4 skeleton-animation block h-5 w-full truncate break-words rounded-sm  leading-5 text-[#fdfbfc]" />
                      <x-drama-card-title class="text-3 skeleton-animation block h-3 w-24 truncate break-words rounded-sm  leading-5 text-[#797b7d]" />
                    </x-drama-desc-wrap>
                  </x-drama-card>
                ))}
              </x-history-list-content>
            </x-history-list>
          ) : (!dramaFeeds?.value || dramaFeeds?.value.length <= 0) ? (
            <x-empty class="mt-48%  relative flex size-full flex-col">
              <EmptyPage image-size="160" description={T.myList.noData()} />
              {/* <div class="flex flex-1 flex-col items-center justify-center gap-y-5">
                <img src={noData} class="size-40" />
                <div class="text-4 text-center text-[#a1a2a3]">{T.myList.noData()}</div>
              </div> */}
            </x-empty>
          ) : (
            <InfinityScroll
              hasMore={hasMoreDramaFeeds.value}
              next={dramaFeedsNext.value}
              loading={loadingDramaFeeds.value}
              onLoad={() => getDramaFeeds(false)}
            >
              <x-history-list class="flex flex-col gap-y-2">
                <x-history-list-content class="flex w-full flex-col gap-2 overflow-hidden">
                  { dramaFeeds.value?.map((drama, index) => <DramaCard key={`RecommendationDramaCard-${drama.key}-${index}`} drama={drama} />)}
                </x-history-list-content>
              </x-history-list>
            </InfinityScroll>
          )
        }
      </main>
    </MergeClass>
  )
})

export default WatchHistoryPage
