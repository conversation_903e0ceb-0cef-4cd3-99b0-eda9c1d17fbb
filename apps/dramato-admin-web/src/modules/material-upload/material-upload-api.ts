
import { httpClient } from 'src/lib/http-client'

export const apiGetMaterialUploadList = (data: M.QueryMaterialUploadListParams) => {
  return httpClient.post<ApiResponse<{ total: number, list: M.MaterialUploadItem[] }>>('material_v1/search', data)
}

export const apiUploadMaterial = (data: M.UploadMaterialParams) => {
  return httpClient.post<ApiResponse<{
    material_id: number
  }>>('material_v1/upload', data)
}

export const apiUploadFile = (data: { data: M.UploadFileParams[] }) => {
  return httpClient.post<ApiResponse>('material_v1/file_upload', data)
}

export const apiDeleteMaterial = (data: { material_id: number }) => {
  return httpClient.post<ApiResponse>('material_v1/del', data)
}

export const apiGetRoleList = (data: { role_id: number }) => {
  return httpClient.post<ApiResponse<{ list: { Username: string }[] }>>('material_v1/user_role_list', data)
}

export const apiQueryByKeywords = (data: {
  app_id: number
  language_version?: string
  search_keyword?: string
}) => {
  return httpClient.post<ApiResponse<{
    total: number
    list: { title: string, series_key: string}[]
  }>>('/drama/query-by-keyword', data)
}