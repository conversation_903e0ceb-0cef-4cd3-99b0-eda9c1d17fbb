import { CreateF<PERSON>, CreateTableOld, showFailToast } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetUserAssetQuery } from '../user-asset-manage/user-asset-manage-api'
export const useUserComDis = () => {
  return {
    Form,
    FormOne,
    FormTwo,
    params,
    search,
    loading,
    userConDisForm,
    compensationGrant,
  }
}

const Form = CreateForm<M.UserAssetManage.Params>()
const FormOne = CreateForm<M.UserCompensationGrant.Params>()
const FormTwo = CreateForm<M.UserAssetManage.Response>()
const params = ref<M.UserAssetManage.Params>({ old_user_id: undefined, is_single_user_query: 1 })
const userConDisForm = ref<M.UserAssetManage.Response>({})
const compensationGrant = ref<M.UserCompensationGrant.Params>({ remark: '' })
const loading = ref<boolean>(false)

const search = async () => {
  if (params.value.old_user_id) {
    loading.value = true
    userConDisForm.value = {}
    const res = await apiGetUserAssetQuery({ ...params.value })
      .finally(() => {
        loading.value = false
      }).catch(() => {
        showFailToast('网络出错～')
      })
    userConDisForm.value = res?.data || {}
  } else {
    showFailToast('请先输入用户ID哦～')
  }
}
