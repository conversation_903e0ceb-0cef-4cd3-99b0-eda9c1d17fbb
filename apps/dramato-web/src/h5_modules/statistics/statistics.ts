import { h5Track } from 'src/lib/h5-track'
import { ref, watch } from 'vue'
import { createUuid, tryParseJson } from '@skynet/shared'

const sessionId = ref(createUuid())
const playbackId = ref(createUuid())
const reported358 = ref(tryParseJson(localStorage.getItem('reported358'), { 3: false, 5: false, 8: false }))
/**
 * 播放过的剧集
 */
const episodesPlayed = ref<Record<string, number>>(tryParseJson(localStorage.getItem('playedEpisodes'), {}))
watch(() => episodesPlayed.value, newVal => {
  localStorage.setItem('playedEpisodes', JSON.stringify(newVal))
}, { deep: true })
/**
 * 播放过一半以上的剧集（不考虑快进）
 */
const episodesPlayedMost = ref<Record<string, { series_id: string }>>(tryParseJson(localStorage.getItem('playedEpisodesMost'), {}))
watch(() => episodesPlayedMost.value, newVal => {
  localStorage.setItem('playedEpisodesMost', JSON.stringify(newVal))
}, { deep: true })

// 监听 reported358 变化，保存到 localStorage
watch(() => reported358.value, newVal => {
  localStorage.setItem('reported358', JSON.stringify(newVal))
}, { deep: true })

function markEpisodeAsPlayed(episodeId: string) {
  console.log('markEpisodeAsPlayed', episodeId)
  const n = typeof episodesPlayed.value[episodeId] === 'number' ? (episodesPlayed.value[episodeId] || 0) : 0
  episodesPlayed.value[episodeId] = n + 1
}

const milestones = [
  { threshold: 8, event: 'count_8' },
  { threshold: 5, event: 'count_5' },
  { threshold: 3, event: 'count_3' },
] as const

/**
 * 目前只在注册之后的 24 小时内调用 markEpisodeAsPlayedMost
 * @param episodeId
 * @param series_id
 * @returns
 */
function markEpisodeAsPlayedMost(episodeId: string, series_id: string) {
  if (episodesPlayedMost.value[episodeId]) return
  episodesPlayedMost.value[episodeId] = { series_id }
  markEpisodeAsPlayed(episodeId)
  const keys = Object.keys(episodesPlayedMost.value)
  const length = keys.length
  const lastKey = keys[length - 1]
  console.log('观看超过 50% 的集数', length)

  for (const { threshold, event } of milestones) {
    if (length >= threshold && !reported358.value[threshold]) {
      reported358.value[threshold] = true
      console.log('上报事件', `video_play_${event}`)
      h5Track('video', 'play', event, {
        series_id: episodesPlayedMost.value[lastKey].series_id,
      })
      break
    }
  }
}

function refreshSessionId() {
  sessionId.value = createUuid()
}
function refreshPlaybackId() {
  playbackId.value = createUuid()
}

function getPlayedCount(): number {
  return Object.values(episodesPlayed.value).reduce((sum, count) => sum + count, 0)
}

function clearStorage() {
  localStorage.removeItem('playedEpisodes')
  localStorage.removeItem('playedEpisodesMost')
  localStorage.removeItem('reported358')
}

export function useStatistics() {
  return {
    episodesPlayed,
    episodesPlayedMost,
    markEpisodeAsPlayed,
    markEpisodeAsPlayedMost,
    sessionId,
    playbackId,
    refreshSessionId,
    refreshPlaybackId,
    getPlayedCount,
    clearStorage,
  }
}
