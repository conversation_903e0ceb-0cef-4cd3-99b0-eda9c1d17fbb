import { createComponent } from '@skynet/shared'
import { Tooltip, DateTime } from '@skynet/ui'
import { watch, ref } from 'vue'
import { useImagePreviewStore } from '@skynet/ui/image/use-preview-store'
type ShowStatusOptions = {
  props: {
    data: M.IResourceAuditProcessItem
    recycleStatus?: number
  }
}
export const ShowStatus = createComponent<ShowStatusOptions>({
  props: {
    data: {
      page_type: 0,
      status: -1,
      reject_reason: '',
      auditor: '',
      updated: 0,
      reject_from_page: 0,
      reject_image_list: [],
    },
    recycleStatus: 0,
  },
}, props => {
  const renderStatus = ref()
  const { showImagePreviewDialog } = useImagePreviewStore()
  watch(() => props.data, () => {
    renderStatus.value = getRenderStatus()
  }, {
    deep: true,
  })

  const getRenderStatus = () => {
    const status = props.data.status
    const options = [
      {
        value: 0,
        label: '待审核',
      },
      {
        value: 1,
        label: '审核驳回',
      },
      {
        value: 2,
        label: '审核通过',
      },
    ]
    const label = options.find(item => item.value === status)?.label || '无'
    const color = {
      0: 'badge-primary',
      1: 'text-red-500',
      2: 'text-green-500',
    }[status || 0] || ''
    if ([4, 5].includes(props.recycleStatus)) {
      return (
        <div class="line-clamp-1 flex items-center gap-x-4 overflow-hidden pb-4 text-sm">
          <div class="badge  badge-outline text-red-500">{ props.recycleStatus === 4 ? '已暂缓' : '已退剧' }</div>
        </div>
      )
    } else if (status === 1) {
      return (
        <div class="line-clamp-1 flex items-center gap-x-4 overflow-hidden pb-4 text-sm">
          <div class="badge  badge-outline text-red-500">审核驳回</div>
          {props.data.auditor ? <span>操作人：{props.data.auditor} <DateTime value={props.data.updated * 1000} /></span> : null}
          { props.data.reject_reason
            ? (
                <Tooltip popContent={() => props.data.reject_reason}>
                  <span>{props.data.reject_reason}</span>
                </Tooltip>
              )
            : null }
          {(props.data.reject_image_list || [])?.length > 0 ? (
            <img class="size-[60px] cursor-pointer object-contain" src={props.data.reject_image_list?.[0]} alt="" onClick={() => {
              showImagePreviewDialog({
                imageList: props.data.reject_image_list?.map(imageUrl => ({
                  src: imageUrl,
                  width: 2000,
                })) || [],
                canEscClose: true,
              })
            }} />
          ) : null}
        </div>
      )
    } else {
      return <div class={`badge badge-outline mb-4 ${color}`}>{label}</div>
    }
  }

  return () => renderStatus.value
})

export default ShowStatus
