#!/bin/sh

REPO=https://git.tianmai.cn/fe/open-platform.git
JENKINS_SUFFIX="open-platform"
MODE=$1

function info() {
  echo -e "\033[1;32m🍺 $1\033[0m"
}
function error() {
  echo -e "\033[1;31m🚨 $1\033[0m" >&2
}
function warn() {
  echo -e "\033[1;33m🚧 $1\033[0m"
}
function clean() {
  rm -rf $TEMP
}

# 如果 MODE 为 prod，则 JENKINS_PREFIX 为 prod；否则为 test
if [ "$MODE" == 'prod' ]; then
  JENKINS_PREFIX="prod-frontend/job/prod-"
  BRANCH=main
else
  JENKINS_PREFIX="test-frontend/job/test-"
  BRANCH=test
fi

CURRENT_DIR=$(pwd)
PROJECT_DIR=$(git rev-parse --show-toplevel)
RELATIVE_PATH=${CURRENT_DIR#$PROJECT_DIR/}
SUB_PROJECT_NAME=$(echo $RELATIVE_PATH | awk -F'/' '{print $NF}')
CURRENT_REMOTE=$(git remote get-url origin)
TEMP="/tmp/$SUB_PROJECT_NAME-${MODE}"
JENKINS_JOB="${JENKINS_PREFIX}${JENKINS_SUFFIX}"

