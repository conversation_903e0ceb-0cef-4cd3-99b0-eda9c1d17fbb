/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from 'src/lib/http-client'
import { filterIt } from '@skynet/shared'
import { get_k_sso_token } from 'src/lib/device-id.ts'
import { flow } from 'lodash-es'

const dateCheck = (value?: number) => {
  if (!value) return 0
  return value
}

export const trimSplitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`)).map(str => str.replace(new RegExp(`^[${[' '].join('')}]+|[${[' '].join('')}]+$`, 'g'), ''))

const toNumArr = (value: string[]) => {
  return value.map(v => +v)
}

export const apiGetUserFeedbackList = (data: M.UserFeedback.Params) =>
  httpClient.post<ApiResponse<M.UserFeedback.Response>>('/customer_service_center/feedback/list', data, {
    transformRequestData: {
      start_time: [dateCheck],
      end_time: [dateCheck],
      resource_ids: [trimSplitIt([',', ';', '，', ' ', '；']), filterIt(Boolean), toNumArr],
    },
  })

export const apiGetProblemTypes = () =>
  httpClient.get<ApiResponse<{
    list: M.UserFeedback.ProblemType[]
  }>>('/customer_service_center/problem_type')

export const apiGetDramaId = (params: {
  app_name: string
  series_key: string
}) =>
  httpClient.post<ApiResponse<{
    drama_id: number
  }>>('/customer_service_center/feedback/jump', params)

export const apiGetUserFeedbackExport = (data: any) => {
  const params = {
    ...data,
  }
  params.resource_ids = flow(trimSplitIt([',', ';', '，', ' ', '；']), filterIt(Boolean), toNumArr)(params.resource_ids)
  params.start_time = dateCheck(params.start_time)
  params.end_time = dateCheck(params.end_time)
  return fetch(`${import.meta.env.VITE_DRAMA_API_URL}/customer_service_center/export`, {
    method: 'post',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      Device: 'Web',
      Token: get_k_sso_token() || '',
    },
    body: JSON.stringify(params),
  })
}

export const apiGetUserFeedbackById = (data: {
  id: number
}) => httpClient.post<ApiResponse<M.UserFeedback.DetailResponse>>('/customer_service_center/feedback/detail', data)

export const apiReplyIssues = (data: M.UserFeedback.Reply) => httpClient.post<ApiResponse<null>>('/customer_service_center/feedback/reply', data)

export const apiCloseOrder = (data: {
  id: number
}) => httpClient.post<ApiResponse<null>>('/customer_service_center/feedback/close', data)
