import { defineComponent, onMounted, ref } from 'vue'
import { useSearch } from './use-search'
import { Popup } from 'vant'
import hot from 'src/h5_modules/images/search-delete-icon.webp'
import { T } from './search.i18n'
import coverDefBg from 'src/h5_modules/images/cover-def.webp'

export default defineComponent({
  name: 'SearchHistory',
  setup() {
    const showMenu = ref(false)
    const { searchedKeywords, chooseKeyword, clearSearchKeywords } = useSearch()

    onMounted(() => {
      searchedKeywords.value = JSON.parse(localStorage.getItem('drama.searchKeywords') ?? '[]')
    })
    const handleImageError = event => {
      event.target.src = coverDefBg
    }
    return () => (
      <>
        {searchedKeywords.value && searchedKeywords.value.length > 0 && (
          <section class="searchHistory px-3 pb-4 pt-[53px]">
            <div class="flex h-11 items-center justify-between py-3">
              <h2 class="flex-1 text-base font-bold leading-tight text-[#fdfbfc]">
                {T.search.searchHistory()}
              </h2>
              <img
                class="w-4.5 h-4.5"
                src={hot}
                error={handleImageError}
                onClick={() => showMenu.value = true}
              />
            </div>
            <div class="flex flex-wrap gap-3 pt-1">
              {searchedKeywords.value.map(keyword => (
                keyword && (
                  <div
                    key={keyword}
                    class="rounded-50 inline-block bg-[#FDFBFC3B] px-2 py-1 text-xs font-normal leading-3 text-[#cccacb]"
                    onClick={() => chooseKeyword(keyword)}
                  >
                    {keyword}
                  </div>
                )
              ))}
            </div>
          </section>
        )}

        <Popup
          v-model:show={showMenu.value}
          position="bottom"
          class="rounded-t-xl !bg-[#434546] pt-0"
        >
          <h1 class="flex items-center justify-center py-4 text-base font-medium text-white">
            {T.search.deleteAllHistoryTips()}
          </h1>
          <ul class="w-full text-white">
            <li
              class="flex items-center justify-center py-4 text-[#FF2351]"
              onClick={() => {
                clearSearchKeywords()
                showMenu.value = false
              }}
            >
              {T.search.confirm()}
            </li>
            <li class="h-2 w-full bg-[#1D1D1E]" />
            <li
              class="flex items-center justify-center py-4"
              onClick={() => showMenu.value = false}
            >
              {T.search.cancel()}
            </li>
          </ul>
        </Popup>
      </>
    )
  },
})
