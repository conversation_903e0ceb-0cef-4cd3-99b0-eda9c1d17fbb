import { ElMessage } from 'element-plus'
import { pick } from 'lodash-es'
import { keepError } from 'src/lib/http-client'
import { ref, watch } from 'vue'
import { storageKeys } from '../common/constants'
import { sessionApi } from './session-api'

const token = ref(localStorage.getItem(storageKeys.token))
const signInWithPassword = async () => {
  token.value = await sessionApi.signInWithPassword(pick(formData.value, ['phone', 'pwd']))
  return token.value
}
const signInWithGoogle = async formData => {
  token.value = await sessionApi.loginWithGoogle(formData)
  return token.value
}
const signInWithFacebook = async formData => {
  token.value = await sessionApi.loginWithFacebook(formData)
  return token.value
}
const signInWithCode = async () => {
  token.value = await sessionApi.signInWithCode(pick(formData.value, ['phone', 'code']))
  return token.value
}
const signUp = async () => {
  await sessionApi.signUp(formData.value)
    .catch(keepError(error => ElMessage.error(error.message || '注册失败')))
  ElMessage.success('注册成功')
}

const signOut = async () => {
  await sessionApi.signOut()
  token.value = null
  localStorage.removeItem(storageKeys.token)
}

watch(() => token.value, t => {
  if (t) localStorage.setItem(storageKeys.token, t)
  else {
    localStorage.removeItem(storageKeys.token)
    localStorage.removeItem(storageKeys.me)
  }
})
const formData = ref({
  phone: '',
  pwd: '',
  code: '',
})

export const useSession = () => {
  return {
    signInWithPassword,
    signInWithCode,
    signUp,
    signOut,
    token,
    formData,
    signInWithGoogle,
    signInWithFacebook,
  }
}
