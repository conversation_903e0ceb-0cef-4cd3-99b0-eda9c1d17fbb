declare namespace Api {
  namespace MultiSubtitle {
      type Request = {
        title?: string
        id?: number
        partner_name?: string
        create_start_time?: number // 录入开始时间
        create_end_time?: number // 录入结束时间
        audit_status_list: number[]
        release_round?: 1 | 2
      }
      type Response = ApiResponse<{ list: M.MultiSubtitle.Resource[], total: number }>
  }
}

declare namespace M {
  namespace MultiSubtitle {
      type Resource = {
        id: number // 资源id
        title: string // 资源名称
        preview_status?: 1 | 2
        multi_subtitle_progress: Progress[] // 多语言字幕进度
        release_round: number
        claim_name: string
        audit_status?: 0 | 1 | 2 | 99 | number
        audit_reason?: string
        partner_name: string
        en_audit_process: {
          total_count: number
          finish_count: number
        }
        series_key_or_title_list: string | string[]
        resource_id_or_title_list: string | string[]
        multi_subtitle_audit_progress: Progress[]
        audit_to_status: 0 | 1 | 2 | 99 | number
        audit_to_reason: string
        reject_image_list?: string[]
        my: 0 | 1// 0: 不是我领取，1：是我领取 展示我的标签在每行记录，20250414新增
        count: number
        duration: number
      }

      type Progress = {
        language_code: string
        status: number // 1 已生成
      }
  }
}
