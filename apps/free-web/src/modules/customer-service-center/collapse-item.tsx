import { createComponent, fn, mc } from '@skynet/shared'
import { ref } from 'vue'
import { arrowLight } from './images/images'
import { MdPreview } from 'md-editor-v3'
import { track } from 'src/lib/track'
import { Fn } from '@vueuse/core'
type CollapseItemOptions = {
  props: {
    title: string
    content: string
  }
  emits: {
    expand: Fn
  }
}
export const CollapseItem = createComponent<CollapseItemOptions>({
  props: {
    title: '',
    content: '',
  },
  emits: {
    expand: fn,
  },
}, (props, { emit }) => {
  const visible = ref<boolean>(false)
  return () => (
    <x-collapse class="flex flex-col gap-y-2 px-3 py-2 bg-[var(--fill-5)] rounded-lg">
      <div class="py-3 justify-between items-start flex gap-3 text-[var(--text-2)] text-sm font-normal" onClick={() => {
        visible.value = !visible.value
        if (visible.value) {
          emit('expand')
        }
        track('csc', 'faq', 'click', {
          q_content: props.title,
          action_type: visible.value ? 1 : 0,
        })
      }}
      >
        {props.title}
        <img src={arrowLight} class={mc('w-5 h-5 transition-all duration-300 ease-in-out', visible.value ? 'rotate-180' : 'rotate-0')} />
      </div>
      { visible.value && (
        <div class="w-full border-t-solid border-t-[0.5px] border-t-[var(--line-1)]">
          <MdPreview
            no-img-zoom-in={true}
            model-value={props.content}
            no-mermaid={true}
            showCodeRowNumber={true}
            preview-theme="default"
            class="
            rounded-lg
            bg-[var(--fill-5)]
            text-[var(--text-3)]
            pt-4
            pb-2
            text-sm
            leading-tight
            [&_a]:bg-transparent
        [&_a]:text-[#4493f8]
            [&_a]:no-underline
            [&_ol]:list-decimal
            [&_ol]:pl-4
            [&_ol_li]:my-3
            [&_ul_li]:my-3
            [&_ul_li:nth-child(1)]:mt-0
            [&_ul_li:nth-last-child(1)]:mb-0
            [&_ol_li:nth-child(1)]:mt-0
            [&_ol_li:nth-last-child(1)]:mb-0
            [&_ol+p]:mt-3
            [&_ul+p]:mt-3
            [&_p+ol]:mt-3
            [&_p+ul]:mt-3"
          />
        </div>
      )}
    </x-collapse>
  )
})
