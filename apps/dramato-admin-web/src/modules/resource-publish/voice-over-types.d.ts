declare namespace Api {
  namespace VoiceOver {
    interface ListReqParams {
      resource_id?: number
      resource_name?: string
      page_index?: number
      page_size?: number
    }

    interface ListResp {
      list: IVoiceOverItem[]
      total: number
    }

    interface ResourceInfo {
      resource_id: number
      numbers?: number[]
    }

    interface PublishReqParams {
      resource_infos: ResourceInfo[]
      langs: string[]
      audio_type: 1 | 2 | number
    }

    interface GenerateReqParams {
      resource_id: number
      langs: string[]
    }
  }

  namespace AudioUpload {
    interface UploadReqParams {
      series_resource_id: number
      language_code: string
      netdisk_url: string
    }

  }
}

declare namespace M {
  interface IVoiceOverPublishData {
    language_code: string
    publish_status: number // 1 发布已完成 2 发布未完成
  }

  interface IVoiceOverNetDiskData {
    language_code: string
    netdisk_status: 0 | 1 | 2 | 3 // 网盘解析状态 0: 未开始 1：解析中 2：解析失败 3：解析完成
  }

  interface IVoiceOverItem {
    resource_id: number
    resource_name: string
    publish_data: IVoiceOverPublishData[]
    netdisk_data: IVoiceOverNetDiskData[]
    queue_data: IVoiceOverNetDiskData[]
    ai_voice_data?: IVoiceOverAiVoiceData[]
    showAll?: boolean
  }
}
