import { httpClient } from 'src/lib/http-client'

export const apiGetWalletStoreList = () => {
  return httpClient.get<ApiResponse<{
    exchange_list: TopUp.ExchangeProduct[]
    recharge_list: TopUp.RechargeProduct[]
  }>>('/fr-api/wallet/store/list')
}

export const apiGetMyWallet = () => {
  return httpClient.get<ApiResponse<MyWallet.MyWallet>>('/fr-api/wallet/my')
}

export const apiExchange = (data: {
  product_id: number
  series_key?: string
  episode_key?: string
  appsflyer_id?: string
  r_info?: string
}) => {
  return httpClient.post<ApiResponse<{
    order_id: number
  }>>('/fr-api/wallet/coins_exchange', data)
}
