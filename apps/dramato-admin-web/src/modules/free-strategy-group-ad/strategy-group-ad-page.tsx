/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, on } from '@skynet/shared'
import { Button, DateTime, Pager, showAlert, transformInteger } from '@skynet/ui'
import { set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { apiListStrategyGroupAds, apiUpdateStrategyGroupAdsState } from './strategy-group-ad-api'
import { computed, onMounted, ref, watch } from 'vue'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { useStrategyGroupAdStore } from './strategy-group-ad-store'
import { useListParamsStore } from './use-list-params-store'
type StrategyGroupAdPageOptions = {
  props: {}
}

const initListParams = {
  strategy_id: '',
  status: undefined,
  pay_mode: '',
  platform: '',
  sort: {
    created: 'DESC',
    id: 'DESC',
  },
  ad_type: 'common',
}
export const StrategyGroupAdPage = createComponent<StrategyGroupAdPageOptions>({
  props: {},
}, props => {
  const {
    Form,
    listParams,
    Table,
    list,
  } = useListParamsStore()

  const { resetFormData } = useStrategyGroupAdStore()
  const route = useRoute()
  const router = useRouter()

  onMounted(() => {
    void search(1)
    on('refresh:strategyGroup', () => {
      void search(1)
    })
  })
  const loading = ref<boolean>(false)
  const search = async (_page?: number) => {
    _page = _page || page.value + 1
    loading.value = true
    const res = await apiListStrategyGroupAds({ ...listParams.value, page_info: { offset: (_page - 1) * pageSize.value, size: pageSize.value }, pay_mode: (route.params.tabName as string).toLocaleUpperCase() })
      .finally(() => {
        loading.value = false
      })
    list.value = res.data?.list || []
    total.value = res.data?.page_info.total || 0
    page.value = _page
  }

  const isIAP = computed(() => route.params.tabName === 'iap')

  watch(() => listParams.value.sort, () => {
    void search(1)
  }, { deep: true })

  watch(() => router.currentRoute.value, () => {
    void search(1)
  }, { deep: true })

  const page = ref<number>(0)
  const pageSize = ref<number>(20)
  const total = ref<number>(1)
  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <x-nav class="space-y-2">
            <x-hide-when-in-dialog>
              <ul>
                <li>策略组列表</li>
              </ul>
            </x-hide-when-in-dialog>
            <x-nav-tab role="tablist" class="w-1/2 tabs tabs-boxed">
              <RouterLink to="iap" class="tab" activeClass="tab-active">IAP 广告方案</RouterLink>
              <RouterLink to="iaa" class="tab" activeClass="tab-active">IAA</RouterLink>
            </x-nav-tab>
          </x-nav>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              listParams.value = { ...initListParams }
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)} data={listParams.value} items={[
              ['策略ID', 'strategy_id', { type: 'text' }],
              ['状态', 'status', {
                type: 'select',
                options: [
                  { label: '线上', value: 1 },
                  { label: '草稿', value: 2 },
                  { label: '未上架', value: 3 },
                ],
              }, { transform: transformInteger }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex justify-between items-center">
            <div class="flex items-center gap-2">
              排序
              {listParams.value.sort.created === 'DESC'
                ? <span class="badge badge-outline" onClick={() => listParams.value.sort.created = 'ASC'}>创建时间：从新到旧</span>
                : <span class="badge badge-outline" onClick={() => listParams.value.sort.created = 'DESC'}>创建时间：从旧到新</span>}
              {listParams.value.sort.id === 'DESC'
                ? <span class="badge badge-outline" onClick={() => listParams.value.sort.id = 'ASC'}>ID：从大到小</span>
                : <span class="badge badge-outline" onClick={() => listParams.value.sort.id = 'DESC'}>ID：从小到大</span>}
            </div>
            <x-hide-when-in-dialog class="flex items-center gap-2">
              <Button class="btn-primary btn btn-sm" onClick={
                () => {
                  resetFormData()
                  void router.push(`/free-ad-strategy-group/${route.params.tabName as string}/create`)
                }
              }
              >{isIAP.value ? '新建方案' : '新建策略组'}
              </Button>
            </x-hide-when-in-dialog>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['策略组ID', 'id', { class: 'w-[100px]' }],
            ['策略组名称', 'name', { class: 'w-[200px]' }],
            ['聚合平台', 'ad_platform', { class: 'w-[100px]' }],
            [<span>权重<small class="ml-2">越大越优先</small></span>, 'priority', { class: 'w-[100px]' }],
            ['上架状态', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { label: '线上', value: 1 },
                { label: '草稿', value: 2 },
                { label: '未上架', value: 3 },
              ].find(item => item.value === row.status)?.label ?? row.status}
              </span>
            ), { class: 'w-[80px]' }],
            ['创建时间', row => (<DateTime value={(row?.created || 0) * 1000} />), { class: 'w-[150px]' }],
            ['创建人', 'created_operator_name', { class: 'w-[150px]' }],
            ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: 'w-[150px]' }],
            ['更新人', 'updated_operator_name', { class: 'w-[200px]' }],
            [<span class="px-3">操作</span>, row => (
              <div class="flex gap-x-2">
                <Button
                  class={mc('btn btn-outline btn-xs', row.status === 2 ? 'hidden' : '')}
                  onClick={() => {
                    if (!row.id) return
                    void apiUpdateStrategyGroupAdsState(
                      {
                        id: row.id,
                        operation: row.status === 3 ? 1 : 3,
                      },
                    )
                      .then(() => {
                        void search(page.value)
                      })
                      .catch((error: any) => {
                        showAlert(error.response.data.message, 'error')
                      })
                  }}
                >
                  {row.status === 3 ? '上架' : '下架'}
                </Button>
                <RouterLink to={`/free-ad-strategy-group/${route.params.tabName as string}/create?id=${row.id}`} class={mc('btn btn-outline btn-xs', [2, 3].includes(row.status || 0) ? '' : 'hidden')}>修改</RouterLink>
                <RouterLink to={`/free-ad-strategy-group/${route.params.tabName as string}/create?id=${row.id}&mode=view`} class={mc('btn btn-outline btn-xs', row.status === 1 ? '' : 'hidden')}>查看</RouterLink>
              </div>
            ), {
              class: 'w-[180px]',
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default StrategyGroupAdPage
