import { ref } from 'vue'
import { StsSign } from './upload-types'
import { apiGetOssSign } from './upload-api'
import { showToast } from 'src/modules/common/toast/toast'
import axios from 'axios'

const ossData = ref<StsSign>()
const ossDataLoading = ref(false)
const isUploading = ref<boolean>(false)

const getOssData = async () => {
  if (ossDataLoading.value) return ossData.value
  ossDataLoading.value = true
  try {
    const rs = await apiGetOssSign()
    ossData.value = rs.data
  } catch (error) {
    // TODO: 处理错误
  } finally {
    ossDataLoading.value = false
  }
}

const resetOssData = () => {
  ossData.value = undefined
  ossDataLoading.value = false
}

let failCount = 0

const uploadRequest = (data: FormData, errorTips: string) => {
  if (failCount !== 0) console.log('重试次数', failCount)
  return new Promise((resolve, reject) => {
    void axios.post(ossData.value?.host || '', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(() => {
      resolve(null)
    }).catch((error: unknown) => {
      console.log('upload error', error)
      // @ts-expect-error never mind
      if (error?.code === 'ERR_NETWORK') {
        // 这是一个阿里云存储服务直传图片的接口，不知道什么原因，会报这个错误，但是其实已经上传成功了，图片可以正常展示了，所以忽略这个错误
        // 为什么不知道原因呢，因为在付费版短剧端内打开以同样的方式就可以上传，但是在免费版不行，怀疑是客户端有什么限制
        return reject()
      }
      // @ts-expect-error never mind
      if (error?.response?.status === 403) {
        void getOssData().then(() => uploadRequest(data, errorTips)).then(resolve)
        return
      }
      if (failCount === 3) {
        showToast(errorTips)
        reject()
        return
      }
      failCount = failCount + 1
      void uploadRequest(data, errorTips).then(resolve).catch(reject)
    })
  })
}

export const useUploader = () => {
  return {
    ossData,
    getOssData,
    resetOssData,
    uploadRequest,
    isUploading,
  }
}
