/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert } from '@skynet/ui'
import { set } from 'lodash-es'
// import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { ref } from 'vue'
import { Fn } from '@vueuse/core'
import { apiAddScore } from '../spend_board/spend_board-api'
import { requiredLabel } from 'src/lib/required-label'

type RecommendFormOptions = {
  props: {
    series_resource_id: number
    editor_rating?: string
    deploy_task_doc?: string
  }
  emits: {
    cancel: Fn
    ok: () => void
  }
}

const scoreOptions = [
  { value: 'S+', label: 'S+' },
  { value: 'S', label: 'S' },
  { value: 'A+', label: 'A+' },
  { value: 'A', label: 'A' },
  { value: 'B+', label: 'B+' },
  { value: 'B', label: 'B' },
  { value: 'B-', label: 'B-' },
]
export const RecommendForm = createComponent<RecommendFormOptions>({
  props: {
    series_resource_id: 0,
    editor_rating: '',
  },
  emits: {
    cancel: fn,
    ok: fn,
  },
}, (props, { emit }) => {
  const isUpdating = ref(false)
  const params = ref<{
    score: string
    editor_rating: string
    deploy_task_doc: string
  }>({
    score: '',
    editor_rating: props.editor_rating,
    deploy_task_doc: props.deploy_task_doc,
  })

  const Form = CreateForm<{
    score: string
  }>()
  const formRules = z.object({
    score: z.string().min(1, {
      message: '请输入评分',
    }),
  })

  const { error, validateAll } = useValidator(params, formRules)

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        {/* {JSON.stringify(params.value)} */}
        <Form
          class="grid gap-y-1 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            console.log('path', path, 'value', value)
            set(params.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('内容剪辑评分'),
              'score',
              {
                type: 'select',
                options: scoreOptions,
                autoInsertEmptyOption: false,
              },
            ],
            [
              requiredLabel('剧拆文档'),
              'deploy_task_doc',
              {
                type: 'text',
              },
            ],
          ]}
          data={params.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={() => emit('cancel')}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const exclude: string[] = []

            if (!validateAll({ exclude })) {
              console.log('err', error)

              return
            }

            isUpdating.value = true
            void apiAddScore({
              series_resource_id: props.series_resource_id,
              editor_rating: params.value?.score,
              deploy_task_doc: params.value?.deploy_task_doc,
            }).then(() => {
              emit('ok')
            }).catch((error: any) => {
              showAlert(error.response.data.err_msg || error.response.data.message || '更新失败', 'error')
            }).finally(() => {
              isUpdating.value = false
            })
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确认'}
        </Button>
      </div>
    </>
  )
})

export default RecommendForm
