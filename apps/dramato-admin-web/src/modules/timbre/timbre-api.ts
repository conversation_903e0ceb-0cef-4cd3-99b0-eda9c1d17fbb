import { httpClient } from 'src/lib/http-client'

export const apiGetTimbreList = (data: Api.Timbre.ListReqParams) =>
  httpClient.post<ApiResponse<Api.Timbre.TimbreListResp>>('/aivoice/timbre_list', data)

export const apiCreateTimbre = (data: Api.Timbre.TimbreItem) =>
  httpClient.post<ApiResponse<null>>('/aivoice/timbre_create', data)

export const apiDelTimbre = (data: {
  timbre_id: number
}) =>
  httpClient.post<ApiResponse<null>>('/aivoice/timbre_delete', data)

export const apiGetFilterTimbre = (data: Api.Timbre.ListReqParams) =>
  httpClient.post<ApiResponse<Api.Timbre.TimbreListResp>>('/aivoice/timbre_filter_list', data)
