declare namespace M {
  namespace UserFeedback {
    interface Params {
      user_id?: number // 用户id
      email?: string
      app_name?: string
      app_version?: string
      problem_type?: number
      start_time?: number // 反馈日期起始
      end_time?: number // 反馈日期截止
      series_key?: string
      episode_key?: string
      resource_ids: string[] | string
      page_info?: {
        offset: number
        size: number
      }
    }

    interface Item {
      id: number
      user_id: number // 用户id
      email: string
      app_name: string
      operator: string
      // series_key: number // 剧id
      // episode_key: number // 集id
      app_version: string
      description: string
      image_list: string[] // 反馈图片
      created: number // 反馈日期
      problem_type?: number
      translated_description?: string
      series_key?: string
      episode_key?: string
      en_description?: string
      resource_id: number
      release_round: 1 | 2
      serial_number?: number
      language_code?: string
      feedback_status: 0 | 1 | 2 | number
      vip_level?: number
      vip_period?: string
      last_subscribe_period?: string
      vip_expire?: number
    }

    interface Response {
      list: Item[]
      page_info: PageInfo2
    }

    interface DetailItem {
      user_id: number
      email: string
      app_name: string
      app_version: string
      series_key: string
      episode_key: string
      problem_type: number
      description: string
      translated_description: string
      en_description: string
      image_list: string[]
      created: number
      resource_id: number
      release_round: number
      serial_number: number
      language_code: number
      id: number // 每行记录的唯一id
      work_order_id: number // 工单id，如果工单id=0，说明这条记录是工单
      feedback_status: 0 | 1 | 2 | number // 反馈状态 0:待回复 1:处理中 2:已关闭
      operator: string // 操作人
      be_replied_id: number // 被回复的问题id
      input_content?: string
      reply_content: {
        content: string // 回复内容
        image_list: string[] // 图片列表
      }
    }

    interface DetailResponse {
      list: DetailItem[]
    }

    interface Reply {
      be_replied_id: number // 被回复的问题id
      reply_content: {
        content: string // 回复内容
        image_list?: string[] // 图片列表
      }
    }

    interface ProblemType {
      id: number
      problem_type: string
    }
  }

}
