/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, DateTime, showAlert, transformNumber } from '@skynet/ui'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useRank } from './use-rank'
import { requiredLabel } from 'src/lib/required-label'
import { useAppAndLangOptions } from '../options/use-app-options'
import { apiEditRank } from './rank-api'
import { useEpisodeTags } from '../episode-tags/use-episode-tags'
import { watch } from 'vue'

type RankOptions = {
  props: {}
}
export const Rank = createComponent<RankOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    search,
    onCreateBtnClick,
    onEditBtnClick,
    onGetRankInfo,
    RankInfoForm,
    currentRankInfo,
    onSaveRankInfo,
  } = useRank()

  const { getTags } = useEpisodeTags()

  const { appOptions, languageOptions } = useAppAndLangOptions(() => params.value.app_id, {
    onSuccess: async () => {
      params.value.app_id = appOptions.value[0].value
      params.value.language_version = appOptions.value[0].language[0]
      await onGetRankInfo({
        type: params.value.type || 1,
        app_id: params.value.app_id,
        language_version: params.value.language_version,
      })
      if (!currentRankInfo.value.id) {
        return
      }
      void search()
    },
    isFree: true
  })

  watch(() => params.value.language_version, () => {
    if (!params.value.language_version) return;
    void getTags(params.value.language_version || '')
  }, { deep: true, immediate: true })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li class="flex items-center gap-x-2">
              Rank管理后台
            </li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            resetText=""
            onSubmit={async () => {
              await onGetRankInfo({
                type: params.value.type || 1,
                app_id: params.value.app_id || 0,
                language_version: params.value.language_version || '',
              })
              void search()
            }}
            data={params.value}
            items={[
              {
                label: requiredLabel('应用'),
                path: 'app_id',
                transform: transformNumber,
                input: {
                  type: 'select',
                  class: 'w-[240px]',
                  autoInsertEmptyOption: false,
                  options: appOptions.value,
                },
              },
              { label: requiredLabel('语言'),
                path: 'language_version',
                input: {
                  type: 'select',
                  autoInsertEmptyOption: false,
                  options: languageOptions.value,
                },
              },
              { label: requiredLabel('榜单类型'),
                path: 'type',
                input: {
                  type: 'select',
                  autoInsertEmptyOption: false,
                  options: [
                    { label: '垂直标签榜', value: 1 },
                    { label: '运营榜', value: 2 },
                  ],
                },
                transform: transformNumber,
              },
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex flex-col justify-between items-start">
            <RankInfoForm
              onChange={(path, value) => {
                set(currentRankInfo.value, path, value)
              }}
              resetText=""
              submitText="保存"
              onSubmit={() => {
                void onSaveRankInfo()
              }}
              data={currentRankInfo.value}
              items={[
                {
                  label: requiredLabel('榜单名称'),
                  path: 'name',
                  input: {
                    type: 'text',
                  },
                },
                { label: requiredLabel('榜单优先级'),
                  path: 'priority',
                  input: {
                    type: 'number',
                  },
                  transform: transformNumber,
                  hint: <span>用来短剧特色标签的展示优先级，非必要不动</span>,
                },
              ]}
            />
            <x-button-wrap class="flex justify-between items-center w-full">
              app按顺序从左到右显示，当只有一个Tab时，不显示顶部分类栏
              <Button class="btn-primary btn btn-sm" disabled={!currentRankInfo.value.id} onClick={onCreateBtnClick}>新增Rank</Button>
            </x-button-wrap>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['榜单ID', 'id', { class: 'w-[100px]' }],
            ['优先级', 'priority', { class: 'w-[100px]' }],
            ['细分榜单名称', 'name', { class: 'w-[120px]' }],
            ['细分榜单类型', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { value: 1, label: '人工' },
                { value: 2, label: '自动' },
              ].find(item => item.value === row.type)?.label ?? '-空-'}
              </span>
            ), { class: 'w-[100px]' }],
            ['榜单最大长度', 'size', { class: 'w-[100px]' }],
            ['覆盖剧数', row => row.series_info?.length, { class: 'w-[100px]' }],
            ['创建时间', row => <DateTime value={row?.created ? row?.created * 1000 : 0} />, { class: 'w-[150px]' }],
            ['创建人', row => row?.create_user_name, { class: 'w-[100px]' }],
            ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : 0} />, { class: 'w-[150px]' }],
            ['修改人', row => row?.update_user_name, { class: 'w-[100px]' }],
            ['上架状态', row => (
              <span class="badge badge-outline whitespace-nowrap">{[
                { label: '线上', value: 1 },
                { label: '未上架', value: 2 },
              ].find(item => item.value === row.status)?.label ?? '未上架'}
              </span>
            ), { class: 'w-[80px]' }],
            [<span class="px-3">操作</span>, row => (
              <div class="flex gap-x-2">
                <Button class="btn-outline btn btn-xs" onClick={() => {
                  void apiEditRank({...row, status: row.status === 1 ? 2 : 1}).then(() => {
                    void search()
                    showAlert('操作成功')
                  }).catch((error: any) => {
                    showAlert(error.response.data.message || '编辑失败', 'error')
                  })
                }}
                >{row.status === 1 ? '下架' : '上架'}
                </Button>
                <Button class="btn-outline btn btn-xs" onClick={() => onEditBtnClick(row)}>编辑</Button>
              </div>
            ), {
              class: 'w-[100px]',
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
      }}
    </NavFormTablePager>
  )
})

export default Rank
