:root {
  --phone-page-max-width: 450px;
  --pad-page-max-width: 720px;
  --pc-page-max-width: 1140px;
  --top-bar-height: 64px;
  --black-a08: rgba(0, 0, 0, 0.08);
  --light-bg: #ffffff;
  --dark-bg: #1E2024;
  --btn-color-disabled: 'rgba(144, 144, 144, 0.4)';
}

.tm-no-dark {
  color-scheme: none;
}

/* 以下是颜色规范 */
:root, .tm-no-dark {
  /* 强调/标题/正文 */
  --text-1: #141414;
  /* 次强调/标题/正文 */
  --text-2: #595C63;
  /* 次要 */
  --text-3: #9798A1;
  /* 置灰 */
  --text-4: #CBCED4;
  /* 白字 */
  --text-5: #FFFFFF;
  /* 反白，仅用于蓝色背景 */
  --text-6: #FFFFFF;
  /* 本色文字置灰 */
  --text-7: #FFFFFF;
  /* Surface（⚠️ 在 light mode 模式下均为白色，但在 dark mode 模式下会呈现不同海拔的灰度，越高则越灰） */
  /* 基础背景 */
  --surface-0: #E6E8ED;
  /* 卡片背景 */
  --surface-1: #FFFFFF;
  /* 动作面板背景 */
  --surface-2: #FFFFFF;
  /* 弹窗背景 */
  --surface-3: #FFFFFF;
  /* 弹窗背景+1 */
  --surface-4: #FFFFFF;

  --mask-1: rgb(20, 20, 20, 0.4);

  /* 背景色 */
  /* 深白背景 */
  --fill-1: #E6E8ED;
  /* 中白背景 */
  --fill-2: #F2F3F7;
  /* 浅白背景 */
  --fill-3: #FAFBFD;
  /* 纯白背景 */
  --fill-4: #FFFFFF;
  /* 深黑背景 */
  --fill-5: #141414;
  /* 中黑北京 */
  --fill-6: #292C31;
  /* 浅黑背景 */
  --fill-7: #32353B;
  /* 特殊背景 */
  --fill-8: #F0F1F5;

  /* 线 */
  /* 深色线 */
  --line-1: #D1D5DB;
  /* 中色线 */
  --line-2: #DEDFE4;
  /* 浅色线 */
  --line-3: #E6EbED;
  /* 纯白线 */
  --line-4: #FFFFFF;

  --mask-1: #14141466;

  /* 品牌色 */
  --brand-6: #0A66C2;
  /* 激活色 */
  --brand-7: #1E50A6;
  /* hover 色 */
  --brand-5: #558ED9;
  /* 禁用色 */
  --brand-4: #91BEF2;
  /* 浅色背景 */
  --brand-3: #B2D9FF;
  /* 浅色背景+1 */
  --brand-2: #CCE7FF;
  /* 浅色背景+2 */
  --brand-1: #EBF6FF;

  /* 报错色 */
  --error-6: #E52E27;
  /* 报错 hover 色 */
  --error-7: #DB332C;
  /* 报错禁用色 */
  --error-3: #FE7670;
  /* 禁用背景 */
  --error-1: #FCEBEB;

  /* 绿色 */
  --green-6: #2F8E4E;
  /* 浅绿色 */
  --green-4: #45AB68;
  /* 淡绿色 */
  --green-1: #E0F6E3;
  /* 紫色 */
  --purple-6: #A471E1;
  /* 金色 */
  --gold-6: #BA7F33;
  /* 浅金色 */
  --gold-2: #F3C787;

  /* 渐变色 */
  --blue-grad-start: #009DFF00;
  --blue-grad-end: #009DFF28;

  --text-1-6: rgba(20, 20, 20, 0.06);
  --text-1-12: rgba(20, 20, 20, 0.12);
}
