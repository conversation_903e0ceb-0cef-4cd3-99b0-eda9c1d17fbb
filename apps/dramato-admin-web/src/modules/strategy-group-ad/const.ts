export const adSceneMap: Record<M.AdScene, <PERSON><PERSON>AdSceneName> = {
  app_open: '开屏',
  drama_free: '面板前',
  drama_lock: '面板后',
  reward: 'reward',
  quit_player: '退出播放器',
  out_flow: '外流位',
} as const
export const adSceneOptions = (disabled?: boolean) => [
  { label: adSceneMap.app_open, value: 'app_open', disabled },
  { label: adSceneMap.drama_free, value: 'drama_free', disabled },
  { label: adSceneMap.drama_lock, value: 'drama_lock', disabled },
  { label: adSceneMap.reward, value: 'reward', disabled },
  { label: adSceneMap.quit_player, value: 'quit_player', disabled },
  { label: adSceneMap.out_flow, value: 'out_flow', disabled },
]
