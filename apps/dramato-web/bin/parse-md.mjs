import fs from 'fs'
import path from 'path'

// 汇总所有mark1和mark2
const mark1Array = ['Q:', '問：', '问：', 'प्रश्न:']
const mark2Array = ['A:', '答：', '答：', 'उत्तर:']

// 解析md文件
function parseMd(content) {
  const result = []
  const lines = content.split('\n')
  let question = ''
  let answer = []
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    
    const isQuestion = mark1Array.some(mark => line.startsWith(mark))
    const isAnswer = mark2Array.some(mark => line.startsWith(mark))
    
    if (isQuestion) {
      // 如果已有问题和答案,先保存
      if (question && answer.length) {
        result.push({
          question,
          answer: answer.join('\n')
        })
        answer = []
      }
      const mark = mark1Array.find(mark => line.startsWith(mark))
      question = line.slice(mark.length).trim()
    } else if (isAnswer) {
      // 收集mark2后的内容
      if (question) {
        const mark = mark2Array.find(mark => line.startsWith(mark))
        answer.push(line.slice(mark.length).trim())
      }
    } else if (question) {
      // 收集答案内容
      // 过滤掉以**或#开头的行
      if (!line.startsWith('**') && !line.startsWith('#')) {
        answer.push(line)
      }
    }
  }
  // 保存最后一组问答
  if (question && answer.length) {
    result.push({
      question,
      answer: answer.join('\n')
    })
  }
  
  return result
}

// 递归遍历目录
function traverseDir(dir) {
  const files = fs.readdirSync(dir)
  const result = {}
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      const subResult = traverseDir(filePath)
      Object.assign(result, subResult)
    } else if (file === 'issue.md') {
      const content = fs.readFileSync(filePath, 'utf-8')
      // 获取相对路径作为key
      const relativePath = path.relative(path.resolve('bin/public/dramawave/rules'), filePath)
      const key = path.dirname(relativePath)
      result[key] = parseMd(content)
    }
  })
  
  return result
}

// 开始执行
const allResults = traverseDir(path.resolve('bin/public/dramawave/rules'))

// 写入到一个总的json文件
const targetPath = path.resolve('src/modules/customer-service-center/all-faq.json')
fs.mkdirSync(path.dirname(targetPath), { recursive: true })
fs.writeFileSync(targetPath, JSON.stringify(allResults, null, 2))
