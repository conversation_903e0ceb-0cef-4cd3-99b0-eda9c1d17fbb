import { httpClient } from 'src/lib/http-client'

export const apiGetVoiceList = (data: M.VoiceoverEditor.Params) =>
  httpClient.post<M.VoiceoverEditor.Response>('/aivoice/aivoice_episode_voices', data)

export const apiRecreateVoice = (data: M.VoiceoverEditor.reCreateParams) =>
  httpClient.post<M.VoiceoverEditor.Response>('/aivoice/aivoice_episode_voice_modify', data)

export const apiCombineVoice = (data: M.VoiceoverEditor.combineParams) =>
  httpClient.post<M.VoiceoverEditor.Response>('/aivoice/aivoice_episode_voice_combine', data)

export const apiAdjustVoice = (data: M.VoiceoverEditor.adjustParams) =>
  httpClient.post<M.VoiceoverEditor.Response>('/aivoice/aivoice_episode_voice_adjust', data)

export const apiUploadAudio = (data: M.VoiceoverEditor.uploadParams) =>
  httpClient.post<M.VoiceoverEditor.Response>('/aivoice/aivoice_audio_upload', data)

export const apiSplitAudio = (data: M.VoiceoverEditor.splitParams) =>
  httpClient.post<M.VoiceoverEditor.Response>('/aivoice/aivoice_audio_seperate', data)

export const apiDeleteVoice = (data: M.VoiceoverEditor.deleteParams) =>
  httpClient.post<M.VoiceoverEditor.Response>('/aivoice/aivoice_audio_delete', data)