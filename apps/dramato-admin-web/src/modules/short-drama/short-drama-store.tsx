import { ref, watch } from 'vue'
import { apiDramaDetails, apiDramaUpdateListStatus, apiDramaUpdatePrice, DramaItem, updateListingStatus, updatePrice, apiGetLabelList } from './short-drama-api'
import dayjs from 'dayjs'
import { showAlert } from '@skynet/ui'
import { useAppAndLangOptions } from '../options/use-app-options'

const dramaList = ref<DramaItem[]>([])
const selectedDramas = ref<DramaItem[]>([]) // 选中的搜索结果集
const listStatus = ref<-1 | 1 | 2 | 3 | 4>(2)
const hasTime = ref<boolean>(false)
const time = ref<string>('')
const loading = ref<boolean>(false)
const startPayingEpisodes = ref<number>()
const episodesPrice = ref<number>()
const dramaDetails = ref<DramaItem | null>()
const isFree = ref<boolean>(false)
const freeStartTime = ref<number>()
const freeEndTime = ref<number>()
const platform_sync_list = ref<boolean>(true)
const unshelve_reason = ref('')
const labels = ref<M.ITag[]>([])

const { appOptions } = useAppAndLangOptions(() => undefined, {})
export const LIST_STATUS_MAP = [{
  code: 1,
  name: '未上架',
},
{
  code: 2,
  name: '待上架',
},
{
  code: 3,
  name: '已上架',
},
{
  code: 4,
  name: '待下架',
},
{
  code: -1,
  name: '已下架',
}]

watch(() => listStatus.value, () => {
  if (listStatus.value === 2 || listStatus.value === 4) {
    hasTime.value = true
  } else {
    hasTime.value = false
  }
}, {
  immediate: true,
})

const addDramas = (data: DramaItem[]) => {
  data.forEach(drama => {
    if (!selectedDramas.value.find(item => item.drama_id === drama.drama_id)) {
      selectedDramas.value.push(drama)
    }
  })
}

const removeDramas = (data: DramaItem[]) => {
  data.forEach(drama => {
    const idx = selectedDramas.value.findIndex(item => item.drama_id === drama.drama_id)
    if (idx > -1) {
      selectedDramas.value.splice(idx, 1)
    }
  })
}

const getDetails = (dramaId: number) => {
  dramaDetails.value = null
  void apiDramaDetails(dramaId).then(res => {
    if (!res.data) return
    dramaDetails.value = res.data
    listStatus.value = res.data.listing_status
    hasTime.value = res.data.listing_status === 2 ? res.data.is_time_listing : res.data.listing_status === 4 ? res.data.is_time_removal : false
    time.value = listStatus.value === 2 ? formatTime(res.data.listing_time) : listStatus.value === 4 ? formatTime(res.data.removal_time) : ''
    startPayingEpisodes.value = res.data.start_paying_episodes
    episodesPrice.value = res.data.episodes_price
    isFree.value = res.data.free
    freeStartTime.value = res.data.free_start
    freeEndTime.value = res.data.free_end
    unshelve_reason.value = res.data.unshelve_reason || ''
  })
}

const formatTime = (time: number) => {
  if (time <= 0) {
    return '--'
  }
  const date = new Date(time * 1000)
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const updateDramasListingStatus = (app_id: string, multiple: boolean, refresh: boolean, handler?: (fails?: [{
  id: string
  msg: string
}]) => void) => {
  if (multiple) {
    if (selectedDramas.value.length === 0) {
      showAlert('请选择要操作的数据', 'error')
      return
    }
  }
  if (hasTime.value && !time.value) {
    showAlert('请选择时间', 'error')
    return
  }
  if (unshelve_reason.value === '' && listStatus.value === -1) {
    showAlert('请输入下架原因', 'error')
    return
  }
  const appName = appOptions.value.find(row => row.value + '' == app_id)?.label || ''
  console.log(appName.includes('H5'))
  let params: updateListingStatus = {
    id: multiple ? selectedDramas.value.map(item => item.drama_id) : [dramaDetails.value!.drama_id],
    list_status: listStatus.value,
    list_time: undefined,
    platform_sync_list: appName.includes('H5') ? false : platform_sync_list.value,
  }
  if (listStatus.value === 2) {
    params = {
      ...params,
      list_time: hasTime.value ? dayjs(time.value).format('YYYY-MM-DDTHH:mm:ss+08:00') : undefined,
    }
  } else if (listStatus.value === 4) {
    params = {
      ...params,
      list_time: hasTime.value ? dayjs(time.value).format('YYYY-MM-DDTHH:mm:ss+08:00') : undefined,
    }
  } else if (listStatus.value === -1) {
    params = {
      ...params,
      unshelve_reason: unshelve_reason.value,
    }
  }
  loading.value = true
  void apiDramaUpdateListStatus(params).then(res => {
    if (refresh) {
      const item = dramaList.value.find(item => item.drama_id === dramaDetails.value?.drama_id)
      if (item) {
        void apiDramaDetails(item.drama_id).then(res => {
          if (res.data) {
            Object.assign(item, res.data)
          }
        })
      }
    }
    if (handler) {
      handler(res.data?.fail)
    }
  }).catch(error => {
    showAlert(error.response.data.message, 'error')
  }).finally(() => {
    loading.value = false
  })
}

const updateDramasPrice = (multiple: boolean, refresh: boolean, handler?: (fails?: [{
  id: string
  msg: string
}]) => void) => {
  if (multiple) {
    if (selectedDramas.value.length === 0) {
      showAlert('请选择要操作的数据', 'error')
      return
    }
  }
  if (!multiple) {
    if (!episodesPrice.value && episodesPrice.value !== 0) {
      showAlert('请输入剧集单价', 'error')
      return
    }
  }
  if (episodesPrice.value === 0 || episodesPrice.value! < 1 || episodesPrice.value! > 10000) {
    showAlert('剧集单价需在1-10000之间', 'error')
    return
  }
  // 开始付费集数不能小于任意一个当前付费集数
  if (
    startPayingEpisodes.value
    && ((multiple && startPayingEpisodes.value < Math.min(...selectedDramas.value.map(item => item.start_paying_episodes)))
    || (!multiple && startPayingEpisodes.value < dramaDetails.value!.start_paying_episodes))) {
    showAlert('开始付费集数不能小于当前付费集数', 'error')
    return
  }
  const params: updatePrice = {
    id: multiple ? selectedDramas.value.map(item => item.drama_id) : [dramaDetails.value!.drama_id],
    episodes_price: episodesPrice.value ? episodesPrice.value * 1 : -1,
    start_paying_episodes: startPayingEpisodes.value,
    free_start: freeStartTime.value,
    free_end: freeEndTime.value,
    platform_sync_list: platform_sync_list.value,
  }
  loading.value = true
  void apiDramaUpdatePrice(params).then(res => {
    if (refresh) {
      const item = dramaList.value.find(item => item.drama_id === dramaDetails.value?.drama_id)
      if (item) {
        void apiDramaDetails(item.drama_id).then(res => {
          if (res.data) {
            Object.assign(item, res.data)
          }
        })
      }
    }
    if (handler) {
      handler(res.data?.fail)
    }
  }).catch(error => {
    showAlert(error.response.data.message, 'error')
  }).finally(() => {
    loading.value = false
  })
}

const resetStore = () => {
  listStatus.value = 2
  hasTime.value = true
  time.value = ''
  episodesPrice.value = undefined
  startPayingEpisodes.value = undefined
  isFree.value = false
  freeStartTime.value = undefined
  freeEndTime.value = undefined
  platform_sync_list.value = true
  unshelve_reason.value = ''
}

/**
 * @description 根据listingStatus获取状态名
 * @param code 上架状态码
 * @returns 对应名称
 */
const getListingStatusDesc = (code: 1 | 2 | 3 | 4 | -1 | number) => {
  return LIST_STATUS_MAP.find(item => item.code === code)?.name
}

const getLabels = async (type: 1 | 2 | 3) => {
  const rootRes = await apiGetLabelList({
    language_code: 'zh-CN',
    label_type: type,
  })
  labels.value = labels.value.concat(rootRes.data?.list || [])
}

const getLabelList = () => {
  void getLabels(1)
  void getLabels(2)
  void getLabels(3)
}

export const useShortDramaStore = () => {
  return {
    dramaDetails,
    selectedDramas,
    dramaList,
    listStatus,
    loading,
    time,
    hasTime,
    episodesPrice,
    freeStartTime,
    freeEndTime,
    isFree,
    startPayingEpisodes,
    addDramas,
    removeDramas,
    updateDramasListingStatus,
    updateDramasPrice,
    getDetails,
    formatTime,
    resetStore,
    getListingStatusDesc,
    platform_sync_list,
    unshelve_reason,
    getLabelList,
    labels,
  }
}
