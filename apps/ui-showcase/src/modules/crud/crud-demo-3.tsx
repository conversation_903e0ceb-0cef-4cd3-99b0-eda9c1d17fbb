import { createComponent } from '@skynet/shared'
import { Pager } from '@skynet/ui'
import { Wrapper } from 'src/layout/wrapper'
import { ref } from 'vue'
type CrudDemo1Options = {
  props: {}
}
export const CrudDemo1 = createComponent<CrudDemo1Options>({
  props: {},
}, () => {
  const page = ref(1)
  const pageSize = ref(10)
  const total = ref(10000000)
  return () => (
    <div class="space-y-4">
      <Wrapper>
        <Pager class="justify-end" v-model:page={page.value} v-model:size={pageSize.value} total={total.value} />
      </Wrapper>
    </div>
  )
})

export default CrudDemo1
