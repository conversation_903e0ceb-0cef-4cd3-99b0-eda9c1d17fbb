import plugin from 'tailwindcss/plugin'
import {breakpoints} from '@skynet/shared/env-helper'

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/**/*.{html,js,ts,jsx,tsx,md}',
    '../../packages/ui/!(node_modules)/**/*.{html,js,ts,jsx,tsx,vue}',
  ],
  theme: {
    extend: {
      colors: {
        /* 强调/标题/正文 */
        'text-1': 'var(--text-1)',
        /* 次强调/标题/正文 */
        'text-2': 'var(--text-2)',
        /* 次要 */
        'text-3': 'var(--text-3)',
        /* 置灰 */
        'text-4': 'var(--text-4)',
        /* 白字 */
        'text-5': 'var(--text-5)',
        /* 反白，仅用于蓝色背景 */
        'text-6': 'var(--text-6)',
        /* 本色文字置灰 */
        'text-7': 'var(--text-7)',
        /* 基础背景 */
        'surface-0': 'var(--surface-0)',
        /* 卡片背景 */
        'surface-1': 'var(--surface-1)',
        /* 动作面板背景 */
        'surface-2': 'var(--surface-2)',
        /* 弹窗背景 */
        'surface-3': 'var(--surface-3)',
        /* 弹窗背景+1 */
        'surface-4': 'var(--surface-4)',
        'mask-1': 'var(--mask-1)',

        /* 深白背景 */
        'fill-1': 'var(--fill-1)',
        /* 中白背景 */
        'fill-2': 'var(--fill-2)',
        /* 浅白背景 */
        'fill-3': 'var(--fill-3)',
        /* 纯白背景 */
        'fill-4': 'var(--fill-4)',
        /* 深黑背景 */
        'fill-5': 'var(--fill-5)',
        /* 中黑背景 */
        'fill-6': 'var(--fill-6)',
        /* 浅黑背景 */
        'fill-7': 'var(--fill-7)',
        /* 特殊背景 */
        'fill-8': 'var(--fill-8)',
        /* 深色线 */
        'line-1': 'var(--line-1)',
        /* 中色线 */
        'line-2': 'var(--line-2)',
        /* 浅色线 */
        'line-3': 'var(--line-3)',
        /* 纯白线 */
        'line-4': 'var(--line-4)',
        /* 品牌色 */
        'brand-6': 'var(--brand-6)',
        /* 激活色 */
        'brand-7': 'var(--brand-7)',
        /* hover 色 */
        'brand-5': 'var(--brand-5)',
        /* 禁用色 */
        'brand-4': 'var(--brand-4)',
        /* 浅色背景 */
        'brand-3': 'var(--brand-3)',
        /* 浅色背景+1 */
        'brand-2': 'var(--brand-2)',
        /* 浅色背景+2 */
        'brand-1': 'var(--brand-1)',
        /* 报错色 */
        'error-6': 'var(--error-6)',
        /* 报错 hover 色 */
        'error-7': 'var(--error-7)',
        /* 报错禁用色 */
        'error-3': 'var(--error-3)',
        /* 禁用背景 */
        'error-1': 'var(--error-1)',
        /* 绿色 */
        'green-6': 'var(--green-6)',
        /* 浅绿色 */
        'green-4': 'var(--green-4)',
        /* 淡绿色 */
        'green-1': 'var(--green-1)',
        /* 紫色 */
        'purple-6': 'var(--purple-6)',
        /* 金色 */
        'gold-6': 'var(--gold-6)',
        /* 浅金色 */
        'gold-2': 'var(--gold-2)',
        /* 渐变色 */
        'blue-grad-start': 'var(--blue-grad-start)',
        'blue-grad-end': 'var(--blue-grad-end)',
      },
      spacing: {
        'top-bar': 'var(--top-bar-height)',
      },
      dropShadow: {
        center: '2px 2px 8px rgba(0, 0, 0, 0.1)',
      }
    },
    zIndex: {
      // 局部
      'up': '1',
      'up-up': '2',
      // 全局
      'table-header': '32',
      'footer': '64',
      'shareButton': '128',
      'top-bar': '256',
      'popover': '512',
      'dialog': '1024',
      'popover-in-dialog': 1024 + 32,
      'alert': '4096'
    },
    screens: {
      pad: breakpoints.pad.min,
      pc: breakpoints.pc.min,
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
    }
  },
  plugins: [
    require('tailwind-scrollbar'),
    plugin(function({ matchUtilities, theme, addUtilities }) {
      matchUtilities(
        {
          'grid-areas': (value) => ({
            'grid-template-areas': value
          }),
          'grid-in': (value) => ({
            'grid-area': value
          }),
        }
      )

      const generateSpacingUtilities = (sizes) => {
        const newUtilities = {};

        sizes.forEach((size) => {
          const pxValue = `${size}px`;
          newUtilities[`.m-${size}px`] = { margin: pxValue };
          newUtilities[`.p-${size}px`] = { padding: pxValue };
          newUtilities[`.w-${size}px`] = { width: pxValue };
        });

        return newUtilities;
      };

      const sizes = Array.from({ length: 2000 }, (_, i) => i + 1); // 生成 1px 到 2000px 的类名
      const spacingUtilities = generateSpacingUtilities(sizes);

      addUtilities(spacingUtilities, ['responsive']);

    })
  ],

}

