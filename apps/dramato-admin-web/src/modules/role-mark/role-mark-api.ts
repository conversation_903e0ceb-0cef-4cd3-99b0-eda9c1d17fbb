import { httpClient } from 'src/lib/http-client'

export const apiGetAiVoiceResource = (data: {
  series_resource_id: number
}) => httpClient.post<ApiResponse<Api.RoleMark.IAiVoiceResource>>('/aivoice/aivoice_resource_info', data)

export const apiGetSingleSubtitle = (data: {
  series_resource_id: number
  serial_number: number
  language?: string
}) => httpClient.post<ApiResponse<Api.RoleMark.ISingleSubtitle>>('/aivoice/aivoice_single_subtitle', data, {
  transformResponseData: {
    'data.origin_syllable_list': [(value: unknown) => {
      if (!value) return []
      return value
    }],
    'data.shorten_syllable_list': [(value: unknown) => {
      if (!value) return []
      return value
    }],
  },
})

type IAnnotation = {
  order?: number
  character_id?: number
  emotion?: string
  effect?: string
}

type sbContItem = {
  text: string
  index: number
}

export const apiSaveSingleSubtitleRole = (data: {
  series_resource_id: number
  serial_number: number
  annotations: IAnnotation[]
}) =>
  httpClient.post<ApiResponse<Api.RoleMark.ISingleSubtitle>>('/aivoice/aivoice_subtitle_annotate', data)

export const apiGetSubtitleSyllableCount = (data: {
  series_resource_id: number
  serial_number: number
  language: string
  no_ai_horizontal?: boolean
  texts: sbContItem[]
}) => httpClient.post<ApiResponse<{
  series_resource_id: number
  serial_number: number
  language: string
  texts: {
    index: number
    text: string
    syllable_count: number
  }[]
}>>('/aivoice/aivoice_get_subtitle_syllable_cnt', data)

// 10. 单集打标信息-打标角色列表
export const apiGetSeriesCharacters = (data: {
  series_resource_id: number
}) => httpClient.post<ApiResponse<{ characters: Api.RoleMark.ICharacter[] }>>('/aivoice/aivoice_series_characters', data)

export const apiSaveSubtitleContent = (data: {
  series_resource_id: number
  serial_number: number
  language_code: string
  audio_type: 0 | 1 | 2 // 0-默认 1-配音 2-ai配音
  content: string
  alter_type?: 0 | 1 | 2 | 3
}) =>
  httpClient.post<ApiResponse<{
    subtitle_uri: string
  }>>('/series_resource/upload_subtitle_text', data)
