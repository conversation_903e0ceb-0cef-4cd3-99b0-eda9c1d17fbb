import { ClassName, createComponent, mergeClass } from '@skynet/shared'
import ImageWrapper from './image-wrapper'
import Image from './image'
import { MergeClass } from '../ui'
import { useImagePreviewStore } from './use-preview-store'
type ImageGroupOptions = {
  props: {
    imageList: {
      src: string
      width: number
      height: number
    }[]
  }
}

const getImagesLayout: (count: number) => ClassName = (count: number) => {
  let gridLayout: ClassName = ''
  if (count === 0) return ''
  if (count > 3) {
    gridLayout = `grid grid-cols-2 grid-rows-3 gap-1 p-1 h-[554px]`
  } else if (count === 3) {
    gridLayout = `grid grid-cols-2 grid-rows-2 gap-1 p-1 h-[368px]`
  } else if (count === 2) {
    gridLayout = 'grid gap-1 grid-cols-2 grid-rows-1 p-1 h-[276px]'
  } else if (count === 1) {
    gridLayout = 'grid gap-1 grid-cols-1 grid-rows-1 h-[368px] overflow-hidden p-1'
  }
  return gridLayout
}

export const ImageGroup = createComponent<ImageGroupOptions>({
  props: {
    imageList: [],
  },
}, props => {
  return () => {
    const { showImagePreviewDialog } = useImagePreviewStore()
    const list = [
      ...props.imageList.map(i => i.src),
      'https://img.tianmai.cn/sky-link/user-post/u-1c05d1ad85441c9d2c8e14a59c40fe/image_1826208345215062017.jpg',
      'https://img.tianmai.cn/sky-link/user-post/u-1c05d1ad85441c9d2c8e14a59c40fe/image_1826208345215062017.jpg',
      'https://img.tianmai.cn/sky-link/user-post/u-1c05d1ad85441c9d2c8e14a59c40fe/image_1826208345215062017.jpg',
      'https://img.tianmai.cn/sky-link/user-post/u-1c05d1ad85441c9d2c8e14a59c40fe/image_1826208345215062017.jpg',
      'https://img.tianmai.cn/sky-link/user-post/u-1c05d1ad85441c9d2c8e14a59c40fe/image_1826208345215062017.jpg',
      'https://img.tianmai.cn/sky-link/user-post/u-1c05d1ad85441c9d2c8e14a59c40fe/image_1826208345215062017.jpg',
      'https://img.tianmai.cn/sky-link/user-post/u-1c05d1ad85441c9d2c8e14a59c40fe/image_1826208345215062017.jpg',
    ]
    if (!props.imageList || props.imageList.length === 0) {
      return null
    }

    return (
      <div
        class={mergeClass('grid gap-1 w-full', getImagesLayout(list.length))}
        onClick={() => showImagePreviewDialog({
          imageList: props.imageList || [],
        })}
      >
        {
          list.slice(0, 4)
            .map((image, idx) => (
              <MergeClass
                baseClass="relative cursor-pointer"
                class={[
                  list.length === 3 && idx === 0 ? 'row-span-2' : '',
                  list.length === 3 && idx === 2 ? 'col-start-2 row-start-2' : '',
                  list.length > 3 && idx === 0 ? 'row-span-3' : '',
                  list.length > 3 && idx === 2 ? 'col-start-2' : '',
                  list.length > 3 && idx === 3 ? 'col-start-2 row-start-3' : '',
                ]}
              >
                <ImageWrapper class="absolute left-0 right-0 top-0 bottom-0 pointer-events-none">
                  <Image class="size-full object-cover object-c" src={image} />
                </ImageWrapper>
                {list.length > 3 && idx === 3 && (
                  <div class="size-full z-up-up bg-[#00000080] absolute left-0 right-0 top-0 bottom-0 flex items-center justify-center text-white font-bold text-8">
                    +
                    {list.length - 4}
                  </div>
                )}
              </MergeClass>
            ))
        }
      </div>
    )
  }
})
