/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref } from 'vue'
import { set } from 'lodash-es'
import { useVoiceoverRole } from './use-voiceover-role-store'
import { ElTable, ElTableColumn } from 'element-plus'
import { useRoute } from 'vue-router'

type VoiceOverPageOptions = {
  props: {}
}
export const VoiceOverPage = createComponent<VoiceOverPageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, onReset, Form, params, columns, getTimbreList } = useVoiceoverRole()
  const tableRef = ref<InstanceType<typeof ElTable>>()
  const tableHeight = ref(0)
  const route = useRoute()

  onMounted(() => {
    tableHeight.value = document.body.clientHeight - 300
    if (route.query.id) {
      params.value.series_resource_id = +route.query.id
      void onQuery()
    }
  })

  return () => (
    <x-timbre-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>角色-音色管理</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={() => onQuery()}
            onReset={() => {
              onReset()
            }}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['资源ID', 'series_resource_id', { type: 'text', placeholder: '请输入资源ID' }],
            ]}
          />
        ),
        table: () => (
          <ElTable
            ref={tableRef}
            v-loading={loading.value}
            scrollbar-always-on
            maxHeight={tableHeight.value}
            data={list.value || []}
          >
            {columns.value.map(col => {
              const { render, ...rest } = col
              return (
                <ElTableColumn
                  key={col.prop}
                  {...rest}
                  v-slots={render ? {
                    default: ({ row }: { row: any }) => render({ row }),
                  } : undefined}
                />
              )
            })}
          </ElTable>
        ),
      }}
      </NavFormTablePager>
    </x-timbre-page>
  )
})

export default VoiceOverPage
