<template>
  <van-action-sheet v-model:show="show" @close="onClose" class="custom-popup text-[#888 ] max-h-4/5 z-dialog" title="">
    <div class="custom-title py-4.5 sticky top-0 z-up flex w-full items-center justify-between px-3">
      <div class="flex items-center text-xs text-[#cccacb]">
        <p class="mr-3">
          {{ T.productPanel.thisEpisode.ref.value }} <span class="text-xm">
            <SvgIcon name="s-coins-fill" class="size-4 shrink-0" />{{
              episode }}
          </span>
        </p>
        <p>
          {{ T.productPanel.balance.ref.value }} <span class="text-xm">
            <SvgIcon name="s-coins-fill" class="mr-[2px] size-4 shrink-0" />{{
              myWallet }}
          </span>
        </p>
      </div>
      <SvgIcon name="ic_close_dark" class="size-4.5 shrink-0 text-[#999]" @click="onClickClose" />
    </div>
    <div class="content h-[calc(100%_-_7rem)] p-3 pb-[4.5rem] pt-0">
      <van-loading v-if="!productData.recharge_list" class="mt-17 text-center" color="#ff007a" size="38px" />
      <div v-else class="flex flex-wrap gap-3">
        <div v-for="item in productData.recharge_list" :key="item.product_id"
          :class="{ coinActive: productId === item.product_id }"
          class="relative w-[calc(50%-0.5rem)] flex-col overflow-hidden rounded-lg border border-solid border-[#434546] bg-[#1d1d1e] p-3 text-[#cccacb]"
          @click="() => {
            productId = item.product_id
            vipSelectedId = 0
            h5Track('pay_unlock', 'purchase', 'click', {
              product_id: item.product_id,
              series_id: route.params.series_id,
              video_id: route.params.episode_id,
              pay_channel: payChannel,
              pay_sub_channel: paySubChannel,
              currency: item.currency,
            });
          }">
          <p class="mb-1 flex items-center">
            <SvgIcon name="p-coins-fill" class="ml-[-2px] mr-1 size-5 shrink-0" />
            <span class="font-bold text-[#fdfbfc]">{{ item?.delivery_details?.quanity }}</span>
            <span v-if="item?.delivery_details?.bonus" class="ml-1 text-sm">+{{ item?.delivery_details?.bonus }}</span>
          </p>
          <p class="text-sm">
            {{ item?.currency_symbol }} {{ item?.discount_price / 100 }}
          </p>
          <div v-if="productId === item.product_id" class="coinActiveBg" />
          <div v-if="item.slogan" style="background: linear-gradient(90deg, #F418EF 0%, #FF2D3F 50%);"
            :class="[item.slogan?.indexOf('%') > -1 && 'font-bold']"
            class="slogan absolute right-0 top-0 flex items-center rounded-bl px-1 py-0.5 text-[10px] leading-3 text-white">
            <!-- <span v-if="item.slogan?.indexOf('%') > -1">+</span> -->
            {{ item.slogan }}
            <SvgIcon v-if="item.slogan?.indexOf('%') > -1" name="ic_up" class="size-3" />
          </div>
        </div>
        <!-- vip块 -->
        <div v-for="(item, k) in productData.membership" :key="item.id"
          class="relative flex w-full overflow-hidden rounded-lg border border-solid border-line-2 p-3 transition-all"
          :class="[
            productId === item.product_id ? 'border border-solid border-line-4' : '',
            k === 0 ? 'mt-1' : '',
          ]"
          :style="{ background: productId === item.product_id ? 'linear-gradient(90deg, #5C3F21 0%, #20160C 100%)' : 'linear-gradient(90deg, #352616 0%, #080808 100%)' }"
          @click="() => {
            productId = item.product_id
            vipSelectedId = 0
            if (selectedPayChannel === 'Paypal') {
              setDefaultPayChannel();
            }
            h5Track('pay_unlock', 'subscription', 'click', {
              product_id: item.product_id,
              series_id: route.query.series_id,
              video_id: route.query.video_id,
              pay_channel: payChannel,
              pay_sub_channel: paySubChannel,
              currency: item.currency,
              price: item.discount_price / 100,
            });
          }">
          <div class="desc flex flex-1 flex-col gap-1 ">
            <div class="title flex h-5 items-center gap-1 text-base font-bold text-text-12">
              <img :src="Crown" class="size-5">
              {{ item.title }}
            </div>
            <div class="subtitle text-xs font-normal text-text-2">
              {{ item.description || ' ' }}
            </div>
            <div class="tip text-[10px] text-text-8">
              {{ item.discount_desc }}
            </div>
          </div>
          <div class="flex flex-col items-end justify-center">
            <div class="price text-lg font-bold text-text-12">
              {{ item.currency_symbol }}{{ item.discount_price / 100 }}
            </div>
            <div class="origin-price text-xs text-text-3 line-through">
              {{ item.currency_symbol }}{{ item.price / 100 }}
            </div>
          </div>
          <div v-if="productId === item.product_id" class="coinActiveBg" />
        </div>
        <!-- 支付方式 -->
        <div class=" mt-1 w-full text-text-2">
          <div class="mb-3 text-[#CCCACB]">
            {{ PT.paymentPage.paymentMethod.ref }}
          </div>
          <div class="flex w-[calc(100%+0.75rem)] gap-3 overflow-x-auto overflow-y-hidden pb-0.5 pr-3">
            <div v-for="item in orderedProductList" :key="item.pay_channel" :class="getPayMethodClass(item)"
              class="relative flex h-[3.1875rem] w-[6.5rem] shrink-0 items-center justify-center overflow-hidden rounded-lg border border-solid border-line-2 bg-[var(--grey-13)] px-3"
              :style="selectedPayChannel === item.pay_channel && { background: 'var(--grey-13)', borderColor: '#fff8e6' }"
              @click="() => {
                selectedPayChannel = item.pay_channel
                payChannel = item.payment_channel
                paySubChannel = item.sub_payment_channel
                h5Track('pay_unlock', 'channel', 'click', {
                  series_id: route.params.series_id as string,
                  video_id: route.params.episode_id as string,
                  pay_channel: item.pay_channel,
                  pay_sub_channel: item.sub_payment_channel,
                  product_id: productId,
                  currency: selectedProduct?.currency,
                })
              }">
              <Image class="w-20" :src="item.icon" />
              <div v-if="selectedPayChannel === item.pay_channel" class="channelActiveBg" />
            </div>
          </div>
        </div>
      </div>
      <!-- 固定区域 -->
      <div v-if="productData.recharge_list"
        class="fixed bottom-[env(safe-area-inset-bottom)] left-0 z-up mt-1 w-full bg-[var(--black)] px-3 py-2 text-text-2">
        <div class="flex h-11 w-full items-center  justify-center  rounded-lg bg-brand-6 font-medium text-white" @click="() => {
          h5Track('pay_unlock', 'payment', 'click', {
            series_id: route.params.series_id as string,
            video_id: currentEpisodeId as string,
            pay_channel: payChannel,
            pay_sub_channel: paySubChannel,
            product_id: productId,
            currency: selectedProduct?.currency,
          })
          handlePay({
            from: curPath,
            series_id: route.params.series_id as string,
            video_id: currentEpisodeId as string,
            video_type: props.episodeType,
          })
        }">
          {{ PT.paymentPage.payNow.ref }} {{ selectedProduct?.currency_symbol }} {{ (selectedProduct?.discount_price
            || 0)
            / 100 }}
        </div>
      </div>
      <SvgIcon v-if="payLoading" name="payment/loading"
        class="fixed left-1/2 top-1/2 size-8 -translate-x-1/2 -translate-y-1/2 animate-spin" />
      <Popup v-model:show="showPop" :destroy-on-close="true" :close-on-click-overlay="true" position="bottom"
        :style="{ height: '220px' }" class="rounded-t-lg !bg-white">
        <PayThirdButton :pay-channel="payChannel" :pay-sub-channel="paySubChannel" :type="thirdType"
          :product-id="productId" :from="curPath" :video-type="props.episodeType" :currency="selectedProduct?.currency"
          @click-rule="gotoRules" />
      </Popup>
    </div>
  </van-action-sheet>
</template>

<script setup lang="ts">
import { SvgIcon } from '@skynet/ui';
import { Image, Popup, ActionSheet as vanActionSheet, Loading as vanLoading } from 'vant';
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { T as PT } from '../payment/payment-page.i18n';
import PayThirdButton from '../payment/payment-third-button.vue';
import { usePayment } from '../payment/use-payment';
import { useMyWallet } from '../wallet/use-my-wallet';
import { T } from './product-panel.i18n';
import { useSeriesItem } from './use-series-item';
import { h5Track } from '~/lib/h5-track';
import { Crown } from '~/modules/payment/images/images';
import { gotoRules } from '~/modules/common/common';

const route = useRoute()
const { wallet, fetchWallet } = useMyWallet()
const { productId, handlePay, thirdType, showPop, payChannel, getPayMethodClass,
  paySubChannel, selectedPayChannel, payLoading, productData, getProductList,
  orderedProductList, selectedProduct, setDefaultPayChannel } = usePayment()
const { currentEpisodeId } = useSeriesItem(route.params.series_id.toString())

const curPath = encodeURIComponent((window.location.pathname + window.location.search).slice(1))

const vipSelectedId = ref(0);
const myWallet = ref(0);

const onClose = () => {
  emit('cancel')
}

const onClickClose = () => {
  show.value = false;
}

const props = defineProps({
  productPanelVisible: {
    type: Boolean,
    required: false
  },
  episode: {
    type: Number,
    required: true
  },
  episodeType: {
    type: String,
    required: true
  }
});
const emit = defineEmits(['update:productPanelVisible', 'cancel']);

const show = ref(props.productPanelVisible);

const getWalletMy = async () => {
  myWallet.value = (wallet.value?.cash_balance ?? 0) + (wallet.value?.bonus_balance ?? 0)
}

watch(() => props.productPanelVisible, newVal => {
  show.value = newVal;
  if (newVal) {
    fetchWallet()
    const start_time = Date.now()
    getProductList().then(() => {
      h5Track('h5', 'performance', 'product_panel_after_fetch_product_list', {
        load_time: Date.now() - start_time,
      })
      // 补充product_id 和 currency, 所以要放在getProductList之后
      h5Track('pay_unlock', 'purchase', 'show', {
        series_id: route.params.series_id,
        video_id: currentEpisodeId.value,
        product_id: productId.value,
        currency: selectedProduct.value?.currency,
      })
      h5Track('pay_unlock', 'channel', 'show', {
        series_id: route.params.series_id as string,
        video_id: currentEpisodeId.value,
        pay_channel: payChannel.value,
        pay_sub_channel: paySubChannel.value,
        product_id: productId.value,
        currency: selectedProduct.value?.currency,
      })
    });

    getWalletMy();
  }
}, { immediate: true });

watch(show, newVal => {
  emit('update:productPanelVisible', newVal);
});
</script>

<style>
.custom-popup.van-popup {
  background: #0b080b;
  /* min-height: 20rem; */
}

.custom-title {
  background: #0b080b url(/modules/images/goods-bg.webp) no-repeat center top/ 100% 83px;
}

.van-action-sheet__close {
  color: #797b7d;
}
</style>

<style scoped>
.vipActive {
  background-image: linear-gradient(to right, #5b3f21, #1f160c);
  border-color: #fff8e6;
  transition: all 0.3s ease;
}

.coinActive {
  border-color: #fff8e6;
  transition: all 0.3s ease;
}

.coinActiveBg {
  width: 5rem;
  height: 14rem;
  left: 4.5rem;
  top: -3rem;
  transition: all 0.3s ease;
  position: absolute;
  transform-origin: top left;
  transform: rotate(30deg);
  background: linear-gradient(270deg,
      rgba(253, 215, 121, 0) 0%,
      rgba(253, 215, 121, 0.2) 50%,
      rgba(253, 215, 121, 0) 100%);
  animation: shine 2s ease-in-out infinite;
}

@keyframes shine {
  0% {
    left: -25%;
  }

  100% {
    left: 125%;
  }
}

.channelActiveBg {
  width: 5rem;
  height: 14rem;
  left: 2.5rem;
  top: -3rem;
  transition: all 0.3s ease;
  position: absolute;
  transform-origin: top left;
  transform: rotate(30deg);
  background: linear-gradient(270deg,
      rgba(253, 215, 121, 0) 0%,
      rgba(253, 215, 121, 0.2) 50%,
      rgba(253, 215, 121, 0) 100%);
  animation: shine 2s ease-in-out infinite;
}
</style>
