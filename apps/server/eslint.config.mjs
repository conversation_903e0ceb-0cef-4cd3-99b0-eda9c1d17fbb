// @ts-check
import withNuxt from './.nuxt/eslint.config.mjs'

export default withNuxt(
  {
    rules: {
      '@stylistic/comma-dangle': 'off',
      '@stylistic/semi': 'off',
      '@stylistic/array-bracket-spacing': ['error', 'never'],
      '@stylistic/arrow-parens': ['error', 'as-needed'],
      '@stylistic/jsx-one-expression-per-line': ['off'],
      '@stylistic/jsx-first-prop-new-line': 'off',
      '@stylistic/jsx-max-props-per-line': 'off',
      '@stylistic/quote-props': ['error', 'as-needed'],
      '@stylistic/max-statements-per-line': ['error', { max: 2 }],
      '@stylistic/jsx-closing-bracket-location': ['off'],
      '@stylistic/brace-style': ['error', '1tbs', { allowSingleLine: true }],
      '@stylistic/multiline-ternary': 'off',
      'import/newline-after-import': 'off',
    }
  }

).override('nuxt/typescript/rules', {
  rules: {
    'no-unused-vars': ['off', {
      vars: 'all',
      args: 'none',
      argsIgnorePattern: '^_',
      caughtErrors: 'none',
      ignoreRestSiblings: true,
    }],
    '@stylistic/multiline-ternary': 'off',
    '@typescript-eslint/ban-types': 'off',
    '@typescript-eslint/no-unsafe-argument': 'off',
    '@typescript-eslint/no-unsafe-assignment': 'off',
    '@typescript-eslint/no-unsafe-call': 'off',
    '@typescript-eslint/no-unsafe-return': 'off',
    '@typescript-eslint/no-unsafe-member-access': 'off',
    '@typescript-eslint/no-misused-promises': 'off',
    '@typescript-eslint/no-redundant-type-constituents': 'off',
    '@typescript-eslint/no-unnecessary-type-constraint': 'off',
    '@typescript-eslint/no-unused-vars': ['warn', {
      vars: 'all',
      args: 'none',
      caughtErrors: 'none',
      ignoreRestSiblings: true,
      argsIgnorePattern: '^_',
    }],

  }
})
