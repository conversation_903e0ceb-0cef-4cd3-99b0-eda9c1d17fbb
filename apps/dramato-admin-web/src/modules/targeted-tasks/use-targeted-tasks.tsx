/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetTaskList, apiSaveTask, apiSyncTaskList } from './targeted-tasks-api'
import { TargetedTasksForm } from './targeted-tasks-form'

export const useTasks = () => {
  return {
    Table,
    list,
    loading,
    search,
    onCreate,
    showCreateTaskDialog,
    hideCreateTaskDialog,
    task,
    syncTask,
  }
}

const Table = CreateTableOld<M.TargetedTasks.ListRequest.Item>()
const list = ref<M.TargetedTasks.ListRequest.Item[]>([])
const loading = ref<boolean>(false)

const defaultTask = {
  info_from: 1, //  1 海外 2 国内
  search_key: '',
  search_type: 1, // 1: 全部 2： 产品名称 7 短剧名字
  period: 1, // 1:全部 2:7天 3: 30天
  start_page: 1, // 默认为1
  comment: '',
}

const task = ref<M.TargetedTasks.SaveRequest.Task>(Object.assign({}, defaultTask))

const search = async () => {
  loading.value = true
  const res = await apiGetTaskList().finally(() => {
    loading.value = false
  })
  list.value = res.data?.task_list || []
}

const hideCreateTaskDialog = ref<any>(null)
const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0 w-[800px]'
const showCreateTaskDialog = () => {
  task.value = Object.assign({}, defaultTask)
  hideCreateTaskDialog.value = openDialog({
    title: '新建任务',
    body: () => <TargetedTasksForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px]',
  })
}

const onCreate = async () => {
  try {
    await apiSaveTask(task.value)
    showAlert('创建成功')
    void search()
    hideCreateTaskDialog.value && hideCreateTaskDialog.value()
  } catch (error: any) {
    showAlert(error.response.data.message || '创建失败')
  }
}

const syncTask = () => {
  void apiSyncTaskList()
    .then(() => {
      showAlert('同步任务成功')
      void search()
    }).catch ((error: any) => {
      showAlert(error.response.data.message || '同步任务失败')
    })
}
