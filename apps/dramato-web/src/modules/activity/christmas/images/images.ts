import { default as header } from './en/1.webp'
import { default as body499 } from './en/2-4.99.webp'
import { default as body699 } from './en/2-6.99.webp'
import { default as body999 } from './en/2-9.99.webp'
import { default as body1999 } from './en/2-19.99.webp'
import { default as body2499 } from './en/2-24.99.webp'
import { default as footer } from './en/3.webp'
import { default as esHeader } from './es/1.webp'
import { default as esBody499 } from './es/2-4.99.webp'
import { default as esBody699 } from './es/2-6.99.webp'
import { default as esBody999 } from './es/2-9.99.webp'
import { default as esBody1999 } from './es/2-19.99.webp'
import { default as esBody2499 } from './es/2-24.99.webp'
import { default as esFooter } from './es/3.webp'
import { default as ptHeader } from './pt/1.webp'
import { default as ptBody499 } from './pt/2-4.99.webp'
import { default as ptBody699 } from './pt/2-6.99.webp'
import { default as ptBody999 } from './pt/2-9.99.webp'
import { default as ptBody1999 } from './pt/2-19.99.webp'
import { default as ptBody2499 } from './pt/2-24.99.webp'
import { default as ptFooter } from './pt/3.webp'

export const christmasImages: Record<string, Record<string, string[]>> = {
  en: {
    499: [header, body499, footer],
    699: [header, body699, footer],
    999: [header, body999, footer],
    1999: [header, body1999, footer],
    2499: [header, body2499, footer],
  },
  es: {
    499: [esHeader, esBody499, esFooter],
    699: [esHeader, esBody699, esFooter],
    999: [esHeader, esBody999, esFooter],
    1999: [esHeader, esBody1999, esFooter],
    2499: [esHeader, esBody2499, esFooter],
  },
  pt: {
    499: [ptHeader, ptBody499, ptFooter],
    699: [ptHeader, ptBody699, ptFooter],
    999: [ptHeader, ptBody999, ptFooter],
    1999: [ptHeader, ptBody1999, ptFooter],
    2499: [ptHeader, ptBody2499, ptFooter],
  },
}
