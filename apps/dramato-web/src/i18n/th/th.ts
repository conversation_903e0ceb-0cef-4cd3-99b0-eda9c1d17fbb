export const th = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: 'ตัวเลือกยอดนิยม',
    },
  },
  myList: {
    watchHistory: `ประวัติการรับชม`,
    myList: 'รายการของฉัน',
    seeAll: 'ดูทั้งหมด',
    noData: 'ไม่มีผลลัพธ์ที่เกี่ยวข้อง',
  },
  // 分享页
  share: {
    watchNow: 'ดูตอนนี้',
    episodeDialog: {
      title: 'มาร่วมกับฉันและรับเหรียญพิเศษ!',
      description: 'ฉันติดละครเรื่องนี้แล้ว!',
      tips: 'คำแนะนำ',
      button: 'ชมละครเต็มเรื่อง',
      limit24: 'เวลาแบบจำกัด: 24h',
    },
    toast: {
      takeOffShelf: 'ไม่มีเนื้อหาที่พร้อมให้บริการ',
    },
  },
  'customer-service-center': {
    title: 'ศูนย์บริการลูกค้า',
    description: 'รายละเอียด',
    'description-placeholder': 'โปรดระบุรายละเอียดเพื่อช่วยให้เราเข้าใจปัญหา',
    'email-placeholder': 'กรอกอีเมลของคุณเพื่อรับการอัปเดตความคิดเห็น',
    'submit-feedback': 'ส่ง',
    'enter-feedback': 'ปัญหาอื่นๆ',
    'upload-pictures': 'อัปโหลดรูปภาพ',
    feedback: 'ความคิดเห็น',
    'problem-type': 'ประเภทปัญหา',
    'problem-type-placeholder': 'กรุณาเลือก',
    'payment-issue-placeholder': 'โปรดระบุหมายเลขประจำตัวของคุณและใบเสร็จการสั่งซื้อ: Google: https://pay.google.com/ (หมายเลขคำสั่งซื้อเริ่มต้นด้วย GPA...) iOS: App Store - Apple ID - ประวัติการซื้อ (หมายเลขคำสั่งซื้อ)',
    'content-issue-placeholder': 'โปรดระบุชื่อซีรีส์ จำนวนตอน และภาษาซับที่ขาดหายไป',
    'ads-issue-placeholder': 'โปรดระบุรุ่นอุปกรณ์ของคุณ ประเทศ และการตั้งค่าภาษา',
    'technical-issue-placeholder': 'โปรดระบุรุ่นอุปกรณ์ของคุณ ประเทศ และการตั้งค่าภาษา',
    'other-issue-placeholder': 'โปรดระบุรายละเอียดเพิ่มเติม รวมถึงรุ่นอุปกรณ์และประเทศของคุณ',
    'i-want-to-give-feedback': 'ฉันต้องการให้ข้อเสนอแนะ',
    'are-you-satisfied-with-this-reply': 'คุณพอใจกับคำตอบนี้หรือไม่?',
    unsatisfied: 'ไม่พอใจ',
    satisfied: 'พอใจ',
    myFeedback: 'ความคิดเห็นของฉัน',
    'reply-to-feedback': 'ตอบกลับข้อเสนอแนะ',
    'description-min-length': 'กรอกคำอธิบายปัญหา (อย่างน้อย 6 ตัวอักษร)',
    'problem-type-not-choose': 'กรุณาเลือกประเภท ข้อเสนอแนะ',
    'description-empty': 'ข้อมูลที่กรอกไม่ สามารถเว้นว่างได้',
  },
  terminate: {
    question: 'ฉันจะจัดการการสมัครสมาชิกของฉันได้อย่างไร',
    desc: 'คลิกปุ่มด้านล่างเพื่อเข้าสู่หน้าการจัดการสัญญา คุณสามารถจัดการการสมัครของคุณได้โดยคลิกปุ่มสลับสัญญา:',
    title: 'การจัดการสัญญา',
    expiration: 'ระยะเวลาหมดอายุ',
    manageDesc: 'ความสัมพันธ์ตามสัญญา',
  },
  toast: {
    'upload-failed': 'อัปโหลดไม่สำเร็จ โปรดอัปโหลดอีกครั้ง',
    'submit-success': 'ส่งสำเร็จ',
    'email-incorrect': 'รูปแบบอีเมลไม่ถูกต้อง',
  },
  not_found: {
    title: 'หน้าที่คุณต้องการไม่มีอยู่',
  },
  regulations: {
    howUseData: 'เราจะใช้ข้อมูลของคุณอย่างไร',
    content: `หากคุณเลือก "อนุญาต" ในคำขออนุญาตความโปร่งใสในการติดตามแอป เราจะใช้ข้อมูลและข้อมูลของคุณเพื่อปรับปรุงบริการของเราและมอบประสบการณ์ที่ดีที่สุดให้กับคุณ มิฉะนั้นเราจะรวบรวมข้อมูลเท่านั้นและจะไม่ดำเนินกิจกรรมการติดตามใด ๆ

หากคุณยินยอมให้ DramaWave และพันธมิตรและผู้ให้บริการของเราใช้ข้อมูลจากอุปกรณ์ของคุณเพื่อวิเคราะห์วิธีที่คุณใช้แอปพลิเคชัน ปรับแต่งคำแนะนำเนื้อหา และให้บริการที่เกี่ยวข้องกับข้อมูลเพิ่มเติม โปรดคลิก "ยอมรับและดำเนินการต่อ" เพื่อดำเนินการต่อ`,
    acceptAll: 'ยอมรับทั้งหมด',
    dataSettings: 'การตั้งค่าข้อมูล',
    next: 'ถัดไป',
    cancel: 'ยกเลิก',
    aboutYourPrivacy: 'เกี่ยวกับความเป็นส่วนตัวของคุณ',
    personalDataName: 'ข้อมูลส่วนบุคคล',
    personalDataDescription: 'เราใช้ข้อมูลนี้เพื่อจดจำตัวเลือกของคุณและให้คำแนะนำโฆษณาและเนื้อหาที่ปรับให้เหมาะสม การแจ้งเตือนและการเตือนความจำ ฯลฯ',
    advertisementDataName: 'ข้อมูลโฆษณา',
    advertisementDataDescription: 'คุกกี้ ตัวระบุอุปกรณ์ของคุณ หรือข้อมูลอื่น ๆ อาจถูกจัดเก็บหรือเข้าถึงได้บนอุปกรณ์ของคุณเพื่อวัตถุประสงค์ในการโฆษณาออนไลน์ ข้อมูลผู้ใช้อาจถูกแชร์กับพันธมิตรโฆษณาของเราด้วย',
    behaviorsDataName: 'ข้อมูลพฤติกรรม',
    behaviorsDataDescription: 'ข้อมูลที่เกี่ยวข้องกับการวิเคราะห์ เช่น คุกกี้หรือตัวระบุอุปกรณ์ อาจถูกจัดเก็บเพื่อวัดและวิเคราะห์การใช้งาน พฤติกรรม และประสิทธิภาพการโฆษณา',
    privacyAndTerms: 'นโยบายความเป็นส่วนตัวและข้อกำหนดการให้บริการ',
    referToPrivacy: 'โปรดดูนโยบายความเป็นส่วนตัว',
    referToTerms: 'โปรดดูข้อกำหนดการให้บริการ',
    agreeAndSave: 'ยอมรับและบันทึก',
  },
  coins: {
    earnings: {
      title: 'รายได้ของฉัน',
      total: 'เพชรรวม',
      exchange: 'แลกเปลี่ยน',
      earningsHistory: 'ประวัติรายได้',
      exchangeHistory: 'ประวัติการแลก',
      noRecords: 'ไม่มีบันทึก',
      last50earnings: 'แสดงเฉพาะ 50 รายการรายได้ล่าสุด',
      last50exchanges: 'แสดงเฉพาะ 50 รายการแลกเปลี่ยนล่าสุด',
      benefits: 'สิทธิประโยชน์',
      gotIt: 'รับทราบ',
    },
    exchange: {
      title: 'โซนแลกเปลี่ยน',
      notOpen: 'ยังไม่เปิดให้บริการ กรุณารอติดตาม',
      receiveBtn: 'รับอย่างมีความสุข',
      earnCoins: 'รับเพชร',
      redeemRequiredXCoins: 'แลกรางวัลโดยใช้เพชร {X}',
      exchangeSuccess: 'แลกเปลี่ยนสำเร็จ',
      cardId: 'รหัสไอดี',
      exchangeFailed: 'การแลกล้มเหลว',
      transferFailedInsufficientStock: 'สินค้าหมดแล้ว กรุณามาใหม่พรุ่งนี้แต่เช้า.',
      gotItBtn: 'เข้าใจแล้ว',
      transferFailedRiskControl: 'บัญชีมีปัญหา, ไม่สามารถแลกได้ชั่วคราว.',
      tryAgainBtn: 'ลองอีกครั้งภายหลัง',
      transferConfirm: 'ยืนยันการโอน',
      paypalAccountEmail: 'อีเมลบัญชี Paypal',
      confirmPaypalAccountEmail: 'ยืนยันอีเมลบัญชี Paypal',
      transferX: 'โอน {X}',
      transferXConfirm: 'ยืนยันการโอน {X}',
      transferToYourAccountX: 'โอนเข้าบัญชีของคุณ {X}',
      confirmBtn: 'ยืนยัน',
      redeemFailed: 'การถอนเงินล้มเหลว',
      redeemFailedContent: 'เกิดข้อผิดพลาด  กรุณาลองใหม่อีกครั้งในภายหลัง',
      transferSuccessTitle: 'เรากำลังส่งเงิน!',
      transferSuccessContent: 'เงินของคุณจะมาถึงภายใน 5 นาทีถึง 72 ชั่วโมง',
      copySuccess: 'คัดลอกสำเร็จ',
      enterEmailError: 'กรุณากรอกอีเมลที่ถูกต้อง',
      enterEmailValidError: 'ข้อมูลไม่ถูกต้อง กรุณากรอกใหม่',
      emailNotMatchError: 'ที่อยู่อีเมลไม่ตรงกัน กรุณากรอกใหม่',
    },
  },
  meal_check_in: {
    sleeping_link: 'รับเพชรขณะนอนหลับ',
    rules_title: 'กฎกิจกรรม',
    benefits_title: 'Benefits',
    rules_1: '1. ทุกวันมีสี่ช่วงเวลาที่คุณสามารถเช็คอินมื้ออาหารของคุณได้: อาหารเช้า: 07:00-10:00; มื้อกลางวัน: 11:00-14:00; มื้อเย็น: 17:00-20:00; ขนมยามดึก: 21:00-24:00.',
    rules_2: '2. หากคุณพลาดเวลาเช็คอินที่กำหนดไว้ คุณสามารถดูวิดีโอเพื่อขอรับรางวัลได้.',
    rules_3: '3. รางวัลจะรีเซ็ตทุกวันเวลาเที่ยงคืน อย่าลืมขอรับรางวัลนะคะ.',
  },
  sleep_check_in: {
    earn_link: 'บันทึกมื้ออาหารเพื่อรับ',
    benefits_title: 'Benefits',
    rules_1: '1. ทุกคืนตั้งแต่เวลา 19:00 ถึง 24:00 สามารถเปิดโหมด \'นอนทำเงิน\' ได้',
    rules_2: '2. หลังจากเปิดโหมดแล้วในวันถัดไป เมื่อคุณนอนครบ 8 ชั่วโมงแล้ว คุณสามารถรับรางวัลจากการนอนระหว่างเวลา 08:00 ถึง 12:00',
    rules_3: '3. หากคุณพลาดเวลาในการรับรางวัล คุณสามารถดูวิดีโอระหว่างเวลา 12:00 ถึง 19:00 ของวันถัดไปเพื่อรับรางวัล',
  },
  bind_email: {
    title: 'กรุณากรอก E-mail ของคุณ',
    list_1: 'ผูกE-mail ของคุณ เพื่อรับเหรียญ รางวัลพิเศษ',
    list_2: 'รับการแจ้งเตือนสำหรับกิจกรรมที่ จะเกิดขึ้นและ ข้อเสนอพิเศษ',
    list_3: 'เป็นคนแรกที่รู้ เกี่ยวกับตอนใหม่',
    email_placeholder: 'กรุณาใส่E-mail ของคุณ',
    verify: 'ยืนยัน',
    email_error: 'กรุณากรอกที่อยู่ อีเมลที่ถูกต้อง',
    verify_progress: 'กำลังตรวจสอบอีเมล',
    verify_success: 'รับสิทธิ์สำเร็จ',
    re_enter: 'กรอกใหม่อีกครั้ง',
    email_repeat: 'อีเมลนี้เคยเข้าร่วมกิจกรรมแล้ว!',
  },
  infinityScroll: {
    end: 'สิ้นสุด',
    loading: 'กำลังโหลด...',
  },
}
