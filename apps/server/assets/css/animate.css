:root {
  --slide-duration: 0.3s;
  --skeleton-duration: 1.4s;
}
@keyframes blink {
    0% { transform: scale(1.2);}
    50% { transform: scale(1);}
    100% { transform: scale(1.1);}
}

/* 往上滑 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--slide-duration) linear;
}

.slide-up-enter-from{
  transform: translateY(100vh);
}
.slide-up-leave-to {
  transform: translateY(-100vh);
}

/* 往下滑 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all var(--slide-duration) linear;
}

.slide-down-enter-from {
  transform: translateY(-100vh);
}

.slide-down-leave-to {
  transform: translateY(100vh);
}

/* .skeleton-animation {
    background:  linear-gradient(153deg, #1D1D1E 0%, #2E2F30 98.79%);
    animation-name: Skeleton;
    animation-duration: var(--skeleton-duration);
    animation-timing-function: ease;
    animation-iteration-count: infinite;
}  */
.skeleton-animation {
  background: linear-gradient(
    153deg,
    #1D1D1E 0%,
    #2E2F30 98.79%
  );
  background-size: 200% 200%;
  animation: subtle-wave 2s ease infinite;
}
@keyframes subtle-wave {
  0% {
    background-position: 0% 50%;
    opacity: 0.5;
  }
  50% {
    background-position: 100% 50%;
    opacity: 1;
  }
  100% {
    background-position: 0% 50%;
    opacity: 0.5;
  }
}