/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, Icon, SvgIcon } from '@skynet/ui'
import { ElInput, ElTable, ElTableColumn } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { ref, watch } from 'vue'

type CharacterTableOptions = {
  props: {
    list: M.ITerminology[]
    loading: boolean
  }
  emits: {
    select: (row: M.ITerminology) => void
    save: (list: M.ITerminology[]) => void
    insertRole: (row: M.ITerminology) => void
    fixTop: (row: M.ITerminology) => void
  }
}
export const CharacterTable = createComponent<CharacterTableOptions>({
  props: {
    list: [],
    loading: false,
  },
  emits: {
    select: (row: M.ITerminology) => {},
    save: (list: M.ITerminology[]) => {},
    insertRole: (row: M.ITerminology) => {},
    fixTop: (row: M.ITerminology) => {},
  },
}, (props, { emit }) => {
  const list = ref<M.ITerminology[]>([])

  watch(() => props.list, newVal => {
    list.value = cloneDeep(newVal)
  }, {
    immediate: true,
  })

  return () => (
    <div class="space-y-4">
      <ElTable
        scrollbar-always-on
        stripe
        style={{ width: '100%', height: '500px' }}
        data={list.value || []}
      >
        <ElTableColumn
          prop="name"
          label=""
          minWidth={119}
          v-slots={{
            default: ({ row }: { row: M.ITerminology }) => {
              const ts = Date.now()
              return (
                <div class="flex justify-between items-center">
                  <Button class="btn btn-xs btn-primary" onClick={() => {
                    emit('insertRole', row)
                  }}>选中角色</Button>
                  <div key={`${row.character_id}_${ts}_fixed`}>
                    {
                      row.isFixed ? (
                        <SvgIcon name="ic_cancel_fix_top" class="size-6 cursor-pointer" onClick={() => {
                          emit('fixTop', row)
                        }} />
                      )
                        : (
                            <SvgIcon name="ic_fix_top" class="size-6 cursor-pointer" onClick={() => {
                              emit('fixTop', row)
                            }} />
                          )
                    }
                  </div>
                </div>
              )
            },
          }}
        />
        <ElTableColumn
          prop="name"
          label="角色名称"
          minWidth={100}
          v-slots={{
            default: ({ row }: { row: M.ITerminology }) => {
              return (
                <ElInput modelValue={row.name} class="flex-1" onUpdate:modelValue={e => {
                  row.name = e
                  row.update = true
                }} />
              )
            },
          }}
        />
        <ElTableColumn
          prop="official_name"
          label="官方名"
          minWidth={110}
          v-slots={{
            default: ({ row }: { row: any }) => (
              <ElInput modelValue={row.official_name} onUpdate:modelValue={e => {
                row.official_name = e
                row.update = true
              }} />
            ),
          }}
        />
        <ElTableColumn
          prop="opr"
          label="操作"
          width={60}
          v-slots={{
            default: ({ row, $index }: { row: any, $index: number }) => {
              return !row.character_id ? (
                <SvgIcon class="size-[20px] cursor-pointer" name="ic_del" onClick={() => {
                  list.value.splice($index, 1)
                }} />
              ) : null
            },
          }}
        />

      </ElTable>
      <div class="flex justify-end">
        <Button class="btn btn-link btn-xs" onClick={() => {
          list.value.push({
            name: '',
            type: 1,
            official_name: '',
          })
        }}>新增</Button>
        <Button class="btn btn-primary btn-xs" disabled={props.loading} onClick={() => {
          emit('save', list.value)
        }}>
          {props.loading ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
          保存角色
        </Button>
      </div>
    </div>
  )
})

export default CharacterTable
