/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, Icon, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { useRank } from './use-rank'
import { Uploader } from '../common/uploader/uploader'
import { onMounted, ref, watch } from 'vue'
import { apiGetEpisodeSeriesCount } from '../episode-tags/episode-tags-api'
import { useEpisodeTags } from '../episode-tags/use-episode-tags'
import { apiGetEpisodeData } from '../episode/episode-api'

type RankFormOptions = {
  props: {}
}

export const RankForm = createComponent<RankFormOptions>({
  props: {},
}, props => {
  const {
    currentRank,
    closeRankDialog,
    onCreate,
    onEdit,
    isUpdating,
    params,
  } = useRank()

  const { tags } = useEpisodeTags()

  const Form = CreateForm<M.Rank.CreateRank>()
  const formRules = z.object({
    priority: z.number().min(1, '请填写实验编号'),
    name: z.string().min(1, '请输入'),
    theme_bg_color: z.string().min(1, '请输入'),
    theme_text_color: z.string().min(1, '请输入'),
    theme_img: z.string().min(1, '请上传'),
    type: z.number().min(1, '请选择'),
    size: z.number().min(1, '请选择'),
    // sampling_type: z.number().min(1, '请选择'),
    // sampling_hots: z.string().min(1, '请选择'),
    tag_id_list: z.array(z.string()).min(1, '请填写'),
    auto_type: z.number().min(1, '请选择'),
    series_info: z.array(z.object({
      series_id: z.string().min(1),
    })).min(1),
  })

  const { error, validateAll } = useValidator(currentRank, formRules)

  watch(
    () => currentRank.value.tag_id_list,
    () => {
      if (!currentRank.value.tag_id_list || params.value.type !== 1) {
        currentRank.value.series_count = 0
        return
      }

      void apiGetEpisodeSeriesCount({
        label_ids: currentRank.value.tag_id_list.join(','),
        app_id: params.value.app_id,
        language: params.value.language_version || '',
      }).then(res => {
        currentRank.value.series_count = res.data?.count || 0
      })
    },
    {
      deep: true,
      immediate: true,
    },
  )

  onMounted(() => {
    if (!currentRank.value.tag_id_list || params.value.type !== 1) {
      currentRank.value.series_count = 0
      return
    }

    void apiGetEpisodeSeriesCount({
      label_ids: currentRank.value.tag_id_list.join(','),
      app_id: params.value.app_id,
      language: params.value.language_version || '',
    }).then(res => {
      currentRank.value.series_count = res.data?.count || 0
    })
  })

  console.log('tags.value', tags.value)

  const currentVersion = ref<M.Rank.SeriesInfo>({})

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentRank.value || {}, path, value)
          }}
          items={[
            [
              requiredLabel('优先级'),
              'priority',
              {
                type: 'number',
                min: 1,
                placeholder: '请输入榜单顺序',
              },
              {
                transform: transformNumber,
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('榜单名称'),
              'name',
              {
                type: 'textarea',
                placeholder: '请输入榜单名称',
                rows: 3,
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('标签背景色值 (点击色块选择色值)'),
              'theme_bg_color',
              {
                type: 'color',
                placeholder: '请输入标签背景色值',
                hasAlpha: true,
              },
              {
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('标签文字色值 (点击色块选择色值)'),
              'theme_text_color',
              {
                type: 'color',
                placeholder: '请输入标签文字色值',
                hasAlpha: true,
              },
              {
                class: mc('col-span-1'),
              },
            ],
            {
              label: requiredLabel('标签背景图片'),
              path: 'cover',
              input: {
                type: 'custom',
                render: () => (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 10}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      onUploadSuccess={d => {
                        currentRank.value.theme_img = 'https://static-v1.mydramawave.com/banner/cover/' + d.temp_path
                      }}
                      isImage={true}
                      uploadUrl="/banner/upload/cover"
                    >
                      {
                        currentRank.value.theme_img
                          ? <img src={currentRank.value.theme_img.includes('https://') ? currentRank.value.theme_img : 'https://static-v1.mydramawave.com/banner/cover/' + currentRank.value.theme_img} class="size-full object-cover" />
                          : <span class="size-full flex items-center justify-center">上传</span>
                      }
                    </Uploader>
                    <x-upload-cover-tip class="text-gray-600 text-sm">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>
                  </x-upload-cover>

                ),
              },
              hint: '横图适用1.0.90版本及以上',
              class: mc('col-span-1'),
            },
            [
              requiredLabel('细分榜单类型'),
              'type',
              {
                type: 'radio',
                options: [{ label: '人工', value: 1 }, { label: '自动', value: 2 }],
              },
              {
                // transform: transformNumber,
                class: mc('col-span-1'),
              },
            ],
            [
              requiredLabel('榜单长度'),
              'size',
              {
                type: 'number',
                min: 1,
                placeholder: '请输入榜单长度',
              },
              {
                class: mc('col-span-1', currentRank.value.type === 2 ? '' : 'hidden'),
                transform: transformNumber,
              },
            ],
            {
              label: requiredLabel('内容标签'),
              path: 'tag_id_list',
              input: {
                type: 'multi-select',
                search: true,
                popoverWrapperClass: 'z-popover-in-dialog',
                options: tags.value.map(tag => ({
                  value: tag.label_id + '',
                  label: tag.content,
                })),
              },
              class: mc('col-span-1', params.value.type === 1 && currentRank.value.type === 2 ? '' : 'hidden'),
            },
            [
              requiredLabel('自动榜单选择'),
              'auto_type',
              {
                type: 'radio',
                options: [{ label: 'Most trending', value: 1 }, { label: 'Top Searched', value: 2 }, { label: 'New Releases', value: 3 }],
              },
              {
                // transform: transformNumber,
                class: mc('col-span-1', params.value.type === 2 && currentRank.value.type === 2 ? '' : 'hidden'),
              },
            ],
            [
              '覆盖剧集',
              'series_count',
              {
                type: 'text',
                disabled: true,
              },
              {
                class: mc('col-span-1', currentRank.value.type === 2 ? '' : 'hidden'),
              },
            ],
            [
              '榜单明细',
              'series_info',
              {
                type: 'custom',
                render: () => {
                  return (
                    <x-ab-test-group class="flex flex-col gap-y-2">
                      {/* <x-tips class="text-[14px] text-gray-500">请按照A/B testing平台对应填写</x-tips> */}
                      {
                        currentRank.value.series_info && currentRank.value.series_info.length > 0
                          ? currentRank.value.series_info.map((item, index) => {
                            return (
                              <x-ab-test-version class="p-2 border rounded-md flex flex-col gap-y-2 text-[14px]" key={index} index={index}>
                                <x-title class="flex flex-row items-center justify-between">
                                  榜单{index + 1}
                                  <Icon name="ant-design:delete-filled" onClick={() => {
                                    if (!currentRank.value.series_info || currentRank.value.series_info.length <= 0) {
                                      return
                                    }
                                    currentRank.value.series_info = currentRank.value.series_info.filter((_, i) => i !== index)
                                  }}
                                  />
                                </x-title>
                                <x-ab-test-item>
                                  <label>剧集ID</label>
                                  <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                                    <input
                                      type="text"
                                      class={mc('grow')}
                                      value={item.series_id}
                                      onFocus={() => {
                                        currentVersion.value = item
                                      }}
                                      onInput={(e: Event) => {
                                        currentVersion.value.series_id = (e.target as HTMLInputElement).value || ''
                                      }}
                                      onKeydown={(e: KeyboardEvent) => {
                                        if (e.key !== 'Enter') {
                                          return
                                        }
                                        if (!currentRank.value.series_info || !currentRank.value.series_info[index]) {
                                          return
                                        }
                                        currentRank.value.series_info[index].series_id = currentVersion.value.series_id
                                        void apiGetEpisodeData({ series_key: currentVersion.value.series_id || '' }).then(rs => {
                                          currentRank.value.series_info[index].name = rs.data?.title || ''
                                        })
                                      }}
                                      onBlur={() => {
                                        if (!currentRank.value.series_info || !currentRank.value.series_info[index]) {
                                          return
                                        }
                                        currentRank.value.series_info[index].series_id = currentVersion.value.series_id
                                        void apiGetEpisodeData({ series_key: currentVersion.value.series_id || '' }).then(rs => {
                                          currentRank.value.series_info[index].name = rs.data?.title || ''
                                        })
                                      }}
                                    />
                                  </label>
                                </x-ab-test-item>
                                <x-ab-test-item>
                                  <label>剧集名称</label>
                                  <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                                    <input
                                      type="text"
                                      class={mc('grow')}
                                      disabled
                                      value={item.name}
                                    />
                                  </label>
                                </x-ab-test-item>
                                <x-ab-test-item>
                                  <label>热度值🔥</label>
                                  <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                                    <input
                                      type="text"
                                      class={mc('grow')}
                                      value={item.hot}
                                      onFocus={() => {
                                        currentVersion.value = item
                                      }}
                                      onInput={(e: Event) => {
                                        currentVersion.value.hot = (e.target as HTMLInputElement).value || ''
                                      }}
                                      onKeydown={(e: KeyboardEvent) => {
                                        if (e.key !== 'Enter') {
                                          return
                                        }
                                        if (currentVersion.value.hot === item.hot) {
                                          return
                                        }
                                        if (!currentRank.value.series_info || !currentRank.value.series_info[index]) {
                                          return
                                        }
                                        currentRank.value.series_info[index].hot = currentVersion.value.hot
                                      }}
                                      onBlur={() => {
                                        if (currentVersion.value.hot === item.hot) {
                                          return
                                        }
                                        if (!currentRank.value.series_info || !currentRank.value.series_info[index]) {
                                          return
                                        }
                                        currentRank.value.series_info[index].hot = currentVersion.value.hot
                                      }}
                                    />
                                  </label>
                                </x-ab-test-item>
                              </x-ab-test-version>
                            )
                          })
                          : null
                      }
                      <Button class="btn btn-outline btn-sm  w-[120px]" onClick={() => {
                        if (!currentRank.value.series_info) {
                          currentRank.value.series_info = []
                        }
                        currentRank.value.series_info.push({})
                      }}
                      >新增榜单明细
                      </Button>
                    </x-ab-test-group>
                  )
                },
              },
              {
                class: mc('col-span-1', currentRank.value.type === 1 ? '' : 'hidden'),
              },
            ],
          ]}
          data={currentRank.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeRankDialog.value}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const next = () => !currentRank.value?.id ? void onCreate() : void onEdit()
            try {
              const exclude: string[] = []

              if (!(params.value.type === 2 && currentRank.value.type === 2)) {
                exclude.push('auto_type')
              }

              if (!(params.value.type === 1 && currentRank.value.type === 2)) {
                exclude.push('tag_id_list')
              }

              if (currentRank.value.type !== 2) {
                exclude.push('size')
              }

              if (currentRank.value.type !== 1) {
                exclude.push('series_info')
              }

              if (!validateAll({ exclude })) {
                console.log('err', error)

                return
              }

              next()
            } catch (error) {
              console.log('error', error)
            }
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
