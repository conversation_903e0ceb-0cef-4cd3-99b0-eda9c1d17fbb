import { ClassName, createComponent, mergeClass } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import imageLoading from './source/image-loading.png'
import defaultAvatar from './source/ic_person.png'
import { useEventListener } from '@vueuse/core'

type ImageOptions = {
  props: {
    src?: string
    width?: number | string
    height?: number | string
    class?: ClassName
    isAvatar?: boolean
  }
}

export const Image = createComponent<ImageOptions>({
  props: {
    src: '',
    width: '',
    height: '',
    class: '',
    isAvatar: false,
  },
}, props => {
  const imgRef = ref<HTMLImageElement | null>()
  onMounted(() => {
    if (imgRef.value) {
      useEventListener(imgRef.value, 'error', () => {
        imgRef.value && (imgRef.value.src = props.isAvatar ? defaultAvatar : imageLoading) && (imgRef.value.style.border = 'none')
      })
      useEventListener(imgRef.value, 'load', function () {
        imgRef.value && (imgRef.value.style.opacity = '1') // 显示图片
      })
    }
  })
  return () => (
    <img ref={imgRef} src={props.src} class={mergeClass('pointer-events-none !bg-[--fill-2]', props.class)} width={props.width} height={props.height} />
  )
})

export default Image
