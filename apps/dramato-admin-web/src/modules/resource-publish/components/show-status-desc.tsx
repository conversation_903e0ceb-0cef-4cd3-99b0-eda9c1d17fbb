import { createComponent } from '@skynet/shared'
import { Tooltip, DateTime } from '@skynet/ui'
import { watch, ref } from 'vue'
import { useImagePreviewStore } from '@skynet/ui/image/use-preview-store'

type ShowStatusDescOptions = {
  props: {
    items: M.IResourceAuditProcessItem[]
    pageType: number
  }
}
export const ShowStatusDesc = createComponent<ShowStatusDescOptions>({
  props: {
    items: [{
      page_type: 0,
      status: -1,
      reject_reason: '',
      auditor: '',
      updated: 0,
      reject_image_list: [],
    }],
    pageType: 0,
  },
}, props => {
  const renderStatus = ref()
  const { showImagePreviewDialog } = useImagePreviewStore()
  watch(() => props.items, () => {
    renderStatus.value = getRenderStatus()
  }, {
    deep: true,
  })

  const getRenderStatus = () => {
    const descObj = props.items.find(item => item.reject_from_page === props.pageType) as M.IResourceAuditProcessItem
    const currentData = props.items.find(item => item.page_type === props.pageType) as M.IResourceAuditProcessItem
    if (!currentData) return '-'
    const status = currentData.status
    const options = [
      {
        value: 0,
        label: '待审核',
      },
      {
        value: 1,
        label: '审核驳回',
      },
      {
        value: 2,
        label: '审核通过',
      },
    ]
    const label = options.find(item => item.value === status)?.label || '无'
    const color = {
      0: 'badge-primary',
      1: 'text-red-500',
      2: 'text-green-500',
    }[status || 0] || ''

    if (status === 1) {
      return (
        <div class="line-clamp-1 flex items-center gap-x-4 overflow-hidden pb-4 text-sm">
          <div class="badge  badge-outline text-red-500">审核驳回</div>
          {currentData.auditor ? <span>操作人：{currentData.auditor} <DateTime value={currentData.updated * 1000} /></span> : null}
          { currentData.reject_reason
            ? (
                <Tooltip popContent={() => currentData.reject_reason}>
                  <span>{currentData.reject_reason}</span>
                </Tooltip>
              )
            : null }
          <div class="flex flex-wrap gap-x-2">
            {
              currentData.reject_image_list?.map(image => (
                <img class="size-[30px] cursor-pointer object-cover" src={image} alt="" onClick={() => {
                  showImagePreviewDialog({
                    imageList: currentData.reject_image_list?.map(image => ({
                      src: image,
                      width: 2000,
                    })) || [],
                    canEscClose: true,
                  })
                }} />
              ))
            }
          </div>
        </div>
      )
    } else if (status === 0) {
      return (
        <div class="mb-4 line-clamp-1 flex items-center space-x-4 overflow-hidden">
          <div class={`badge badge-outline ${color}`}>{label}</div>
          { descObj?.reject_reason ? <div>驳回详细：{descObj.reject_reason}</div> : null }
        </div>
      )
    } else {
      return <div class={`badge badge-outline mb-4 ${color}`}>{label}</div>
    }
  }

  return () => renderStatus.value
})

export default ShowStatusDesc
