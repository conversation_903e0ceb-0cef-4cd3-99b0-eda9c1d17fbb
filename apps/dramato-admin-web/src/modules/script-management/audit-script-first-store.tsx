/* eslint-disable @typescript-eslint/no-explicit-any */
import { openDialog, showSuccessToast, showFailToast, Icon, Button } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiUploadContract, apiGetAuditFirstDetail, apiUpdateAuditFirst, apiGetAuditFirstMajorDetail, apiUpdateAuditFirstMajor, apiGetEditorList } from './audit-script-api'
import { requiredLabel } from 'src/lib/required-label'
import { mc } from '@skynet/shared'
import { ElCollapse, ElCollapseItem } from 'element-plus'
import { openFile } from './util'
import { PreviewFile } from './components/preview-file'
import { cloneDeep } from 'lodash-es'
import { defaultTemplateStep1 } from './constant'
import { Step1ScoreTable } from './components/step1-score-table'
import { labels } from './audit-script-store'
import { useMenu } from 'src/modules/menu/use-menu'
import { Uploader } from './uploader/uploader'

export const useAuditFirstStore = () => {
  return {
    statusMap,
    onFirstResponsibleEditorEvaluate,
    onFirstChiefEditorEvaluate,
    uploadContract
  }
}

const btnLoading = ref(false)
const { getUserRoles } = useMenu()

const detailData = ref()
const playItem = ref<M.IScriptItemDetail>()
const majorDetailData = ref<Api.AuditScriptFirstMajorDetail.Response.majorAuditDetail>()
const editorList = ref<M.IAuditUserList[]>([])
const detailLoading = ref(false)
const currentRoles = ref<number[]>(getUserRoles())
const scoreData = ref<M.IScoreStep1[]>([])
const PASS_POINTS = 6

const statusMap: Record<number, { label: string, color: string }> = {
  1: {
    label: '待审核',
    color: 'default',
  },
  2: {
    label: '审核拒绝',
    color: 'error',
  },
  3: {
    label: '放弃',
    color: 'ghost',
  },
  4: {
    label: '审核通过',
    color: 'primary',
  },
}

const totalScore = computed(() => {
  return scoreData.value.reduce((sum, item) => sum + (item.score || 0), 0)
})

const rating = computed(() => {
  return totalScore.value >= 6 ? '通过' : '不通过'
})

const isAuditable = computed(() => {
  return playItem.value?.audit_status === 1
})

const auditCommit = async (data: Api.AuditScriptStep1.Request.Params, cb?: () => void) => {
  btnLoading.value = true
  try {
    await apiUpdateAuditFirst(data)
    cb && cb()
    showSuccessToast('操作成功')
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    btnLoading.value = false
  }
}

const auditCommitMajor = async (data: Api.AuditScriptStep1.Request.Params, cb?: () => void) => {
  btnLoading.value = true
  try {
    await apiUpdateAuditFirstMajor(data)
    cb && cb()
    showSuccessToast('操作成功')
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    btnLoading.value = false
  }
}

const getAuditDetail = async (play_key: string) => {
  detailLoading.value = true
  detailData.value = {}
  try {
    const res = await apiGetAuditFirstDetail({ play_key })
    detailData.value = res.data?.detail
    playItem.value = res.data?.play_item
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    detailLoading.value = false
  }
}

const getAuditMajorDetail = async (play_key: string) => {
  detailLoading.value = true
  majorDetailData.value = {
    list: [],
    main_detail: {
      audit_user: '',
      status: 0,
      principal: '',
      rejected_reason: '',
    },
    cover: '',
    play_path: '',
  }
  try {
    const res = await apiGetAuditFirstMajorDetail({ play_key })
    majorDetailData.value = res.data
    playItem.value = res.data?.play_item
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    detailLoading.value = false
  }
}

const getEditorList = async () => {
  const res = await apiGetEditorList()
  editorList.value = res.data?.list || []
}

// 责编评估列表
const editorEvaluateList = (list: Api.AuditScriptFirstMajorDetail.Response.IScriptEvaluate[]) => {
  return list.map(item => {
    return (
      <div class="mb-2 space-y-2 rounded-lg bg-gray-100 p-2">
        <div>评估人：{item.audit_user}</div>
        <div>评估结果：
          <span class={`badge- badge${statusMap[item.status]?.color}`}>{statusMap[item.status]?.label}</span>
        </div>
        {
          item.rejected_reason
            ? (
                <div class="flex flex-row items-start space-x-4">
                  <div>备注:</div>
                  <div class="flex-1">{item.rejected_reason}</div>
                </div>
              )
            : null
        }
      </div>
    )
  })
}

// 主编评估
const onFirstChiefEditorEvaluate = ({ play_key, title }: { play_key: string, title: string }, hasEvaluateNum: number, cb?: () => void) => {
  const principal = ref('')
  openDialog({
    title: '主编评估',
    mainClass: 'pb-0 px-5',
    customClass: '!w-[870px]',
    body: () => (
      <x-audit-confirm-dialog v-loading={detailLoading.value} class="flex flex-col gap-y-[25px]">
        <div class="flex flex-row space-x-4">
          <div class="w-[600px] space-y-4">
            <PreviewFile customClass="w-[600px] h-[600px] block text-center" url={majorDetailData.value?.play_path || ''} />
          </div>
          <div class="flex flex-1 flex-col gap-y-2">
            <div>
              剧名: {title}
            </div>
            <div class="flex items-center flex-wrap gap-2">
              标签：{
                (playItem.value?.audit_label_ids as number[])?.map(item => (
                  <span class="badge badge-primary">{labels.value.find(label => label.label_id === item)?.content || ''}</span>
                ))
              }
            </div>
            <div>
              审核状态：{majorDetailData.value?.main_detail ? <span class={`badge- badge${statusMap[majorDetailData.value?.main_detail?.status]?.color}`}>{statusMap[majorDetailData.value?.main_detail?.status]?.label}</span> : null }
            </div>
            <div class="link link-primary" onClick={() => openFile(majorDetailData.value?.play_path || '')}>查看剧本</div>
            {
              majorDetailData.value?.main_detail?.status === 2
                ? (
                    <div>
                      驳回原因：{majorDetailData.value?.main_detail?.rejected_reason}
                    </div>
                  )
                : null
            }
            {isAuditable.value && playItem.value?.audit_failed_reason
              ? (
                  <div>
                    修改原因: {playItem.value?.audit_failed_reason}
                  </div>
                )
              : null}
            {
              majorDetailData.value?.main_detail?.status === 4
                ? (
                    <div>
                      指定责编：{majorDetailData.value?.main_detail?.principal}
                    </div>
                  )
                : null
            }
            {
              majorDetailData.value && majorDetailData.value.list?.length > 0
                ? (
                    <ElCollapse class="max-h-[400px] overflow-y-auto">
                      <ElCollapseItem title="责编评估详细" name="1">
                        {editorEvaluateList(majorDetailData.value?.list || [])}
                      </ElCollapseItem>
                    </ElCollapse>
                  )
                : null
            }
          </div>
        </div>
        {isAuditable.value && currentRoles.value.includes(26) && hasEvaluateNum >= 5
          ? (
              <x-audit-footer class="flex w-full justify-end gap-x-[10px]">
                <button class="btn btn-error btn-sm" onClick={() => {
                  const rejected_reason = ref('')
                  const rejected_reason_en = ref('')
                  const dialog = openDialog({
                    title: '打回修改',
                    customClass: '!w-[450px]',
                    body: () => (
                      <div class="flex flex-col gap-y-4">
                        <div class="flex flex-col gap-y-4">
                          <textarea
                            class={mc('textarea textarea-bordered block')}
                            value={rejected_reason.value}
                            onInput={(e: Event) => {
                              rejected_reason.value = (e.target as HTMLTextAreaElement).value
                            }}
                            placeholder="请填写中文备注"
                          />
                        </div>
                        <div class="flex flex-col gap-y-4">
                          <textarea
                            class={mc('textarea textarea-bordered block')}
                            value={rejected_reason_en.value}
                            onInput={(e: Event) => {
                              rejected_reason_en.value = (e.target as HTMLTextAreaElement).value
                            }}
                            placeholder="请填写英文备注"
                          />
                        </div>
                        <div class="flex w-full justify-end gap-x-[10px]">
                          <button class="btn btn-ghost btn-sm" onClick={() => {
                            dialog()
                          }}
                          >取消
                          </button>
                          <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                            if (!rejected_reason.value) {
                              showFailToast('请填写备注')
                              return
                            }
                            btnLoading.value = true
                            await auditCommitMajor({
                              play_key: play_key,
                              data: {
                                status: 2,
                                rejected_reason: rejected_reason.value,
                                rejected_reason_en: rejected_reason_en.value
                              },
                            }, () => {
                              void getAuditMajorDetail(play_key)
                              cb && cb()
                              dialog()
                            })
                          }}
                          >
                            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            确定
                          </button>
                        </div>
                      </div>
                    ),
                  })
                }}
                >
                  打回修改
                </button>
                <button class="btn btn-primary btn-sm" onClick={() => {
                  const dialog = openDialog({
                    title: '评估通过',
                    customClass: '!w-[450px]',
                    body: () => (
                      <div class="flex flex-col gap-y-4">
                        <div class="flex flex-col gap-y-2">
                          {requiredLabel('指定责编：')}
                          <select class="select select-bordered select-sm w-full" v-model={principal.value}>
                            {
                              editorList.value.map(item => (
                                <option value={item.username}>{item.username}</option>
                              ))
                            }
                          </select>
                        </div>
                        <div class="flex w-full justify-end gap-x-[10px]">
                          <button class="btn btn-ghost btn-sm" onClick={() => {
                            dialog()
                          }}
                          >取消
                          </button>
                          <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
                            if (!principal.value) {
                              showFailToast('请选择指定责编')
                              return
                            }
                            void auditCommitMajor({
                              play_key: play_key,
                              data: {
                                status: 4,
                                principal: principal.value,
                              },
                            }, () => {
                              void getAuditMajorDetail(play_key)
                              cb && cb()
                              dialog()
                            })
                          }}
                          >
                            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            确定
                          </button>
                        </div>
                      </div>
                    ),
                  })
                }}
                >评估通过
                </button>
              </x-audit-footer>
            )
          : null}
      </x-audit-confirm-dialog>
    ),
  })
  void getAuditMajorDetail(play_key)
  void getEditorList()
}

const renderAuditTable = (list: M.IScoreStep1[]) => {
  const data = list && list.length > 0
    ? list.map((item, index) => ({
      id: String(index + 1),
      category: item.item || '',
      criteria: item.content || '',
      desc: item.desc || '',
      score: (item.score !== undefined && item.score !== null) ? item.score : undefined,
      isOption: item.isOption,
    }))
    : cloneDeep(defaultTemplateStep1 as unknown as M.ScoreTableDataStep1[])

  scoreData.value = data.map(item => ({
    item: item.category,
    content: item.criteria,
    desc: item.desc || '',
    score: item.score,
    isOption: item.isOption,
  }))

  const tableData = ref<M.ScoreTableDataStep1[]>(data as M.ScoreTableDataStep1[])

  return (
    <div>
      <Step1ScoreTable
        disabled={list.length !== 0}
        data={tableData.value}
        onScoreChange={(newData, total, rate) => {
          scoreData.value = newData.map(item => ({
            item: item.category,
            content: item.criteria,
            desc: item.desc ===  undefined ? undefined : item.desc,
            score: (item.score === undefined || item.score === null) ? undefined : item.score,
            isOption: item.isOption,
          }))
        }}
      />
    </div>
  )
}

const isUpdating = ref(false)
const isUploading = ref(false)
const isUploadedFail = ref(false)
const tempFiles = ref([])
const uploadContractFile = ref()

type StsSign = {
  accessid: string
  host: string
  expire: number
  signature: string
  policy: string
  dir: string
  callback: string
  oss_static_video_prefix: string
  oss_static_prefix: string
}
const ossData = ref<StsSign>()

const uploadRef = ref<InstanceType<typeof Uploader>>()
// 上传合同
const uploadContract = (row: M.IScriptManageListItem, cb?: () => void) => {
  console.log('uploadContract', row)
  uploadContractFile.value = ''
  const closeDialog = openDialog({
    title: '上传合同',
    mainClass: 'pb-0 px-5',
    customClass: '!w-[500px]',
    body: () => (
      <x-audit-confirm-dialog v-loading={detailLoading.value} class="flex flex-col gap-y-[25px]">
        <div class="flex flex-col gap-y-4">
          <Uploader ref={uploadRef} accept="pdf,doc,docx" maxsize={1024 * 1024 * 100} ossKeyType="resource"
            showFileList={false}
            class="w-full h-[123px] cursor-pointer overflow-hidden rounded-md border border-dashed border-[#CCCACB]"
            onUploadSuccess={d => {
              uploadContractFile.value = d.oss_static_prefix + d.key!
            }}
          >
            {
              uploadContractFile.value ? (
                <div class="relative h-full break-all">
                  {uploadContractFile.value}
                </div>
              ) : (
                <div class="bg-fill-1 flex h-[123px] w-full shrink-0 items-center justify-center text-sm text-[#A1A0A3]">
                  上传合同
                </div>
              )
            }
          </Uploader>
          <div class="py-4 text-center"><Button class="btn btn-info btn-sm" onClick={onClickSaveContract}>上传合同</Button></div>

        </div>
        
      </x-audit-confirm-dialog>
    ),
  })

  const onClickSaveContract = async () => {
    if (!uploadContractFile.value) {
      showFailToast('请上传合同')
      return
    }
    const uploadContractData = {
      play_key: row.play_key,
      contract_path: uploadContractFile.value,
    }
    try {
      await apiUploadContract(uploadContractData)
      showSuccessToast('上传成功')
      closeDialog()
    } catch (error) {
      console.log('error', error)
      showFailToast(error?.response?.data?.message || '上传失败')
    }
  }
}


// 责编评估
const onFirstResponsibleEditorEvaluate = ({ play_key, title }: { play_key: string, title: string }, cb?: () => void) => {
  const closeDialog = openDialog({
    title: '责编评估',
    mainClass: 'pb-0 px-5',
    customClass: '!w-[1200px]',
    body: () => (
      <x-audit-confirm-dialog v-loading={detailLoading.value} class="flex flex-col gap-y-[25px]">
        <div class="flex flex-row space-x-4">
          <div class="w-[600px] space-y-4">
            <PreviewFile customClass="w-[600px] h-[600px] block text-center" url={playItem.value?.play_path || ''} />
          </div>
          <div class="flex flex-col gap-y-2">
            <div>
              剧名: {title}
            </div>
            <div class="flex items-center flex-wrap gap-2">
              标签：{
                (playItem.value?.audit_label_ids as number[])?.map(item => (
                  <span class="badge badge-primary">{labels.value.find(label => label.label_id === item)?.content || ''}</span>
                ))
              }
            </div>
            <div>
              状态：<span class={`badge- badge${statusMap[detailData.value?.status]?.color}`}>{statusMap[detailData.value?.status]?.label}</span>
            </div>
            {detailData.value?.rejected_reason
              ? (
                  <div>
                    修改原因: {detailData.value?.rejected_reason}
                  </div>
                )
              : null}
            <div>
              <span class="link link-primary" onClick={() => openFile(playItem.value?.play_path || '')}>查看剧本</span>
            </div>
            {
              renderAuditTable(detailData.value?.list || [])
            }
          </div>
        </div>
        {detailData.value?.status === 1 && currentRoles.value.includes(27)
          ? (
              <x-audit-footer class="flex w-full justify-end gap-x-[10px]">
                <button class="btn btn-primary btn-sm" onClick={() => {
                  const rejected_reason = ref('')
                  const isPass = ref(totalScore.value >= PASS_POINTS ? 4 : 2)
                  const isNotValid = scoreData.value.some(item => item.score === undefined || item.score === null)
                  if (isNotValid) {
                    showFailToast('评估维度结果必选')
                    return
                  }
                  const dialog = openDialog({
                    title: '提交评估',
                    customClass: '!w-[450px]',
                    body: () => (
                      <div class="flex flex-col gap-y-4">
                        <div class="flex items-center">
                          {requiredLabel('总评：')}
                          <select class="select select-sm select-bordered" value={isPass.value} onChange={(e: Event) => {
                            isPass.value = Number((e.target as HTMLInputElement).value)
                          }}>
                            <option value={4}>通过</option>
                            <option value={2}>不通过</option>
                          </select>
                        </div>
                        <div class="flex flex-col gap-y-4">
                          <textarea
                            class={mc('textarea textarea-bordered block')}
                            value={rejected_reason.value}
                            onInput={(e: Event) => {
                              rejected_reason.value = (e.target as HTMLTextAreaElement).value
                            }}
                            placeholder="请填写评估备注"
                          />
                        </div>
                        <div class="flex w-full justify-end gap-x-[10px]">
                          <button class="btn btn-ghost btn-sm" onClick={() => {
                            dialog()
                          }}
                          >取消
                          </button>
                          <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                            if (!rejected_reason.value) {
                              showFailToast('请填写备注')
                              return
                            }
                            btnLoading.value = true
                            await auditCommit({
                              play_key: play_key,
                              data: {
                                status: isPass.value,
                                rejected_reason: rejected_reason.value,
                                list: scoreData.value,
                                score: totalScore.value,
                                rating: rating.value,
                              },
                            }, () => {
                              void getAuditMajorDetail(play_key)
                              cb && cb()
                              dialog()
                              closeDialog()
                            })
                          }}
                          >
                            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            确定
                          </button>
                        </div>
                      </div>
                    ),
                  })
                }}
                >
                  确认提交
                </button>
              </x-audit-footer>
            )
          : null}
      </x-audit-confirm-dialog>
    ),
  })
  void getAuditDetail(play_key)
}
