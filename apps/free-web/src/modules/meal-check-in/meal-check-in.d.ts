export interface EtaItem {
  schedule: number
  name: string
  status: number
  gold: number
  bottom_show_text: string
  is_curr_node: boolean
}

interface Widget {
  widget_icon: string
  widget_link: string
  widget_text: string
}
export interface DialogInfo {
  reward_num: number
  title: string
  reward_text: string
  sub_title: string
  add_coins_txt: string
  major_btn_img: string
  major_btn_txt: string
  major_link: string
  minor_btn_txt: string
}
export type WelfareEatDetails = {
  welfare_id: number
  welfare_key: string
  task_total_gold: number
  status: number
  eta_item_list: EtaItem[]
  button_show: { btn_txt: string }
  top_show: { title: string, sub_title: string }
  bottom_show: { title: string, sub_title: string }
  jump_widget: Widget
  activity_rules_widget: Widget
}

interface Widget {
  widget_icon: string
  widget_link: string
  widget_text: string
}
export type ReceiveRes = {
  ad_extra: {
    text: string
    extra: string
  }
  dialog: {
    reward_num: number
    title: string
    reward_text: string
    sub_title: string
    add_coins_txt: string
    major_btn_img: string
    major_btn_txt: string
    major_link: string
    minor_btn_txt: string
  }
  toast: {
    icon: string
    text: string
  }
}
export type ReclaimReceiveRes = {
  toast: {
    icon: string
    text: string
  }
}
