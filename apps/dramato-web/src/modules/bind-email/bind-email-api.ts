import { httpClient } from 'src/lib/http-client'

export const apiCheckEmail = (data: {
  email: string
  from: string // push reward
}) => {
  return httpClient.post<ApiResponse>('dm-api/user/email/check', data)
}

export const apiBindEmail = (params: { userInfo: string, verificationCode: string }) => {
  return httpClient.get<ApiResponse>(`dm-api/user/email/check/${params.userInfo}/${params.verificationCode}`)
}

export const apiCheckEmailResult = () => {
  return httpClient.get<ApiResponse<{
    email: string
    state: number // 状态，1，等待验证，2，验证成功, 3，超时
  }>>('dm-api/user/email/check/result')
}
