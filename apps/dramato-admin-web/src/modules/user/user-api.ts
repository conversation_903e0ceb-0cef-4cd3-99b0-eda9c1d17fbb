import { httpClient } from 'src/lib/http-client'

// 用户列表
export const apiGetUserList = (data: M.IRoleUserParams) => {
  return httpClient.post<ApiResponse<{
    list: M.IRoleUser[]
    total: number
  }>>('/backend/user_list', data)
}

// 修改用户角色
export const apiUpdateUserRole = (data: {
  user_ids: string[]
  open_roles: number[]
}) => {
  return httpClient.post<ApiResponse<null>>('/backend/user_role_alter', data)
}

export const apiGetRoleList = () => {
  return httpClient.post<ApiResponse<{
    list: M.ISimpleRole[]
  }>>('/backend/user_role_list')
}

export const apiEditUser = (data: M.IUser) => {
  return httpClient.post<ApiResponse<M.IUser>>('/backend/operate_external_user', { ...data, access_key: '111222333@' })
}

export const apiUserMenu = (data: { role_ids: number[] }) => {
  return httpClient.post<ApiResponse<{ list: M.IUserMenuItem[] }>>('/backend/role_menus', { ...data })
}

export const apiResetQrCode = (data: {
  username: string
}) => httpClient.post<ApiResponse<null>>('/backend/reset_auth_status', data)
