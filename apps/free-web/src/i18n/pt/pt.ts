export const pt = {
  // 通用
  common: {
    cancel: 'Cancelar',
  },
  // 分享页
  share: {
    watchNow: 'Ver agora',
    episodeDialog: {
      title: 'Junte-se a mim e ganhe moedas extras!',
      description: 'Estou viciado nesta série!',
      tips: 'Recomendação',
      button: 'Assista ao drama completo',
      limit24: 'Tempo limitado: 24h',
    },
    toast: {
      takeOffShelf: 'O conteúdo não está disponível',
    },
  },
  'customer-service-center': {
    title: 'Centro de atendimento ao cliente',
    description: 'Descrição',
    'description-placeholder': 'Forneça detalhes para nos ajudar a entender o problema',
    'email-placeholder': 'Insira seu e-mail para receber atualizações de opinião.',
    'submit-feedback': 'Enviar',
    'enter-feedback': 'Comentários soubre outros problemas',
    'upload-pictures': 'Carregar fotos',
    feedback: 'opinião',
    faq: {
      question1: 'Por que diz \'<PERSON><PERSON><PERSON>s Ainda Não Disponíveis\'?',
      answer1: 'Às vezes, conexões instáveis com o servidor ou com os anunciantes podem causar problemas. Recomendamos tentar novamente mais tarde e não clicar em "Assistir Anúncios" com muita frequência. Limites de anúncios podem se aplicar devido a políticas de terceiros ou restrições do seu país. Estamos constantemente trabalhando com nossos provedores de anúncios para melhorar sua experiência de visualização de anúncios.',
      question2: 'Por que diz \'Rede Indisponível\' para anúncios?',
      answer2: 'Por favor, tente reiniciar o aplicativo fechando-o completamente e reabrindo-o. Se os anúncios ainda não estiverem carregando, espere um pouco antes de tentar novamente e evite clicar com muita frequência. Se o problema persistir, você pode entrar em contato através do nosso sistema de Feedback. Também estamos trabalhando com nossa equipe de anúncios para melhorar a experiência de visualização. Obrigado pela sua paciência.',
    },
  },
  toast: {
    'upload-failed': 'Falha na carrega, por favor tente novamente',
    'submit-success': 'Enviado com sucesso',
    'email-incorrect': 'O formato do e-mail está incorreto',
  },
  not_found: {
    title: 'A página que você está procurando não existe',
  },
  regulations: {
    howUseData: 'Como usamos seus dados',
    content: `Se você selecionar "Permitir" na solicitação de permissão de Transparência de Rastreamento de Aplicativos, usaremos suas informações e dados para otimizar nossos serviços e fornecer a melhor experiência para você, caso contrário, apenas coletaremos os dados e não realizaremos nenhuma atividade de rastreamento.

Se você concordar em permitir que a DramaWave e nossos parceiros e fornecedores usem dados do seu dispositivo para analisar como você usa o aplicativo, personalizar recomendações de conteúdo e fornecer mais serviços relacionados a informações, clique em "Concordar e Continuar" para continuar.`,
    acceptAll: 'Aceitar tudo',
    dataSettings: 'Configurações de dados',
    next: 'Próximo',
    confirm: 'Confirmar',
    aboutYourPrivacy: 'Sobre sua privacidade',
    personalDataName: 'Dados personalizados',
    personalDataDescription: 'Usamos essas informações para lembrar suas escolhas e fornecer recomendações de publicidade e conteúdo personalizadas, notificações e lembretes, etc.',
    advertisementDataName: 'Dados de publicidade',
    advertisementDataDescription: 'Cookies, identificadores do seu dispositivo ou outras informações podem ser armazenados ou acessados no seu dispositivo para fins de publicidade online. Os dados do usuário também podem ser compartilhados com nossos parceiros de publicidade.',
    behaviorsDataName: 'Dados comportamentais',
    behaviorsDataDescription: 'Dados relacionados à análise, como cookies ou identificadores de dispositivos, podem ser armazenados para medir e analisar o uso, comportamento e desempenho da publicidade.',
    privacyAndTerms: 'Política de Privacidade e Termos de Serviço',
    referToPrivacy: 'Consulte a Política de Privacidade',
    referToTerms: 'Consulte os Termos de Serviço',
    agreeAndSave: 'Concordar e Salvar',
  },
  coins: {
    earnings: {
      title: 'Meus Ganhos',
      total: 'Moedas Totais',
      exchange: 'Troca',
      earningsHistory: 'Histórico de Ganhos',
      exchangeHistory: 'Histórico de Trocas',
      noRecords: 'Sem registro',
      last50earnings: 'Apenas os últimos 50 ganhos são exibidos',
      last50exchanges: 'Apenas os últimos 50 registros de troca são exibidos',
      benefits: 'Benefícios',
      gotIt: 'Entendi',
    },
    exchange: {
      title: 'Zona de Troca',
      notOpen: 'Ainda não aberto, por favor, fique atento.',
      receiveBtn: 'Receber felizmente',
      earnCoins: 'Ganhar Moedas',
      redeemRequiredXCoins: 'Resgatar requer {X} moedas',
      exchangeSuccess: 'Troca realizada com sucesso',
      cardId: 'ID do CARTÃO',
      exchangeFailed: 'Troca Falhou',
      transferFailedInsufficientStock: 'Fora de estoque. Por favor, venha cedo amanhã.',
      gotItBtn: 'Entendi',
      transferFailedRiskControl: 'Conta incomum, temporariamente incapaz de trocar.',
      tryAgainBtn: 'Tente Novamente Mais Tarde',
      transferConfirm: 'Confirmar Transferência',
      paypalAccountEmail: 'Email da Conta Paypal',
      confirmPaypalAccountEmail: 'Confirmar Email da Conta Paypal',
      transferX: 'Transferir {X}',
      transferXConfirm: 'Confirmar Transferência de {X}',
      transferToYourAccountX: 'Transferir para sua conta {X}',
      confirmBtn: 'Confirmar',
      transferSuccessTitle: 'Estamos enviando o dinheiro para sua conta Paypal!',
      transferSuccessContentX: 'Seu dinheiro chegará dentro de 5 dias na conta de {X}.',
      copySuccess: 'Cópia bem-sucedida',
      enterEmailError: 'Por favor, insira um endereço de e-mail válido.',
      enterEmailValidError: 'Erro de entrada, por favor insira novamente.',
      emailNotMatchError: 'O endereço de e-mail não coincide, por favor insira novamente.',
    },
  },
  my_wallet: {
    title: 'Minha Carteira',
    help_dialog: {
      title: 'Regras de Uso de Bilhetes',
      content1: `1 Bilhete Comprado = 1 Bilhete de Recompensa Comprado = 1 Bilhete de Recompensa de Anúncio = 1 Bilhete de Troca de Moedas – Todos esses podem ser usados para desbloquear episódios. `,
      content2: `2. Todos os ingressos desbloqueiam direitos de visualização permanentes, mas os ingressos de recompensa comprados expirarão após um determinado período.`,
      content3: `3. Bilhetes Comprados podem ser adquiridos através de compra direta.`,
      content4: `4. Bilhetes de Recompensa Comprados só podem ser obtidos durante períodos promocionais específicos através de compra.`,
      content5: `5. Bilhetes de Recompensa de Anúncio podem ser ganhos completando tarefas de visualização de anúncios.`,
      content6: `6. Bilhetes de Troca de Moedas podem ser obtidos trocando moedas.`,
    },
    account_balance: 'Saldo da Conta',
    purchased_tickets: 'Bilhetes Comprados',
    purchase_reward_tickets: 'Bilhetes de Recompensa Comprados',
    coin_exchange_tickets: 'Bilhetes de Troca de Moedas',
    ad_reward_tickets: 'Bilhetes de Recompensa de Anúncios',
    top_up: 'Recarga',
    purchase_history: 'Histórico de Compras',
    rewards_history: 'Histórico de Recompensas',
    exchange_history: 'Histórico de Trocas',
    consumption_records: 'Registros de Consumo',
    auto_unlock: 'Episódios em desbloqueio automático',
  },
  top_up: {
    exchange_title: 'Troca de Moedas',
    purchase_title: 'Compra',
    tickets: 'Bilhetes',
    coins: 'Moedas',
    balance: 'Saldo',
    title: 'Loja',
    tips: 'Dicas',
    tips_1: '1.Nossa plataforma oferece tanto dramas curtos gratuitos quanto dramas premium de acesso antecipado.',
    tips_2: '2.Para assistir ao conteúdo de acesso antecipado com antecedência, você precisa resgatá-lo com Bilhetes. Esses Bilhetes podem ser obtidos através de compra, troca de moedas ou assistindo a anúncios.',
    tips_3: '3.Todos os ingressos desbloqueiam direitos de visualização permanentes, mas os ingressos de recompensa comprados expirarão após um determinado período.',
    tips_4: '4.Bilhetes de Recompensa Comprados serão usados primeiro ao desbloquear episódios. Se o saldo for insuficiente, seus bilhetes regulares serão usados automaticamente.',
    tips_5: '5.Se o seu pagamento de recarga for bem-sucedido, mas o seu saldo não foi atualizado, tente atualizar sua carteira.',
    tips_6: '6.Para qualquer outra dúvida, sinta-se à vontade para nos contatar através de "Perfil" -> "Centro de Atendimento ao Cliente" -> "Feedback".',
    exchange_dialog_title: 'Tem certeza que quer trocar?',
    redeem_required: 'Resgate necessário',
    exchange_success: 'xxx ingressos trocados com sucesso',
    balance_not_enough: 'Saldo insuficiente',
    exchange_failed: 'Troca falhou',
  },
  my_wallet_in: {
    transaction_history: 'Histórico de transações',
    EPnum: 'EP.{num}',
    Expired: 'Expirar',
    AD: 'ANÚNCIO',
    Expires_on_num: 'Expira em{num}',
    Thats_all_for_now: 'Isso é tudo por agora.',
    consumption_records: 'Registros de Consumo',
  },
  meal_check_in: {
    sleeping_link: 'Earn Coins While Sleeping',
    rules_title: 'Regras da atividade',
    benefits_title: 'Benefits',
    rules_1: '1. Todos os dias há quatro horários para você registrar suas refeições: Café da manhã: 07:00-10:00; Almoço: 11:00-14:00; Jantar: 17:00-20:00; Lanche da noite: 21:00-24:00.',
    rules_2: '2. Se você perder o horário de registro correspondente, pode assistir a um vídeo para reivindicar a recompensa.',
    rules_3: '3. As recompensas são redefinidas todos os dias à meia-noite, então não se esqueça de reclamá-las.',
  },
  sleep_check_in: {
    earn_link: 'Registre refeições para ganhar',
    benefits_title: 'Benefits',
    rules_1: '1. Todas as noites, das 19h às 24h, você pode ativar o modo \'Dormir para Ganhar\'.',
    rules_2: '2. Após ativá-lo, no dia seguinte, após dormir por 8 horas, você pode reclamar a recompensa de sono entre 8h e 12h.',
    rules_3: '3. Se você perder o horário para reivindicar a recompensa, pode assistir a um vídeo entre 12h e 19h no dia seguinte para reivindicar novamente a recompensa.',
  },

}
