/* eslint-disable @typescript-eslint/no-explicit-any */
import { onUnmounted, ref, watch } from 'vue'
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateForm, transformDatetime, EChart, showFailToast, SvgIcon } from '@skynet/ui'
import { set } from 'lodash-es'
import dayjs from 'dayjs'
import { apiGetStatistic } from './statistics-api.ts'
import { getOptions } from './tools.tsx'
import { useRoute } from 'vue-router'

type StatisticsPageOptions = {
  props: {}
}

type IncomeQueryForm = {
  dt: string
}

export const StatisticsPage = createComponent<StatisticsPageOptions>({
  props: {},
}, props => {
  const route = useRoute()
  const chartMap = ref<any>({})
  const type = ref('')
  const typeList = ref<{
    value: string
    label: string
    title: string
  }[]>([])

  const typeMap: {
    [record: string]: {
      name: string
      list: {
        value: string
        label: string
        title: string
      }[]
    }
  } = {
    income: {
      name: '充值数据',
      list: [{
        value: 'income_all',
        label: '充值+订阅收入',
        title: '充值数据',
      }, {
        value: 'income_membership_all',
        label: '订阅收入',
        title: '充值数据',
      }, {
        value: 'new_user_income_all',
        label: '新用户充值+订阅收入',
        title: '充值数据',
      }, {
        value: 'pay_unlock_payment_show_all',
        label: '支付弹窗面板展示次数',
        title: '充值数据',
      }, {
        value: 'dw_ad_cost_all',
        label: '投放花费',
        title: '充值数据',
      }, {
        value: 'dw_ad_reward_earn_all',
        label: '广告收入',
        title: '充值数据',
      }, {
        value: 'watch_ads_revenue_count_all',
        label: '有收入广告次数',
        title: '充值数据',
      }, {
        value: 'ad_revenue_newuser_all',
        label: '新用户广告收入',
        title: '充值数据',
      }]
    },
    freereels: {
      name: 'freereels用户数据',
      list: [{
        value: 'fr_new_user_all',
        label: '新增用户数',
        title: 'freereels用户数据',
      }, {
        value: 'fr_dau_user_all',
        label: '日活',
        title: 'freereels用户数据',
      }, {
        value: 'fr_ad_revenue_price_all',
        label: '广告收入',
        title: 'freereels用户数据',
      }] },
    user: {
      name: '用户数据',
      list: [{
        value: 'new_user_all',
        label: '新增用户数',
        title: '用户数据',
      }, {
        value: 'dau_user_all',
        label: '日活',
        title: '用户数据',
      }, {
        value: 'play_duration_all',
        label: '总播放时长(分钟)',
        title: '用户数据',
      }] },
    push: {
      name: 'push数据',
      list: [{
        value: 'push_send_all',
        label: 'Push-Send',
        title: 'push数据',
      }, {
        value: 'push_receive_all',
        label: 'Push-Receive',
        title: 'push数据',
      }, {
        value: 'push_show_all',
        label: 'Push-Show',
        title: 'push数据',
      }, {
        value: 'push_click_all',
        label: 'Push-Click',
        title: 'push数据',
      }, {
        value: 'push_send_auto_gen_all',
        label: '自动化内容push发送',
        title: 'push数据',
      }, {
        value: 'push_send_manual_gen_all',
        label: '人工运营push发送',
        title: 'push数据',
      }, {
        value: 'dw_push_send_relate_all',
        label: '相关push发送',
        title: 'push数据',
      }, {
        value: 'push_send_local_all',
        label: 'Local Push 发送',
        title: 'push数据',
      }]
    },
    'freereels-push': {
      name: 'FreeReels-PUSH数据',
      list: [{
        value: 'fr_push_send_all',
        label: 'Push-Send',
        title: 'FreeReels-PUSH数据',
      }]
    },
    finances: {
      name: '商业化数据',
      list: [{
        value: 'recharge_count_all',
        label: '充值成功次数',
        title: '商业化数据',
      }, {
        value: 'subscribe_count_all',
        label: '订阅成功次数',
        title: '商业化数据',
      }, {
        value: 'refund_count_all',
        label: '退款次数',
        title: '商业化数据',
      }, {
        value: 'gold_consumer_total_all',
        label: '金币消耗数量',
        title: '商业化数据',
      }, {
        value: 'bonus_get_total_all',
        label: '赠金发放数量',
        title: '商业化数据',
      }]
    },
    advertise: {
      name: '广告链路数据',
      list: [{
        value: 'ad_show_action_count_all',
        label: 'as_show_action 次数',
        title: '广告链路数据',
      }, {
        value: 'ad_show_count_all',
        label: 'ad_show 次数',
        title: '广告链路数据',
      }, {
        value: 'ad_revenue_count_all',
        label: 'ad_revenue 次数',
        title: '广告链路数据',
      }]
    },
    w2a: {
      name: 'w2a链路数据',
      list: [{
        value: 'yl_w2a_write_clip_all',
        label: '盈量w2a落地页写剪切板数',
        title: 'w2a链路数据',
      }, {
        value: 'yl_w2a_page_view_all',
        label: '盈量w2a落地页view数',
        title: 'w2a链路数据',
      }, {
        value: 'yl_w2a_page_click_all',
        label: '盈量w2a落地页跳转按钮click数',
        title: 'w2a链路数据',
      }]
    }
  }

  const QueryForm = CreateForm<IncomeQueryForm>()

  const queryForm = ref({
    dt: dayjs().format('YYYYMMDD'),
  })
  const emptyPage = () => {
    return (
      <div class="h-full w-full flex flex-col justify-center items-center">
        <SvgIcon class="w-[180px] h-[180px]" name="ic_empty" />
        <div class="text-[var(--text-3)]">暂无数据</div>
      </div>
    )
  }

  const onQuery = () => {
    typeList.value.forEach(async typeObj => {
      chartMap.value[typeObj.value].loading = true
      try {
        const res = await apiGetStatistic({ ...queryForm.value, type: typeObj.value })
        void getOptions(res.data as M.IChartData, typeObj.value, chartMap)
      } catch (error: any) {
        showFailToast(error?.response?.data?.message)
      } finally {
        chartMap.value[typeObj.value].loading = false
      }
    })
  }

  let interval = null

  interval = setInterval(() => {
    void onQuery()
  }, 5 * 60 * 1000)

  watch(() => route.path, () => {
    type.value = route.path.split('/').pop() as string
    chartMap.value = {}
    typeList.value = typeMap[type.value].list
    typeList.value.map(typeObj => {
      return chartMap.value[typeObj.value] = {
        label: typeObj.label,
        options: {},
        loading: false,
        hasData: false,
      }
    })
    onQuery()
  }, {
    immediate: true,
  })

  onUnmounted(() => {
    clearInterval(interval)
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>{typeMap[type.value].name}</li>
          </ul>
        ),
        form: () => (
          <div class="flex justify-start items-center space-x-2">
            <QueryForm class=" flex flex-row"
              onSubmit={onQuery}
              data={queryForm.value}
              hasAction={false}
              onChange={(path, value) => {
                set(queryForm.value, path, value)
                void onQuery()
              }}
              items={[{
                label: '日期',
                path: 'dt',
                input: { type: 'date', rawFormat: 'YYYYMMDD', displayFormat: 'YYYY-MM-DD' },
                transform: transformDatetime,
                hint: () => <div class="text-xs text-[var(--text-2)]">* UTC0时区时间</div>,
              },
              ]}
            />
          </div>
        ),
        table: () => {
          return Object.keys(chartMap.value).map(type => (
            <div class="flex flex-col w-full h-[500px]">
              <div class="text-lg text-[var(--text-1)] px-4">{chartMap.value[type].label}</div>
              {
                chartMap.value[type].loading
                  ? (
                      <div class="flex items-center justify-center h-[500px]">
                        <div class="text-[var(--text-3)]">正在加载中……</div>
                      </div>
                    )
                  : chartMap.value[type].hasData
                    ? (
                        <div class="w-full">
                          <EChart class="bg-white w-full h-[420px]" option={chartMap.value[type].options} />
                        </div>
                      )
                    : emptyPage()
              }
            </div>
          ))
        },
      }}
      </NavFormTablePager>
    </div>
  )
})

export default StatisticsPage
