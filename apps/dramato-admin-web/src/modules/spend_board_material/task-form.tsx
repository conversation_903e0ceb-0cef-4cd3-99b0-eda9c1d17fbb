/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert, transformTimestamp, transformInteger, CreateTableOld } from '@skynet/ui'
import { set } from 'lodash-es'
// import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { ref } from 'vue'
import { Fn } from '@vueuse/core'
import { apiAddSeriesTaskListBatchAdd } from '../spend_board/spend_board-api'

type TaskFormOptions = {
  props: {
    checkedSeries: M.SpendBoardMaterial.SeriesItem[]
  }
  emits: {
    cancel: Fn
    ok: () => void
  }
}
export const TaskForm = createComponent<TaskFormOptions>({
  props: {
    checkedSeries: [],
  },
  emits: {
    cancel: fn,
    ok: fn,
  },
}, (props, { emit }) => {
  const Table = CreateTableOld<M.SpendBoardMaterial.SeriesItem>()
  const isUpdating = ref(false)
  const params = ref<{
    deploy_task_time: number
    comment: string
    team: 1 | 2
  }>({
    deploy_task_time: 0,
    comment: '',
    team: 1,
  })

  const Form = CreateForm<{
    deploy_task_time: number
    comment: string
    team: 1 | 2
  }>()

  const formRules = z.object({
    deploy_task_time: z.number().min(1, '请选择剧单日期'),
    comment: z.string().min(1, '请选择任务备注'),
    team: z.number().min(1, '请选择投放团队'),
  })

  const { error, validateAll } = useValidator(params, formRules)

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <label class="font-bold  text-[18px]">已选中剧单</label>
        <Table
          class="tm-table-fix-first-column tm-table-fix-last-column"
          list={props.checkedSeries || []}
          columns={[
            ['资源ID', 'series_resource_id', { class: 'w-[80px]' }],
            ['资源名称', 'series_resource_title', { class: 'w-[190px]' }],
            ['语种', 'language_version'],
          ]}
        />
        <label class="font-bold  text-[18px]">指定任务</label>
        <Form
          class="grid gap-y-1 grid-cols-3"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            console.log('path', path, 'value', value)
            set(params.value || {}, path, value)
          }}
          items={[
            [
              '剧单日期',
              'deploy_task_time',
              { type: 'date', class: 'leading-[32px]', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD' },
              {
                transform: transformTimestamp,
              },
            ],
            [
              '制定投放团队',
              'team',
              {
                type: 'select',
                options: [{ label: '昆仑', value: 1 }, { label: '盈亮', value: 2 }],
                autoInsertEmptyOption: false,
              },
              {
                transform: transformInteger,
              },
            ],
            [
              '任务备注',
              'comment',
              {
                type: 'text',
                placeholder: '请输入任务备注',
              },
            ],
          ]}
          data={params.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={() => emit('cancel')}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const exclude: string[] = []

            if (!validateAll({ exclude })) {
              console.log('err', error)

              return
            }

            isUpdating.value = true
            void apiAddSeriesTaskListBatchAdd({
              team: params.value.team,
              comment: params.value.comment,
              deploy_task_time: params.value.deploy_task_time,
              tasks: props.checkedSeries.map(i => {
                return {
                  series_resource_id: i.series_resource_id,
                  lang: i.language_version,
                }
              }),
            }).then(() => {
              emit('ok')
            }).catch((error: any) => {
              showAlert(error.response.data.err_msg || error.response.data.message || '更新失败', 'error')
            }).finally(() => {
              isUpdating.value = false
            })
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确认推荐'}
        </Button>
      </div>
    </>
  )
})

export default TaskForm
