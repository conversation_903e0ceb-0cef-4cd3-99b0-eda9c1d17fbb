/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, getQuery, mc } from '@skynet/shared'
import { BackButton } from 'src/modules/common/button/back-button'
import { useActivityStore } from '../use-activity-store'
import { useRoute } from 'vue-router'
import { computed, onMounted, ref } from 'vue'
import { track } from 'src/lib/track'
import { mothersDayImages } from './images/images'
import { ActivityBlock } from '../activity-block'
import { isIos } from 'src/lib/ua'
import { jsBridge } from 'src/lib/jsbridge'

const imageToButtonMap: Record<number, number | null> = {
  0: null,
  1: 0,
  2: 1,
  3: null,
}

export const MothersDayPage = createComponent(null, () => {
  const { config, fetchConfig } = useActivityStore()
  const route = useRoute()
  const language = getQuery('language', 'en')
  const eventId = getQuery('event_id', 0)
  const images = computed(() => {
    return mothersDayImages[language][isIos() ? 'ios' : 'android']
  })

  onMounted(() => {
    if (!eventId) return
    void fetchConfig(eventId)
  })

  onMounted(() => {
    track('activity_H5', 'page', 'show', {
      activity_id: route.query.event_id,
      user_id: route.query.user_id,
      from: route.query.from,
    })
  })

  const needChangeBgColor = ref(false)

  const isSupportedAllScreen = ref<boolean>(false)
  onMounted(() => {
    if (!jsBridge.inDramawaveApp) {
      isSupportedAllScreen.value = false
      return
    }

    try {
      void jsBridge<{ app_version_code: number, app_version: string }>('getDeviceInfo').then(({ app_version_code, app_version }) => {
        console.log('>> current app_version_code', app_version_code)

        if (!isIos()) {
          isSupportedAllScreen.value = app_version_code >= 1370000
        } else {
          isSupportedAllScreen.value = +(app_version.replaceAll('.', '')) >= 1370
        }
      })
    } catch (error) {
      isSupportedAllScreen.value = false
    }
  })
  return () => (
    <x-activity-page class="size-full overflow-auto relative block bg-[#0c0a0d]"
      onScroll={(e: any) => {
        if (e.target.scrollTop > 0) {
          needChangeBgColor.value = true
        } else {
          needChangeBgColor.value = false
        }
      }}>
      <div class={mc(
        ' w-full left-0 top-0 pt-[max(env(safe-area-inset-top),_44px)] z-up',
        isSupportedAllScreen.value && needChangeBgColor.value ? 'bg-[#0c0a0d]' : '',
        isSupportedAllScreen.value ? 'fixed' : 'absolute',
      )}>
        <BackButton />
      </div>
      <x-block-list class="flex flex-col gap-0">
        {images.value?.map((i, index) => (
          <ActivityBlock key={index} type="image" src={i} button={config.value[imageToButtonMap[index] ?? -1]} />
        ))}
      </x-block-list>
    </x-activity-page>
  )
})

export default MothersDayPage
