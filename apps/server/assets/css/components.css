input:disabled {
  cursor: not-allowed;
  @apply bg-[var(--fill-3)] border-[#e6e8ed] text-[var(--text-3)]
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.dramato-textarea {
  @apply w-full resize-none px-3 pt-4 pb-3 bg-[var(--grey-13)] overflow-y-auto text-[var(--white)] rounded-lg border-none outline-none active:border-none focus:border-none;
  &::placeholder {
    @apply text-[var(--grey-6)]
  }
}

.dramato-mobile-textarea-word-limit {
  @apply pt-1 pb-2 pr-3 bg-[var(--grey-13)] text-[#434546] text-right w-full !bottom-1 !right-0 rounded-b-lg;
}

.dramato-input {
  @apply w-full resize-none px-3 py-4 bg-[var(--grey-13)] text-[var(--white)] rounded-lg border-none outline-none active:border-none focus:border-none;
  &::placeholder {
    @apply text-[var(--grey-6)]
  }
}

.dramato-button {
  &:disabled {
    @apply !bg-[var(--brand-3)] !text-[#fdfbfc]/50
  }
}

.button-primary {
  @apply rounded-[200px] border-none bg-[var(--brand-6)] hover:bg-[var(--brand-5)] active:bg-[var(--brand-7)] text-[14px] text-[var(--text-6)] font-medium tracking-[0.3px];
}

