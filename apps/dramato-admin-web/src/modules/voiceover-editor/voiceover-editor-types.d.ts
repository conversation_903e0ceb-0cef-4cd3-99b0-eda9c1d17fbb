declare namespace M {
  namespace VoiceoverEditor {
    interface Params {
      series_resource_id: number
      serial_number: number
      language: string
    }

    interface Response {
      srt_path: string
      noauido_path: string
      voices: {
        order: number
        voice_path: string
        start_time: number
        duration: number
        head: number
        tail: number
        status: number
        speed: number
        tts_text: string
      }[]
      combine_path: string
    }

    interface srtItem {
      language: string
      type: string
      subtitle: string
    }
    interface reCreateParams {
      series_resource_id?: number
      serial_number?: number
      language?: string
      order?: number
      sentence?: string
      model?: string
      emotion?: string
      speed?: number
    }
    interface combineParams {
      series_resource_id: number
      serial_numbers: number[]
      languages: string[]
    }
    interface adjustParams {
      series_resource_id: number
      serial_number: number
      language: string
      voices: {
        order: number
        start_time: number
        head: number
        tail: number
      }[]
    }

    interface uploadParams {
      series_resource_id: number
      serial_number: number
      language: string
      order: number
      duration: number
      oss_path: string
    }
    interface splitParams {
      series_resource_id: number
      serial_number: number
      language: string
      src_order: number
      sep_seg: {
        start_time: number
        head: number
        tail: number
        tts_text: string
        duration: number
      }
    }
    interface deleteParams {
      series_resource_id: number
      serial_number: number
      language: string
      order: number
    }
  }
}
