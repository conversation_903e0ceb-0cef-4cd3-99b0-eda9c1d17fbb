/* eslint-disable @typescript-eslint/no-explicit-any */
import { nextTick, onUnmounted, ref } from 'vue'
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateForm, transformDatetime, EChart, showFailToast, SvgIcon } from '@skynet/ui'
import { set, get } from 'lodash-es'
import { apiGetStatistic, type IncomeData } from './statistic-api'
import dayjs from 'dayjs'
import * as echarts from 'echarts'

type StatisticPageOptions = {
  props: {}
}
export const StatisticPage = createComponent<StatisticPageOptions>({
  props: {},
}, props => {
  type IncomeQueryForm = {
    dt: string
    type: 'income_all' | 'new_user_income_all' | 'new_user_all' | 'dau_user_all' | 'play_duration_all' | 'push_receive_all' | 'push_show_all' | 'push_click_all' | string
  }
  const color = ['#3B82F6', '#FACC15', '#D50B95', '#14B8A6', '#F59E0B', '#FF5555']
  const echartOption = ref({})
  const chartLoading = ref(false)
  const hasData = ref(true)
  const QueryForm = CreateForm<IncomeQueryForm>()
  const queryForm = ref({
    dt: dayjs().format('YYYYMMDD'),
    type: 'income_all',
  })

  const formatXAxis = (arr: IncomeData[]) => {
    return arr.map((row: { time_str: string }) => row.time_str?.split(' ')[1])
  }

  const emptyPage = () => {
    return (
      <div class="h-full w-full flex flex-col justify-center items-center">
        <SvgIcon class="w-[180px] h-[180px]" name="ic_empty" />
        <div class="text-[var(--text-3)]">暂无数据</div>
      </div>
    )
  }

  function getGradientColor(color: string) {
    return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: hexToRgba(color, 0.37), // 半透明
      },
      {
        offset: 1,
        color: hexToRgba(color, 0), // 完全透明
      },
    ])
  }

  // 将 hex 颜色转换为 rgba 格式
  function hexToRgba(hex: string, alpha: number) {
    const r = parseInt(hex.slice(1, 3), 16)
    const g = parseInt(hex.slice(3, 5), 16)
    const b = parseInt(hex.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
  }

  const onQuery = async () => {
    chartLoading.value = true
    try {
      const res = await apiGetStatistic(queryForm.value)
      const legendDescMap = {
        today: '当日',
        yesterday: '前一天',
        week_ago: '七天前',
        month_ago: '上月',
      }
      const legendKeys = Object.values(legendDescMap)
      // 获取x轴时间
      const xAxis = formatXAxis(res.data?.yesterday || [])

      const yAxis = Object.keys(legendDescMap).map((k, index) => {
        const statisticData = get(res.data, k, [])
        return {
          name: legendKeys[index],
          type: 'line',
          smooth: true,
          showSymbol: false,
          data: statisticData.map((row: { k_value: string }) => row.k_value),
          areaStyle: {
            color: getGradientColor(color[index]), // 动态计算渐变色
          },
        }
      })
      if (xAxis.length === 0) {
        hasData.value = false
      } else {
        hasData.value = true
      }
      chartLoading.value = false
      await nextTick()
      echartOption.value = {
        color: color,
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: legendKeys,
        },
        grid: {
          left: 30,
          right: 30,
          bottom: 10,
          top: 30,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxis,
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E7EAEE',
            },
          },
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E7EAEE',
            },
          },
        },
        series: yAxis,
      }
    } catch (error: any) {
      showFailToast(error?.response?.data?.message)
    } finally {
      chartLoading.value = false
    }
  }

  void onQuery()
  let interval = null

  interval = setInterval(() => {
    void onQuery()
  }, 5 * 60 * 1000)

  onUnmounted(() => {
    clearInterval(interval)
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>实时充值数据</li>
          </ul>
        ),
        form: () => (
          <div class="flex justify-start items-center space-x-2">
            <QueryForm class=" flex flex-row"
              onSubmit={onQuery}
              data={queryForm.value}
              hasAction={false}
              onChange={(path, value) => {
                set(queryForm.value, path, value)
                void onQuery()
              }}
              items={[{
                label: '日期',
                path: 'dt',
                input: { type: 'date', rawFormat: 'YYYYMMDD', displayFormat: 'YYYY-MM-DD' },
                transform: transformDatetime,
              }, {
                label: '数据类型',
                path: 'type',
                input: { type: 'select', options: [{
                  value: 'income_all',
                  label: '充值数据',
                }, {
                  value: 'new_user_income_all',
                  label: '新用户充值数据',
                }, {
                  value: 'new_user_all',
                  label: '新增用户数',
                }, {
                  value: 'dau_user_all',
                  label: '日活',
                }, {
                  value: 'play_duration_all',
                  label: '总播放时长(分钟)',
                }, {
                  value: 'push_receive_all',
                  label: 'Push-Receive',
                }, {
                  value: 'push_show_all',
                  label: 'Push-Show',
                }, {
                  value: 'push_click_all',
                  label: 'Push-Click',
                }], autoInsertEmptyOption: false },
              }]}
            />
          </div>
        ),
        table: () => {
          return (
            <div class="flex justify-center items-center h-[500px]">
              { chartLoading.value
                ? <div class="text-[var(--text-3)]">正在加载中……</div>
                : hasData.value
                  ? (
                      <div class="w-full">
                        <div class="p-4 mt-4 text-xs text-[var(--text-2)]">* UTC0时区时间</div>
                        <EChart class="bg-white w-full h-[500px]" option={echartOption.value} />
                      </div>
                    )
                  : emptyPage()}
            </div>
          )
        },
      }}
      </NavFormTablePager>
    </div>
  )
})

export default StatisticPage
