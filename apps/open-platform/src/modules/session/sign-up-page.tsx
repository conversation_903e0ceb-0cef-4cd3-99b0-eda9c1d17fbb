import { createComponent } from '@skynet/shared'
import { Tab } from '@skynet/ui'
import { ref } from 'vue'
import { SessionLayout } from './session-layout'
import { SignInWithPassword } from './sign-in-with-password'
import { SignInWithCode } from './sign-in-with-code'
import SignUpForm from './sign-up-form'
export const SignUpPage = createComponent(null, () => {
  const kind = ref<'password' | 'code'>('password')
  const bar = (
    <div class="w-6 h-1 absolute bg-[#fc2763] rounded-sm bottom-0 left-1/2 transform -translate-x-1/2" />
  )
  return () => (
    <SessionLayout>
      <x-sign-up-page class="block bg-white px-10 pb-8 pt-4 rounded-xl mx-[120px] ">
        <SignUpForm />
      </x-sign-up-page>
    </SessionLayout>
  )
})

export default SignUpPage
