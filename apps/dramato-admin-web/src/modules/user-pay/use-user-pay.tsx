import { CreateForm, CreateTableOld, showFailToast, showSuccessToast } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetUserPayList, apiWalletAssetReport } from './user-pay-api'
import { unknown } from 'zod'

export const useUserPay = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    closeOrderPopupModal,
    orderForm,
    isUpdating,
    onSave,
  }
}

const Form = CreateForm<M.UserPay.Params>()
const params = ref<M.UserPay.Params>({
  pay_type: 'Google',
})

const Table = CreateTableOld<M.UserPay.Item>()
const list = ref<M.UserPay.Item[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(1)

const search = async (_page?: number) => {
  if (!params.value.out_txn_id) {
    return
  }
  _page = _page || page.value + 1
  loading.value = true
  const res = await apiGetUserPayList({
    ...params.value,
    page_info: {
      offset: (_page - 1) * pageSize.value, size: pageSize.value,
    } })
    .finally(() => {
      loading.value = false
    }).catch(e => {
      if (e?.data?.code !== 200) {
        list.value = []
        total.value = 0
        loading.value = false
      }
    })
  list.value = res?.data?.items || []
  total.value = res?.data?.total || 0
  page.value = _page
}

// 补单相关数据
const isUpdating = ref(false)
const closeOrderPopupModal = ref(() => { })
const orderForm = ref<M.UserPay.orderFormParams>({
  out_txn_id: '',
  sku_id: '',
  pay_channel: 'googleplay',
  purchase_token: '',
})
const onSave = () => {
  console.log(orderForm.value)
  if (isUpdating.value === true) {
    return
  }
  isUpdating.value = true
  apiWalletAssetReport(orderForm.value)
    .then(() => {
      showSuccessToast('添加成功～')
      closeOrderPopupModal.value()
    }).finally(() => {
      isUpdating.value = false
    }).catch(e => {
      console.log(e)
      showFailToast(e.response.data.message || '补单失败！')
    })
}
