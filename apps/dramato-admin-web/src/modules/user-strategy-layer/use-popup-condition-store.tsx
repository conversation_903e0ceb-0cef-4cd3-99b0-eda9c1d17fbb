/* eslint-disable @typescript-eslint/no-explicit-any */
import { bindLoading } from '@skynet/shared'
import { ref } from 'vue'
import { CreateForm } from '@skynet/ui'
import { apiGetUserProfileItem } from './user-strategy-layer-api'
// import { apiGetStrategyGroupUserProfile } from '../strategy-group/strategy-group-api'

export type UserProfile = Api.StrategyGroupUserProfile.Item
export const usePopupConditionStore = () => {
  const Form = CreateForm<M.UserStrategyLayer.UserStrategy>()
  const userProfileList = ref<M.UserStrategyLayer.ProfileItem[]>([])
  const series_package_list = ref<M.UserStrategyLayer.ProfileItem[]>([])
  const fetchingUserProfileList = ref(false)
  const formData = ref<M.UserStrategyLayer.UserStrategy>({
    name: '',
    desc: '',
    send_formal_user: 1,
    user_persona: {},
    series_package_user_persona: {},
    persona_type: 0,
  })

  const fetchUserProfileList = async () => {
    fetchingUserProfileList.value = true
    const res = await bindLoading(apiGetUserProfileItem(), fetchingUserProfileList)
    if (!res.data) return
    userProfileList.value = res.data.list
    series_package_list.value = res.data.series_package_list
  }

  // const fetchUserProfileList = async () => {
  //   fetchingUserProfileList.value = true
  //   const res = await bindLoading(apiGetStrategyGroupUserProfile({ platform: 'All' }), fetchingUserProfileList)
  //   if (!res.data) return
  //   userProfileList.value = res.data.list
  // }

  return {
    Form,
    formData,
    userProfileList,
    series_package_list,
    fetchingUserProfileList,
    fetchUserProfileList,
  }
}
