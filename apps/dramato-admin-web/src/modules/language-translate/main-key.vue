<template>
  <div>
    <div class="py-4">主键库</div>
    <div class="bg-white p-4 rounded-md">
      <ElForm :model="formSearch" inline>
        <ElFormItem label="key_name">
          <ElInput v-model="formSearch.key_name" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="任务开始时间">
          <ElDatePicker v-model="formSearch.created_time_start" type="datetime" placeholder="请选择" />
        </ElFormItem>
        <ElFormItem label="任务结束时间">
          <ElDatePicker v-model="formSearch.created_time_end" type="datetime" placeholder="请选择" />
        </ElFormItem>
        <ElFormItem label="涉及端">
          <ElSelect style="width: 100px;" v-model="formSearch.platform" placeholder="请选择">
            <ElOption v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton @click="resetForm">重置</ElButton>
          <ElButton type="primary" @click="getKeyList">查询</ElButton>
          <ElButton type="primary" @click="downloadExcel">下载</ElButton>
        </ElFormItem>
      </ElForm>
    </div>
    <div class="mt-4">
      <ElTable :data="tableData" style="width: 100%">
        <ElTableColumn prop="key_name" label="key_name" width="200" />
        <ElTableColumn prop="content" label="源语言(英文)" width="200" />
        <ElTableColumn prop="completed_language" label="已翻译语言" width="200" />
        <ElTableColumn prop="create_time" label="创建日期" width="200" />
        <ElTableColumn prop="operator_name" label="创建人" width="200" />
        <ElTableColumn prop="status" label="翻译进度">
          <template #default="scope">
            <span :class="scope.row.status == '已完成' ? 'text-green-500' : 'text-red-500'">{{ scope.row.status }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="platform" label="涉及端" width="200">
          <template #default="scope">
            <span>{{platformList.find(item => item.value === scope.row.platform)?.label}}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" fixed="right">
          <template #default="scope">
            <ElButton type="primary" @click="handleView(scope.row)">查看</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
      <div class="mt-4 flex justify-end">
        <ElPagination layout="total, sizes, prev, pager, next" v-model="pageInfo.page" :page-size="pageInfo.pageSize"
          :total="pageInfo.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElButton, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElSelect, ElOption, ElDatePicker, ElMessage, ElLoading, ElPagination } from 'element-plus'
import { ref, onMounted, watch } from 'vue'
import { apiGetKeyList } from './language-translate-api'
import { useRouter, useRoute } from 'vue-router'
import dayjs from 'dayjs'
import { get_k_sso_token } from 'src/lib/device-id.ts'
const router = useRouter()
const route = useRoute()
const platformList = ref([
  { value: 1, label: 'IOS' },
  { value: 2, label: '安卓' },
  { value: 3, label: 'H5' },
  { value: 4, label: '服务端' },
  { value: 5, label: '前端' },
])
const pageInfo = ref({
  page: 1,
  pageSize: 10,
  total: 0,
})
const formSearchInit = {
  "key_name": undefined, // 模糊查询
  "platform": undefined, // 平台，1，ios, 2，android，3，H5，4，服务端，5，前端
  "created_time_start": undefined, // 创建时间开始
  "created_time_end": undefined // 创建时间结束
}
const formSearch = ref(formSearchInit)
const tableData = ref([])

const resetForm = () => {
  router.push({ query: {} })
  formSearch.value = { ...formSearchInit }
  getKeyList()
}

const handleView = (row: any) => {
  router.push({
    path: '/language-translate-task-detail/' + row.id,
    query: {
      from: 'main-key'
    }
  })
}

const formatSearchFormToUrl = () => {
  router.push({
    query: {
      ...(formSearch.value.key_name && { key_name: formSearch.value.key_name }),
      ...(formSearch.value.platform !== undefined && { platform: formSearch.value.platform.toString() }),
      ...(formSearch.value.created_time_start && { created_time_start: new Date(formSearch.value.created_time_start).toISOString() }),
      ...(formSearch.value.created_time_end && { created_time_end: new Date(formSearch.value.created_time_end).toISOString() })
    }
  })
}

const getUrlParams = () => {
  const { key_name, platform, created_time_start, created_time_end } = route.query

  formSearch.value = {
    key_name: key_name?.toString() || undefined,
    platform: platform ? Number(platform) : undefined,
    created_time_start: created_time_start ? new Date(created_time_start.toString()) : undefined,
    created_time_end: created_time_end ? new Date(created_time_end.toString()) : undefined
  }
}

const handleSizeChange = (size: number) => {
  pageInfo.value.pageSize = size
  pageInfo.value.page = 1
  getKeyList()
}

const handleCurrentChange = (page: number) => {
  pageInfo.value.page = page
  getKeyList()
}

const downloadExcel = () => {
  if (!formSearch.value.platform) {
    ElMessage.error('请选择涉及端')
    return
  }
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  // 获取token
  const token = get_k_sso_token()
  if (!token) {
    ElMessage.error('未登录或token失效')
    loading.close()
    return
  }

  fetch(import.meta.env.VITE_DRAMA_API_URL + '/translation/key/list/export', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'token': token,
      'Accept': '*/*'
    } as HeadersInit,
    body: JSON.stringify({ ...formSearch.value })
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response.blob()
    })
    .then(blob => {
      if (blob.size === 0) {
        throw new Error('下载的文件为空')
      }
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      const fileName = `主键库-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    })
    .catch(err => {
      ElMessage.error(err.message || '下载失败')
      console.error('下载失败:', err)
    })
    .finally(() => {
      loading.close()
    })
}

const getKeyList = () => {
  if (!formSearch.value.platform) {
    if (formSearch.value.key_name || formSearch.value.created_time_start || formSearch.value.created_time_end) {
      ElMessage.error('请选择涉及端')
    }
    return
  }
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  formatSearchFormToUrl()

  apiGetKeyList({ ...formSearch.value, ...pageInfo.value }).then((res: any) => {
    tableData.value = res.data.items
    pageInfo.value.total = res.data.total
  }).catch((err) => {
    ElMessage.error(err?.response?.data?.err_msg || '请求失败')
  }).finally(() => {
    loading.close()
  })
}

// 监听路由参数变化
watch(() => route.query,
  () => {
    getUrlParams()
  },
  { immediate: true }
)

onMounted(() => {
  getUrlParams() // 页面加载时读取 URL 参数
  getKeyList()
})
</script>
