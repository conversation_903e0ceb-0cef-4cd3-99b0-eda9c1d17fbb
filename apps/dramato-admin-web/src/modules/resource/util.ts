import { trim } from 'lodash-es'

export const trimSplitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`)).map(str => str.replace(new RegExp(`^[${[' '].join('')}]+|[${[' '].join('')}]+$`, 'g'), ''))

/**
 * @description 是否是第三方
 */
export const strToArr = (str: string) => {
  if (!str) return []
  return str.replace(/，/g, ',').split(',').map(s => trim(s)).filter(s => !!s)
}

export const idStrToList = (str: string) => {
  if (!str) return []
  const trimSplit = trimSplitIt([',', ';', '，', ' ', '；', '\t'])
  // 如果数组中list每个元素都为数字，且大于 资源id大于等于9900，如果有一个符合，则 返回符合规则的，如果都不符合规则，则返回
  const list = trimSplit(str)
  const isResourceIds = list.filter(item => {
    return /^\d+$/.test(item) && +item >= 9900
  })
  if (isResourceIds.length > 0) {
    return isResourceIds
  } else {
    return [trim(str)]
  }
}

export function getUuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export function subtitleTimestampCheck(content: string, duration: number): number[] {
  let count = 0
  const items = content.split('\n')
  const errRowsMap: { [key: number]: number } = {}
  const errorLines = []
  // 1. Check for "-1:59:59" format
  const re = /-1:59:59,\d{3}/
  items.forEach((item, i) => {
    if (item.length < 10) return
    if (re.test(item)) {
      count++
      errRowsMap[i] = 1
    }
  })

  // 2. Check for incorrect timestamp format (should be hh:mm:ss,SSS)
  const re1 = /\d{2}:\d{2}:\d{2},\d{3}/
  items.forEach((item, i) => {
    if (item.length < 5) return
    if (item.includes('-->')) {
      const timestamps = item.split('-->')
      if (timestamps.length !== 2) return
      const startTime = timestamps[0].trim()
      const endTime = timestamps[1].trim()
      if (!re1.test(startTime) || !re1.test(endTime)) {
        count++
        errRowsMap[i] = 1
      }
    }
  })

  // 3. Check if timestamps exceed video duration + 30 seconds
  if (duration > 10) {
    items.forEach((item, i) => {
      if (item.length < 5) return
      if (item.includes('-->')) {
        const timestamps = item.split('-->')
        if (timestamps.length !== 2) return
        const startTime = getVideoTimeStampByStr(timestamps[0].trim())
        const endTime = getVideoTimeStampByStr(timestamps[1].trim())
        if (endTime === -1 || startTime === -1) {
          count++
          errRowsMap[i] = 1
        } else if (startTime > (duration + 30) * 1000 || endTime > (duration + 30) * 1000) {
          count++
          errRowsMap[i] = 1
        }
      }
    })
  }

  // 4. Check if start time is greater than end time
  items.forEach((item, i) => {
    if (item.length < 5) return
    if (item.includes('-->')) {
      const timestamps = item.split('-->')
      if (timestamps.length !== 2) return
      const startTime = getVideoTimeStampByStr(timestamps[0].trim())
      const endTime = getVideoTimeStampByStr(timestamps[1].trim())
      if (endTime === -1 || startTime === -1) {
        count++
        errRowsMap[i] = 1
      } else if (endTime - startTime < 0) {
        count++
        errRowsMap[i] = 1
      }
    }
  })

  // Generate error message
  if (count > 0) {
    const keys = Object.keys(errRowsMap).map(Number)
    keys.sort((a, b) => a - b)
    for (let i = 0; i < Math.min(200, keys.length); i++) {
      errorLines.push(keys[i] + 1)
    }
  }
  return errorLines
}

// Helper function to convert timestamp string into a number (milliseconds)
// function getVideoTimeStampByStr(timestamp: string): number {
//   const parts = timestamp.split(':')
//   const minutes = parseInt(parts[0], 10)
//   const seconds = parseInt(parts[1], 10)
//   if (!parts[2]) {
//     return 0
//   }
//   const ms = parseInt(parts[2].split(',')[1], 10)
//   return (minutes * 60 + seconds) * 1000 + ms
// }

function getVideoTimeStampByStr(timeStr: string): number {
  // 正则表达式：匹配时:分:秒,毫秒
  const regex = /^(\d{2}):(\d{2}):(\d{2}),(\d{3})$/
  const match = timeStr.match(regex)

  if (!match) {
    return -1
  }
  // 从匹配中提取时、分、秒、毫秒
  const hours = parseInt(match[1])
  const minutes = parseInt(match[2])
  const seconds = parseInt(match[3])
  const milliseconds = parseInt(match[4])

  // 计算总毫秒数
  const totalMilliseconds = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds

  return totalMilliseconds
}

export function formatSubtitles(input: string) {
  const regex = /(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3}\s*)[\r\n]+([\s\S]*?)(?=\n\s*\n|$)/g

  const matches = []
  let match

  while ((match = regex.exec(input)) !== null) {
    matches.push({
      time: `${match[1]} --> ${match[2]}`,
      content: match[3].trim(),
    })
  }

  return matches.map((row, index) => `${index + 1}\n${row.time}\n${row.content}\n`).join('\n')
}
