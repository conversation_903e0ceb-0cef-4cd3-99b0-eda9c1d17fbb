import { createComponent } from '@skynet/shared'
import { SvgIcon } from '@skynet/ui'
import { onMounted, onUnmounted, ref, watch } from 'vue'
import Loading from 'src/modules/common/loading/loading'
import { closeFill, noData } from '../images/images'
import { useSearch } from './use-search'
import { DramaItem } from './drama-item'
import { useDebounceFn, watchDebounced } from '@vueuse/core'
import { searchKeywords } from './search-api'
import InfinityScroll from '../common/infinity-scroll/infinity-scroll'
import { T } from './search.i18n'
import { Back } from '../common/back/back'

type InputAreaProps = {
  props: {
    query?: string
  }
}

export const InputArea = createComponent<InputAreaProps>({
  props: {
    query: '',
  },
}, () => {
  const inputRef = ref<HTMLInputElement | null>(null)

  onMounted(() => {
    inputRef.value?.focus()
  })

  const {
    query,
    keywordsList,
    isSearching,
    isSearching<PERSON><PERSON><PERSON>,
    showKeywords<PERSON>ist,
    showDramatoList,
    dramatoList,
    chooseKeyword,
    search,
    withHighlight,
    hasMore,
    next,
  } = useSearch()

  const debounceSearch = useDebounceFn(search, 500)
  const hideLoading = ref(false)

  watchDebounced(query, () => {
    if (query.value === '') {
      showKeywordsList.value = false
      showDramatoList.value = false
      return
    }
    if (!isSearchingDramato.value) {
      showKeywordsList.value = true
      keywordsList.value = []
      isSearching.value = true
      void searchKeywords(query.value).then(res => {
        keywordsList.value = res.data?.keywords || []
      }).finally(() => {
        isSearching.value = false
      })
    }
  }, { debounce: 500 })

  const scrollSearch = (nextPage: string) => {
    hideLoading.value = true
    search(nextPage)
  }

  watch(() => showDramatoList.value, () => {
    if (showDramatoList.value) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'auto'
    }
  })

  onUnmounted(() => {
    query.value = ''
    showKeywordsList.value = false
    showDramatoList.value = false
    dramatoList.value = []
    document.body.style.overflow = 'auto'
  })

  return () => (
    <Back isWhite={true} class="z-100 fixed flex !h-[53px] w-full items-center bg-[#0B080B] py-2" backTo="/">
      <div class="ml-1 mr-3 flex h-8 w-full items-center gap-x-1 rounded-lg bg-[#242526] p-2 text-[#FDFBFCB2]">
        <SvgIcon name="ic-search" class="size-4" />
        <form class="flex-1" onSubmit={event => {
          event.preventDefault()
          void debounceSearch()
          hideLoading.value = false
        }}>
          <input
            ref={inputRef}
            v-model={query.value}
            class="no-tap-color w-full border-none bg-transparent text-sm text-[#fdfbfc] caret-[#fc2763] outline-none placeholder:text-[#797b7d]"
            type="text"
            placeholder={T.search.placeholder()}
          />
        </form>
        {query.value !== '' && (
          <img
            src={closeFill}
            class="ml-1 size-4"
            onClick={() => {
              query.value = ''
              dramatoList.value = []
              inputRef.value?.focus()
            }}
          />
        )}
      </div>
      <button
        class="h-full whitespace-nowrap border-none bg-transparent text-sm text-[#fc2763]"
        onClick={() => {
          void debounceSearch()
          hideLoading.value = false
        }}
      >
        {T.search.search()}
      </button>
      {showKeywordsList.value && (
        <div class="z-100 fixed left-0 top-[53px] h-[calc(100vh-53px)] w-full overflow-y-auto bg-[#0B080B] px-3">
          {isSearching.value ? (
            <div class="flex size-full items-center justify-center text-sm text-[#fc2763]">
              <Loading />
            </div>
          ) : keywordsList.value.length > 0 ? (
            <ul class="flex flex-col">
              {keywordsList.value.map(keyword => (
                <li
                  key={keyword.keyword}
                  class="border-b-solid border-b-[0.5px] border-b-[#242526] py-4 text-white"
                  onClick={() => {
                    chooseKeyword(keyword.keyword)
                    hideLoading.value = false
                  }}
                >
                  <div class="text-sm" innerHTML={withHighlight(keyword.highlight)} />
                </li>
              ))}
            </ul>
          ) : (
            <NoResults />
          )}
        </div>
      )}
      {showDramatoList.value && (
        <div class="z-100 fixed left-0 top-[53px] w-full overflow-y-auto bg-[#0B080B] px-3" style={{
          bottom: 'calc(env(safe-area-inset-bottom))',
        }}>
          {isSearchingDramato.value && !hideLoading.value ? (
            <div class="flex size-full items-center justify-center text-sm text-[#fc2763]">
              <Loading />
            </div>
          ) : dramatoList.value.length > 0 ? (
            <InfinityScroll
              hasMore={hasMore.value}
              next={next.value}
              loading={isSearchingDramato.value}
              onLoad={scrollSearch}
            >
              <div class="flex size-full flex-col gap-y-4 py-3">
                {dramatoList.value.map(dramato => (
                  <DramaItem key={dramato.id} dramato={dramato} from="/search_result" />
                ))}
              </div>
            </InfinityScroll>
          ) : (
            <NoResults />
          )}
        </div>
      )}
    </Back>
  )
})

const NoResults = () => (
  <div class="flex size-full -translate-y-[53px] flex-col items-center justify-center gap-y-5 text-sm text-[#fc2763]">
    <img src={noData} class="size-40" />
    <div class="text-center text-base text-[#cccacb]">
      {T.search.noResults()}
    </div>
  </div>
)

export default InputArea
