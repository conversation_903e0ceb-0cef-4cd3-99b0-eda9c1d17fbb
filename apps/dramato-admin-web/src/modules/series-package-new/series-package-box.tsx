/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { useSeriesPackageAddFormStore } from './use-series-package-add-form-store'
import { set } from 'lodash-es'
import { transformNumberArray } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import { lang } from 'src/lib/constant'
import { onMounted, ref } from 'vue'
import { apiListSeriesPackage } from './series-package-api'
type SeriesPackageBoxOptions = {
  props: {}
}
export const SeriesPackageBox = createComponent<SeriesPackageBoxOptions>({
  props: {},
}, props => {
  const {
    formData,
    Form,
    oneStepError,
  } = useSeriesPackageAddFormStore()

  const list = ref<M.SeriesPackage.Details[]>([])

  onMounted(() => {
    void apiListSeriesPackage({
      import_types: [1, 2, 3, 4],
      page_info: { page_index: 1, page_size: 9999 },
    }).then(res => {
      list.value = res.data?.list || []
    })
  })

  return () => (
    <Form
      class="flex flex-1 flex-col gap-y-3"
      hasAction={false}
      error={oneStepError.value}
      onChange={(path, value) => {
        set(formData.value || {}, path, value)
      }}
      items={[
        [
          requiredLabel('名称'),
          'package_name',
          {
            type: 'textarea',
            placeholder: '请输入名称',
            rows: 3,
          },
          {
            class: mc('col-span-1'),
          },
        ],
        [
          requiredLabel('集合方式'),
          'package_config.package_type_5_config.collection_type',
          {
            type: 'radio',
            options: [
              { label: '正选', value: 1 },
              { label: '反选', value: 2 },
            ],
          },
          {
            hint: `正选=统计选中剧包所包含的所有剧
                   反选=排除选中剧包ID的所有剧`,
          },
        ],
        [
          requiredLabel('剧包ID'),
          `package_config.package_type_5_config.collection_package_ids`,
          {
            type: 'multi-select',
            search: true,
            popoverWrapperClass: 'z-popover-in-dialog',
            options: list.value.map((n, index) => {
              return { value: n.id, label: `${n.id}/${n.package_name}` }
            }),
          },
          {
            class: mc('col-span-1'),
            // transform: [
            //   (raw?: string) => raw ? raw.split(',').map((n: string) => +(n.trim())) : [],
            //   (display: number[]): string => display ? display.join(',') : '',
            // ] as const,
          },
        ],
        [
          '备注',
          'remark',
          {
            type: 'textarea',
            placeholder: '请输入备注',
            rows: 3,
          },
          {
            class: mc('col-span-1'),
          },
        ],
      ] as any}
      data={formData.value}
    />
  )
})

export default SeriesPackageBox
