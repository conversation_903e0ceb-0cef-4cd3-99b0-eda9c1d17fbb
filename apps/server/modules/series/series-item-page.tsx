import { reportNow } from '@skynet/client-track'
import { betterReplace, createComponent, getWindowHeight, getWindowHeightPx, required, useAutoOverflowBody } from '@skynet/shared'
import { SvgIcon, useTween } from '@skynet/ui'
import type { AxiosError } from 'axios'
import { showToast } from 'vant'
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { time } from '../common/constants'
import GoogleAd from '../google-ad/google-ad'
import { CT } from '../i18n/common.i18n'
import type { WalletDataInfo } from '../profile/profile'
import { useStatistics } from '../statistics/statistics'
import { useMyWallet } from '../wallet/use-my-wallet'
import { useYlAd } from '../yl-ad/yl-ad'
import './icons/icons'
import { Player } from './player'
import { seriesApi } from './series-api'
import { T } from './series-item-page.i18n'
import SeriesMenuBar from './series-menu-bar'
import { useImmersive } from './use-immersive'
import { useSeriesItem } from './use-series-item'

// import FaqPanel from './faq-panel.vue'
// import { useOg } from './use-og'
import { useOverlays } from './use-overlays'
import { useSecondCover } from './use-second-cover'
import { useSlide } from './use-slide'
import { useSubtitles } from './use-subtitles'
import { useWatermark } from './use-watermark'
import { keepError } from '~/lib/http-client'
import { h5Track } from '~/lib/h5-track'

type SeriesItemPageOptions = {
  props: {
    data: M.Dramato
    wallet: WalletDataInfo
  }
}
export const SeriesItemPage = createComponent<SeriesItemPageOptions>({
  name: 'SeriesItemPage',
  props: {
    data: required,
    wallet: required,
  },
}, props => {
  const startTime = Date.now()
  const loading = ref(true)
  const route = useRoute()
  const series_id = computed(() => route.params.series_id.toString() || '')
  const from_page = computed(() => route.query.from_page?.toString() || '')
  const auto_play = computed(() => route.query.auto_play?.toString() || 'yes')
  const { seriesItem, fetchSeriesItem, currentEpisodeId, getCurrentEpisode, getNextEpisode,
    getPreviousEpisodeById, manualUnlockEpisode, unlockEpisode, unlockOverlayVisible, productPanelVisible, manualUnlockPanelVisible,
    prefetchVideo, updateActiveEpisodes, activeEpisodes, getNextEpisodeById, getIndexInActiveEpisodes, getEpisodesWithAds, currentAdId,
    findEpisode, findEpisodeByIndex, anonymousUserLoginCheck, episodeSelectMenuVisible,
  } = useSeriesItem(series_id.value)
  const currentEpisode = computed(() => getCurrentEpisode())
  const { immersive, resetImmersiveTimer, cancelImmersiveTimer } = useImmersive(series_id.value)
  const firstPlayReported = shallowRef(false)
  watch(() => immersive.value, im => {
    if (!im) {
      resetImmersiveTimer()
    }
  })
  watch(() => episodeSelectMenuVisible.value, visible => {
    if (!visible) {
      resetImmersiveTimer()
    }
  })
  const wrapper = ref<HTMLDivElement>()
  const router = useRouter()
  const { markEpisodeAsPlayed, markEpisodeAsPlayedMost } = useStatistics()
  const { sessionId, refreshSessionId } = useStatistics()
  const { subtitles } = useSubtitles(series_id.value)
  const { translateY, tween } = useSlide(series_id.value, wrapper, (fromIndex, toIndex) => {
    const from = findEpisodeByIndex(fromIndex)
    const to = findEpisodeByIndex(toIndex)
    h5Track('video', 'page', 'swipe', {
      video_id: from?.id,
      to_video_id: to?.id,
      series_id: series_id.value,
      from: from_page.value,
      session_id: sessionId.value,
      video_type: from?.video_type ? 'free' : 'charge',
    })
  })
  const { registerTime } = useYlAd()
  useAutoOverflowBody()
  useWatermark(series_id.value)
  // useOg(series_id.value)
  const { renderSecondCover } = useSecondCover(series_id.value, onClickUnlock)
  const { renderOverlays } = useOverlays(series_id.value, { onClickUnlock, onCancelRecharge, onClickManualUnlock })
  const everPlayed = ref(false)
  const { wallet } = useMyWallet()
  // #region watchers
  // 监听路由变化
  // episode_id => currentEpisodeId
  watch(() => route.params?.episode_id?.toString(), id => {
    if (id) {
      currentEpisodeId.value = id
    }
    unlockOverlayVisible.value = false
  }, { immediate: true })
  // 更新路由（不需要触发路由变化，只是为了方便用户刷新页面）
  watch(() => currentEpisode.value, episode => {
    if (!episode) return
    if (window.location.pathname !== '/series/' + series_id.value + '/' + episode.id) {
      history.replaceState({}, '', '/series/' + series_id.value + '/' + episode.id + window.location.search)
    }
  }, { immediate: true })
  watch(() => currentEpisode.value, episode => {
    if (!episode) return
    updateActiveEpisodes()
    setTimeout(() => {
      prefetchVideo()
    }, 5000)
  }, { immediate: true })
  watch(() => seriesItem.value, item => {
    if (!item) return
    h5Track('video', 'page', 'show', {
      video_id: currentEpisodeId.value,
      series_id: series_id.value,
      from: from_page.value,
      session_id: sessionId.value,
      video_type: item.free ? 'free' : 'charge',
    }, {
      relative_time: Date.now() - window.startReportTime,
    })
    if (!findEpisode(currentEpisodeId.value)) {
      const startEpisode = findEpisodeByIndex(item.start_episode - 1)
      const viewEpisode = findEpisodeByIndex(item.view_episode - 1)
      const firstEpisode = findEpisodeByIndex(0)
      if (wallet.value?.vip_expire && wallet.value?.vip_expire < Date.now()) {
        currentEpisodeId.value = viewEpisode?.id ?? firstEpisode?.id ?? null
      } else if (wallet.value?.vip_used !== true) {
        currentEpisodeId.value = startEpisode?.id ?? firstEpisode?.id ?? null
      } else {
        currentEpisodeId.value = startEpisode?.id ?? firstEpisode?.id ?? null
      }
    }
  }, { immediate: true })

  // #endregion

  // #region 生命周期
  onMounted(() => {
    if (seriesItem.value === null || seriesItem.value.id !== series_id.value) {
      if (window.startReportTime) {
        h5Track('h5', 'performance', 'player_before_fetch_series', {
          load_time: Date.now() - window.startReportTime,
        })
      }
      loading.value = true
      const task = props.data ? Promise.resolve(props.data) : fetchSeriesItem()
      // #region 服务端设置数据
      if (props.data) {
        seriesItem.value = props.data
      }
      if (props.wallet) {
        wallet.value = props.wallet as WalletDataInfo
      }
      // #endregion
      void task.then(() => {
        if (!currentEpisodeId.value) return
        updateActiveEpisodes()
        const index = getIndexInActiveEpisodes(currentEpisodeId.value)
        translateY.value = -index * getWindowHeight()
      }).finally(() => {
        if (window.startReportTime) {
          h5Track('h5', 'performance', 'player_after_fetch_series', {
            load_time: Date.now() - window.startReportTime,
          })
        }
      })
    }
    // 页面关闭之前调用 onCloseWindow
    window.addEventListener('beforeunload', onCloseWindow)
    // 监听 page visibility
    document.addEventListener('visibilitychange', onChangePageVisibility)
  })
  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', onCloseWindow)
    window.removeEventListener('visibilitychange', onChangePageVisibility)
    currentEpisodeId.value = null
    seriesItem.value = null
    refreshSessionId()
    // 重置匿名用户登录检查
    anonymousUserLoginCheck.value = false
  })
  onBeforeRouteUpdate((to, from, next) => {
    const { episode_id: toEpisodeId } = to.params
    if (toEpisodeId) {
      const previousEpisode = getPreviousEpisodeById(toEpisodeId.toString())
      if (!previousEpisode) return next()
      if (previousEpisode.unlock !== true) {
        showToast(T.seriesItemPage.dontMissOut())
        return
      }
    }
    next()
  })
  // #endregion

  return () => (
    <x-series-item-page class="relative block h-[calc(100vh_-_var(--vh-offset,0px))] overflow-hidden" ref={wrapper}>
      {renderViewWindow()}
      {renderLoading()}
      {renderMenu()}
      {renderOverlays()}
      {renderSecondCover()}
    </x-series-item-page>
  )

  // #region helpers

  function renderMenu() {
    return (
      <SeriesMenuBar class={immersive.value ? 'invisible' : 'visible'} seriesId={series_id.value}
        onClick={() => {
          // cancelImmersiveTimer()
        }}
        onUnlock={episode_id => {
          // window.location.href = window.location.origin +`/series/${series_id.value}/${episode_id}?from_episode=prev`
          void betterReplace(router, {
            params: { episode_id: episode_id },
            // path: `/series/${series_id.value}/${episode_id}`,
            query: { from_episode: 'prev' },
          })
        }} />
    )
  }

  function renderLoading() {
    return (
      loading.value
        ? (
            <x-loading class="h-safe-screen absolute left-0 top-0 flex w-full items-center justify-center bg-black/50 text-white">
              <x-loading-circle class="flex size-[120px] items-center justify-center ">
                <SvgIcon name="ic-video-loading" class="h-[100px] w-[66px] animate-pulse" />
              </x-loading-circle>
            </x-loading>
          )
        : null
    )
  }

  function renderViewWindow() {
    return (
      <x-view-window class="relative block w-full" style={{ transform: `translateY(${translateY.value}px)`, height: getWindowHeight() + 'px' }}>
        {activeEpisodes.value.map(item => (
          ('kind' in item)
            ? (
                <x-google-ad-wrapper
                  key={item.id}
                  data-index={getIndexInActiveEpisodes(item.id)}
                  class="h-safe-screen absolute left-0 top-0 flex w-full items-center justify-center"
                  style={{ zIndex: getZIndex(item.id), transform: `translateY(${getIndexInActiveEpisodes(item.id) * getWindowHeight()}px)`, height: getWindowHeightPx() }}
                >
                  <GoogleAd onNext={swipeToNext} dom={item.dom} />
                </x-google-ad-wrapper>
              )
            : (
                <Player
                  key={item.id}
                  data-index={getIndexInActiveEpisodes(item.id)}
                  class="absolute left-0 top-0 w-full"
                  episodeId={item.id}
                  loadType={item.id === currentEpisodeId.value ? 'ongoing' : 'pre'}
                  style={{ zIndex: getZIndex(item.id), transform: `translateY(${getIndexInActiveEpisodes(item.id) * getWindowHeight()}px)`, height: getWindowHeightPx() }}
                  paused={!!currentAdId.value || item.id !== currentEpisodeId.value}
                  subtitles={item.id === currentEpisodeId.value ? subtitles.value : []}
                  src={getPlayUrl(item)}
                  autoPlay={item.id === currentEpisodeId.value && auto_play.value !== 'no'}
                  loading={item.id === currentEpisodeId.value ? loading.value : true}
                  onCanPlay={() => {
                    if (item.id === currentEpisodeId.value) loading.value = false
                  }}
                  onFirstPlay={() => {
                    onFirstPlay(item.id)
                    // 启播之后隐藏封面
                    const coverImg = document.getElementById('__server-cover-img__')
                    if (coverImg) {
                      coverImg.classList.add('hidden')
                    }
                  }} onEndPlay={onEndPlay}
                  onTimeUpdate={(d, u) => onTimeUpdate(d, u, item.id)} seriesId={series_id.value}
                />
              )
        ))}
      </x-view-window>
    )
  }

  /*****************************************************************************
   * 帮助函数
   ****************************************************************************/

  function onChangePageVisibility() {
    if (document.visibilityState === 'hidden') {
      h5Track('video', 'page', 'hide', {
        video_id: currentEpisodeId.value,
        series_id: series_id.value,
        from: from_page.value,
        session_id: sessionId.value,
        video_type: currentEpisode.value?.video_type ? 'free' : 'charge',
      })
      reportNow()
    }
  }
  function onCloseWindow() {
    h5Track('video', 'page', 'close', {
      video_id: currentEpisodeId.value,
      series_id: series_id.value,
      from: from_page.value,
      session_id: sessionId.value,
      video_type: currentEpisode.value?.video_type ? 'free' : 'charge',
    })
    reportNow()
  }
  function onTimeUpdate(t: number, duration: number, episode_id: string) {
    if (t <= 0 || duration <= 0) return
    unlockOverlayVisible.value = false
    manualUnlockPanelVisible.value = false

    if (t / duration > 0.5 && Date.now() - registerTime.value <= time.day) {
      markEpisodeAsPlayedMost(episode_id, series_id.value)
    }
  }
  function onCancelRecharge() {
    unlockOverlayVisible.value = true
  }

  function getZIndex(episodeId?: string) {
    if (!episodeId) return 0
    if (episodeId === currentEpisodeId.value) {
      return 2
    }
    const prev = getPreviousEpisodeById(currentEpisodeId.value)
    const next = getNextEpisodeById(currentEpisodeId.value)
    if (episodeId === prev?.id || episodeId === next?.id) {
      return 1
    }
    return 0
  }

  function swipeToNext() {
    const index = Math.abs(translateY.value / getWindowHeight())
    const targetIndex = index + 1
    const targetY = getWindowHeight() * targetIndex
    tween.value = useTween({
      from: { x: 0, y: translateY.value },
      to: { x: 0, y: -targetY },
      duration: 150,
      onUpdate({ y }) {
        translateY.value = y
      },
      onComplete() {
        // FIXME: 根据 targetIndex 计算 currentEpisodeId
        const list = getEpisodesWithAds()
        const target = list[targetIndex]
        if (!target) return
        if ('kind' in target) {
          currentEpisodeId.value = null
          currentAdId.value = target.id
        } else {
          currentEpisodeId.value = target.id ?? null
          currentAdId.value = null
        }
      },
    })
  }

  async function onFirstPlay(episode_id: string) {
    const episode = findEpisode(episode_id)
    if (episode.unlock !== true) return
    if (everPlayed.value === false) {
      everPlayed.value = true
      if (!episode) return
      if (firstPlayReported.value) return
      h5Track('video', 'page', 'first_play', {
        video_id: episode_id,
        series_id: series_id.value,
        from: from_page.value,
        session_id: sessionId.value,
        video_type: episode.video_type ? 'free' : 'charge',
      }, {
        relative_time: Date.now() - startTime,
      })
      firstPlayReported.value = true
    }
    loading.value = false
    markEpisodeAsPlayed(episode_id)
    await seriesApi.viewDrama({
      series_id: series_id.value,
      episode_id,
    }).catch(keepError((err: unknown) => {
      const error = err as AxiosError
      if (!error.response) return
      const response = error.response
      if (!response?.data) return
      const data = response.data as { code: number }
      if (data.code === 608) { // 移除最老的设备
        void seriesApi.removeOtherDevice()
      }
    }))
  }

  function onError(err: unknown) {
    const error = err as AxiosError
    if (!error.response) return
    const response = error.response
    if (!response?.data) return
    const data = response.data as { code: number, message: string }
    if (data.code === 1000) {
      productPanelVisible.value = true
    } else if (data.code === 1018) {
      showToast(T.seriesItemPage.dontMissOut())
    } else if (data.code === 1019) {
      manualUnlockPanelVisible.value = true
    } else if (data.code === 1020) {
      showToast(CT.unknownError())
    } else {
      unlockOverlayVisible.value = true
    }
  }
  async function onClickUnlock(_episode_id?: string) {
    const episode_id = _episode_id ?? currentEpisode.value?.id ?? ''
    console.log('unlock')
    await unlockEpisode(episode_id).then(() => {
      if (episode_id !== currentEpisode.value?.id) {
        // window.location.href = window.location.origin + `/series/${series_id.value}/${episode_id}?from_episode=prev`
        void betterReplace(router, {
          path: `/series/${series_id.value}/${episode_id}`,
          query: { from_episode: 'prev' },
        })
      }
    }, onError)
  }
  async function onClickManualUnlock(auto_unlock: boolean) {
    await manualUnlockEpisode(currentEpisode.value?.id ?? '', auto_unlock).catch(onError)
  }
  function onEndPlay() {
    // window.location.href = window.location.origin + `/series/${series_id.value}/${getNextEpisode()?.id}?from_episode=prev`
    void betterReplace(router, {
      path: `/series/${series_id.value}/${getNextEpisode()?.id}`,
      query: { from_episode: 'prev' },
    })
  }
  function getPlayUrl(episode: M.Episode) {
    if (!episode) return ''
    const testM3u8 = route.query._test_m3u8
    if (testM3u8) return testM3u8.toString()
    return episode.external_audio_h264_m3u8 || episode.m3u8_url
      || episode.video_url || episode.external_audio_h265_m3u8 || ''
  }

  // #endregion
})
export default SeriesItemPage
