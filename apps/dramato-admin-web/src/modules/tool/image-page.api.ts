import { httpClient } from 'src/lib/http-client'
import axios from 'axios'

export type StsSign = {
  accessid: string
  host: string
  expire: number
  signature: string
  policy: string
  dir: string
  callback: string
  oss_static_video_prefix: string
}

export const apiGetOssSign = (sign: string) => {
  return axios.post(`${import.meta.env.VITE_DRAMA_API_URL}/web/upload/sign`, null, {
    headers: {
      ContentType: 'application/json',
      sign,
    },
  })
}
// 图片增强
export const apiOptImage = (data: {
  oss_path: string
  type: 1 | 2 // type 1 文字抹除  2图片增强
}, sign: string) => {
  return httpClient.post<ApiResponse<StsSign>>('web/image/optimization', data, {
    headers: {
      ContentType: 'application/json',
      sign,
    },
  })
}
