import { encryptedHttpClient } from 'src/lib/encrypted-http-client'
// import { h5HttpClient } from 'src/lib/http-client'

export const HomePageApi = {
  getTabList: () => encryptedHttpClient.get<Api.GetTabList.Response>('/h5-api/homepage/v2/tab/list'),
  getModuleList: (d: Api.GetHomeModuleList.Query) => encryptedHttpClient.get<Api.GetHomeModuleList.Response>('/h5-api/homepage/v2/tab/index', d),
  getFeeds: (d: Api.GetFeed.Query) => encryptedHttpClient.post<Api.GetFeed.Response>('/h5-api/homepage/v2/tab/feed', d),
}
