import { createComponent } from '@skynet/shared'
import { FormItem } from '@skynet/ui'
import { ref, watch } from 'vue'

export const FormItemTypeExamples = createComponent(null, () => {
  const formData = ref<{
    name: string
    city: string
    checked: boolean
    gender: string
    startDate: string
    endDate: string
  }>({
    name: '',
    city: '',
    checked: false,
    gender: 'male',
    startDate: '2000-01-01',
    endDate: '2024-01-01',
  })

  const options = [{
    label: '男',
    value: 'male',
  }, {
    label: '女',
    value: 'female',
  }]

  watch(() => formData.value, () => {
    console.log(formData.value)
  }, { deep: true })

  return () => (
    <form onSubmit={event => event.preventDefault()} class="flex flex-col gap-4 p-4">
      <div>default：</div>
      <FormItem
        type="text"
        placeholder="请输入用户名"
        label="用户名"
        v-model={formData.value.name}
      />
      <div>select：</div>
      <FormItem
        type="select"
        v-model={formData.value.city}
        placeholder="请选择城市"
        label="城市"
        choices={[
          { label: '北京', value: 'beijing' },
          { label: '上海', value: 'shanghai' },
        ]}
      />
      <div>checkbox：</div>
      <FormItem
        type="checkbox"
        label="是否"
        v-model={formData.value.checked}
      />
      <div>radio：</div>
      <FormItem
        type="radio"
        label="性别"
        options={options}
        v-model={formData.value.gender}
      />
      <div>date-range：</div>
      <FormItem
        type="date-range"
        label="日期"
        class="w-120"
        format="YYYY/MM/DD"
        modelValue={[formData.value.startDate, formData.value.endDate]}
        onUpdate:modelValue={value => {
          if (!value) return
          formData.value.startDate = (value as [string, string])[0]
          formData.value.endDate = (value as [string, string])[1]
        }}
      />
    </form>
  )
})

export default FormItemTypeExamples
