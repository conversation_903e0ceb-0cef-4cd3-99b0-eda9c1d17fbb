<template>
  <div>
    <div class="my-4 p-4 bg-white rounded-lg shadow">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="资源ID" class="w-[250px]">
          <el-input v-model="searchForm.resource_id" placeholder="用英文逗号隔开" />
        </el-form-item>
        <el-form-item label="剧情拆解进度" class="w-[200px]">
          <el-select class="!w-[150px]" v-model="searchForm.progress" placeholder="剧情拆解进度">
            <el-option class="w-[150px]" v-for="(item, key) in progressOptions" :key="key" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色标注情况" class="w-[200px]">
          <el-select class="!w-[150px]" v-model="searchForm.role_mark" placeholder="角色标注情况">
            <el-option class="w-[150px]" v-for="(item, key) in roleMarkOptions" :key="key" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="素材情况" class="w-[200px]">
          <el-select class="!w-[150px]" v-model="searchForm.material_status" placeholder="素材情况">
            <el-option class="w-[150px]" v-for="(item, key) in materialStatusOptions" :key="key" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>



        <el-form-item>
          <el-button type="primary" @click="onSubmit">筛选</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
          <el-button type="primary" @click="multiSplit">批量拆剧</el-button>
          <el-button type="primary" :disabled="multipleSelection.length === 0"
            @click="promptPlanPop">Prompt方案</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="push-task-table p-4 bg-white rounded-lg shadow mb-4">
      <el-table @selection-change="handleSelectionChange" :data="list" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />

        <el-table-column prop="auth_status" label="操作" width="150">
          <template #default="scope">
            <el-button type="text" size="mini" :disabled="scope.row.split_acting_status === 1"
              @click="handleSplitActing(scope.row)">剧情拆解</el-button>
            <span>{{ splitStatusOptions[scope.row.split_acting_status].label || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="资源ID" width="80" />
        <el-table-column prop="nickname" label="资源名称" width="200" />

        <el-table-column prop="auth_status" label="拆剧语言" width="150">
          <template #default="scope">
            <span>{{ scope.row.split_acting_language || '-' }}</span>
            <el-button type="text" size="mini" @click="handlePreviewLanguage(scope.row)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="auth_status" label="角色信息" width="150">
          <template #default="scope">
            <el-button v-if="scope.row.role_mark === 1" type="text" size="mini"
              @click="handlePreviewRole(scope.row)">查看</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="auth_status" label="角色标注" width="150">
          <template #default="scope">
            <el-button type="text" size="mini" :disabled="scope.row.role_mark === 1"
              @click="handleSplitMark(scope.row)">标注</el-button>
            <span>{{ roleMarkOptions[scope.row.role_mark].label || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="auth_status" label="台词" width="150">
          <template #default="scope">
            <el-button type="text" size="mini" :disabled="scope.row.role_mark === 1"
              @click="handleSplitMark(scope.row)">标注</el-button>
            <span>{{ roleMarkOptions[scope.row.role_mark].label || '-' }}</span>
          </template>
        </el-table-column>



        <el-table-column prop="uid" label="用户ID" width="300" />
        <el-table-column prop="phone" label="联系方式" width="200" />
        <el-table-column prop="company" label="公司名称" width="200" />
        <el-table-column prop="usci" label="营业执照号" width="200" />
        <el-table-column prop="licence_pic" label="营业执照照片" width="200" />
        <el-table-column prop="auth_status" label="认证状态" width="100">
          <template #default="scope">
            {{ progressOptions[scope.row.auth_status].label || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="auth_user" label="审核人" width="200" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <div class="flex gap-2">
              <el-button type="primary" size="mini" @click="handlePreview(scope.row)">审核</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container my-2">
        <el-pagination v-model:current-page="searchForm.page_index" v-model:page-size="searchForm.page_size"
          :total="total" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>

    </div>
    <el-dialog v-model="dialogVisible" title="Prompt方案" width="70%">
      <div>
        <el-form :model="formData" label-width="120px">
          <el-form-item label="默认方案">
            <el-select v-model="formData.default_plan" placeholder="请选择">
              <el-option v-for="item in defaultPlanOptions" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
            <el-button type="primary" @click="handleDefaultPlan">确定</el-button>
          </el-form-item>
          <div>
            <el-form-item label="Prompt管理">
              <el-button type="primary" @click="handlePromptAdd">添加</el-button>
            </el-form-item>
          </div>
          <div>
            内容
          </div>
        </el-form>
        <div class="flex justify-end">
          <el-button type="primary" @click="handleSubmit">通过</el-button>
          <el-button type="danger" @click="handleReject">不通过</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { Plus, Delete, Download, InfoFilled } from '@element-plus/icons-vue'
import { useMaterialTools } from './use-material-tools';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute, useRouter } from 'vue-router'
import { Uploader } from '../common/uploader/uploader'

const {
  progressOptions,
  roleMarkOptions,
  materialStatusOptions,
  onSearchRoleList,
  list,
  loading,
  searchForm,
  total,
  onReset,
  multiSplit
} = useMaterialTools()


const route = useRoute()
const router = useRouter()

const dialogVisible = ref(false)
const formData = ref({
  default_plan: 1,
})

const defaultPlanOptions = [
  { label: '方案1', value: 1 },
  { label: '方案2', value: 2 },
  { label: '方案3', value: 3 },
]
const splitActingStatus = ref(1)
const splitStatusOptions = [
  { label: '未开始', value: 0 },
  { label: '进行中', value: 1 },
  { label: '已完成', value: 2 },
  { label: '已失败', value: 3 },
]
const markStatusOptions = [
  { label: '未标注', value: 0 },
  { label: '已标注', value: 1 }
]
const handleSplitMark = (row: M.SplitActingItem) => {
  console.log('handleSplitMark', row)
}

const handleDefaultPlan = () => {
  console.log('handleDefaultPlan')
}

const handlePreviewRole = (row: M.SplitActingItem) => {
  console.log('handlePreviewRole', row)
}

const promptPlanPop = () => {
  dialogVisible.value = true
}

const handlePromptAdd = () => {
  console.log('handlePromptAdd')
}

const handleSplitActing = (row: M.SplitActingItem) => {
  console.log('handleSplitActing', row)
}

const handlePreviewLanguage = (row: M.SplitActingItem) => {
  console.log('handlePreviewLanguage', row)
}

// 处理操作方法
const handlePreview = (row: M.SplitActingItem) => {
  formData.value = JSON.parse(JSON.stringify(row))
  dialogVisible.value = true
}


const handleSubmit = () => {
  ElMessageBox.confirm('确定通过吗？', '提示', {
    confirmButtonText: '通过',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    console.log('handleSubmit')
  })
}

const handleReject = () => {
  ElMessageBox.prompt('请输入不通过原因', '提示', {
    confirmButtonText: '不通过',
    cancelButtonText: '取消',
    type: 'warning',
  }).then((res: any) => {
    console.log('handleReject', res)
  })
}












// ----------------
onMounted(() => {
  console.log('mounted')
  onSearchRoleList();

})
const multipleSelection = ref<M.SplitActingItem[]>([])

const onSubmit = () => {
  console.log('submit!')
  console.log(searchForm)
  onSearchRoleList();
}



const handleSelectionChange = (val: M.SplitActingItem[]) => {
  multipleSelection.value = val
}

// 处理页码改变
const handleCurrentChange = (val: number): void => {
  searchForm.value.page_index = val
  onSearchRoleList()
}

// 处理每页条数改变
const handleSizeChange = (val: number): void => {
  searchForm.value.page_size = val
  onSearchRoleList()
}

</script>

<style scoped></style>./use-material-tools