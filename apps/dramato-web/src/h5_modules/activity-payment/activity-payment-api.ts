import { encryptedHttpClient } from 'src/lib/encrypted-http-client'
// import { h5HttpClient } from 'src/lib/http-client'

export const apiGetProductList = (params: { uid: number, country_code: string }) => {
  return encryptedHttpClient.get<ApiResponse<M.ActivityPayment.PaymentResponse>>('/drama-api/wallet/h5/store/list', params)
}

export const apiUidLogin = (uid: number) => {
  return encryptedHttpClient.get<ApiResponse<{
    user_avatar: string
  }>>('/drama-api/wallet/h5/uid/login', { uid })
}

export const apiGetAliwallexCardOrder = (params: M.ActivityPayment.AliwallexPaymentParams) => {
  return encryptedHttpClient.get<ApiResponse<M.ActivityPayment.AliwallexPaymentResponse>>('/drama-api/wallet/h5/payment/airwallex/purchase', params)
}

export const apiGetShareStatus = (uid: number) => {
  return encryptedHttpClient.post<ApiResponse<{
    status: number // 状态 0:未参与 1:未推荐成功 2：推荐成功且赠金未领取 3：推荐成功且赠金已领取
    referer_avatar: string // 推荐人头像 (默认头像返回空)
    referee_avatar: string // 被推荐人头像 （默认头像返回空）
    bonus: number
  }>>('/h5-api/activity/recharge/relation', { uid })
}

export const apiRechargeShare = (uid: number) => {
  return encryptedHttpClient.post<ApiResponse<{
    bonus: number
  }>>('/h5-api/activity/recharge/share', { uid })
}

export const apiReceiveBonus = (uid: number, bonus: number) => {
  return encryptedHttpClient.post<ApiResponse<{
    bonus: number
  }>>('/h5-api/activity/recharge/bonus', { uid, bonus })
}

export const apiGetBanner = () => {
  return encryptedHttpClient.post<ApiResponse<{
    banner_items: M.ActivityPayment.Banner[]
  }>>('/h5-api/activity/banner', { key: 'most_trending_rank' })
}
