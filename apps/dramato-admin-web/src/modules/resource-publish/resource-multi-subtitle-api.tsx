import { httpClient } from 'src/lib/http-client'
import { checkResourceTitleOrId, checkEpisodeTitleOrId } from './util'

const toNumArr = (value: string[]) => {
  return value.map(v => +v)
}

const dateCheck = (value?: number) => {
  if (!value) return 0
  return value
}

export const apiGetMultiSubtitleList = (data: Api.MultiSubtitle.Request) =>
  httpClient.post<Api.MultiSubtitle.Response>('/series_resource_v2/multi_lang_list', data, {
    transformRequestData: {
      series_key_or_title_list: [checkEpisodeTitleOrId],
      resource_id_or_title_list: [checkResourceTitleOrId],
      create_start_time: [dateCheck],
      create_end_time: [dateCheck],
    },
  })
