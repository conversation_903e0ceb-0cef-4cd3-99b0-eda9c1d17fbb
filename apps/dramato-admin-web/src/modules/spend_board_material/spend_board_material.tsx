/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, openDialog, Pager, Switch, transformNumber, transformTimestamp } from '@skynet/ui'
import dayjs from 'dayjs'
import { ElPagination, ElTable, ElTableColumn } from 'element-plus'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref, withModifiers } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { apiGetDeployCalendar } from './spend_board_material-api'
import SpendBoardMaterialItem from './spend_board_material-item'
import { useSpendBoardMaterial } from './use-spend_board_material'
type SpendBoardMaterialOptions = {
  props: {}
}

export const SpendBoardMaterial = createComponent<SpendBoardMaterialOptions>({
  props: {},
}, props => {
  const route = useRoute()
  const router = useRouter()
  const {
    Form,
    params,
    total,
    search,
    list,
    dramaList,
    dramaLoading,
    selectDramas,
    onSearchDrama,
    onUpdateDrama,
    limitText,
    clearAllDrama,
    resourceList,
    onSearchResource,
    selectResources,
    resourceLoading,
    onUpdateResource,
    clearAllResource,
  } = useSpendBoardMaterial()

  const seeAllData = ref<boolean>(false)
  const seeAllResource = ref<boolean>(false)

  onMounted(() => {
    void search(1)
  })

  const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[1000px] overflow-hidden'
  const calendarList = ref<M.SpendBoard.DeployCalendarItem[]>([])
  const calendarPage = ref({
    page_index: 1,
    page_size: 20,
    total: 0,
  })
  const getDeployCalendar = async () => {
    const res = await apiGetDeployCalendar({
      page_index: calendarPage.value.page_index,
      page_size: calendarPage.value.page_size,
    })
    if (res.data) {
      calendarList.value = res.data.list
      calendarPage.value.total = res.data.total
    }
  }

  const onClickCalendar = () => {
    void getDeployCalendar()

    const hide = openDialog({
      title: '首发日历（同步近7天首发剧）',
      body: () => (
        <div>
          <ElTable height="500px" v-model:data={calendarList.value} style={{ width: '100%' }}>
            <ElTableColumn label="资源ID-资源名" prop="series_key">
              {{
                default: (scope: any) => (
                  <span class="cursor-pointer" onClick={() => {
                    if (scope.row.status == 2) {
                      params.value.series_resource_id = String(scope.row.id)
                      void search(1)
                      hide()
                    }
                  }}>{scope.row.id}-{scope.row.title}</span>
                ),
              }}
            </ElTableColumn>
            <ElTableColumn label="可投放时间" prop="deploy_time">
              {{
                default: (scope: any) => (
                  scope.row.deploy_time ? dayjs(scope.row.deploy_time * 1000).format('YYYY-MM-DD HH:mm') : '-'
                ),
              }}
            </ElTableColumn>
            <ElTableColumn label="资源状态" prop="status">
              {{
                default: (scope: any) => (
                  <span onClick={() => {
                    if (scope.row.status == 2) {
                      params.value.series_resource_id = String(scope.row.id)
                      void search(1)
                      hide()
                    }
                  }}>{['', '审核中', '可剪辑'][scope.row.status]}</span>
                ),
              }}
            </ElTableColumn>
          </ElTable>
          <div class="flex justify-end mt-2">
            <ElPagination
              v-model:current-page={calendarPage.value.page_index}
              onUpdate:current-page={(e: number) => {
                calendarPage.value.page_index = e
                void getDeployCalendar()
              }}
              v-model:page-size={calendarPage.value.page_size}
              total={calendarPage.value.total}
              layout="prev, pager, next"
              small={true}
            />
          </div>
        </div>
      ),
      mainClass: `${dialogMainClass} !w-full`,
      customClass: '!w-[600px] overflow-hidden',
    })
  }

  return () => (
    <NavFormTablePager tableClass="bg-none">
      {{
        nav: () => (
          <ul>
            <li>短剧投放监控</li>
            {/* {JSON.stringify(selectDramas.value)} */}
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = {
                page_index: 1,
                page_size: 20,
                // task_time_start: dayjs(dayjs().format('YYYY-MM-DD')).unix(),
                // task_time_end: dayjs(dayjs().format('YYYY-MM-DD')).unix(),
              }
              void search(1)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              [
                '资源名/资源ID',
                'series_resource_id',
                {
                  type: 'custom',
                  render: () => (
                    <div>
                      <multiselect class="spend-board-material h-8" modelValue={selectResources.value} onUpdate:modelValue={onUpdateResource} id="ajax" label="resource_name" track-by="resource_id" placeholder="输入资源名搜索"
                        open-direction="bottom" options={resourceList.value} multiple={true} searchable={true} loading={resourceLoading.value}
                        internal-search={false} clear-on-select={false} close-on-select={false} options-limit={300}
                        limit={3} limit-text={limitText} max-height={600} show-no-results={false} hide-selected={true}
                        onSearchChange={onSearchResource}
                      >
                        {{
                          noResult: () => (
                            <span>没有找到结果，试试换个搜索词</span>
                          ),
                          tag: ({ option, remove }: { option: any, remove: any }) => (
                            <span class="custom__tag"><span>{option.resource_name}</span>
                              <span
                                onClick={withModifiers(() => remove(option), ['stop', 'prevent'])}
                                class="custom__remove"
                              >❌
                              </span>
                            </span>
                          ),
                          clear: () => (
                            <div class="multiselect__clear"
                              onMousedown={withModifiers(clearAllResource, ['stop', 'prevent'])}
                            />
                          ),

                        }}
                      </multiselect>
                    </div>
                  ),
                },
              ],
              [
                '剧名/剧ID',
                'series_key',
                {
                  type: 'custom',
                  render: () => (
                    <div>
                      <multiselect class="spend-board-material h-8" modelValue={selectDramas.value} onUpdate:modelValue={onUpdateDrama} id="ajax" label="drama_name" track-by="series_key" placeholder="输入剧名搜索"
                        open-direction="bottom" options={dramaList.value} multiple={true} searchable={true} loading={dramaLoading.value}
                        internal-search={false} clear-on-select={false} close-on-select={false} options-limit={300}
                        limit={3} limit-text={limitText} max-height={600} show-no-results={false} hide-selected={true}
                        onSearchChange={onSearchDrama}
                      >
                        {{
                          noResult: () => (
                            <span>没有找到结果，试试换个搜索词</span>
                          ),
                          tag: ({ option, remove }: { option: any, remove: any }) => (
                            <span class="custom__tag"><span>{option.drama_name}</span>
                              <span
                                onClick={withModifiers(() => remove(option), ['stop', 'prevent'])}
                                class="custom__remove"
                              >❌
                              </span>
                            </span>
                          ),
                          clear: () => (
                            <div class="multiselect__clear"
                              onMousedown={withModifiers(clearAllDrama, ['stop', 'prevent'])}
                            />
                          ),

                        }}
                      </multiselect>
                    </div>
                  ),
                },
              ],
              [
                '剧集语言',
                'language',
                {
                  type: 'select',
                  options: [
                    { value: 'ja', label: '日语' },
                    { value: 'en', label: '英语' },
                    { value: 'ko', label: '韩语' },
                    { value: 'es', label: '西班牙语' },
                    { value: 'th', label: '泰语' },
                    { value: 'id', label: '印尼语' },
                    { value: 'vi', label: '越南语' },
                    { value: 'pt', label: '葡萄牙语' },
                    { value: 'tl', label: '菲律宾语' },
                    { value: 'it', label: '意大利语' },
                    { value: 'fr', label: '法语' },
                    { value: 'de', label: '德语' },
                    { value: 'tr', label: '土耳其语' },
                    { value: 'ru', label: '俄语' },
                    { value: 'ms', label: '马来西亚语' },
                    { value: 'zh-TW', label: '繁体中文' },
                    { value: 'hi', label: '印地语' },
                  ],
                },
              ],
              [
                '上架状态',
                'deploy_status', // 1 未上架 2 待上架 3 已上架 4 待下架 -1 已下架
                {
                  type: 'select',
                  options: [
                    { value: 1, label: '待上架' },
                    { value: 2, label: '已上架' },
                    { value: 3, label: '已推送' },
                    { value: 4, label: '素材中' },
                    { value: 5, label: '投放中' },
                  ],
                },
                {
                  transform: transformNumber,
                },
              ],
              ['任务日期', 'task_time_start', { type: 'date', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD' }, {
                transform: transformTimestamp,
              }],
              [<div class="h-6 w-1" />, '', { type: 'custom', render: () => <div class="h-[34px] leading-[34px] text-gray-400">至</div> }],
              [<div class="h-6 w-1" />, 'task_time_end', { type: 'date', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD' }, {
                transform: transformTimestamp,
              }],
              [
                '商务评级',
                'content_rating',
                {
                  type: 'select',
                  options: [
                    { value: 'S', label: 'S' },
                    { value: 'A', label: 'A' },
                    { value: 'B', label: 'B' },
                    { value: 'C', label: 'C' },
                  ],
                },
              ],
              [
                '剪辑师评级',
                'editor_rating',
                {
                  type: 'select',
                  options: [
                    { value: 1, label: '1' },
                    { value: 2, label: '2' },
                    { value: 3, label: '3' },
                    { value: 4, label: '4' },
                    { value: 5, label: '5' },
                    { value: 6, label: '6' },
                    { value: 7, label: '7' },
                    { value: 8, label: '8' },
                    { value: 9, label: '9' },
                    { value: 10, label: '10' },
                  ],
                },
                {
                  // transform: transformNumber,
                },
              ],
              [
                '发行轮次',
                'resource_round',
                {
                  type: 'select',
                  options: [
                    { value: 1, label: '首发' },
                    { value: 2, label: '二轮' },
                  ],
                },
              ],
              [
                '发行状态',
                'release_status',
                {
                  type: 'select',
                  options: [
                    { value: 0, label: '关闭' },
                    { value: 1, label: '开启' },
                  ],
                },
              ],
              [
                '只看推荐',
                'recommend',
                {
                  type: 'select',
                  options: [
                    { value: 1, label: '推荐' },
                    { value: 2, label: '不推荐' },
                  ],
                },
              ],
              [
                '只看今日',
                'only_today',
                {
                  type: 'radio',
                  options: [
                    { value: true, label: '是' },
                    { value: false, label: '否' },
                  ],
                },
              ],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex w-full items-center justify-between">
            <span />
            <x-table-actions-right class="flex gap-x-2">
              <Button class="btn btn-primary btn-sm" onClick={onClickCalendar}>投放日历</Button>
              <x-warp-desc class="flex items-center gap-x-2">
                <x-label>只看资源：</x-label>
                <Switch
                  modelValue={seeAllResource.value}
                  onUpdate:modelValue={v => {
                    seeAllResource.value = v

                    console.log('seeAllResource.value', seeAllResource.value, v)
                  }}
                  labels={{ on: '开', off: '关' }}
                />
              </x-warp-desc>
              <x-warp-desc class="flex items-center gap-x-2">
                <x-label>只看投放数据：</x-label>
                <Switch
                  modelValue={seeAllData.value}
                  onUpdate:modelValue={v => {
                    seeAllData.value = v

                    console.log('seeAllData.value', seeAllData.value, v)
                  }}
                  labels={{ on: '开', off: '关' }}
                />
              </x-warp-desc>
            </x-table-actions-right>

          </x-table-actions>
        ),
        table: () => (
          <x-list class="flex w-full flex-col gap-y-2">
            {
              list.value.length <= 0
                ? <div class="py-8 text-center">数据为空</div>
                : list.value.map(i => <SpendBoardMaterialItem key={i.series_resource_id} i={i} seeAllResource={seeAllResource.value} seeAllData={seeAllData.value} />)
            }
          </x-list>
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={params.value.page_index}
                  v-model:size={params.value.page_size}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(params.value.page_index)
                  }}
                  onUpdate:size={() => {
                    void search(params.value.page_index)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})
