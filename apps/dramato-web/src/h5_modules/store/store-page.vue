<template>
  <x-store-page
    class="bg-top-center flex flex-col bg-[url('src/h5_modules/images/store-bg.webp')] bg-no-repeat text-[#fdfbfc]">
    <Back :is-white="true" :title="T.storePage.store.ref.value" back-to="/profile"
      class="z-100 sticky top-0 flex h-11 w-full shrink-0 items-center bg-transparent font-bold" />
    <div
      class="fineBorder mx-3 mt-5 flex items-center justify-between rounded-lg bg-gradient-to-t from-[#0c090d] to-[#1b1722] py-5">
      <div class="w-50% text-center">
        <p class="text-2xl font-bold">
          {{ myWallet.cash_balance }}
        </p>
        <p class="text-xs text-[#797b7d]">
          {{ T.storePage.coinsText.ref.value }}
        </p>
      </div>
      <div class="h-10 w-px bg-[#2e2f30]" />
      <div class="w-50% text-center">
        <p class="text-2xl font-bold">
          {{ myWallet.bonus_balance }}
        </p>
        <p class="text-xs text-[#797b7d]">
          {{ T.storePage.rewardCoins.ref.value }}
        </p>
      </div>
    </div>
    <div class="scrollArea h-[calc(100vh_-_var(--vh-offset,0px)_-_10rem)] overflow-y-auto">
      <div class="content  px-3">
        <p class="pb-3 pt-4 text-base text-[#cccacb]">
          {{ T.storePage.coinsText.ref.value }}
        </p>
        <div class="flex flex-wrap gap-4">
          <template v-if="productData.recharge_list">
            <div v-for="item in productData.recharge_list" :key="item.product_id"
              :class="{ 'coinActive': productId === item.product_id }"
              class="fineBorder relative w-[calc(50%-0.5rem)] flex-col overflow-hidden rounded-lg bg-[#1d1d1e] p-3 text-[#cccacb]"
              @click="() => {
                productId = item.product_id
                vipSelectedId = 0
                h5Track('my_wallet', 'purchase', 'click', {
                  series_id: route.params.series_id as string,
                  video_id: route.params.episode_id as string,
                  product_id: item.product_id,
                })
              }">
              <p class="ml-[-2px] flex items-center">
                <SvgIcon name="p-coins-fill" class="mr-[2px] size-4 shrink-0" />
                <span class="text-4.5 font-bold text-[#fdfbfc]">{{ item?.delivery_details?.quanity }}</span>
                <span v-if="item?.delivery_details?.bonus" class="ml-1 text-sm">+{{ item?.delivery_details?.bonus
                  }}</span>
              </p>
              <p class="text-sm">
                {{ item?.currency_symbol }} {{ item?.discount_price / 100 }}
              </p>
              <div v-if="productId === item.product_id" class="coinActiveBg" />
              <div v-if="item.slogan" style="background: linear-gradient(90deg, #F418EF 0%, #FF2D3F 50%);"
                class="slogan absolute right-0 top-0 flex items-center rounded-bl px-1 py-0.5 text-[10px] font-bold leading-3 text-white">
                <!-- <span v-if="item.slogan?.indexOf('%') > -1">+</span> -->
                {{ item.slogan }}
                <SvgIcon v-if="item.slogan?.indexOf('%') > -1" name="ic_up" class="size-3" />
              </div>
            </div>
          </template>
        </div>
        <!-- vip块 -->
        <div v-for="(item) in productData.membership" :key="item.id"
          class="relative mt-3 flex w-full overflow-hidden rounded-lg border border-solid border-line-2 p-3 transition-all"
          :class="productId === item.product_id && 'border-line-4 border-solid border'"
          :style="{ background: productId === item.product_id ? 'linear-gradient(90deg, #5C3F21 0%, #20160C 100%)' : 'linear-gradient(90deg, #352616 0%, #080808 100%)' }"
          @click="() => {
            productId = item.product_id
            vipSelectedId = 0
            if (selectedPayChannel === 'Paypal') {
              setDefaultPayChannel();
            }
            h5Track('my_wallet', 'subscription', 'click', {
              series_id: route.params.series_id as string,
              video_id: route.params.episode_id as string,
              pay_channel: payChannel,
              pay_sub_channel: paySubChannel,
              price: item.discount_price / 100,
              currency: item.currency,
            })
          }">
          <div class="desc flex flex-1 flex-col gap-1 ">
            <div class="title flex h-5 items-center gap-1 text-base font-bold text-text-12">
              <img :src="Crown" class="size-5">
              {{ item.title }}
            </div>
            <div class="subtitle text-xs font-normal text-text-2">
              {{ item.description || ' ' }}
            </div>
            <div class="tip text-[10px] text-text-8">
              {{ item.discount_desc }}
            </div>
          </div>
          <div class="flex flex-col items-end justify-center">
            <div class="price text-lg font-bold text-text-12">
              {{ item.currency_symbol }}{{ item.discount_price / 100 }}
            </div>
            <div class="origin-price text-xs text-text-3 line-through">
              {{ item.currency_symbol }}{{ item.price / 100 }}
            </div>
          </div>
          <div v-if="productId === item.product_id" class="coinActiveBg" />
        </div>
        <!-- 支付方式 -->
        <div v-if="productData?.channel_list"
          class="fixed bottom-0 left-0 z-up mt-1 w-full bg-black px-3 py-2 text-text-2">
          <div class="mb-3">
            {{ PT.paymentPage.paymentMethod.ref }}
          </div>
          <div class="flex w-[calc(100%+0.75rem)] overflow-x-auto overflow-y-hidden pb-0.5 pr-3">
            <div class="flex gap-3">
              <div v-for="item in orderedProductList" :key="item.pay_channel" :class="getPayMethodClass(item)"
                class="relative flex h-[3.1875rem] w-[6.5rem] shrink-0 items-center justify-center overflow-hidden rounded-lg border border-solid border-line-2 bg-[var(--grey-13)] px-3"
                :style="selectedPayChannel === item.pay_channel && { background: 'var(--grey-13)', 'borderColor': '#fff8e6' }"
                @click="() => {
                  selectedPayChannel = item.pay_channel
                  payChannel = item.payment_channel
                  paySubChannel = item.sub_payment_channel
                  h5Track('pay_unlock', 'channel', 'click', {
                    series_id: route.params.series_id as string,
                    video_id: route.params.episode_id as string,
                    pay_channel: item.pay_channel,
                    pay_sub_channel: item.sub_payment_channel,
                  })
                  h5Track('my_wallet', 'channel', 'click', {
                    series_id: route.params.series_id as string,
                    video_id: route.params.episode_id as string,
                    pay_channel: item.pay_channel,
                    sub_pay_channel: item.sub_payment_channel,
                  })
                }">
                <Image class="w-20" :src="item.icon" />
                <div v-if="selectedPayChannel === item.pay_channel" class="channelActiveBg" />
              </div>
            </div>
          </div>
          <div class="mt-5 flex h-11 w-full items-center justify-center rounded-lg bg-brand-6 font-medium text-white"
            @click="() => {
              handlePay({
                from: curPath,
                series_id: route.params.series_id as string,
                video_id: route.params.episode_id as string,
                video_type: '',
              })
              h5Track('my_wallet', 'payment', 'click', {
                series_id: route.params.series_id as string,
                video_id: route.params.episode_id as string,
                pay_channel: payChannel,
                sub_pay_channel: paySubChannel,
              })
            }">
            {{ PT.paymentPage.payNow.ref }} {{ selectedProduct?.currency_symbol }} {{ (selectedProduct?.discount_price
              || 0) /
              100 }}
          </div>
        </div>
      </div>

      <div class="mb-44  mt-2 flex flex-col gap-2 px-3 text-xs leading-4 text-[#434546]">
        <p>{{ T.storePage.tips.ref.value }}</p>
        <p>{{ T.storePage.rule_1.ref.value }}</p>
        <p>{{ T.storePage.rule_2.ref.value }}</p>
        <p>{{ T.storePage.rule_3.ref.value }}</p>
        <p>{{ T.storePage.rule_4.ref.value }}</p>
        <p>{{ T.storePage.rule_5.ref.value }}</p>
        <p>{{ T.storePage.rule_6.ref.value }}</p>
        <p>{{ T.storePage.rule_7.ref.value }}</p>
        <p>{{ T.storePage.rule_8.ref.value }}</p>
        <p>{{ T.storePage.rule_9.ref.value }}</p>
      </div>
    </div>
    <SvgIcon v-if="payLoading" name="payment/loading" class="fixed inset-0 m-auto size-8 animate-spin" />
    <Popup v-model:show="showPop" :destroy-on-close="true" :close-on-click-overlay="true" position="bottom"
      :style="{ height: '220px' }" class="rounded-t-lg">
      <PayThirdButton :pay-channel="payChannel" :pay-sub-channel="paySubChannel" :type="thirdType"
        :product-id="productId" :from="curPath" :currency="selectedProduct?.currency" @click-rule="gotoRules" />
    </Popup>
  </x-store-page>
</template>
<script setup lang="ts">
import { Popup, Image } from 'vant';
import { SvgIcon } from '@skynet/ui'
import { ref, onMounted, computed } from 'vue';
import { Back } from 'src/h5_modules/common/back/back'
import { apiProductList } from './store-api'
import { StoreDataInfo, MyWalletInfo } from './store'
import { apiWalletMy } from 'src/h5_modules/profile/profile-api'
import { T } from './store-page.i18n'
import { T as PT } from '../payment/payment-page.i18n'
import { h5Track } from 'src/lib/h5-track';
import { useRoute, useRouter } from 'vue-router'
import { usePayment } from '../payment/use-payment';
import { gotoRules } from 'src/h5_modules/common/common'
import PayThirdButton from '../payment/payment-third-button.vue'
import { isIos, isTikTok } from 'src/lib/ua'
import { Crown } from 'src/h5_modules/payment/images/images'
import { getUser } from '../common/user';
const user = getUser()

const { productId, handlePay, thirdType, showPop, payChannel, paySubChannel,
  selectedPayChannel, payLoading, productData, getProductList, getPayMethodClass,
  orderedProductList, selectedProduct, isRecurringSelected, setDefaultPayChannel } = usePayment()

const route = useRoute()
const curPath = encodeURIComponent((window.location.pathname + window.location.search).slice(1))

const myWallet = ref<MyWalletInfo>({
  cash_balance: 0,
  bonus_balance: 0
})

const vipSelectedId = ref(0);


const getMyWallet = async () => {
  const res = await apiWalletMy();
  if (res.data && res.data) {
    myWallet.value = {
      cash_balance: res.data?.cash_balance ? res.data?.cash_balance : 0,
      bonus_balance: res.data?.bonus_balance ? res.data?.bonus_balance : 0
    }
  }
}

onMounted(() => {
  getProductList().then(() => {
    h5Track('my_wallet', 'channel', 'show', {
      series_id: route.params.series_id as string,
      video_id: route.params.episode_id as string,
      payment_channel: orderedProductList.value.map(item => item.payment_channel).join(','),
      sub_pay_channel: orderedProductList.value.map(item => item.sub_payment_channel).join(','),
    })
    // productData的打点要获取到才能打点
    h5Track('my_wallet', 'store_page', 'show', {
      conins: productData.value.recharge_list.length > 0 ? 1 : 0, // 0=未展示；1=展示
      vip: productData.value.membership.length > 0 ? 1 : 0,
    })
  });
  getMyWallet();
})

</script>
<style scoped>
.vipActive {
  background-image: linear-gradient(to right, #5b3f21, #1f160c);
  border: 1px #FFF8E6 solid;
  transition: all 0.3s ease;
}

.fineBorder {
  border: 1px #434546 solid;
}

.coinActive {
  border-color: #FFF8E6;
  transition: all 0.3s ease;
}

.coinActiveBg {
  width: 5rem;
  height: 14rem;
  left: 4.5rem;
  top: -3rem;
  transition: all 0.3s ease;
  position: absolute;
  transform-Origin: top left;
  transform: rotate(30deg);
  background: linear-gradient(270deg, rgba(253, 215, 121, 0) 0%, rgba(253, 215, 121, 0.20) 50%, rgba(253, 215, 121, 0) 100%);
  animation: shine 2s ease-in-out infinite;
}

.channelActiveBg {
  width: 5rem;
  height: 14rem;
  left: 2.5rem;
  top: -3rem;
  transition: all 0.3s ease;
  position: absolute;
  transform-Origin: top left;
  transform: rotate(30deg);
  background: linear-gradient(270deg, rgba(253, 215, 121, 0) 0%, rgba(253, 215, 121, 0.20) 50%, rgba(253, 215, 121, 0) 100%);
  animation: shine 2s ease-in-out infinite;
}

@keyframes shine {
  0% {
    left: -25%;
  }

  100% {
    left: 125%;
  }
}
</style>
