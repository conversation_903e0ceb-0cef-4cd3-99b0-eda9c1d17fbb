import { parse } from 'csv-parse/sync'
import { set, merge } from 'lodash-es'
import fs from 'fs'
import path from 'path'

const userInput = process.argv.slice(2)
// i18n的namespace
const pageName = 'memberCenterPage'
// 需要修改的文件地址
const targetFile = 'src/h5_modules/member-center/member-center.i18n.ts'
// csv文件路径
const csvFile = path.resolve(process.cwd(), './bin/i18n.csv')
// csv中的字段名称
const keyNameKey = '字段（可自行定义）'

const langs = ['en-US', 'ja-JP', 'ko-KR', 'es-ES', 'pt-PT', 'vi-VN', 'th-TH', 'id-ID', 'tl-PH', 'fr-FR', 'de-DE', 'it-IT', 'ru-RU', 'tr-TR', 'ms-MY', 'zh-TW', 'zh-CN']
const keyMap = {
  "en-US": "EN",
  "ja-JP": "ja",
  "ko-KR": "ko",
  "es-ES": "es",
  "pt-PT": "pt",
  "vi-VN": "vi(VN)",
  "th-TH": "th",
  "id-ID": "id",
  "tl-PH": "tl",
  "fr-FR": "fr",
  "de-DE": "Ge (De)",
  "it-IT": "It",
  "ru-RU": "Ru",
  "tr-TR": "Tu（TR)",
  "ms-MY": "马来语MS",
  "zh-TW": "繁体中文",
  "zh-CN": "简中"
}

// 读取现有的 i18n 文件
const readExistingI18n = () => {
  const content = fs.readFileSync(path.resolve(process.cwd(), targetFile), 'utf-8')
  // 提取现有的翻译对象
  const match = content.match(/mergeI18n\(\{([\s\S]*?)\}\)/)
  if (!match) return {}

  try {
    return eval(`({${match[1]}})`)
  } catch (e) {
    console.error('解析现有翻译失败:', e)
    return {}
  }
}

// 读取并解析 CSV 文件
const readCsvData = () => {
  const content = fs.readFileSync(csvFile, 'utf-8')
  return parse(content, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
    quote: '"',
    escape: '"',
    relax_quotes: true,  // Add this to be more forgiving with quotes
    relax_column_count: true  // Add this to handle inconsistent column counts
  })
}

// 生成新的翻译
const generateNewTranslations = () => {
  const newTranslations = {}
  const csvData = readCsvData()

  for (const key of userInput) {
    if (csvData.some(obj => obj[keyNameKey] === key)) {
      const object = csvData.find(obj => obj[keyNameKey] === key)
      const keyName = object[keyNameKey]

      for (const lang of langs) {
        const value = object[keyMap[lang]]
        if (value) {
          set(newTranslations, `${lang}.${pageName}.${keyName}`, value)
          console.log(`已添加翻译：${lang}.${pageName}.${keyName} = ${value}`)
        }
      }

    } else {
      console.log(`键 "${key}" 不存在于 CSV 文件中。`)
    }
  }

  return newTranslations
}

// 合并并写入文件
const mergeAndWrite = (existing, newContent) => {
  // 深度合并现有翻译和新翻译
  const merged = merge({}, existing, newContent)

  const fileContent = `import { mergeI18n } from 'src/h5_modules/i18n/i18n'

export const T = mergeI18n(${JSON.stringify(merged, null, 2)})
`

  fs.writeFileSync(path.resolve(process.cwd(), targetFile), fileContent)
  console.log('翻译已成功合并并写入文件！')
}

// 主流程
const main = () => {
  if (userInput.length === 0) {
    console.log('请输入要添加的键值对的键，用空格分隔。')
    process.exit(1)
  }

  const existing = readExistingI18n()
  const newTranslations = generateNewTranslations()
  mergeAndWrite(existing, newTranslations)
}

main()