{
  "folders": [
    {
      "name": "root",
      "path": ".",
    },
    {
      "name": "server",
      "path": "apps/server",
    },
    {
      "name": "dramato-web",
      "path": "apps/dramato-web",
    },
    {
      "name": "dramato-admin-web",
      "path": "apps/dramato-admin-web",
    },
    {
      "name": "free-web",
      "path": "apps/free-web",
    },
    {
      "name": "open-platform",
      "path": "apps/open-platform",
    },
    {
      "name": "ui-showcase",
      "path": "apps/ui-showcase",
    },
    {
      "name": "ui",
      "path": "packages/ui",
    },
  ],
  "settings": {
    // 设置 tab size 为 2
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "[jsonc]": {
      "editor.tabSize": 4
    },
    "[markdown]": {
      "editor.tabSize": 4
    },
    // We don't want to show duplicates, so we hide the directories that are already showing up as workspaces
    "files.exclude": {
      "apps/dramato-web": true,
      "apps/dramato-admin-web": true,
      "apps/free-web": true,
      "apps/server": true,
      "apps/open-platform": true,
      "packages/ui": true,
      "**/rules/**": true,
      "**/recharge-h5/**": true,
    },
    "workbench.colorCustomizations": {
      "[Default Light Modern]": {
        "titleBar.activeBackground": "#462db5",
        "titleBar.activeForeground": "#ffffff",
        "statusBar.background": "#462db5",
        "statusBar.foreground": "#ffffff"
      }
    },
    "search.exclude": {
      "**/node_modules": true,
      "**/dist": true,
    },
    "files.associations": {
      "*.css": "tailwindcss"
    },
    "editor.quickSuggestions": {
      "strings": "on"
    },
    "eslint.format.enable": true,
    "eslint.codeAction.showDocumentation": {
      "enable": true
    },
    "eslint.quiet": true,
    // Silent the stylistic rules in you IDE, but still auto fix them
    "eslint.rules.customizations": [
      {
        "rule": "style/*",
        "severity": "off"
      },
      {
        "rule": "format/*",
        "severity": "off"
      },
      {
        "rule": "*-indent",
        "severity": "off"
      },
      {
        "rule": "*-spacing",
        "severity": "off"
      },
      {
        "rule": "*-spaces",
        "severity": "off"
      },
      {
        "rule": "*-order",
        "severity": "off"
      },
      {
        "rule": "*-dangle",
        "severity": "off"
      },
      {
        "rule": "*-newline",
        "severity": "off"
      },
      {
        "rule": "*quotes",
        "severity": "off"
      },
      {
        "rule": "*semi",
        "severity": "off"
      }
    ],
    "eslint.validate": [
      "javascript",
      "javascriptreact",
      "typescript",
      "typescriptreact",
      "vue",
      "html",
      "markdown",
      "json",
      "jsonc",
      "yaml",
      "toml"
    ],
    "editor.formatOnSave": true,
    "editor.wordWrap": "off",
    "[javascript]": {
      "editor.defaultFormatter": "dbaeumer.vscode-eslint"
    },
    "[typescript]": {
      "editor.defaultFormatter": "dbaeumer.vscode-eslint"
    },
    "[vue]": {
      "editor.defaultFormatter": "Vue.volar"
    },
    "prettier.enable": false,
    "editor.gotoLocation.multipleTypeDefinitions": "goto",
    "editor.gotoLocation.multipleReferences": "goto",
    "editor.gotoLocation.multipleDefinitions": "goto",
    "editor.gotoLocation.multipleImplementations": "goto",
    "editor.gotoLocation.multipleDeclarations": "goto",
    "typescript.preferences.preferTypeOnlyAutoImports": true,
    "window.title": "${rootName}", // this make tabs more readable
    "unocss.root": "packages/client",
    "cSpell.words": [
      "earncoins",
      "exchangezone",
      "licence",
      "moboshort",
      "moboshort",
      "myearnings",
      "vant",
    ],
    "oxc.enable": false,
  },
}
