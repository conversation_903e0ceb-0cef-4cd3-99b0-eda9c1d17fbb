declare namespace M {
  namespace UserAssetManage {
    interface Params {
      old_user_id?: number
      new_user_id?: number // 可选参数
      is_single_user_query?: number // 查老用户和新用户：0, 单查询：1
    }
    interface Response {
      old_user_asset?: {
        user_id: number // UID
        country_code: string // 国家
        last_active_dt: number // 最后活跃日期
        platform: 1 // 平台 0:unknown 1:ios 2:android
        subscription_status: string // "": 无，"weekly":周卡会员
        subscription_expire_time: number // 过期时间戳
        coins: number
        bonus: number
        rewards: number
        coupon: number
        unlocked_episode: number
      }
      new_user_asset?: {
        user_id: number
        country_code: string
        last_active_dt: number
        platform: number // 平台 0:unknown 1:ios 2:android
        subscription_status: string // "": 无，"weekly":周卡会员
        subscription_expire_time: number // 过期时间戳
        coins: number
        bonus: number
        rewards: number
        coupon: number
        unlocked_episode: number
      }
    }
    namespace UserAssetTransfer{
      interface Params {
        old_user_id?: number
        new_user_id?: number
        transfer_asset_type_list?: string[]
      }
      interface Response {
        success?: number // 0：操作失败 1:操作成功
        out_account_info?: {
          user_id: number
          country_code: string
          last_active_dt: number
          platform: number // 平台 0:unknown 1:ios 2:android
        }
        in_account_info?: {
          user_id: number
          country_code: string
          last_active_dt: number
          platform: number // 平台 0:unknown 1:ios 2:android
        }
        transfered_asset?: {
          subscription: string // "": 无，"weekly":周卡会员
          subscription_expire_time: number
          coins: number
          bonus: number
          rewards: number
          coupon: number
          unlocked_episode: number
        }
      }
    }
  }
}
