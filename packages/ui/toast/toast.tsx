import {
  watch,
  onMounted,
  onUnmounted,
  defineComponent,
  type PropType,
  type TeleportProps,
  type CSSProperties,
  type ExtractPropTypes,
  useId,
} from 'vue'

// Utils
import {
  pick,
  isDef,
  unknownProp,
  numericProp,
  makeStringProp,
  makeNumberProp,
} from '../utils'
import { lockClick } from './toast-lock-click'

import Popup from '../popup/popup'
import type { ToastType, ToastPosition, ToastWordBreak } from './toast-types'
import { SvgIcon } from '../ui'

const popupInheritProps = [
  'show',
  'overlay',
  'teleport',
  'transition',
  'overlayClass',
  'overlayStyle',
  'closeOnClickOverlay',
  'zIndex',
] as const

export const toastProps = {
  icon: String,
  show: Boolean,
  type: makeStringProp<ToastType>('text'),
  overlay: Boolean,
  message: numericProp,
  iconSize: numericProp,
  duration: makeNumberProp(2000),
  position: makeStringProp<ToastPosition>('middle'),
  teleport: [String, Object] as PropType<TeleportProps['to']>,
  wordBreak: String as PropType<ToastWordBreak>,
  className: unknownProp,
  iconPrefix: String,
  transition: makeStringProp('van-fade'),
  // loadingType: String as PropType<LoadingType>,
  forbidClick: Boolean,
  overlayClass: unknownProp,
  overlayStyle: Object as PropType<CSSProperties>,
  closeOnClick: Boolean,
  closeOnClickOverlay: Boolean,
  zIndex: numericProp,
}

export type ToastProps = ExtractPropTypes<typeof toastProps>

export default defineComponent({
  props: toastProps,

  emits: ['update:show'],

  setup(props, { emit, slots }) {
    let timer: number | undefined
    let clickable = false

    const toggleClickable = () => {
      const newValue = props.show && props.forbidClick
      if (clickable !== newValue) {
        clickable = newValue
        lockClick(clickable)
      }
    }

    const updateShow = (show: boolean) => emit('update:show', show)

    const onClick = () => {
      if (props.closeOnClick) {
        updateShow(false)
      }
    }

    const clearTimer = () => window.clearTimeout(timer)

    const renderIcon = () => {
      const { icon, type } = props
      const hasIcon = icon || type === 'success' || type === 'fail'
      const id = useId()
      if (hasIcon) {
        return (
          <SvgIcon
            key={id}
            name={icon || type}
            class="mr-1 min-w-5"
          />
        )
      }
    }

    const renderMessage = () => {
      const { type, message } = props

      if (slots.message) {
        return <div>{slots.message()}</div>
      }

      if (isDef(message) && message !== '') {
        return type === 'html'
          ? (
              <div key={0} innerHTML={String(message)} />
            )
          : (
              <div class="w-fit break-all !text-white">{message}</div>
            )
      }
    }

    watch(() => [props.show, props.forbidClick], toggleClickable)

    watch(
      () => [props.show, props.type, props.message, props.duration],
      () => {
        clearTimer()
        if (props.show && props.duration > 0) {
          timer = window.setTimeout(() => {
            updateShow(false)
          }, props.duration)
        }
      },
    )

    onMounted(toggleClickable)
    onUnmounted(toggleClickable)

    return () => (
      <Popup
        class={[
          props.className,
          'px-4 py-5 rounded max-w-90 w-90 flex items-start z-toast! fixed top-16 left-1/2 -translate-x-1/2 max-w-[1/2] bg-[#292C31]',
        ]}
        lockScroll={false}
        // @ts-expect-error 不知道当时为什么要加 onClick
        onClick={onClick}
        onClosed={clearTimer}
        onUpdate:show={updateShow}
        {...pick(props, popupInheritProps)}
      >
        {renderIcon()}
        {renderMessage()}
      </Popup>
    )
  },
})
