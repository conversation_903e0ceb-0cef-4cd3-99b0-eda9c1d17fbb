<template>
  <div class="h-screen bg-white">
    <NavBar title="Recharge" class="font-medium text-text-1" :border="false" @click-left="router.back()">
      <template #left>
        <SvgIcon name="ic-chevron-left" class="size-6 text-text-1 " />
      </template>
    </NavBar>
    <div class="h-[90%]">
      <div class="flex h-full flex-col items-center justify-center ">
        <SvgIcon name="payment/ic-check-circle-fill" class="!size-110px" />
        <span class="text-text-1 text-lg font-bold mt-5">{{ T.paymentPage.rechargeSuccess.ref }}</span>
        <span class="text-text-2 text-base mt-3">{{ $t('paymentPage.waitToClose', { secondes: countDown }) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NavBar } from 'vant'
import { useRouter, useRoute } from 'vue-router'
import { SvgIcon } from '@skynet/ui'
import { h, onMounted, onUnmounted, ref } from 'vue'
import { T } from './payment-page.i18n'
import { h5Track } from 'src/lib/h5-track';
import { reportNow } from '@skynet/client-track'
import { setIOSTheme } from 'src/lib/set-ios-theme';
import { showDialog, closeDialog } from 'vant';
import AnonymousDialog from './anonymous-dialog.vue'
import { getUser } from 'src/h5_modules/common/user'
import { T as TL } from 'src/h5_modules/i18n/login.i18n'

const router = useRouter()
const route = useRoute()
const auth_params = getUser()

const countDown = ref(3)

const decodeHelper = (str: string) => {
  const decodeStr = decodeURIComponent(str)
  const url = new URL(decodeStr, window.location.origin)
  url.searchParams.append('success', 'true')
  return url.pathname + url.search
}

const startCountDown = () => {
  const timer = setInterval(() => {
    if (countDown.value > 1) {
      countDown.value--
    } else {
      clearInterval(timer)
      const { from } = router.currentRoute.value.query
      // TODO: 判断用户状态, 如果用户未登录, 则跳转到登录页面
      const product_id = route.query.product_id as string
      const series_id = route.query.series_id as string
      const video_id = route.query.video_id as string
      console.log('video_id:', video_id)
      const video_type = route.query.video_type as string
      const pop_scene = series_id ? 'video' : 'store'
      if (auth_params.user_type === 0 && (from?.indexOf('series') || -1) > -1) { // 只处理非播放器场景
        h5Track('hint', 'pop', 'show', {
          event: 'hint_pop_show',
          from: decodeHelper(from?.toString() || ''),
          pop_type: 1, // 支付后弹窗
          pop_scene: pop_scene,
          product_id: product_id,
          series_id: series_id,
          video_id: video_id,
          video_type: video_type,
          session_id: `${new Date().getTime()}${(auth_params.user_id || '')}`,
        })
        showDialog({
          message: () => h(AnonymousDialog, {
            title: TL.login.secureYou.ref.value,
            message: TL.login.bindAccount.ref.value,
            confirmText: TL.login.goToSign.ref.value,
            cancelText: TL.login.maybeLater.ref.value,
            onClose: () => {
              h5Track('hint', 'pop_close', 'click', {
                event: 'hint_pop_close_click',
                from: decodeHelper(from?.toString() || ''),
                pop_type: 1, // 支付后弹窗
                pop_scene: pop_scene,
                product_id: product_id,
                series_id: series_id,
                video_id: video_id,
                video_type: video_type,
                session_id: `${new Date().getTime()}${(auth_params.user_id || '')}`,
              })
              router.push(decodeHelper(from?.toString() || ''))
              closeDialog()
              // location.href = location.origin + decodeHelper(from?.toString() || '')
            },
            onConfirm: () => {
              h5Track('hint', 'pop_sign_in', 'click', {
                event: 'hint_pop_sign_in_click',
                from: decodeHelper(from?.toString() || ''),
                pop_type: 1, // 支付后弹窗
                pop_scene: pop_scene,
                product_id: product_id,
                series_id: series_id,
                video_id: video_id,
                video_type: video_type,
                session_id: `${new Date().getTime()}${(auth_params.user_id || '')}`,
              })
              void router.push({ path: '/login', query: { from: route.path, series_id, video_id, video_type, pop_scene } })
              closeDialog()
            }
          }),
          showConfirmButton: false,
          className: "p-0"
        })
      }
      if (from && (from.indexOf('dramawave://') > -1 || from.indexOf('dramabuzz://') > -1)) {
        location.href = from?.toString() || ''
      } else if (from && (from.toString().indexOf('series') === 0)) {
        location.href = location.origin + decodeHelper(from?.toString() || '')
      } else {
        router.push(decodeHelper(from?.toString() || ''))
      }
    }
  }, 1000)
}

onMounted(() => {
  // 设置状态栏颜色
  setIOSTheme('#fff')
})

onUnmounted(() => {
  setIOSTheme('#0b080b')
})

onMounted(() => {
  const { product_id, from } = router.currentRoute.value.query
  // from 可能包含 dramawave:// 或 dramabuzz://
  if (from && (from.indexOf('dramawave://') > -1 || from.indexOf('dramabuzz://') > -1)) {
    h5Track('payment', 'success', 'show', {
      event: 'payment_success_show',
      paysource: 'iap_pop',
    })
  } else {
    h5Track('payment', 'page', 'show', {
      event: 'payment_page_show',
      product_id: product_id?.toString(),
      series_id: route.query.series_id as string,
      video_id: route.query.video_id as string,
      order_id: route.query.order_id as string,
      currency: route.query.currency as string,
      payment_channel: route.query.pay_channel as string,
      sub_pay_channel: route.query.pay_sub_channel as string,
    })
  }

  reportNow();
})

onMounted(async () => {
  startCountDown()
})

</script>
