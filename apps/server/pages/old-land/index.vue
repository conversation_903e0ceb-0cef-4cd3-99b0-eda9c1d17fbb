<script setup lang="ts">
import initJs from './client/init.js?raw'
import indexCss from './client/index.css?raw'
import mainJs from './client/main.js?raw'
import clipboardJs from './client/clipboard.min.js?raw'
import facebookJs from './client/facebook.js?raw'
import { translations } from './index.i18n'
definePageMeta({
  layout: 'empty-layout'
})
useHead({
  script: [
    {
      children: initJs,
      body: true
    }, {
      children: facebookJs,
      body: true
    }, {
      children: clipboardJs,
      body: true
    }, {
      children: mainJs,
      body: true
    }
  ],
  style: [
    {
      // #region css
      innerHTML: indexCss
      // #endregion
    }
  ]
})

const api = useApi()
const query = useRoute().query
const { data: seriesItem } = await useAsyncData('series_info', async () => {
  const cacheKey = `land/ seriesItem / ${query.content_id}`
  const cachedData = lruCache.get(cacheKey)
  if (cachedData) {
    return cachedData.series_info
  }
  const data = await api.request('/dm-api/drama/share/series_info?series_id=' + query.content_id,
    { method: 'GET', skipAuth: true }
  ).catch(error => {
    console.log('error:', error)
    return null
  })
  if (data) {
    lruCache.set(cacheKey, data)
  }
  return data?.series_info
})
const { ip } = useIp()
const { languageAndLocale } = useLanguage()
const language = seriesItem.value.original_audio_language || languageAndLocale
const t = translations[language] || translations['en-US']

</script>

<template>
  <x-index class="block">
    <div id="_data_series_info" style="display: none;">{{ JSON.stringify(seriesItem) }}</div>
    <div id="_data_ip" style="display: none;">{{ JSON.stringify(ip) }}</div>
    <div id="app" style="position: relative;">
      <x-top-nav-wrapper style="display: block; min-height: 62px;">
        <x-top-nav id="minorImages" style="display: none;">
          <img class="logo" data-src="https://static-v1.mydramawave.com/frontend_static/assets/logo-ZkLn7rqY.webp"
            width="37" height="37" />
          <img class="title" data-src="https://static-v1.mydramawave.com/frontend_static/assets/dramawave-JYta-J2M.webp"
            width="92" height="12" />
          <span class="subtitle">{{ seriesItem.name }}</span>
          <button>{{ t.open }}</button>
        </x-top-nav>
      </x-top-nav-wrapper>
      <article class="art">
        <x-wrapper>
          <x-bg>
            <img class="bg" :src="seriesItem.cover" alt="background" />
            <x-mask />
          </x-bg>
          <main style="position: relative; z-index: 3;">
            <x-cover>
              <img class="cover" :src="seriesItem.cover" alt="cover" />
              <img class="play" width="90" height="90"
                src="https://static-v1.mydramawave.com/frontend_static/dramato-web/art/btn.svg" alt="play" />
            </x-cover>
            <x-summary style="display: flex; flex-direction: column; row-gap: 12px; text-align: center;">
              <h1>{{ seriesItem.name }}</h1>
              <ul class="">
                <li v-for="tag in seriesItem.content_tags" :key="tag">{{ tag }}</li>
              </ul>
              <div style="text-align: left;">
                <x-desc class="unfolded">{{ seriesItem.desc }}</x-desc>
              </div>
              <button>
                <img src="https://static-v1.mydramawave.com/frontend_static/dramato-web/art/click.webp" width="60"
                  height="51" alt="click" />
                <span>{{ t.continueWatching }}</span>
              </button>
            </x-summary>
          </main>
        </x-wrapper>
      </article>

    </div>
    <div style="width: 0; height: 0; margin-left: -10px;">
      <iframe id="iframe" src="" width="0" height="0" name="iframe"></iframe>
    </div>
  </x-index>
</template>
