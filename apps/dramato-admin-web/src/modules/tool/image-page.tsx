/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Uploader } from 'src/modules/common/uploader/uploader'
import { useUploader } from 'src/modules/common/uploader/use-uploader'
import { useRoute } from 'vue-router'
import { onMounted, ref } from 'vue'
import { showFailToast, Button, Icon } from '@skynet/ui'
import { apiGetOssSign, apiOptImage } from './image-page.api'

type ImagePageOptions = {
  props: {}
}
export const ImagePage = createComponent<ImagePageOptions>({
  props: {},
}, props => {
  const loading = ref(true)
  const btnLoading = ref(false)
  const { ossDataBj } = useUploader()
  const route = useRoute()
  const path = ref('')
  const filename = ref('')
  if (!route.query.sign) {
    showFailToast('当前页面不可用！')
  }

  function getFileExtension(url: string) {
    // 使用正则表达式匹配文件后缀
    const match = url.match(/\.(\w+)(\?.*)?$/)
    return match ? match[1] : null
  }

  function changeOrAddFileExtension(url, newExtension) {
    if (/\.\w+(\?.*)?$/.test(url)) {
      // 如果有后缀，替换
      return url.replace(/\.\w+(\?.*)?$/, `.${newExtension}$1`)
    } else {
      // 如果没有后缀，添加新后缀
      return `${url}.${newExtension}`
    }
  }

  async function downloadImage(imageUrl: string) {
    try {
      const ext = getFileExtension(imageUrl)
      const name = changeOrAddFileExtension(filename.value, ext)
      const response = await fetch(imageUrl, { mode: 'cors' }) // 确保 fetch 支持跨域
      if (!response.ok) throw new Error('网络响应失败')
      const blob = await response.blob() // 转为 Blob 对象
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = name || `image.${ext}` // 默认文件名
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url) // 释放 URL 对象
    } catch (error) {
      console.error('图片下载失败:', error)
    }
  }

  const onRemoveText = async () => {
    try {
      btnLoading.value = true
      const res = await apiOptImage({
        oss_path: path.value,
        type: 1,
      }, route.query.sign as string)
      await downloadImage(res.data.url)
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }
  const onImageOpt = async () => {
    try {
      btnLoading.value = true
      const res = await apiOptImage({
        oss_path: path.value,
        type: 2,
      }, route.query.sign as string)
      await downloadImage(res.data.url)
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }

  onMounted(async () => {
    try {
      loading.value = true
      const res = await apiGetOssSign(route.query.sign as string)
      if (res.data.code === 401) {
        showFailToast('sign已过期')
        return
      }
      ossDataBj.value = res.data.data
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      loading.value = false
    }
  })
  return () => (
    <div class="flex items-center flex-col pt-[20vh]">
      {!loading.value && ossDataBj.value
        ? (
            <>
              {/* <h4>图片优化</h4> */}
              <x-upload-cover class="gap-y-2 grid">
                <Uploader
                  accept="png,jpg,jpeg"
                  ossKeyType="resource"
                  maxsize={1024 * 1024 * 2}
                  class="border-[1px] border-dashed rounded-md cursor-pointer overflow-hidden size-[200px]"
                  onUploadSuccess={d => {
                    path.value = `https://img.tianmai.cn/${d.temp_path}`
                    filename.value = d.file?.name || ''
                  }}
                  isImage={false}
                >
                  {
                    path.value
                      ? <img src={path.value.includes('https://') ? path.value : 'https://img.tianmai.cn/' + path.value} class="object-cover size-full" />
                      : <span class="flex justify-center items-center size-full">上传图片</span>
                  }
                </Uploader>
                <x-upload-cover-tip class="text-gray-600 text-sm">png,jpg,jpeg格式，大小限制2M</x-upload-cover-tip>
              </x-upload-cover>
              <div>
                <img src="" alt="" />
              </div>
              <x-button-group class="p-4 space-x-4">
                <Button class="btn btn-primary btn-sm" disabled={!path.value || btnLoading.value} onClick={onRemoveText}>
                  {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                  文字抹除
                </Button>
                <Button class="btn btn-primary btn-sm" disabled={!path.value || btnLoading.value} onClick={onImageOpt}>
                  {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                  图片增强
                </Button>
              </x-button-group>
            </>
          )
        : null}
    </div>
  )
})

export default ImagePage
