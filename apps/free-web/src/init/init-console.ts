import { openedInProd } from '@skynet/shared'

window.originalLog = console.log.bind(console)
window.originalLogs = []
console.log = (...args) => {
  window.originalLogs?.push([...args])
  window.originalLog?.(...args)
}

if (!openedInProd) {
  setTimeout(() => {
    console.log('originalLogs', window.originalLogs)
  }, 10000)
}

declare global {
  interface Window {
    originalLog?: typeof console.log
    originalLogs?: unknown[]
  }
}
