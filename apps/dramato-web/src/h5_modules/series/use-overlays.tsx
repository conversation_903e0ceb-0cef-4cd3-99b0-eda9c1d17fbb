import { computed } from 'vue'
import { useSeriesItem } from './use-series-item'
import ManualUnlockPanel from './manual-unlock-panel.vue'
import ProductPanel from './product-panel.vue'
import UnlockOverlay from './unlock-overlay'
import FaqPanel from './faq-panel.vue'

type Params = {
  onClickUnlock: (episodeId?: string) => void
  onCancelRecharge: () => void
  onClickManualUnlock: (auto_unlock: boolean) => void
}
export function useOverlays(series_id: string, params: Params) {
  const { onClickUnlock, onCancelRecharge, onClickManualUnlock } = params
  const { seriesItem, getCurrentEpisode, unlockOverlayVisible, productPanelVisible, manualUnlockPanelVisible,
    faqPanelVisible,
  } = useSeriesItem(series_id)
  const currentEpisode = computed(() => getCurrentEpisode())
  return {
    renderOverlays,
  }
  function renderOverlays() {
    return seriesItem.value && currentEpisode.value
      ? (
          <>
            {unlockOverlayVisible.value && <UnlockOverlay bgUrl={seriesItem.value.cover} onClickUnlock={onClickUnlock} class="z-dialog" />}
            <ProductPanel v-model:productPanelVisible={productPanelVisible.value} episode={currentEpisode.value.episode_price}
              episodeType={seriesItem.value.free ? 'free' : 'charge'} onCancel={onCancelRecharge} />
            <ManualUnlockPanel v-model:unlockPanelVisible={manualUnlockPanelVisible.value} episode={currentEpisode.value.episode_price}
              onUnlockClick={onClickManualUnlock} />
            <FaqPanel v-model:faqPanelVisible={faqPanelVisible.value} />
          </>
        )
      : null
  }
}
