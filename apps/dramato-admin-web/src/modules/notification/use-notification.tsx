/* eslint-disable @typescript-eslint/no-explicit-any */
import { openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { ref } from 'vue'
import { apiEditInfoPush, apiGetInfoPushList } from './notification-api'
import { NotificationPreview } from './notification-preview'

const searchForm = ref<M.PushNotificationSearchOption>({
  page_info: {
    page_index: 1,
    page_size: 10,
  },
})

const list = ref<M.PushNotification[]>([])
const total = ref<number>(10)
const applicationList = ref<Array<{ label: string, value: number, platform: number, language: string[] }>>([])
const closeEditPushNotificationModal = ref(() => {})
const loading = ref(false)
const isUpdating = ref(false)

const InitPushNotificationOption: M.PushNotification = {
  priority: 3,
  user_identify_val: '',
  notify_btn_bg_color: '#FC2763'
}

const currentNotification = ref<M.PushNotification>(InitPushNotificationOption)

const getList = async () => {
  loading.value = true
  const rs = await apiGetInfoPushList(searchForm.value)
  list.value = rs.data?.list || []
  total.value = rs.data?.total || 0
  loading.value = false
}

const onSearchNotifications = (isFirst?: boolean) => {
  if (isFirst) {
    searchForm.value.page_info = {
      page_index: 1,
      page_size: 10,
    }
  }
  void getList()
}

const onPageChange = (page_index: number) => {
  searchForm.value.page_info = {
    ...(searchForm.value.page_info || {}),
    page_index,
  }
  onSearchNotifications()
}

const onPageSizeChange = (page_size: number) => {
  searchForm.value.page_info = {
    page_index: 1,
    page_size,
  }
  onSearchNotifications()
}

const onReset = () => {
  searchForm.value.search_val = undefined
  searchForm.value.state = undefined
  onSearchNotifications(true)
}

const onEditSuccess = (isCreate?: boolean) => {
  isUpdating.value = false
  closeEditPushNotificationModal.value && closeEditPushNotificationModal.value()
  if (isCreate) {
    onSearchNotifications(true)
    return
  }
  onSearchNotifications()
}

const switchPushNotificationParams = () => {
  const ids = currentNotification.value.target_app_ids as number[] || []
  const names = applicationList.value.filter(item => ids.includes(item.value))?.map(item => item.label).join(',') || ''
  return {
    ...currentNotification.value,
    target_app_ids: ids.join(','),
    target_app_names: names,
    image_url: currentNotification.value?.image_url?.includes('https://') ? currentNotification.value.image_url.replace('https://static-v1.mydramawave.com/push/task/image/notify/', '') : currentNotification.value.image_url,
    timed_ts: currentNotification.value.ageing_type === 2 ? new Date(currentNotification.value.timed_ts || '').getTime() / 1000 : undefined,
    user_identify_val: currentNotification.value.target_user_type === 2 ? currentNotification.value.user_identify_val : undefined,
  }
}

const onCreate = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditInfoPush(switchPushNotificationParams())
    showSuccessToast('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    isUpdating.value = false
    showFailToast(error.response.data.message || '创建失败')
  }
}

const onEdit = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditInfoPush(switchPushNotificationParams())
    showSuccessToast('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    isUpdating.value = false
    showFailToast(error.response.data.message || '编辑失败')
  }
}

const onPreview = () => {
  openDialog({
    title: '预览',
    mainClass: 'pb-0 px-5',
    customClass: '!w-[400px]',
    body: <NotificationPreview />,
  })
}

export const useNotification = () => {
  return {
    searchForm,
    list,
    total,
    closeEditPushNotificationModal,
    InitPushNotificationOption,
    loading,
    onPageChange,
    onPageSizeChange,
    onReset,
    onSearchNotifications,
    onCreate,
    onEdit,
    onPreview,
    currentNotification,
    applicationList,
    isUpdating,
  }
}
