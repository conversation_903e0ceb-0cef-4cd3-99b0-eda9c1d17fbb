/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { CreateForm, showFailToast, Icon, Button, Checkbox } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { langKey, langValue } from 'src/modules/resource/constant'
import { z } from 'zod'
import { useResourceStore } from 'src/modules/resource-publish/use-resource-publish-store'
import { apiTranslateSeriesInfo } from '../resource-publish-api'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'

type TranslateFormOptions = {
  props: {
    series_resource_id: number
  }
  emits: {
    finish: () => void
  }
}
export const TranslateForm = createComponent<TranslateFormOptions>({
  props: {
    series_resource_id: 0,
  },
  emits: {
    finish: () => {}
  }
}, (props, { emit }) => {
  const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()

  const { episodeSeriesInfoList } = useResourceStore()

  const form = ref<{
    target_lang_list: string[]
  }>({
    target_lang_list: [],
  })
  const btnLoading = ref(false)

  const TranslateForm = CreateForm<{
    target_lang_list: string[]
  }>()

  const rules = z.object({
    target_lang_list: z.array(z.string()).min(1, {
      message: '请选择目标语言',
    }),
  })

  const { error, validateAll } = useValidator(form, rules)

  const onSubmit = async () => {
    if (!validateAll()) {
      return
    }
    const originLangObj = episodeSeriesInfoList.value.find(row => row.language_code === 'en')
    if (!originLangObj?.title || !originLangObj.description) {
      showFailToast(`请填写英文标题和描述`)
      return
    }
    btnLoading.value = true
    try {
      const res = await apiTranslateSeriesInfo({
        series_resource_id: props.series_resource_id,
        title: originLangObj.title,
        description: originLangObj.description,
        ...form.value,
      })

      const list = res.data?.list || []
      list.forEach(row => {
        const langCol = episodeSeriesInfoList.value.find(item => row.language_code === item.language_code)
        if (langCol) {
          langCol.description = row.description
          langCol.title = row.title
        }
      })
    } catch (error: any) {
      showFailToast(error?.response?.data?.message || error?.response?.data?.msg)
    } finally {
      btnLoading.value = false
      emit('finish')
    }
  }

  return () => (
    <div class="inline-flex space-x-4 items-center justify-start">
      <TranslateForm
        class="w-full"
        error={error.value}
        data={form.value}
        hasAction={false}
        submitText="翻译"
        onChange={(path, value) => {
          set(form.value, path, value)
        }}
        items={[
          {
            label: '目标语言',
            path: 'target_lang_list',
            input: {
              type: 'custom',
              render: () => {
                return (
                  <div class="flex">
                    <FormMultiSelect
                      class="w-[200px]"
                      popoverWrapperClass="z-popover-in-dialog"
                      options={langKey.map((n, index) => {
                        return { value: langKey[index], label: langValue[index] }
                      })}
                      modelValue={form.value.target_lang_list}
                      onUpdate:modelValue={e => {
                        form.value.target_lang_list = e as string[]
                      }}
                    />
                    <label class="flex items-center ml-2">
                      <span class="text-xs text-[var(--text-2)]">全选</span>
                      <Checkbox
                        label=""
                        modelValue={form.value.target_lang_list.length === langKey.length}
                        onUpdate:modelValue={(value: boolean) => {
                          if (value) {
                            form.value.target_lang_list = [...langKey]
                          } else {
                            form.value.target_lang_list = []
                          }
                        }}
                      />
                    </label>
                  </div>
                )
              },
            }
          },
        ]}
      />
      <Button disabled={btnLoading.value} class="btn mt-2 btn-primary btn-sm " onClick={onSubmit}>
        翻译
        {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
      </Button>
    </div>
  )
})
