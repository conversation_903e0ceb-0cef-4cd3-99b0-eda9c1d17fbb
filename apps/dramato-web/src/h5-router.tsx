import { r } from '@skynet/shared'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from './layouts/main-layout'
import { userStore } from './h5_modules/common/user-store'
import { getUser } from './h5_modules/common/user'
import { showFailToast } from 'vant'
import { activityRoutes } from './modules/activity/activity-routes'
import { bindEmailRoutes } from './modules/bind-email/bind-email-routes'
import { coinsRoutes } from './modules/coins/coins-routes'
import { customerServiceCenterRoutes } from './modules/customer-service-center/customer-service-center-routes'
import { mealCheckInRoutes } from './modules/meal-check-in/meal-check-in-routes'
import { rechargeRoutes } from './modules/recharge/recharge-routes'
import { regulationsRoutes } from './modules/regulations/regulations-routes'
import { shareRoutes } from './modules/share/share-routes'
import { sleepCheckInRoutes } from './modules/sleep-check-in/sleep-check-in-routes'
import { NotFoundPage } from './modules/special-pages/not-found-page'
import { ttkolRoutes } from './modules/ttkol/ttkol-routes'
import { useStatistics } from './h5_modules/statistics/statistics'
import { h5AdTrack } from 'src/lib/h5-track'

declare module 'vue-router' {
  interface RouteMeta {
    title?: string
  }
}

/**
 * 公开路由，无需登录即可访问
 * 路由一经发布，不得修改，只能新增和重定向
 */
export const publicRoutes: RouteRecordRaw[] = [
  r('/', '', MainLayout, [
    r('', '', () => import('./h5_modules/layout/phone-layout'), [
      // 所有 H5 页面都写在这个子路由中
      // 注意路径不要跟现有的路由冲突
      r('landingpage', 'landingpage', () => import('src/h5_modules/landing/landing-page')),
      r('settings', 'Settings', () => import('src/h5_modules/settings/settings-page.vue')),

      r('wallet', 'Wallet', () => import('src/h5_modules/wallet/wallet-page.vue')),
      r('transaction-history', 'Transaction History', () => import('src/h5_modules/wallet/pages/transaction-history-page.vue')),
      r('consumption-records', 'Consumption Records', () => import('src/h5_modules/wallet/pages/consumption-records-page.vue')),
      r('reward-history', 'Reward History', () => import('src/h5_modules/wallet/pages/reward-history-page.vue')),
      r('payment', 'Payment', () => import('src/h5_modules/payment/payment-page.vue')),
      r('payment-third', 'Payment Third', () => import('src/h5_modules/payment-third/payment-page.vue')),
      r('payment-two-step', 'Payment Two Step', () => import('src/h5_modules/payment-third/payment-two-step-page.vue')),
      r('payment-landing', 'Payment Landing', () => import('src/h5_modules/payment/payment-landing-page.vue')),
      r('payment-landing-third', 'Payment Landing Third', () => import('src/h5_modules/payment-third/payment-landing-page.vue')),
      r('payment-success', 'Payment Success', () => import('src/h5_modules/payment/payment-success-page.vue')),
      r('store', 'Store', () => import('src/h5_modules/store/store-page.vue')),
      r('login', 'Login', () => import('src/h5_modules/login/login-page.vue')),
      r('member-center', 'Member Center', () => import('src/h5_modules/member-center/member-center-page.vue')),
      r('watch-history', 'Watch History', () => import('src/h5_modules/watch-history/watch-history-page.tsx')),

      r('search', 'Search', () => import('src/h5_modules/search/search-page.tsx')),
      r('activity/payment', 'Payment', () => import('src/h5_modules/activity-payment/activity-payment-page')),
      r('activity/recharge', 'Payment Recharge', () => import('src/h5_modules/activity-payment/activity-recharge-page')),
      r('activity/recharge-success', 'Payment Recharge Success', () => import('src/h5_modules/activity-payment/activity-recharge-success-page')),
      r('series', '', () => import('src/h5_modules/layout/transition-layout'), [
        r(':series_id', 'Series', null, [
          r(':episode_id?', 'Playing', () => import('src/h5_modules/series/series-item-page')),
        ]),
      ]),
      r('', '', () => import('src/h5_modules/layout/search-and-tabs-layout'), [
        r('', 'DramaWave', () => import('src/h5_modules/home/<USER>')),
        r('my-list', 'My List', () => import('src/h5_modules/my-list/my-list-page.tsx')),
        r('profile', 'Profile', () => import('src/h5_modules/profile/profile-page.vue')),
      ]),
      r('test', '', null, [
        r('player', 'Player', () => import('src/h5_modules/test/player-page.tsx')),
        r('player2', 'Player2', () => import('src/h5_modules/test/player-2-page.tsx')),
      ]),
    ]),
    r('', '', () => import('./layouts/old-layout'), [
      r(['account-deletion', 'delete_account'], 'Account Deletion', () => import('./modules/home/<USER>')),
      shareRoutes,
      customerServiceCenterRoutes,
      rechargeRoutes,
      activityRoutes,
      regulationsRoutes,
      bindEmailRoutes,
      coinsRoutes,
      mealCheckInRoutes,
      sleepCheckInRoutes,
      ttkolRoutes,
      r('/:pathMatch(.*)*', 'Not Found', NotFoundPage),
    ]),
  ]),
]

export const h5History = createWebHistory()

const { loginAnonymous } = userStore()
const { clearStorage } = useStatistics()
export const h5Router = createRouter({
  history: h5History,
  routes: publicRoutes,
})

h5Router.beforeEach((to, from, next) => {
  const me = getUser()
  const excludePathList = ['/', '/settings', '/wallet', '/transaction-history', '/consumption-records', '/reward-history',
    '/payment-landing', '/payment-success', '/store', '/login', '/member-center', '/watch-history', '/search', '/series']
  // const excludePathList1 = ['/activity/payment', '/activity/recharge', '/activity/recharge-success']
  if (excludePathList.includes(to.path) || to.path.startsWith('/series/')) { // 确保短剧H5的路由才走这个匿名登录的逻辑
    if (!me.auth_key || !me.auth_secret) {
      clearStorage()
      const start_time = Date.now()
      void loginAnonymous().then(() => {
        h5AdTrack('', '', 'login_anonymous_time', {
          load_time: Date.now() - start_time,
        })
        next()
      }, () => {
        next()
        setTimeout(() => {
          showFailToast('匿名登录失败，请重试。') // 如果不推迟显示，会导致提示没有样式
        }, 500)
      })
        .finally(() => {
          const loading = document.getElementById('global-loading')
          if (loading) loading.style.display = 'none'
        // loading?.parentNode?.removeChild(loading)
        })
    } else {
      next()
    }
  } else {
    next()
  }
})
