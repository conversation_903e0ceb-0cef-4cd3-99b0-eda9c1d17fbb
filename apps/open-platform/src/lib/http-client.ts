/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { get_k_sso_token } from './device-id.ts'
import { openDialog } from '@skynet/ui'
import { Audit } from '../modules/common/audit.tsx'
import { omit } from 'lodash-es'
import { toRaw } from 'vue'
import { get_k_device_hash_from_cookie } from './device-id.ts'
import { get_authorization_for_browser } from './authorization.ts'

import type {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  CreateAxiosDefaults,
  InternalAxiosRequestConfig,
} from 'axios'
import axios, { isAxiosError } from 'axios'
import { cloneDeep, get, set } from 'lodash-es'
import { ResponseError } from 'src/modules/common/errors.ts'
import router from 'src/router.tsx'
import { h } from 'vue'
import { i18n } from 'src/locales/index.ts'

export interface ConstructParams extends CreateAxiosDefaults {
  /**
   * 用来判断 response 是不是错误
   * @example
   * ```ts
   * isError: (response: AxiosResponse) => response.data.code !== 200
   * ```
   */
  isError?: (response: AxiosResponse) => null | ResponseError
  /**
   * 用来获取业务数据
   * 建议尽量获取全面的数据，不要只获取 data 而忽略 code
   * @example
   * ```ts
   * getData: (response: AxiosResponse) => response.data // 不推荐 response.data.data
   * ```
   */
  getData?: <T>(response: AxiosResponse<T>) => T
  /**
   * 请求拦截器
   */
  requestInterceptor?: [
    ((config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig) | null,
    ((error: unknown) => Promise<unknown>) | null,
  ]
  /**
   * 响应拦截器
   */
  responseInterceptor?: [
    ((response: AxiosResponse) => AxiosResponse) | null,
    ((error: unknown) => Promise<unknown>) | null,
  ]
}
export class HttpClient {
  instance: AxiosInstance
  isError: ConstructParams['isError']
  getData: Exclude<ConstructParams['getData'], undefined> = response => {
    if (!response) throw new Error('response is undefined')
    return response.data
  }

  requestInterceptor: ConstructParams['requestInterceptor']
  responseInterceptor: ConstructParams['responseInterceptor']
  constructor(params: ConstructParams) {
    const { isError, getData, requestInterceptor, responseInterceptor, ...rest } = params
    this.instance = axios.create(rest)
    this.isError = isError
    this.getData = getData || this.getData
    this.requestInterceptor = requestInterceptor
    this.responseInterceptor = responseInterceptor
    this.intercept()
  }

  private intercept() {
    if (this.requestInterceptor) {
      this.instance.interceptors.request.use(...this.requestInterceptor)
    }
    if (this.responseInterceptor) {
      this.instance.interceptors.response.use(...this.responseInterceptor)
    }
  }

  /**
   * 发送 GET 请求
   * @param url 请求地址
   * @param params 查询参数
   * @param config 请求配置
   * @returns 请求结果
   * @example
   * ```ts
   * httpClient.get<ServerResponse<User>>("/api/user", { id: 1 })
   * ```
   */
  get<T = unknown>(
    url: string,
    params?: HttpRequestConfig['params'],
    config = {} as Omit<HttpRequestConfig, 'params'>,
  ) {
    return this._request<T>({ url, params, ...config, method: 'GET' })
  }

  /**
   * 发送 POST 请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns 请求结果
   * @example
   * ```ts
   * httpClient.post<ServerResponse<User>>("/api/user", { id: 1 })
   * ```
   */
  post<T = unknown>(url: string, data?: any, config: HttpRequestConfig = {}) {
    return this._request<T>({ url, data, ...config, method: 'POST' })
  }

  /**
   * 发送 PUT 请求
   */
  put<T = unknown>(url: string, data?: JsonValue, config: HttpRequestConfig = {}) {
    return this._request<T>({ url, data, ...config, method: 'PUT' })
  }

  /**
   * 发送 DELETE 请求
   */
  delete<T = unknown>(
    url: string,
    params?: HttpRequestConfig['params'],
    config = {} as Omit<HttpRequestConfig, 'params'>,
  ) {
    return this._request<T>({ url, params, ...config })
  }

  _request<T = unknown>(config: HttpRequestConfig) {
    if (config.transformRequestData) {
      const data = cloneDeep(config.data)
      Object.entries(config.transformRequestData).forEach(([key, _fnList]) => {
        const fnList = Array.isArray(_fnList) ? _fnList : [_fnList]
        fnList.forEach(fn => {
          try {
            const value = get(data, key)
            set(data, key, fn(value, data))
          } catch (error) {
            console.error('transformRequestData error:', error)
          }
        })
      })
      config.data = data
    }
    return this.instance.request<T>(config).then(response => {
      const error = this.isError ? this.isError(response) : false
      if (error) throw (new ResponseError(error.code, error.message))
      return response
    }).then(this.getData, (err: any) => {
      if (err instanceof ResponseError) {
        const error = handleError(err)
        if (error) throw error
      }
      throw err
    }).then(data => {
      if (config.transformResponseData) {
        const obj = cloneDeep(data) as Record<string, unknown>
        Object.entries(config.transformResponseData).forEach(([key, _fnList]) => {
          const fnList = Array.isArray(_fnList) ? _fnList : [_fnList]
          fnList.forEach(fn => {
            try {
              const value = get(obj, key)
              set(obj, key, fn(value, obj))
            } catch (error) {
              console.error('transformResponseData error:', error)
            }
          })
        })
        return obj as typeof data
      }
      return data
    })
  }
}

export type TransformItem = (value?: any, data?: unknown) => any
export type HttpRequestConfig = AxiosRequestConfig & {
  transformRequestData?: Record<string, TransformItem | TransformItem[]>
  transformResponseData?: Record<string, TransformItem | TransformItem[]>
  disableAutoErrorHandle?: boolean
}

const commonRequestInterceptor: HttpClient['requestInterceptor'] = [
  config => {
    Object.assign(config.headers, {
      device: 'Web',
      token: get_k_sso_token(),
    })
    return config
  },
  null,
]

const codeErrMap: { [key: number]: { 'zh-CN': string, 'en-US': string } } = {
  2200: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2201: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2202: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2213: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2214: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2215: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2216: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2217: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2218: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2219: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2220: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
  2400: { 'zh-CN': '此剧本正在审核中,无法修改', 'en-US': 'This script is under review and cannot be modified' },
}

const commonParams: ConstructParams = {
  baseURL: import.meta.env.VITE_DRAMA_API_URL,
  timeout: 60000,
  withCredentials: true,
  isError: response => {
    if (![0, 200].includes(response.data.code)) {
      if (codeErrMap[response.data.code]) {
        const lang = i18n.global.locale.value as 'zh-CN' | 'en-US'
        return new ResponseError(response.data.code, codeErrMap[response.data.code][lang] || response.data.message)
      }
      return new ResponseError(response.data.code, response.data.message)
    }
    if (response.data.data === undefined) {
      return new ResponseError(response.data.code, response.data.message)
    }
    return null
  },
  requestInterceptor: commonRequestInterceptor,
  responseInterceptor: [
    null,
    err => {
      // 这个业务用不到这个错误处理，因为状态码永远都是 200
      if (isAxiosError(err) && err.response) {
        handleError(new ResponseError(err.response.status, err.response.statusText))
      }
      throw err
    }],

}
// const { currentLocale } = useLocale()
// const localeObject = omit(toRaw(currentLocale.value), 'name')
// web H5配置写这里
// const h5CommonParams: ConstructParams = {
//   ...commonParams,
//   requestInterceptor: [
//     config => {
//       Object.assign(config.headers, {
//         'app-name': 'com.dramawave.app', // 如果是facebook登录要暂时换成：com.dramabuzz.app 因为appId用的安卓的，上线前会替换appId,
//         'app-version': '1.2.20',
//         'device-hash': get_k_device_hash_from_cookie(),
//         'device-id': get_k_device_hash_from_cookie(),
//         authorization: get_authorization_for_browser(),
//         device: 'android',
//         'Skip-Encrypt': 1,
//         // ...localeObject,
//       })
//       return config
//     },
//     null,
//   ],
// }

/**
 * 如果以下配置不满足你的需求，你可以修改，或者创建其他 httpClient
 */
export const httpClient = new HttpClient(commonParams)
// export const h5HttpClient = new HttpClient(h5CommonParams)

export function keepError(fn?: (err: any) => void) {
  return (err: any) => {
    fn?.(err)
    throw err
  }
}

function handleError(error: ResponseError): ResponseError | void {
  const handlers: Record<number, (err: ResponseError) => ResponseError | void> = {
    401: error => {
      const yes = window.confirm('登录已过期，是否重新登录？')
      if (!yes) return error
      const path = window.location.pathname + window.location.search
      void router.push({ path: '/sign_in', query: { returnTo: path } })
    },
    2400: error => {
      const close = openDialog({
        title: i18n.global.t('tip'),
        body: h(Audit, {
          onConfirm: () => {
            router.push({ path: '/user' })
            close()
          },
        }),
        closeVisible: true,
        customClass: '!w-[400px]',
      })
    },
  }
  handlers[error.code]?.(error)
  return error
}
