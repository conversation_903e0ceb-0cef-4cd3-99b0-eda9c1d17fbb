declare namespace MyWallet {
  interface MyWallet {
    ticket_total: string // 观影券总数
    ticket: string // 观影券余额
    show_coins: boolean // 是否隐藏金币
    ticket_bonus: string // 赠送观影券余额
    ticket_coin_exchange: string // 金币兑换观影券余额
    ticket_ad_reward: string // 看广告奖励观影券余额
    gold_total: number // 金币总数
    cash_balance: number // 金币余额
    bonus_balance: number // 赠送金币余额
    vip_level: number // vip等级 0:非vip  1:vip
    vip_expire: number // 会员过期时间戳，单位-秒
    auto_unlock: number // 自动解锁 0:关闭 1:开启
    membership_product: {
      title: string
      description: string
    }
  }

  interface RechargeHistoryList {
    items: PurchaseHistoryItem[],
    page_info: {
      next: string,
      has_more: boolean
    }
  }
  
}
