import { defineConfig } from 'unocss'
import presetWind from '@unocss/preset-wind'
import { skynetUnoPreset } from '@skynet/preset/uno'

export default defineConfig({
  content: {
    filesystem: [
      'src/**/*.{html,js,ts,jsx,tsx,vue,svelte,astro}',
    ],
  },
  theme: {
    colors: {
      primary: '#2F6DCD',
      // 其他颜色变量的自定义...
      // secondary - 次色调
      // accent - 强调色
      // success - 成功状态色
      // info - 信息状态色
      // warning - 警告状态色
      // error - 错误状态色
    },
    zIndex: {
      // 局部
      up: '1',
      'up-up': '2',
      // 全局
      tooltip: '16',
      popover: '32',
      select: '64',
      'top-bar': '128',
      footer: '129',
      'dialog-mask': '1023',
      dialog: '1024',
      toast: '2048',
    },
  },
  presets: [
    skynetUnoPreset,
    presetWind(), // 兼容 tailwind
  ],
  rules: [
  ],
})
