import { createComponent, fn } from '@skynet/shared'
import { listingStatusList } from './short-drama-api'
import { Button, Input, openDialog } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { MultiSaveErrorDialog } from './multi-save-error-dialog'
import { Icon } from '@skynet/ui/icon/icon'
import { useShortDramaStore } from './short-drama-store'
type MultiModifyDramaListingStatusOptions = {
  emits: {
    cancel: Fn
    allSaveSuccess: Fn
    updateData: Fn
  }
}
export const MultiModifyDramaListingStatus = createComponent<MultiModifyDramaListingStatusOptions>({
  emits: {
    cancel: fn,
    allSaveSuccess: fn,
    updateData: fn,
  },
}, (_props, { emit }) => {
  const { selectedDramas, loading, listStatus, time, hasTime, platform_sync_list, updateDramasListingStatus, unshelve_reason } = useShortDramaStore()
  const save = () => {
    const app_id = selectedDramas.value[0]?.app_id || ''
    updateDramasListingStatus(app_id, true, false, fails => {
      emit('updateData')
      if (fails && fails.length > 0) {
        const close = openDialog({
          title: '部分短剧修改失败',
          body: (
            <MultiSaveErrorDialog
              type="status"
              fails={fails}
              onCancel={() => {
                close()
              }}
              onConfirm={() => {
                close()
              }}
            />
          ),
        })
      } else {
        emit('allSaveSuccess')
      }
    })
  }

  return () => (
    <div class="text-sm text-gray-500">
      <div class="text-base font-medium text-[#141414]">已查询{selectedDramas.value.length}部短剧，该操作将修改这{selectedDramas.value.length}部短剧的上架状态</div>
      <div class="flex items-center gap-8 py-4">
        <label class="flex items-center gap-2">
          <span class="shrink-0">上架状态</span>
          <select
            class="select select-bordered select-sm w-full max-w-xs"
            v-model={listStatus.value}
          >
            {listingStatusList.slice(1).map(option => (
              <option value={option.code}>{option.name}</option>
            ))}
          </select>
        </label>
        {
          (listStatus.value === 2 || listStatus.value === 4)
          && (
            <>
              <label class="flex items-center gap-2">
                <input class="checkbox checkbox-sm checkbox-primary" type="checkbox" v-model={hasTime.value} />
                <span class="shrink-0">{listStatus.value === 2 ? '定时上架' : '定时下架'}</span>
              </label>
              <Input inputClass="input input-bordered input-sm w-full max-w-xs" format="YYYY-MM-DDTHH:mm:ss+08:00" type="datetime" v-model={time.value} disabled={!hasTime.value} />
            </>
          )
        }
      </div>
      { selectedDramas.value.every(item => import.meta.env.VITE_DRAMA_WAVE_APP_ID.split(',').includes('' + item.app_id))
        ? (
            <div class="gap-8 pb-4">
              <label class="flex cursor-pointer items-center gap-2">
                <span class="shrink-0">双端同步</span>
                <input class="checkbox checkbox-sm checkbox-primary" type="checkbox" v-model={platform_sync_list.value} />
              </label>
            </div>
          )
        : null}
      {
        listStatus.value === -1
          ? (
              <div class="gap-8 pb-4">
                <label class="flex cursor-pointer items-start gap-2">
                  <span class="shrink-0">下架原因</span>
                  <textarea class="textarea textarea-bordered textarea-sm w-full max-w-xs" v-model={unshelve_reason.value} placeholder="请输入下架原因（必填）" />
                </label>
              </div>
            )
          : null
      }
      <div>
        <p>状态修改说明：</p>
        <p>1. 未上架，可以修改为任意状态</p>
        <p>2. 待上架，可以修改为：已上架、待下架、已下架</p>
        <p>3. 已上架，可以修改为：待上架、待下架、已下架</p>
        <p>4. 待下架，可以修改为：待上架、已上架、已下架</p>
        <p>5. 已下架，可以修改为：待上架、已上架、待下架</p>
      </div>
      <div class="btn-group absolute bottom-4 right-5 flex w-full items-center justify-end gap-3">
        <Button class="btn btn-default btn-sm" onClick={() => emit('cancel')}>
          取消
        </Button>
        <Button class="btn btn-primary btn-sm" onClick={save} disabled={loading.value}>
          {loading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
          保存
        </Button>
      </div>
    </div>
  )
})
