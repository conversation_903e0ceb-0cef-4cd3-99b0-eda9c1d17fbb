<template>
  <div class="flex items-center ml-2" @click="showMenu = true">
    <SvgIcon name="ic-globe-alt" class="size-5 mr-0.5" />
    <!-- <span class="text-sm text-white uppercase">{{ currentLocale.shortCode }}</span> -->
    <SvgIcon name="ic-chevron-right" class="w-4 h-4 text-white" />
  </div>
  <Popup v-model:show="showMenu" position="bottom" class="p-3 pt-0 bg-[var(--grey-13)] rounded-t-xl"
    :style="{ height: '80%' }">
    <h1 class="sticky top-0 py-3 pt-6 text-base font-medium text-white bg-[var(--grey-13)]">{{ CT.displayLanguage() }}
    </h1>
    <ul class="mt-3 text-white">
      <li v-for="localeObject in supportedLocaleList" :key="localeObject.shortCode" class="px-2 py-3"
        :class="{ 'bg-[#2E2F30] rounded-lg': localeObject.shortCode === currentLocale.shortCode }"
        @click="onLangChange(localeObject.language)">
        <SvgIcon :class="localeObject.shortCode === currentLocale.shortCode ? 'text-[#FC2763]' : 'opacity-0'"
          :name="'ic_check'" class="mr-2 size-6" />
        {{ localeObject.name }}
      </li>
    </ul>
  </Popup>
</template>

<script setup lang="tsx">
import { SvgIcon } from '@skynet/ui'
import { ref } from 'vue';
import { Popup } from 'vant';
import { SupportedLocale, supportedLocaleList } from 'src/h5_modules/common/constants';
import { langApi } from './lang-api';
import { useHomePageStore } from '../home/<USER>';
import { useLocale } from '../i18n/i18n';
import { CT } from '../i18n/common.i18n';

const { currentLocale } = useLocale()

const { getHomeTab, getHomeModuleList } = useHomePageStore();

const showMenu = ref(false);

const onLangChange = async function (l: SupportedLocale) {
  const target = supportedLocaleList.find(item => item.language === l)
  currentLocale.value.shortCode = target?.shortCode ?? 'en'
  currentLocale.value.language = target?.language ?? 'en-US'
  showMenu.value = false;
  await langApi.settingLang(l);
  const res = await getHomeTab();
  await getHomeModuleList({ tab_key: res[0].tab_key, position_index: res[0].position_index, first: '' });
};
</script>

<style scoped>
:global(.van-overlay) {
  --van-overlay-background: #0B080B99;
}

:global(.van-popup) {
  --van-popup-background: #1D1D1E
}
</style>
