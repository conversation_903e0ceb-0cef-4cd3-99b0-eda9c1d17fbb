import { ref } from 'vue'
import { apiGetMessageList, apiGetUnreadMessageCount, apiSetMessageRead } from './message-api'

export const useMessage = () => {
  const messageList = ref<M.Message.Message[]>([])
  const messageLoading = ref(false)
  const messageTotal = ref(0)
  const messageRequestParams = ref<M.Message.ListParams>({
    type: 0,
    page_index: 1,
    page_size: 10,
  })
  const getMessageList = async () => {
    try {
      messageLoading.value = true
      const res = await apiGetMessageList(messageRequestParams.value)
      if (res.code === 200) {
        messageList.value = messageList.value.concat(res.data?.list || [])
        messageTotal.value = res.data?.total || 0
      }
    } catch (error) {
      console.log(error)
    }
    messageLoading.value = false
  }
  const getUnreadMessageCount = async () => {
    const res = await apiGetUnreadMessageCount()
    if (res.code === 200) {
      return res.data?.unread_num || 0
    }
    return 0
  }

  const setMessageReadStatus = async (ids: number[]) => {
    const res = await apiSetMessageRead({
      ids: ids, // 默认是所有
    })
    if (res.code === 200) {
      return res.data
    }
    return false
  }
  return {
    messageRequestParams,
    messageList,
    messageTotal,
    getMessageList,
    getUnreadMessageCount,
    setMessageReadStatus,
    messageLoading,
  }
}
