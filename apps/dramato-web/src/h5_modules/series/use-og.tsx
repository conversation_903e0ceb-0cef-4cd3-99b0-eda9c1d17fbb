import { watch } from 'vue'
import { useSeriesItem } from './use-series-item'
import { setOgInfo } from 'src/utils/og-helper'

export function useOg(series_id: string) {
  const { seriesItem } = useSeriesItem(series_id)
  // 监听数据变化，更新 og 信息
  watch(() => seriesItem.value, newItem => {
    if (newItem) {
      setOgInfo({
        title: newItem.name,
        description: newItem.desc,
        image: newItem.cover,
        url: window.location.href,
      })
    }
  }, { immediate: true })
}
