import { createComponent } from '@skynet/shared'
import { T } from './activity-payment.i18n'
import { computed } from 'vue'
import { gotoRules } from '../common/common'
import { logo } from './images/images'
export const CommonRules = createComponent(null, () => {
  const activityRuleList = computed(() => {
    return T.paymentPage.noticeDetail.ref.value.split('\n').map(item => item.replace(/^\d./g, ''))
  })
  return () => (
    <x-rules class="mt-5 flex flex-col px-3 text-xs text-[#FFFFFFB2]">
      <div class="text-sm font-bold text-white">{T.paymentPage.notice()}:</div>
      {
        activityRuleList.value.map((item, index) => (
          <p class="mt-3" key={index}>{index + 1}. {item}</p>
        ))
      }
      <p class="mt-3" innerHTML={T.paymentPage.contact().replace('<EMAIL>', `<a href="mailto:<EMAIL>" class="underline"><EMAIL></a>`)} />
      <div class="mt-3 h-3.5 text-center text-xs font-normal text-white">
        <span class="underline" onClick={() => {
          gotoRules('terms')
        }}>{T.paymentPage.termsOfUse()}</span>
        <span> · </span>
        <span class="underline" onClick={() => {
          gotoRules('privacy')
        }}>{T.paymentPage.privacyPolicy()}</span>
      </div>
      <img src={logo} class="mt-60px share-bottom-2 mx-auto block h-6 w-auto" />
    </x-rules>
  )
})

export default CommonRules
