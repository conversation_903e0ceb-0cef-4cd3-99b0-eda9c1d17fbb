import { MetaInfo } from '../types/meta'

/**
 * 设置页面的 Open Graph 元信息
 * @param meta Open Graph 元信息对象
 */
export function setOgInfo(meta: MetaInfo) {
  const { title, description, image, url } = meta

  // 设置 og:title
  const ogTitleElement = document.querySelector('meta[property="og:title"]')
  if (ogTitleElement) {
    ogTitleElement.setAttribute('content', title || '')
  }

  // 设置 og:description
  const ogDescElement = document.querySelector('meta[property="og:description"]')
  if (ogDescElement) {
    ogDescElement.setAttribute('content', description || '')
  }

  // 设置 og:image
  const ogImageElement = document.querySelector('meta[property="og:image"]')
  if (ogImageElement) {
    ogImageElement.setAttribute('content', image || '')
  }

  // 设置 og:url
  const ogUrlElement = document.querySelector('meta[property="og:url"]')
  if (ogUrlElement) {
    ogUrlElement.setAttribute('content', url || '')
  }

  // 设置普通 description
  const descElement = document.querySelector('meta[property="description"]')
  if (descElement) {
    descElement.setAttribute('content', description || '')
  }

  // 设置页面标题
  document.title = title || 'DramaWave'

  // 设置隐藏的 h1 标签内容（用于 SEO）
  const ogDescriptionH1 = document.getElementById('og-description')
  if (ogDescriptionH1) {
    ogDescriptionH1.textContent = description || ''
  }
}
