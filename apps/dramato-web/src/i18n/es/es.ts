export const es = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: 'Opciones Populares',
    },
  },
  myList: {
    watchHistory: 'Ver Historial',
    myList: 'Mi Lista',
    seeAll: 'Ver todo',
    noData: 'No hay resultados relacionados',
  },
  // 分享页
  share: {
    watchNow: 'Ver Ahora',
    episodeDialog: {
      title: '¡Únete a mí y consigue monedas extra!',
      description: '¡Estoy enganchado con esta serie!',
      tips: 'Recomendación',
      button: 'Ver Drama Completo',
      limit24: 'Tiempo limitado: 24h',
    },
    toast: {
      takeOffShelf: 'El contenido no está disponible',
    },
  },
  'customer-service-center': {
    title: 'Centro de atención al cliente',
    description: 'Descripción',
    'description-placeholder': 'Proporcione detalles para ayudarnos a entender el problema',
    'email-placeholder': 'Introduzca su dirección de correo electrónico para recibir actualizaciones',
    'submit-feedback': 'Enviar',
    'enter-feedback': 'Comentarios sobre otros problemas',
    'upload-pictures': 'Subir fotos',
    feedback: 'Comentarios',
    'problem-type': 'Tipo de problema',
    'problem-type-placeholder': 'Por favor seleccione',
    'payment-issue-placeholder': 'Proporcione su ID y el recibo del pedido: Google: https://pay.google.com/ (el número de pedido comienza con GPA...) iOS: App Store - Apple ID - historial de compras (ID de pedido)',
    'content-issue-placeholder': 'Proporcione el título del drama, los números de los episodios y los idiomas de los subtítulos que faltan.',
    'ads-issue-placeholder': 'Proporcione la configuración de modelo, país e idioma de su dispositivo.',
    'technical-issue-placeholder': 'Proporcione la configuración de modelo, país e idioma de su dispositivo.',
    'other-issue-placeholder': 'Proporcione más detalles, incluido el modelo de su dispositivo y el país.',
    'i-want-to-give-feedback': 'Quiero hacer una reseña',
    'are-you-satisfied-with-this-reply': '¿Estás satifecho/a con esta respuesta?',
    unsatisfied: 'Insatisfecho',
    satisfied: 'Satisfecho',
    myFeedback: 'Mis Comentarios',
    'reply-to-feedback': '',
    'description-min-length': 'Describe el problema (al menos 6 caracteres).',
    'problem-type-not-choose': 'Por favor, selecciona el tipo de comentario.',
    'description-empty': 'El campo no puede estar vacío',
  },
  terminate: {
    question: '¿Cómo gestiono mi suscripción?',
    desc: 'Haz clic en el botón de abajo para acceder a la página de gestión de contratos. Puedes gestionar tu suscripción haciendo clic en el botón de contrato:',
    title: 'Gestión de Contrato',
    expiration: 'Tiempo de Expiración',
    manageDesc: 'Relación Contractual',
  },
  toast: {
    'upload-failed': 'Carga fallida, por favor, vuelva a cargar',
    'submit-success': 'Enviar correctamente',
    'email-incorrect': 'El formato del correo electrónico es incorrecto',
  },
  not_found: {
    title: 'La página que está buscando no existe',
  },
  regulations: {
    howUseData: 'Cómo usamos sus datos',
    content: `Si selecciona "Permitir" en la solicitud de permiso de Transparencia de Seguimiento de Aplicaciones, utilizaremos su información y datos para optimizar nuestros servicios y brindarle la mejor experiencia, de lo contrario, solo recopilaremos los datos y no realizaremos ninguna actividad de seguimiento.

Si acepta permitir que DramaWave y nuestros socios y proveedores utilicen datos de su dispositivo para analizar cómo utiliza la aplicación, personalizar las recomendaciones de contenido y proporcionar más servicios relacionados con la información, haga clic en "Aceptar y Continuar" para continuar.`,
    acceptAll: 'Aceptar todo',
    dataSettings: 'Configuración de datos',
    next: 'Siguiente',
    cancel: 'Cancelar',
    aboutYourPrivacy: 'Sobre su privacidad',
    personalDataName: 'Datos personalizados',
    personalDataDescription: 'Utilizamos esta información para recordar sus elecciones y proporcionar recomendaciones de publicidad y contenido personalizadas, notificaciones y recordatorios, etc.',
    advertisementDataName: 'Datos de publicidad',
    advertisementDataDescription: 'Las cookies, los identificadores de su dispositivo u otra información pueden almacenarse o accederse en su dispositivo con fines de publicidad en línea. Los datos del usuario también pueden compartirse con nuestros socios publicitarios.',
    behaviorsDataName: 'Datos de comportamiento',
    behaviorsDataDescription: 'Los datos relacionados con el análisis, como las cookies o los identificadores de dispositivos, pueden almacenarse para medir y analizar el uso, el comportamiento y el rendimiento de la publicidad.',
    privacyAndTerms: 'Política de privacidad y términos de servicio',
    referToPrivacy: 'Consulte la política de privacidad',
    referToTerms: 'Consulte los términos de servicio',
    agreeAndSave: 'Aceptar y guardar',
  },
  coins: {
    earnings: {
      title: 'Mis Ganancias',
      total: 'Total de Diamantes',
      exchange: 'Canjear',
      earningsHistory: 'Historial de Ganancias',
      exchangeHistory: 'Historial de Canjes',
      noRecords: 'Sin Registro',
      last50earnings: 'Sólo se Muestran las Últimas 50 Ganancias',
      last50exchanges: 'Sólo se Muestran los Últimos 50 Registros de Canjes',
      benefits: 'Beneficios',
      gotIt: 'Entiendido',
    },
    exchange: {
      title: 'Zona de Canje',
      notOpen: 'Todavía no ha sido abierta, por favor estate atento',
      receiveBtn: 'Recibe felizmente',
      earnCoins: 'Ganar Diamantes',
      redeemRequiredXCoins: 'Canjear diamantes {X} requeridas',
      exchangeSuccess: 'Canjear con éxito',
      cardId: 'ID TARJETA',
      exchangeFailed: 'Canje fallido',
      transferFailedInsufficientStock: 'Agotado. Por favor ven mañana temprano.',
      gotItBtn: 'Entendido',
      transferFailedRiskControl: 'Cuenta inusual, temporalmente incapaz de canjear.',
      tryAgainBtn: 'Inténtalo de nuevo más tarde',
      transferConfirm: 'Confirmar transferencia',
      paypalAccountEmail: 'Correo electrónico de la cuenta Paypal',
      confirmPaypalAccountEmail: 'Confirmar email de cuenta Paypal',
      transferX: 'Transferir {X}',
      transferXConfirm: 'Confirmar la transferencia {X}',
      transferToYourAccountX: 'Transferencia a su cuenta {X}',
      confirmBtn: 'Confirmar',
      redeemFailed: 'Canjeo fallido',
      redeemFailedContent: 'Hubo un error. Por favor, intenta nuevamente más tarde.',
      transferSuccessTitle: '¡Estamos enviando el dinero! ',
      transferSuccessContent: 'Tu dinero llegará en un plazo de 5 minutos a 72 horas!',
      copySuccess: 'Copiado con éxito',
      enterEmailError: 'Por favor, introduzca una dirección de correo válida',
      enterEmailValidError: 'Error de escritura, por favor escríbelo de nuevo',
      emailNotMatchError: 'La dirección de correo no coincide, por favor introdúzcala otra vez',
    },
  },
  meal_check_in: {
    sleeping_link: 'Gana diamantes mientras duermes',
    rules_title: 'Reglas de actividad',
    benefits_title: 'Benefits',
    rules_1: '1. Todos los días hay cuatro franjas horarias para registrar tus comidas: Desayuno: 07:00-10:00; Almuerzo: 11:00-14:00; Cena: 17:00-20:00; Bocadillo nocturno: 21:00-24:00.',
    rules_2: '2. Si pierdes el tiempo de registro correspondiente, puedes ver un video para reclamar la recompensa.',
    rules_3: '3. Las recompensas se restablecen todos los días a medianoche, así que no olvides reclamarlas.',
  },
  sleep_check_in: {
    earn_link: 'Registrar comidas para ganar',
    benefits_title: 'Benefits',
    rules_1:
      '1. Cada noche, de 7 p. m. a 12 a. m., puedes activar el modo \'Dormir para ganar\'.',
    rules_2:
      '2. Después de activarlo, al día siguiente, después de dormir 8 horas, puedes reclamar la recompensa por dormir entre las 8 a. m. y las 12 p. m.',
    rules_3:
      '3. Si te pierdes el tiempo para reclamar la recompensa, puedes ver un video entre las 12 p. m. y las 7 p. m. al día siguiente para reclamarla nuevamente.',
  },
  bind_email: {
    title: 'Introduzca su correo electrónico',
    list_1: 'Vincula tu dirección de correo electrónico para obtener monedas de bonificación.',
    list_2: 'Recibe notificaciones sobre próximos eventos y ofertas especiales.',
    list_3: 'Se la primera persona en enterarse de los nuevos episodios.',
    email_placeholder: 'Por favor, introduzca su dirección de correo electrónico.',
    verify: 'Verificar',
    email_error: 'Por favor, ingrese una dirección de correo electrónico válida.',
    verify_progress: 'Verificando correo electrónico',
    verify_success: '¡Recuperación realizada con éxito!',
    re_enter: 'Volver a ingresar',
    email_repeat: '¡Este correo electrónico ya ha participado en la actividad!',
  },
  infinityScroll: {
    end: 'Fin',
    loading: 'Cargando...',
  },
}
