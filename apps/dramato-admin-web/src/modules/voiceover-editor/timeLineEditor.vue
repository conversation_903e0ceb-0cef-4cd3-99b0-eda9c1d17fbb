<template>
  <!-- 添加一个根div来包裹所有内容 -->
  <div class="timeline-editor-wrapper">
    <div class="timeline-editor">
      <input 
        type="file" 
        ref="videoInput" 
        accept="video/*" 
        style="display: none" 
        @change="handleVideoImport"
      />
      <input 
        type="file" 
        ref="audioInput" 
        accept="audio/*" 
        style="display: none" 
        @change="handleAudioImport"
      />
      <input 
        type="file" 
        ref="backgroundAudioInput" 
        accept="audio/*" 
        style="display: none" 
        @change="handleBackgroundAudioImport"
      />
      <div class="toolbar flex justify-between">
        <!-- <el-button @click="importVideo">导入视频</el-button>
        <el-button @click="importAudio">导入AI配音</el-button>
        <el-button @click="importBackgroundAudio">导入背景音乐</el-button>
        <el-button @click="addSubtitle">新建字幕</el-button>
        <el-button @click="importData">导入数据</el-button>
        <el-button @click="exportTimeline">导出结果</el-button> -->
        <div class="flex items-center gap-2">
          <el-button @click="onPrevEpisode">上一集</el-button>
          <span>{{serial_number}}</span>
          <el-button @click="onNextEpisode">下一集</el-button>
        </div>
        <div>
          <span v-if="loading || loadedResources < totalResources">资源加载中... {{loadedResources}}/{{totalResources}}</span>
          
          <el-button type="warning" :disabled="!state.selectedClip" @click="handleSplit">
            分割
          </el-button>
          <el-button type="danger" :disabled="!state.selectedClip" @click="handleDelete">
            删除
          </el-button>
          <el-button 
            @click="togglePlay" 
            :type="state.isPlaying ? 'danger' : 'primary'"
            :disabled="loading || loadedResources < totalResources"
          >
            {{ state.isPlaying ? '暂停' : '播放' }}
          </el-button>
        </div>
        <!-- <el-progress 
          v-if="loading" 
          :percentage="(loadedResources / totalResources) * 100" 
          :format="() => `加载中 ${loadedResources}/${totalResources}`"
        />
        <el-slider
          v-model="zoomLevel"
          :min="1"
          :max="10"
          @input="handleZoom"
        ></el-slider>
        <div class="track-visibility-controls">
          <el-switch
            v-model="trackVisibility.video"
            active-text="视频轨道"
            @change="handleTrackVisibilityChange"
          />
          <el-switch
            v-model="subtitleEditable"
            active-text="允许编辑字幕"
          />
        </div> -->
      </div>
      
      <div class="timeline-container h-[250px]" ref="container" v-loading="loading || loadedResources < totalResources" element-loading-background="rgba(122, 122, 122, 0.8)">
        <div class="track-headers">
          <div class="track-header mt-[20px]">AI配音音轨</div>
          <div class="track-header">字幕</div>
          <div class="track-header">背景音轨</div>
          <div class="track-header" v-show="trackVisibility.video">视频</div>
        </div>
        
        <div class="canvas-container" 
          @wheel.prevent="handleScroll"
          @mousemove="handleMouseMove"
          @mousedown="handleMouseDown"
          @mouseup="handleMouseUp"
          @mouseleave="handleMouseUp"
          @click="handleTimelineClick"
          @dblclick="handleDoubleClick"
          style="cursor: default;">
          <canvas ref="timelineCanvas"></canvas>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { watch } from 'vue'

interface VideoThumbnails {
  [key: string]: string
}

interface Clip {
  id: string
  type: 'subtitle' | 'audio' | 'main-audio' | 'video'
  start_time: number
  duration: number
  sourceDuration: number
  head: number
  tail: number
  content?: string
  url?: string
  isMainTrack?: boolean
  tts_text?: string
  speed: number
  order: number
  voice_path: string
  waveform?: number[]
  videoElement?: HTMLVideoElement
  thumbnails?: VideoThumbnails
  videoWidth?: number
  videoHeight?: number
  audioElement?: HTMLAudioElement
}

// 添加导入数据的接口定义
interface ImportData {
  clips: {
    id: string
    type: Clip['type']
    start_time: number
    duration: number
    sourceDuration?: number
    head: number
    tail: number
    content?: string
    url?: string // 添加 url 字段
    isMainTrack?: boolean
    tts_text?: string
    speed?: number // 添加速度属性，默认为1
  }[]
}

export default defineComponent({
  name: 'TimelineEditor',
  // 添加 emits 定义
  emits: ['video-imported', 'time-change', 'play-state-change', 'voice-selected', 'prevEpisode', 'nextEpisode', 'voice-split', 'voice-delete'],
  props: {
    voicesList: {
      type: Array,
      default: () => [],
    },
    totalData: {
      type: Object,
      default: () => {},
    },
    serial_number: {
      type: Number,
      default: 0,
    },
  },
  setup(props, { emit }) {
    const container = ref<HTMLDivElement | null>(null)
    const timelineCanvas = ref<HTMLCanvasElement | null>(null)
    const ctx = ref<CanvasRenderingContext2D | null>(null)
    
    const trackVisibility = reactive({
      audio: true,      // AI配音音轨
      subtitle: true,   // 字幕
      'main-audio': true, // 背景音轨
      video: false,      // 视频
    })

    const state = reactive({
      clips: [] as Clip[],
      videoDuration: 300, // 设置一个默认视频时长，比如300秒
      timelineWidth: 0,
      scrollLeft: 0,
      isPlaying: false,
      currentTime: 0,
      lastFrameTime: 0, // 用于计算帧间隔
      zoomLevel: 1,
      mouseX: 0,
      selectedClip: null as Clip | null,
      isDragging: false,
      isResizing: false,
      resizeDirection: '' as 'left' | 'right' | '',
      minZoom: 0.1, // 最小缩放比例
      maxZoom: 10,  // 最大缩放比例
      viewportWidth: 0, // 视口宽度
      contentWidth: 0,  // 内容总宽度
      dragStartX: 0,
      dragStartScrollLeft: 0,
      dragStartClipTime: 0,
      resizeStartWidth: 0,
      editingClip: null as Clip | null,
      editingContent: '',
      dragStartEndTime: 0, // 添加这个属性
      dragStartTrimStart: 0, // 添加这个属性来记录初始裁剪起点
      dragStartDuration: 0, // 添加这个属性来记录初始持续时间
      syncScheduled: false, // 添加这个属性来控制同步调度
      subtitleEditable: false, // 添加字幕编辑开关
      dragStartTail: 0,  // 添加这一行
    })

    // 添加文件输入引用
    const videoInput = ref<HTMLInputElement | null>(null)
    const audioInput = ref<HTMLInputElement | null>(null)
    const backgroundAudioInput = ref<HTMLInputElement | null>(null)

    // 添加加载状态
    const loading = ref(false)
    const loadedResources = ref(0)
    const totalResources = ref(0)

    // 修改常量和相关尺寸
    const TRACK_HEIGHT = 60  // 轨道高度
    const SCALE_HEIGHT = 20  // 时间刻度高度
    const TRACK_PADDING = 4  // 轨道内边距
    const TOTAL_TRACKS = 4   // 总轨道数

    // 在 setup 中添加字幕编辑开关
    const subtitleEditable = ref(false)

    // 初始化Canvas
    const initCanvas = () => {
      if (!timelineCanvas.value || !container.value) return
      
      const dpr = window.devicePixelRatio || 1
      const containerWidth = container.value.clientWidth
      const containerHeight = container.value.clientHeight
      const trackHeaderWidth = 100 // 左侧轨道标题的宽度
      
      // 计算实际可用于时间轴的宽度
      const timelineWidth = containerWidth - trackHeaderWidth
      
      state.viewportWidth = timelineWidth
      state.contentWidth = Math.max(
        state.videoDuration * 100 * state.zoomLevel,
        timelineWidth
      )
      
      // 设置 Canvas 的实际大小
      timelineCanvas.value.width = timelineWidth * dpr
      timelineCanvas.value.height = containerHeight * dpr
      
      // 设置 Canvas 的显示大小
      timelineCanvas.value.style.width = `${timelineWidth}px`
      timelineCanvas.value.style.height = `${containerHeight}px`
      
      ctx.value = timelineCanvas.value.getContext('2d')
      if (ctx.value) {
        // 重置变换
        ctx.value.setTransform(1, 0, 0, 1, 0, 0)
        // 应用 DPR 缩放
        ctx.value.scale(dpr, dpr)
      }
      
    }


    // 绘制时间轴
    const drawTimeline = () => {
      if (!ctx.value || !timelineCanvas.value) return
      
      // 清空画布
      ctx.value.clearRect(0, 0, timelineCanvas.value.width, timelineCanvas.value.height)
      
      // 绘制时间刻度
      drawTimeScale()
      
      // 绘制轨道背景
      drawTrackBackground()
      
      // 绘制片段
      drawClips()
      
      // 绘制播放线
      drawPlayhead()
      
      // 绘制鼠标位置线
      // drawMouseLine()
    }

    // 绘制时间刻度
    const drawTimeScale = () => {
      if (!ctx.value) return
      
      const pixelsPerSecond = 100 * state.zoomLevel
      const totalSeconds = Math.max(state.videoDuration, 60)
      
      ctx.value.fillStyle = '#333'
      ctx.value.fillRect(0, 0, timelineCanvas.value!.width, SCALE_HEIGHT)
      
      // 根据缩放级别决定刻度线的间隔
      let mainInterval = 1 // 主刻度间隔（秒）
      let subDivisions = 4 // 默认每秒4个子刻度（显示0.25秒的刻度）
      
      if (state.zoomLevel >= 5) {
        // 非常放大时，显示更密集的子刻度
        mainInterval = 1
        subDivisions = 10 // 每秒10个子刻度（0.1秒）
      } else if (state.zoomLevel >= 2) {
        // 较放大时
        mainInterval = 1
        subDivisions = 5 // 每秒5个子刻度（0.2秒）
      } else if (state.zoomLevel <= 0.5) {
        // 缩小时，增加主刻度间隔，减少子刻度
        mainInterval = 5
        subDivisions = 5 // 每个主刻度5个子刻度（1秒）
      } else {
        // 正常缩放级别
        mainInterval = 1
        subDivisions = 4 // 每秒4个子刻度（0.25秒）
      }
      
      // 计算可见区域的时间范围
      const start_time = Math.floor(state.scrollLeft / pixelsPerSecond)
      const endTime = Math.ceil((state.scrollLeft + timelineCanvas.value!.width) / pixelsPerSecond)
      
      // 绘制刻度和时间文本
      ctx.value.fillStyle = '#fff'
      ctx.value.font = '12px Arial'
      
      // 绘制主刻度和子刻度
      for (let i = start_time; i <= endTime; i++) {
        // 主刻度
        if (i % mainInterval === 0) {
          const x = i * pixelsPerSecond - state.scrollLeft
          
          // 绘制主刻度线（更长更粗）
          ctx.value.fillStyle = '#fff'
          ctx.value.fillRect(x - 1, SCALE_HEIGHT - 12, 2, 12)
          
          // 绘制时间文本背景
          const timeText = formatTime(i * 1000)
          const textWidth = ctx.value.measureText(timeText).width
          
          // 绘制半透明背景
          ctx.value.fillStyle = 'rgba(51, 51, 51, 0.8)'
          ctx.value.fillRect(x - textWidth / 2 - 2, 2, textWidth + 4, 14)
          
          // 绘制时间文本
          ctx.value.fillStyle = '#fff'
          ctx.value.textAlign = 'center'
          ctx.value.textBaseline = 'middle'
          ctx.value.fillText(timeText, x, 9)
        }
        
        // 子刻度
        if (subDivisions > 1) {
          const subInterval = mainInterval / subDivisions
          for (let j = 1; j < subDivisions; j++) {
            const subX = (i + j * subInterval) * pixelsPerSecond - state.scrollLeft
            
            // 绘制子刻度线（较短较细）
            ctx.value.fillStyle = '#aaa' // 使用较浅的颜色
            ctx.value.fillRect(subX - 0.5, SCALE_HEIGHT - 6, 1, 6)
          }
        }
      }
    }

    // 时间格式化函数
    const formatTime = (ms: number): string => {
      const totalSeconds = ms / 1000
      const minutes = Math.floor(totalSeconds / 60)
      const seconds = Math.floor(totalSeconds % 60)
      const milliseconds = Math.floor((totalSeconds % 1) * 10) // 只显示一位小数
      
      if (state.zoomLevel >= 2) {
        // 放大时显示毫秒
        return `${minutes}:${seconds.toString().padStart(2, '0')}.${milliseconds}`
      } else {
        // 正常显示
        return `${minutes}:${seconds.toString().padStart(2, '0')}`
      }
    }

    // 绘制轨道背景
    const drawTrackBackground = () => {
      if (!ctx.value || !timelineCanvas.value) return
      
      let currentY = SCALE_HEIGHT
      const trackColors = [
        ['#2a3a4a', '#243240'], // AI配音音轨
        ['#2c2c2c', '#262626'], // 字幕
        ['#3a2c2c', '#342626'], // 背景音轨
        ['#2c2c2c', '#262626'], // 视频
      ]
      
      // 按顺序绘制可见轨道的背景
      const tracks: Array<keyof typeof trackVisibility> = ['audio', 'subtitle', 'main-audio', 'video']
      tracks.forEach((track, i) => {
        if (trackVisibility[track]) {
          ctx.value!.fillStyle = i % 2 === 0 ? trackColors[i][0] : trackColors[i][1]
          ctx.value!.fillRect(
            0,
            currentY,
            timelineCanvas.value!.width,
            TRACK_HEIGHT
          )
          
          // 绘制轨道分隔线
          ctx.value!.strokeStyle = '#333'
          ctx.value!.beginPath()
          ctx.value!.moveTo(0, currentY + TRACK_HEIGHT)
          ctx.value!.lineTo(timelineCanvas.value!.width, currentY + TRACK_HEIGHT)
          ctx.value!.stroke()
          
          currentY += TRACK_HEIGHT
        }
      })
    }

    // 绘制片段
    const drawClips = () => {
      if (!ctx.value || !timelineCanvas.value) return
      
      const contentHeight = TRACK_HEIGHT - TRACK_PADDING * 2
      const pixelsPerSecond = 100 * state.zoomLevel
      
      // 按轨道类型分组绘制
      const drawClipsByType = (type: Clip['type']) => {
        // 如果轨道被隐藏，不绘制该类型的片段
        if (!trackVisibility[type]) {
          return
        }
        // 获取该类型的所有片段
        const clips = state.clips.filter(clip => clip.type === type)
        
        // 对同类型的片段进行排序，选中的片段放在最上面
        const sortedClips = [...clips].sort((a, b) => {
          if (a === state.selectedClip) return 1
          if (b === state.selectedClip) return -1
          return 0
        })
        
        // 绘制该类型的所有片段
        sortedClips.forEach(clip => {
          // 如果是视频片段且视频轨道被隐藏，跳过绘制
          if (clip.type === 'video' && !trackVisibility.video) return
          
          const x = clip.start_time * pixelsPerSecond - state.scrollLeft
          let width = clip.duration * pixelsPerSecond
          
          // 根据不同类型的片段计算实际显示宽度
          if (clip.type === 'audio' && clip.speed) {
            // 对于音频片段，考虑播放速度影响
            const originalDuration = clip.sourceDuration! - (clip.head / 1000 / clip.speed) - (clip.tail / 1000 / clip.speed)
            width = (originalDuration / clip.speed) * pixelsPerSecond
          }
          
          const y = getTrackY(clip)
          
          // 根据片段类型设置不同的背景颜色
          if (clip === state.selectedClip) {
            ctx.value!.fillStyle = '#4a9eff' // 选中状态颜色保持不变
          } else {
            // 根据类型设置不同的默认颜色
            switch (clip.type) {
              case 'subtitle':
                ctx.value!.fillStyle = 'rgba(61, 69, 85, 0.85)' // 半透明的字幕背景色
                break
              default:
                ctx.value!.fillStyle = '#3a3a3a' // 其他类型保持原来的颜色
            }
          }
          
          // 绘制片段背景
          ctx.value!.fillRect(x, y + TRACK_PADDING, width, contentHeight)
          
          // 绘制片段边框，字幕边框使用更亮的颜色
          if (clip.type === 'subtitle') {
            ctx.value!.strokeStyle = '#8899aa' // 更亮的字幕边框颜色
            ctx.value!.lineWidth = 2 // 加粗字幕边框
          } else {
            ctx.value!.strokeStyle = '#555'
            ctx.value!.lineWidth = 1
          }
          ctx.value!.strokeRect(x, y + TRACK_PADDING, width, contentHeight)
          
          // 绘制调整手柄，但不为主轨道绘制
          if (clip === state.selectedClip && !clip.isMainTrack) {
            ctx.value!.fillStyle = '#fff'
            ctx.value!.fillRect(x - 2, y + TRACK_PADDING, 4, contentHeight)
            ctx.value!.fillRect(x + width - 2, y + TRACK_PADDING, 4, contentHeight)
          }
          
          // 根据类型绘制特定内容
          switch (type) {
            case 'video':
              if (trackVisibility.video && clip.thumbnails && clip.videoWidth && clip.videoHeight) {
                const maxHeight = contentHeight
                
                // 根据原始视频比例计算缩略图尺寸
                const aspectRatio = clip.videoWidth / clip.videoHeight
                let thumbWidth, thumbHeight
                
                if (aspectRatio > 1) {
                  // 宽视频
                  thumbHeight = Math.min(maxHeight, maxHeight / aspectRatio)
                  thumbWidth = thumbHeight * aspectRatio
                } else {
                  // 高视频
                  thumbWidth = Math.min(maxHeight * aspectRatio, maxHeight)
                  thumbHeight = thumbWidth / aspectRatio
                }
                
                // 计算垂直居中的Y坐标
                const thumbY = y + (TRACK_HEIGHT - thumbHeight) / 2
                
                // 计算需要绘制的缩略图数量
                const numThumbs = Math.ceil(width / thumbWidth)
                
                for (let i = 0; i < numThumbs; i++) {
                  const thumbX = x + i * thumbWidth
                  if (thumbX + thumbWidth > x + width) continue
                  
                  // 计算当前缩略图对应的时间点（向下取整到最近的秒）
                  const timeOffset = Math.floor(i * thumbWidth / (100 * state.zoomLevel))
                  const thumbnail = clip.thumbnails[timeOffset]
                  
                  if (thumbnail) {
                    const img = new Image()
                    img.src = thumbnail
                    ctx.value!.drawImage(img, thumbX, thumbY, thumbWidth, thumbHeight)
                  }
                }
              }
              break
              
            case 'subtitle':
              if (clip.content) {
                ctx.value!.fillStyle = '#ffffff' // 更亮的文字颜色
                ctx.value!.font = '12px Arial'
                ctx.value!.textBaseline = 'middle'
                ctx.value!.textAlign = 'left' // 确保文本左对齐
                
                // 计算文本显示区域
                const padding = 5
                const maxWidth = Math.max(0, width - padding * 2) // 确保不会是负值
                const textY = y + TRACK_HEIGHT / 2
                const textX = x + padding
                
                // 如果片段太窄，不显示文本
                if (maxWidth <= 0) return
                
                // 添加省略号的文本截断
                const text = clip.content
                let displayText = text
                
                // 测量文本宽度
                const textWidth = ctx.value!.measureText(text).width
                
                if (textWidth > maxWidth) {
                  // 如果文本太长，需要截断
                  let ellipsis = '...'
                  let truncated = text
                  const ellipsisWidth = ctx.value!.measureText(ellipsis).width
                  
                  // 逐字符截断，直到文本加省略号能放入可用宽度
                  while (truncated.length > 0 && 
                         ctx.value!.measureText(truncated + ellipsis).width > maxWidth) {
                    truncated = truncated.slice(0, -1)
                  }
                  
                  displayText = truncated + ellipsis
                }
                
                // 使用 clip() 确保文本不会超出片段区域
                ctx.value!.save() // 保存当前上下文状态
                
                // 创建裁剪区域
                ctx.value!.beginPath()
                ctx.value!.rect(x, y + TRACK_PADDING, width, contentHeight)
                ctx.value!.clip()
                
                // 绘制文本
                ctx.value!.fillText(displayText, textX, textY)
                
                ctx.value!.restore() // 恢复上下文状态
              }
              break
              
            case 'main-audio':
            case 'audio':
              if (clip.waveform) {
                const waveformHeight = contentHeight
                const waveformY = y + TRACK_PADDING
                
                // 计算可见区域
                const clipStartX = clip.start_time * pixelsPerSecond
                const clipEndX = clipStartX + (clip.duration * pixelsPerSecond)
                const visibleStartX = Math.max(clipStartX, state.scrollLeft)
                const visibleEndX = Math.min(clipEndX, state.scrollLeft + timelineCanvas.value!.width)
                
                if (visibleEndX > visibleStartX) {
                  ctx.value!.save()
                  ctx.value!.beginPath()
                  if(clip.order === 2){
                    // console.log('clip2-------',clip)
                  }
                  let width = (clip.duration - clip.head / 1000) * pixelsPerSecond
                  if(clip.speed){
                    width = (clip.sourceDuration - (clip.head / 1000 / clip.speed) - (clip.tail / 1000 / clip.speed)) / clip.speed * pixelsPerSecond
                  }
                  ctx.value!.rect(
                    clipStartX - state.scrollLeft,
                    waveformY,
                    width,
                    waveformHeight
                  )
                  ctx.value!.clip()

                  // 计算波形数据
                  const waveformData = clip.waveform
                  const totalSamples = waveformData.length
                  
                  // 计算原始音频中每个采样点对应的时间（秒）
                  const timePerSample = clip.sourceDuration! / totalSamples
                  
                  // 计算裁剪后在原始音频中的时间范围（秒）
                  const clipStartTime = clip.head / 1000  // 裁剪起始时间（秒）
                  const clipDuration = clip.duration      // 裁剪后的持续时间（秒）
                  
                  // 计算对应的采样点范围
                  const startSample = Math.floor(clipStartTime / timePerSample)
                  const samplesNeeded = Math.floor(clipDuration / timePerSample)
                  
                  // 计算显示参数，考虑播放速度
                  const speed = clip.type === 'audio' ? (clip.speed || 1) : 1
                  const displayWidth = clip.duration * pixelsPerSecond
                  // 根据速度调整每像素对应的采样点数
                  const samplesPerPixel = (samplesNeeded * speed) / displayWidth
                  
                  ctx.value!.fillStyle = clip.type === 'main-audio' ? '#8B5CF6' : '#4CAF50'
                  
                  // 绘制波形
                  for (let x = 0; x < displayWidth; x++) {
                    // 根据速度调整采样点位置
                    const sampleIndex = Math.floor(startSample + (x * samplesPerPixel))
                    const nextSampleIndex = Math.floor(startSample + ((x + 1) * samplesPerPixel))
                    
                    // 计算这个范围内的最大振幅
                    let maxAmplitude = 0
                    for (let i = sampleIndex; i < nextSampleIndex; i++) {
                      if (i >= 0 && i < totalSamples) {
                        maxAmplitude = Math.max(maxAmplitude, waveformData[i])
                      }
                    }
                    
                    // 计算绘制位置
                    const barX = clipStartX + x - state.scrollLeft
                    
                    // 只在可见区域内绘制
                    if (barX >= clipStartX - state.scrollLeft && barX <= clipEndX - state.scrollLeft) {
                      const barHeight = Math.max(2, maxAmplitude * waveformHeight * 0.8)
                      
                      // 绘制波形的上下两部分
                      ctx.value!.fillRect(
                        barX,
                        waveformY + (waveformHeight / 2) - barHeight / 2,
                        1,
                        barHeight / 2
                      )
                      ctx.value!.fillRect(
                        barX,
                        waveformY + (waveformHeight / 2),
                        1,
                        barHeight / 2
                      )
                    }
                  }
                  
                  ctx.value!.restore()
                }
              }
              
              // 在波形绘制完成后，添加 order 显示
              if ('order' in clip) {
                ctx.value!.save()
                // 设置文本样式
                ctx.value!.font = '12px Arial'
                ctx.value!.fillStyle = '#ffffff'
                ctx.value!.textBaseline = 'top'
                
                // 计算文本位置（在片段左上角）
                const x = clip.start_time * pixelsPerSecond - state.scrollLeft // 使用片段的实际位置
                const textX = x + 4 // 左边距 4px
                const textY = y + TRACK_PADDING + 4 // 上边距 4px
                
                // 绘制背景
                const orderText = `#${clip.order}`
                const textMetrics = ctx.value!.measureText(orderText)
                const padding = 4
                
                ctx.value!.fillStyle = 'rgba(0, 0, 0, 0.5)' // 半透明黑色背景
                ctx.value!.fillRect(
                  textX - padding,
                  textY - padding,
                  textMetrics.width + padding * 2,
                  16 + padding * 2
                )
                
                // 绘制文本
                ctx.value!.fillStyle = '#ffffff'
                ctx.value!.fillText(orderText, textX, textY)
                
                ctx.value!.restore()
              }
              break
          }
        })
      }
      
      // 按顺序绘制各种类型的片段
      if (trackVisibility.video) {  // 只在视频轨道可见时绘制
        drawClipsByType('video')    // 视频
      }
      drawClipsByType('audio')
      drawClipsByType('main-audio')  // 确保背景音轨也被绘制
      drawClipsByType('subtitle')
    }

    // 绘制播放头
    const drawPlayhead = () => {
      if (!ctx.value || !timelineCanvas.value) return
      
      const pixelsPerSecond = 100 * state.zoomLevel
      const x = state.currentTime * pixelsPerSecond - state.scrollLeft
      
      // 使用 transform 来优化绘制
      ctx.value.save()
      ctx.value.translate(Math.round(x), 0) // 使用 Math.round 避免亚像素渲染
      
      // 绘制播放头线
      ctx.value.strokeStyle = '#ff0000'
      ctx.value.lineWidth = 2
      ctx.value.beginPath()
      ctx.value.moveTo(0, 0)
      ctx.value.lineTo(0, timelineCanvas.value.height)
      ctx.value.stroke()
      
      // 绘制播放头三角形
      ctx.value.fillStyle = '#ff0000'
      ctx.value.beginPath()
      ctx.value.moveTo(-8, 0)
      ctx.value.lineTo(8, 0)
      ctx.value.lineTo(0, 8)
      ctx.value.closePath()
      ctx.value.fill()
      
      ctx.value.restore()
    }

    // 修改 drawMouseLine 方法
    const drawMouseLine = () => {
      if (!ctx.value || !timelineCanvas.value || state.mouseX <= 0) return
      
      const dpr = window.devicePixelRatio || 1
      
      // 保存当前上下文状态
      ctx.value.save()
      
      // 重置变换以确保正确的坐标系
      ctx.value.setTransform(dpr, 0, 0, dpr, 0, 0)
      
      // 计算鼠标线的实际位置，不需要考虑滚动偏移，因为 mouseX 已经是相对于视口的位置
      const mouseXPosition = (state.mouseX - state.scrollLeft) * dpr
      
      // 绘制鼠标线
      ctx.value.strokeStyle = '#666'
      ctx.value.setLineDash([5, 5])
      ctx.value.lineWidth = 1
      ctx.value.beginPath()
      ctx.value.moveTo(mouseXPosition, 0)
      ctx.value.lineTo(mouseXPosition, timelineCanvas.value.height)
      ctx.value.stroke()
      ctx.value.setLineDash([])
      
      // 恢复上下文状态
      ctx.value.restore()
    }

    const togglePlayState = (isPlaying: boolean) => {
      if(state.isPlaying === isPlaying){
        return
      }

      togglePlay()
    }

    const handleDelete = () => {
      if(!state.selectedClip){
        ElMessage.warning('请先选择音频片段')
        return
      }
      emit('voice-delete', {
        order: state.selectedClip.order
      })
    }

    const handleSplit = () => {
      if(!state.selectedClip){
        ElMessage.warning('请先选择音频片段')
        return
      }
      if(state.selectedClip.type !== 'audio'){
        ElMessage.warning('请先选择音频片段')
        return
      }
      if(state.currentTime < state.selectedClip.start_time){
        ElMessage.warning('分割时间不能小于片段开始时间')
        return
      }
      if(state.currentTime > state.selectedClip.start_time + state.selectedClip.duration){
        ElMessage.warning('分割时间不能大于片段结束时间')
        return
      }

      // 计算分割点相对于片段开始的时间（秒）
      const splitPointRelative = state.currentTime - state.selectedClip.start_time
      
      // 计算前半段和后半段的实际时长（考虑速度）
      const speed = state.selectedClip.speed || 1
      const prePartDuration = splitPointRelative
      const afterPartDuration = state.selectedClip.duration - splitPointRelative

      // 计算在原始音频中的分割位置（毫秒）
      const prePartLong = splitPointRelative * speed * 1000

      // 创建新片段（后半段）
      const newClip = {
        start_time: Number((state.currentTime * 1000).toFixed(0)),
        duration: afterPartDuration,
        head: Number((state.selectedClip.head + prePartLong).toFixed(0)),
        tail: Number((state.selectedClip.tail).toFixed(0)),
        tts_text: state.selectedClip.tts_text
      }

      // 更新原片段（前半段）
      state.selectedClip.duration = prePartDuration
      state.selectedClip.tail = Number((state.selectedClip.sourceDuration! * 1000 - state.selectedClip.head - prePartLong).toFixed(0))
      
      // 发送分割事件
      const upData = {
        src_order: state.selectedClip.order,
        sep_seg: newClip,
        src_seg: {
          start_time: Number((state.selectedClip.start_time * 1000).toFixed(0)),
          head: Number(state.selectedClip.head.toFixed(0)),
          tail: Number(state.selectedClip.tail.toFixed(0)),
          duration: state.selectedClip.duration,
          tts_text: state.selectedClip.tts_text
        }
      }

      
      emit('voice-split', upData)
    }

    const appendClip = async (clipData: Clip) => {
      console.log('appendClip----',clipData)
      const newClip: Clip = {
        ...clipData,
        id: clipData.id,
        type: clipData.type,
        start_time: clipData.start_time,
        duration: clipData.duration,
        sourceDuration: clipData.sourceDuration,
        head: clipData.head,
        tail: clipData.tail,
        content: clipData.content,
        isMainTrack: clipData.isMainTrack,
        speed: clipData.speed || 1, // 添加速度属性，默认为1
      }
      const { audioElement, waveform, duration } = await loadAudioFromUrl(clipData.voice_path)
      newClip.audioElement = audioElement
      newClip.waveform = waveform
      newClip.sourceDuration = duration

      console.log('newClip----',newClip)
      state.clips.push(newClip)
      drawTimeline()
    }

    const resetClip = async (clipData: Clip) => {
      console.log('resetClip----',clipData)
      let clip = state.clips.find(clipD => clipD.order === clipData.order)
      if(clip){
        clip = {...clip, ...clipData}
        const { audioElement, waveform, duration } = await loadAudioFromUrl(clipData.voice_path)
        clip.audioElement = audioElement
        clip.waveform = waveform
        clip.sourceDuration = duration
        for(let i = 0; i < state.clips.length; i++){
          if(state.clips[i].order === clipData.order){
            state.clips[i] = clip
          }
        }
        drawTimeline()
      }
    }



      
    const deleteClip = (order: number) => {
      state.clips = state.clips.filter(clip => clip.order !== order)
      drawTimeline()
    }

    // 修改播放控制方法
    const togglePlay = () => {
      if (loading.value || loadedResources.value < totalResources.value) {
        ElMessage.warning('资源正在加载中，请稍后再试')
        return
      }
      
      state.isPlaying = !state.isPlaying
      console.log('state.isPlaying',state.isPlaying)
      emit('play-state-change', state.isPlaying)
      
      if (state.isPlaying) {
        state.lastFrameTime = performance.now()
        // 播放所有音频
        state.clips.forEach(clip => {
          if (clip.audioElement) {
            const clipTime = state.currentTime - clip.start_time
            
            if (clipTime >= 0 && clipTime < clip.duration) {
              let audioTime
              if (clip.type === 'audio') {
                // 考虑播放速度计算实际播放位置
                const speed = clip.speed || 1
                audioTime = clip.head / 1000 + (clipTime * speed)
                
                console.log('AI配音播放位置:', {
                  clipTime,
                  head: clip.head,
                  audioTime,
                  sourceDuration: clip.sourceDuration,
                  duration: clip.duration,
                  speed,
                  headInSeconds: clip.head / 1000
                })
              } else {
                // 其他类型音频保持原有逻辑
                const originalDuration = clip.sourceDuration!
                const trimmedDuration = originalDuration - (clip.head + (clip.tail || 0))
                const playbackRatio = clipTime / clip.duration
                audioTime = clip.head + (trimmedDuration * playbackRatio)
              }
              
              // 设置音频属性
              clip.audioElement.preload = 'auto'
              clip.audioElement.loop = false
              clip.audioElement.currentTime = audioTime
              // 设置播放速率
              if (clip.type === 'audio') {
                clip.audioElement.playbackRate = clip.speed || 1
              }
              
              try {
                const playPromise = clip.audioElement.play()
                if (playPromise !== undefined) {
                  playPromise.catch(error => {
                    console.error('音频播放失败:', error)
                  })
                }
              } catch (error) {
                console.error('音频播放失败:', error)
              }
            }
          }
        })
        
        requestAnimationFrame(updatePlayback)
      } else {
        // 暂停所有音频
        state.clips.forEach(clip => {
          if (clip.audioElement && !clip.audioElement.paused) {
            clip.audioElement.pause()
          }
        })
      }
      emit('play-state-change', state.isPlaying)
    }

    // 更新播放进度
    const updatePlayback = (timestamp: number) => {
      if (!state.isPlaying) return
      
      const deltaTime = (timestamp - state.lastFrameTime) / 1000
      state.lastFrameTime = timestamp
      state.currentTime += deltaTime
      
      // 计算播放线在画布上的位置
      const pixelsPerSecond = 100 * state.zoomLevel
      const playheadX = state.currentTime * pixelsPerSecond - state.scrollLeft
      
      // 检查播放线是否超出可视区域，使用视口宽度作为边界
      if (playheadX < 0 || playheadX >= state.viewportWidth) {
        // 立即将播放线重置到可视区域中间
        state.scrollLeft = Math.max(
          0,
          Math.min(
            state.currentTime * pixelsPerSecond - state.viewportWidth / 2,
            state.contentWidth - state.viewportWidth
          )
        )
        
        // 立即重绘以更新视图
        drawTimeline()
      }
      
      // 处理音频同步
      state.clips.forEach(clip => {
        if (clip.audioElement) {
          const clipTime = state.currentTime - clip.start_time
          
          if (clipTime >= 0 && clipTime < clip.duration && clipTime < (clip.sourceDuration - clip.head / 1000)) {
            if (clip.audioElement.paused) {
              let audioTime
              if (clip.type === 'audio') {
                // 对于AI配音音轨，直接使用裁剪位置
                audioTime = clip.head / 1000 + clipTime
                
                console.log('clip.speed-------',clip.speed)
                clip.audioElement.playbackRate = clip.speed || 1
                console.log('AI配音播放位置2:', {
                  clipTime,
                  head: clip.head,
                  audioTime,
                  sourceDuration: clip.sourceDuration,
                  duration: clip.duration,
                  headInSeconds: clip.head / 1000
                })
              } else {
                // 其他音轨保持原有的计算方式
                const originalDuration = clip.sourceDuration!
                const trimmedDuration = originalDuration - (clip.head + (clip.tail || 0))
                const playbackRatio = clipTime / clip.duration
                audioTime = clip.head + (trimmedDuration * playbackRatio)
              }
              
              clip.audioElement.currentTime = audioTime
              clip.audioElement.loop = false
              if(state.currentTime >= clip.start_time && state.currentTime < clip.start_time + 0.1){
                clip.audioElement.play().catch(() => {})
              }
            }
          } else if (!clip.audioElement.paused && clipTime >= clip.duration) {
            clip.audioElement.pause()
          }
        }
      })
      
      // 检查是否到达背景音轨结束位置
      if (state.currentTime >= state.videoDuration - 2) { // 减去之前添加的2秒缓冲
        // 检查是否还有正在播放的音频
        const hasPlayingAudio = state.clips.some(clip => 
          clip.audioElement && !clip.audioElement.paused && !clip.audioElement.ended
        )

        if (!hasPlayingAudio) {
          // 所有音频都播放完毕，停止播放并重置
          state.clips.forEach(clip => {
            if (clip.audioElement) {
              clip.audioElement.pause()
              clip.audioElement.currentTime = 0
            }
          })
          
          state.currentTime = 0
          state.isPlaying = false
          emit('play-state-change', state.isPlaying)
          state.scrollLeft = 0 // 重置滚动位置到开始
          drawTimeline()
        } else {
          // 还有音频在播放，继续更新
          drawPlayheadOnly()
          requestAnimationFrame(updatePlayback)
        }
      } else {
        drawPlayheadOnly()
        requestAnimationFrame(updatePlayback)
      }
    }

    // 修改 drawPlayheadOnly 方法
    const drawPlayheadOnly = () => {
      if (!ctx.value || !timelineCanvas.value) return
      
      // 完整重绘一次
      drawTimeline()
      
      // 绘制播放头
      const pixelsPerSecond = 100 * state.zoomLevel
      const x = state.currentTime * pixelsPerSecond - state.scrollLeft
      
      // 使用 transform 来优化绘制播放头
      ctx.value.save()
      
      // 绘制播放头线
      ctx.value.strokeStyle = '#ff0000'
      ctx.value.lineWidth = 2
      ctx.value.beginPath()
      ctx.value.moveTo(Math.round(x), 0)
      ctx.value.lineTo(Math.round(x), timelineCanvas.value.height)
      ctx.value.stroke()
      
      // 绘制播放头三角形
      ctx.value.fillStyle = '#ff0000'
      ctx.value.beginPath()
      ctx.value.moveTo(Math.round(x) - 8, 0)
      ctx.value.lineTo(Math.round(x) + 8, 0)
      ctx.value.lineTo(Math.round(x), 8)
      ctx.value.closePath()
      ctx.value.fill()
      
      ctx.value.restore()
    }

    // 添加辅助方法：绘制指定区域的时间刻度
    const drawTimeScaleRegion = (startX: number, endX: number, time: number) => {
      if (!ctx.value) return
      
      ctx.value.fillStyle = '#333'
      ctx.value.fillRect(startX, 0, endX - startX, SCALE_HEIGHT)
      
      // 绘制时间文本
      const timeText = formatTime(time * 1000)
      ctx.value.fillStyle = '#fff'
      ctx.value.font = '12px Arial'
      ctx.value.textAlign = 'center'
      ctx.value.textBaseline = 'middle'
      ctx.value.fillText(timeText, (startX + endX) / 2, SCALE_HEIGHT / 2)
    }

    // 添加辅助方法：绘制指定区域的片段
    const drawClipRegion = (clip: Clip, startX: number, endX: number) => {
      if (!ctx.value) return
      
      const pixelsPerSecond = 100 * state.zoomLevel
      const x = clip.start_time * pixelsPerSecond - state.scrollLeft
      let y = SCALE_HEIGHT
      
      // 根据类型确定在哪个轨道上绘制
      switch (clip.type) {
        case 'subtitle':
          y = SCALE_HEIGHT; break
        case 'audio':
          y = SCALE_HEIGHT + TRACK_HEIGHT; break
        case 'video':
          y = SCALE_HEIGHT + TRACK_HEIGHT * 2; break
      }
      
      // 只绘制可见区域
      ctx.value.save()
      ctx.value.beginPath()
      ctx.value.rect(startX, y, endX - startX, TRACK_HEIGHT)
      ctx.value.clip()
      
      // 绘制片段背景
      ctx.value.fillStyle = clip === state.selectedClip ? '#4a9eff' : '#3a3a3a'
      ctx.value.fillRect(x, y + TRACK_PADDING, clip.duration * pixelsPerSecond, TRACK_HEIGHT - TRACK_PADDING * 2)
      
      ctx.value.restore()
    }

    // 修改导入视频方法
    const importVideo = () => {
      videoInput.value?.click()
    }

    // 修改导入音频方法
    const importAudio = () => {
      audioInput.value?.click()
    }

    const onPrevEpisode = () => {
      emit('prevEpisode')
    }

    const onNextEpisode = () => {
      console.log('onNextEpisode')
      emit('nextEpisode')
    }
    // 添加导入背景音乐方法
    const importBackgroundAudio = () => {
      backgroundAudioInput.value?.click()
    }

    // 添加音频文件加载函数
    const loadAudioFile = async (url: string): Promise<AudioBuffer> => {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const arrayBuffer = await fetch(url).then(response => response.arrayBuffer())
      return await audioContext.decodeAudioData(arrayBuffer)
    }

    // 添加波形数据生成函数
    const generateWaveformData = (audioBuffer: AudioBuffer): number[] => {
      const channelData = audioBuffer.getChannelData(0)
      const sampleRate = 100 // 每秒采样点数
      const duration = audioBuffer.duration
      const samples = Math.floor(sampleRate * duration)
      const blockSize = Math.floor(channelData.length / samples)
      const waveform = []

      for (let i = 0; i < samples; i++) {
        const start = i * blockSize
        const end = start + blockSize
        let max = 0

        for (let j = start; j < end; j++) {
          const amplitude = Math.abs(channelData[j])
          if (amplitude > max) {
            max = amplitude
          }
        }

        waveform.push(max)
      }

      return waveform
    }

    // 修改背景音频导入处理函数
    const handleBackgroundAudioImport = async (url: string) => {
      if (!url) return;
      
      try {
        console.log('Loading background audio from:', url)
        const { audioElement, waveform, duration } = await loadAudioFromUrl(url)
        
        // 移除之前的背景音轨
        state.clips = state.clips.filter(clip => clip.type !== 'main-audio')
        
        // 创建新的背景音轨片段
        const newClip: Clip = {
          id: 'background-audio',
          type: 'main-audio',
          start_time: 0,
          duration: duration,
          sourceDuration: duration,
          head: 0,
          tail: 0,
          waveform,
          audioElement,
          isMainTrack: true,
          order: 0
        }
        
        // 更新时间轴总长度为背景音频长度
        state.videoDuration = duration+2
        
        // 更新内容宽度
        state.contentWidth = Math.max(
          state.videoDuration * 100 * state.zoomLevel,
          state.viewportWidth
        )
        
        console.log('Created new background audio clip:', newClip)
        state.clips.push(newClip)
        
        // 重新初始化画布并重绘
        initCanvas()
        drawTimeline()
      } catch (error) {
        console.error('Error importing background audio:', error)
      }
    }

    // 添加生成缩略图的方法
    const generateThumbnail = async (video: HTMLVideoElement, time: number): Promise<string> => {
      video.currentTime = time
      await new Promise<void>((resolve) => {
        video.onseeked = () => resolve()
      })
      
      const canvas = document.createElement('canvas')
      canvas.width = 320  // 缩略图宽度
      canvas.height = 180 // 缩略图高度
      const ctx = canvas.getContext('2d')
      ctx?.drawImage(video, 0, 0, canvas.width, canvas.height)
      return canvas.toDataURL('image/jpeg', 0.8)
    }

    // 修改 handleVideoImport 方法
    const handleVideoImport = async (event: Event) => {
      const input = event.target as HTMLInputElement
      const file = input.files?.[0]
      if (!file) return

      try {
        const videoUrl = URL.createObjectURL(file)
        emit('video-imported', videoUrl)
        
        // 创建视频元素
        const video = document.createElement('video')
        video.src = videoUrl
        
        // 等待视频元数据加载
        await new Promise((resolve) => {
          video.onloadedmetadata = () => resolve(null)
        })

        await new Promise((resolve) => {
          video.oncanplay = () => resolve(null)
          video.load()
        })

        const duration = video.duration
        state.videoDuration = duration // 更新视频总时长

        // 移除现有的视频轨道片段（只移除视频，不影响其他轨道）
        state.clips = state.clips.filter(clip => clip.type !== 'video')

        // 创建视频轨道片段
        const videoClip: Clip = {
          id: `video-${Date.now()}`,
          type: 'video',
          start_time: 0,
          duration: duration,
          head: 0,
          tail: 0,
          videoElement: video,
          thumbnails: {}, // 先创建空的缩略图对象
          videoWidth: video.videoWidth,
          videoHeight: video.videoHeight,
          isMainTrack: true
        }

        // 生成视频缩略图（异步进行，不阻塞主流程）
        const generateThumbnails = async () => {
          const thumbnails: VideoThumbnails = {}
          for (let time = 0; time < duration; time += 1) {
            thumbnails[time] = await generateThumbnail(video, time)
          }
          videoClip.thumbnails = thumbnails
          drawTimeline() // 重绘以显示新生成的缩略图
        }
        generateThumbnails()

        // 添加视频片段
        state.clips.push(videoClip)

        // 更新时间轴宽度
        state.contentWidth = Math.max(
          state.videoDuration * 100,
          state.viewportWidth / state.zoomLevel
        )

        drawTimeline()
      } catch (error) {
        console.error('视频导入失败:', error)
      }

      input.value = ''
    }

    // 修改 handleAudioImport 方法
    const handleAudioImport = async (event: Event) => {
      const input = event.target as HTMLInputElement
      const file = input.files?.[0]
      if (!file) return

      try {
        const audioUrl = URL.createObjectURL(file)
        const audio = new Audio(audioUrl)
        
        // 设置音频属性以减少延迟
        audio.preload = 'auto'
        audio.defaultPlaybackRate = 1.0
        
        await new Promise((resolve) => {
          audio.onloadedmetadata = () => resolve(null)
        })

        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        const arrayBuffer = await file.arrayBuffer()
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
        const channelData = audioBuffer.getChannelData(0)
        const sampleRate = 100
        const duration = audioBuffer.duration
        const waveform = generateWaveform(channelData, sampleRate * duration)

        // 创建新的音频片段，确保正确设置所有裁剪相关属性
        const newClip: Clip = {
          id: `audio-${Date.now()}`,
          type: 'audio',
          start_time: state.currentTime,
          duration: duration,
          head: 0, // 初始没有裁剪开头，单位毫秒
          tail: 0, // 初始没有裁剪结尾，单位毫秒
          sourceDuration: duration,
          waveform,
          audioElement: audio
        }

        // 检查是否会超出时间轴范围
        if (newClip.start_time + newClip.duration > state.videoDuration) {
          newClip.start_time = Math.max(0, state.videoDuration - newClip.duration)
        }

        state.clips.push(newClip)
        drawTimeline()
      } catch (error) {
        console.error('音频导入失败:', error)
      }

      input.value = ''
    }

    // 修改 generateWaveform 方法，优化波形数据生成
    const generateWaveform = (channelData: Float32Array, samples: number): number[] => {
      const blockSize = Math.floor(channelData.length / samples)
      const waveform: number[] = []

      for (let i = 0; i < samples; i++) {
        const start = i * blockSize
        const end = Math.min(start + blockSize, channelData.length)
        let sum = 0
        let max = 0

        // 计算这个区块的RMS（均方根）和最大值
        for (let j = start; j < end; j++) {
          const amplitude = Math.abs(channelData[j])
          sum += amplitude * amplitude
          max = Math.max(max, amplitude)
        }

        // 使用RMS和最大值的加权平均作为最终振幅
        const rms = Math.sqrt(sum / (end - start))
        waveform.push((rms * 0.7 + max * 0.3)) // 70% RMS + 30% 最大值
      }

      return waveform
    }

    // 修改字幕添加方法
    const addSubtitle = () => {
      const newClip: Clip = {
        id: `subtitle-${Date.now()}`,
        type: 'subtitle',
        start_time: state.currentTime,
        duration: 3, // 保持3秒，因为 duration 单位是秒
        head: 0, // 初始没有裁剪开头，单位毫秒
        tail: 0, // 初始没有裁剪结尾，单位毫秒
        content: '新建字幕',
        isMainTrack: false
      }

      state.clips.push(newClip)
      drawTimeline()
    }

    // 修改导出时间轴数据方法
    const exportTimeline = () => {
      const result = {
        // subtitles: state.clips
        //   .filter(clip => clip.type === 'subtitle')
        //   .map(clip => ({
        //     content: clip.content,
        //     start_time: clip.start_time * 1000, // 转换为毫秒
        //     duration: clip.duration * 1000,
        //     head: clip.head,
        //     tail: clip.tail
        //   })),
        audio: state.clips
          .filter(clip => clip.type === 'audio')
          .map(clip => ({
            start_time: parseInt((clip.start_time * 1000).toString()),
            duration: parseFloat(clip.duration.toFixed(3)),
            head: parseInt(clip.head.toString()),
            tail: parseInt(clip.tail.toString()),
            order: clip.order,
            speed: clip.speed
          }))
      }

      // 在控制台输出格式化的 JSON
      console.log('Timeline Export Result:')
      console.log(result)
      return result
    }

    const handleZoom = (e: WheelEvent | number) => {
      const oldZoom = state.zoomLevel
      
      // 计算最小缩放级别，使背景音频正好填满视口
      const minZoomLevel = state.viewportWidth / (state.videoDuration * 100)
      
      if (typeof e === 'number') {
        // 直接设置缩放级别时，确保不小于最小缩放级别
        state.zoomLevel = Math.max(minZoomLevel, e)
      } else {
        // 处理鼠标滚轮缩放
        e.preventDefault()
        const delta = e.deltaY > 0 ? 0.9 : 1.1
        state.zoomLevel = Math.min(
          state.maxZoom,
          Math.max(minZoomLevel, state.zoomLevel * delta)
        )
      }

      // 计算鼠标位置相对于内容的位置比例
      const mouseX = (state.mouseX + state.scrollLeft) / state.contentWidth
      
      // 更新内容总宽度，确保有足够的滚动空间，且不小于最小宽度
      const minWidth = state.videoDuration * 100 * minZoomLevel
      state.contentWidth = Math.max(
        state.videoDuration * 100 * state.zoomLevel,
        minWidth
      )
      
      // 调整滚动位置，保持鼠标指向的内容位置不变，并确保不超出范围
      state.scrollLeft = Math.max(
        0,
        Math.min(
          mouseX * state.contentWidth - state.mouseX,
          Math.max(0, state.contentWidth - state.viewportWidth)
        )
      )

      drawTimeline()
    }

    const handleScroll = (e: WheelEvent) => {
      e.preventDefault()
      
      // 如果按住 Shift 键或者有水平滚动，则进行水平滚动
      if (e.shiftKey || e.deltaX !== 0) {
        // 优先使用水平滚动值，如果没有则使用垂直滚动值
        const scrollDelta = e.deltaX || e.deltaY
        const newScrollLeft = state.scrollLeft + scrollDelta
        
        
        state.scrollLeft = Math.max(
          0,
          Math.min(
            newScrollLeft,
            state.contentWidth - state.viewportWidth
          )
        )
        
      }
      // 否则进行缩放
      else if (e.deltaY !== 0) {
        handleZoom(e)
      }
      
      drawTimeline()
    }

    // 修改 handleMouseMove 方法，同时支持拖动和裁剪
    const handleMouseMove = (e: MouseEvent) => {
      if (!timelineCanvas.value) return
      
      const rect = timelineCanvas.value.getBoundingClientRect()
      // 修正鼠标位置计算，考虑滚动偏移
      state.mouseX = e.clientX - rect.left + state.scrollLeft
      
      // 如果正在操作字幕片段且字幕编辑被禁用，则不处理
      if (state.selectedClip?.type === 'subtitle' && !state.subtitleEditable) {
        if (state.isDragging || state.isResizing) {
          state.isDragging = false
          state.isResizing = false
        }
        return
      }

      // 处理片段裁剪
      if (state.isResizing && state.selectedClip) {
        togglePlayState(false)
        const pixelsPerSecond = 100 * state.zoomLevel
        const deltaX = state.mouseX - state.dragStartX
        const deltaTime = deltaX / pixelsPerSecond
        const deltaTimeMs = Math.round(deltaTime * 1000) // 转换为毫秒

        // 只处理 AI 配音片段的裁剪
        if (state.selectedClip.type === 'audio') {
          // 获取源音频时长（毫秒）
          const sourceDurationMs = Math.round(state.selectedClip.sourceDuration! * 1000)
          const speed = state.selectedClip.speed || 1
          
          if (state.resizeDirection === 'left') {
            // 计算新的头部裁剪值，确保不会小于0
            const speed = state.selectedClip.speed || 1
            const deltaTimeMs = Math.round(deltaTime * 1000)
            
            // 计算新的开始时间和持续时间
            const newStartTime = Math.max(0, state.dragStartClipTime + deltaTime)
            const newDuration = state.dragStartDuration - deltaTime
            
            // 如果新的开始时间为0（已经到达左边界），则不应该继续移动
            if (newStartTime === 0) {
              // 计算从拖动开始到现在被限制的时间差
              const restrictedDeltaTime = -state.dragStartClipTime
              
              // 使用被限制的时间差来计算新的头部裁剪值
              const newHead = state.dragStartTrimStart + (restrictedDeltaTime * speed * 1000)
              
              // 更新片段属性
              if (newHead >= 0) {
                state.selectedClip.start_time = 0
                state.selectedClip.head = newHead
                state.selectedClip.duration = state.dragStartDuration + state.dragStartClipTime
              }
            } else {
              // 正常情况下的裁剪逻辑
              const newHead = state.dragStartTrimStart + (deltaTimeMs * speed)
              
              // 确保新的头部裁剪值在有效范围内
              if (newHead >= 0 && newDuration >= 0.1) {
                state.selectedClip.head = newHead
                state.selectedClip.start_time = newStartTime
                state.selectedClip.duration = newDuration
              }
            }
          } else {
            console.log('右侧')
            // 右侧裁剪
            const sourceDurationMs = Math.round(state.selectedClip.sourceDuration! * 1000)
            const speed = state.selectedClip.speed || 1
            const newDuration = Math.max(0.1, state.dragStartDuration + deltaTime)
            
            // 基于初始 tail 值计算新的 tail 值
            const newTail = state.dragStartTail + ((state.dragStartDuration - newDuration) * speed * 1000)
            
            // 确保新的 tail 值不会小于0且不会超过源时长
            if (newTail >= 0 && newTail <= sourceDurationMs - state.selectedClip.head) {
              state.selectedClip.duration = newDuration
              state.selectedClip.tail = newTail
            }
          }
        } else {
          // 非 AI 配音片段的处理保持不变
          if (state.resizeDirection === 'left') {
            const newStartTime = Math.max(0, state.dragStartClipTime + deltaTime)
            const newDurationMs = Math.round(state.dragStartDuration * 1000 - deltaTimeMs)
            
            if (newDurationMs >= 100) {
              state.selectedClip.start_time = newStartTime
              state.selectedClip.duration = state.dragStartDuration - deltaTime
            }
          } else {
            const newDuration = Math.max(0.1, state.dragStartDuration + deltaTime)
            const newDurationMs = Math.round(newDuration * 1000)
            
            if (newDurationMs >= 100 && state.selectedClip.start_time + newDuration <= state.videoDuration) {
              state.selectedClip.duration = newDuration
            }
          }
        }
        
        drawTimeline()
        return // 添加return语句，防止执行后续的拖动逻辑
      }
      
      // 处理片段拖动
      if (state.isDragging && state.selectedClip) {
        togglePlayState(false)
        e.preventDefault()
        
        const pixelsPerSecond = 100 * state.zoomLevel
        const deltaX = state.mouseX - state.dragStartX
        const deltaTime = deltaX / pixelsPerSecond
        
        // 计算新位置
        const newStartTime = Math.max(0, state.dragStartClipTime + deltaTime)
        
        // 确保不会超出时间轴范围
        if (newStartTime + state.selectedClip.duration <= state.videoDuration) {
          state.selectedClip.start_time = newStartTime
          drawTimeline() // 实时重绘拖动效果
        }
      }
      
      updateCursor(state.mouseX - state.scrollLeft)
      drawTimeline()
    }

    // 修改 handleMouseDown 方法，确保正确初始化拖动和裁剪状态
    const handleMouseDown = (e: MouseEvent) => {
      if (!timelineCanvas.value) return
      
      const rect = timelineCanvas.value.getBoundingClientRect()
      const mouseX = e.clientX - rect.left + state.scrollLeft
      const mouseY = e.clientY - rect.top
      
      // 查找点击的片段
      const clickedClip = findClipAtPosition(mouseX, mouseY)
      
      togglePlayState(false)
      if (clickedClip) {
        console.log('clickedClip', clickedClip)
        emit('voice-selected', clickedClip)
        state.selectedClip = clickedClip
        
        // 计算片段的起始和结束位置（像素）
        const pixelsPerSecond = 100 * state.zoomLevel
        const clipStartX = clickedClip.start_time * pixelsPerSecond
        let clipWidth
        
        // 计算实际显示宽度，考虑裁剪和速度因素
        if (clickedClip.type === 'audio' && clickedClip.speed) {
          // 使用实际持续时间计算宽度（考虑裁剪和速度）
          const actualDuration = (clickedClip.sourceDuration! - 
            (clickedClip.head / 1000 / clickedClip.speed) - 
            (clickedClip.tail / 1000 / clickedClip.speed)) / 
            clickedClip.speed
          clipWidth = actualDuration * pixelsPerSecond
        } else {
          // 其他类型片段的宽度计算
          const originalDuration = clickedClip.sourceDuration || clickedClip.duration
          const trimmedDuration = originalDuration - 
            ((clickedClip.head || 0) / 1000) - 
            ((clickedClip.tail || 0) / 1000)
          clipWidth = trimmedDuration * pixelsPerSecond
        }
        
        const clipEndX = clipStartX + clipWidth
        
        // 重要：先保存初始值，不管是左边还是右边拖动都需要这些值
        state.dragStartX = mouseX
        state.dragStartClipTime = clickedClip.start_time
        state.dragStartDuration = clickedClip.duration
        state.dragStartTrimStart = clickedClip.head || 0
        state.dragStartTail = clickedClip.tail || 0  // 添加这一行
        
        // 检查是否点击在片段边缘（用于调整大小）
        const resizeHandleWidth = 10 // 调整手柄的宽度（像素）
        
        // 检查是否在左右边界附近
        const isNearLeftEdge = Math.abs(mouseX - clipStartX) <= resizeHandleWidth
        const isNearRightEdge = Math.abs(mouseX - clipEndX) <= resizeHandleWidth
        
        if (isNearLeftEdge) {
          // 点击在左边缘
          state.isResizing = true
          state.resizeDirection = 'left'
        } else if (isNearRightEdge) {
          // 点击在右边缘
          state.isResizing = true
          state.resizeDirection = 'right'
        } else {
          // 点击在片段中间，开始拖拽
          state.isDragging = true
        }
        
        console.log('Mouse down state:', {
          dragStartX: state.dragStartX,
          dragStartClipTime: state.dragStartClipTime,
          dragStartDuration: state.dragStartDuration,
          dragStartTrimStart: state.dragStartTrimStart,
          dragStartTail: state.dragStartTail,  // 添加这一行
          isResizing: state.isResizing,
          resizeDirection: state.resizeDirection,
          isDragging: state.isDragging
        })
      } else {
        state.selectedClip = null
      }
      
      drawTimeline()
    }

    const handleMouseUp = () => {
      if (!timelineCanvas.value) return
      
      if (state.isDragging && state.selectedClip) {
        const pixelsPerSecond = 100 * state.zoomLevel
        const deltaX = state.mouseX - state.dragStartX
        const deltaTime = deltaX / pixelsPerSecond
        
        // 计算最终位置
        const newStartTime = Math.max(0, state.dragStartClipTime + deltaTime)
        
        // 确保不会超出时间轴范围
        if (newStartTime + state.selectedClip.duration <= state.videoDuration) {
          state.selectedClip.start_time = newStartTime
        }
      }
      
      if (state.isResizing && state.selectedClip) {
        // 调整片段时长，确保不小于最小时长
        const minDuration = 0.1 // 最小持续时间（秒）
        state.selectedClip.duration = Math.max(minDuration, state.selectedClip.duration)
        
        // 确保不超出时间轴范围
        if (state.selectedClip.start_time + state.selectedClip.duration > state.videoDuration) {
          if (state.resizeDirection === 'right') {
            state.selectedClip.duration = state.videoDuration - state.selectedClip.start_time
          } else {
            state.selectedClip.start_time = state.videoDuration - state.selectedClip.duration
          }
        }
      }
      
      // 重置所有拖拽相关状态
      state.isDragging = false
      state.isResizing = false
      state.resizeDirection = ''
      state.dragStartX = 0
      state.dragStartScrollLeft = 0
      state.dragStartClipTime = 0
      state.dragStartEndTime = 0
      state.resizeStartWidth = 0
      
      drawTimeline()
    }

    // 辅助函数：根据位置查找片段
    const findClipAtPosition = (x: number, y: number): Clip | null => {
      const pixelsPerSecond = 100 * state.zoomLevel
      const startY = SCALE_HEIGHT
      
      for (let i = state.clips.length - 1; i >= 0; i--) {
        const clip = state.clips[i]
        const clipX = clip.start_time * pixelsPerSecond
        const clipWidth = clip.duration * pixelsPerSecond
        const trackY = getTrackY(clip)
        
        if (x >= clipX && 
            x <= clipX + clipWidth && 
            y >= trackY + TRACK_PADDING && 
            y <= trackY + TRACK_HEIGHT - TRACK_PADDING) {
          return clip
        }
      }
      
      return null
    }

    // 更新鼠标样式
    const updateCursor = (mouseX: number) => {
      if (!timelineCanvas.value) return
      
      const pixelsPerSecond = 100 * state.zoomLevel
      let cursor = 'default'
      
      if (state.selectedClip) {
        // 如果是主轨道，保持默认光标
        if (state.selectedClip.isMainTrack) {
          cursor = 'default'
        } else {
          const clipX = state.selectedClip.start_time * pixelsPerSecond - state.scrollLeft
          let clipWidth
          
          // 计算实际显示宽度，考虑裁剪和速度因素
          if (state.selectedClip.type === 'audio' && state.selectedClip.speed) {
            // 使用实际持续时间计算宽度（考虑裁剪和速度）
            const actualDuration = (state.selectedClip.sourceDuration! - 
              (state.selectedClip.head / 1000 / state.selectedClip.speed) - 
              (state.selectedClip.tail / 1000 / state.selectedClip.speed)) / 
              state.selectedClip.speed
            clipWidth = actualDuration * pixelsPerSecond
          } else {
            // 其他类型片段的宽度计算
            const originalDuration = state.selectedClip.sourceDuration || state.selectedClip.duration
            const trimmedDuration = originalDuration - 
              ((state.selectedClip.head || 0) / 1000) - 
              ((state.selectedClip.tail || 0) / 1000)
            clipWidth = trimmedDuration * pixelsPerSecond
          }
          
          // 检查鼠标是否在左右边界附近
          const resizeHandleWidth = 10
          const isNearLeftEdge = Math.abs(mouseX - clipX) <= resizeHandleWidth
          const isNearRightEdge = Math.abs(mouseX - (clipX + clipWidth)) <= resizeHandleWidth
          
          if (isNearLeftEdge || isNearRightEdge) {
            cursor = 'ew-resize'
          } else if (state.isDragging) {
            cursor = 'grabbing'
          } else if (mouseX >= clipX && mouseX <= clipX + clipWidth) {
            cursor = 'grab'
          }
        }
      }
      
      timelineCanvas.value.style.cursor = cursor
    }

    // 修改 handleTimelineClick 方法
    const handleTimelineClick = (e: MouseEvent) => {
      const rect = timelineCanvas.value?.getBoundingClientRect()
      if (!rect) return
      
      const mouseX = e.clientX - rect.left
      const mouseY = e.clientY - rect.top
      
      // 检查是否点击到了片段
      const clickedClip = findClipAtPosition(mouseX + state.scrollLeft, mouseY)
      
      // 如果点击到片段，不处理播放位置
      if (clickedClip) return
      
      // 检查是否在刻度线区域或轨道空白处点击
      if (mouseY >= 0) {
        const pixelsPerSecond = 100 * state.zoomLevel
        const clickTime = (mouseX + state.scrollLeft) / pixelsPerSecond
        
        state.currentTime = Math.max(0, Math.min(clickTime, state.videoDuration))
        emit('time-change', state.currentTime) // 发送时间变化事件
        
        // 如果正在播放，同步所有媒体元素
        if (state.isPlaying) {
          state.clips.forEach(clip => {
            if (clip.type === 'video' && clip.videoElement) {
              const clipTime = state.currentTime - clip.start_time
              if (clipTime >= 0 && clipTime < clip.duration) {
                clip.videoElement.currentTime = clipTime
              }
            }
          })
        }
        
        drawTimeline()
      }
    }

    // 添加键盘事件处理
    const handleKeydown = (e: KeyboardEvent) => {
      // 如果正在输入（比如在输入框中），不处理空格键
      if (e.target instanceof HTMLInputElement || 
          e.target instanceof HTMLTextAreaElement ||
          e.target instanceof HTMLButtonElement) {
        return
      }
      
      // 空格键控制播放/暂停
      if (e.code === 'Space') {
        e.preventDefault() // 防止页面滚动
        togglePlay()
      }
    }

    // 添加双击事件处理
    const handleDoubleClick = (e: MouseEvent) => {
      const rect = timelineCanvas.value?.getBoundingClientRect()
      if (!rect) return

      const mouseX = e.clientX - rect.left
      const mouseY = e.clientY - rect.top
      
      // 检查是否双击到了字幕片段
      const clickedClip = findClipAtPosition(mouseX + state.scrollLeft, mouseY)
      
      if (clickedClip?.type === 'subtitle') {
        state.editingClip = clickedClip
        state.editingContent = clickedClip.content || ''
        
        // 创建并显示输入框
        const input = document.createElement('input')
        input.type = 'text'
        input.value = state.editingContent
        input.style.position = 'absolute'
        input.style.left = `${mouseX}px`
        input.style.top = `${mouseY}px`
        input.style.width = '200px'
        input.style.zIndex = '1000'
        
        // 添加样式
        input.style.padding = '4px'
        input.style.border = '1px solid #4a9eff'
        input.style.borderRadius = '4px'
        input.style.backgroundColor = '#fff'
        input.style.color = '#000'
        
        // 处理输入完成
        const handleInputComplete = () => {
          if (state.editingClip) {
            state.editingClip.content = input.value
          }
          input.remove()
          state.editingClip = null
          state.editingContent = ''
          drawTimeline()
        }
        
        // 添加事件监听
        input.addEventListener('blur', handleInputComplete)
        input.addEventListener('keydown', (e) => {
          if (e.key === 'Enter') {
            handleInputComplete()
          } else if (e.key === 'Escape') {
            input.remove()
            state.editingClip = null
            state.editingContent = ''
            drawTimeline()
          }
        })
        
        // 将输入框添加到容器中
        timelineCanvas.value?.parentElement?.appendChild(input)
        input.focus()
        input.select()
      }
    }

    // 添加更新当前时间的方法
    const updateCurrentTime = (time: number) => {
      if (state.currentTime !== time) {
        state.currentTime = time
        // 只在非播放状态下进行完整重绘
        if (!state.isPlaying) {
          drawTimeline()
        } else {
          drawPlayheadOnly()
        }
      }
    }

    // 添加设置播放状态的方法
    const setPlayState = (isPlaying: boolean) => {
      state.isPlaying = isPlaying
      if (isPlaying) {
        state.lastFrameTime = performance.now()
        requestAnimationFrame(updatePlayback)
      }
    }

    // 修改 getTrackY 方法
    const getTrackY = (clip: Clip): number => {
      let y = SCALE_HEIGHT
      const tracks: Array<keyof typeof trackVisibility> = ['audio', 'subtitle', 'main-audio', 'video']
      
      for (let i = 0; i < tracks.length; i++) {
        const track = tracks[i]
        if (track === clip.type) {
          return y
        }
        if (trackVisibility[track]) {
          y += TRACK_HEIGHT
        }
      }
      return y
    }

    // 添加处理轨道显示变化的方法
    const handleTrackVisibilityChange = () => {
      // 重新计算容器高度
      const visibleTracks = Object.values(trackVisibility).filter(v => v).length
      const containerHeight = SCALE_HEIGHT + visibleTracks * TRACK_HEIGHT
      
      if (container.value) {
        container.value.style.height = `${containerHeight}px`
      }
      
      // 重新初始化画布并重绘
      initCanvas()
      drawTimeline()
    }

    // 添加从 URL 加载音频并生成波形的方法
    const loadAudioFromUrl = async (url: string): Promise<{ 
      audioElement: HTMLAudioElement,
      waveform: number[],
      duration: number
    }> => {
      // 创建音频元素
      const audio = new Audio()
      audio.crossOrigin = 'anonymous' // 允许跨域加载
      audio.preload = 'auto' // 设置预加载
      audio.src = url
      
      // 等待音频完全加载
      await new Promise((resolve, reject) => {
        const loadHandler = () => {
          audio.removeEventListener('canplaythrough', loadHandler)
          audio.removeEventListener('error', errorHandler)
          resolve(null)
        }
        
        const errorHandler = (e: ErrorEvent) => {
          audio.removeEventListener('canplaythrough', loadHandler)
          audio.removeEventListener('error', errorHandler)
          reject(new Error(`Failed to load audio: ${e.message}`))
        }
        
        audio.addEventListener('canplaythrough', loadHandler)
        audio.addEventListener('error', errorHandler)
        
        // 强制开始加载
        audio.load()
      })
      
      // 获取音频数据
      const response = await fetch(url)
      const arrayBuffer = await response.arrayBuffer()
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
      
      // 生成波形数据
      const waveform = generateWaveformData(audioBuffer)
      
      return {
        audioElement: audio,
        waveform,
        duration: audioBuffer.duration
      }
    }

    // 修改导入数据的方法
    const importTimelineData = async (data: ImportData) => {
      try {
        console.log('importTimelineData----')
        loading.value = true
        
        // 计算需要加载的资源数量
        totalResources.value = data.clips.length
        loadedResources.value = 0
        
        // 清空现有片段
        state.clips = []
        console.log('data.clips',data.clips)

        // 在所有片段导入完成后，再导入背景音频
        if (props.totalData?.bg_path) {
          await handleBackgroundAudioImport(props.totalData.bg_path)
        }
        // 导入新片段
        for (const clipData of data.clips) {
          const newClip: Clip = {
            ...clipData,
            id: clipData.id,
            type: clipData.type,
            start_time: clipData.start_time,
            duration: clipData.duration,
            sourceDuration: clipData.sourceDuration,
            head: clipData.head,
            tail: clipData.tail,
            content: clipData.content,
            isMainTrack: clipData.isMainTrack,
            speed: clipData.speed || 1, // 添加速度属性，默认为1
          }
          const newClip2: Clip = {
            ...clipData,
            id: clipData.id,
            type: 'subtitle',
            start_time: clipData.start_time,
            duration: clipData.duration,
            sourceDuration: clipData.sourceDuration,
            head: clipData.head,
            tail: clipData.tail,
            content: clipData.tts_text,
            isMainTrack: clipData.isMainTrack
          }

          // 如果是音频片段，加载音频并生成波形
          if ((clipData.type === 'audio' || clipData.type === 'main-audio') && clipData.voice_path) {
            try {
              const { audioElement, waveform, duration } = await loadAudioFromUrl(clipData.voice_path)
              newClip.audioElement = audioElement
              newClip.waveform = waveform
              newClip.sourceDuration = duration
              loadedResources.value++
            } catch (error) {
              loadedResources.value++
              console.log('error',`音频 #${clipData.order} ${clipData.voice_path} 加载失败`)
              ElMessage.error(`音频 #${clipData.order} ${clipData.voice_path} 加载失败`)
            }
          }
          
          state.clips.push(newClip)
          state.clips.push(newClip2)
        }
        
        drawTimeline()
      } catch (error) {
        console.log(error)
        ElMessage.error('导入数据失败')
      } finally {
        loading.value = false
      }
    }

    // 添加文件导入处理函数
    const handleDataImport = async (e: Event) => {
      const input = e.target as HTMLInputElement
      if (!input.files?.length) return
      
      const file = input.files[0]
      console.log('Importing data file:', file.name)
      
      try {
        const text = await file.text()
        const data = JSON.parse(text) as ImportData
        await importTimelineData(data)
      } catch (error) {
        console.error('Error importing data file:', error)
      }
      
      input.value = ''
    }

    const importData = () => {
      ElMessageBox.prompt('请输入时间轴数据 (JSON格式)', '导入数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: `示例格式：
{
  "clips": [
    {
      "id": "audio-1",
      "type": "audio",
      "start_time": 0,
      "duration": 5,
      "head": 0,
      "tail": 0,
      "url": "音频文件URL"
    }
  ]
}`,
        customClass: 'json-import-dialog'
      }).then(async ({ value }) => {
        try {
          const data = JSON.parse(value) as ImportData
          await importTimelineData(data)
          // ElMessage.success('数据导入成功')
        } catch (error) {
          console.error('Error parsing JSON:', error)
          ElMessage.error('JSON格式错误，请检查输入')
        }
      }).catch(() => {
        // 用户取消输入
      })
    }

    const handleInitVoicesData = () => {
      state.isPlaying = false
      emit('play-state-change', state.isPlaying)
      console.log(props.voicesList)
      importTimelineData({clips: props.voicesList})
    }

    const handleStartChange = (data: any) => {
      console.log('handleStartChange', data)
      state.clips.forEach((clip: Clip) => {
        // 同时更新音频片段和对应的字幕片段
        if ((clip.type === 'audio') && clip.order === data.order) {
          clip.start_time = Number(data.start_time)
          
          // 如果是音频片段，还需要保持其他属性不变
          if (clip.type === 'audio') {
            // 确保裁剪值和持续时间保持不变
            const originalDuration = clip.duration
            const originalHead = clip.head
            const originalTail = clip.tail
            
            clip.duration = originalDuration
            clip.head = originalHead
            clip.tail = originalTail
          }
        }
      })
      drawTimeline()
    }

    //设置倍速
    const setClipSpeed = (order: number, speed: number) => {
      // 限制速度范围在 0.5 到 2.0 之间
      const clampedSpeed = Math.max(0.5, Math.min(2.0, speed))
      
      // 查找对应的音频片段
      const audioClip = state.clips.find(clip => 
        clip.type === 'audio' && clip.order === order
      )
      
      if (!audioClip) {
        console.warn(`未找到序号为 ${order} 的AI配音片段`)
        return
      }
      console.log('audioClip',audioClip)
      if(audioClip.speed === clampedSpeed) return
      // 保存原始时长

      // audioClip.head = audioClip.head / clampedSpeed
      // audioClip.tail = audioClip.tail / clampedSpeed
      const originalDuration = audioClip.sourceDuration! - (audioClip.head / 1000) - (audioClip.tail / 1000)
      
      // 更新速度和时长
      audioClip.speed = clampedSpeed
      audioClip.duration = originalDuration / clampedSpeed

      
      // 如果正在播放且是当前片段，更新播放速度
      if (state.isPlaying && audioClip.audioElement) {
        const clipTime = state.currentTime - audioClip.start_time
        if (clipTime >= 0 && clipTime < audioClip.duration) {
          audioClip.audioElement.playbackRate = clampedSpeed
        }
      }
      
      // 重绘时间轴
      drawTimeline()
    }

    onMounted(() => {
      handleInitVoicesData()
      initCanvas()
      drawTimeline()
      
      window.addEventListener('resize', initCanvas)
      window.addEventListener('keydown', handleKeydown)
      
      return () => {
        window.removeEventListener('resize', initCanvas)
        window.removeEventListener('keydown', handleKeydown)
      }
    })

    return {
      handleStartChange,
      container,
      timelineCanvas,
      importVideo,
      importAudio,
      importBackgroundAudio,
      onPrevEpisode,
      onNextEpisode,
      addSubtitle,
      exportTimeline,
      handleZoom,
      handleScroll,
      togglePlay,
      handleMouseMove,
      handleMouseDown,
      handleMouseUp,
      ...state,
      state,
      videoInput,
      audioInput,
      backgroundAudioInput,
      handleVideoImport,
      handleAudioImport,
      handleBackgroundAudioImport,
      handleTimelineClick,
      handleDoubleClick,
      updateCurrentTime,
      setPlayState,
      handleTrackVisibilityChange,
      trackVisibility,
      handleDataImport,
      loading,
      loadedResources,
      totalResources,
      importData,
      subtitleEditable,
      handleInitVoicesData,
      togglePlayState,
      setClipSpeed,
      handleSplit,
      handleDelete,
      deleteClip,
      appendClip,
      resetClip
    }
  }
})
</script>

<style scoped>
.timeline-editor-wrapper {
  width: 100%;
  height: 100%;
}

.timeline-editor {
  width: 100%;
  height: 100%;
}

.toolbar {
  padding: 10px;
  background: #f5f5f5;
  display: flex;
  gap: 10px;
  align-items: center;
}

.timeline-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.track-headers {
  width: 100px;
  text-align: center;
  background: #333;
  color: white;
}

.track-header {
  height: 60px; /* 匹配新的轨道高度 */
  padding: 4px 10px;
  text-align: center;
  border-bottom: 1px solid #444;
  display: flex;
  align-items: center;
  justify-content: center;
}

.canvas-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.json-import-dialog .el-message-box__input textarea {
  font-family: monospace;
  min-height: 200px;
  white-space: pre;
}
</style>
