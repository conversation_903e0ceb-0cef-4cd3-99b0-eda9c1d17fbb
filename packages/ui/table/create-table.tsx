import { ClassName, createComponent, mc, tryCall } from '@skynet/shared'
import { difference, isEqual } from 'lodash-es'
import { computed, ref, VNodeChild } from 'vue'
import { Button, Checkbox, MergeClass, Tooltip } from '../ui'
import { useTableStore } from './use-table-store'
export type TableColumn<D> = [
  name: symbol | string | VNodeChild | ((column: TableColumn<D>, data: D[]) => VNodeChild),
  render: keyof D | ((row: D, rowIndex: number, data: D[]) => VNodeChild),
  {
    class?: ClassName | ((row: D | null, rowIndex: number, data: null | D[]) => ClassName)
    contentClass?: ClassName
    tipVisible?: boolean
    width?: 'max-content' | 'min-content' | 'auto' | 'minmax(min, max)' | 'fit-content(?)' | '1fr' | '100px' | '50%' | string
  },
] | [
  name: symbol | string | VNodeChild | ((column: TableColumn<D>, data: D[]) => VNodeChild),
  render: keyof D | ((row: D, rowIndex: number, data: D[]) => VNodeChild),
] | null
type TableOptions<D> = {
  props: {
    store?: ReturnType<typeof useTableStore> | null
    list: D[] | null
    columns: Array<TableColumn<D>>
    loading?: boolean
    actionsClass?: ClassName
    cellClass?: ClassName
    headCellClass?: ClassName
    columnHoverClass?: ClassName
    multiSelect?: boolean
    actions?: (() => VNodeChild) | null
  }
}
const resolve = <D extends { id?: string | number }>(maybeString: symbol | string | VNodeChild | ((column: TableColumn<D>, data: D[]) => VNodeChild), column: TableColumn<D>, data: D[]) => {
  return typeof maybeString === 'function' ? maybeString(column, data) : maybeString
}
export const expanded = Symbol('expanded')
export const CreateTable = <D extends object & { id?: string | number }>() => createComponent<TableOptions<D>>({
  props: {
    store: null,
    list: null,
    columns: [],
    loading: false,
    actionsClass: '',
    cellClass: '',
    headCellClass: '',
    multiSelect: false,
    columnHoverClass: '',
    actions: null,
  },
}, props => {
  const { selectedRowIds, mode, expandedRowIds } = props.store ?? useTableStore()
  const handleSelectAll = (value: boolean) => {
    if (!props.list) return
    selectedRowIds.value = value ? [...listIds.value] : []
  }
  const handleSelectionChange = (row: D) => {
    if (!row.id) return
    if (selectedRowIds.value.includes(row.id)) {
      const index = selectedRowIds.value.indexOf(row.id)
      selectedRowIds.value.splice(index, 1)
    } else {
      selectedRowIds.value.push(row.id)
    }
  }
  const columns = computed(() => props.columns.filter(col => col !== null))
  const listIds = computed(() => props.list?.map(item => item.id).filter(id => id !== undefined) ?? [])
  const cellContentClass = 'inline-block max-w-full truncate'
  const finalCellClass = computed(() => mc('flex items-center px-3 py-2 max-w-full text-[#606266] text-[14px] leading-[22px] [&.odd]:bg-[#FAFAFA]', props.cellClass))
  const finalHeadCellClass = computed(() => mc(finalCellClass.value, 'whitespace-nowrap text-[#909399] bg-[#EBEEF5]', props.headCellClass))
  const normalColumns = computed(() => columns.value.filter(col => col[0] !== expanded))
  const expandedColumns = computed(() => columns.value.filter(col => col[0] === expanded))
  const hoveredIndex = ref(-1)
  return () => (
    <MergeClass baseClass="block overflow-x-auto w-full break-all relative space-y-4">
      {(props.actions || props.multiSelect) && (
        <MergeClass tag="x-table-actions" baseClass="flex mt-4 px-4 gap-4 items-center" class={props.actionsClass}>
          {props.multiSelect && (
            mode.value === 'normal'
              ? (
                  <Button class="h-8 text-sm font-bold btn btn-sm" onClick={() => mode.value = 'select'}> 多选 </Button>
                )
              : (
                  <Button class="h-8 text-sm font-bold btn btn-sm" onClick={() => mode.value = 'normal'}> 取消多选 </Button>
                )
          )}
          {props.actions?.()}
        </MergeClass>
      )}
      <x-table-wrapper class="block relative min-h-[20px]">
        {
          props.list?.[0]
            ? (
                <x-table class="relative grid w-full overflow-x-auto" style={{
                  gridTemplateColumns: [mode.value === 'select' ? 'max-content' : '', ...normalColumns.value.map(column => column[2]?.width ?? 'max-content')].join(' '),
                }}
                >
                  {mode.value === 'select' && (
                    <x-cell role="head" class={mc(finalHeadCellClass.value, 'px-2 w-8')}>
                      <Checkbox
                        modelValue={isEqual(selectedRowIds.value, listIds.value)}
                        indeterminate={selectedRowIds.value.length > 0 && difference(listIds.value, selectedRowIds.value).length > 0}
                        onUpdate:modelValue={handleSelectAll}
                      />
                    </x-cell>
                  )}
                  {/* 表头 */}
                  {normalColumns.value.map((column, index) => (
                    <x-cell
                      onMouseenter={() => onMouseEnter(column, index)}
                      onMouseleave={() => onMouseLeave(column, index)}
                      role="head" class={mc(
                        'flex items-center justify-center', tryCall(column[2]?.class), finalHeadCellClass.value,
                        hoveredIndex.value === index && props.columnHoverClass,
                      )}
                    >
                      {resolve(column[0], column, props.list!)}
                    </x-cell>
                  ))}
                  {/* 表体 */}
                  {props.list?.map((row, rowIndex) => (
                    <>
                      {/* 勾选 */}
                      {row.id && mode.value === 'select' && (
                        <x-cell class={mc(finalCellClass.value, 'w-8 px-2 whitespace-nowrap text-sm', rowIndex % 2 === 0 ? 'even' : 'odd')}>
                          <Checkbox
                            modelValue={selectedRowIds.value.includes(row.id)}
                            onUpdate:modelValue={() => handleSelectionChange(row)}
                          />
                        </x-cell>
                      )}
                      {/* 所有列 */}
                      {normalColumns.value.map((column, index) => (
                        <x-cell
                          onMouseenter={() => onMouseEnter(column, index)}
                          onMouseleave={() => onMouseLeave(column, index)}
                          class={mc(finalCellClass.value, tryCall(column[2]?.class), rowIndex % 2 === 0 ? 'even' : 'odd',
                            hoveredIndex.value === index && props.columnHoverClass,
                          )}
                        >
                          {column[2]?.tipVisible
                            ? (
                                // @ts-expect-error never mind
                                <Tooltip popContent={() => typeof column[1] === 'function' ? column[1](row, rowIndex, props.list ?? []) : row[column[1]]}>
                                  <x-cell-content class={mc(cellContentClass, column[2]?.contentClass ?? '')}>
                                    {typeof column[1] === 'function' ? column[1](row, rowIndex, props.list ?? []) : row[column[1]]}
                                  </x-cell-content>
                                </Tooltip>
                              )
                            : (
                                <x-cell-content class={mc(cellContentClass, column[2]?.contentClass ?? '')}>
                                  {typeof column[1] === 'function' ? column[1](row, rowIndex, props.list ?? []) : row[column[1]]}
                                </x-cell-content>
                              )}
                        </x-cell>
                      ))}
                      {row.id && expandedRowIds.value?.includes(row.id) && (
                        <>{
                          mode.value === 'select' && <x-cell class="col-span-1" />
                        }{
                          expandedColumns.value.map(column => (
                            <x-cell class={mc(finalCellClass.value, tryCall(column[2]?.class), rowIndex % 2 === 0 ? 'even' : 'odd')}>
                              {typeof column[1] === 'function' ? column[1](row, rowIndex, props.list ?? []) : row[column[1]]}
                            </x-cell>
                          ))
                        }
                        </>
                      )}
                    </>
                  ))}
                </x-table>
              )
            : !props.loading && (
                <div class="py-8 text-center">数据为空</div>
              )
        }
        {props.loading
          ? (
              <div class="absolute left-0 top-0 flex size-full items-center justify-center rounded-lg border border-gray-100 bg-white/50 py-8 text-center">
                <div class="size-6 animate-spin rounded-full border-2 border-[#464b65] border-t-white" />
              </div>
            )
          : null}
      </x-table-wrapper>
    </MergeClass>

  )
  function onMouseEnter(column: TableColumn<D>, index: number) {
    hoveredIndex.value = index
  }
  function onMouseLeave(column: TableColumn<D>, index: number) {
    hoveredIndex.value = -1
  }
})
