// import { h5HttpClient } from 'src/lib/http-client'
import type { WalletDataInfo } from './profile'
import { encryptedHttpClient } from '~/lib/encrypted-http-client'

export const apiHistoryList = () => {
  return encryptedHttpClient.get<ApiResponse<Api.MyList.Response>>(`h5-api/drama/view_history`)
}

export const apiWalletMy = () => {
  return encryptedHttpClient.get<ApiResponse<WalletDataInfo>>(`h5-api/wallet/my`)
}
