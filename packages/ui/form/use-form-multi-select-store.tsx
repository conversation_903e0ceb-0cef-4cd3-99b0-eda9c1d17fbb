import { createCachedFn, Key } from '@skynet/shared'
import { shallowRef } from 'vue'

export const useFormMultiSelectStore = createCachedFn((_key: Key) => {
  const icons = shallowRef({
    removeItem: '❎',
    clear: '✖',
    up: '▲', down: '▼',
    unchecked: '○', checked: '●',
    // <span class="size-4 inline-flex items-center justify-center shrink-0 grow-0">{selected ? '☑️' : '□️'}</span>
  })

  return {
    icons,
  }
})
