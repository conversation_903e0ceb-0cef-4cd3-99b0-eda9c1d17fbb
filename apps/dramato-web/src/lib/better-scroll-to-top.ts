export const betterScrollToTop = () => {
  // 每隔100ms检测页面是否滚动到顶部，如果是则停止检测，如果不是那么scrollTo({top: 0, behavior: 'smooth'})
  // 如果用户有行为，则停止检测
  // 如果超过1s，也停止检测
  const startTime = Date.now()
  let userActed = false
  const timer = setInterval(() => {
    if (Date.now() - startTime > 3000 || userActed) {
      clearInterval(timer)
    } else {
      run()
    }
  }, 100)

  const run = () => {
    if (document.documentElement.scrollTop !== 0) {
      // IOS 设置为0 并没有到顶部，设置一个负值
      window.scrollTo({ top: -1 })
    }
  }

  window.addEventListener('touchmove', () => {
    userActed = true
  })

  window.addEventListener('wheel', () => {
    userActed = true
  })
}
