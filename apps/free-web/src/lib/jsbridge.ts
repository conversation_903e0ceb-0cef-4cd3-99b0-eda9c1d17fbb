import { createNumberId } from '@skynet/shared/id-helper'

window.callbacks = {}
window.frBridge = {
  callback: function (handlerName: string, isSuccess: boolean, result: unknown) {
    const callback = window.callbacks[handlerName]
    callback && callback(isSuccess, result)
  },
}

export const jsBridge = <T = unknown> (method: string, data: JsonValue = null): Promise<T> => {
  if (!inApp) return Promise.reject('不在APP内')
  console.log('jsBridge', method, data)
  return new Promise((resolve, reject) => {
    const handlerName = 'func_' + createNumberId()
    window.callbacks[handlerName] = (isSuccess: boolean, res: unknown) => {
      // ios在失败时 第二个回调参数为true, 目前h5修改兼容
      if (res === 'failed') {
        console.log('jsBridge failed', method, 'error res', res)
        reject(res)
      }
      if (isSuccess) {
        let obj = null
        try {
          obj = JSON.parse(res as string)
        } catch (e) {
          obj = res
        }
        console.log('jsBridge success', method, ' res', obj)
        resolve(obj)
      } else {
        console.log('js<PERSON><PERSON> failed', method, 'error res', res)
        reject(res)
      }
    }
    if (typeof window.frJsHandler === 'undefined') {
      return reject('jsBridge is not ready')
    }
    window.frJsHandler.execute(method, data === null ? null : JSON.stringify(data), handlerName)
  })
}

export const inApp = typeof window.frJsHandler !== 'undefined'

declare global {
  interface Window {
    callbacks: Record<string, (isSuccess: boolean, res: unknown) => void>
    frJsHandler: {
      execute: (method: string, data: string | null, handlerName: string) => void
    }
    frBridge: {
      callback: (name: string, isSuccess: boolean, res: unknown) => void
    }
  }
}
