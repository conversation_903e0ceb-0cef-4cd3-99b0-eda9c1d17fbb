import { createComponent } from '@skynet/shared'
import { useScriptManageStore } from './script-manage-store'
import { onMounted } from 'vue'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { set } from 'lodash-es'
import { Button, Icon, Pager, transformNumber, transformTimestamp } from '@skynet/ui'
import { auditStatus, rateStatus, exportAble } from './constant'

type ScriptManagePageOptions = {
  props: {}
}
export const ScriptManagePage = createComponent<ScriptManagePageOptions>({
  props: {},
}, props => {
  const { onQuery, list, loading, total, Form, params, onReset, Table, columns, onPageChange, onPageSizeChange, currentRoles, isExporting, handleExport, getLabels } = useScriptManageStore()

  onMounted(async () => {
    await getLabels()
    void onQuery()
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>剧本库</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['剧名/ID', 'title_or_key_list', { type: 'text', placeholder: '请输入剧名/ID' }],
              ['公司', 'openplatform_principal', { type: 'text', placeholder: '请输入公司名称' }],
              ['审核状态', 'audit_status', { type: 'select', options: Object.entries(auditStatus).map(([value, label]) => ({ label, value })), placeholder: '请选择状态' }, { transform: transformNumber }],
              ['最终评级', 'audit3_rating', { type: 'select', options: rateStatus.map(s => {
                return {
                  label: s,
                  value: s
                }
              }), placeholder: '请选择最终评级' }],
              ['责编', 'principal', { type: 'text', placeholder: '请输入责编名称' }],
              [
                ['开始时间：', 'created_start', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
                ['结束时间：', 'created_end', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
              ],
            ]}
          />
        ),
        // tableActions: () => (
        //   <div class="flex items-center justify-end">
        //     {exportAble.some(role => currentRoles.value.includes(role)) && (
        //       <Button class="btn btn-primary btn-sm" onClick={handleExport} disabled={isExporting.value}>
        //         {isExporting.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
        //         导出
        //       </Button>
        //     )}
        //   </div>
        // ),
        table: () => (
          <Table
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_index} v-model:size={params.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ScriptManagePage
