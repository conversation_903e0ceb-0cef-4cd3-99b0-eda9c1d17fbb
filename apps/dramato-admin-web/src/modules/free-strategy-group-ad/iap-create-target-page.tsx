import { createComponent, mc } from '@skynet/shared'
import { Button, Checkbox, Empty, showAlert, transformDatetime, transformNumber, transformStringArray } from '@skynet/ui'
import { FormOptions } from '@skynet/ui/form/form-types'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { apiGetStrategyGroupAdChannelList, apiGetStrategyGroupUserProfile } from 'src/modules/strategy-group/strategy-group-api'
import { useStrategyGroupAdStore } from './strategy-group-ad-store'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { apiSaveStrategyGroupAds } from './strategy-group-ad-api'
import { ProfileCustomRenderers, profileDefaultValues } from '../strategy-group/profile-custom-renders'
export const FreeIapCreateTargetPage = createComponent(null, props => {
  const { formData, Form, stepOneError, validateStepOne, activeUserType,
    resetUserFormData, userProfileList, fetchingUserProfileList, platformMap } = useStrategyGroupAdStore()
  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')
  const userWhiteListEnabled = ref(false)

  watch(() => formData.value.user_platform, async () => {
    fetchingUserProfileList.value = true
    const response = await apiGetStrategyGroupUserProfile({ platform: platformMap[formData.value.user_platform] ?? 'All', scene: 'fr_ad_strategy_group' })
      .finally(() => {
        fetchingUserProfileList.value = false
      })
    if (!response) return
    userProfileList.value = response.data?.list ?? []
    if (formData.value.id) return
    userProfileList.value.forEach(item => {
      set(formData.value, `user_config.custom_users_config.${item.item_code}`, profileDefaultValues[item.item_code] ?? [''])
    })
  }, { immediate: true })

  const adChannelList = ref<Array<{ name: string, code: string }>>([])
  onMounted(async () => {
    const response = await apiGetStrategyGroupAdChannelList()
    if (!response) return
    adChannelList.value = response.data?.list ?? []
  })
  const router = useRouter()
  const onSubmit = () => {
    if (!validateStepOne()) return
    void router.push({ path: './solution', query: { ...route.query, id: formData.value.id } })
  }
  const addAdset = () => {
    if (!formData.value.user_config?.af_ad_config?.adset_list || !Array.isArray(formData.value.user_config?.af_ad_config?.adset_list)) {
      set(formData.value, 'user_config.af_ad_config.adset_list', [])
    }
    formData.value.user_config?.af_ad_config?.adset_list?.push({
      id: '',
      code: '',
      name: '',
    })
  }
  const removeAdset = (index: number) => {
    if (!formData.value.user_config?.af_ad_config?.adset_list) return
    if (formData.value.user_config.af_ad_config?.adset_list?.length <= 1) return
    formData.value.user_config?.af_ad_config?.adset_list?.splice(index, 1)
  }
  watch(() => formData.value.user_config?.af_ad_config?.adset_list?.map(item => item.code), value => {
    const list = formData.value.user_config?.af_ad_config?.adset_list ?? []
    list.forEach(item => { item.name = item.code })
  })

  watch(() => formData.value.user_white_list, () => {
    if (formData.value.user_white_list && formData.value.user_white_list.length > 0) {
      userWhiteListEnabled.value = true
    } else {
      userWhiteListEnabled.value = false
    }
  }, {
    immediate: true,
  })

  watch(() => userWhiteListEnabled.value, () => {
    if (!userWhiteListEnabled.value) {
      formData.value.user_white_list = undefined
    }
  })

  // 是否订阅用户级联逻辑
  watch(() => formData.value.user_config?.custom_users_config?.SubscribeStatus, (newV, oldV) => {
    if (!newV || !oldV || !formData.value.user_config?.custom_users_config) return
    const againstList = userProfileList.value.find(item => item.item_code === 'SubscribeStatus')?.item_child?.filter(item => ['All', '是（VIP中）', '从未订阅', '否，曾经是'].includes(item.item_name))?.map(item => item.item_value) ?? []
    const cascadeParentItem = userProfileList.value.find(item => item.item_code === 'SubscribeStatus')?.item_child?.filter(item => ['否，曾经是'].includes(item.item_name))?.map(item => item.item_value)?.[0] ?? undefined
    const cascadeChildList = userProfileList.value.find(item => item.item_code === 'SubscribeStatus')?.item_child?.filter(item => ['曾是Weekly', '曾是Yearly'].includes(item.item_name))?.map(item => item.item_value) ?? []
    const added = againstList.filter(item => newV.includes(item) && !oldV.includes(item))
    const removed = oldV.filter(item => !newV.includes(item))
    if (added.length > 0) {
      // 添加了互斥列表中的项
      if (cascadeParentItem !== undefined && added.includes(cascadeParentItem)) {
        set(formData.value.user_config.custom_users_config, 'SubscribeStatus', newV.filter(item => (againstList.includes(item) && added.includes(item)) || !againstList.includes(item)).concat(cascadeChildList.filter(item => !newV.includes(item))))
      } else {
        set(formData.value.user_config.custom_users_config, 'SubscribeStatus', newV.filter(item => (againstList.includes(item) && added.includes(item)) || !againstList.includes(item)))
      }
    } else if (removed.length > 0) {
      // 移除了级联父项
      if (cascadeParentItem !== undefined && removed.includes(cascadeParentItem)) {
        set(formData.value.user_config.custom_users_config, 'SubscribeStatus', newV.filter(item => !cascadeChildList.includes(item)))
      }
      // 移除了级联子项
      if (cascadeChildList.includes(removed[0])) {
        // 如果级联子项都被移除
        if (newV.filter(item => cascadeChildList.includes(item)).length === 0) {
          set(formData.value.user_config.custom_users_config, 'SubscribeStatus', newV.filter(item => item !== cascadeParentItem))
        }
      }
      if (newV.length === 0) {
        set(formData.value.user_config.custom_users_config, 'SubscribeStatus', [''])
      }
    }
  })

  // 近3天登录状态互斥规则
  watch(() => formData.value.user_config?.custom_users_config?.RecentThreeDaysLoginStatus, (newV, oldV) => {
    if (!newV || !oldV || !formData.value.user_config?.custom_users_config) return
    const againstList = userProfileList.value.find(item => item.item_code === 'RecentThreeDaysLoginStatus')?.item_child?.map(item => item.item_value) ?? []
    const added = againstList.filter(item => newV.includes(item) && !oldV.includes(item))
    if (added.length > 0) {
      set(formData.value.user_config.custom_users_config, 'RecentThreeDaysLoginStatus', newV.filter(item => (againstList.includes(item) && added.includes(item)) || !againstList.includes(item)))
    }
  })

  const items = computed(() => [
    [
      [requiredLabel('方案名称'), 'name', { type: 'text', disabled: isViewMode.value }, { class: 'w-[20em]' }],
      formData.value.id && ['方案id', 'id', { type: 'text', disabled: true }, { class: 'shrink-0' }],
    ],
    <h2 class="col-span-2 mb-4 font-bold text-lg"> { requiredLabel('目标用户群') } </h2>,
    [<span>用户设备 <small class="ml-2 text-gray-400">配置用户画像时，需保证用户设备与此选项一致</small></span>, 'platform', {
      type: 'radio',
      options: [
        { label: 'iOS', value: 'ios', disabled: isViewMode.value },
        { label: 'Android', value: 'android', disabled: isViewMode.value },
      ],
    }],
    ['用户类型', 'user_type', {
      type: 'radio',
      options: [
        { label: '自定义用户画像', value: 2, disabled: isViewMode.value },
        // { label: '活跃用户', value: 1, disabled: isViewMode.value },
        // { label: '联动广告组', value: 3, disabled: isViewMode.value },
      ],
    }],
    formData.value.user_type === 1 && [
      'flex px-8 py-6 bg-gray-100 rounded-lg mb-8',
      ['时间范围', 'user_config.active_users_config.time_type', { type: 'custom', render: () => (
        <div>
          <select disabled={isViewMode.value} v-model={activeUserType.value} class="w-full select-bordered select-sm select">
            <option value="latest_days">最近</option>
            <option value="start_and_end">起始时间</option>
          </select>
        </div>
      ) }],
      [Empty, 'user_config.active_users_config.latest_days', { type: 'number', suffix: '天', min: 1, max: 99, disabled: isViewMode.value },
        { class: activeUserType.value === 'latest_days' ? '' : 'hidden', transform: transformNumber }],
      ['开始时间', 'user_config.active_users_config.start_days', { type: 'datetime', disabled: isViewMode.value },
        { class: activeUserType.value === 'start_and_end' ? '' : 'hidden', transform: transformDatetime }],
      ['结束时间', 'user_config.active_users_config.end_days', { type: 'datetime', disabled: isViewMode.value },
        { class: activeUserType.value === 'start_and_end' ? '' : 'hidden', transform: transformDatetime }],
    ],
    formData.value.user_type === 2 && [
      'flex-col px-8 py-6 bg-gray-100 rounded-lg mb-8',
      // 自定义用户画像
      ...userProfileList.value.map(item =>

        [
          item.item_name,
          `user_config.custom_users_config.${item.item_code}`,
          Object.keys(ProfileCustomRenderers).includes(item.item_code)
            ? {
                type: 'custom',
                render: ProfileCustomRenderers[item.item_code](item),
              }
            : {
                type: 'checkbox-group',
                minLength: 1,
                options: item.item_child.map(child => ({ value: child.item_value, label: child.item_name, disabled: isViewMode.value })),
              },
          { errorVisible: false },
        ]),
    ],
    formData.value.user_type === 3 && [
      'grid grid-cols-[1fr_1fr_min-content] gap-4 px-8 py-6 bg-gray-100 rounded-lg mb-8 self-start',
      ...(formData.value.user_config?.af_ad_config?.adset_list?.map?.((adset, index) => [
        ['广告渠道商', `user_config.af_ad_config.adset_list.${index}.code`, { type: 'select', options: adChannelList.value.map(item => ({
          value: item.code,
          label: item.name,
          disabled: isViewMode.value,
        })) }],
        ['adset ID', `user_config.af_ad_config.adset_list.${index}.id`, { type: 'text', disabled: isViewMode.value }],
        [Empty, '', {
          type: 'custom',
          render: () => (
            index !== 0 ? <Button class="btn btn-outline btn-sm" disabled={isViewMode.value} onClick={() => removeAdset(index)}>删除</Button> : <Empty />
          ),
        }],
      ]).flat(1) ?? []),
      <div class="col-span-2">
        <Button class="btn btn-outline btn-sm" disabled={isViewMode.value} onClick={addAdset}>添加</Button>
      </div>,
    ],
    [
      requiredLabel('聚合平台'),
      'ad_platform',
      {
        type: 'radio',
        options: [{ label: 'admob', value: 'admob', disabled: isViewMode.value }, { label: 'max', value: 'max', disabled: isViewMode.value }, { label: 'meta', value: 'meta', disabled: isViewMode.value }],
        disabled: isViewMode.value,
      },
      {
        class: mc('col-span-1'),
      },
    ],
    <h2 class="col-span-2 font-bold text-lg"> 激励视频控制参数 </h2>,
    [
      '激励广告播放频次',
      'display_control.reward_ad_count_limit',
      {
        type: 'number',
        disabled: isViewMode.value,
      },
      {
        transform: transformNumber,
      },
    ],
    [
      [
        '看广告获得券数量',
        'display_control.watch_ad_count',
        {
          type: 'number',
          min: 0,
          max: 100,
          suffix: '次',
          disabled: isViewMode.value,
        },
        {
          transform: transformNumber,
          hint: '输入范围0-100，仅支持整数',
        },
      ],
      [
        '获得券',
        'display_control.watch_ad_obtain_ticket_count',
        {
          type: 'number',
          min: 0,
          suffix: '张',
          disabled: isViewMode.value,
        },
        {
          transform: transformNumber,
        },
      ],
    ],
    <h2 class="text-lg font-bold col-span-2"> 原生和插屏广告控制参数 </h2>,
    [
      '广告开始加载剧集',
      'display_control.start',
      {
        type: 'number',
        min: 1,
        disabled: isViewMode.value,
      },
      {
        transform: transformNumber,
        hint: '第N集开始插入广告',
      },
    ],
    [
      '间隔剧集',
      'display_control.skip',
      {
        type: 'number',
        min: 1,
        disabled: isViewMode.value,
      },
      {
        transform: transformNumber,
        hint: '间隔N集插入广告',
      },
    ],
    [
      '间隔时间',
      'display_control.watch_time',
      {
        type: 'number',
        min: 1,
        suffix: 'min',
        disabled: isViewMode.value,
      },
      {
        transform: [
          (raw?: unknown) => !raw ? '' : String(+raw / (1000 * 60)),
          (display: string) => display === '' ? undefined : Number(display) * 1000 * 60,
        ] as const,
      },
    ],
    [
      '原生广告强制观看时长（仅控制原生广告）',
      'display_control.ad_force_time',
      {
        type: 'number',
        min: 1,
        suffix: 's',
        disabled: isViewMode.value,
      },
      {
        transform: [
          (raw?: unknown) => !raw ? '' : String(+raw / 1000),
          (display: string) => display === '' ? undefined : Number(display) * 1000,
        ] as const,
      },
    ],
    [
      '预加载广告数量',
      'display_control.preload_ad_count',
      {
        type: 'number',
        min: 1,
        disabled: isViewMode.value,
      },
      {
        transform: transformNumber,
      },
    ],
    [
      '看第几次广告弹出激励视频弹窗',
      'display_control.show_reward_ad_native_ad_count',
      {
        type: 'number',
        min: 1,
        max: 999,
        disabled: isViewMode.value,
      },
      {
        transform: transformNumber,
      },
    ],
    [
      '看激励视频免广告时间',
      'display_control.skip_native_ad_time_ms',
      {
        type: 'number',
        min: 1,
        max: 999,
        suffix: 'min',
        disabled: isViewMode.value,
      },
      {
        transform: [
          (raw?: unknown) => !raw ? '' : String(+raw / (1000 * 60)),
          (display: string) => display === '' ? undefined : Number(display) * (1000 * 60),
        ] as const,
      },
    ],
    [
      '广告类型间gap',
      'display_control.ad_switch_interval',
      {
        type: 'custom',
        render: () => {
          return (
            <x-custom-input class="flex items-center gap-2">
              间隔 <input disabled={isViewMode.value} type="number" min={0} class="input input-bordered input-sm w-16" value={formData.value?.display_control?.ad_switch_interval} onInput={e => set(formData.value, 'display_control.ad_switch_interval', Number((e.target as HTMLInputElement)?.value))} placeholder="N次" /> N比1
            </x-custom-input>
          )
        },
      },
    ],
    [
      '连续几次不看激励视频免广告关闭弹窗（可输入0-999）',
      'display_control.ad_reward_limit',
      {
        type: 'number',
        min: 0,
        max: 999,
        suffix: '次',
        disabled: isViewMode.value,
      },
      {
        transform: transformNumber,
      },
    ],
    <h2 class="text-lg font-bold col-span-2"> 策略范围 </h2>,
    [
      'flex flex-col gap-4',
      ['', 'send_pro_user',
        { type: 'checkbox', label: '下发给符合条件的用户', disabled: isViewMode.value },
        { class: 'shrink-0', errorVisible: false, transform: [
          (raw: number) => {
            return raw ? true : false
          },
          (display: boolean) => {
            return display ? 1 : 0
          },
        ] },
      ],
      ['', 'user_white_list', { type: 'custom', render: ({ onInput, item, value }) => (
        <div>
          {/* {value?.toString() ?? ''} */}
          <Checkbox v-model={userWhiteListEnabled.value} label="下发给白名单用户" disabled={isViewMode.value} />
          <textarea value={value?.toString() ?? ''}
            onInput={e => onInput((e.target as HTMLTextAreaElement).value)}
            placeholder="请输入 UID，用逗号或者换行分隔"
            class="textarea-bordered w-full min-h-[8em] textarea textarea-sm"
            disabled={!userWhiteListEnabled.value}
          />
          {isViewMode.value && (
            <Button disabled={isViewMode.value} class="btn btn-sm btn-outline" onClick={async () => {
              const res = await apiSaveStrategyGroupAds({
                ...formData.value,
                save_type: 1,
                pay_mode: route.path.includes('iaa') ? 'IAA' : 'IAP',
              })
              if (res.code === 200) {
                showAlert('保存成功')
              }
            }}
            >保存白名单
            </Button>
          )}
        </div>
      ) }, { transform: transformStringArray, class: 'grow-1 w-full', errorVisible: false }],
    ],
  ] as FormOptions<FormData>['props']['items'])
  return () => (
    <div>
      <Form class="flex flex-col gap-4 p-8" data={formData.value} items={items.value}
        error={stepOneError.value}
        onSubmit={onSubmit}
        onReset={resetUserFormData}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        actions={() => (
          <div class="flex justify-between">
            {isViewMode.value ? <Empty /> : <Button class="btn btn-sm" type="reset">重置</Button>}
            <Button class="btn btn-primary btn-sm" type="submit">下一步</Button>
          </div>
        )}
        actionClass="col-span-2"
      />
    </div>
  )
})
export default FreeIapCreateTargetPage
