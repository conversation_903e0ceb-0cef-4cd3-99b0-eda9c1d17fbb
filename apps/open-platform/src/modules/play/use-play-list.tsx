import { ref } from 'vue'
import { Page, playApi } from './play-api'
import { bindLoading } from '@skynet/shared'

const list = ref<M.Play[]>([])
const fetchingList = ref(false)
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const fetchList = async ({ page_index, page_size }: Page) => {
  const data = await bindLoading(playApi.getList({ page_index, page_size }), fetchingList)
  list.value = data.list
  total.value = data.total
  page.value = page_index ?? 1
}
export const usePlayList = () => {
  return {
    list,
    fetchList,
    fetchingList,
    page,
    pageSize,
    total,
  }
}
