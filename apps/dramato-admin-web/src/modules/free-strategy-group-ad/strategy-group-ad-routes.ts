import { r, redirect } from '@skynet/shared'

export const FreeStrategyGroupAdRoutes = r('free-ad-strategy-group', '广告策略组', null, [
  redirect('', 'iap'),
  r(':tabName(iap|iaa)', '策略组', () => import('src/modules/free-strategy-group-ad/strategy-group-ad-page.tsx')),
  r('iaa/create', 'IAA广告策略组', () => import('src/modules/free-strategy-group-ad/strategy-group-ad-create-layout.tsx'), [
    redirect('', 'target'),
    r('target', '目标条件', () => import('src/modules/free-strategy-group-ad/strategy-group-iaa-create-target-page.tsx')),
    r('product', '商品配置', () => import('src/modules/free-strategy-group-ad/strategy-group-iaa-create-product-page.tsx')),
    r('plan', '任务计划', () => import('src/modules/free-strategy-group-ad/strategy-group-iaa-create-plan-page.tsx')),
  ]),
  r('iap/create', 'IAP广告方案', () => import('src/modules/free-strategy-group-ad/strategy-group-ad-create-layout.tsx'), [
    redirect('', 'target'),
    r('target', '目标条件', () => import('src/modules/free-strategy-group-ad/iap-create-target-page')),
    r('solution', '方案配置', () => import('src/modules/free-strategy-group-ad/strategy-group-iap-create-solution-page.tsx')),
    r('plan', '任务计划', () => import('src/modules/free-strategy-group-ad/strategy-group-iap-create-plan-page.tsx')),
  ]),
])
