input:disabled {
  cursor: not-allowed;
  @apply bg-[var(--fill-3)] border-[#e6e8ed] text-[var(--text-3)]
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.dramato-textarea {
  @apply w-full resize-none px-3 pt-4 pb-3 bg-[var(--fill-5)] overflow-y-auto text-[var(--text-1)] rounded-lg border-none outline-none active:border-none focus:border-none;
  &::placeholder {
    @apply text-[var(--text-4)];
  }
}

.dramato-mobile-textarea-word-limit {
  @apply pt-1 pb-2 pr-3 bg-[var(--fill-5)] text-[var(--grey-6)] text-right w-full !bottom-1 !right-0 rounded-b-lg;
}

.dramato-input {
  @apply no-tap-color w-full resize-none px-3 py-4 bg-[var(--fill-5)] text-[var(--text-1)] rounded-lg border-none outline-none active:border-none focus:border-none;
  &::placeholder {
    @apply text-[var(--text-4)];
  }
}

.dramato-button {
  &:disabled {
    @apply !bg-[var(--brand-3)] !text-[var(--text-8)];
  }
}

.button-primary {
  @apply rounded-[200px] border-none bg-[var(--brand-6)] hover:bg-[var(--brand-5)] active:bg-[var(--brand-7)] text-[14px] text-[var(--text-6)] font-medium tracking-[0.3px];
}

.multiline-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}