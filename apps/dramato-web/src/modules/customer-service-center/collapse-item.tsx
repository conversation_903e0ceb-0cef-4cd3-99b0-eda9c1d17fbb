import { createComponent, fn, mc } from '@skynet/shared'
import { ref } from 'vue'
import { arrow } from './images/images'
import { MdPreview } from 'md-editor-v3'
import { track } from 'src/lib/track'
import { Fn } from '@vueuse/core'
import { Button } from 'vant'
type CollapseItemOptions = {
  props: {
    title: string
    content: string
    button: string
  }
  emits: {
    expand: Fn
    clickButton: Fn
  }
}
export const CollapseItem = createComponent<CollapseItemOptions>({
  props: {
    title: '',
    content: '',
    button: '',
  },
  emits: {
    expand: fn,
    clickButton: fn,
  },
}, (props, { emit }) => {
  const visible = ref<boolean>(false)
  return () => (
    <x-collapse class="flex flex-col gap-y-2">
      <div class="py-3 justify-between items-start flex gap-3 text-[var(--white)] text-sm font-normal" onClick={() => {
        visible.value = !visible.value
        if (visible.value) {
          emit('expand')
        }
        track('csc', 'faq', 'click', {
          q_content: props.title,
          action_type: visible.value ? 1 : 0,
        })
      }}
      >
        {props.title}
        <img src={arrow} class={mc('w-5 h-5 transition-all duration-300 ease-in-out', visible.value ? 'rotate-180' : 'rotate-0')} />
      </div>
      { visible.value && (
        <div class="w-full">
          {
            props.button ? (
              <div class="
            px-3
            rounded-lg
            bg-[var(--grey-13)]
            text-[var(--grey-4)]
            py-4
            text-sm
            leading-tight
            [&_a]:bg-transparent
        [&_a]:text-[#4493f8]
            [&_a]:no-underline
            [&_ol]:list-decimal
            [&_ol]:pl-4
            [&_ol_li]:my-3
            [&_ul_li]:my-3
            [&_ul_li:nth-child(1)]:mt-0
            [&_ol_li:nth-child(1)]:mt-0
            [&_ol_li:nth-last-child(1)]:mb-0
            [&_ol+p]:mt-3
            [&_ul+p]:mt-3
            [&_p+ol]:mt-3
            [&_p+ul]:mt-3
            [&_img]:max-w-full
            [&_img]:mb-3
            [&_img]:pointer-events-none
            [&_img]:rounded-lg
            [&_figure_img]:mb-0
            flex flex-col items-start
            ">
                <x-desc>{props.content}</x-desc>
                <Button
                  class="mt-6 h-8 px-4 rounded-2 bg-[#434546] text-3.5 leading-8 text-[#FDFBFC] border-none"
                  onClick={() => emit('clickButton')}
                >
                  {props.button}
                </Button>
              </div>
            ) : (
              <MdPreview
                no-img-zoom-in={true}
                model-value={props.content}
                no-mermaid={true}
                showCodeRowNumber={true}
                preview-theme="default"
                class="
            px-3
            rounded-lg
            bg-[var(--grey-13)]
            text-[var(--grey-4)]
            py-4
            text-sm
            leading-tight
            [&_a]:bg-transparent
        [&_a]:text-[#4493f8]
            [&_a]:no-underline
            [&_ol]:list-decimal
            [&_ol]:pl-4
            [&_ol_li]:my-3
            [&_ul_li]:my-3
            [&_ul_li:nth-child(1)]:mt-0
            [&_ol_li:nth-child(1)]:mt-0
            [&_ol_li:nth-last-child(1)]:mb-0
            [&_ol+p]:mt-3
            [&_ul+p]:mt-3
            [&_p+ol]:mt-3
            [&_p+ul]:mt-3
            [&_img]:max-w-full
            [&_img]:mb-3
            [&_img]:pointer-events-none
            [&_img]:rounded-lg
            [&_figure_img]:mb-0
            "
              />
            )
          }
        </div>
      )}
    </x-collapse>
  )
})
