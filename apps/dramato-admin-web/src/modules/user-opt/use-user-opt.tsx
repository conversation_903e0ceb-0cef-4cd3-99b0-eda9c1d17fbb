import { CreateForm, CreateTableOld } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetUserOptList } from './user-opt-api'

export const useUserOpt = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
  }
}

const Form = CreateForm<M.UserOpt.Params>()
const params = ref<M.UserOpt.Params>({})

const Table = CreateTableOld<M.UserOpt.Item>()
const list = ref<M.UserOpt.Item[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(0)
const pageSize = ref<number>(20)
const total = ref<number>(1)

const search = async (_page?: number) => {
  _page = _page || page.value + 1
  loading.value = true
  const res = await apiGetUserOptList({
    ...params.value,
    page_info: {
      offset: (_page - 1) * pageSize.value, size: pageSize.value } })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.items || []
  total.value = res.data?.total || 0
  page.value = _page
}
