import Nested from 'postcss-nested'
import Import from 'postcss-import'
import AutoPrefixer from 'autoprefixer'
import Tai<PERSON>wind from 'tailwindcss'

export const postcssConfig = (ctx) => {
  /**
   * @type {import('postcss').ProcessOptions}
   */
  const config = {
    map: ctx.env === 'development' ? ctx.map : false,
    plugins: [
      AutoPrefixer(),
      Tailwind(),
      Import(),
      Nested(),
    ]
  }
  return config
}

export default postcssConfig
