import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformNumber } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { useUserPay } from './use-user-pay'
export const SupplementaryOrderForm = createComponent(null, () => {
  const Form = CreateForm<M.UserPay.orderFormParams>()
  const {
    closeOrderPopupModal,
    orderForm,
    isUpdating,
    onSave,
  } = useUserPay()
  const formRules = z.object({
    user_id: z.number().min(1, '请输入用户ID'),
    out_txn_id: z.string().min(1, '请输入订单ID'),
    sku_id: z.string().min(1, '请输入sku_id'),
    purchase_token: z.string().min(1, '请输入支付渠道token'),
  })

  const { error, validateAll } = useValidator(orderForm, formRules)
  onMounted(() => {
  })

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="grid flex-1 grid-cols-1 gap-y-1"
          hasAction={false}
          error={error.value}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onChange={(path, value: any) => {
            set(orderForm.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('UID'),
              path: 'user_id',
              input: {
                type: 'number',
                placeholder: '请输入UID',
              },
              transform: transformNumber,
              class: 'col-span-3',
            },
            {
              label: requiredLabel('订单ID'),
              path: 'out_txn_id',
              input: {
                type: 'text',
                placeholder: '请输入订单ID',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('支付渠道'),
              path: 'pay_channel',
              input: {
                type: 'select',
                options: [{
                  value: 'googleplay',
                  label: '谷歌',
                }, {
                  value: 'appleiap',
                  label: '苹果',
                }],
                autoInsertEmptyOption: false,
              },
              class: 'col-span-3',
            },
            {
              label: orderForm.value.pay_channel == 'googleplay' ? requiredLabel('商品sku') : '商品sku',
              path: 'sku_id',
              input: {
                type: 'text',
                placeholder: '请输入商品sku',
              },
              class: 'col-span-3',
            },
            {
              label: orderForm.value.pay_channel == 'googleplay' ? requiredLabel('支付渠道token') : '支付渠道token',
              path: 'purchase_token',
              input: {
                type: 'text',
                placeholder: '请输入支付渠道token',
              },
              class: 'col-span-3',
            },
          ]}
          data={orderForm.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeOrderPopupModal.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" disabled={isUpdating.value} onClick={() => {
          const exclude: string[] = []
          if (orderForm.value.pay_channel == 'appleiap') {
            exclude.push('sku_id')
            exclude.push('purchase_token')
          }
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          void onSave()
        }}>
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
