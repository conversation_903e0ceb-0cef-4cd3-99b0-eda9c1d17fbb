/**
 * 导出 csv 文件。注意，由于浏览器的安全限制，只能在用户主动触发的情况下导出
 */
export const exportAsCsv = (data: string[][], filename: string) => {
  const csvContent = data.map(e => e.join(',')).join('\n')

  // 添加 UTF-8 BOM
  const bom = '\uFEFF'
  const finalContent = bom + csvContent

  // 创建一个 Blob 对象
  const blob = new Blob([finalContent], { type: 'text/csv;charset=utf-8;' })

  // 创建下载链接
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
