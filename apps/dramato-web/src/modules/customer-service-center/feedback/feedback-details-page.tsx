import { createComponent, getQueries, mc } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { MyFeedbackApi } from './feedback-api'
import { Back } from 'src/h5_modules/common/back/back'
import { jsBridge } from 'src/lib/jsbridge'
import { Button } from '@skynet/ui'
import dayjs from 'dayjs'
import InfinityScroll from 'src/h5_modules/common/infinity-scroll/infinity-scroll'
import { commentBg, like, unlike } from '../images/images'
import { useLocale } from 'src/lib/use-locale'
import { showToast } from '../../common/toast/toast'
import { useRouter } from 'vue-router'
import { track } from 'src/lib/track'
import { isIos } from 'src/lib/ua'

type FeedbackDetailsPageOptions = {
  props: {}
}
export const FeedbackDetailsPage = createComponent<FeedbackDetailsPageOptions>({
  name: 'FeedbackDetailsPage',
  props: {},
}, props => {
  const router = useRouter()
  const { id, safe_bottom, language, showBack } = getQueries({ id: 1, safe_bottom: '', language: '', showBack: 1 })
  const chatList = ref<M.MyFeedback.Details.Chat[]>([])
  const workOrderInfo = ref<M.MyFeedback.Details.WorkOrderInfo>()
  const loadingFeedback = ref(false)
  const { t } = useLocale(language)

  const onCommentClick = (satisfied_status: number) => {
    track('feedback', 'evaluation', 'click', {
      content: satisfied_status === 1 ? 1 : 10,
    })
    void MyFeedbackApi.commentFeedback({ work_order_id: id, satisfied_status }).then(() => {
      showToast(t('toast.submit-success'))
      if (!workOrderInfo.value) {
        return
      }
      isFirstComment.value = false
      workOrderInfo.value.comment.satisfied_status = satisfied_status
    })
  }

  const renderChat = (i: M.MyFeedback.Details.Chat, index?: number) => (
    <x-message key={i.created} class={mc('flex gap-x-2 border border-x-0  border-b-0 border-solid border-[#2E2F30] py-5', index === 0 ? 'border-t-0' : 'border-t-.5')}>
      <img src={i.avatar} alt="" class="size-6 rounded-full" />
      <x-feedback-item-content class="flex flex-1 flex-col gap-y-2 overflow-hidden pr-2">
        <x-feedback-item-desc class="text-3 flex w-full flex-row items-center justify-between gap-x-2 text-[#797B7D]">
          <x-feedback-item-status class="text-3.5 leading-4.25 text-[#CCCACB]">{i.name}</x-feedback-item-status>
          <x-feedback-item-create>{dayjs(i.created * 1000).format('YYYY-MM-DD HH:mm:ss')}</x-feedback-item-create>
        </x-feedback-item-desc>
        <x-feedback-item-title class="text-3.5 leading-4.25 w-full break-all text-[#FDFBFC]">{i.description}</x-feedback-item-title>
        {
          i.image_list && i.image_list.length > 0 && i.image_list.map(img => <img src={img} alt="" class="w-full rounded-lg" />)
        }
      </x-feedback-item-content>
    </x-message>
  )

  const isFirstComment = ref(false)

  onMounted(() => {
    document.title = t('customer-service-center.myFeedback')

    void MyFeedbackApi.clickRedDot({ type: 2, work_order_id: id })
    loadingFeedback.value = true
    void MyFeedbackApi.getFeedbackDetail(id).then(res => {
      chatList.value = res.data?.chat_list || []
      workOrderInfo.value = res.data?.work_order_info
      isFirstComment.value = !res.data?.work_order_info?.comment?.satisfied_status
    }).finally(() => {
      loadingFeedback.value = false
    })
  })

  return () => (
    <x-feedback-details-page class="overflow-hide flex h-full flex-col bg-[var(--black)]">
      {showBack === 1 && <Back title={t('customer-service-center.myFeedback')} isWhite={true} class="shrink-0 bg-transparent" /> }

      <x-feedback-details-box class={mc(
        workOrderInfo.value?.feedback_status === 2 ? '' : jsBridge.inDramawaveApp ? 'mb-23' : 'mb-16',
        safe_bottom === 'false' ? 'pb-safe-bottom' : 'pb-0',
        ' w-full flex-1 overflow-auto px-3')}>
        <InfinityScroll
          hasMore={loadingFeedback.value}
          next=""
          loading={loadingFeedback.value}
          onLoad={undefined}
          loadingText={t('infinityScroll.loading')}
          endText={t('infinityScroll.end')}
        >
          {chatList.value.map((i, index) => (
            <>
              {renderChat(i, index)}
              {i.answer && i.answer.length > 0 && i.answer.map(j => renderChat(j))}
            </>
          ))}
        </InfinityScroll>

      </x-feedback-details-box>

      {
        workOrderInfo.value?.feedback_status === 2 ? (
          <x-action-dialog-main-wrapper
            class={mc(
              'text-[#FDFBFC] bg-[var(--black)] rounded-md  w-full block share-bottom-8-4',
              isFirstComment.value ? '' : 'hidden',
            )}
            style={{
              backgroundImage: `url(${commentBg})`,
              backgroundSize: '100% auto',
              backgroundRepeat: 'no-repeat',
            }}
          >
            <h3 class="h-16.75 text-4 flex flex-wrap items-center justify-center font-medium leading-5">
              {t('customer-service-center.are-you-satisfied-with-this-reply')}
            </h3>
            <main class="mb-3  flex w-full items-center justify-start gap-x-2 px-3">
              <div
                class={mc(
                  'h-22.5 flex flex-1 flex-col items-center justify-center gap-y-2 rounded-lg bg-[#242526]',
                  workOrderInfo.value?.comment.satisfied_status === 10 ? 'border border-1 border-solid border-[#FFF8E6]' : '',
                )}
                onClick={() => onCommentClick(10)}
              >
                <img src={unlike} class="size-10" />
                <div class="text-3.5 text-[##FFFFFF]">{t('customer-service-center.unsatisfied')}</div>
              </div>
              <div
                class={mc(
                  'h-22.5 flex flex-1 flex-col items-center justify-center gap-y-2 rounded-lg bg-[#242526]',
                  workOrderInfo.value?.comment.satisfied_status === 1 ? 'border border-1 border-solid border-[#FFF8E6]' : '',
                )}
                onClick={() => onCommentClick(1)}
              >
                <img src={like} class="size-10" />
                <div class="text-3.5 text-[##FFFFFF]">{t('customer-service-center.satisfied')}</div>
              </div>
            </main>
          </x-action-dialog-main-wrapper>
        ) : (
          <div class={`pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] fixed bottom-0 left-1/2 z-shareButton w-full -translate-x-1/2 bg-[var(--black)] ${jsBridge.inDramawaveApp ? 'h-23' : 'share-bottom-2 flex h-16 items-center pb-2 pt-3'}`}>
            <Button
              class="no-tap-color dramato-button pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] fixed  left-1/2 z-shareButton box-border h-11 w-[calc(100%-1.5rem)] -translate-x-1/2 cursor-pointer text-nowrap rounded-lg border-none bg-[var(--brand-6)] text-base font-medium leading-tight text-[var(--text-5)] outline-none"
              onClick={() => {
                void router.push({ path: 'feedback', query: { ...router.currentRoute.value.query, from: 'csc', id, problem_type: workOrderInfo.value?.problem_type } })
              }}
            >
              {t('customer-service-center.reply-to-feedback')}
            </Button>
          </div>
        )
      }
    </x-feedback-details-page>
  )
})

export default FeedbackDetailsPage
