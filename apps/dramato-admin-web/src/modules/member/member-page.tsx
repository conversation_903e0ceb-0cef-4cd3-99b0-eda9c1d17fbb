/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { CreateTableOld, CreateForm, transformNumber, TableColumnOld, Pager, showFailToast, Button, openDialog, showSuccessToast, Checkbox, showAlert } from '@skynet/ui'
import { nextTick, onMounted, ref, watch } from 'vue'
import { set, get, cloneDeep } from 'lodash-es'
import { periodList, consumablePeriodList, platformList, currencyList } from 'src/lib/constant.ts'
import dayjs from 'dayjs'
import { apiUpdateMemberPriority } from './member.api'
import { apiGetAppOptions } from 'src/modules/application/application.api'
import { AxiosError } from 'axios'
import { z } from 'zod'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { apiGetProductUseDate } from '../recharge-level/recharge-level-api'
import { MemberForm } from './member-form'
import { useMember } from './use-member.tsx'

type MemberPageOptions = {
  props: {
    hasActions?: boolean
    hasCheckItem?: boolean
    checkedItem?: M.MemberLevel[]
    hasNav?: boolean
    hasPriority?: boolean
    platform?: number
    appIdSelectDisabled?: boolean
  }
  emits: {
    // hide: () => void
    add: (id: M.MemberLevel) => void
    remove: (id: M.MemberLevel) => void
  }
}

export const MemberPage = createComponent<MemberPageOptions>({
  props: {
    hasActions: true,
    hasCheckItem: false,
    checkedItem: [],
    hasNav: true,
    hasPriority: true,
    platform: 1,
    appIdSelectDisabled: false,
  },
  emits: {
    add: (item: M.MemberLevel) => {},
    remove: (item: M.MemberLevel) => {},
  },
}, (props, { emit }) => {
  const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0'
  const numbers = Array.from({ length: 12 }, (_, index) => ({
    value: index + 1,
    label: String(index + 1),
  }))
  const QueryForm = CreateForm<M.QueryMemberLevel>()
  const Table = CreateTableOld<M.MemberLevel>()
  const {
    defaultData,
    form,
    dialogRef,
    appList,
    getList,
    tableLoading,
    list,
    queryForm,
    total,
    onPageSizeChange,
    onPageChange,
    onQuery,
    onReset,
  } = useMember()

  onMounted(async () => {
    list.value = []
    queryForm.value.app_id = undefined
    tableLoading.value = true
    await getAppList()
    await onQuery()
  })
  watch(() => [
    form.value.app_id,
    form.value.status,
  ], newVal => {
    const platform = appList.value.find(row => row.value === newVal[0])?.platform
    form.value.store = platform === 'h5' ? 'h5' : platform === 'iOS' ? 'Apple Store' : 'Google Play'
    form.value.membership_type = form.value?.membership_type ? form.value?.membership_type : 'vip'
    form.value.platform = platform
  }, {
    immediate: true,
  })

  watch(() => [queryForm.value.app_id, props.hasCheckItem], newVal => {
    const platform = appList.value.find(row => row.value === newVal[0])?.platform
    // 如果是h5, 搜索表单显示为空
    queryForm.value.store = platform === 'h5' ? '' : platform === 'iOS' ? 'Apple Store' : 'Google Play'
    if (platform === 'h5' && props.hasCheckItem) {
      set(queryForm.value, 'show_platform', 'ios')
    } else {
      set(queryForm.value, 'show_platform', '')
    }
  })

  const getPeriodName = (row: M.MemberLevel) => {
    return consumablePeriodList.find(item => item.value === row?.delivery_details?.period)?.label
  }

  const showCreateDialog = () => {
    dialogRef.value = openDialog({
      mainClass: dialogMainClass,
      title: () => <div>{ form.value.id ? '编辑会员档位' : '新建会员档位' }</div>,
      body: () => <MemberForm />,
    })
  }

  const changePriority = async (row: M.MemberLevel, value: string, oldValue: number) => {
    if (+oldValue === +value) return
    row.priority = +value
    const schema = z.number().min(1, {
      message: '请输入正确序号',
    }).int({
      message: '请输入正确序号 1-12',
    }).max(12, {
      message: '请输入正确序号 1-12',
    })
    try {
      schema.parse(+value)

      tableLoading.value = true
      const res = await apiUpdateMemberPriority({
        id: row.id as number,
        priority: +value,
      })
      if (res.code === 200) {
        showSuccessToast('操作成功！')
        await onQuery()
      } else {
        row.priority = oldValue
        showFailToast(res?.message || '服务忙碌，稍后再试')
      }
    } catch (e) {
      row.priority = oldValue
      const error = e as AxiosError
      showFailToast(get(e, 'issues[0].message') || get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    }
  }
  const getAppList = async () => {
    try {
      const res = await apiGetAppOptions({
        app_name: '',
      })
      if (!res.data) return
      appList.value = res.data.list?.filter(i => i.app_name.includes('Drama')).map(row => {
        return {
          value: row.id,
          label: row.app_name,
          platform: platformList.find(item => item.value === row.platform)?.label || '',
        }
      }) || []
      await nextTick()
      // queryForm.value.app_id = appList.value.filter(i => (i.platform === 'iOS' && props.platform === 1) || (i.platform !== 'iOS' && props.platform === 2))[0]?.value

      let key = props.platform === 1 ? window.location.origin.includes('-test') ? 'DramaBuzz_iOS' : 'Dramawave_iOS' : 'Dramawave_Android'
      if (props.platform === 3) {
        key = 'Dramawave_H5'
      }
      queryForm.value.app_id = appList.value?.find(i => i.label === key)?.value || 0
    } catch (e) {
      const error = e as AxiosError
      showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    }
  }
  const columns: TableColumnOld<M.MemberLevel>[] = [
    [
      '',
      row => {
        const id = row.id as number
        return (
          <Checkbox
            label=""
            disabled={!!!id}
            modelValue={props.checkedItem.map(i => i.id).includes(id)}
            onUpdate:modelValue={(value: unknown) => {
              if (value) {
                if (!props.checkedItem.map(i => i.id).includes(id)) emit('add', row)
              } else {
                const rowIndex = props.checkedItem.findIndex(i => i.id === id)
                if (rowIndex !== -1) {
                  emit('remove', row)
                }
              }
            }}
          />
        )
      },
      { class: mc('w-[60px]', props.hasCheckItem ? '' : 'hidden') },
    ],
    ['档位ID', 'id', { class: 'w-[100px]' }],
    ['档位说明（内部用）', 'title_admin', { class: 'w-[200px]' }],
    ['商品名称', 'title', { class: 'w-[200px]' }],
    ['序号', row => {
      return row.status === 1 && (
        <select class="select select-bordered select-sm" value={row.priority} onChange={e => {
          const target = e.target as HTMLSelectElement
          const value = target.value
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          changePriority(row, value, row.priority as number)
        }}
        >
          {
            numbers.map(n => <option value={n.value}>{n.label}</option>)
          }
        </select>
      )
    },
    { class: mc('w-[100px]', props.hasPriority ? '' : 'hidden') },
    ],
    ['订阅周期', row => <div><span class={mc(row.membership_type === 'coins_pass' ? '' : 'hidden')}>-</span><span class={mc(row.membership_type === 'coins_pass' ? 'hidden' : '')}>{row?.delivery_details?.quanity}{getPeriodName(row)}</span></div>, { class: 'w-[120px]' }],
    ['币种', row => <span>{currencyList.find(item => item.value === row.currency)?.label}</span>, { class: 'w-[120px]' }],
    ['类型', row => <span>{row.first_recharge ? (row.first_recharge === 1 ? '首充' : '再订阅') : '常规'}</span>, { class: 'w-[120px]' }],
    ['价格', row => +((row?.price || 0) / 100).toFixed(2), { class: 'w-[120px]' }],
    ['折扣价', row => <span>{(row.price !== row.discount_price) ? +((row.discount_price || 0) / 100).toFixed(2) : ''}</span>, { class: 'w-[120px]' }],
    ['商品ID', 'sku_id', { class: 'w-[230px]' }],
    ['上架商店', 'store', { class: 'w-[120px]' }],
    ['应用名称', row => {
      return appList.value.find(item => item.value === row.app_id)?.label
    }, { class: 'w-[160px]' }],
    ['状态',
      row => (
        <div class="flex items-center space-x-1">
          {row.status === 1
            ? (
                <>
                  <div class="badge badge-xs bg-green-600" />
                  <div>展示</div>
                </>
              )
            : (
                <>
                  <div class="badge badge-xs bg-gray-300" />
                  <div>不展示</div>
                </>
              )}
        </div>
      ),
      { class: 'w-[100px]' },
    ],
    [
      '更新时间',
      row => !!row.updated ? dayjs(row.updated * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
      { class: 'w-[180px]' },
    ],
    ['更新人', 'update_user', { class: 'w-[160px]' }],
    ['渠道', 'show_platform', { class: 'w-[160px]' }],
    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap">
            <Button class="btn btn-link btn-primary btn-sm" onClick={() => {
              const editRow = cloneDeep(row)
              editRow.price = +((editRow.price || 0) / 100).toFixed(2)
              editRow.discount_price = +((editRow.discount_price || 0) / 100).toFixed(2)
              editRow.time_limit = !editRow.time_limit ? undefined : editRow.time_limit
              editRow.member_limited = 1
              if (editRow.membership_type === 'coins_pass') {
                editRow.delivery_details!.periodDay = periodList.find(item => item.value === editRow.delivery_details!.period)?.days
              }
              form.value = editRow
              showCreateDialog()
            }}
            >
              编辑
            </Button>
            <Button class="btn btn-link btn-sm" onClick={() => {
              void apiGetProductUseDate({ id: row.id || 0 }).then(rs => {
                if (!rs.data?.items || rs.data?.items?.length === 0) {
                  return showAlert('该档位暂无使用记录', 'error')
                }
                openDialog({
                  title: '使用情况',
                  body: () => (
                    <x-content class="flex flex-col gap-y-2">
                      {
                        rs.data?.items?.map(item => {
                          return (
                            <div class="flex flex-row flex-wrap gap-x-2 break-all">
                              <span>{item.label.replace('3', '')}：</span>
                              <span>{item.ids.join(',')}</span>
                            </div>
                          )
                        })
                      }
                    </x-content>
                  ),
                })
              })
            }}>
              使用情况
            </Button>
            <Button class={mc('btn btn-sm btn-link btn-error', row.first_recharge === 2 ? '' : 'hidden')} onClick={() => {
              const editRow = cloneDeep(row)
              editRow.price = +((editRow.price || 0) / 100).toFixed(2)
              editRow.discount_price = +((editRow.discount_price || 0) / 100).toFixed(2)
              editRow.member_limited = 1
              if (editRow.membership_type === 'coins_pass') {
                editRow.delivery_details!.periodDay = periodList.find(item => item.value === editRow.delivery_details!.period)?.days
              }
              form.value = editRow
              delete form.value.id
              showCreateDialog()
            }}>
              创建首订档位
            </Button>
          </div>
        )
      },
      { class: mc('w-[300px]', props.hasActions ? '' : 'hidden') },
    ],
  ]

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: props.hasNav
          ? () => (
              <ul>
                <li>会员档位管理</li>
              </ul>
            )
          : null,
        form: () => (
          <QueryForm class="flex w-full flex-row"
            onSubmit={onQuery}
            onReset={onReset}
            data={queryForm.value}
            onChange={(path, value) => {
              set(queryForm.value, path, value)
            }}
            items={[
              { label: () => (
                <div>应用： <span class="text-sm">上架商店：{ queryForm.value.store || '无' }</span></div>
              ), path: 'app_id', input: { type: 'select', options: appList.value, autoInsertEmptyOption: false,
                disabled: props.appIdSelectDisabled,
              }, transform: transformNumber },
              { label: '档位说明', path: 'title_admin', input: { type: 'text' } },
              { label: '档位ID', path: 'id',
                input: { type: 'number' }, transform: transformNumber },
              { label: '商品名称', path: 'title', input: { type: 'text' } },
              { label: '商品ID', path: 'product_id', input: { type: 'text' } },
              {
                label: '档位属性',
                path: 'first_recharge',
                transform: transformNumber,
                input: {
                  type: 'select',
                  options: [
                    { label: '常规', value: 0 },
                    { label: '首充', value: 1 },
                    { label: '再订阅', value: 2 },
                  ],
                },
              },
              { label: '价格', path: 'price',
                input: { type: 'number' }, transform: transformNumber },
              // { label: '周期', path: 'period',
              //   input: { type: 'select', options: [
              //     {label: '周', value: 'weekly'},
              //     {label: '月', value: 'monthly'},
              //     {label: '年', value: 'yearly'}
              //   ] } },
              { label: '面板展示：', path: 'status', input: { type: 'select', options: [{
                value: 1,
                label: '展示',
              },
              {
                value: 2,
                label: '不展示',
              }] }, transform: transformNumber },
            ]}
          />
        ),
        tableActions: () => (
          props.hasActions
            ? (
                <x-table-actions class="flex items-center gap-4">
                  <span class="mr-auto">会员档位列表</span>
                  <Button class="btn btn-primary btn-sm" onClick={() => {
                    form.value = cloneDeep(defaultData)
                    form.value.app_id = queryForm.value.app_id
                    form.value.tips = 'Auto renew · Cancel anytime'
                    showCreateDialog()
                  }}
                  >新建会员档案
                  </Button>
                </x-table-actions>
              )
            : null
        ),
        table: () => (
          <Table
            list={list.value || []}
            class="tm-table-fix-last-column"
            columns={columns}
            loading={tableLoading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={queryForm.value.page} v-model:size={queryForm.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default MemberPage
