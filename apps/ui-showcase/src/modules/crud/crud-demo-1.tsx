import { createComponent, useValidator } from '@skynet/shared'
import { CreateForm, transformBool, transformNumber } from '@skynet/ui'
import { Wrapper } from 'src/layout/wrapper'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { z } from 'zod'
type CrudDemo1Options = {
  props: {}
}
export const CrudDemo1 = createComponent<CrudDemo1Options>({
  props: {},
}, () => {
  type Data = {
    id?: number
    name: string
    gender: string
    birthday?: string
    enabled?: boolean
    city: string[]
  }
  const Form = CreateForm<Data>()
  const formData = ref<Data>({
    id: undefined,
    name: '',
    gender: '',
    birthday: undefined,
    enabled: undefined,
    city: [],
  })
  const onSubmit = () => {
    if (validateAll()) {
      console.log('ajax')
    }
  }
  const { validateAll, error } = useValidator(formData, z.object({
    id: z.number().int().positive(),
    name: z.string().min(1),
    city: z.string().array().nonempty({
      message: '请选择城市',
    }),
  }))
  return () => (
    <div class="space-y-4">
      <Wrapper>
        <Form
          onSubmit={onSubmit}
          onReset={(_e, data) => { formData.value = data }}
          data={formData.value}
          onChange={(path, value) => {
            set(formData.value, path, value)
          }}
          error={error.value}
          items={[
            { label: <span>ID *</span>, path: 'id', input: { type: 'text' }, transform: transformNumber },
            { label: '姓名', path: 'name', input: { type: 'text' } },
            { label: '生日', path: 'birthday', input: { type: 'date' } },
            { label: '启用', path: 'enabled', transform: transformBool(), input: { type: 'radio', options: [
              { label: '是', value: true },
              { label: '否', value: false },
            ] } },
            { label: '性别', path: 'gender', input: {
              type: 'select',
              options: [
                { label: '男', value: 'male' },
                { label: '女', value: 'female' },
              ],
            } },
            { label: '城市', path: 'city', input: { type: 'checkbox', options: [
              { label: '北京', value: '北京' },
              { label: '天津', value: '天津' },
            ] } },
          ]}
        />
      </Wrapper>
    </div>
  )
})

export default CrudDemo1
