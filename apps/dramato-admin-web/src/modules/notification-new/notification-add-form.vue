<template>
  <el-form :model="form" :rules="rules">
    <el-form-item label="push类型" :label-width="formLabelWidth" v-if="form.list_type === 2">
      <el-select v-model="form.rec_type" placeholder="请选择">
        <el-option label="签到提醒" :value="23"></el-option>
        <el-option label="订阅到期次日" :value="19"></el-option>
        <el-option label="订阅到期5天" :value="20"></el-option>
        <el-option label="免费部分完播" :value="21"></el-option>
        <el-option label="充值用户" :value="22"></el-option>
        <el-option label="续播剧" :value="3"></el-option>
        <el-option label="客户端主动获取" :value="15"></el-option>
        <el-option label="高付费用户" :value="16"></el-option>
        <el-option label="免费剧" :value="17"></el-option>
        <el-option label="折扣剧" :value="18"></el-option>
        <el-option label="新剧上线" :value="24"></el-option>
        <el-option label="付费版个性化推荐" :value="26"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="Push标题" :label-width="formLabelWidth" props="style_config.title">
      <el-input @input="() => turnToUnTranslate([1, 2])" v-model="form.style_config.title" :maxlength="titleMaxLength"
        show-word-limit autocomplete="off" />
    </el-form-item>
    <el-form-item label="Push内容" :label-width="formLabelWidth" props="style_config.body">
      <div class="flex items-center w-full space-x-2">
        <el-input ref="textareaRef" class="flex-1" @input="() => turnToUnTranslate([1, 2])"
          v-model="form.style_config.body" :maxlength="contentMaxLength" show-word-limit type="textarea" />
        <SvgIcon class="cursor-pointer size-5" name="ic_emoji" @click="emojiPickerVisible = true" />
      </div>
    </el-form-item>
    <el-form-item label="" :label-width="formLabelWidth">
      <span class="text-xs text-gray-400">{{ titleTip }}</span>
      <div class="flex justify-between w-full"><span>翻译状态： {{ checkIsTranslate(1) ? '已翻译' : '未翻译' }}
          <el-icon class="translate-y-0.5">
            <CircleCheckFilled v-if="checkIsTranslate(1)" />
            <CircleCloseFilled v-else />
          </el-icon>
        </span><el-button @click="() => getTranslateContent([form.style_config.title, form.style_config.body], [1, 2])"
          type="primary">一键翻译</el-button>
      </div>
    </el-form-item>
    <el-form-item label="样式" :label-width="formLabelWidth">
      <el-select v-model="form.style_config.push_type" placeholder="请选择">
        <el-option label="1-纯文本" value="1"></el-option>
        <el-option label="2-小图（仅安卓）" value="2"></el-option>
        <el-option label="3-大图和小图" value="3"></el-option>
        <el-option label="4-左图右文(仅IOS)" value="4"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="" :label-width="formLabelWidth">
      <span class="text-xs text-gray-400">{{ listTypeTip }}</span>
    </el-form-item>
    <el-form-item label="小图" :label-width="formLabelWidth" v-if="form.style_config.push_type !== '1'" required>
      <x-upload-cover class="grid gap-y-2">
        <Uploader v-loading="image1Loading" accept="png,jpg,jpeg" :maxsize="1024 * 1024 * 10"
          class="border-[1px] border-dashed rounded-md cursor-pointer overflow-hidden size-[200px]"
          @uploadSuccess="(d) => { console.log(d); form.style_config.small_image = d.temp_path || ''; image1Loading = false }"
          :beforeUpload="(obj) => { image1Loading = true }" :isImage="true" uploadUrl="/push/upload/image">
          <img v-if="form.style_config.small_image" :src="form.style_config.small_image.includes('https://') ?
            form.style_config.small_image : 'https://static-v1.mydramawave.com/push/task/image/notify/' +
            form.style_config.small_image" class="object-cover size-full" />
          <span v-else class="flex items-center justify-center size-full">上传素材</span>
        </Uploader>
        <x-upload-cover-tip class="text-xs text-gray-400">提示：1.支持 png、jpg、jpeg 格式图片，大小限制 10M 以内
          2.建议图片尺寸iOS：351*182；Android：351*223；两端同时发送可参考iOS 建议尺寸
        </x-upload-cover-tip>
      </x-upload-cover>
    </el-form-item>
    <el-form-item label="大图" :label-width="formLabelWidth" v-if="['3', '4'].includes(form.style_config.push_type)"
      required>
      <x-upload-cover class="grid w-full gap-y-2">
        <Uploader v-loading="image2Loading" accept="png,jpg,jpeg" :maxsize="1024 * 1024 * 10"
          class="border-[1px] border-dashed rounded-md cursor-pointer overflow-hidden h-[200px] w-full"
          :beforeUpload="(obj) => { image2Loading = true }"
          @uploadSuccess="(d) => { console.log(d); form.style_config.image = d.temp_path || ''; image2Loading = false }"
          :isImage="true" uploadUrl="/push/upload/image">
          <img v-if="form.style_config.image" :src="form.style_config.image.includes('https://') ?
            form.style_config.image : 'https://static-v1.mydramawave.com/push/task/image/notify/' +
            form.style_config.image" class="object-cover size-full" />
          <span v-else class="flex items-center justify-center size-full">上传素材</span>
        </Uploader>
        <x-upload-cover-tip class="text-xs text-gray-400">提示：支持 png、jpg、jpeg 格式图片，大小限制 10M 以内
        </x-upload-cover-tip>
      </x-upload-cover>
    </el-form-item>
    <el-form-item label="tip" :label-width="formLabelWidth" v-if="form.style_config.push_type !== '4'">
      <el-radio-group v-model="form.style_config.has_tips">
        <el-radio :value="true">是</el-radio>
        <el-radio :value="false">否</el-radio>
      </el-radio-group>
      <div class="flex gap-2" v-if="form.style_config.has_tips">
        <el-form-item label="文案">
          <el-input @input="() => turnToUnTranslate([3])" maxlength="10" show-word-limit
            v-model="form.style_config.tips" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="颜色">
          <el-select style="width: 100px; " v-model="form.style_config.tips_style" placeholder="请选择">
            <el-option label="空" value=""></el-option>
            <el-option label="purple" value="purple"></el-option>
            <el-option label="blue" value="blue"></el-option>
            <el-option label="green" value="green"></el-option>
            <el-option label="orange" value="orange"></el-option>
            <el-option label="red" value="red"></el-option>
            <el-option label="cyan" value="cyan"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div v-if="form.style_config.has_tips" class="flex justify-between w-full mt-2"><span>翻译状态： {{
        checkIsTranslate(3) ? '已翻译' : '未翻译' }}
        </span><el-button @click="() => getTranslateContent([form.style_config.tips], [3])"
          type="primary">一键翻译</el-button>
      </div>
    </el-form-item>
    <el-form-item label="Deeplink" :label-width="formLabelWidth">
      <el-select v-model="form.style_config.btn1_deep_link" placeholder="请选择">
        <el-option v-for="dpl in deepLinks" :label="dpl.name" :value="dpl.path"></el-option>
      </el-select>
      <el-input class="mt-2" v-if="form.style_config.btn1_deep_link === '/'"
        v-model="form.style_config.btn1_deep_link_custom" placeholder="请输入" />
      <el-input class="mt-2" v-if="form.style_config.btn1_deep_link.indexOf('webpage') > -1"
        v-model="form.style_config.btn1_deep_link_url" placeholder="请输入跳转的url" />
      <div class="mt-2 text-xs text-gray-400">提示：此为点击push主体时的跳转链接，按钮1共用此链接，按钮2单独设置
      </div>
    </el-form-item>
    <el-form-item v-if="!['1', '4'].includes(form.style_config.push_type)" label="按钮/进度条" :label-width="formLabelWidth">
      <el-radio-group v-model="form.style_config.buttonOrProgress">
        <el-radio value="1">按钮</el-radio>
        <el-radio value="2">进度条</el-radio>
        <el-radio value="3">无</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="" :label-width="formLabelWidth"
      v-if="form.style_config.buttonOrProgress === '1' && form.style_config.push_type !== '1'">
      <el-form-item label="按钮1">
        <el-radio-group v-model="form.style_config.has_btn1">
          <el-radio :value="true">有</el-radio>
          <el-radio :value="false">无</el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-if="form.style_config.has_btn1">
        <el-form-item label="文案">
          <el-input @input="() => turnToUnTranslate([4])" v-model="form.style_config.btn1_text" show-word-limit
            :maxlength="button1MaxLength" placeholder="" />
          <div class="flex justify-between w-full mt-2"><span>翻译状态： {{ checkIsTranslate(4) ? '已翻译' : '未翻译' }}
            </span><el-button @click="() => getTranslateContent([form.style_config.btn1_text], [4])"
              type="primary">一键翻译</el-button>
          </div>
        </el-form-item>
      </div>
    </el-form-item>

    <el-form-item label="" :label-width="formLabelWidth"
      v-if="form.style_config.buttonOrProgress === '1' && !['1', '4'].includes(form.style_config.push_type)">
      <el-form-item label="按钮2">
        <el-radio-group v-model="form.style_config.has_btn2">
          <el-radio :value="true">有</el-radio>
          <el-radio :value="false">无</el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-if="form.style_config.has_btn2">
        <el-form-item label="文案">
          <el-input @input="() => turnToUnTranslate([5])" v-model="form.style_config.btn2_text" show-word-limit
            :maxlength="button2MaxLength" placeholder="" />
          <div class="flex justify-between w-full mt-2"><span>翻译状态： {{ checkIsTranslate(5) ? '已翻译' : '未翻译' }}
            </span><el-button @click="() => getTranslateContent([form.style_config.btn2_text], [5])"
              type="primary">一键翻译</el-button>
          </div>
        </el-form-item>
        <el-form-item class="mt-2" label="Deeplink" :label-width="formLabelWidth">
          <el-select v-model="form.style_config.btn2_deep_link" placeholder="请选择">
            <el-option v-for="dpl in deepLinks" :label="dpl.name" :value="dpl.path"></el-option>
          </el-select>
          <el-input class="mt-2" v-if="form.style_config.btn2_deep_link === '/'"
            v-model="form.style_config.btn2_deep_link_custom" placeholder="请输入" />
          <el-input class="mt-2" v-if="form.style_config.btn2_deep_link.indexOf('webpage') > -1"
            v-model="form.style_config.btn2_deep_link_url" placeholder="请输入跳转的url" />
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="" :label-width="formLabelWidth"
      v-if="form.style_config.buttonOrProgress === '2' && !['1', '4'].includes(form.style_config.push_type)">
      <el-form-item label="进度值">
        <el-radio-group v-model="form.style_config.media_progress_type">
          <el-radio value="1">随机值</el-radio>
          <el-radio value="2">固定值</el-radio>
        </el-radio-group>
        <el-input style="width: 100px; margin-left: 16px;" v-model="form.style_config.media_progress" type="number"
          max="100" placeholder="请输入" />
      </el-form-item>
    </el-form-item>
    <el-form-item label="应用" :label-width="formLabelWidth" required>
      <el-select v-model="form.target_app_names_source" multiple placeholder="请选择">
        <el-option :label="item.label" :value="item.label" v-for="item in appOptions" :key="item.value"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="用户范围" :label-width="formLabelWidth">
      <el-radio-group v-model="form.target_user_type">
        <el-radio :value="1">全部开Push用户</el-radio>
        <el-radio :value="3">指定用户分层</el-radio>
        <el-radio :value="2">指定用户</el-radio>
      </el-radio-group>
      <div class="w-full mt-2" v-if="form.target_user_type === 3">
        <el-form-item label="用户分层ID" :label-width="formLabelWidth">
          <el-input v-model="form.target_user_val" type="textarea" placeholder="用户分层ID之间用英文逗号分隔" />
        </el-form-item>
      </div>
      <div class="w-full mt-2" v-if="form.target_user_type === 2">
        <el-form-item label="用户ID" :label-width="formLabelWidth">
          <el-input v-model="form.target_user_val" type="textarea" placeholder="用户ID之间用英文逗号分隔" />
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="剧集范围" :label-width="formLabelWidth">
      <el-radio-group v-model="form.resource_scope_type">
        <el-radio :value="2">指定剧集</el-radio>
        <el-radio :value="0">无</el-radio>
      </el-radio-group>
      <!-- /** 指定剧集 */ -->
      <div class="w-full" v-if="form.resource_scope_type === 2">
        <el-form-item label="资源ID" :label-width="formLabelWidth">
          <el-input v-model="form.resource_scope_val" type="textarea" placeholder="资源ID之间用英文逗号分隔" />
          <div class="text-xs text-gray-400">提示：包含多部时，将随机选取一部进行发送</div>
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item v-if="form.resource_scope_type === 2" label="剧集类型" :label-width="formLabelWidth">
      <el-checkbox-group v-model="form.audio_types_for_show">
        <el-checkbox :value="0">字幕剧</el-checkbox>
        <el-checkbox :value="1">人工配音剧</el-checkbox>
        <el-checkbox :value="2">AI配音剧</el-checkbox>
      </el-checkbox-group>
      <!-- <el-radio-group v-model="form.audio_type">
        <el-radio :value="0">字幕剧</el-radio>
        <el-radio :value="1">人工配音剧</el-radio>
        <el-radio :value="2">AI配音剧</el-radio>
      </el-radio-group> -->
    </el-form-item>
    <el-form-item label="发送时间" :label-width="formLabelWidth">
      <el-radio-group v-model="form.ageing_type">
        <el-radio :value="1">立即发送</el-radio>
        <el-radio :value="2">定时发送</el-radio>
      </el-radio-group>
      <div class="flex flex-col mt-2 gap-y-2" v-if="form.ageing_type === 2">
        <el-form-item label="开始时间" :label-width="formLabelWidth">
          <el-date-picker v-model="form.timed_start_date_time" type="datetime" placeholder="选择日期时间"
            value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" :label-width="formLabelWidth">
          <el-date-picker v-model="form.timed_end_date_time" type="datetime" placeholder="选择日期时间"
            value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
          <div class="mt-2 text-xs text-gray-400">提示：均为当地时间</div>
        </el-form-item>
      </div>
      <div class="flex gap-1 mt-2" v-if="form.ageing_type === 1">
        结束时间： <el-input style="width: 40px;" v-model="form.end_now_add_hours" type="number"></el-input>小时
        <el-input style="width: 40px;" v-model="form.end_now_add_minute" type="number"></el-input>分钟后
      </div>
    </el-form-item>
    <!-- 翻译弹窗 -->
    <el-dialog v-model="translateVisible" width="500" title="一键翻译" append-to-body>
      <el-table v-loading="translateLoading" :data="translateContent" style="width: 100%" stripe>
        <el-table-column prop="lang" label="语言" width="200"></el-table-column>
        <el-table-column v-for="tc in translateCol" :prop="tc.value" :label="tc.name" width="200">
          <template #default="{ row }">
            <el-input v-model="row[tc.value]" placeholder="请输入" />
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="translateVisible = false">放弃</el-button>
          <el-button type="primary" @click="confirmTranslate">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="emojiPickerVisible" width="370">
      <EmojiPicker @insertEmoji="insertEmoji" />
    </el-dialog>
  </el-form>
</template>


<script setup lang="ts">
import { Uploader } from 'src/modules/common/uploader/uploader'
import { CircleCloseFilled, CircleCheckFilled } from '@element-plus/icons-vue'
import { computed, reactive, ref } from 'vue';
import { useNotification } from './use-notification';
import { ElMessage, FormRules } from 'element-plus';
import { SvgIcon } from '@skynet/ui';
import EmojiPicker from '../common/emoji-picker/emoji-picker';
import { useAppAndLangOptions } from '../options/use-app-options';

const { pushConfig, getTranslate, deepLinks, onCreate, form, isUpdating } = useNotification('add-dialog')
const formLabelWidth = '80px';
const translateVisible = ref(false)
const translateLoading = ref(false)
const emojiPickerVisible = ref(false)
const translateContent = ref<Record<string, string>[]>([])
const translateCol = ref<{ name: string, value: string }[]>([])
const translateSource = ref<{ translate: Record<string, string>, type: M.TranslateType }[]>([])
const image1Loading = ref(false)
const image2Loading = ref(false)
const textareaRef = ref<unknown | null>(null)

const { appOptions } = useAppAndLangOptions(() => undefined)

defineExpose({
  submit: async () => {
    console.log(form)
    if (form.value.style_config.has_btn1 && !form.value.translate_list.some((item: { type: number }) => item.type === 4)) {
      ElMessage.error('按钮1文案未翻译')
      return
    }
    if (form.value.style_config.has_btn2 && !form.value.translate_list.some((item: { type: number }) => item.type === 5)) {
      ElMessage.error('按钮2文案未翻译')
      return
    }
    if (form.value.style_config.has_tips && !form.value.translate_list.some((item: { type: number }) => item.type === 3)) {
      ElMessage.error('tip文案未翻译')
      return
    }
    if (!form.value.translate_list.some((item: { type: number }) => item.type === 1)) {
      ElMessage.error('Push标题和Push内容未翻译')
      return
    }
    // if (!['1', '4'].includes(form.value.style_config.push_type) && form.value.style_config.buttonOrProgress === '1' && (!form.value.style_config.has_btn1 || !form.value.style_config.has_btn2)) {
    //   ElMessage.error('按钮文案未填写')
    //   return
    // }
    // if (form.value.style_config.buttonOrProgress === '2' && form.value.style_config.media_progress_type === '2' && !form.value.style_config.media_progress) {
    //   ElMessage.error('进度值未填写')
    //   return
    // }

    // 处理deeplink web url
    let finalDeepLink = form.value.style_config.btn1_deep_link;
    if (finalDeepLink.indexOf('webpage') > -1) {
      if (!form.value.style_config.btn1_deep_link_url) {
        ElMessage.error('请填写按钮1跳转链接')
        return
      }
      finalDeepLink = 'dramawave://dramawave.app/webpage' + '?url=' + encodeURIComponent(form.value.style_config.btn1_deep_link_url)
    } else if (finalDeepLink === '/') {
      finalDeepLink = form.value.style_config.btn1_deep_link_custom + ''
    }
    // 处理btn2 deeplink
    let finalDeepLink2 = form.value.style_config.btn2_deep_link;
    if (finalDeepLink2.indexOf('webpage') > -1) {
      // 坑，切换到纯文本时，不需要deeplink验证
      if (!form.value.style_config.btn2_deep_link_url && form.value.style_config.push_type !== '1') {
        ElMessage.error('请填写按钮2跳转链接')
        return
      }
      finalDeepLink2 = 'dramawave://dramawave.app/webpage' + '?url=' + encodeURIComponent(form.value.style_config.btn2_deep_link_url)
    } else if (finalDeepLink2 === '/') {
      finalDeepLink2 = form.value.style_config.btn2_deep_link_custom + ''
    }

    let image = form.value.style_config.image?.includes('https://') ? form.value.style_config.image?.replace('https://static-v1.mydramawave.com/push/task/image/notify/', '') : form.value.style_config.image
    let small_image = form.value.style_config.small_image?.includes('https://') ? form.value.style_config.small_image?.replace('https://static-v1.mydramawave.com/push/task/image/notify/', '') : form.value.style_config.small_image
    // 纯文本时，不需要图片和deeplink
    if (form.value.style_config.push_type === '1') {
      image = ''
      small_image = ''
      finalDeepLink2 = ''
      form.value.style_config.btn1_text = ''
      form.value.style_config.btn2_text = ''
      form.value.style_config.has_btn1 = false
      form.value.style_config.has_btn2 = false
    }
    // console.log('>>>', (form.value.style_config.media_progress_type === '1' && form.value.style_config.buttonOrProgress === '2') ? ((Math.floor(Math.random() * 100)) || '') + '' : (form.value.style_config.media_progress || '') + '');

    return await onCreate({
      ...form.value,
      target_app_names: form.value.target_app_names_source.join(','),
      end_now_add_hours: Number(form.value.end_now_add_hours),
      end_now_add_minute: Number(form.value.end_now_add_minute),
      audio_types: form.value.resource_scope_type === 0 ? '' : form.value.audio_types_for_show.join(','),
      style_config: {
        ...form.value.style_config,
        deeplink: finalDeepLink,
        btn1_deep_link: finalDeepLink,
        btn2_deep_link: finalDeepLink2,
        image,
        small_image,
        media_progress_type: form.value.style_config.buttonOrProgress !== '2' ? '' : form.value.style_config.media_progress_type,
        media_progress: (form.value.style_config.media_progress_type === '1' && form.value.style_config.buttonOrProgress === '2') ? ((Math.floor(Math.random() * 100)) || '') + '' : (form.value.style_config.media_progress || '') + '',
      } as any,
    })
  }
})

const rules = reactive<FormRules<M.NewCreatePushForm>>({
  'style_config.title': [{ required: true, message: '请输入Push标题', trigger: 'blur' }],
  'style_config.body': [{ required: true, message: '请输入Push内容', trigger: 'blur' }],
  'style_config.push_type': [{ required: true, message: '请选择样式', trigger: 'blur' }],
  'target_app_names_source': [{ required: true, message: '请选择应用', trigger: 'blur' }],
  'target_user_type': [{ required: true, message: '请选择用户范围', trigger: 'blur' }],
  'resource_scope_type': [{ required: true, message: '请选择剧集范围', trigger: 'blur' }],
  'ageing_type': [{ required: true, message: '请选择发送时间', trigger: 'blur' }],
  'timed_start_date_time': [{ required: true, message: '请选择开始时间', trigger: 'blur' }],
  'timed_end_date_time': [{ required: true, message: '请选择结束时间', trigger: 'blur' }],
  'end_now_add_hours': [{ required: true, message: '请输入小时', trigger: 'blur' }],
  'end_now_add_minute': [{ required: true, message: '请输入分钟', trigger: 'blur' }],
  'target_user_val': [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
  'resource_scope_val': [{ required: true, message: '请输入资源ID', trigger: 'blur' }],
  'style_config.small_image': [{ required: true, message: '请上传小图', trigger: 'blur' }],
  'style_config.image': [{ required: true, message: '请上传大图', trigger: 'blur' }],
  'style_config.tips': [{ required: true, message: '请输入tip文案', trigger: 'blur' }],
  'style_config.btn1_text': [{ required: true, message: '请输入按钮1文案', trigger: 'blur' }],
  'style_config.btn2_text': [{ required: true, message: '请输入按钮2文案', trigger: 'blur' }],
  'style_config.btn1_deep_link': [{ required: true, message: '请选择按钮1跳转链接', trigger: 'blur' }],
  'style_config.btn2_deep_link': [{ required: true, message: '请选择按钮2跳转链接', trigger: 'blur' }],
  'style_config.media_progress': [{ required: true, message: '请输入进度值', trigger: 'blur' }],
  'style_config.media_progress_type': [{ required: true, message: '请选择进度值类型', trigger: 'blur' }],
})



const listTypeTip = computed(() => {
  return pushConfig.value?.form.push_type.tips[form.value.style_config.push_type]
})

const titleTip = computed(() => {
  return pushConfig.value?.form.title_input.tips
})
const titleMaxLength = computed(() => {
  return pushConfig.value?.form.title_input.limit.max
})
const contentMaxLength = computed(() => {
  return pushConfig.value?.form.content_input.limit.max
})

const button1MaxLength = computed(() => {
  return pushConfig.value?.form.btn1.limit.max
})

const button2MaxLength = computed(() => {
  return pushConfig.value?.form.btn2.limit.max
})


const getTranslateContent = async (content: string[], type: M.TranslateType[]) => {
  const isSomeEmpty = content.some((item) => item === '')
  if (isSomeEmpty) {
    ElMessage.error('翻译内容不能为空')
    return
  }
  translateVisible.value = true
  translateLoading.value = true
  const task = content.map((item, index) => {
    return getTranslate(item, type[index])
  })
  const res = await Promise.all(task)

  const filteredRes = res.filter((item): item is { type: number, translate: Record<string, string> } => item !== undefined)
  translateSource.value = filteredRes
  setTranslateContent(filteredRes)
  translateLoading.value = false
}

const typeMap = {
  1: { name: 'Push标题', value: 'title' },
  2: { name: 'Push内容', value: 'body' },
  3: { name: 'tips', value: 'tips' },
  4: { name: '按钮1', value: 'btn1_text' },
  5: { name: '按钮2', value: 'btn2_text' }
}
// 将   { type: 1, "translate":{"en":"","ja":"xxx","ko":"xxx"}} 转换为 { lang: 'en', title: 'xxx', body: 'xxx' }
const setTranslateContent = (res: {
  type: number,
  translate: Record<string, string>
}[]) => {
  const translateMap = new Map();
  // 设置表头 { name: 'Push标题', value: 'title' }
  translateCol.value = res.map(item => {
    translateMap.set(typeMap[item.type as keyof typeof typeMap].value, item.translate)
    return { name: typeMap[item.type as keyof typeof typeMap].name, value: typeMap[item.type as keyof typeof typeMap].value }
  })
  // 设置内容 { lang: 'en', title: 'xxx', body: 'xxx' }
  translateContent.value = Object.keys(res[0].translate).map(lang => {
    const obj: Record<string, string> = { lang }
    translateCol.value.map(tc => {
      obj[tc.value] = translateMap.get(tc.value)[lang]
    })
    return obj
  })
  // 设置表头
}

const confirmTranslate = () => {
  if (translateLoading.value) {
    ElMessage.error('正在翻译中，请稍后')
    return
  }
  // 检查newTranslateList中的translate是否有空值
  const hasVoidValue = translateContent.value.some(item => Object.values(item).some(v => !v))
  if (hasVoidValue) {
    ElMessage.error('翻译内容不能为空, 请填写内容')
    return
  }
  // 更新form
  form.value.translate_list = form.value.translate_list.filter((item: { type: number }) => !translateSource.value.some((ts: { type: number }) => ts.type === item.type))

  const newTranslateList = translateSource.value.map(item => {
    return {
      text: form.value.style_config[typeMap[item.type].value as keyof typeof form.value.style_config] || '',
      type: item.type,
      translate: getTranslateFromTable(item.type) // 使用用户可能修改的结果
    }
  })

  form.value.translate_list = form.value.translate_list.concat(newTranslateList)
  translateVisible.value = false
}

const getTranslateFromTable = (type: number) => {
  return translateContent.value.reduce((acc, item) => {
    acc[item.lang] = item[typeMap[type as keyof typeof typeMap].value]
    return acc;
  }, {})
}

const checkIsTranslate = (type: number) => {
  return form.value.translate_list?.some((item: { type: number }) => item.type === type)
}

// 输入框内容变化时，将对应的翻译状态置为未翻译
const turnToUnTranslate = (type: number[]) => {
  form.value.translate_list = form.value.translate_list.filter((item: { type: number }) => !type.includes(item.type))
}

const insertEmoji = (emoji: any) => {
  const textarea = (textareaRef.value as any)?.textarea
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const currentText = form.value.style_config.body || ''

    form.value.style_config.body = currentText.slice(0, start) + emoji.native + currentText.slice(end)

    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + emoji.native.length, start + emoji.native.length)
    }, 0)
  } else {
    form.value.style_config.body = (form.value.style_config.body || '') + emoji.native
  }
  emojiPickerVisible.value = false
}
</script>

<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
