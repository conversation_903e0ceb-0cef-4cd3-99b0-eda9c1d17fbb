<template>
  <x-transaction-history-page class="wallet relative flex size-full flex-col gap-y-2 overflow-y-auto bg-black px-3 text-[#cccacb]">
    <Back 
      :is-white="true" 
      :title="T.walletPage.transactionHistory.ref.value" 
      :is-close-webview="false"
      class="z-100 sticky top-0 flex h-11 w-full shrink-0 items-center bg-black font-bold text-[#fdfbfc]"
    />
    <div
      v-if="(!rechargeList || !rechargeList.items?.length) && !pageLoading"
      class="mt-48% flex flex-1 items-center justify-center"
    >
      <EmptyPage
        :image-size="160"
        :description="T.walletPage.noContentAvailable.ref.value"
      />
    </div>
    <template
      v-for="_ in 8"
      v-if="pageLoading"
      :key="_"
    >
      <van-skeleton>
        <template #template>
          <div class="mb-4 flex w-full items-center justify-between">
            <div class="w-60% flex flex-col">
              <van-skeleton-paragraph
                :round="true"
                row-width="60%"
              />
              <van-skeleton-paragraph
                :round="true"
                row-width="35%"
              />
            </div>
            <div class="w-40% flex flex-col items-end ">
              <van-skeleton-paragraph
                :round="true"
                row-width="20%"
              />
              <van-skeleton-paragraph
                :round="true"
                row-width="40%"
              />
            </div>
          </div>
        </template>
      </van-skeleton>
    </template>
    <div
      v-if="rechargeList && rechargeList.items && rechargeList.items.length"
      class="flex flex-col gap-3 px-3 py-5 text-base text-[#cccacb]"
    >
      <div
        v-for="item in rechargeList.items"
        :key="item.id"
        class="flex justify-between"
      >
        <div>
          <p class="mb-2">
            {{ item.title }}
          </p>
          <p class="text-xs text-[#434546]">
            {{ item.pay_finish_time ? dayjs.unix(item.pay_finish_time).format('YYYY-MM-DD') : 0 }}
          </p>
        </div>
        <div>
          <p
            v-if="item.product_type === 'recharge'"
            class="mb-2 flex justify-end text-sm"
          >
            {{ item.business_data.quanity }}
            <SvgIcon
              name="ic-coins-fill"
              class="ml-1 size-5 shrink-0"
            />
          </p>
          <p
            v-else
            class="mb-2 text-right text-sm"
          >
            {{ item.business_data.period }}
          </p>
          <p
            class="text-xs text-[#434546]"
          >
            {{ $t('walletPage.expiresOn', [formatTime(item.business_data?.vip_expire_time)]) }}
          </p>
        </div>
      </div>
      <p class="mb-3 mt-10 text-center  text-sm text-[#6e7071]">
        {{ T.walletPage.lastLine.ref.value }}
      </p>
    </div>
  </x-transaction-history-page>
</template>
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import {  Skeleton as VanSkeleton, SkeletonParagraph as VanSkeletonParagraph } from 'vant';
import { Back } from 'src/h5_modules/common/back/back'
import {apiRechargeList} from '../wallet-api'
import dayjs from 'dayjs'
import { T } from '../wallet-page.i18n'
import { SvgIcon } from '@skynet/ui'
import EmptyPage from 'src/h5_modules/common/empty-page/empty-page.vue'

const pageLoading = ref(true);
const rechargeList = ref({
  // items: [
  // {
  //     "order_id": "1834185690976690176",
  //     "title": "Top-up",
  //     "amount": 99,                //充值金额
  //     "product_type": "recharge",  //交易类型，recharge-充值，membership-vip订阅
  //     "pay_finish_time": 1719724817,        //交易完成时间戳，单位秒
  //     "order_vip_expired": false,   //订单的vip是否已过期
  //     "business_data": {
  //         "bonus": 10,     //recharge-赠送的金币数
  //         "period": "",    //单位，仅在membership时有用，weekly-周，monthly-月，yearly-年
  //         "quanity": 100,   //recharge-充值的金币数，membership-vip购买period单位的数量
  //         "vip_expire_time": 1719724817,  //会员过期时间戳，单位秒
  //         "product_title":""    //产品标题
  //     },
  //     "txn_amount":0,// 交易金额
  //     "txn_currency":"cny"// 交易货币
  // },
  // {
  //     "order_id": "1834185690976690176",
  //     "title": "Top-up",
  //     "amount": 99,                //充值金额
  //     "product_type": "membership-vip",  //交易类型，recharge-充值，membership-vip订阅
  //     "pay_finish_time": 0,        //交易完成时间戳，单位秒
  //     "order_vip_expired": false,   //订单的vip是否已过期
  //     "business_data": {
  //         "bonus": 10,     //recharge-赠送的金币数
  //         "period": "weekly",    //单位，仅在membership时有用，weekly-周，monthly-月，yearly-年
  //         "quanity": 100,   //recharge-充值的金币数，membership-vip购买period单位的数量
  //         "vip_expire_time": 0,  //会员过期时间戳，单位秒
  //         "product_title":""    //产品标题
  //     },
  //     "txn_amount":0,// 交易金额
  //     "txn_currency":"cny"// 交易货币
  // }
  // ]
})

const formatTime = (time) => {
  return time ? dayjs.unix(time).format('YYYY.MM.DD') : 0 
}

const getRechargeList = async () => {
  const res =  await apiRechargeList()
  pageLoading.value = false;
  if (res.data && res.data) {
    rechargeList.value = res.data
  }
}

onMounted(() => {
  getRechargeList();
})
</script>

<style scoped>
.van-skeleton-paragraph {
  height: 0.8rem;
  background-color: #242526;
}
</style>