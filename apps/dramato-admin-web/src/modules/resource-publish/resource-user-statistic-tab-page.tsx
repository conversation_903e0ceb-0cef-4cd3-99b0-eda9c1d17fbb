/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Button, CreateForm, Icon, Pager, showFailToast, transformNumber, transformTimestamp } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { ElTable, ElTableColumn, ElDatePicker } from 'element-plus'
import { apiGetUserStatistic, apiExportGroupStatistic } from './resource-user-statistic-api'

type ResourceUserTabStatisticOptions = {
  props: {}
}

export const ResourceUserTabStatistic = createComponent<ResourceUserTabStatisticOptions>({
  props: {},
}, props => {
  const loading = ref(false)
  const list = ref<Api.ResourceUserStatistic.List>([])
  const exportLoading = ref(false)
  const defaultParams: Api.ResourceUserStatistic.Param = {
    start_time: 0,
    end_time: 0,
    username: '',
    user_role: 0,
  }
  const form = ref<Api.ResourceUserStatistic.Param>({ ...defaultParams })
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const QueryForm = CreateForm<Api.ResourceUserStatistic.Param>()
  const groupOptions = [
    {
      value: 14,
      label: '资源验收组长',
    },
    {
      value: 15,
      label: '多语言信息校验组长',
    },
    {
      value: 16,
      label: '多语言字幕校验组长',
    },
    {
      value: 17,
      label: '终审发布组长',
    },
    {
      value: 18,
      label: '资源验收组员',
    },
    {
      value: 19,
      label: '多语言信息校验组员',
    },
    {
      value: 20,
      label: '多语言字幕校验组员',
    },
    {
      value: 21,
      label: '终审发布组员',
    },
  ]
  const getList = async () => {
    if (form.value.start_time > form.value.end_time) {
      showFailToast('结束时间必须小于开始时间')
      return
    }
    const params = {
      ...form.value,
      ...pageInfo.value,
      start_time: form.value.start_time || 0,
      end_time: form.value.end_time || 0,
    }
    loading.value = true
    list.value = []
    try {
      const res = await apiGetUserStatistic(params)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const columns = [
    { prop: 'username', label: '人员', minWidth: 120, fixed: true },
    { prop: 'user_role', label: '分组', render: (scope: any) => {
      const roles = scope.row.user_roles ? scope.row.user_roles.split(',').map((userRole: string) => groupOptions.find(g => g.value === +userRole)?.label || '-') : '-'
      return <span>{roles.join(',') || '-'}</span>
    }, minWidth: 200, fixed: true },
    { prop: 'cal_time', label: '时间', minWidth: 180 },
    { prop: 'check_count_first_round', label: '审核量-首发', minWidth: 180 },
    { prop: 'rejected_count_first_round', label: '驳回数-首发', minWidth: 180 },
    { prop: 'pass_count_first_round', label: '通过数-首发', minWidth: 180 },
    { prop: 'check_count_second_round', label: '审核量-二轮', minWidth: 180 },
    { prop: 'rejected_count_second_round', label: '驳回数-二轮', minWidth: 180 },
    { prop: 'pass_count_second_round', label: '通过数-二轮', minWidth: 180 },
    { prop: 'rejected_count', label: '被驳回数', minWidth: 180 },
  ]

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = { ...defaultParams }
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }

  onQuery()

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>人员统计</li>
          </ul>
        ),
        tableActions: () => (
          <div class="flex items-center justify-end">
            <Button class="btn btn-primary btn-sm" disabled={exportLoading.value} onClick={async () => {
              exportLoading.value = true
              const response = await apiExportGroupStatistic({
                ...form.value,
                ...pageInfo.value,
              })
              // 检查响应是否成功
              if (!response.ok) {
                showFailToast('下载失败')
                return
              }
              exportLoading.value = false
              // 获取文件流
              const blob = await response.blob()
              // 检查 Blob 是否有效
              if (blob.size === 0) {
                showFailToast('下载的文件为空')
                return
              }
              const url = window.URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = '各组汇总.csv'
              a.click()
              window.URL.revokeObjectURL(url)
            }}>
              {exportLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              导出
            </Button>
          </div>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              [
                ['开始时间：', 'start_time', {
                  type: 'custom',
                  render: () => {
                    return (
                      <ElDatePicker
                        v-model={form.value.start_time}
                        type="date"
                        placeholder="选择日期"
                      />
                    )
                  },
                }],
                ['结束时间：', 'end_time', {
                  type: 'custom',
                  render: () => {
                    return (
                      <ElDatePicker
                        v-model={form.value.end_time}
                        type="date"
                        placeholder="选择日期"
                      />
                    )
                  },
                }],
              ],
              ['账号：', 'username', { type: 'text', placeholder: '请输入账号名称' }],
              ['分组：', 'user_role', { type: 'select', options: groupOptions, placeholder: '请选择分组' }, { transform: transformNumber }],
            ]}
          />
        ),
        table: () => (
          <ElTable
            data={list.value || []}
            v-loading={loading.value}
          >
            {columns.map(col => {
              if (col.render) {
                return (
                  <ElTableColumn key={col.prop} prop={col.prop} label={col.label} width={col.width} v-slots={{
                    default: ({ row }: { row: any }) => col.render({ row }),
                  }}
                  />
                )
              } else {
                return (
                  <ElTableColumn key={col.prop} prop={col.prop} label={col.label} width={col.width} fixed={col.fixed} />
                )
              }
            })}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ResourceUserTabStatistic
