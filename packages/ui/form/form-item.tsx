import { createComponent, createStringId, fn, mc, required, tryCall } from '@skynet/shared'
import { omit } from 'lodash-es'
import { isVNode } from 'vue'
import { Empty, renderInput } from './form-helper'
import { FormItemObject, FormItemOptions } from './form-types'

export const FormItem = createComponent<FormItemOptions>({
  props: {
    data: required,
    item: required,
    labelClass: '',
    error: null,
    hideEmptyError: false,
  },
  emits: {
    change: fn,
  },
}, (props, { emit }) => {
  const id = createStringId('input')
  return () => {
    if (props.item === null) return null
    if (isVNode(props.item)) return props.item
    const item = Array.isArray(props.item)
      ? { label: props.item[0], path: props.item[1], input: props.item[2], ...omit(props.item[3], ['label', 'path', 'input']) } as FormItemObject
      : props.item
    const label = tryCall(item.label)
    const error = props.error?.[item.path]?.[0]
    const hint = tryCall(item.hint)
    return (
      <x-form-item key={item.path} class={mc('flex flex-col gap-1', item.class)}>
        <x-label class={mc('block whitespace-nowrap', props.labelClass)} for={id}>{label}</x-label>
        {renderInput({ item, id, data: props.data, error: error?.message, onChange: value => emit('change', item.path, value) })}
        {hint && <x-hint class="block text-gray-400 text-xs">{hint}</x-hint>}
        {item.errorVisible === false || (props.hideEmptyError && !error)
          ? null
          : <x-error class="block text-error-6 text-xs truncate">{error?.message ?? <Empty />}</x-error>}
      </x-form-item>
    )
  }
})
