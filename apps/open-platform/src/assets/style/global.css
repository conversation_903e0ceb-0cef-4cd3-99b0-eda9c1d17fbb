@import './reset.css';
@import './vars.css';
@import './components.css';
@import './daisy-modify.css';

@tailwind base;
@tailwind components;
@tailwind utilities;



body {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  background-color: #FAF7FA;
  /* 改为 tailwind 写法 */
  /* @apply antialiased bg-[var(--fill-4)] text-[var(--text-1)]; */
}



body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Arial,
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    sans-serif;
}

html {
  min-height: 100%;
  background-color: var(--fill-4);
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.no-tap-color {
  -webkit-tap-highlight-color: transparent;
  /* iOS Chrome */
  -ms-tap-highlight-color: transparent;
  /* IE */
  -moz-tap-highlight-color: transparent;
  /* Firefox */
  -o-tap-highlight-color: transparent;
  /* Opera */
}


:focus {
  outline: none;
}
