import { bindLoading, createComponent } from '@skynet/shared'
import { Button, Empty, transformInteger, transformNumber } from '@skynet/ui'
import { FormOptions } from '@skynet/ui/form/form-types'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { userEngagementStore } from './user-engagement-store'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'

type userEngagementCreateTargetPageOptions = {
  props: {}
}
export const UserEngagementCreateTargetPage = createComponent<userEngagementCreateTargetPageOptions>({
  props: {},
}, () => {
  const { formData, Form, resetTargetFormData, stepOneError, validateStepOne } = userEngagementStore()
  const route = useRoute()
  const {
    list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()
  const isViewMode = computed(() => route.query.mode === 'view')
  const router = useRouter()
  const onSubmit = () => {
    if (!validateStepOne()) return
    void router.push({ path: './strategy', query: { ...route.query, id: formData.value.id } })
  }

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
  })

  const items = computed(() => [
    [
      [requiredLabel('策略组名称'), 'name', { type: 'text', disabled: isViewMode.value }, { class: 'w-[20em]' }],
      formData.value.id && ['策略组id', 'id', { type: 'text', disabled: true }, { class: 'shrink-0' }],
    ],
    [
      [requiredLabel('是否AB实验'), 'is_abtest', {
        type: 'radio', options: [
          { label: '是', value: 1, disabled: isViewMode.value },
          { label: '否', value: 0, disabled: isViewMode.value },
        ],
      }],
    ],

    <h2 class="col-span-2 mb-4 text-lg font-bold"> 目标用户群 </h2>,
    [
      [<span>用户设备 <small class="ml-2 text-gray-400">配置用户画像时，需保证用户设备与此选项一致</small></span>, 'platform', {
        type: 'radio',
        options: [
          { label: '全部', value: 0, disabled: isViewMode.value },
          { label: 'iOS', value: 1, disabled: isViewMode.value },
          { label: 'Android', value: 2, disabled: isViewMode.value },
        ],
      }, { errorVisible: false }],
      (formData.value.is_abtest === 0
        ? ([
            '分层画像',
            'strategy_layer_ids',
            {
              type: 'multi-select',
              search: true,
              popoverWrapperClass: 'z-popover-in-dialog',
              options: list.value.map(n => {
                return { value: n.id, label: `${n.id}/${n.name}` }
              }),
              class: 'w-[400px]',
            },
          ])
        : null),
    ],
    <h2 class="col-span-2 text-lg font-bold"> 任务计划 </h2>,
    [
      [
        requiredLabel(<span>任务优先级<small class="pl-1 text-gray-500">数字越大越高</small></span>),
        'priority',
        {
          type: 'number',
          disabled: isViewMode.value,
        },
        { transform: transformInteger },
      ],
      [
        requiredLabel('策略持续时间'),
        'duration',
        {
          type: 'number',
          placeholder: '填 -1 表示不限',
          suffix: '天',
          min: -1,
          disabled: isViewMode.value,
        },
        { transform: transformNumber, class: 'w-[12em]' },
      ],
    ],
    <h2 class="col-span-2 text-lg font-bold"> 策略范围 </h2>,
    [
      'flex flex-col gap-4',
      ['', 'send_pro_user',
        { type: 'checkbox', label: '下发给符合条件的用户', disabled: isViewMode.value },
        {
          class: 'shrink-0', errorVisible: false, transform: [
            (raw: number) => {
              return raw ? true : false
            },
            (display: boolean) => {
              return display ? 1 : 0
            },
          ],
        },
      ],
    ],
  ] as FormOptions<FormData>['props']['items'])

  const formError = computed(() => {
    return {
      ...stepOneError.value,
    }
  })
  return () => (
    <div>
      <Form class="flex flex-col gap-4 p-8"
        data={formData.value as M.UserEngagement.Save.Request}
        items={items.value}
        error={formError.value}
        onSubmit={onSubmit}
        onReset={resetTargetFormData}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        actions={() => (
          <div class="flex justify-between gap-x-2">
            {isViewMode.value ? <Empty /> : <Button class="btn btn-sm" type="reset">重置</Button>}
            <Button class="btn btn-primary btn-sm" type="submit">下一步</Button>
          </div>
        )}
        actionClass="col-span-2"
      />
    </div>
  )
})
export default UserEngagementCreateTargetPage
