import { defineConfig } from 'unocss'
import presetWind, { Theme } from '@unocss/preset-wind'
import { skynetUnoPreset } from '@skynet/preset/uno'
import { breakpoints } from './src/lib/constants'
// @ts-expect-error never mind
import tailwindConfig from './delete-tailwind.config.js'

export default defineConfig({
  presets: [
    // @ts-expect-error never mind
    presetWind({
      dark: {
        light: '',
        dark: '.dark',
      },
    }), // 兼容 tailwind
    skynetUnoPreset,
  ],

  content: {
    filesystem: [
      'src/**/*.{html,js,ts,jsx,tsx,vue}',
      '../../packages/ui/**/*.{html,js,ts,jsx,tsx,vue}',
    ],
  },
  extendTheme: (theme: Theme) => {
    return {
      ...theme,
      maxWidth: {
        ...theme.maxWidth,
        pad: 'var(--pad-page-max-width)',
        pc: 'var(--pc-page-max-width)',
      },
      maxHeight: {
        ...theme.maxHeight,
        'safe-screen': 'calc(100vh - var(--vh-offset,0px))',
      },
      height: {
        ...theme.height,
        'top-bar': 'var(--top-bar-height)',
        'safe-screen': 'calc(100vh - var(--vh-offset,0px))',
      },
      backgroundColor: {
        ...theme.backgroundColor,
        'fill-1': 'var(--fill-1)',
        'fill-2': 'var(--fill-2)',
        'fill-3': 'var(--fill-3)',
        'fill-4': 'var(--fill-4)',
        'fill-5': 'var(--fill-5)',
        'fill-6': 'var(--fill-6)',
        'fill-7': 'var(--fill-7)',
        'fill-8': 'var(--fill-8)',
      },
      spacing: {
        'safe-bottom': 'env(safe-area-inset-bottom)',
      },
    }
  },
  theme: {
    breakpoints: {
      // phone: breakpoints.phone.min + 'px', // phone 没有意义，因为所有样式默认都是优先适配手机
      pad: breakpoints.pad.min + 'px',
      pc: breakpoints.pc.min + 'px',
    },
    colors: {
      ...tailwindConfig.theme.extend.colors, // 复用 tailwind 颜色
      primary: '#0a66c2',
    },
    zIndex: {
      // 局部
      up: '1',
      'up-up': '2',
      // 全局
      footer: '64',
      dialog: '128',
      'series-menu': '129',
      popover: '1024',
      shareButton: '2048',
      mask: '2560',
      toast: '5120',
    },
  },
  rules: [
  ],
  preflights: [
    {
      getCSS: () => `
        :root {
          font-size: calc(16 * 100vw / 375);
        }
        @media screen and (min-width: 768px) {
          :root {
            font-size: 16px;
          }
        }
      `,
    },
  ],
})
