import { ClassName, createComponent, fn, mc, SlotFn, tryCall } from '@skynet/shared'
import { MergeClass } from '../ui'
type TabOptions = {
  props: {
    items?: Array<[string, SlotFn]>
    modelValue?: string
    itemClass?: string | ((key: string) => ClassName)
    tag?: string
  }
  emits: {
    'update:modelValue': (value: string) => void
  }
}
export const Tab = createComponent<TabOptions>({
  props: {
    items: [],
    modelValue: '',
    itemClass: '',
    tag: 'div',
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const onSelect = (key: string) => {
    emit('update:modelValue', key)
  }
  return () => (
    <MergeClass baseClass="flex flex-nowrap" role="tablist" tag={props.tag}>
      {props.items.map(([key, render], index) => (
        <x-tab-item role="tab" key={key} onClick={() => onSelect(key)}
          class={
            mc('block', key === props.modelValue ? 'cursor-default' : 'cursor-pointer', tryCall(props.itemClass, key === props.modelValue))
          }
        >{render(key === props.modelValue)}
        </x-tab-item>
      ))}
    </MergeClass>
  )
})

export default Tab
