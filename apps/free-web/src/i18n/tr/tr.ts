export const tr = {
  // 通用
  common: {
    cancel: '<PERSON>pta<PERSON>',
  },
  // 分享页
  share: {
    watchNow: '<PERSON><PERSON><PERSON> İzle',
    episodeDialog: {
      title: 'Bana katıl ve ekstra jeton kazan!',
      description: 'Bu diziye bayıldım!',
      tips: 'Tav<PERSON><PERSON>',
      button: '<PERSON>\'ı İzle',
      limit24: 'Sınırlı süre: 24h',
    },
    toast: {
      takeOffShelf: 'İçerik Mevcut Değil',
    },
  },
  'customer-service-center': {
    title: 'Müşteri Hizmetleri Merkezi',
    description: 'Açıklama',
    'description-placeholder': 'So<PERSON><PERSON> anlamamıza yardımcı olacak ayrıntıları sağlayın',
    'email-placeholder': 'Geri bildirim güncellemeleri almak için e-postanızı girin',
    'submit-feedback': 'Gönder',
    'enter-feedback': '<PERSON>ğer sorunlar geri bildirimi',
    'upload-pictures': '<PERSON><PERSON><PERSON><PERSON> yükle',
    feedback: '<PERSON><PERSON> bildirim',
    faq: {
      question1: 'Neden \'Reklamlar Henüz Hazır Değil\' diyor?',
      answer1: 'Bazen dengesiz sunucu veya reklamveren bağlantıları sorunlara neden olabilir. Daha sonra tekrar denemenizi ve “Reklamları İzle” seçeneğine çok sık tıklamamanızı öneririz. Üçüncü taraf politikaları veya ülkenizin kısıtlamaları nedeniyle reklam sınırları geçerli olabilir. Reklam izleme deneyiminizi geliştirmek için reklam sağlayıcılarımızla sürekli çalışıyoruz.',
      question2: 'Reklamlar için neden \'Ağ Kullanılamıyor\' diyor?',
      answer2: 'Lütfen uygulamayı tamamen kapatıp yeniden açarak yeniden başlatmayı deneyin. Reklamlar hala yüklenmiyorsa, tekrar denemeden önce bir süre bekleyin ve çok sık tıklamaktan kaçının. Sorun devam ederse, Geri Bildirim sistemimiz aracılığıyla bize ulaşabilirsiniz. Ayrıca, görüntüleme deneyimini geliştirmek için reklam ekibimizle birlikte çalışıyoruz. Sabrınız için teşekkür ederiz.',
    },
  },
  toast: {
    'upload-failed': 'Yükleme başarısız, lütfen tekrar yükleyin',
    'submit-success': 'Başarıyla gönderildi',
    'email-incorrect': 'E-posta biçimi yanlış',
  },
  not_found: {
    title: 'Aradığınız sayfa bulunamadı',
  },
  regulations: {
    howUseData: 'Verilerinizi Nasıl Kullanıyoruz',
    content: `Uygulama İzleme Şeffaflığı izin isteğinde "İzin Ver" seçeneğini belirlerseniz, bilgilerinizi ve verilerinizi hizmetlerimizi optimize etmek ve size en iyi deneyimi sunmak için kullanacağız, aksi takdirde sadece verileri toplayacağız ve herhangi bir izleme faaliyeti yürütmeyeceğiz.

DramaWave ve ortaklarımızın ve tedarikçilerimizin cihazınızdaki verileri kullanarak uygulamayı nasıl kullandığınızı analiz etmesine, içerik önerilerini kişiselleştirmesine ve daha fazla bilgiyle ilgili hizmetler sunmasına izin vermeyi kabul ediyorsanız, devam etmek için lütfen "Kabul Et ve Devam Et"e tıklayın.`,
    acceptAll: 'Hepsini Kabul Et',
    dataSettings: 'Veri Ayarları',
    next: 'Sonraki',
    confirm: 'Onayla',
    aboutYourPrivacy: 'Gizliliğiniz Hakkında',
    personalDataName: 'Kişiselleştirilmiş Veriler',
    personalDataDescription: 'Bu bilgileri, seçimlerinizi hatırlamak ve kişiselleştirilmiş reklamlar ve içerik önerileri, bildirimler ve hatırlatmalar sağlamak için kullanıyoruz.',
    advertisementDataName: 'Reklam Verileri',
    advertisementDataDescription: 'Çevrimiçi reklam amaçları için cihazınızda çerezler, cihazınızın tanımlayıcıları veya diğer bilgiler saklanabilir veya erişilebilir. Kullanıcı verileri ayrıca reklam ortaklarımızla paylaşılabilir.',
    behaviorsDataName: 'Davranış Verileri',
    behaviorsDataDescription: 'Kullanım, davranış ve reklam performansını ölçmek ve analiz etmek için çerezler veya cihaz tanımlayıcıları gibi analizle ilgili veriler saklanabilir.',
    privacyAndTerms: 'Gizlilik Politikası ve Hizmet Şartları',
    referToPrivacy: 'Lütfen Gizlilik Politikasına bakın',
    referToTerms: 'Lütfen Hizmet Şartlarına bakın',
    agreeAndSave: 'Kabul Et ve Kaydet',
  },
  coins: {
    earnings: {
      title: 'Kazançlarım',
      total: 'Toplam Çip',
      exchange: 'Değiştir',
      earningsHistory: 'Kazanç Geçmişi',
      exchangeHistory: 'Değişim Geçmişi',
      noRecords: 'Kayıt yok',
      last50earnings: 'Sadece son 50 kazanç gösterilmektedir',
      last50exchanges: 'Sadece son 50 değişim kaydı gösterilmektedir',
      benefits: 'Faydalar',
      gotIt: 'Tamam',
    },
    exchange: {
      title: 'Değişim Alanı',
      notOpen: 'Henüz açılmadı, lütfen beklemede kalın',
      receiveBtn: 'Mutlu Bir Şekilde Alın',
      earnCoins: 'Çip Kazan',
      redeemRequiredXCoins: 'Geri ödeme için {X} çip gerekli',
      exchangeSuccess: 'Başarıyla Değiştirildi',
      cardId: 'KART ID',
      exchangeFailed: 'Değişim Başarısız',
      transferFailedInsufficientStock: 'Stokta yok. Lütfen yarın erken gelin.',
      gotItBtn: 'Tamam',
      transferFailedRiskControl: 'Hesapta anormallik var, geçici olarak değişim yapılamıyor.',
      tryAgainBtn: 'Sonra Tekrar Deneyin',
      transferConfirm: 'Transfer Onayı',
      paypalAccountEmail: 'Paypal Hesap e-posta adresi',
      confirmPaypalAccountEmail: 'Paypal Hesap e-posta adresini onaylayın',
      transferX: '{X} Transferi',
      transferXConfirm: '{X} Transferi Onayı',
      transferToYourAccountX: 'Hesabınıza {X} kadar transfer',
      confirmBtn: 'Onayla',
      transferSuccessTitle: 'Parayı Paypal hesabınıza gönderiyoruz!',
      transferSuccessContentX: 'Paranız 5 gün içinde {X} hesabınıza ulaşacak',
      copySuccess: 'Başarı Kopyalandı',
      enterEmailError: 'Lütfen geçerli bir e-posta adresi girin',
      enterEmailValidError: 'Giriş hatası, lütfen yeniden girin',
      emailNotMatchError: 'E-posta adresi eşleşmiyor, lütfen yeniden girin',
    },
  },
  my_wallet: {
    title: 'Benim Cüzdanım',
    help_dialog: {
      title: 'Bilet Kullanım Kuralları',
      content1: `1. 1 Satın Alınan Bilet = 1 Satın Alınan Ödül Bileti = 1 AD Ödül Bileti = 1 Çip Değişim Bileti – Bunların tümü bölümlerin kilidini açmak için kullanılabilir.`,
      content2: `2. Tüm biletler, kalıcı izleme hakları sağlar, ancak Purchased Reward Ticket belirli bir süre sonra sona erecektir.`,
      content3: `3. Satın alınan Biletler doğrudan satın alma yoluyla edinilebilir.`,
      content4: `4. Satın alınan Ödül Biletleri, yalnızca satın alma yoluyla belirli promosyon dönemlerinde elde edilebilir.`,
      content5: `5. AD Ödül Biletleri, reklam görüntüleme görevlerini tamamlayarak kazanılabilir.`,
      content6: `6. Çip Değişim Biletleri, madeni para alışverişi yapılarak elde edilebilir.`,
    },
    account_balance: 'Hesap Bakiyesi',
    purchased_tickets: 'Satın Alınan Biletler',
    purchase_reward_tickets: 'Satın Alınan Ödül Biletleri',
    coin_exchange_tickets: 'Çip Değişim Biletleri',
    ad_reward_tickets: 'AD Ödül Biletleri',
    top_up: 'Yükle',
    purchase_history: 'Satın Alma Geçmişi',
    rewards_history: 'Ödül Geçmişi',
    exchange_history: 'Borsa Geçmişi',
    consumption_records: 'Tüketim Kayıtları',
    auto_unlock: 'Bölümler Otomatik Kilit Açma',
  },
  top_up: {
    exchange_title: 'Çip Değişimi',
    purchase_title: 'Satın al',
    tickets: 'Bilet',
    coins: 'Çipler',
    balance: 'Bakiye',
    title: 'Mağaza',
    tips: 'İpuçları',
    tips_1: '1. Platformumuz hem ücretsiz kısa dramalar hem de premium erken erişim dramaları sunmaktadır.',
    tips_2: '2. Erken erişim içeriğini önceden izlemek için bu içeriği Tickets ile kullanmanız gerekir. Bu Biletler satın alma, çip değişimi veya reklam izleyerek elde edilebilir.',
    tips_3: '3. Tüm biletler, kalıcı izleme hakları sağlar, ancak Purchased Reward Ticket belirli bir süre sonra sona erecektir.',
    tips_4: '4. Satın alınan Ödül Biletleri, bölümlerin kilidi açılırken ilk olarak kullanılacaktır. Bakiye yetersizse, normal biletleriniz otomatik olarak kullanılacaktır.',
    tips_5: '5. Yükleme ödemeniz başarılı olduysa ancak bakiyeniz güncellenmediyse, lütfen cüzdanınızı yenilemeyi deneyin.',
    tips_6: '6. Diğer sorularınız için "Profil" -> "Müşteri Hizmetleri Merkezi" -> "Geri Bildirim" aracılığıyla bizimle iletişime geçin.',
    exchange_dialog_title: 'Değiştirmek istediğinizden emin misiniz?',
    redeem_required: 'Gerekli kurtarmak',
    exchange_success: 'xxx bilet başarıyla değiştirildi',
    balance_not_enough: 'Yetersiz bakiye',
    exchange_failed: 'Değişim başarısız',
  },
  my_wallet_in: {
    transaction_history: 'İşlem Geçmişi',
    EPnum: 'BÖLÜM.{num}',
    Expired: 'Son kullanma tarihi',
    AD: 'Reklam',
    Expires_on_num: '{num} tarihinde sona eriyor',
    Thats_all_for_now: 'Şimdilik bu kadar.',
    consumption_records: 'Tüketim Kayıtları',
  },
  meal_check_in: {
    sleeping_link: 'Earn Coins While Sleeping',
    rules_title: 'Aktivite Kuralları',
    benefits_title: 'Benefits',
    rules_1: '1. Her gün, yemeklerinizi kaydetmek için dört zaman dilimi vardır: Kahvaltı: 07:00-10:00; Öğle Yemeği: 11:00-14:00; Akşam Yemeği: 17:00-20:00; Gece Atıştırması: 21:00-24:00. ',
    rules_2: '2. İlgili kaydetme zamanını kaçırırsanız, ödülü almak için bir video izleyebilirsiniz. ',
    rules_3: '3. Ödüller her gün gece yarısı sıfırlanır, bu yüzden ödüllerinizi unutmadan alın.',
  },
  sleep_check_in: {
    earn_link: 'Yemekleri Kaydederek Kazanın',
    benefits_title: 'Benefits',
    rules_1: '1. Her akşam 19:00 ile 24:00 arasında \'Uyuyarak Kazan\' modunu etkinleştirebilirsiniz.',
    rules_2: '2. Modu etkinleştirdikten sonra, ertesi gün, 8 saat uyuduktan sonra, ödülünüzü sabah 8:00 ile 12:00 arasında alabilirsiniz.',
    rules_3: '3. Ödül alma zamanını kaçırırsanız, ertesi gün öğle 12:00 ile akşam 7:00 arasında bir video izleyerek ödülünüzü yeniden alabilirsiniz.',
  },
}
