import { Ref, ref, shallowRef, UnwrapRef } from 'vue'
type Options<D, E = unknown> = {
  shallow?: boolean
  finally?: (data: D | null, error: E | null, loading: boolean) => void
}
export const usePromise = <D, E = unknown>(promise: Promise<D>, options: Options<D, E> = {}) => {
  const { shallow = false } = options
  const data = shallow ? shallowRef<D | null>(null) : ref<D | null>(null)
  const error = shallow ? shallowRef<E | null>(null) : ref<E | null>(null)
  const loading = ref(true)
  promise.then(v => {
    data.value = v as UnwrapRef<D>
  }, e => {
    error.value = e
  }).finally(() => {
    loading.value = false
    options.finally?.(data.value, error.value, loading.value)
  })
  return [data, error, loading] as const
}

export function bindLoading<D>(promise: Promise<D>, loading: Ref<boolean>, delay?: number): Promise<D>
export function bindLoading<D>(loading: Ref<boolean>, promise: Promise<D>, delay?: number): Promise<D>
export function bindLoading<D>(
  first: Promise<D> | Ref<boolean>,
  second: Ref<boolean> | Promise<D>,
  delay = 0,
): Promise<D> {
  let promise: Promise<D>
  let loading: Ref<boolean>

  if (first && first instanceof Promise) {
    promise = first
    loading = second as Ref<boolean>
  } else {
    loading = first
    promise = second as Promise<D>
  }

  loading.value = true
  void promise.finally(() => {
    if (delay) {
      setTimeout(() => {
        loading.value = false
      }, delay)
    } else {
      loading.value = false
    }
  })
  return promise
}
export const when = (getValue: () => unknown | Promise<unknown>, timeout = 2000) => {
  return new Promise((resolve, reject) => {
    const timer = window.setInterval(async () => {
      const value = await getValue()
      if (value) {
        window.clearInterval(timer)
        resolve(value)
      }
    }, 200)
    window.setTimeout(() => {
      window.clearInterval(timer)
      reject()
    }, timeout)
  })
}

/**
 * Polyfill for Promise.allSettled
 * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/allSettled
 */
export function polyfillForPromiseAllSettled() {
  if (typeof Promise.allSettled !== 'function') {
    Promise.allSettled = <T extends unknown>(promises: Array<Promise<T>>) =>
      Promise.all(
        promises.map(p =>
          p
            .then(value => ({ status: 'fulfilled', value }))
            .catch(reason => ({ status: 'rejected', reason })),
        ),
      ) as Promise<Array<{ status: 'fulfilled', value: T } | { status: 'rejected', reason: unknown }>>
  }
}
