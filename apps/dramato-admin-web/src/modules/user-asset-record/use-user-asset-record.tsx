import { CreateForm, CreateTableOld } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetUserAssetOps } from './user-asset-record-api'

export const useUserAssetRecord = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    opsCompensationDetail,
    opsTransferDetail,
  }
}

const Form = CreateForm<M.UserAssetRecord.Params>()
const params = ref<M.UserAssetRecord.Params>({ op_type: 'all' })
const opsCompensationDetail = ref<M.UserAssetRecord.OpsCompensationDetail>()
const opsTransferDetail = ref<M.UserAssetRecord.OpsTransferDetail>()
const Table = CreateTableOld<M.UserAssetRecord.Item>()
const list = ref<M.UserAssetRecord.Item[]>([])
const loading = ref<boolean>(false)
const page = ref<number>(1)
const pageSize = ref<number>(20)
const total = ref<number>(1)

const search = async () => {
  loading.value = true
  const res = await apiGetUserAssetOps({
    ...params.value,
    page_index: page.value,
    page_size: pageSize.value })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.items || []
  total.value = res.data?.total || 0
}
