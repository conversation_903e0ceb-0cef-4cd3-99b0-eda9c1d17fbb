import { createComponent, mc } from '@skynet/shared'
import { Button, openDialog, Tooltip } from '@skynet/ui'
import { onClickOutside } from '@vueuse/core'
import router from 'src/router'
import { onMounted, ref } from 'vue'
import { RouterLink } from 'vue-router'
import { useLoginStore } from '../login/use-login-store'
import { useUploadMaterialStore } from '../material-upload/use-upload-material-store'
type TopBarOptions = {
  props: {}
}
export const TopBar = createComponent<TopBarOptions>({
  props: {},
}, props => {
  const isTestDomain = window.location.hostname.includes('test') || window.location.hostname.match(/^(\d|\.)+&/) || window.location.hostname === 'localhost'
  const { logout, userInfo } = useLoginStore()
  const { uploadMap } = useUploadMaterialStore()
  const login = () => {
    void router.push({
      path: '/login',
      query: {
        returnTo: window.location.pathname + window.location.search,
      },
    })
  }

  const showFlag = ref<boolean>(false)
  const uploadListRef = ref<HTMLDivElement>()

  onMounted(() => {
    onClickOutside(uploadListRef, () => {
      showFlag.value = false
    })
  })

  return () => (
    <x-top-bar class="py-2 px-4 flex items-center justify-start h-full">

      <RouterLink to="/">
        <h2 class="text-2xl">Dramato 短剧管理后台</h2>
      </RouterLink>
      <section class="flex flex-nowrap gap-2 items-center ml-auto">
        {
          Array.from(uploadMap.value.entries()).length > 0 && (
            <div class="text-gray-500 w-[100px] text-center relative cursor-pointer" onClick={() => showFlag.value = true}>
              待上传素材
              {
                showFlag.value
                && (
                  <x-uploading-list ref={uploadListRef} class="flex absolute bottom-0 left-0 flex-col gap-2 w-full h-[300px] overflow-y-auto bg-white shadow-lg text-sm rounded-md translate-y-full p-3">
                    {
                      Array.from(uploadMap.value.entries()).map(item => {
                        return (
                          <div class="flex items-center justify-center">{item[1].materialId}:
                            <x-upload-status class={mc('', item[1].uploadStatus === 2 ? 'text-green-500' : item[1].uploadStatus === 3 ? 'text-red-500' : 'text-orange-500')}>{['', '上传中', '上传成功', '上传失败'][item[1].uploadStatus]}</x-upload-status>
                          </div>
                        )
                      })
                    }
                  </x-uploading-list>
                )
              }
            </div>
          )
        }
        <x-extra class="divide-x gap-4 flex justify-center items-center">
          <Tooltip popContent={() => <img class="w-[350px]" src="https://static-v1.mydramawave.com/frontend_static/manage/20250605/group.webp" />} placement="bottom">
            <div class="text-gray-500">问题反馈</div>
          </Tooltip>
          {isTestDomain ? <div class="text-red-500">测试环境</div> : <div class="text-green-500">生产环境</div>}
          <div>
            {userInfo?.value
              ? (
                  <Button onClick={() => {
                    const hideDeleteDialog = openDialog({
                      title: '提示',
                      mainClass: 'pb-0 px-5',
                      customClass: '!w-[400px]',
                      body: (
                        <x-login-out-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <x-login-out-body>确认登出</x-login-out-body>
                          <x-login-out-footer class="flex w-full justify-end gap-x-[10px]">
                            <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                            <button class="btn btn-primary btn-sm" onClick={() => {
                              logout()
                              hideDeleteDialog()
                            }}
                            >确定
                            </button>
                          </x-login-out-footer>
                        </x-login-out-confirm-dialog>
                      ),
                    })
                  }} class="btn btn-sm btn-outline">登出</Button>
                )
              : <Button onClick={login} class="btn btn-sm btn-outline">登录</Button>}
          </div>
        </x-extra>
      </section>
    </x-top-bar>
  )
})
