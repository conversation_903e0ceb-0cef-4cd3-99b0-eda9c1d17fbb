import { createI18n } from 'vue-i18n'
import zhCN from './zh-CN'
import enUS from './en-US'

const messages = {
  'zh-CN': zhCN,
  'en-US': enUS,
}

export const i18n = createI18n({
  legacy: false, // 使用 Composition API 模式
  locale: navigator.language.indexOf('zh') > -1 ? 'zh-CN' : 'en-US', // 'zh-CN', // 默认语言
  fallbackLocale: 'en-US', // 回退语言
  messages,
  numberFormats: {
    'zh-CN': {
      currency: {
        style: 'currency',
        currency: 'CNY',
      },
    },
    'en-US': {
      currency: {
        style: 'currency',
        currency: 'USD',
      },
    },
  },
})

// 语言切换函数
export const setLocale = (locale: 'zh-CN' | 'en-US') => {
  i18n.global.locale.value = locale
  localStorage.setItem('locale', locale)
}

export const getLocale = () => {
  return i18n.global.locale.value
}
// 初始化语言
const savedLocale = localStorage.getItem('locale') as 'zh-CN' | 'en-US'
if (savedLocale) {
  setLocale(savedLocale)
}
