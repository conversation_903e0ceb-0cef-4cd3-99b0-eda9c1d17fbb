import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, openDialog, SvgIcon, transformInteger } from '@skynet/ui'
import { stopPropagation } from '@skynet/ui/utils'
import { ElMessage } from 'element-plus'
import { set } from 'lodash-es'
import router from 'src/router'
import { computed, onMounted, ref } from 'vue'
import { RouterLink, useRoute } from 'vue-router'
import { z } from 'zod'
import { Uploader } from '../common/uploader/uploader'
import { useLabelList } from '../label/use-label-list'
import { coverImg } from './images/play-images'
import { usePlayItem } from './use-play-item'
import LabelSelect from './label-select'
import { useI18n } from 'vue-i18n'
export const EditPlayPage = createComponent(null, () => {
  const { t } = useI18n()
  const Form = CreateForm<M.Play>()
  const route = useRoute()
  const id = computed(() => route.params.id as string)
  const { formData, save, fetchItem, submitForAudit, resetFormData } = usePlayItem(id.value)
  const onSave = async () => {
    if (!validateAll()) {
      ElMessage.error(t('pleaseCheckFormContent'))
      return Promise.reject()
    }
    return save()
  }
  const onClickAudit = async () => {
    await onSave()
    await submitForAudit()
    void router.push('/play')
  }

  const onClickUpload = () => {
    // @ts-expect-error Vue defineComponent 不支持设置 expose 类型
    uploadRef.value?.openFileDialog()
  }

  const { labelList, fetchLabelList } = useLabelList()
  onMounted(() => {
    void fetchLabelList()
    if (id.value) {
      void fetchItem()
    } else {
      resetFormData()
    }
  })
  const uploadRef = ref<InstanceType<typeof Uploader>>()
  const rules = z.object({
    title: z.string().min(1, { message: t('pleaseEnterTheName') }).max(50, { message: t('titleCanOnlyBeUpTo50Words') }),
    label_ids: z.array(z.number()).min(1, { message: t('pleaseSelectAWorkTag') }),
    count: z.number().min(1, { message: t('pleaseEnterAnEstimatedNumberOfEpisodes') }).max(1000, { message: t('upTo1000Episodes') }),
    description: z.string().min(50, { message: t('enterScriptSummary') }).max(500, { message: t('scriptSummaryCanOnlyBeUpTo500Words') }),
    play_path: z.string().min(1, { message: t('pleaseUploadTheScript') }),
  })
  const { error, validateAll } = useValidator(formData, rules)

  const onSelectLabel = () => {
    const closeDialog = openDialog({
      title: t('worksLabel'),
      body: () => <LabelSelect modelValue={formData.value.label_ids} onClose={closeDialog} onUpdate:modelValue={v => set(formData.value, 'label_ids', v)} />,
    })
  }
  return () => (
    <x-edit-play-page class="block flex-1 overflow-auto">
      <header class="relative flex items-center gap-2 text-2xl font-medium leading-[34px] text-[var(--text-1)]">
        <RouterLink to="/play" class="">
          <SvgIcon name="ic-chevron-down" />
        </RouterLink>
        <h2>{t('myShortPlayScript')}</h2>
      </header>
      <main class="mt-10">
        <Form
          class="cp-form !grid grid-cols-[120px_auto] !gap-x-[60px] grid-areas-['image_detail''action_action'] [&_x-label]:w-[84px]"
          hideEmptyError onChange={(path, value) => set(formData.value, path, value)} onSubmit={onSave} error={error.value} data={formData.value}
          items={[
            ['', 'play_path', {
              type: 'custom', render: ({ onInput, item, value }) => {
                const v = value as string
                return (
                  <>
                    <Uploader ref={uploadRef} accept="pdf,doc,docx" maxsize={1024 * 1024 * 100} ossKeyType="resource"
                      showFileList={false}
                      class="h-[164px] w-[123px] cursor-pointer overflow-hidden rounded-md border border-dashed border-[#CCCACB]"
                      onUploadSuccess={d => {
                        onInput(d.oss_static_prefix + d.key!)
                      }}
                    >
                      {
                        v ? (
                          <div class="relative h-full">
                            <img src={coverImg} class="size-full object-cover" />
                            <a target="_blank" rel="noopener noreferrer" href={v}
                              class="absolute inset-0 z-up flex h-full flex-col items-center justify-center gap-1 bg-[#0B080B80] text-xs text-white"
                              onClick={stopPropagation}>
                              <SvgIcon name="ic-eye" noColor />
                              {t('viewTheScript')}
                            </a>
                          </div>
                        ) : (
                          <div class="flex h-[164px] w-[123px] shrink-0 items-center justify-center bg-fill-1 text-sm text-[#A1A0A3]">
                            {t('uploadNewScript')}
                          </div>
                        )
                      }
                    </Uploader>
                    <div class="py-4 text-center"><Button class="btn btn-info btn-sm" onClick={onClickUpload}>{t('uploadNewScript')}</Button></div>
                  </>
                )
              },
            }, { class: 'grid-in-[image]' }],
            [
              'flex flex-col gap-2 p-0 grid-in-[detail] gap-y-6',
              [t('titleOfThePlay'), 'title', {
                type: 'text', class: 'leading-[22px] py-2 px-4 pr-16 bg-fill-1 rounded-lg',
                placeholder: t('enterTitle'), suffix: () => (
                  <div class="absolute right-4 text-[var(--text-4)]">
                    <span class="text-[var(--text-3)]">{formData.value.title?.length || 0}</span>/50
                  </div>
                ),
                maxlength: 50,
                wrapperClass: 'relative w-full grow',
              }, { class: 'cp-form-item' }],
              [t('label'), 'label_ids', {
                type: 'custom',
                render: ({ onInput }) => (
                  <x-tag-select
                    class="flex h-[38px] w-full cursor-pointer items-center rounded bg-fill-1 px-4 py-[11px] leading-4"
                    onClick={onSelectLabel}>
                    {formData.value.label_ids?.[0] ? (
                      <div class="flex h-full flex-wrap gap-2 overflow-hidden truncate">{
                        formData.value.label_ids.map(labelId => (
                          <x-tag key={labelId} class="whitespace-nowrap">
                            {labelList.value.find(label => label.label_id === labelId)?.content}
                          </x-tag>
                        ))
                      }</div>
                    ) : (
                      <span> {t('selectTags')} </span>
                    )}
                    <SvgIcon name="ic-chevron-down" class="ml-auto shrink-0 -rotate-90" />
                  </x-tag-select>
                ),
              }, { class: 'cp-form-item' }],
              [t('estimatedEpisodes'), 'count', {
                type: 'number', class: 'leading-[22px] py-2 px-4 bg-fill-1 rounded-lg w-[10em]',
                placeholder: t('enterEstimatedEpisodes'),
                wrapperClass: 'relative w-full grow',
              }, { class: 'cp-form-item', transform: transformInteger }],
              [t('introduction'), 'description', {
                type: 'textarea', class: 'leading-[22px] py-2 px-4 bg-fill-1 rounded-lg min-h-[200px] hide-scrollbar',
                placeholder: t('enterScriptSummary'),
                maxlength: 500,
                wrapperClass: ' w-full grow relative',
                suffix: () => (
                  <div class="absolute right-0 top-full flex justify-end px-4 py-2 text-[var(--text-4)]">
                    <span class="text-[var(--text-3)]">{formData.value.description?.length || 0}</span>/500
                  </div>
                ),
              }, { class: 'cp-form-item' }],
            ],
          ]}
          actionClass="grid-in-[action] [&_x-label]:hidden pt-10"
          actions={() => (
            <div class="space-x-5">
              <RouterLink to="/play">
                <Button type="button" class="btn btn-secondary px-10">{t('cancel')}</Button>
              </RouterLink>
              <Button type="submit" class="btn btn-ghost px-10">{t('save')}</Button>
              <Button type="button" class="btn px-10" onClick={onClickAudit}>{t('submitForReview')}</Button>
            </div>
          )} />

      </main>
    </x-edit-play-page>
  )
})

export default EditPlayPage
