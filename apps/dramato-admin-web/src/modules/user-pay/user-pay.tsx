import { createComponent } from '@skynet/shared'
import { onMounted } from 'vue'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { DateTime, Pager, transformNumber, Button, openDialog } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { useUserPay } from './use-user-pay'
import { SupplementaryOrderForm } from './supplementary-order-form'
import { App } from 'src/app'
import { AppleOrderPop } from './apple-order-pop'
type UserPayOptions = {
  props: {}
}
const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[600px] overflow-hidden'

export const UserPay = createComponent<UserPayOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    closeOrderPopupModal,
  } = useUserPay()

  onMounted(() => {
    void search(1)
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>购买记录</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { }
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              ['用户ID', 'user_id', { type: 'number' }, { transform: transformNumber }],
              ['UID', 'uid', { type: 'number' }, { transform: transformNumber }],
              {
                label: '订单类型',
                path: 'pay_type',
                input: {
                  type: 'select',
                  options: [{
                    label: 'Google',
                    value: 'Google',
                  }, {
                    label: 'Apple',
                    value: 'Apple',
                  }],
                  autoInsertEmptyOption: false,
                },
              },
              ['用户支付订单号', 'out_txn_id', { type: 'text' }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2" />
            <x-hide-when-in-dialog>
              <Button class="btn btn-primary btn-sm mr-2" onClick={
                () => {
                  closeOrderPopupModal.value = openDialog({
                    title: '苹果订单查询',
                    body: () => <AppleOrderPop />,
                    mainClass: dialogMainClass,
                    customClass: '!w-[600px] overflow-hidden',
                  })
                }
              }
              >苹果订单查询
              </Button>
              <Button class="btn btn-primary btn-sm" onClick={
                () => {
                  closeOrderPopupModal.value = openDialog({
                    title: '创建补单',
                    body: () => <SupplementaryOrderForm />,
                    mainClass: dialogMainClass,
                    customClass: '!w-[600px] overflow-hidden',
                  })
                }
              }
              >补单
              </Button>
            </x-hide-when-in-dialog>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['日期/时间', row => (<DateTime value={(row?.pay_finish_time || 0) * 1000} />), { class: 'w-[150px]' }],
            ['Order ID', 'order_id', { class: 'w-[250px]' }],
            ['用户ID', 'user_id', { class: 'w-[150px]' }],
            ['UID', 'uid', { class: 'w-[100px]' }],
            ['订单状态', 'status', { class: 'w-[100px]' }],
            ['充值金额（美元）', r => r.pay_amount / 100, { class: 'w-[100px]' }],
            ['兑换金额', r => r.delivery_details.quanity, { class: 'w-[100px]' }],
            ['会员权益', r => r.delivery_details.period, { class: 'w-[100px]' }],
            ['充值平台', 'platform', { class: 'w-[100px]' }],
            ['订单号', 'out_txn_id', { class: 'w-[200px]' }],
            ['原单号', 'original_txn_id', { class: 'w-[200px]' }],
          ]}
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})
