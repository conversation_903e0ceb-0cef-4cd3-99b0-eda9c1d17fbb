import { ClassName, OptionsToProps } from '@skynet/shared'
import { onMounted, onUnmounted, ref, shallowRef, watch } from 'vue'
import { Popover2Options } from './popover-2'
import { usePopover2Store } from './use-popover-2-store'

export const usePopover2 = (options: OptionsToProps<Popover2Options> & { class?: ClassName }) => {
  const { triggerType = 'hover' } = options
  const { setPopoverVisible, addPopover, removePopover, updatePopover, findPopover } = usePopover2Store()
  const id = ref<string>()

  watch([options.triggerElement, id], ([trigger]) => {
    if (!trigger) return
    if (!id.value) return
    if (triggerType === 'hover') {
      trigger.addEventListener('mouseenter', () => {
        show()
      })
      trigger.addEventListener('mouseleave', () => {
        hideWithDelay()
      })
      updatePopover(id.value, {
        onMouseEnter: () => {
          cancelHide()
        },
        onMouseLeave: () => {
          hideWithDelay()
        },
      })
    } else if (triggerType === 'click') {
      trigger.addEventListener('click', (event) => {
        // 防止点击事件传播到document
        event.stopPropagation()
        toggle()
        if (!id.value) return
        // 添加点击外部处理逻辑
        const clickOutsideHandler = (event: MouseEvent) => {
          const popover = findPopover(id.value as string)
          if (!popover || !popover.visible) return
          // 检查点击是否在popover外部
          const target = event.target as Node
          const triggerEl = options.triggerElement()
          // 获取popover内容元素
          const popoverElement = document.querySelector(`.v-popover-${id.value}`)
          // 如果点击在触发元素或内容元素上，不关闭popover

          if ((triggerEl && triggerEl.contains(target)) ||
              (popoverElement && popoverElement.contains(target)) || (popoverElement && popoverElement?.contains(target))) {
            return
          }
          toggle()
        }
        // 添加document点击事件监听
        document.addEventListener('click', clickOutsideHandler)
        // 清理点击事件监听
        updatePopover(id.value, {
          onRemove: () => {
            document.removeEventListener('click', clickOutsideHandler)
          }
        })
      })
    } else if (triggerType === 'focus') {
      trigger.addEventListener('focus', () => {
        show()
      })
      trigger.addEventListener('blur', () => {
        hideWithDelay()
      })
    }
  })

  onMounted(() => {
    id.value = addPopover({
      ...options,
      onRemove: hide,
    })
  })

  const show = () => {
    cancelHide()
    if (!id.value) return
    setPopoverVisible(id.value, true)
  }
  const hide = () => {
    if (!id.value) return
    setPopoverVisible(id.value, false)
  }

  const delayTimer = shallowRef<null | number>(null)

  const hideWithDelay = (delay = 200) => {
    if (!id.value) return
    if (delayTimer.value) {
      window.clearTimeout(delayTimer.value)
      delayTimer.value = null
    }
    delayTimer.value = setTimeout(() => {
      setPopoverVisible(id.value as string, false)
      delayTimer.value = null
    }, delay)
  }

  const destroy = () => {
    if (!id.value) return
    removePopover(id.value)
    id.value = undefined
  }

  const toggle = () => {
    const popover = findPopover(id.value)
    if (!popover) return
    popover.visible = !popover.visible
  }

  const cancelHide = () => {
    if (delayTimer.value) {
      window.clearTimeout(delayTimer.value)
      delayTimer.value = null
    }
  }

  onUnmounted(() => {
    destroy()
  })

  return { show, hide, toggle, destroy }
}
