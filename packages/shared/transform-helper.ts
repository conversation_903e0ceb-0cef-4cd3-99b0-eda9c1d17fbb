/**
 * 去除字符串两端的指定字符
 * @example trimIt([',', ' '])(str)
 */
export const trimIt = (pad: string[] = [' ']) => (str: string) => str.replace(new RegExp(`^[${pad.join('')}]+|[${pad.join('')}]+$`, 'g'), '')

/**
 * 将字符串按指定分隔符分割
 * @example splitIt([',', ' '])(str)
 */
export const splitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`))

/**
 * 过滤数组
 * @example filterIt(Boolean)(arr)
 */
export const filterIt = (fn: (item: any) => boolean) => (arr: string[]) => arr.filter(fn)
