/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { lang<PERSON>ey, langValue } from 'src/modules/resource/constant'
import { useResourceDetailStore } from './use-multiple-language-subtitle-store'
import { M3u8Player } from '../resource/components/m3u8-player'
import Subtitle from '../common/subtitle/subtitle1'
import { Button, Checkbox, Icon, openDialog, showFailToast, showSuccessToast, SvgIcon, Tooltip } from '@skynet/ui'
import { apiSaveSubtitleContent, apiGetSubtitleSyllableCount } from '../role-mark/role-mark-api'
import { getSubtitleContent } from 'src/modules/common/subtitle/until'
import { apiUpdateOtherLanguageTerminology, apiGetTransTerminologyList, apiSubtitleSync, apiUpdateMark, apiCommitSubtitleCheckLog, apiGetSubtitleErrorsDetail } from 'src/modules/resource-publish/resource-publish-api'
import dayjs from 'dayjs'
import { ElTooltip } from 'element-plus'
import TermTable from 'src/modules/resource-publish/components/term-table'
import { trim } from 'lodash-es'
import { matchSubtitleErrors } from 'src/modules/common/subtitle/until'
import Editor from '../resource/components/editor'
import { formatSubtitles } from 'src/modules/resource/util'

type MultipleLanguageSubtitlePageOptions = {
  props: {}
}
export const MultipleLanguageSubtitlePage = createComponent<MultipleLanguageSubtitlePageOptions>({
  props: {},
}, props => {
  const route = useRoute()
  const router = useRouter()
  const { defaultLanguageCode, setSeriesId, getRealSubtitle, getList, list, loading, getDetail, currentResource, getCharacters, getSubtitleRecordList, subtitleLogs, subtitleErrorList, getSubtitleErrors } = useResourceDetailStore()
  setSeriesId(+route.params.id)

  // 如果不存在 默认用英语
  const original_language = route.query.language || 'en'
  const reference_language = route.query.ref || defaultLanguageCode.value || 'en'
  // 如果不存在 默认第一集
  const serial_number = ref<number>(+(route.query.number || 1))
  const langIndex = langKey.findIndex(r => r === original_language)
  const refIndex = langKey.findIndex(r => r === reference_language)
  const btnLoading = ref(false)
  const resourceLoading = ref(false)
  const isInit = ref(false)
  // 当前选中行
  const currentRow = ref<M.IResourceDrama>({})
  const origin_syllable_list = ref<Api.VoiceoverResource.ISyllable[]>([])
  const shorten_syllable_list = ref<Api.VoiceoverResource.ISyllable[]>([])
  // 剧集原语言
  const referenceLangIndex = ref(refIndex)
  const videoRef = ref()
  const currentTime = ref(0)
  // 右侧展示语言
  const originalLangIndex = ref(langIndex)
  // 左侧
  const originalRef = ref()
  const referenceRef = ref()

  const isAsync = ref(true)

  const lineNotMatch = ref(false)

  const videoType = ref<'pure_path' | 'origin_path'>('pure_path')
  const videoUrl = ref('')
  const hasChecked = new Map()
  // 备注相关
  const tooltipRef = ref<any>(null)
  const remark = ref('')
  const textarea = ref('')
  // 操作日志
  const oprObj = ref<M.IAlterSubtitle | null>()

  const errDetail = ref<M.IResourceSubtitleDetailError[]>([])
  const isReplacing = ref(false)

  const reference = ref<{
    code: string
    language: string
  }>({
    code: '',
    language: '',
  })
  const original = ref<{
    code: string
    language: string
  }>({
    language: langKey[langIndex],
    code: '',
  })
  const subtitles = ref<{ language: string, type: string, subtitle: string }[]>([])
  const termList = ref<M.ITerminology[]>([])

  // 左侧语言、右侧语言及技术发生变化
  const setRouter = (params: { [Record: string]: string | number }) => {
    const query = {
      ...route.query,
      ...params,
    }
    void router.replace({
      path: location.pathname,
      query,
    })
  }

  const isFinalPage = computed(() => {
    return !route.fullPath.includes('/resource-publish/multi-subtitle/detail')
  })

  watch(() => [currentRow.value?.serial_number, originalLangIndex.value], async (newVal, oldVal) => {
    resourceLoading.value = true
    currentTime.value = 0
    lineNotMatch.value = false
    errDetail.value = []
    shorten_syllable_list.value = []
    origin_syllable_list.value = []
    if (currentRow.value?.serial_number) {
      setRouter({
        number: currentRow.value?.serial_number,
      })
    }
    if (newVal[1] !== oldVal[1]) {
      setRouter({
        language: langKey[newVal[1]],
      })
    }
    videoUrl.value = currentRow.value[videoType.value]
    if (referenceLangIndex.value < 0 && originalLangIndex.value < 0) return

    // 并行请求所有数据
    const promiseList = [
      getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[referenceLangIndex.value]}_subtitle_path`])),
      getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_subtitle_path`])),
    ] as Promise<any>[]

    const isErrorSubtitle = subtitleErrorList.value?.find(item => item.language_code === langKey[originalLangIndex.value] && item.serial_number === currentRow.value.serial_number)

    // 如果有错误字幕，添加到并行请求列表
    if (isErrorSubtitle && langKey[originalLangIndex.value]) {
      promiseList.push(
        apiGetSubtitleErrorsDetail({
          series_resource_id: +route.params.id,
          serial_number: currentRow.value.serial_number,
          language_code: langKey[originalLangIndex.value],
        }),
      )
    }

    // 添加术语列表请求到并行请求列表
    promiseList.push(
      getTermList(langKey[originalLangIndex.value], currentRow.value.serial_number),
    )

    // 并行执行所有请求
    const results = await Promise.all(promiseList)

    // 解构结果
    const [code1, code2] = results

    // 处理错误详情结果
    if (isErrorSubtitle && langKey[originalLangIndex.value]) {
      const errorDetailRes = results[2]
      errDetail.value = errorDetailRes.data?.errs || []
    } else {
      errDetail.value = []
    }

    // 术语列表请求已经在 getTermList 函数中处理了状态更新

    // 左侧对照永远选择 中文
    reference.value = {
      code: code1,
      language: langKey[referenceLangIndex.value],
    }
    original.value = {
      language: langKey[originalLangIndex.value],
      code: code2,
    }
    subtitles.value = [
      {
        language: original.value.language,
        type: 'normal',
        subtitle: getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_subtitle_path`]),
      },
    ]
    oprObj.value = subtitleLogs.value.find(r => r.serial_number === currentRow.value.serial_number && r.language_code === langKey[originalLangIndex.value]) || null
    textarea.value = remark.value = currentResource.value?.list?.find(item => item.serial_number === currentRow.value.serial_number)?.remark || ''
    // 可选的角色 suggestion
    resourceLoading.value = false

    if (!isFinalPage.value) {
      void checkSubtitle({
        id: +route.params.id,
        type: 1,
        language_code: langKey[originalLangIndex.value],
        serial_number: currentRow.value?.serial_number,
      })
    }
  })

  watch(() => videoRef.value, newVal => {
    if (newVal) {
      videoRef.value.on('timeupdate', () => {
        if (!resourceLoading.value) currentTime.value = videoRef.value.getCurrentTime() || 0
      })
    }
  })

  watch(() => videoType.value, () => {
    videoUrl.value = currentRow.value[`${videoType.value}`]
  })

  watch(() => referenceLangIndex.value, async () => {
    resourceLoading.value = true
    lineNotMatch.value = false
    const code = await getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[referenceLangIndex.value]}_subtitle_path`]))
    reference.value = {
      code,
      language: langKey[referenceLangIndex.value],
    }
    resourceLoading.value = false
  })

  // watch(() => originalLangIndex.value, async () => {
  //   resourceLoading.value = true
  //   lineNotMatch.value = false
  //   const code = await getSubtitleContent(getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_subtitle_path`]))
  //   original.value = {
  //     code,
  //     language: langKey[originalLangIndex.value],
  //   }
  //   resourceLoading.value = false
  // }, {
  //   immediate: true,
  // })

  const getTermList = async (language: string, num: number) => {
    if (!language) return
    try {
      const res = await apiGetTransTerminologyList({
        series_resource_id: +route.params.id,
        language_code: language,
        serial_number: num,
      })
      termList.value = res.data?.term_list || []
      shorten_syllable_list.value = res.data?.shorten_syllable_list || []
      origin_syllable_list.value = res.data?.origin_syllable_list || []
      isReplacing.value = res.data?.replacing === 1
      if (isReplacing.value) {
        showFailToast('术语替换中，当前语言字幕暂时无法操作')
      }
    } catch (error: any) {
      console.error('获取术语列表失败:', error)
      // 避免在错误处理中修改响应式状态导致递归更新
    }
  }

  const getFormatSubtitle = (list: Api.RoleMark.ISubtitleLine[]) => {
    return list.map(item => {
      return `${item.order}\n${item.time}\n${item.content}\n`
    }).join('\n')
  }

  const checkSubtitle = async (data: M.ISubtitleCheck) => {
    if (hasChecked.get(`${data.language_code}_${data.serial_number}`)) {
      return
    }
    try {
      const res = await apiCommitSubtitleCheckLog(data)
      if (res.code === 200) {
        hasChecked.set(`${data.language_code}_${data.serial_number}`, false)
      }
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    }
  }

  const onSaveSubtitle = async (cb?: () => {}) => {
    const _list = originalRef.value.getContent() as Api.RoleMark.ISubtitleLine[]
    const referenceList = referenceRef.value.getContent() as Api.RoleMark.ISubtitleLine[]

    const list = _list.map((item, index) => {
      item.order = index + 1
      return item
    })

    const arr = list.filter(item => !item.content || !item.time)
    if (arr.length > 0) {
      showFailToast(`字幕第${arr.map(item => item.order).join(',')}请完成字幕内容及时间的填写`)
      return
    }

    if (_list.length !== referenceList.length) {
      lineNotMatch.value = true
    } else {
      lineNotMatch.value = false
    }

    const formatCode = getFormatSubtitle(list)
    btnLoading.value = true

    try {
      const res = await apiSaveSubtitleContent({
        serial_number: currentRow.value?.serial_number,
        series_resource_id: +route.params.id,
        language_code: original.value.language,
        content: formatCode,
        audio_type: 0,
        alter_type: 1,
      })
      currentRow.value[`${original.value.language}_subtitle_path`] = res.data?.subtitle_uri || ''
      subtitles.value = [
        {
          language: original.value.language,
          type: 'normal',
          subtitle: res.data?.subtitle_uri || '',
        },
      ]
      const diffList: { index: number, text: string }[] = []
      for (let i = 0; i < list.length; i++) {
        const iItem = list[i]
        const iContent = iItem['content']
        const shortCont = (iItem?.errs || []).find(err => err.canReplace)?.shorten_content || ''
        const oldContent = iItem?.oldContent || ''
        if (iContent !== shortCont && iContent !== oldContent) {
          diffList.push({
            index: iItem.order!,
            text: iContent,
          })
        }
      }

      try {
        const res2 = await apiGetSubtitleSyllableCount({
          series_resource_id: +route.params.id,
          serial_number: currentRow.value?.serial_number,
          language: original.value.language,
          texts: diffList,
          no_ai_horizontal: true
        })
        showSuccessToast('保存成功')
        if (res2.data?.series_resource_id === +route.params.id && res2.data?.serial_number === currentRow.value?.serial_number && res2.data?.language === original.value.language) {
          const newDiffList = res2.data?.texts
          for (let j = 0; j < newDiffList.length; j++) {
            for (let m = 0; m < list.length; m++) {
              if (list[m].order === newDiffList[j].index && list[m].content === newDiffList[j].text) {
                for (let k = 0; k < shorten_syllable_list.value.length; k++) {
                  if (shorten_syllable_list.value[k].line_number === newDiffList[j].index) {
                    shorten_syllable_list.value[k].content = newDiffList[j].text
                    shorten_syllable_list.value[k].content_syllable_count = newDiffList[j].syllable_count
                    break
                  }
                }
                break
              }
            }
          }
        }
        const code2 = await getSubtitleContent(getRealSubtitle(currentRow.value[`${original.value.language}_subtitle_path`]))
        original.value.code = code2
      } catch (error: any) {
        console.log(error)
      }
      cb && cb()
    } catch (error: any) {
      showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }

  const init = async () => {
    isInit.value = false
    void getDetail()
    void getSubtitleRecordList()
    void getSubtitleErrors()
    await getList()
    referenceLangIndex.value = langKey.findIndex(key => key === reference_language)
    const obj = list.value.find(item => item.serial_number === serial_number.value)
    if (obj) currentRow.value = obj

    reference.value = {
      code: '',
      language: langKey[referenceLangIndex.value],
    }

    original.value = {
      language: langKey[originalLangIndex.value],
      code: '',
    }

    subtitles.value = [
      {
        language: original.value.language,
        type: 'normal',
        subtitle: getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_subtitle_path`]),
      },
    ]
    isInit.value = true
  }

  void init()

  return () => (
    <x-resource-detail-page class="block">
      {
        isInit.value ? (
          <div class="px-4 flex flex-col h-[100vh] overflow-hidden max-w-[1220px] mx-auto">
            <x-nav class="py-4 flex items-center space-x-4">
              <a class="btn btn-sm md:btn-md gap-2 lg:gap-3" href={`/resource-publish/multi-subtitle/detail/${+route.params.id}`}>
                <svg class="h-6 w-6 fill-current md:h-8 md:w-8 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                  <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z" />
                </svg>
                <div class="flex flex-col items-start gap-0.5 leading-[1.1]">
                  <span>返回</span>
                </div>
              </a>
              {currentResource.value?.title ? (
                <>
                  <div>资源名称：<span class="text-[var(--text-1)] font-bold">{currentResource.value?.title}</span></div>
                  <div>资源ID：<span class="text-[var(--text-1)] font-bold">{currentResource.value?.id}</span></div>
                  <div>原视频语言：<span class="text-[var(--text-1)] font-bold">{langValue[langKey.findIndex(key => currentResource.value?.upload_subtitle_language_code && key === currentResource.value?.upload_subtitle_language_code[0])]}</span></div>
                  <div>
                    {errDetail.value && errDetail.value.length > 0 ? (
                      <ElTooltip placement="bottom" v-slots={{
                        content: () => <pre class="whitespace-pre-wrap break-all">{matchSubtitleErrors(original.value.code, errDetail.value, false).map(item => `第${item.subtitleIndex}行：${item.err_content}`).join('\n')}</pre>,
                      }}>
                        <div class="flex items-center space-x-1">
                          <span class="text-xs text-[var(--text-2)]">异常</span>
                          <SvgIcon class="size-3" name="ic_exclamation" />
                        </div>
                      </ElTooltip>
                    ) : null}
                  </div>
                  <div>
                    {remark.value ? (
                      <Tooltip placement="bottom-start" popWrapperClass="z-popover-in-dialog max-w-[300px]" popContent={() => (
                        <pre class="whitespace-pre-wrap break-words">备注：{remark.value}</pre>
                      )}>
                        <div class="line-clamp-1 w-[400px] flex-1 text-sm text-[var(--text-2)]">
                          备注：{remark.value}
                        </div>
                      </Tooltip>
                    ) : null}
                  </div>
                  <span class="text-red-500 text-sm">{lineNotMatch.value ? '字幕行数对不上' : ''}</span>
                </>
              ) : null}
            </x-nav>
            <div class="flex flex-1 flex-row overflow-hidden space-x-2">
              <x-video-area class="block">
                { videoUrl.value
                  ? (
                      <M3u8Player
                        subtitles={subtitles.value}
                        currentLanguage={langKey[originalLangIndex.value]}
                        url={getRealSubtitle(videoUrl.value)}
                        onPlayerReady={(e: any) => {
                          videoRef.value = e
                        }}
                      />
                    )
                  : <div class="flex h-[550px] w-[309px] items-center justify-center text-[var(--text-2)]">暂无视频</div> }
              </x-video-area>
              <x-subtitle-area v-loading={resourceLoading.value} class="flex h-full min-w-[820px] flex-1 relative overflow-hidden">
                <div class="flex-1">
                  <div class="flex flex-row justify-between items-center px-4 h-[30px]">
                    <select value={reference.value.language} class="select select-bordered select-xs w-[100px]" onChange={(e: any) => {
                      const value = e.target.value as string
                      referenceLangIndex.value = langKey.findIndex(key => key === value)
                      setRouter({
                        ref: value,
                      })
                    }}
                    >
                      {
                        langKey.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
                      }
                    </select>
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-xs" onClick={() => {
                        const _list = originalRef.value.getContent() as Api.RoleMark.ISubtitleLine[]
                        const list = _list.map((item, index) => {
                          item.order = index + 1
                          return item
                        })

                        const formatCode = getFormatSubtitle(list)
                        const code = ref(formatCode)
                        const btnLoading = ref(false)
                        const hideDialog = openDialog({
                          mainClass: 'pb-0 px-5',
                          customClass: '!w-[600px]',
                          title: '字幕覆盖',
                          body: () => (
                            <div>
                              <Editor
                                customClass="w-full h-[550px]"
                                code={code.value}
                                options={{
                                  language: 'plaintext',
                                  formatOnPaste: false,
                                  tabSize: 2,
                                  inDiffEditor: false,
                                  minimap: {
                                    enabled: false,
                                  } }}
                                onChange={e => {
                                  code.value = e
                                }}
                              />
                              <div class="flex justify-end space-x-4">
                                <Button class="btn-default btn btn-sm" onClick={() => {
                                  hideDialog()
                                }}>取消</Button>
                                <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} loading={btnLoading.value} onClick={async () => {
                                  btnLoading.value = true
                                  try {
                                    const content = formatSubtitles(code.value)
                                    const res = await apiSaveSubtitleContent({
                                      serial_number: currentRow.value?.serial_number,
                                      series_resource_id: +route.params.id,
                                      language_code: original.value.language,
                                      content,
                                      audio_type: 0,
                                      alter_type: 1,
                                    })
                                    currentRow.value[`${original.value.language}_subtitle_path`] = res.data?.subtitle_uri || ''
                                    original.value.code = content
                                    showSuccessToast('保存成功')
                                    hideDialog()
                                  } catch (error: any) {
                                    showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                                  } finally {
                                    btnLoading.value = false
                                  }
                                }}
                                >
                                  {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                                  保存
                                </Button>
                              </div>
                            </div>
                          ),
                        })
                      }}>字幕覆盖</Button>
                      <select value={original.value.language} class="select select-bordered select-xs w-[100px]" onChange={(e: any) => {
                        const value = e.target.value as string
                        originalLangIndex.value = langKey.findIndex(key => key === value)
                        setRouter({
                          language: value,
                        })
                      }}
                      >
                        {
                          langKey.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
                        }
                      </select>
                    </div>
                  </div>
                  <div class="flex flex-row h-full overflow-auto">
                    <x-reference class="flex-1">
                      <Subtitle
                        ref={referenceRef}
                        class="h-full"
                        code={reference.value.code}
                        onVideoProgress={(time: number) => {
                          if (videoRef.value) videoRef.value?.seek(time)
                        }}
                        showCharacters={false}
                        showContentText={false}
                        rolePick={false}
                        isEditable={false}
                        subtitleHeightFixed={true}
                        rtl={reference.value.language === 'ar'}
                        syllable_list={origin_syllable_list.value}
                      />
                    </x-reference>
                    <x-original class="flex-1">
                      <Subtitle
                        ref={originalRef}
                        class="h-full"
                        code={original.value.code}
                        currentTime={currentTime.value}
                        onVideoProgress={(time: number) => {
                          if (videoRef.value) videoRef.value?.seek(time)
                        }}
                        errors={errDetail.value}
                        showCharacters={false}
                        characters={termList.value}
                        annotations={[]}
                        isEditable={true}
                        rolePick={false}
                        showContentText={false}
                        isAsyncScroll={isAsync.value}
                        subtitleHeightFixed={true}
                        highlightKey="translation"
                        syllable_list={shorten_syllable_list.value}
                        rtl={original.value.language === 'ar'}
                      />
                    </x-original>
                  </div>
                </div>
                <div class="w-[220px] flex flex-col h-full sticky top-0">
                  <TermTable
                    v-loading={resourceLoading.value}
                    height="100%"
                    class="h-full flex-1 overflow-y-auto"
                    list={termList.value}
                    isTrans={defaultLanguageCode.value !== original.value.language}
                    language={original.value.language}
                    onChange={({ item }) => {
                      const index = termList.value.findIndex(obj => obj.term_id === item.term_id)
                      termList.value.splice(index, 1, {
                        ...item,
                      })
                    }}
                  />
                  <div class="mt-4 h-[30px] flex justify-end space-x-2">
                    {
                      defaultLanguageCode.value !== original.value.language ? (
                        <Button class="btn btn-primary btn-xs" disabled={btnLoading.value || isReplacing.value} onClick={async () => {
                          btnLoading.value = true
                          try {
                            if (termList.value.filter(item => item.delete === 0 || item.delete === 1).length > 0) {
                              await apiUpdateOtherLanguageTerminology({
                                series_resource_id: +route.params.id,
                                terms: termList.value.filter(item => item.delete === 0 || item.delete === 1).map(item => {
                                  item.name = trim(item.name)
                                  item.official_name = trim(item.official_name)
                                  item.translation = trim(item.translation)
                                  return item
                                }),
                              })
                            }
                            showSuccessToast('保存成功')
                            setTimeout(() => {
                              location.reload()
                            }, 1000)
                          } catch (error: any) {
                            showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                          } finally {
                            btnLoading.value = false
                          }
                        }}>
                          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                          保存术语
                        </Button>
                      ) : null
                    }
                  </div>
                </div>
              </x-subtitle-area>
            </div>
            <x-subtitle-operation class="block">
              <div class="py-4 h-[64px] flex items-center w-full justify-between pt-4">
                <div class="flex">
                  <Button class="btn btn-outline btn-xs" disabled={(currentRow.value?.serial_number) === (list.value[0].serial_number) || resourceLoading.value || btnLoading.value || isReplacing.value} onClick={() => {
                    const index = list.value.findIndex(item => item.serial_number === currentRow.value?.serial_number)
                    currentRow.value = list.value[index - 1]
                    subtitles.value = []
                  }}
                  >上一集
                  </Button>
                  <div class="px-4">{ currentRow.value?.serial_number }</div>
                  <Button class="btn btn-outline btn-xs" disabled={list.value[list.value.length - 1].serial_number === currentRow.value?.serial_number || resourceLoading.value || btnLoading.value || isReplacing.value} onClick={() => {
                    const index = list.value.findIndex(item => item.serial_number === currentRow.value?.serial_number)
                    currentRow.value = list.value[index + 1]
                    subtitles.value = []
                  }}
                  >下一集
                  </Button>
                  <select value={videoType.value} class="select select-bordered select-xs ml-4 w-[100px]" onChange={(e: any) => {
                    const value = e.target.value
                    videoType.value = value
                  }}
                  >
                    <option value="pure_path">无字幕视频</option>
                    <option value="origin_path">原视频</option>
                  </select>
                  <label class="flex items-center ml-4 cursor-pointer">
                    <Checkbox
                      label=""
                      modelValue={isAsync.value}
                      onUpdate:modelValue={(value: boolean) => {
                        isAsync.value = value
                      }}
                    />
                    <span>同步滚动</span>
                  </label>
                  <Tooltip
                    ref={tooltipRef}
                    triggerType="click"
                    popWrapperClass="z-popover-in-dialog w-[460px] p-4"
                    popContent={() => (
                      <div class="flex flex-col gap-y-4">
                        <textarea value={textarea.value} class="textarea textarea-bordered textarea-sm h-[200px] w-full" placeholder="请输入备注" onChange={e => {
                          textarea.value = (e.target as HTMLTextAreaElement).value
                        }} />
                        <div class="flex justify-end gap-x-2">
                          <Button class="btn btn-ghost btn-sm" onClick={() => {
                            tooltipRef.value.toggle()
                          }}>取消</Button>
                          <Button class="btn btn-primary btn-sm " disabled={btnLoading.value} onClick={async () => {
                            btnLoading.value = true
                            try {
                              await apiUpdateMark({
                                series_resource_id: +route.params.id,
                                serial_number: +currentRow.value.serial_number,
                                content: textarea.value || '',
                              })
                              remark.value = textarea.value
                              showSuccessToast('操作成功')
                              const itemIndex = currentResource.value?.list?.findIndex(item => item.serial_number === currentRow.value.serial_number) as number
                              currentResource.value?.list?.splice(itemIndex, 1, {
                                ...currentResource.value?.list[itemIndex],
                                remark: textarea.value,
                              })
                              tooltipRef.value.toggle()
                            } catch (error: any) {
                              showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                            } finally {
                              btnLoading.value = false
                            }
                          }}>
                            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            保存
                          </Button>
                        </div>

                      </div>
                    )}>
                    <Button class="btn btn-link btn-xs">备注</Button>
                  </Tooltip>
                  <div class="ml-4">
                    { oprObj.value ? `${oprObj.value.operate_user}:${dayjs(oprObj.value.operate_time * 1000).format('YYYY-MM-DD HH:mm')}` : null }
                  </div>
                </div>
                <div class="space-x-4">
                  <Button class="btn btn-primary btn-sm" disabled={btnLoading.value || resourceLoading.value || isReplacing.value} onClick={() => {
                    void onSaveSubtitle(async () => {
                      try {
                        await apiSubtitleSync({
                          language_code: langKey[originalLangIndex.value],
                          serial_number: currentRow.value.serial_number,
                          id: +route.params.id,
                        })
                      } catch (error: any) {
                        showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
                      } finally {
                        btnLoading.value = false
                      }
                    })
                  }}>
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    提交到线上
                  </Button>
                  <Button class="btn btn-primary btn-sm" disabled={btnLoading.value || resourceLoading.value || isReplacing.value} onClick={() => {
                    void onSaveSubtitle()
                  }}>
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    保存字幕
                  </Button>
                </div>
              </div>
            </x-subtitle-operation>
          </div>
        ) : (
          <div class="w-[100vw] h-[100vh] flex items-center justify-center">
            <Icon name="line-md:loading-twotone-loop" class="size-5" />
            <div class="text-lg text-[var(--text-1)]">
              加载中……
            </div>
          </div>
        )
      }
    </x-resource-detail-page>
  )
})

export default MultipleLanguageSubtitlePage
