/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Button, Icon, Pager, transformNumber } from '@skynet/ui'
import { onMounted, ref } from 'vue'
import { set } from 'lodash-es'
import { useResourceIncome } from './use-resource-income'
import { ElTable, ElTableColumn, ElDatePicker } from 'element-plus'
import dayjs from 'dayjs'

type ResourceIncomePageOptions = {
  props: {}
}
export const ResourceIncomePage = createComponent<ResourceIncomePageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, onReset, Form, params, columns, total, onPageChange, onPageSizeChange, updateTime, handleExport, isExporting, defaultParams } = useResourceIncome()
  const tableRef = ref<InstanceType<typeof ElTable>>()

  const disabledDate = (time: Date) => {
    // 最晚可以选到 上个月
    return dayjs(time).isAfter(dayjs().subtract(1, 'month'))
  }

  onMounted(() => {
    void onQuery()
  })

  return () => (
    <x-competition-content-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>收入分账汇总</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={() => onQuery()}
            onReset={() => {
              tableRef.value?.clearSort()
              onReset()
            }}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['资源ID', 'res_id', { type: 'text', placeholder: '请输入资源id' }, { transform: transformNumber }],
              ['资源名称', 'res_title', { type: 'text', placeholder: '请输入资源名称' }],
              ['月份', 'month', { type: 'custom', render: () => {
                return (
                  <ElDatePicker
                    v-model={params.value.month}
                    disabled-date={disabledDate}
                    onUpdate:modelValue={val => {
                      params.value.month = val ? dayjs(val).toDate() : defaultParams.month
                      onQuery()
                    }}
                    type="month"
                    placeholder="请选择时间"
                  />
                )
              } }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div>更新时间：{updateTime.value}</div>
            {/* <div>
              <Button class="btn btn-primary btn-sm" onClick={handleExport} disabled={isExporting.value}>
                {isExporting.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                导出
              </Button>
            </div> */}
          </div>
        ),
        table: () => (
          <ElTable
            ref={tableRef}
            v-loading={loading.value}
            data={list.value || []}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            onSortChange={(column: any) => {
              // 降序排序字段：1-播放PV 2-播放时长 3-完播率 4-累计关注数 5-总收入 6-订阅收入 7-充值摊销收入 8-订阅
              if (column.order === 'descending') {
                const orderMap = {
                  play_pv: 1,
                  play_duration: 2,
                  play_finish_ration: 3,
                  follow_cnt: 4,
                  all_revenue: 5,
                  subscribe_pay_revenue: 6,
                  recharge_distribute_revenue: 7,
                  subscribe_distribute_revenue: 8,
                  ad_revenue: 9,
                }
                params.value.order_by = orderMap[column.prop as keyof typeof orderMap]
              } else {
                params.value.order_by = undefined
              }
              void onQuery()
            }}
          >
            {columns.value.map(col => {
              const props = {
                ...col,
                sortable: col.sortable === 'custom' ? 'custom' : undefined
              }

              return (
                <ElTableColumn
                  key={col.prop}
                  {...props}
                  v-slots={col.render ? {
                    default: ({ row }: { row: any }) => col.render?.({ row }),
                  } : undefined}
                />
              )
            })}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_index} v-model:size={params.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </x-competition-content-page>
  )
})

export default ResourceIncomePage
