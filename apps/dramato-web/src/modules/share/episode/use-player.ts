import { Aliplayer, IPlayerOptions } from 'src/lib/aliplayer'
import { onUnmounted, ref, watch } from 'vue'

/**
 *
 * @param options 播放器配置
 * @param subtitleLanguage 字幕语言
 * @param audioLanguage 音频语言
 * @returns
 */
export const usePlayer = () => {
  const player = ref<Aliplayer>()
  const showPlayOrPause = ref<boolean>(false) // 是否显示播放或暂停按钮
  const isPlaying = ref<boolean>(false) // 是否正在播放
  const showLoading = ref<boolean>(false) // 是否显示加载中
  const showPlayOrPauseTimer = ref<number>()
  const showLoadingTimer = ref<number>()

  const initPlayer = (options: IPlayerOptions) => {
    if (!options || !options.source || !options.id) return
    if (showLoadingTimer.value) {
      window.clearInterval(showLoadingTimer.value)
      showLoadingTimer.value = undefined
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    player.value = new (window as any).Aliplayer(Object.assign(options, {
      mediaType: 'video',
      height: '100%',
      width: '100%',
      playsinline: true,
      autoplay: false,
      preload: 'auto',
      controlBarVisibility: 'never',
      useH5Prism: true,
      format: 'm3u8',
      clickPause: true,
      skinLayoutIgnore: [
        'bigPlayButton',
      ],
    }, options)) as Aliplayer

    player.value.on('waiting', () => {
      console.log('数据缓冲中...')
      showLoading.value = true
      showPlayOrPause.value = false
    })

    player.value.on('playing', () => {
      console.log('正在播放中...')
      showLoading.value = false
    })

    player.value.on('canplay', () => {
      console.log('可以播放了...')
      showLoading.value = false
    })

    player.value.on('play', () => {
      console.log('开始播放...')
      isPlaying.value = true
      showPlayOrPause.value = true
    })

    player.value.on('pause', () => {
      console.log('暂停播放...')
      showPlayOrPause.value = true
      isPlaying.value = false
    })

    player.value.on('liveStreamStop', () => {
      console.log('直播中断...')
      showLoading.value = true
    })

    player.value.on('startSeek', time => {
      console.log('开始拖动进度条...', time)
    })

    player.value.on('completeSeek', time => {
      console.log('停止拖动进度条...', time)
    })

    player.value.on('canplay', () => {
      console.log('可以开始播放...')
      showPlayOrPause.value = true
    })

    player.value.on('ended', () => {
      console.log('播放完毕...')
      isPlaying.value = false
      showPlayOrPause.value = true
    })

    // 防止切换应用后播放器播放，但是loading还在显示
    showLoadingTimer.value = window.setInterval(() => {
      if (player.value?.getStatus() === 'playing' && showLoading.value === true) {
        showLoading.value = false
      }
    }, 300)
  }

  watch(() => showLoading.value, () => {
    // 如果展示了loading，则隐藏播放按钮
    if (showLoading.value) {
      showPlayOrPause.value = false
    }
  })

  watch(() => showPlayOrPause.value, () => {
    // 如果播放按钮展示，则定时隐藏
    if (showPlayOrPauseTimer.value) {
      window.clearTimeout(showPlayOrPauseTimer.value)
      showPlayOrPauseTimer.value = undefined
    }
    if (showPlayOrPause.value) {
      showPlayOrPauseTimer.value = window.setTimeout(() => {
        showPlayOrPause.value = false
      }, 3000)
    }
  })

  onUnmounted(() => {
    player.value?.dispose()
    player.value = undefined
    if (showPlayOrPauseTimer.value) {
      window.clearTimeout(showPlayOrPauseTimer.value)
      showPlayOrPauseTimer.value = undefined
    }
  })

  return {
    initPlayer,
    showPlayOrPause,
    showLoading,
    isPlaying,
    player,
  }
}
