import { insertSvgSymbols } from '@skynet/shared'

// eager: 为 true 时表示立即加载所有模块，而不是懒加载。
//        如果 svg 体积不大，建议用 true；如果 svg 体积大，可以改成
//        const modules = import.meta.glob<{ default: string }>('./*.svg', { query: 'raw', eager: false })
//        lazyInsertSvgSymbols(modules)
// query: 'raw' 表示加载原始内容，而不是获取路径
// !!!下面这句话会被 Vite 静态分析并替换，所以不能把 './*svg' 换成变量，因此也不能抽离成更简洁的函数!!!
export const removeSvgSymbols = insertSvgSymbols(
  import.meta.glob<{ default: string }>( './*.svg', { query: 'raw', eager: true }),
  {
    removeFill: (path, content) => {
      if(
        path.includes('fill') ||
        path.includes('ic-coin.svg') ||
        path.includes('tag-vip-2.svg')
      ) {
        return false
      }
      return true
    },
  }
)
