<template>
  <v-group :config="{ draggable: true, dragBoundFunc: boundFunc }" @dragmove="handleDragMove"
    @transformend="handleTransform">
    <v-rect :config="{
      width: width,
      height: height,
      fill: '#DBEAFE',
      stroke: '#3B82F6',
      strokeWidth: 1,
      cornerRadius: 4
    }" ref="rectRef" />
    <v-transformer ref="transformerRef" :config="{ enabledAnchors: ['middle-right'] }" />
  </v-group>
</template>

<script lang="tsx" setup>
import { ref, onMounted } from 'vue';
import { Vector2d } from 'konva/lib/types';

const props = defineProps<{
  width: number;
  height: number;
  x: number;
  minWidth?: number;
}>();

const emit = defineEmits<{
  (e: 'update:x', x: number): void;
  (e: 'update:width', width: number): void;
}>();

const rectRef = ref();
const transformerRef = ref();

// 限制只能在水平方向拖动
const boundFunc = (pos: Vector2d) => {
  return {
    x: Math.max(0, pos.x),
    y: 0
  };
};

// 处理拖动事件
const handleDragMove = (e: any) => {
  emit('update:x', e.target.x());
};

// 处理变形（缩放）事件
const handleTransform = () => {
  const node = rectRef.value.getNode();
  const scaleX = node.scaleX();
  const newWidth = Math.max(props.minWidth || 50, node.width() * scaleX);

  node.scaleX(1);
  node.width(newWidth);

  emit('update:width', newWidth);
};

onMounted(() => {
  transformerRef.value.getNode().attachTo(rectRef.value.getNode());
});
</script>
