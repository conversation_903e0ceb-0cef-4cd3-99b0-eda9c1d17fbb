{"name": "@skynet/shared", "version": "1.0.0", "description": "", "main": "shared.ts", "type": "module", "exports": {".": "./shared.ts", "./id-helper": "./id-helper.ts", "./env-helper": "./env-helper.ts", "./create-component": "./create-component.ts", "./const": "./const.ts"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "peerDependencies": {"lodash-es": "catalog:", "tailwind-merge": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "dependencies": {"@skynet/preset": "workspace:*", "@types/lodash-es": "catalog:", "uuid": "10.0.0", "zod": "catalog:"}, "devDependencies": {"@types/uuid": "9.0.8"}}