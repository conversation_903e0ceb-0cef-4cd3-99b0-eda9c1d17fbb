#!/bin/bash
set -e
export LANG=en_US.UTF-8

# define vars
TEST_SERVERS=(na-test-dramato-server-1)
PROD_SERVERS=(na-prod-dramato-server-16 na-prod-dramato-server-17 na-prod-freereels-server-3)
TEST_DIR="/home/<USER>/www_cp"
PROD_DIR="/home/<USER>/www_cp"

# check params
if [[ $# != 1 ]]; then
  echo "Usage: $0 [env:test|prod]"
  exit 0
fi

# get base path
CURR_DIR=$(dirname "$0")
OUTPUT_DIR="$CURR_DIR/dist"

# parse parameters
ENV_NAME=$(echo "$1" | tr '[:upper:]' '[:lower:]')

if [[ "$ENV_NAME" == "prod" ]]; then
  SERVERS=("${PROD_SERVERS[@]}")
  DIR=$PROD_DIR
else
  SERVERS=("${TEST_SERVERS[@]}")
  DIR=$TEST_DIR
fi

kinit -kt /etc/krb5.keytab
rm -rf dist || echo 'dist not exits'
mkdir -p dist || echo 'dist exits'
tar -xf dist.tar -C dist

for SERVER in "${SERVERS[@]}"; do
  if [[ "$SERVER" == "${SERVERS[0]}" && "$ENV_NAME" == "prod" ]]; then
    echo "create dist"
    ssh worker@$SERVER "rm -rf $DIR/dist; mkdir -p $DIR/dist"
    echo "scp dist"
    scp -r $OUTPUT_DIR/* worker@$SERVER:$DIR/dist
    echo "oss sync start"
    ssh worker@$SERVER "ossutil sync -f $DIR/dist oss://us-dramato-prod/frontend_static_cp/"
    echo "oss sync success"
  fi
  echo "scp dist"
  scp -r $OUTPUT_DIR/* worker@$SERVER:$DIR
  echo "scp success"
done

set +e
echo 'success'
