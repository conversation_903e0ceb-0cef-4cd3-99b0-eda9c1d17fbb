import { createComponent, fn } from '@skynet/shared'
import { <PERSON>reateForm, Pager, Button, Input, transformNumber, openDialog, transformDatetime, showAlert } from '@skynet/ui'
import { Wrapper } from 'src/layouts/wrapper'
import { computed, onMounted, ref, watch } from 'vue'
import { apiD, apiDramaQueryByKeyword, apiDramaQueryList, DramaItem, listingStatusList, freeStatusList, QueryDramaListParams, serializeList, resourceTypList, releaseRoundList, SortItem } from './short-drama-api'
import { cloneDeep, set, get } from 'lodash-es'
import { Icon } from '@skynet/ui/icon/icon'
import { throttle } from 'echarts'
import { Drama } from './drama'
import { MultiModifyDramaListingStatus } from './multi-modify-drama-listing-status'
import { MultiModifyDramaPrice } from './multi-modify-drama-price'
import { requiredLabel } from 'src/lib/required-label'
import router from 'src/router'
import { useShortDramaStore } from './short-drama-store'
import { useAppAndLangOptions } from 'src/modules/options/use-app-options'
import dayjs from 'dayjs'
import { lang } from 'src/lib/constant'

type ShortDramaProps = {
  props: {
    hasNav: boolean
    hasActions: boolean
    checkedItems: M.ApplicationDramaItem[]
    fixStatus?: number
    appId?: number
  }
  emits: {
    add: (data: DramaItem) => void
    remove: (data: DramaItem) => void
  }
}

export const ShortDrama = createComponent<ShortDramaProps>({
  name: 'ShortDrama',
  props: {
    hasNav: true,
    hasActions: true,
    checkedItems: [],
    // @ts-expect-error never mind
    fixStatus: undefined,
    // @ts-expect-error never mind
    appId: undefined,
  },
  emits: {
    add: fn,
    remove: fn,
  },
}, (props, { emit }) => {
  const { selectedDramas, dramaList, addDramas, removeDramas, resetStore, getListingStatusDesc, getLabelList, labels } = useShortDramaStore()

  const Form = CreateForm<QueryDramaListParams>()
  const dramaParams = ref<QueryDramaListParams>({
    app_id: '',
    language_version_code: '',
    free_status: undefined,
    listing_time_start: undefined,
    updated_time_start: undefined,
    publish_time_start: undefined,
    listing_time_end: undefined,
    updated_time_end: undefined,
    publish_time_end: undefined,
    search_keyword: [],
    resource_type: 0,
    resource_id_or_title_list: '',
    release_round: 0,
    audio_type: -1,
    sort_info: [{
      field: 'publish_time',
      asc: false,
    }],
    page_info: {
      page_index: 1,
      page_size: 30,
    },
  })

  const total = ref<number>(0)
  const queryString = ref<string>('')
  const queryResults = ref<DramaItem[]>([])
  const checkAll = ref<boolean>(false)
  const keywordLoading = ref<boolean>(false)
  const searchLoading = ref<boolean>(false)
  const downloadLoading = ref<boolean>(false)
  const selectedDropdownDrama = ref<DramaItem[]>([]) // 选中的下拉项
  const dataReady = ref<boolean>(false)

  watch(() => [selectedDropdownDrama.value, queryResults.value], () => {
    if (selectedDropdownDrama.value.length === queryResults.value.length && selectedDropdownDrama.value.length > 0) {
      checkAll.value = true
    } else {
      checkAll.value = false
    }
    dramaParams.value.search_keyword = selectedDropdownDrama.value.map(item => item.series_key)
    const keywords = queryString.value.split(/,|，/g)
    keywords.forEach((item, index) => {
      if (selectedDropdownDrama.value.some(drama => drama.series_key === item)) {
        keywords.splice(index, 1)
      }
    })
    queryString.value = keywords.join(',')
  }, {
    deep: true,
    immediate: true,
  })

  watch(() => [dramaParams.value.app_id, dramaParams.value.language_version_code, queryString.value], throttle(() => {
    if (dramaParams.value.app_id === '') {
      return
    }
    // 以中英文逗号分割
    const search_keyword = queryString.value.split(/,|，/g).join(',')
    const params = {
      search_keyword,
      app_id: dramaParams.value.app_id,
      language_version: dramaParams.value.language_version_code,
    }
    keywordLoading.value = true
    void apiDramaQueryByKeyword(params).then(res => {
      if (!res.data) return
      queryResults.value = res.data.list
    }).finally(() => {
      keywordLoading.value = false
    })
  }, 1000))

  const isItemSort = (field: SortItem['field']) => dramaParams.value.sort_info.some(item => item.field === field)
  const isItemSortAsc = (field: SortItem['field']) => dramaParams.value.sort_info.find(item => item.field === field)?.asc

  watch(() => dramaParams.value.page_info, () => {
    search()
  }, {
    deep: true,
  })

  const toggleSort = (field: SortItem['field']) => {
    dramaParams.value.sort_info = [{
      field,
      asc: !isItemSortAsc(field),
    }]
    search()
  }

  const toggleCheckItem = (e: Event, item: DramaItem) => {
    const target = e.target as HTMLInputElement
    if (target.checked) {
      selectedDropdownDrama.value.push(item)
    } else {
      selectedDropdownDrama.value = selectedDropdownDrama.value.filter(i => i.series_key !== item.series_key)
    }
  }

  const toggleCheckAll = (e: Event) => {
    const target = e.target as HTMLInputElement
    if (target.checked) {
      selectedDropdownDrama.value = cloneDeep(queryResults.value)
    } else {
      selectedDropdownDrama.value = []
    }
  }

  const makeExcel = (data: string[][], name: string) => {
    return new Promise(resolve => {
      // 将数据转换为 CSV 格式，确保每个值都被双引号包裹
      const csvContent = data.map((row: string[]) =>
        row.map(value => `"${String(value).replace(/"/g, '""')}"`).join(','),
      ).join('\n')

      // 添加 UTF-8 BOM
      const bom = '\uFEFF'
      const finalContent = bom + csvContent

      // 创建一个 Blob 对象
      const blob = new Blob([finalContent], { type: 'text/csv;charset=utf-8;' })

      // 创建下载链接
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', name)
      document.body.appendChild(link)
      link.click()

      // 释放 URL 资源并解决 Promise
      URL.revokeObjectURL(url)
      resolve('')
    })
  }

  const getAllData = (page_size = 2000) => {
    if (!dramaParams.value.app_id) {
      showAlert('请先选择应用', 'info')
      return
    }
    downloadLoading.value = true
    return new Promise(resolve => {
      void apiDramaQueryList({
        ...dramaParams.value,
        page_info: {
          page_index: 1,
          page_size,
        },
        language_version_code: dramaParams.value.language_version_code ? dramaParams.value.language_version_code : 'all',
      }).then(res => {
        if (!res.data) return
        resolve(res.data.list)
      }).catch(error => {
        showAlert(error.response.data.message, 'error')
      }).finally(() => {
        downloadLoading.value = false
      })
    })
  }

  const getFormatLanguage = (language_version_code: string) => {
    if (!language_version_code) return ''
    const langObj = lang.find(item => item.value === language_version_code)
    const arr = langObj?.label.split('-') || []
    return `${arr[1]}[${arr[0]}]`
  }

  const onDownloadExcel = async (n = 2000, type = 'common') => {
    downloadLoading.value = true
    try {
      const _list = await getAllData(n) as DramaItem[]
      let tableHeader = ['短剧ID', '剧名', '资源ID', '资源名称', '封面地址', '上架状态', '集数', '应用', '发行语言', '资源类型', '发行轮次', '标签', '简介', '连载状态', '上架时间', '现起始付费集', '单集定价', '生效端']
      let tableData = _list.map(item => {
        return [
          item.series_key,
          item.title,
          '' + item.resource_id,
          item.resource_title,
          item.cover_url,
          '' + getListingStatusDesc(item.listing_status),
          '' + item.episodes_number,
          item.app_name,
          getFormatLanguage(item.language_version_name),
          ['', '本土', '翻译'][item.resource_type],
          ['', '首发', '二轮'][item.release_round],
          item.labels,
          item.description,
          ['', '未完结', '已完结'][item.serialize_status],
          item.listing_time ? '' + dayjs(item.listing_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
          item.start_paying_episodes,
          item.episodes_price,
          item.app_platform,
        ]
      })
      if (type === 'origin') {
        tableHeader = ['drama_id', 'series_key', 'title', 'listing_status', 'listing_time', 'episodes_number', 'start_paying_episodes', 'new_start_paying_episodes', 'episodes_price', 'resource_type', 'app_platform']
        tableData = _list.map(item => {
          return [
            item.series_key,
            item.resource_id,
            item.title,
            item.listing_status,
            item.listing_time,
            item.episodes_number,
            item.start_paying_episodes,
            0,
            item.episodes_price,
            item.resource_type,
            item.app_platform,
          ]
        })
      }
      const appName = appOptions.value.find(row => row.value === dramaParams.value.app_id)?.label
      const language = getFormatLanguage(dramaParams.value.language_version_code)
      const listing_time_start = dramaParams.value.listing_time_start ? dayjs(dramaParams.value.listing_time_start).format('YYYYMMDD') : ''
      const listing_time_end = dramaParams.value.listing_time_end ? dayjs(dramaParams.value.listing_time_end).format('YYYYMMDD') : ''
      const now = dayjs().format('YYYYMMDD')
      const filenameArr = [appName, language, listing_time_start + listing_time_end, now].filter(o => o)
      const filename = filenameArr.join('_')
      await makeExcel([
        tableHeader,
        ...tableData,
      ], filename)
    } catch (error) {
      downloadLoading.value = false
    }
  }

  const search = () => {
    if (!dramaParams.value.app_id) {
      showAlert('请先选择应用', 'info')
      return
    }
    searchLoading.value = true
    dramaList.value = []
    void apiDramaQueryList({
      ...dramaParams.value,
      language_version_code: dramaParams.value.language_version_code ? dramaParams.value.language_version_code : 'all',
    }).then(res => {
      if (!res.data) return
      dramaList.value = res.data.list
      total.value = res.data.total
    }).catch(error => {
      showAlert(error.response.data.message, 'error')
    }).finally(() => {
      searchLoading.value = false
    })
  }

  const { appOptions, languageOptions } = useAppAndLangOptions(() => dramaParams.value.app_id, {
    onSuccess: search,
  })

  watch(appOptions, () => {
    if (appOptions.value.length > 0 && !dramaParams.value.app_id) {
      dramaParams.value.app_id = appOptions.value[0].value
    }
  })

  watch(() => dramaParams.value.app_id, id => {
    dramaParams.value.language_version_code = id ? (languageOptions.value[0]?.value) : ''
  })

  const handleKeydown = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      if (queryString.value) {
        const search_keyword = queryString.value.split(/,|，/g).join(',')
        const params = {
          search_keyword,
          app_id: +dramaParams.value.app_id,
          language_version: dramaParams.value.language_version_code,
        }
        void apiDramaQueryByKeyword(params).then(res => {
          if (!res.data) return
          selectedDropdownDrama.value = selectedDropdownDrama.value.concat(res.data.list)
        }).finally(() => {
          keywordLoading.value = false
        })
        queryString.value = ''
      }
    } else if (e.key === 'Backspace') {
      if (selectedDropdownDrama.value.length > 0 && queryString.value === '') {
        selectedDropdownDrama.value.pop()
      }
    }
  }

  const selectAll = () => {
    // 超过100就选中前100个，否则全选
    if (queryResults.value.length > 100) {
      selectedDropdownDrama.value = queryResults.value.slice(0, 100)
    } else {
      selectedDropdownDrama.value = cloneDeep(queryResults.value)
    }
  }

  const searchResult = () => (
    <div class="w-[360px] bg-white px-3 py-1 text-sm text-gray-500">
      <div class="border-b border-gray-200 py-2">
        <div>粘贴ID后，按回车键添加。(多ID值之间请以逗号分隔)</div>
      </div>
      {
        keywordLoading.value
          ? (
              <div class="flex h-32 items-center justify-center">
                <Icon name="line-md:loading-twotone-loop" class="size-5" />
              </div>
            )
          : (
              queryResults.value.length > 0
                ? (
                    <>
                      <div class="flex max-h-[400px] w-full flex-col items-start justify-start overflow-y-auto pt-2">
                        {/* 只有全量的时候才展示全部 */}
                        {
                          queryString.value === ''
                          && (
                            <label class="flex items-center gap-2 py-1">
                              <input type="checkbox" checked={checkAll.value} onChange={(e: Event) => toggleCheckAll(e)} class="checkbox-primary checkbox checkbox-xs" />
                              <div>全部</div>
                            </label>
                          )
                        }
                        {
                          queryResults.value.map(item => (
                            <label class="flex max-w-full items-center gap-2 py-1">
                              <input type="checkbox" checked={selectedDropdownDrama.value.findIndex(i => i.series_key === item.series_key) !== -1} onChange={(e: Event) => toggleCheckItem(e, item)} class="checkbox-primary checkbox checkbox-xs" />
                              <div class="truncate">{item.series_key + '-' + item.title}</div>
                            </label>
                          ))
                        }
                      </div>
                      <div class="flex flex-row justify-between py-2">
                        <div>
                          {queryResults.value.length > 100 ? '100+' : queryResults.value.length}
                          个匹配结果
                        </div>
                        {queryResults.value.length > 0 && (
                          <div class="link-primary cursor-pointer" onClick={selectAll}>
                            全选
                            {queryResults.value.length > 100 ? '前100项' : ''}
                          </div>
                        )}
                      </div>
                    </>
                  )
                : (
                    <div class="flex h-32 items-center justify-center">
                      <p>暂无搜索结果</p>
                    </div>
                  )
            )

      }
    </div>
  )

  const openModifyListingDialog = () => {
    const close = openDialog({
      title: '批量修改上架状态',
      body: (
        <MultiModifyDramaListingStatus
          onCancel={() => close()}
          onAllSaveSuccess={() => {
            showAlert('保存成功')
            close()
            search()
            selectedDramas.value = []
          }}
          onUpdateData={() => {
            search()
          }}
        />
      ),
      beforeClose: () => {
        resetStore()
      },
    })
  }

  const openModifyPriceDialog = () => {
    const close = openDialog({
      title: '批量修改价格',
      body: (
        <MultiModifyDramaPrice
          onCancel={() => close()}
          onAllSaveSuccess={() => {
            showAlert('保存成功', 'success')
            close()
            search()
            selectedDramas.value = []
          }}
          onUpdateData={() => {
            search()
          }}
        />
      ),
      beforeClose: () => {
        resetStore()
      },
    })
  }

  const checkAllCurrentPage = computed(() => {
    return dramaList.value.length > 0 && dramaList.value.every(item => selectedDramas.value.findIndex(i => i.series_key === item.series_key) !== -1)
  })

  const toggleAllCurrentPage = () => {
    if (checkAllCurrentPage.value) {
      removeDramas(dramaList.value)
    } else {
      addDramas(dramaList.value)
    }
  }

  // 生成一段固定secret
  const dMode = (router.currentRoute.value.query.secret_key === 'NeZKTArsP4')
  const d = ref<number>()
  const _d1 = () => {
    void apiD({ ids: [d.value] })
  }
  const _d = () => {
    const close = openDialog({
      title: 'xxx',
      body: computed(() => (
        <div>
          应用：
          <select v-model={d.value} class="select">
            {
              appOptions.value.map(item => <option value={item.value}>{item.label}</option>)
            }
          </select>
          <div class="btn-group absolute bottom-4 right-5 flex w-full items-center justify-end gap-3">
            <Button class="border-primary text-primary w-80px h-8 rounded-md border border-solid bg-transparent text-base font-medium leading-8" onClick={() => close()}>
              取消
            </Button>
            <Button
              class="border-primary bg-primary w-80px flex h-8 items-center justify-center rounded-md border border-solid text-base font-medium leading-8 text-white"
              onClick={() => {
                _d1()
                close()
              }}
            >
              保存
            </Button>
          </div>
        </div>
      )),
    })
  }

  watch(() => props.fixStatus, val => {
    console.log('fixStatus', val)
    if (val) {
      dramaParams.value.listing_status = [val]
    }
  }, {
    immediate: true,
  })

  watch(() => props.appId, val => {
    console.log('appId', val)
    if (val) {
      dramaParams.value.app_id = val
    }
  }, {
    immediate: true,
  })

  onMounted(() => {
    dramaList.value = []
    void getLabelList()
    if (props.checkedItems) {
      selectedDramas.value = [...props.checkedItems]
    }
  })

  return () => (
    <Wrapper>
      <div class="hide-when-in-dialog flex w-full items-end gap-4 border-b border-gray-200 pb-4">
        <div class="text-xl font-medium text-gray-900">应用短剧管理</div>
        <div class="text-base font-normal text-gray-500">管理短剧的应用维度配置信息</div>
      </div>
      <Form
        dataReady={dataReady.value}
        data={dramaParams.value}
        onChange={(path, value) => {
          const timeMap = {
            updated_time_start: 'updated_time_end',
            listing_time_start: 'listing_time_end',
            publish_time_start: 'publish_time_end',
          }
          const values = Object.values(timeMap)
          const keys = Object.keys(timeMap)
          if (['updated_time_end', 'listing_time_end', 'publish_time_end'].includes(path)) {
            const endTime = new Date(value as Date).getTime()
            const curTime = Date.now()
            if (endTime > curTime) {
              showAlert('结束时间不能大于当前时间', 'error')
              set(dramaParams.value, path, get(dramaParams.value, path, ''))
              return
            }
            const index = values.indexOf(path)
            const startTime = get(dramaParams.value, keys[index], '')
            if (startTime && new Date(startTime as Date).getTime() > endTime) {
              set(dramaParams.value, path, get(dramaParams.value, path, ''))
              showAlert('结束时间不能小于开始时间', 'error')
              return
            }
          }
          if (['updated_time_start', 'listing_time_start', 'publish_time_start'].includes(path)) {
            const sTime = new Date(value as Date).getTime()
            const curTime = Date.now()
            if (sTime > curTime) {
              set(dramaParams.value, path, get(dramaParams.value, path, ''))
              showAlert('开始时间不能大于当前时间', 'error')
              return
            }

            const index = keys.indexOf(path)
            const endTime = get(dramaParams.value, values[index], '')
            if (endTime && new Date(endTime as Date).getTime() < sTime) {
              set(dramaParams.value, path, get(dramaParams.value, path, ''))
              showAlert('开始时间不能大于结束时间', 'error')
              return
            }
          }
          set(dramaParams.value, path, value)
        }}
        onReset={(_e, data) => {
          selectedDramas.value = []
          dramaParams.value = data
          dramaParams.value.resource_id_or_title_list = ''
          dramaParams.value.label_id_list = []
          dramaParams.value.audio_type = -1
          selectedDropdownDrama.value = []
          queryString.value = ''
        }}
        onSubmit={() => {
          // 点击查询需要还原分页
          // search() 这里不需要search是因为page_info的值已经变了，会触发watch，然后search
          if (queryString.value) {
            const search_keyword = queryString.value.split(/,|，/g).join(',')
            const params = {
              search_keyword,
              app_id: +dramaParams.value.app_id,
              language_version: dramaParams.value.language_version_code,
            }
            void apiDramaQueryByKeyword(params).then(res => {
              if (!res.data) return
              selectedDropdownDrama.value = selectedDropdownDrama.value.concat(res.data.list)
              dramaParams.value.page_info = {
                page_index: 1,
                page_size: 30,
              }
            }).finally(() => {
              keywordLoading.value = false
            })
            queryString.value = ''
          } else {
            dramaParams.value.page_info = {
              page_index: 1,
              page_size: 30,
            }
          }
          // 查询要清空上一次选中的搜索结果
          selectedDramas.value = []
        }}
        items={[
          [
            requiredLabel('应用'),
            'app_id',
            {
              type: 'select', class: 'w-[240px]', options: appOptions.value, disabled: !!props.appId,
            },
            { transform: transformNumber },
          ],
          [
            requiredLabel('发行语言'),
            'language_version_code',
            {
              type: 'select', class: 'w-[240px]', options: languageOptions.value,
            },
          ],
          {
            label: '上架状态',
            path: 'listing_status',
            input: {
              type: 'multi-select',
              options: listingStatusList.map(item => ({
                label: item.name,
                value: item.code,
                disabled: !!props.fixStatus,
              })),
              disabledItemClass: 'opacity-50 cursor-not-allowed',
              onClear: () => {
                dramaParams.value.listing_status = [props.fixStatus]
              },
              popoverWrapperClass: 'z-popover-in-dialog',
            },
          },
          {
            label: '付费状态',
            path: 'free_status',
            input: {
              type: 'select',
              options: freeStatusList.map(item => ({
                label: item.name,
                value: item.code,
              })),
            },
            transform: transformNumber,
          },
          {
            label: '连载状态',
            path: 'serialize_status',
            input: { type: 'select', class: 'w-[240px]', options: serializeList.map(item => ({
              label: item.name,
              value: item.code,
            })) },
            transform: transformNumber,
          },
          {
            label: '发行轮次',
            path: 'release_round',
            input: { type: 'select', class: 'w-[240px]', options: releaseRoundList, autoInsertEmptyOption: false },
            transform: transformNumber,
          },
          {
            label: '短剧类型',
            path: 'resource_type',
            input: { type: 'select', class: 'w-[240px]', options: resourceTypList.map(item => ({
              label: item.name,
              value: item.code,
            })), autoInsertEmptyOption: false },
            transform: transformNumber,
          },
          {
            label: '上架时间-开始',
            path: 'listing_time_start',
            input: { type: 'datetime', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
            transform: transformDatetime,
          },
          {
            label: '上架时间-结束',
            path: 'listing_time_end',
            input: { type: 'datetime', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
            transform: transformDatetime,
          },
          {
            label: '更新时间-开始',
            path: 'updated_time_start',
            input: { type: 'datetime', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
            transform: transformDatetime,
          },
          {
            label: '更新时间-结束',
            path: 'updated_time_end',
            input: { type: 'datetime', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
            transform: transformDatetime,
          },
          {
            label: '授权时间-开始',
            path: 'publish_time_start',
            input: { type: 'datetime', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
            transform: transformDatetime,
          },
          {
            label: '授权时间-结束',
            path: 'publish_time_end',
            input: { type: 'datetime', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
            transform: transformDatetime,
          },
          {
            label: '短剧ID/名称',
            path: 'search_keyword',
            input: {
              type: 'custom',
              render: () => (
                <Input
                  searchable
                  class="input input-sm input-bordered w-[360px] overflow-hidden"
                  inputClass="w-full grow shrink basis-0 p-0 border-none pr-6"
                  popoverWrapperClass="z-popover-in-dialog"
                  limit={2}
                  v-model={queryString.value}
                  results={searchResult}
                  onKeydown={handleKeydown}
                  labels={selectedDropdownDrama.value.map(item => item.title)}
                  onRemove:label={(index: number) => selectedDropdownDrama.value.splice(index, 1)}
                  v-slots={{
                    suffix: () => <Icon class="size-5" name="material-symbols-light:search" />,
                  }}
                />
              ),
            },
          },
          {
            label: '资源ID/名称',
            path: 'resource_id_or_title_list',
            input: { type: 'text', class: 'w-[225px]' },
          },
          {
            label: '标签',
            path: 'label_id_list',
            input: { type: 'multi-select', class: 'w-[240px]', options: labels.value.map(item => ({
              label: item.content,
              value: item.label_id,
              tooltip: item.meaning,
            })) },
          },
          {
            label: '是否配音版',
            path: 'audio_type',
            input: { type: 'select', class: 'w-[240px]', options: [
              {
                value: '-1',
                label: '全部',
              },
              {
                value: '0',
                label: '非配音剧',
              }, {
                value: '1',
                label: '人工配音',
              },
              {
                value: '2',
                label: 'AI配音',
              },
            ], autoInsertEmptyOption: false },
            transform: transformNumber
          },
        ]}
      />
      <div class="flex items-center justify-between gap-2 border-b border-gray-200 py-2 text-gray-600">
        <div class="flex shrink-0 items-center gap-2">
          排序：
          <div class="cursor-pointer" onClick={() => toggleSort('listing_time')}>
            <label class="swap flex items-center gap-1">
              <span class={isItemSort('listing_time') ? 'font-bold text-gray-800' : 'font-normal'}>上架时间</span>
              <div>
                {
                  isItemSortAsc('listing_time') ? <Icon name="ci:sort-descending" /> : <Icon name="ci:sort-ascending" />
                }
              </div>
            </label>
          </div>
          <div class="cursor-pointer" onClick={() => toggleSort('updated')}>
            <label class="flex items-center gap-1">
              <span class={isItemSort('updated') ? 'font-bold text-gray-800' : 'font-normal'}>更新时间</span>
              <div>
                {
                  isItemSortAsc('updated') ? <Icon name="ci:sort-descending" /> : <Icon name="ci:sort-ascending" />
                }
              </div>
            </label>
          </div>
          <div class="cursor-pointer" onClick={() => toggleSort('publish_time')}>
            <label class="flex items-center gap-1">
              <span class={isItemSort('publish_time') ? 'font-bold text-gray-800' : 'font-normal'}>授权时间</span>
              <div>
                {
                  isItemSortAsc('publish_time') ? <Icon name="ci:sort-descending" /> : <Icon name="ci:sort-ascending" />
                }
              </div>
            </label>
          </div>
        </div>
        <div class="flex gap-4">
          {dMode && <Button class="btn-default btn btn-sm" onClick={_d}>dramato短剧</Button>}
          {/* <Button disabled={downloadLoading.value} class="btn-default hide-when-in-dialog btn btn-sm" onClick={() => onDownloadExcel(30000, 'origin')}>
            {downloadLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
            导出有效剧集
          </Button>
          <Button disabled={downloadLoading.value} class="btn-default hide-when-in-dialog btn btn-sm" onClick={() => onDownloadExcel(2000)}>
            {downloadLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
            导出 2000 条
          </Button>
          <Button disabled={downloadLoading.value} class="btn-default hide-when-in-dialog btn btn-sm" onClick={() => onDownloadExcel(10000)}>
            {downloadLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
            导出 10000 条
          </Button> */}
          <Button class="btn-default btn btn-sm" onClick={toggleAllCurrentPage}>{checkAllCurrentPage.value ? '取消全选' : '全选'}当前页短剧</Button>
          <Button class="hide-when-in-dialog btn btn-primary btn-sm" disabled={selectedDramas.value.length === 0} onClick={openModifyListingDialog}>批量修改选中{selectedDramas.value.length}部短剧的上架状态</Button>
          <Button class="hide-when-in-dialog btn btn-primary btn-sm" disabled={selectedDramas.value.length === 0} onClick={openModifyPriceDialog}>批量修改选中{selectedDramas.value.length}部短剧的定价</Button>
        </div>
      </div>
      <x-drama-list class="flex flex-wrap gap-0 overflow-hidden p-1">
        {
          searchLoading.value
            ? (
                <div class="flex h-[200px] w-full items-center justify-center">
                  <Icon name="line-md:loading-twotone-loop" class="size-5" />
                </div>
              )
            : dramaList.value.length > 0
              ? dramaList.value.map(
                item => (
                  <Drama
                    onAdd={() => emit('add', item)}
                    onRemove={() => emit('remove', item)}
                    key={item.series_key}
                    item={item}
                    class="max-w-[25%]"
                  />
                ),
              )
              : (
                  <div class="flex h-[200px] w-full items-center justify-center">
                    <span class="text- -400">暂无数据</span>
                  </div>
                )
        }

      </x-drama-list>
      <div>
        <div class="flex w-full justify-end">
          {
            total.value > 0 && (
              <Pager
                total={total.value}
                v-model:page={dramaParams.value.page_info.page_index}
                v-model:size={dramaParams.value.page_info.page_size}
              />
            )
          }
        </div>
      </div>
    </Wrapper>
  )
})

export default ShortDrama
