import { createComponent, mc } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Button, DateTime, openDialog, Pager, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { useUserQuery } from './use-user-query'
import { ref } from 'vue'
import { UserQueryForm } from './user-query-form'
type UserQueryOptions = {
  props: {}
}
export const UserQuery = createComponent<UserQueryOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    search,
    pageSize,
    page,
    total,
  } = useUserQuery()

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>用户查询</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { }
              void search(1)
            }}
            onSubmit={() => search(page.value)}
            data={params.value}
            items={[
              ['用户ID', 'user_id', { type: 'textarea', placeholder: '多个ID用换行分隔' }, { hint: (
                <x-tips class="relative mt-2 flex flex-col gap-y-1 text-sm text-gray-600">
                  <x-tip-in class="absolute w-[500px]">
                    选择一种ID填入来提交查询，若2个都填则按用户ID查询
                  </x-tip-in>
                </x-tips>
              ),
              }],
              ['UID', 'uid', { type: 'textarea', placeholder: '多个ID用换行分隔' }, { }],
              ['邮箱', 'email', { type: 'textarea', placeholder: '多个邮箱用换行分隔' }, { }],
            ]}
          />
        ),
        table: () => (
          <Table class="tm-table-fix-last-column" loading={loading.value} list={list.value} columns={[
            ['用户ID', r => r.user_info.user_id, { class: 'w-[120px]' }],
            ['UID', r => r.user_info.uid, { class: 'w-[100px]' }],
            ['头像', row => row.user_info.avatar ? <img src={row.user_info.avatar} class="size-8 rounded-lg" /> : null, { class: 'w-[100px]' }],
            ['用户名', row => row.user_info.name, { class: 'w-[200px]' }],
            ['国家', row => row.user_info.country, { class: 'w-[120px]' }],
            // ['注册类型', row => ['匿名', '注册', '注册', '注册'][row.user_info.user_type], { class: 'w-[150px]' }],
            ['注册类型', row => ['匿名', 'facebook', 'google', 'appstore'][row.user_info.user_type], { class: 'w-[150px]' }],
            ['三方账号', row => row.user_info.third_party_account, { class: 'w-[200px]' }],
            ['Coins余额', row => row.user_wallet.cash_balance, { class: 'w-[200px]' }],
            ['Reward Coins余额', row => row.user_wallet?.bonus_balance + row.user_wallet?.rewards_balance, { class: 'w-[200px]' }],
            ['会员权益', row => row.user_wallet.vip_level ? '会员/' + row.user_wallet.vip_period : '非会员', { class: 'w-[200px]' }],
            ['会员有效期', row => row.user_wallet.vip_level ? <DateTime value={row.user_wallet.vip_expire * 1000} /> : '-', { class: 'w-[200px]' }],
            // ['权益类型', row => ['', '购买订阅', '续订'][row.user_wallet.subscribe_renewal_status], { class: 'w-[200px]' }],
            ['订阅类型', row => row.user_wallet.last_subscribe_period, { class: 'w-[200px]' }],
            ['充值总额', row => row.user_wallet.total_recharge_amount, { class: 'w-[200px]' }],
            ['充值余额', row => row.user_wallet.total_gold_balance, { class: 'w-[200px]' }],
            ['用户对应的所有设备和IP地址', row => {
              const hasMultipleDevices = row.device_info.items.length > 1
              const len = row.showAll ? row.device_info.items.length : 1
              return (
                <x-items key={`device_${row.user_info.uid}_${row.showAll ? 1 : 2}`} class="flex flex-col gap-2">
                  {
                    (row.showAll ? row.device_info.items : row.device_info.items.slice(0, 1)).map((i, index) => (
                      <x-item class="flex flex-col gap-2">
                        <span>IP: {i.ip}</span>
                        <span>设备: {i.device_id} {hasMultipleDevices && index === len - 1 && (
                          <Button
                            class="btn btn-xs btn-link inline-block"
                            onClick={() => {
                              row.showAll = !row.showAll
                            }}
                          >
                            { row.showAll ? '收起' : '展开(' + row.device_info.items.length + ')' }
                          </Button>
                        )}</span>
                      </x-item>
                    ))
                  }
                </x-items>
              )
            }, { class: 'w-[400px]' }],
            ['最近一年活跃时间', row => {
              // 过滤一年内的设备活跃记录
              const now = new Date()
              const oneYearAgo = new Date()
              oneYearAgo.setFullYear(now.getFullYear() - 1)

              // 创建过滤后的设备列表
              const filteredItems = row.device_info.items.filter(item => {
                if (!item.last_active_time) return false
                const timeStr = item.last_active_time.toString()
                if (timeStr.length !== 8) return false

                const year = parseInt(timeStr.substring(0, 4))
                const month = parseInt(timeStr.substring(4, 6)) - 1 // 月份从0开始
                const day = parseInt(timeStr.substring(6, 8))

                const itemDate = new Date(year, month, day)
                return itemDate >= oneYearAgo
              })
              const hasMultipleDevices = filteredItems.length > 1
              const len = row.showAll ? filteredItems.length : 1
              return (
                <x-items key={`last_${row.user_info.uid}_${row.showAll ? 1 : 2}`} class="flex flex-col gap-2">
                  {
                    (row.showAll ? filteredItems : filteredItems.slice(0, 1)).map((i, index) => {
                      return (
                        <x-item class="flex flex-col gap-2">
                          <span>设备: {i.device_id}</span>
                          <span>时间: {i.last_active_time || '-'} {hasMultipleDevices && index === len - 1 && (
                            <Button
                              class="btn btn-xs btn-link inline-block"
                              onClick={() => row.showAll = !row.showAll}
                            >
                              { row.showAll ? '收起' : '展开(' + filteredItems.length + ')' }
                            </Button>
                          )}</span>
                        </x-item>
                      )
                    })
                  }
                </x-items>
              )
            }, { class: 'w-[400px]' }],
            ['最近登陆的设备', row => row.device_info.last_info.device_manufacturer, { class: 'w-[200px]' }],
            ['最后一次活跃时间', row => row.device_info.last_info.last_active_time, { class: 'w-[200px]' }],
            ['最近登陆的设备型号', row => row.device_info.last_info.device_model, { class: 'w-[200px]' }],
            ['最近使用的App版本(最后一次活跃）', row => row.device_info.last_info.last_active_app_version, { class: 'w-[200px]' }],
            [
              <span class="px-3">操作</span>,
              row => (
                <div class="flex flex-nowrap">
                  <Button class="btn btn-link btn-sm hidden" onClick={() => {
                    // currentLevel.value = { ...row }
                    const closeEditRechargeLevelModal = ref()
                    const dialogMainClass = 'overflow-y-auto flex flex-col flex-auto pb-0'

                    closeEditRechargeLevelModal.value = openDialog({
                      title: '资产转移',
                      body: () => <UserQueryForm user={row} onHide={() => closeEditRechargeLevelModal.value?.()} />,
                      mainClass: dialogMainClass,
                    })
                  }}
                  >
                    资产转移
                  </Button>
                </div>
              ),
              { class: mc('w-[100px]') },
            ],
          ]}
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})
