import { httpClient } from 'src/lib/http-client'

export type SeriesInfo = {
  id: string
  name: string
  desc: string
  cover: string
  episode_count: number
  view_count: number
  follow_count: number
  finish_status: number
  view_episode: number
  following: boolean
  episode_price: number
}

export const apiSeriesInfo = (seriesId: string, language?: string) => {
  return httpClient.get<ApiResponse<{ series_info: SeriesInfo }>>('dm-api/drama/share/series_info?series_id=' + seriesId + (language ? '&lang=' + language : ''))
}
