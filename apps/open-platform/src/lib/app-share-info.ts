import { createStringId } from '@skynet/shared'

const keys = ['app_version', 'share_user_id', 'channel']
const id = createStringId()

export const setAppShareInfo = () => {
  const query = new URLSearchParams(location.search)
  keys.forEach(key => {
    const uniqueKey = key + id
    if (!location.pathname.startsWith('/download')) {
      // 下载页之外的页面，删除本地缓存
      sessionStorage.removeItem(uniqueKey)
    }
    const value = query.get(uniqueKey)
    if (value) {
      sessionStorage.setItem(uniqueKey, value)
    }
  })
}

export const getAppShareInfo = (key: string) => {
  const uniqueKey = key + id
  return sessionStorage.getItem(uniqueKey) ?? ''
}
