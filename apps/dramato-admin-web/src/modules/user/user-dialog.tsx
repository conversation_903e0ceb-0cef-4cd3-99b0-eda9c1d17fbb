/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { useRoleStore } from './use-role-store'
import { CreateForm, Button, showFailToast, showToast, Icon } from '@skynet/ui'
import { onMounted, watch } from 'vue'
import { set, cloneDeep } from 'lodash-es'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { apiEditUser } from './user-api'
import { ref } from 'vue'
import { useClipboard } from '@vueuse/core'
import { departments } from './constant'

type UserDialogOptions = {
  props: {
    closeDialog: () => void
    afterSave: () => void
  }
}
export const UserDialog = createComponent<UserDialogOptions>({
  props: {
    closeDialog: () => {},
    afterSave: () => {},
  },
}, props => {
  const { copy, copied } = useClipboard()
  const btnLoading = ref(false)
  const password = ref('')
  const Form = CreateForm<{
    open_roles: number[]
  }>()
  const form = ref<{
    username: string
    open_roles: number[]
    department: string
  }>({
    open_roles: [],
    username: '',
    department: '',
  })

  watch(() => copied.value, () => {
    if (copied.value) {
      showToast({
        message: `操作成功！密码复制成功 密码：${password.value}`,
        duration: 10 * 1000,
        icon: 'success',
      })
    }
  })

  onMounted(() => {
    form.value = {
      open_roles: cloneDeep(currentUser.value?.open_roles) || [],
      username: '',
      department: '',
    }
  })

  const formRules = z.object({
    username: z.string().min(1, '请输入用户名称').refine(e => {
      if (e.startsWith('x-')) return true
      return false
    }, {
      message: '用户名称必须有 x- 前缀',
    }),
    open_roles: z.array(z.number()).min(1, '请选择角色'),
  })
  const { currentUser, roles, getUserMenu } = useRoleStore()
  const { error, validateAll } = useValidator(form, formRules)

  const menuList = ref<M.IUserMenuItem[]>([])
  const menuLoading = ref(false)

  watch(form, async newVal => {
    console.log(form.value.open_roles)
    if (!newVal.open_roles || !newVal.open_roles.length) return
    menuLoading.value = true
    menuList.value = await getUserMenu(newVal.open_roles)
    menuLoading.value = false
  }, { immediate: true, deep: true })

  const renderMenuItem = (menuSubItem: M.IMenuItem, parent_name: string) => {
    return (
      <li class="flex flex-row hover group items-center gap-x-4">
        <a href="javascript:void(0);">{menuSubItem.title} - {menuSubItem.path}</a>
      </li>
    )
  }

  // 目录渲染
  const renderSubTitle = (menuSubItem: M.IMenuItem, parent_name: string) => {
    const id = menuSubItem.id
    return (
      <li>
        <details open>
          <summary class="inline-flex hover group">
            <span>{menuSubItem.title}</span>
          </summary>
          <ul>
            {menuSubItem.children && renderMenu(menuSubItem.children, menuSubItem.title)}
          </ul>
        </details>
      </li>
    )
  }

  const renderMenu = (menus: M.IMenuItem[], parent_name = '根目录') => {
    return menus.map(menuSubItem => {
      if (menuSubItem.path) {
        return renderMenuItem(menuSubItem, parent_name)
      } else {
        return renderSubTitle(menuSubItem, parent_name)
      }
    })
  }

  const save = async () => {
    if (!validateAll()) return
    try {
      btnLoading.value = true
      const res = await apiEditUser({
        username: form.value.username,
        open_roles: form.value.open_roles || [],
        department: form.value.department,
        operate_type: 1,
        source: 2,
      })
      password.value = res.data?.password || ''
      await copy(password.value)
      // showSuccessToast('操作成功')
      props.afterSave()
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }

  return () => (
    <>
      <Form
        data={form.value}
        hasAction={false}
        error={error.value}
        class="w-full flex-col"
        onChange={(path, value) => {
          set(form.value, path, value)
        }}
        items={
          [
            [
              requiredLabel('用户名称：'),
              'username',
              {
                type: 'text',
                placeholder: '用户名称必须有 x- 前缀',
              },
            ],
            [
              requiredLabel('角色：'),
              'open_roles',
              {
                type: 'multi-select',
                options: roles.value.map(roleObj => {
                  return {
                    value: roleObj.id,
                    label: roleObj.name,
                  }
                }),
                popoverWrapperClass: 'z-popover-in-dialog',
                class: 'w-[520px]',
              },
            ],
            [
              requiredLabel('部门：'),
              'department',
              {
                type: 'select',
                options: departments,
                class: 'w-[520px]',
                autoInsertEmptyOption: false,
              },
            ],
            [
              '菜单权限：',
              'menu',
              {
                type: 'custom',
                render: () => (
                  <ul class="menu lg:menu-horizontal bg-base-200 rounded-box lg:mb-64">
                    {!form.value.open_roles.length ? <span>请先选择角色</span> : null}
                    {
                      menuLoading.value ? <span class="loading loading-spinner loading-sm" /> : renderMenu(menuList.value)
                    }
                  </ul>
                ),
              },
            ],
          ]
        }
      />

      <div class="flex justify-end gap-x-2">
        <Button class="btn  btn-sm" onClick={() => props.closeDialog()}>取消</Button>
        <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={save}>
          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
          确定
        </Button>
      </div>
    </>
  )
})
