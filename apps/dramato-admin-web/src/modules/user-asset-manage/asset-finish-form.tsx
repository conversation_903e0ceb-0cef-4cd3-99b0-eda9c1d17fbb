/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator, mc } from '@skynet/shared'
import { Button, DateTime } from '@skynet/ui'
import { useUserAssetManage } from './use-user-asset-manage'
import { set } from 'lodash-es'
import { useRouter } from 'vue-router'

type AssetFinistFormOptions = {
  props: {}
  emits: {
    step: (stepNum: number) => void
  }
}
export const AssetFinistForm = createComponent<AssetFinistFormOptions>({
  props: {},
  emits: {
    step: () => {},
  },
}, (props, { emit }) => {
  const router = useRouter()
  const {
    FormOne,
    params,
    userAssetForm,
    assetMsgList,
    transferResult,
  } = useUserAssetManage()
  const nextStep = () => {
    console.log('关闭')
    params.value = { is_single_user_query: 0 }
    userAssetForm.value = {}
    void router.push('/user-asset-manage')
  }

  return () => (
    <>
      <div>
        <div class="mb-3 grid p-7 bg-gray-100 w-[1040px]">
          <h2 class="font-medium text-lg text-center mb-3">资产转移结果</h2>
          <hr class="w-full my-4" />
          <FormOne
            class="mb-3 grid flex-1 grid-cols-1 gap-y-1"
            data={userAssetForm.value}
            hasAction={false}
            onChange={(path, value) => {
              set(userAssetForm.value || {}, path, value)
            }}
            items={[
              [
                '',
                '',
                {
                  type: 'custom',
                  render: r => {
                    return (
                      <x-push-desc-list class="flex gap-10">
                        <div class=" p-5 w-[500px] bg-gray-100 rounded-lg">
                          <h2 class="font-medium mb-2">转出账号信息</h2>
                          <FormOne
                            class="grid grid-cols-4"
                            hasAction={false}
                            data={userAssetForm.value}
                            items={[
                              ['用户ID：', '', { type: 'custom', render: r => { return (<div>{transferResult.value?.out_account_info?.user_id}</div>) } }, { class: 'flex flex-row col-span-4' }],
                              ['国家/地区代码：', '', { type: 'custom', render: r => { return (<div>{transferResult.value?.out_account_info?.country_code}</div>) } }, { class: 'flex flex-row col-span-4' }],
                              ['账号状态：', '', { type: 'custom', render: r => { return (<div>最后使用时间为 {transferResult.value.out_account_info?.last_active_dt}</div>) } }, { class: 'flex flex-row  col-span-4' }],
                              ['端：', '', { type: 'custom', render: r => { return (<div>{transferResult.value.out_account_info?.platform == 1 ? 'ios' : transferResult.value.out_account_info?.platform == 2 ? 'android' : '不知'}</div>) } }, { class: 'flex flex-row col-span-4' }],
                            ]}
                          />
                        </div>
                        <div class=" p-5 w-[500px] bg-gray-100 rounded-lg">
                          <h2 class="font-medium mb-2">转入账号信息</h2>
                          <FormOne
                            class="grid grid-cols-4"
                            hasAction={false}
                            data={userAssetForm.value}
                            items={[
                              ['用户ID：', '', { type: 'custom', render: r => { return (<div>{transferResult.value?.in_account_info?.user_id}</div>) } }, { class: 'flex flex-row col-span-4' }],
                              ['国家/地区代码：', '', { type: 'custom', render: r => { return (<div>{transferResult.value?.in_account_info?.country_code}</div>) } }, { class: 'flex flex-row col-span-4' }],
                              ['账号状态：', '', { type: 'custom', render: r => { return (<div>最后使用时间为 {transferResult.value.in_account_info?.last_active_dt}</div>) } }, { class: 'flex flex-row  col-span-4' }],
                              ['端：', '', { type: 'custom', render: r => { return (<div>{transferResult.value.in_account_info?.platform == 1 ? 'ios' : transferResult.value.in_account_info?.platform == 2 ? 'android' : '不知'}</div>) } }, { class: 'flex flex-row col-span-4' }],
                            ]}
                          />
                        </div>
                      </x-push-desc-list>

                    )
                  },
                },
                {
                  class: 'p-4',
                },
              ],
              [
                '',
                '',
                {
                  type: 'custom',
                  render: r => {
                    return (
                      <div class=" p-5 bg-gray-100 rounded-lg">
                        <h2 class="font-medium mb-2">转移的资产情况</h2>
                        <div class="flex items-center">
                          <div>
                            {
                              (assetMsgList.value).map(item => (
                                <div class="my-1">
                                  <span class={mc((item.name == '订阅权益' && transferResult.value.transfered_asset?.subscription !== '') && '!inline-block', 'hidden')}><span>{item.name}:</span>：{transferResult.value.transfered_asset?.subscription} (到期时间: <DateTime value={transferResult.value.transfered_asset?.subscription_expire_time ? transferResult.value.transfered_asset?.subscription_expire_time * 1000 : '--'} />)</span>
                                  <span class={mc((item.name == '金币' && transferResult.value.transfered_asset?.coins !== 0) && '!inline-block', 'hidden')}><span>{item.name}:</span>：{transferResult.value.transfered_asset?.coins} coins</span>
                                  <span class={mc((item.name == '奖励币' && (transferResult.value.transfered_asset?.bonus !== 0 || transferResult.value.transfered_asset?.rewards !== 0)) && '!inline-block', 'hidden')}><span>{item.name}:</span>：{transferResult.value.transfered_asset?.bonus} bonus + {transferResult.value.transfered_asset?.rewards} rewards</span>
                                  <span class={mc((item.name == '兑换券' && transferResult.value.transfered_asset?.coupon !== 0) && '!inline-block', 'hidden')}><span>{item.name}:</span>：{transferResult.value.transfered_asset?.coupon}</span>
                                  <span class={mc((item.name == '已解锁的剧集' && transferResult.value.transfered_asset?.unlocked_episode !== 0) && '!inline-block', 'hidden')}><span>{item.name}:</span>：{transferResult.value.transfered_asset?.unlocked_episode}</span>
                                </div>
                              ),
                              )
                            }
                          </div>
                          <div class={mc(1 ? 'text-green-600' : 'text-red-500', 'ml-24')}>
                            {transferResult.value.success == 1 ? '✅ 转移成功' : '❌ 转移失败'}
                          </div>
                        </div>
                      </div>

                    )
                  },
                },
                {
                  class: 'p-4',
                },
              ],
            ]}
          />
        </div>

      </div>
      <div class="flex justify-end gap-x-2 w-[1040px]">
        <Button class="btn btn-primary btn-sm w-28" onClick={() => {
          nextStep()
        }}
        >关闭
        </Button>
      </div>
    </>

  )
})
