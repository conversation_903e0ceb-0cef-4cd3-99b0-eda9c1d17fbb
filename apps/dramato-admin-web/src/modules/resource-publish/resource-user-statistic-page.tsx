/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { ref } from 'vue'
import { ElTabs, ElTabPane } from 'element-plus'
import { ResourceGroupStatistic } from './resource-user-statistic-group-page'
import { ResourceUserTabStatistic } from './resource-user-statistic-tab-page'

type ResourceUserStatisticOptions = {
  props: {}
}

export const ResourceUserStatistic = createComponent<ResourceUserStatisticOptions>({
  props: {},
}, props => {
  const activeName = ref(1)
  return () => (
    <div class="space-y-4">
      <ElTabs v-model={activeName.value}>
        <ElTabPane label="人员统计" name={1}>
          <ResourceUserTabStatistic />
        </ElTabPane>
        <ElTabPane label="各组汇总" name={2}>
          <ResourceGroupStatistic />
        </ElTabPane>
      </ElTabs>
    </div>
  )
})

export default ResourceUserStatistic
