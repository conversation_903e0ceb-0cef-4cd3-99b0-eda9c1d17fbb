import { createComponent, fn, SlotFn } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { RouterView } from 'vue-router'
type Options = {
  slots: {
    default: SlotFn
  }
}
export const OtherLayout = createComponent<Options>(null, (props, { slots }) => {
  return () => (
    <MergeClass tag="x-other-layout" baseClass="block max-w-150 mx-auto">
      <main>
        <RouterView />
      </main>
    </MergeClass>
  )
})

export default OtherLayout
