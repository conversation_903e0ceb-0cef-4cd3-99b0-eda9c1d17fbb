import { collectDefaultMetrics, Histogram, register } from 'prom-client'

// 收集默认指标
collectDefaultMetrics()

// 创建页面响应时间直方图
export const httpRequestDuration = new Histogram({
  name: 'nuxt_page_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 1, 3, 5, 10]
})

register.registerMetric(httpRequestDuration)

// 导出 prometheus register
export { register }
