import { createCachedFn, Key, OptionsToProps } from '@skynet/shared'
import { ref } from 'vue'
import { Dialog, DialogOptions } from './dialog'

type Props = OptionsToProps<DialogOptions>

export const useDialogStore = createCachedFn((id: Key) => {
  const dialogStack = ref<Props[]>([])
  /**
   * 压入 dialog
   * @returns dialog 的 index
   */
  const addDialog = (props: Props) => {
    const length = dialogStack.value.push(props)
    return length - 1
  }

  const removeDialog = (index = -1) => {
    if (index === -1) {
      dialogStack.value.pop()
    } else {
      dialogStack.value.splice(index)
    }
  }

  const renderDialogs = () => {
    return dialogStack.value.map((dialog, index) => (
      <Dialog {...dialog} visible key={index}
        onClose={() => {
          removeDialog(index)
          if (dialog.showParentWhenChildClose && dialogStack.value.length > 0) {
            dialogStack.value[dialogStack.value.length - 1].visible = true
          }
        }}
      />
    ))
  }
  return {
    addDialog,
    removeDialog,
    dialogStack,
    renderDialogs,
  }
})
