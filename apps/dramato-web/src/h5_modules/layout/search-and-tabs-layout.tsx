import { createComponent, fn, SlotFn } from '@skynet/shared'
import { Image } from 'vant'
import { MergeClass } from '@skynet/ui'
import { RouterView, useRoute } from 'vue-router'
import SearchInput from '../search-input/search-input.vue'
import LangSelect from '../lang-select/lang-select.vue'
import Tabs from '../tabs/home-tabs.vue'
import DramaIcon from '../images/drama-icon.webp'
import { useSearchAndTabsLayoutStore } from './use-search-and-tabs-layout-store'
import { useHomePageStore } from '../home/<USER>'
import { KeepAlive, VNode } from 'vue'
import { storeToRefs } from 'pinia'
type Options = {
  slots: {
    default: SlotFn
  }
}
export const SearchAndTabsLayout = createComponent<Options>({ name: 'SearchAndTabsLayout' }, (props, { slots }) => {
  const { mainRef } = useSearchAndTabsLayoutStore()
  const { needChangeBgColor } = storeToRefs(useHomePageStore())
  const route = useRoute()
  return () => (
    <MergeClass tag="x-search-and-tabs-layout" baseClass="max-w-150 mx-auto overflow relative bg-[#0B080B]">
      <div class="flex size-full flex-col">
        { route.path === '/' && (
          <MergeClass class="h-13 z-up fixed left-0 top-0 flex w-full items-center justify-between px-3 pb-3 pt-1.5"
            baseClass={
              needChangeBgColor.value ? 'bg-[#0B080B]' : ''
            }
          >
            <Image src={DramaIcon} class="mr-3 size-[1.54rem] shrink-0" />
            <SearchInput class="flex-1" />
            <LangSelect />
          </MergeClass>
        )}
        <main class="pb-13.5 flex-1 overflow-hidden" ref={mainRef}>
          <RouterView>
            {{
              default: ({ Component }: { Component: VNode }) => (
                <KeepAlive include="HomePage">
                  {Component}
                </KeepAlive>
              ),
            }}
          </RouterView>
        </main>
        <nav class="fixed bottom-0 left-0 w-full bg-[#0B080B] px-3">
          <Tabs />
        </nav>
      </div>
    </MergeClass>
  )
})

export default SearchAndTabsLayout
