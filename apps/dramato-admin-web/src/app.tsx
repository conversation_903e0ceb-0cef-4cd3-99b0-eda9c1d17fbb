import { createComponent } from '@skynet/shared'
import { RouterView } from 'vue-router'
import { useDialogStore, usePopoverStore } from '@skynet/ui'
import { useAlertStore } from '@skynet/ui/alert/use-alert-store'
import { usePopover2Store } from '@skynet/ui/popover/use-popover-2-store'
import { useRoute } from 'vue-router'
import { watch } from 'vue'

export const App = createComponent(null, () => {
  const { renderDialogs, dialogStack } = useDialogStore()
  const { renderPopovers } = usePopoverStore()
  const { renderPopovers: renderPopovers2 } = usePopover2Store()
  const { renderAlerts } = useAlertStore()
  const route = useRoute()

  // 路由变化 清除弹窗
  watch(() => route.path, () => {
    dialogStack.value = []
  })

  return () => (
    <div class="tm-no-dark">
      {/* 所有动态组件都挂载到 mountRoot 中 */}
      <div class="" id="mountRoot">
        { renderDialogs() }
        { renderPopovers() }
        { renderPopovers2() }
        <div class="fixed space-y-4 top-[100px] z-alert left-1/2 transform -translate-x-1/2 items-center justify-center flex-col flex">
          {renderAlerts()}
        </div>
      </div>

      <RouterView />

    </div>
  )
})
