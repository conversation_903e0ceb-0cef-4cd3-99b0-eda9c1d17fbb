{"name": "skynet", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"rm-modules": "find . -type d -name 'node_modules' -exec rm -rf {} \\;", "build": "pnpm -r --filter='./{packages,apps}/*' run build", "//dev": "pnpm -r --parallel --filter='./{packages,apps}/*' run dev", "dev:admin": "pnpm -r --parallel --filter='skylink-admin-web' run dev", "eslint": "eslint", "test": "echo \"Error: no test specified\" && exit 1", "config:sync": "find apps packages -mindepth 1 -maxdepth 1 -type d -exec cp -r .vscode {} \\;"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@unocss/preset-wind": "0.61.3", "eslint": "^8.57.0", "unocss": "catalog:"}, "packageManager": "pnpm@9.5.0+sha256.dbdf5961c32909fb030595a9daa1dae720162e658609a8f92f2fa99835510ca5", "dependencies": {"concurrently": "9.1.2"}, "pnpm": {"patchedDependencies": {"shaka-player@4.11.7": "patches/<EMAIL>"}}}