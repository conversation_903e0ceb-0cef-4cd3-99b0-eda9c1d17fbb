<template>
  <x-login-page
    class="bg-top-center relative flex size-full min-h-screen  flex-col gap-y-2 overflow-y-auto bg-black bg-[url('src/h5_modules/images/login-bg.webp')] bg-cover bg-no-repeat text-[#cccacb]"
  >
    <Back
      :is-white="true"
      title=""
      class="z-100 sticky top-0 flex h-11 w-full shrink-0 items-center bg-transparent font-bold text-[#fdfbfc]"
    />
    <div class="mt-80% flex flex-col items-center">
      <h1 class="w-38.5 h-28">
        <img
          class="size-full"
          src="src/h5_modules/images/logo.webp"
        >
      </h1>
      <div
        class="mt-15 w-78 rounded-50 bg-[#0766ff] bg-[url('src/h5_modules/images/facebook-logo.webp')] bg-[length:1.5rem] bg-[position:0.75rem_center] bg-no-repeat p-3 text-center text-base text-white"
        @click="loginFaceBook"
      >
        {{ T.loginInPage.facebookLogin.ref.value }}
      </div>
      <div
        v-if="!isTikTok()"
        class="w-78 rounded-50 mt-4 bg-[#fdfbfc] bg-[url('src/h5_modules/images/google-logo.webp')] bg-[length:1.5rem] bg-[position:0.75rem_center] bg-no-repeat p-3 text-center text-base text-[#090609]"
        @click="loginGoogle"
      >
        {{ T.loginInPage.googleLogin.ref.value }}
      </div>
      <p class="w-78 mt-7 text-center text-xs text-[#797b7d]">
        {{ T.loginInPage.agreeMsg.ref.value }}
        <span
          class="underline"
          @click="gotoRules('terms')"
        >{{ T.loginInPage.termsOfUse.ref.value }}</span>
        .
        <span
          class="underline"
          @click="gotoRules('privacy')"
        >{{ T.loginInPage.privacyPolicy.ref.value }}</span>
      </p>
    </div>
    <div
      v-if="logoInLoading"
      class="top-50% left-50% absolute ml-[-19px] mt--5"
    >
      <Loading
        color="#ff007a"
        size="38px"
      />
    </div>
  </x-login-page>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { httpClient } from 'src/lib/http-client'
import { userStore } from 'src/h5_modules/common/user-store'
import { showFailToast, Loading } from 'vant';
import { T } from './login-page.i18n'
import { Back } from 'src/h5_modules/common/back/back'
import { useRoute } from 'vue-router'
import { gotoRules } from 'src/h5_modules/common/common'
import { h5Track } from 'src/lib/h5-track';
import { isTikTok, isFacebook } from 'src/lib/ua'
const route = useRoute()

const from = route.query?.from as string || '/';

let goBackPath = from;

// 如果存在series_id，则添加series_id、video_id、video_type参数
if (from.startsWith('/series/')) {
  goBackPath = `${from}${from.indexOf('?') > -1 ? '&' : '?'}series_id=${route.query?.series_id}&video_id=${route.query?.video_id}&video_type=${route.query?.video_type}`;
}

const { login } = userStore();

const logoInLoading = ref(false);

const initializeFacebook = () => {
  // window.fbAsyncInit = function () {
  //   FB.init({
  //     appId: '1031273318485141', // '1041021310761996',
  //     cookie: true,
  //     xfbml: true,
  //     version: 'v1.0'
  //   });
  // };
};
const initializeGoogle = () => {
  // Google SDK 会自动初始化，这里可以放置其他配置
};

onMounted(() => {
  initializeFacebook();
  initializeGoogle();
  tryLogin();
});

const track = (type: string) => {
  h5Track('profile', 'sign_in_platform', 'click', {
    event: 'profile_sign_in_platform_click',
    platform: type,
  })
}

const tryLogin = () => {
  try {
    const tempUrl = new URL(location.origin + '?' + location.hash.slice(1));
    const accessToken = tempUrl.searchParams.get('access_token');
    const state = decodeURIComponent(tempUrl.searchParams.get('state') as string);
    if (!accessToken) {
      return;
    }
    logoInLoading.value = true;
    login('facebook', accessToken as string, state as string, () => {
      logoInLoading.value = false;
    });
  } catch (error) {
    logoInLoading.value = false;
    showFailToast(T.loginInPage.loginFailed.ref.value);
  }
}
const loginFaceBook = () => {
  const redirectUri = encodeURIComponent(location.origin + '/login');
  const client_id = '1031273318485141';
  var t = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${client_id}&state=${encodeURIComponent(goBackPath)}&redirect_uri=${redirectUri}&response_type=token`;
  location.href = t;
  // try {
  //   FB.login(
  //     (response) => {
  //       try {
  //         // if (response.error) {
  //         //   showFailToast(`Facebook Login Error: ${response.error.message}`);
  //         // }

  //         // if (!response.authResponse) {
  //         //   showFailToast('No authResponse received from Facebook');
  //         // }

  //         if (response.authResponse.accessToken) {
  //           logoInLoading.value = true;
  //           login('facebook', response.authResponse.accessToken, goBackPath, () => {
  //             logoInLoading.value = false;
  //           });
  //         } else {
  //           logoInLoading.value = false;
  //           showFailToast('No access token received from Facebook');
  //         }
  //       } catch (error) {
  //         logoInLoading.value = false;
  //         showFailToast(T.loginInPage.loginFailed.ref.value);
  //       }
  //     },
  //     { scope: 'public_profile,email' }
  //   );
  // } catch (error) {
  //   console.error(T.loginInPage.loginFailed.ref.value);
  //   logoInLoading.value = false;
  // }
  track('facebook')
};

const loginGoogle = () => {
  try {
    const client = google.accounts.oauth2.initTokenClient({
      client_id: '************-krdi8sbn4a38628shpi24k7nkrrkb0dr.apps.googleusercontent.com',
      scope: 'openid email profile',
      extraParams: {
        response_type: 'token id_token'
      },
      callback: async (response) => {
        try {
          if (response.error) {
            logoInLoading.value = false;
            throw new Error(`Google OAuth Error: ${response.error}`);
          }

          if (response.access_token) {
            logoInLoading.value = true;
            await login('google', response.access_token, goBackPath as string);
            logoInLoading.value = false;
          } else {
            logoInLoading.value = false;
            throw new Error('No access token received');
          }
        } catch (error) {
          showFailToast(T.loginInPage.loginFailed.ref.value);
        }
      }
    });

    client.requestAccessToken();
  } catch (error) {
    logoInLoading.value = false;
    console.error(T.loginInPage.loginFailed.ref.value);
  }
  track('google')
};
</script>

<style scoped></style>