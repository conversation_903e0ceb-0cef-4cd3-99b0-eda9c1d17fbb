import { createComponent, exportAsCsv, filterIt, splitIt } from '@skynet/shared'
import { Button, TableColumnOld, CreateForm, CreateTableOld, DateTime, Image, Pager, transformInteger, transformTimestamp } from '@skynet/ui'
import { Icon } from '@skynet/ui/icon/icon'
import { set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { mediaList } from './const'
import { apiGetMaterialList } from './material-api'

type DramaMaterialOptions = {
  props: {}
}

export const MaterialPage = createComponent<DramaMaterialOptions>({
  props: {},
}, props => {
  const route = useRoute()
  const pageInfo = reactive({
    next: '',
    page: 1,
    pageSize: 20,
    total: 0,
  })
  const loading = ref(false)
  const list = ref<M.IDramaMaterial[]>()
  const form = ref({
    app_ids: '',
    media: [],
    playlet_name: route.query.playlet_name as string || '',
    search_type: 1,
    search_key: route.query.search_key as string || '',
    start_time: undefined,
    end_time: undefined,
  })
  const order_column = ref('exposure_num')
  const order_type = ref('desc')

  const Table = CreateTableOld<M.IDramaMaterial>()
  const QueryForm = CreateForm<{
    media: string[]
    playlet_name: string
    country?: number
    // 1: 精准搜索 2: 模糊搜索
    search_type?: 1 | 2 | number
  }>()
  const columns: TableColumnOld<M.IDramaMaterial>[] = [
    ['入库时间', row => <DateTime value={row.date_time * 1000} />, { class: 'w-[150px]' }],
    ['素材预览', row => {
      if (!row.video_list) {
        return '❌无视频'
      }
      return row.pic_list && <div class="cursor-pointer" onClick={() => window.open(row.video_list)}><Image src={row.pic_list} /></div>
    }, {
      class: 'w-[200px]',
    }],
    ['素材标题', 'title1', { class: 'w-[200px]' }],
    ['搜索剧名', 'search_key', { class: 'w-[200px]' }],
    ['素材描述', 'title2', { class: 'w-[200px]' }],
    ['预估曝光量', 'exposure_num', { class: 'w-[140px]' }],
    ['下载数量', 'download_num', { class: 'w-[100px]' }],
    ['投放媒体', 'medias', { class: 'w-[200px]' }],
    ['短剧名称', 'playlet_name', { class: 'w-[200px]' }],
    ['产品名称', 'product_name', { class: 'w-[200px]' }],
    ['发行商名称', 'pubilsher_name', { class: 'w-[200px]' }],
    ['广告计划数量', 'creative_num', { class: 'w-[100px]' }],
    ['播放数量', 'play_num', { class: 'w-[100px]' }],
    ['点赞数量', 'like_num', { class: 'w-[100px]' }],
    ['评论数量', 'comment_num', { class: 'w-[100px]' }],
    ['转发数量', 'forward_num', { class: 'w-[100px]' }],
    ['在看数量', 'is_watch', { class: 'w-[100px]' }],
    ['投放天数', 'material_days', { class: 'w-[100px]' }],
    ['出现次数', 'material_num', { class: 'w-[100px]' }],
    ['首次出现时间', 'material_first_seen', { class: 'w-[200px]' }],
  ]

  const getList = async () => {
    const params = {
      ...form.value,
      page: pageInfo.page,
      page_size: pageInfo.pageSize,
      media: form.value.media.join(',') ? form.value.media.join(',') : '全部',
      order_column: order_column.value,
      order_type: order_type.value,
      start_time: form.value.start_time || 0,
      end_time: form.value.end_time || 0,
    }
    loading.value = true
    try {
      const { data } = await apiGetMaterialList(params, {
        transformRequestData: {
          app_ids: [splitIt([',', ';', '，', '；']), filterIt(Boolean)],
        },
      })
      list.value = data?.records
      pageInfo.total = data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const onQuery = async () => {
    pageInfo.page = 1
    await getList()
  }

  const onReset = async () => {
    form.value.media = []
    await onQuery()
  }

  const onPageChange = async () => {
    await getList()
  }
  const onPageSizeChange = async (n: number) => {
    pageInfo.pageSize = n
    await onQuery()
  }

  const sortItems = ref([{ value: 'creative_num', label: '广告计划数量' }, { value: 'play_num', label: '播放数量' }, { value: 'like_num', label: '点赞数量' }, { value: 'comment_num', label: '评论数量' }, { value: 'forward_num', label: '转发数量' }, { value: 'exposure_num', label: '预估曝光量' }, { value: 'download_num', label: '下载数量' }, { value: 'is_watch', label: '在看数量' }, { value: 'material_days', label: '投放天数' }, { value: 'material_num', label: '出现次数' }, { value: 'material_first_seen', label: '首次出现时间' }])

  const toggleSort = async (value: string) => {
    if (order_column.value === value) {
      order_type.value === 'desc' ? order_type.value = 'asc' : order_type.value === 'asc' ? order_type.value = '' : ''
      if (order_type.value === '') {
        order_column.value = ''
      }
    } else {
      order_column.value = value
      order_type.value = 'desc'
    }
    await onQuery()
  }
  const isItemSort = (value: string) => value === order_column.value
  const isItemSortAsc = (value: string) => {
    return value === order_column.value && (order_type.value === 'desc')
  }

  onMounted(async () => {
    await getList()
  })

  const onClickExport = () => {
    const data = [
      ['素材链接', '素材标题', '素材描述', '投放媒体', '短剧名称', '产品名称', '发行商名称', '广告计划数量', '播放数量', '点赞数量', '评论数量', '转发数量', '预估曝光量', '下载数量', '在看数量', '投放天数', '出现次数', '首次出现时间'],
    ].concat(
      list.value?.map(item => [
        item.video_list, item.title1, item.title2, item.medias, item.playlet_name, item.product_name, item.pubilsher_name,
        item.creative_num, item.play_num, item.like_num, item.comment_num, item.forward_num, item.exposure_num, item.download_num,
        item.is_watch, item.material_days, item.material_num, item.material_first_seen,
      ].map(i => JSON.stringify(i))) ?? [],
    )
    exportAsCsv(data, '短剧素材.csv')
  }

  return () => (

    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>短剧素材</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full flex flex-row"
            submitText="搜索"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              if (path === 'playlet_name') {
                set(form.value, path, (value as string).trim())
              } else {
                set(form.value, path, value)
              }
            }}
            items={[
              [<span>应用名称<small class="ml-1 text-gray-500">用逗号或分号分隔</small></span>, 'app_ids', { type: 'text' }],
              ['媒体', 'media', {
                type: 'multi-select', search: { debounce: 20, placeholder: '搜索' }, options: mediaList.map(name => {
                  return { value: name, label: name }
                }) }],
              ['短剧名称', 'playlet_name', { type: 'text' }],
              ['素材标题', 'search_key', { type: 'text' }],
              ['入库开始时间', 'start_time', { type: 'datetime', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD HH:mm' }, {
                transform: transformTimestamp,
              }],
              ['入库结束时间', 'end_time', { type: 'datetime', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD HH:mm' }, {
                transform: transformTimestamp,
              }],
              ['国家', 'country', { type: 'text' }, { transform: transformInteger }],
              ['搜索类型', 'search_type', { type: 'radio', options: [{ value: 1, label: '精准搜索' }, { value: 2, label: '模糊搜索' }] }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex justify-between items-center">
            <div class="space-x-1 py-2 flex flex-nowrap items-center overflow-auto">
              <div class="flex-none">排序：</div>
              {
                sortItems.value.map(item => {
                  return (
                    <div class="cursor-pointer p-2 flex-none" onClick={() => toggleSort(item.value)}>
                      <div class="flex items-center gap-1">
                        <span class={isItemSort(item.value) ? 'font-bold text-gray-800' : 'font-normal'}>{item.label}</span>
                        <div>
                          {
                            order_column.value === item.value
                              ? !isItemSortAsc(item.value) ? <Icon name="ci:sort-descending" /> : <Icon name="ci:sort-ascending" />
                              : ''
                          }
                        </div>
                      </div>
                    </div>
                  )
                })
              }
            </div>
            {/* <div>
              <Button class="btn btn-sm btn-outline" onClick={onClickExport}>导出为 CSV</Button>
            </div> */}

          </x-table-actions>
        ),
        table: () => (
          <Table
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />

        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.page} v-model:size={pageInfo.pageSize} total={pageInfo.total} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default MaterialPage
