import { createComponent, SlotFn } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
type LoadingOptions = {
  props: { }
  slots: {
    default?: SlotFn
  }
}
export const Loading = createComponent<LoadingOptions>({
  props: {},
}, (props, { slots }) => {
  return () => (
    <MergeClass tag="x-loading" baseClass="flex flex-col justify-center items-center min-h-[300px] bg-[var(--fill-4)] rounded-lg my-6">
      <div class="animate-spin rounded-full h-8 w-8 border-4 border-[var(--fill-3)] border-t-[var(--brand-1)]" />
      <div class="text-sm text-center p-4">
        {slots.default?.(props) ?? '加载中...'}
      </div>
    </MergeClass>
  )
})

export default Loading
