import { onMounted } from 'vue'
import { DramaItem } from './drama-item.tsx'
import searchHotIcon from 'src/h5_modules/images/search-hot-icon.webp'
import { createComponent } from '@skynet/shared'
import { useSearch } from './use-search.ts'
import { T } from './search.i18n.ts'

export const TrendingSearches = createComponent(null, () => {
  const { hotDramatoList, getHotDramatoList } = useSearch()
  onMounted(() => {
    getHotDramatoList()
  })
  return () => (
    hotDramatoList.value.length > 0 ? (
      <x-trending-searches class="block px-3 pb-4">
        <div class="flex items-center py-3">
          <img width={20} height={20} class="mr-1 size-5" src={searchHotIcon} />
          <h2 class="text-base font-bold leading-tight text-[#fdfbfc]">{T.search.trendingSearches()}</h2>
        </div>
        <div class="flex flex-col gap-y-3 py-3" style={{
          paddingBottom: 'calc(env(safe-area-inset-bottom) + 20px)',
        }}>
          {
            hotDramatoList.value.map((dramato, idx) => (
              <DramaItem key={dramato.id} dramato={dramato} bigMode rank={idx + 1} from="/trending_search" />
            ))
          }
        </div>
      </x-trending-searches>
    ) : null
  )
})

export default TrendingSearches
