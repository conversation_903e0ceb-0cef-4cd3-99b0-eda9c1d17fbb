export const _in = {
  // 通用
  common: {
    title: 'To<PERSON>',
  },
  // 分享页
  share: {
    watchNow: 'Tonton Sekarang',
    episodeDialog: {
      title: 'Bergabunglah dengan saya dan dapatkan koin ekstra!',
      description: 'Saya ketagihan drama ini!',
      tips: 'Rekomendasi',
      button: 'Tonton drama lengkapnya',
      limit24: 'Waktu terbatas: 24h',
    },
    toast: {
      takeOffShelf: 'Tidak Ada Konten yang Tersedia',
    },
  },
  'customer-service-center': {
    title: 'Pusat Layanan Pelanggan',
    description: 'Deskripsi',
    'description-placeholder': 'Berikan rincian untuk membantu kami memahami masalahnya',
    'email-placeholder': 'Masukkan email Anda untuk menerima pembaruan umpan balik',
    'submit-feedback': 'Kirim',
    'enter-feedback': 'Umpan balik masalah lainnya',
    'upload-pictures': 'Unggah Gambar',
    feedback: 'Umpan Balik',
    faq: {
      question1: 'Mengapa dikatakan \'Iklan Belum Siap\'?',
      answer1: 'Terkadang, koneksi server atau pengiklan yang tidak stabil dapat menyebabkan masalah. Ka<PERSON> sarankan untuk mencoba kembali nanti dan tidak terlalu sering mengeklik "Tonton Iklan". Batasan iklan mungkin berlaku karena kebijakan pihak ketiga atau pembatasan negara Anda. Kami terus bekerja sama dengan penyedia iklan untuk meningkatkan pengalaman menonton iklan Anda.',
      question2: 'Mengapa dikatakan \'Jaringan Tidak Tersedia\' untuk iklan?',
      answer2: 'Coba mulai ulang aplikasi dengan menutupnya sepenuhnya lalu buka kembali. Jika iklan masih belum dimuat, tunggu beberapa saat sebelum mencoba lagi, dan hindari mengklik terlalu sering. Jika masalah masih berlanjut, Anda dapat menghubungi kami melalui sistem Umpan Balik Kami juga bekerja sama dengan tim iklan kami untuk meningkatkan pengalaman menonton. Terima kasih atas kesabaran Anda.',
    },
  },
  toast: {
    'upload-failed': 'Gagal Mengunggah, Silakan Unggah Lagi',
    'submit-success': 'Berhasil dikirim',
    'email-incorrect': 'Format email salah',
  },
  not_found: {
    title: 'Halaman yang Anda cari tidak ada',
  },
  regulations: {
    howUseData: 'Bagaimana Kami Menggunakan Data Anda',
    content: `Jika Anda memilih "Izinkan" pada permintaan izin Transparansi Pelacakan Aplikasi, kami akan menggunakan informasi dan data Anda untuk mengoptimalkan layanan kami dan memberikan pengalaman terbaik kepada Anda, jika tidak, kami hanya akan mengumpulkan data dan tidak akan melakukan aktivitas pelacakan apa pun.

Jika Anda setuju untuk mengizinkan DramaWave dan mitra serta pemasok kami menggunakan data dari perangkat Anda untuk menganalisis bagaimana Anda menggunakan aplikasi, mempersonalisasi rekomendasi konten, dan menyediakan lebih banyak layanan terkait informasi, silakan klik "Setuju dan Lanjutkan" untuk melanjutkan.`,
    acceptAll: 'Terima Semua',
    dataSettings: 'Pengaturan Data',
    next: 'Berikutnya',
    confirm: 'Konfirmasi',
    aboutYourPrivacy: 'Tentang Privasi Anda',
    personalDataName: 'Data yang Dipersonalisasi',
    personalDataDescription: 'Kami menggunakan informasi ini untuk mengingat pilihan Anda dan memberikan rekomendasi iklan dan konten yang dipersonalisasi, pemberitahuan, dan pengingat, dll.',
    advertisementDataName: 'Data Iklan',
    advertisementDataDescription: 'Cookie, pengidentifikasi perangkat Anda, atau informasi lain dapat disimpan atau diakses di perangkat Anda untuk tujuan iklan online. Data pengguna juga dapat dibagikan dengan mitra iklan kami.',
    behaviorsDataName: 'Data Perilaku',
    behaviorsDataDescription: 'Data terkait analitik, seperti cookie atau pengidentifikasi perangkat, dapat disimpan untuk mengukur dan menganalisis penggunaan, perilaku, dan kinerja iklan.',
    privacyAndTerms: 'Kebijakan Privasi dan Ketentuan Layanan',
    referToPrivacy: 'Silakan merujuk ke Kebijakan Privasi',
    referToTerms: 'Silakan merujuk ke Ketentuan Layanan',
    agreeAndSave: 'Setuju & Simpan',
  },
  coins: {
    earnings: {
      title: 'Penghasilan Saya',
      total: 'Total Koin',
      exchange: 'Pertukaran',
      earningsHistory: 'Riwayat Penghasilan',
      exchangeHistory: 'Riwayat Pertukaran',
      noRecords: 'Tidak ada catatan',
      last50earnings: 'Hanya 50 penghasilan terakhir yang ditampilkan',
      last50exchanges: 'Hanya 50 catatan penukaran terakhir yang ditampilkan',
      benefits: 'Manfaat',
      gotIt: 'Mengerti',
    },
    exchange: {
      title: 'Zona Penukaran',
      notOpen: 'Belum dibuka, mohon nantikan',
      receiveBtn: 'Terima dengan Senang Hati',
      earnCoins: 'Dapatkan Koin',
      redeemRequiredXCoins: 'Penukaran membutuhkan {X} koin',
      exchangeSuccess: 'Berhasil Ditukar',
      cardId: 'ID KARTU',
      exchangeFailed: 'Penukaran Gagal',
      transferFailedInsufficientStock: 'Habis. Silakan datang lebih awal besok.',
      gotItBtn: 'Mengerti',
      transferFailedRiskControl: 'Akun tidak biasa, sementara tidak dapat menukar.',
      tryAgainBtn: 'Coba Lagi Nanti',
      transferConfirm: 'Konfirmasi Transfer',
      paypalAccountEmail: 'Email Akun PayPal',
      confirmPaypalAccountEmail: 'Konfirmasi Email Akun PayPal',
      transferX: 'Transfer {X}',
      transferXConfirm: 'Konfirmasi Transfer {X}',
      transferToYourAccountX: 'Transfer ke akun Anda {X}',
      confirmBtn: 'Konfirmasi',
      transferSuccessTitle: 'Kami sedang mengirimkan uang ke akun PayPal Anda!',
      transferSuccessContentX: 'Uang Anda akan tiba dalam waktu 5 hari ke akun {X}',
      copySuccess: 'Salin Berhasil',
      enterEmailError: 'Masukkan alamat email yang valid',
      enterEmailValidError: 'Kesalahan input, harap masukkan kembali',
      emailNotMatchError: 'Alamat email tidak sesuai, harap masukkan kembali',
    },
  },
  my_wallet: {
    title: 'Dompet Saya',
    help_dialog: {
      title: 'Aturan Penggunaan Tiket',
      content1: `1. 1 Pembelian tiket= 1 Hadiah dari pembelian tiket = 1 Hadiah AD tiket = 1 Penukaran koin tiket – Semua ini dapat digunakan untuk membuka episode.`,
      content2: `2. Semua tiket membuka hak menonton episode yang tidak akan kedaluwarsa, tetapi Tiket Hadiah yang Dibeli akan kedaluwarsa setelah periode tertentu.`,
      content3: `3. Pembelian Tiket dapat diperoleh melalui pembelian langsung.`,
      content4: `4. Hadiah dari pembelian tiket hanya dapat diperoleh selama periode promosi tertentu melalui pembelian.`,
      content5: `5. Hadiah AD tiket dapat diperoleh dengan menyelesaikan tugas menonton iklan.`,
      content6: `6. Penukaran koin tiket dapat diperoleh dengan penukaran koin.`,
    },
    account_balance: 'Saldo Akun',
    purchased_tickets: 'Pembelian Tiket',
    purchase_reward_tickets: 'Hadiah dari Pembelian Tiket',
    coin_exchange_tickets: 'Penukaran Koin Tiket',
    ad_reward_tickets: 'Hadiah AD Tiket',
    top_up: 'Isi Ulang',
    purchase_history: 'Riwayat Pembelian',
    rewards_history: 'Riwayat Hadiah',
    exchange_history: 'Riwayat Penukaran',
    consumption_records: 'Jejak Penggunaan',
    auto_unlock: 'Episode pada Buka Kunci Otomtais',
  },
  top_up: {
    exchange_title: 'Penukaran Koin',
    purchase_title: 'Pembelian',
    tickets: 'Tiket',
    coins: 'Koin',
    balance: 'Saldo',
    title: 'Simpan',
    tips: 'Tips',
    tips_1: '1. Platform kami menawarkan drama pendek gratis dan drama akses lebih awal secara premium.',
    tips_2: '2. Untuk menonton konten akses awal terlebih dahulu, Anda perlu menukarkannya dengan Tiket. Tiket ini dapat diperoleh melalui pembelian, penukaran koin, atau dengan menonton iklan.',
    tips_3: '3. Semua tiket membuka hak menonton episode yang tidak akan kedaluwarsa, tetapi Tiket Hadiah yang Dibeli akan kedaluwarsa setelah periode tertentu.',
    tips_4: '4. Hadiah dari pembelian tiket akan digunakan terlebih dahulu saat membuka episode. Jika saldo tidak mencukupi, tiket reguler Anda akan digunakan secara otomatis.',
    tips_5: '5. Jika pembayaran isi ulang Anda berhasil tetapi saldo Anda belum diperbarui, silakan muat ulang/refresh dompet Anda.',
    tips_6: '6. Untuk pertanyaan lain, jangan ragu untuk menghubungi kami melalui "Profil" -> "Pusat Layanan Pelanggan" -> "Masukkan".',
    exchange_dialog_title: 'Apakah Anda yakin ingin bertukar?',
    redeem_required: 'Tebus diperlukan',
    exchange_success: 'Berhasil menukar xxx Tiket',
    balance_not_enough: 'Saldo tidak cukup',
    exchange_failed: 'Pertukaran gagal',
  },
  my_wallet_in: {
    transaction_history: 'Riwayat Transaksi',
    EPnum: 'EP{num}',
    Expired: 'Berakhir',
    AD: 'Iklan',
    Expires_on_num: 'Kadaluarsa pada {num}',
    Thats_all_for_now: 'Di atas adalah semua isi konten',
    consumption_records: 'Jejak Penggunaan',
  },
  meal_check_in: {
    sleeping_link: 'Dapatkan Koin Saat Tidur',
    rules_title: 'Aturan Aktivitas',
    benefits_title: 'Benefits',
    rules_1: '1. Setiap hari, ada empat waktu untuk Anda mencatat makanan Anda: Sarapan: 07:00-10:00; Makan Siang: 11:00-14:00; Makan Malam: 17:00-20:00; Camilan Malam: 21:00-24:00.',
    rules_2: '2. Jika Anda melewatkan waktu pencatatan yang sesuai, Anda bisa menonton video untuk mengklaim hadiah.',
    rules_3: '3. Hadiah akan direset setiap hari pada tengah malam, jadi jangan lupa untuk mengklaim hadiah Anda.',
  },
  sleep_check_in: {
    earn_link: 'Catat makanan untuk mendapatkan',
    benefits_title: 'Benefits',
    rules_1:
      '1. Saat ini, dari pukul 7 malam hingga 12 malam setiap malam, anda boleh mengaktifkan mod \'Tidur untuk Menjana\'.',
    rules_2:
      '2. Setelah mengaktifkan, pada keesokan harinya, selepas tidur selama 8 jam, anda boleh menuntut ganjaran tidur antara pukul 8 pagi hingga 12 tengah hari.',
    rules_3:
      '3. Jika anda terlepas masa untuk menuntut ganjaran, anda boleh menonton video antara pukul 12 tengah hari hingga 7 malam pada keesokan harinya untuk menuntut ganjaran.',
  },
}
