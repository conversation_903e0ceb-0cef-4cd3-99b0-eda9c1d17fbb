import { httpClient } from 'src/lib/http-client'

export const apiNewUserStrategyList = (data: M.UserEngagement.List.Request) =>
  httpClient.post<ApiResponse< M.UserEngagement.List.Response>>('/newuser/strategy/list', data)

export const apiCreateNewUserStrategy = (data: M.UserEngagement.Save.Request) =>
  httpClient.post<ApiResponse<boolean>>('/newuser/strategy/create', data)

export const apiNewUserStrategyDetail = (data: { id: number }) =>
  httpClient.get<ApiResponse<M.UserEngagement.Save.Request>>('/newuser/strategy/detail', data)

export const apiUpdateNewUserStrategy = (data: M.UserEngagement.Save.Request) =>
  httpClient.post<ApiResponse<boolean>>('/newuser/strategy/update', data)

export const apiNewUserStrategyOption = (data: M.UserEngagement.option.Request) =>
  httpClient.post<ApiResponse<boolean>>('/newuser/strategy/option', data)
