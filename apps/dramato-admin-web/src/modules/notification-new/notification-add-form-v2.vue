<template>
  <el-form :model="form">
    <el-form-item label="push类型" :label-width="formLabelWidth">
      <el-select v-model="form.rec_type" placeholder="请选择">
        <el-option :label="i.desc" :value="i.rec_type" v-for="i in pushConfig?.list_rec_type_list[form.list_type]"
          :key="i.rec_type"></el-option>
        <!-- <el-option label="签到提醒" :value="23" v-if="form.list_type === 2"></el-option>
                <el-option label="订阅到期次日" :value="19" v-if="form.list_type === 2"></el-option>
                <el-option label="订阅到期5天" :value="20" v-if="form.list_type === 2"></el-option>
                <el-option label="免费部分完播" :value="21" v-if="form.list_type === 2"></el-option>
                <el-option label="充值用户" :value="22" v-if="form.list_type === 2"></el-option>

                <el-option label="继续观看" :value="3" v-if="form.list_type === 3"></el-option>
                <el-option label="高付费剧" :value="16" v-if="form.list_type === 3"></el-option>
                <el-option label="限免剧" :value="17" v-if="form.list_type === 3"></el-option>
                <el-option label="折扣剧" :value="18" v-if="form.list_type === 3"></el-option>
                <el-option label="预约新剧" :value="24" v-if="form.list_type === 3"></el-option> -->
      </el-select>
    </el-form-item>
    <el-form-item label="Push标题" :label-width="formLabelWidth" props="style_config.title">
      <el-input @input="() => turnToUnTranslate([1, 2])" v-model="form.style_config.title" :maxlength="titleMaxLength"
        show-word-limit autocomplete="off" />
    </el-form-item>
    <el-form-item label="Push内容" v-if="form.list_type === 2" :label-width="formLabelWidth" props="style_config.body">
      <el-input @input="() => turnToUnTranslate([1, 2])" v-model="form.style_config.body" :maxlength="contentMaxLength"
        show-word-limit type="textarea" />
    </el-form-item>
    <el-form-item label="" :label-width="formLabelWidth">
      <span class="text-xs text-gray-400">{{ form.list_type === 2 ?
        '提示：1.请填写英文！2.引用变量时请按如下格式填写（请务必保持规范）：\{\{coin\}\}' : titleTip }}</span>
      <div class="flex w-full justify-between"><span>翻译状态： {{ checkIsTranslate(1) ? '已翻译' : '未翻译' }}
          <el-icon class="translate-y-0.5">
            <CircleCheckFilled v-if="checkIsTranslate(1)" />
            <CircleCloseFilled v-else />
          </el-icon>
        </span><el-button
          @click="() => getTranslateContent(form.list_type === 2 ? [form.style_config.title, form.style_config.body] : [form.style_config.title], form.list_type === 2 ? [1, 2] : [1])"
          type="primary">一键翻译</el-button>
      </div>
    </el-form-item>
    <!-- 翻译弹窗 -->
    <el-dialog v-model="translateVisible" width="500" title="一键翻译" append-to-body>
      <el-table v-loading="translateLoading" :data="translateContent" style="width: 100%" stripe>
        <el-table-column prop="lang" label="语言" width="200"></el-table-column>
        <el-table-column v-for="tc in translateCol" :prop="tc.value" :label="tc.name" width="200">
          <template #default="{ row }">
            <el-input v-model="row[tc.value]" placeholder="请输入" />
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="translateVisible = false">放弃</el-button>
          <el-button type="primary" @click="confirmTranslate">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-form>
</template>

<script setup lang="ts">
import { CircleCloseFilled, CircleCheckFilled } from '@element-plus/icons-vue'
import { computed, reactive, ref } from 'vue';
import { useNotification } from './use-notification';
import { ElMessage, FormRules } from 'element-plus';

const { pushConfig, getTranslate, onCreate, form, searchForm } = useNotification('add-dialog')
const formLabelWidth = '80px';
const translateVisible = ref(false)
const translateLoading = ref(false)
const translateContent = ref<Record<string, string>[]>([])
const translateCol = ref<{ name: string, value: string }[]>([])
const translateSource = ref<{ translate: Record<string, string>, type: M.TranslateType }[]>([])

defineExpose({
  submit: async () => {
    console.log(form)
    searchForm.value.list_type = form.value.list_type
    return await onCreate({
      ...form.value,
    })
  }
})

const titleTip = computed(() => {
  return pushConfig.value?.form.title_input.tips
})
const titleMaxLength = computed(() => {
  return pushConfig.value?.form.title_input.limit.max
})
const contentMaxLength = computed(() => {
  return pushConfig.value?.form.content_input.limit.max
})


const getTranslateContent = async (content: string[], type: M.TranslateType[]) => {
  // const isSomeEmpty = content.some((item) => item === '')
  // if (isSomeEmpty) {
  //     ElMessage.error('翻译内容不能为空')
  //     return
  // }
  translateVisible.value = true
  translateLoading.value = true
  const task = content.map((item, index) => {
    return getTranslate(item, type[index])
  })
  const res = await Promise.all(task)

  const filteredRes = res.filter((item): item is { type: number, translate: Record<string, string> } => item !== undefined)
  translateSource.value = filteredRes
  setTranslateContent(filteredRes)
  translateLoading.value = false
}

const typeMap = {
  1: { name: 'Push标题', value: 'title' },
  2: { name: 'Push内容', value: 'body' },
  3: { name: 'tips', value: 'tips' },
  4: { name: '按钮1', value: 'btn1_text' },
  5: { name: '按钮2', value: 'btn2_text' }
}
// 将   { type: 1, "translate":{"en":"","ja":"xxx","ko":"xxx"}} 转换为 { lang: 'en', title: 'xxx', body: 'xxx' }
const setTranslateContent = (res: {
  type: number,
  translate: Record<string, string>
}[]) => {
  const translateMap = new Map();
  // 设置表头 { name: 'Push标题', value: 'title' }
  translateCol.value = res.map(item => {
    translateMap.set(typeMap[item.type as keyof typeof typeMap].value, item.translate)
    return { name: typeMap[item.type as keyof typeof typeMap].name, value: typeMap[item.type as keyof typeof typeMap].value }
  })
  // 设置内容 { lang: 'en', title: 'xxx', body: 'xxx' }
  translateContent.value = Object.keys(res[0].translate).map(lang => {
    const obj: Record<string, string> = { lang }
    translateCol.value.map(tc => {
      obj[tc.value] = translateMap.get(tc.value)[lang]
    })
    return obj
  })
  // 设置表头
}

const confirmTranslate = () => {
  if (translateLoading.value) {
    ElMessage.error('正在翻译中，请稍后')
    return
  }
  // 检查newTranslateList中的translate是否有空值
  const hasVoidValue = translateContent.value.some(item => Object.values(item).some(v => !v))
  if (hasVoidValue) {
    ElMessage.error('翻译内容不能为空, 请填写内容')
    return
  }
  // 更新form
  form.value.translate_list = form.value.translate_list.filter((item: { type: number }) => !translateSource.value.some((ts: { type: number }) => ts.type === item.type))

  const newTranslateList = translateSource.value.map(item => {
    return {
      text: form.value.style_config[typeMap[item.type].value as keyof typeof form.value.style_config] || '',
      type: item.type,
      translate: getTranslateFromTable(item.type) // 使用用户可能修改的结果
    }
  })

  form.value.translate_list = form.value.translate_list.concat(newTranslateList)
  translateVisible.value = false
}

const getTranslateFromTable = (type: number) => {
  return translateContent.value.reduce((acc, item) => {
    acc[item.lang] = item[typeMap[type as keyof typeof typeMap].value]
    return acc;
  }, {})
}

const checkIsTranslate = (type: number) => {
  return form.value.translate_list?.some((item: { type: number }) => item.type === type)
}

// 输入框内容变化时，将对应的翻译状态置为未翻译
const turnToUnTranslate = (type: number[]) => {
  form.value.translate_list = form.value.translate_list.filter((item: { type: number }) => !type.includes(item.type))
}
</script>

<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>