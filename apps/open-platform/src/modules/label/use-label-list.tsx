import { ref } from 'vue'
import { labelApi } from './label-api'
import { bindLoading } from '@skynet/shared'
import { i18n } from 'src/locales'

const labelList = ref<M.Label[]>([])
const fetchingLabelList = ref(false)
const fetchLabelList = async (params?: { language_code?: string }) => {
  const { language_code = i18n.global.locale.value == 'en-US' ? 'en' : 'zh-CN' } = params || {}
  const response = await bindLoading(labelApi.getList({ language_code }), fetchingLabelList)
  labelList.value = response.list
}
export const useLabelList = () => {
  return {
    labelList,
    fetchLabelList,
    fetchingLabelList,
  }
}
