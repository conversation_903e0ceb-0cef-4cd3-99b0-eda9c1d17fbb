declare namespace M {

  type ResourceLabelType = 0 | 1 | 2
  interface IResourceQueryParams {
    series_resource_id?: number
    series_resource_id_list?: string[]
    series_key_list?: string[]
    title: string
    status?: number // 1 正常 2 已归档 3 已删除
    page_index: number
    page_size: number
  }

  interface IResource {
    id: number
    rangking: number
    playlet_name: string
    cover_url: string
    playlet_tags: string
    material_cnt: number
    release_day: number
    country_ids: string
    product_ids: string
    spide_time: string
    rank_type: string
    created: string
    updated: string
  }

  interface IResourceColumn {
    [Record: string]: string | number | { [record: string]: string | number }
  }

  interface IResourceSeriesParams {
    series_resource_id: number
    page_size: number
    page_index: number
    src_lang?: string
    audio_type?: number
  }

  interface IResourceDrama {
    serial_number: number
    no_audio_path: string
    origin_path: string // 原始集数
    pure_path: string // 纯净单集路径
    cn_subtitle_path: string // 中文字幕地址
    en_subtitle_path: string // 英语字幕路径
    en_episode_path: string // 内嵌英语剧集路径
    ja_subtitle_path: string // 日语字幕路径
    ja_episode_path: string // 日语剧集路径
    es_subtitle_path: string // 西语字幕路径
    es_episode_path: string // 西语剧集路径
    ko_subtitle_path: string // 韩语字幕路径
    ko_episode_path: string // 韩语剧集路径
    pt_subtitle_path: string // 葡萄牙剧集路径
    pt_episode_path: string // 葡萄牙剧集路径
    fr_subtitle_path: string // 法语牙剧集路径
    fr_episode_path: string // 法语剧集路径
    id_subtitle_path: string // 印度尼西亚字幕路径
    id_episode_path: string // 印度尼西亚剧集路径
    airole_subtitle_path: string // ai翻译
    region?: M.Dimension
    origin_compress?: 0 | 1
    pure_compress?: 0 | 1
    [record?: string]: any
  }

  interface ISubtitleShortenTask {
    err_msg: string
    language: string
    serial_number: number
    // "status": 4, // 任务状态：0-未开始，1-进行中，2-已完成，3-失败，其中0和1都可以算做进行中
    status: 0 | 1 | 2 | 3
  }

  interface ISourceInfo {
    clazz?: string
    type?: string
    count: number | undefined
    title?: string
    disabled?: boolean
    btnHidden?: boolean
    release_round?: ResourceLabelType | number
    resource_type?: ResourceLabelType | number
    free_online_time?: string | number
    unlocked_episodes?: number
    serialize_status?: number
    labels?: string
    label_ids?: string
  }

  interface ITag {
    language_code: string
    label_id: number
    content: string
    status?: 1 | 2 | number
    type?: string
    meaning?: string
    label_type?: number
    content_type?: number
  }

  interface IEpisodeSeriesInfo {
    language_code: string
    title: string
    title2?: string
    description: string
    cover: string
    cover2?: string
    is_sync: 0 | 1 | number
    horizontal_cover: string
    color_style?: number
    psd_cover?: string
    push_content_list: Array<{
      id?: number
      content: string
      is_delete: boolean
    }>
  }

  interface IDiskInfo {
    series_resource_id: number
    pure_video_path: string
    origin_video_path: string
    subtitle_path: string
    subtitle_language_code: string
    cover_path: string
  }

  interface IDiskInfoUploadProgress {
    serial_number: number
    netdisk_type: 'cover' | 'source' | 'clear' | string // 网盘文件类型 source:原始剧集 clear:纯净剧集 cover:封面 zh_subtitle:简体中文字幕 en_subtitle:英文字幕
    sync_status: 1 | 2 | 3 | 4 | number// 同步状态 1:未同步 2:开始同步 3:已同步 4 失败
    err_msg: string
  }

  interface IAlterSubtitle {
    serial_number: number
    language_code: string
    operate_user: string
    operate_time: number
    subtitle_is_alter: number // 1 已修改
  }

  interface IExternalTransProgress {
    serial_number?: number
    audio_external_status?: number // 音频外挂状态 0：未外挂 1：外挂中 2：外挂成功 3：外挂失败
    play_path?: string // h264
    play_path_ext?: string // h265
    sync_status: 0 | 1
  }

  type Dimension = [number, number, number, number]

  interface IEpisodeBatchItem {
    series_resource_id: number// 资源id
    serial_number: number
    origin_path: string // 成片地址
    en_episode_path: string // 英文单集地址
    cn_episode_path: string // 中文单集地址
    [record: string]: string
  }
}
