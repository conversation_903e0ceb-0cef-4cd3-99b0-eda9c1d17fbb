<template>
  <x-consumption-records-page class="px-3 wallet text-[#cccacb] overflow-y-auto relative flex flex-col gap-y-2 bg-[#000] size-full">
    <Back 
      :is-white="true" 
      :title="T.walletPage.consumptionRecords.ref.value" 
      :is-close-webview="false"
      class="z-100 text-[#fdfbfc] font-bold w-full flex items-center sticky top-0 h-11 bg-[#000] shrink-0"
    />
    <div
      v-if="(!consumptionList || !consumptionList.items?.length) && !pageLoading"
      class="flex-1 mt-48% flex items-center justify-center"
    >
      <EmptyPage
        :image-size="160"
        :description="T.walletPage.noContentAvailable.ref.value"
      />
    </div>
    <template
      v-for="_ in 8"
      v-if="pageLoading"
      :key="_"
    >
      <van-skeleton>
        <template #template>
          <div class="w-full flex justify-between items-center mb-4">
            <div class="flex w-60%">
              <div class="w-11 h-15 rounded overflow-hidden">
                <img
                  class="w-full h-full"
                  :src="coverDefBg"
                >
              </div>
              <div class="flex-1 ml-2">
                <van-skeleton-paragraph
                  :round="true"
                  row-width="80%"
                />
                <van-skeleton-paragraph
                  :round="true"
                  row-width="45%"
                />
                <van-skeleton-paragraph
                  :round="true"
                  row-width="90%"
                />
              </div>
            </div>
            <div class="w-40% flex flex-col items-end ">
              <van-skeleton-paragraph
                :round="true"
                row-width="20%"
              />
              <van-skeleton-paragraph
                :round="true"
                row-width="40%"
              />
            </div>
          </div>
        </template>
      </van-skeleton>
    </template>
    <div
      v-if="consumptionList && consumptionList.items && consumptionList.items.length"
      class="py-5 px-3 flex flex-col gap-3 text-xs text-[#434546]"
    >
      <div
        v-for="item in consumptionList.items"
        :key="item.id"
        class="flex justify-between items-center"
        @click="() => {
        // 跳转  series_key: business_data.id 跳转去播放器
        }"
      >
        <div class="flex">
          <div class="w-11 h-15 rounded overflow-hidden">
            <img
              class="w-full h-full"
              @error="handleImageError"
              :src="item.business_data?.cover || coverDefBg"
            >
          </div>
          <div class="ml-2">
            <p class="text-[#cccacb] text-base">
              Episode {{ item.business_data?.episode_index }}
            </p>
            <p class="mb-1">
              {{ item.business_data.name }}
            </p>
            <p>{{ item.pay_finish_time ? dayjs.unix(item.pay_finish_time).format('YYYY-MM-DD') : 0 }}</p>
          </div>
        </div>
        <div>
          <div class="text-[#fdfbfc] text-sm flex items-center">
            {{ item.flow_type === 1 ? '+' : '-' }} {{ item.amount }}
            <SvgIcon name="ic-coins-fill" class="size-5 shrink-0 ml-1" />
          </div>
          <p class="text-right">{{ T.walletPage.coinsText.ref.value }}</p>
        </div>
      </div>
      <p class="mt-10 mb-3 text-center  text-[#6e7071] text-sm">
        {{ T.walletPage.lastLine.ref.value }}
      </p>
    </div>
  </x-consumption-records-page>
</template>
<script setup lang="ts">
import { SvgIcon } from '@skynet/ui'
import { ref, computed, onMounted } from 'vue';
import {  Skeleton as VanSkeleton, SkeletonParagraph as VanSkeletonParagraph } from 'vant';
import { Back } from 'src/h5_modules/common/back/back'
import coverDefBg from 'src/h5_modules/images/cover-def.webp'
import {apiConsumptionList} from '../wallet-api'
import { T } from '../wallet-page.i18n'
import dayjs from 'dayjs'
import EmptyPage from 'src/h5_modules/common/empty-page/empty-page.vue'

const pageLoading = ref(true);


const consumptionList = ref({
  // items: [
  //     {
  //               "txn_id": "1833480822366470144",
  //               "title": "购买剧情",
  //               "amount": 1,
  //               "flow_type": 2,
  //               "pay_finish_time": 1725970867,
  //               "bonus_expire_time": 0,
  //               "business_data": {
  //                   "id": "3fTpz6gHqf",
  //                   "name": "宫廷爽剧",
  //                   "cover": "1111",
  //                   "episode_index": 2,
  //                   "expire_time": 0      //过期时间戳，单位秒
  //               },
  //               "txn_type": "ad_unlock" 
  //   },
  //   {
  //               "txn_id": "1833480822366470144",
  //               "title": "购买剧情",
  //               "amount": 1,
  //               "flow_type": 2,
  //               "pay_finish_time": 1725970867,
  //               "bonus_expire_time": 0,
  //               "business_data": {
  //                   "id": "3fTpz6gHqf",
  //                   "name": "宫廷爽剧",
  //                   "cover": "66777",
  //                   "episode_index": 2,
  //                   "expire_time": 0      //过期时间戳，单位秒
  //               },
  //               "txn_type": "ad_unlock" 
  //   }
  // ]
})

const getConsumptionList = async () => {
  const res =  await apiConsumptionList()
  pageLoading.value = false;
  if (res.data && res.data) {
    consumptionList.value = res.data
  }
}

onMounted(() => {
  getConsumptionList();
})
const handleImageError = (event) => {
  event.target.src = coverDefBg
}
</script>

<style scoped>
.van-skeleton-paragraph {
  height: 0.8rem;
  background-color: #242526;
}

</style>