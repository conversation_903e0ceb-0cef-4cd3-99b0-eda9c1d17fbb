/* eslint-disable @typescript-eslint/no-explicit-any */
declare namespace M {
  type AdScene = 'app_open' | 'drama_free' | 'drama_lock' | 'reward' | 'quit_player' | 'out_flow'
  type AdSceneName = '开屏' | '面板前' | '面板后' | 'reward' | '退出播放器' | '外流位'
  type StrategyGroup = {
    id?: string
    name: string
    is_abtest?: 0 | 1
    ad_list?: M.RewardsConfig.WatchAd[]
    last_lock_episode?: number
    // 0: 全部, 1: iOS, 2: Android
    user_platform: 0 | 1 | 2 | number
    pay_mode?: 'IAA' | 'IAP' | string// 枚举:IAP,IAA
    platform?: 'ios' | 'android' | string
    ad_id?: number // 广告ID,
    ad_info?: M.Advertise
    ad_type?: number // 广告类型
    reward_ads?: {
      list: (M.RewardsConfig.WatchAd & { reward_coins?: number })[]
      interstitial_config?: {
        cooling_time?: number // CD时间，单位-秒
        watch_ad_count?: number // 频次上限，单位-次/天
        show_guide?: boolean // 集间是否展示引导页
        reward_unlock_episodes?: number// 赠币解锁集数
        ad_unlock_episodes?: number// 广告解锁集数
        balance_unlock_episodes?: number// 金币解锁集数
        all_unlock_episodes?: number// 解锁集数
        play_episodes?: number// 播放集数
      }
      ad_list_new?: {
        ad_type: string
        ad_platform: string[]
        ad_rep_open?: boolean
        replace_ad: {
          ad_type: string
          ad_platform: string[]
        }[]
      }[]
    }
    quit_player_config?: { // 退出播放器广告位
      strategy?: {
        cooling_time: number // CD时间，单位-秒
        watch_ad_count: number // 频次上限，单位-次/天
        quit_player_count: number// 退出播放器次数
      }
      ad_list_new?: {
        ad_type: string
        ad_platforms: string[]
        ad_rep_open?: boolean
        replace_ad: {
          ad_type: string
          ad_platforms: string[]
        }[]
      }[]
    }
    user_white_list?: string[] // 白名单
    display_control?: {
      start?: number// 广告开始加载位置/剧集
      skip?: number// 间隔的位置/剧集
      watch_time?: number// 观看时长,单位毫秒,十分钟
      ad_force_time?: number// 广告强制看时长,单位毫秒,5秒
      preload_ad_count?: number // 预加载广告数量
      show_reward_ad_native_ad_count?: number // 看了几个原生广告后，展示激励视频按钮
      skip_native_ad_time_ms?: number // 看完激励视频后奖励的免原生广告时长 单位ms
      reward_ad_count_limit?: number // 激励广告播放频次
      ad_switch_interval?: number // 切换的间隔
    }
    // 目标用户群类型
    // 1 活跃用户 2 自定义用户画像 3 联动广告组
    user_type: number
    // 0 不发送 1 发送
    send_pro_user: number
    custom_series_ids?: string[]
    series_package_id?: number
    user_config?: M.NSStrategyGroup.UserConfig
    product_config?: M.NSStrategyGroup.ProductConfig
    task_config?: M.NSStrategyGroup.TaskConfig
    unlock_nodes?: M.NSStrategyGroup.UnlockNode[]
    drama_list?: M.ApplicationDramaItem[]
    iap_strategy_ids?: string[]
    priority?: number
    status?: number | 1 | 2 | 3 | 4 // 状态 1 线上 2 草稿 3 未上架 4 冻结
    created?: number
    created_operator_name?: string
    updated?: number
    updated_operator_name?: string
    iap_strategys?: M.StrategyGroup[]
    ad_platform?: string // 广告平台
    ad_config?: {
      [k: string]: string
    }
    scene_type?: string[]
    watch_ad_num?: number
    unlock_num?: number
    isEdit?: boolean
    strategy_layer_id?: number
    strategy_layer_ids?: number[]
    strategy_layer_names?: string
    panel_type?: number
    panel_config?: {
      highlight_ad?: boolean
      hide_ad?: boolean
      hide_recharge?: boolean
      hide_membership?: boolean
      custom_panel_config?: string[]
    }
    count_limit_type?: 0 | 1 // 广告频次限制类型 1:每人/每天/每部剧维度 2:每人/每天维度
    scene?: AdScene[]
    app_open_config?: {
      strategy?: {
        cooling_time?: number
        watch_ad_count?: number
      }
      ad_list?: (M.RewardsConfig.WatchAd & { reward_coins?: number })[]
      ad_list_new?: {
        ad_type: string
        ad_platform: string[]
        ad_rep_open?: boolean
        replace_ad: {
          ad_type: string
          ad_platform: string[]
        }[]
      }[]
    }
    drama_free_config?: {
      strategy?: {
        reward_type?: string // 激励视频类型
        start?: number// 广告开始展示剧集
        skip?: number// 间隔剧集
        watch_time?: number// 间隔时间,单位-秒
        ad_force_time?: number// 原生广告强制看时长,单位-秒
        preload_ad_count?: number // 预加载广告数量
        show_reward_ad_native_ad_count?: number // 看第几次广告弹出激励视频弹窗
        skip_native_ad_time_ms?: number // 看激励视频免广告时长 单位-秒
        ad_switch_interval?: number // 连续几次不看激励视频免广告关闭弹窗
        ad_type_start?: string // 起始广告
        ad_gap?: Array<{ ad_type: string, ad_count: number }> // 间隔广告
      }
      ad_list?: (M.RewardsConfig.WatchAd & { reward_coins?: number })[]
      ad_list_new?: {
        ad_type: string
        ad_platforms: string[]
        ad_rep_open?: boolean
        replace_ad: {
          ad_type: string
          ad_platforms: string[]
        }[]
      }[]
    }
    reward_config?: {
      ad_list?: (M.RewardsConfig.WatchAd & { reward_coins?: number })[]
      ad_list_new?: {
        ad_type: string
        ad_platforms: string[]
        ad_rep_open?: boolean
        replace_ad: {
          ad_type: string
          ad_platform: string[]
        }[]
      }[]
      strategy?: {
        cd_time?: number
        task?: {
          sort_no: number
          coins: number
        }[]
      }
    }
  }
  /**
   * NS 前缀表示 namespace
   */
  namespace NSStrategyGroup {
    type UserConfig = {
      active_users_config?: {
        latest_days: number // 最近天数
        start_days: string // 范围状态起始时间
        end_days: string // 范围状态截止时间
      }
      custom_users_config?: Record<string, string[]>
      af_ad_config?: {
        adset_list?: Array<{
          id: string
          code: string
          name: string
        }>
      }
    }

    type LevelOption = Pick<M.RechargeLevel, 'priority' | 'id' | 'title' | 'currency' | 'discount_price' | 'delivery_details'> & {
      product_id?: string | number
      price?: number
      first_recharge?: number // 首充标识 1.是 0.否 2.再次
      show_bonus?: number
      slogan?: string
      tips?: string
      description?: string
      props?: string[]
      // 0: 全部, 1: iOS, 2: Android
      platform?: string
      package_type?: number
      unlock_num?: number
      discount_rate?: number
      default_product?: any
      product_ids?: string[]
      product_items?: any
      episode_products?: any
    }

    type Tab = 'ios_config' | 'android_config'

    type ProductConfig = {
      ios_config: {
        recharge_option: LevelOption[] // 充值档位方案
        item_package_option: LevelOption // 充值道具方案
        member_vip_option: LevelOption[] // 订阅方案
      }
      android_config: {
        recharge_option: LevelOption[] // 充值档位方案
        item_package_option: LevelOption // 充值道具方案
        member_vip_option: LevelOption[] // 订阅方案
      }
    }

    type TaskConfig = {
      priority?: number // 优先级
      duration?: number // 持续时间 -1 表示不限
      auto_up?: number // 自动上架时间 0 表示手动上架
      drama_list?: M.Episode[] // 指定生效短剧
      up_time?: number// 上架时间戳
    }

    type UnlockNode = {
      unlock_start_episode?: number
      unlock_end_episode?: number
      need_watch_num?: number
    }
  }
}
declare namespace Api {
  namespace StrategyGroup {
    namespace Request {
      interface List {
        strategy_id?: string
        name?: string
        // 0: 线上, 1: 草稿, 2: 未上架, 3: 冻结
        status?: 0 | 1 | 2 | 3 | number
        // 0: 全部, 1: 否, 2: 是
        is_abtest?: 0 | 1 | 2 | number
        // ASC: 从小到大, DESC: 从大到小
        sort?: {
          created?: 'ASC' | 'DESC' | string
          id?: 'ASC' | 'DESC' | string
          priority?: 'ASC' | 'DESC' | string
        }
        sortType?: string
        page_info: {
          offset: number
          size: number
        }
      }
    }
    namespace Response {
      type List = ApiResponse<{ list: M.StrategyGroup[], page_info: PageInfo2 }>
    }
  }
  namespace StrategyGroupUserProfile {
    type Item = {
      item_code: string
      item_value: string
      item_name: string
      item_description: string
      item_platform: string
      item_component_attr: number
      item_child: Item[]
    }
    namespace Response {
      type List = ApiResponse<{ list: M.StrategyGroupUserProfileItem[] }>
    }
  }
  namespace StrategyGroupAdChannel {
    namespace Response {
      type List = ApiResponse<{ list: Array<{ code: string, name: string }> }>
    }
  }
}
