/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Pager, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { useVoiceOver } from './use-voice-over'
import { ElTable, ElTableColumn } from 'element-plus'
import { onUnmounted } from 'vue'

type VoiceOverPageOptions = {
  props: {}
}
export const VoiceOverPage = createComponent<VoiceOverPageOptions>({
  props: {},
}, props => {
  const { loading, list, onQuery, onReset, getList, Form, params, columns, total, onPageChange, onPageSizeChange } = useVoiceOver()
  let interval = null

  interval = setInterval(() => {
    void getList()
  }, 60 * 1000)

  onUnmounted(() => {
    clearInterval(interval)
  })

  void onQuery()
  return () => (
    <x-voice-over-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>配音管理</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={() => onQuery()}
            onReset={onReset}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['资源ID', 'resource_id', { type: 'text', placeholder: '请输入资源ID' }, { transform: transformNumber }],
              ['资源名称', 'resource_name', { type: 'text', placeholder: '请输入资源名称' }],
            ]}
          />
        ),
        table: () => (
          <ElTable
            v-loading={loading.value}
            data={list.value || []}
          >
            {columns.value.map(col => (
              <ElTableColumn
                {...col}
                v-slots={col.render ? {
                  default: ({ row }: { row: M.IVoiceOverItem }) => col.render({ row }),
                } : undefined}
              />
            ))}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_index} v-model:size={params.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </x-voice-over-page>
  )
})

export default VoiceOverPage
