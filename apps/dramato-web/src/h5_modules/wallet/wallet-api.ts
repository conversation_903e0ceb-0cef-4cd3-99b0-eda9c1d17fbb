// import { h5HttpClient } from 'src/lib/http-client'
import { encryptedHttpClient } from 'src/lib/encrypted-http-client'
import { RechargeListInfo, ConsumptionListInfo, RewardsListInfo } from './wallet'
// 充值历史
export const apiRechargeList = () => {
  return encryptedHttpClient.get<ApiResponse<RechargeListInfo>>(`h5-api/wallet/recharge/list`)
}
// 消费记录
export const apiConsumptionList = () => {
  return encryptedHttpClient.get<ApiResponse<ConsumptionListInfo>>(`h5-api/wallet/consumption/list`)
}

// 奖励金币记录
export const apiRewardsList = () => {
  return encryptedHttpClient.get<ApiResponse<RewardsListInfo>>(`h5-api/wallet/rewards/list`)
}

// 自动解锁开关变换
export const ApiPostAutounlock = (data: {
  auto_unlock: number // 0: 关闭  1：开启
  // diamond_auto_unlock?: number
}) => {
  return encryptedHttpClient.post<ApiResponse>('/h5-api/wallet/autounlock/change', data)
}
