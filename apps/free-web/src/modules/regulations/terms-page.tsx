import { createComponent } from '@skynet/shared'
import { rulesMap } from './rules-map'
import './github-markdown-dark.css'
import router from 'src/router'
import { Back } from '../common/back/back'
import { isIos } from 'src/lib/ua'
type TermsPageOptions = {
  props: {}
}
export const TermsPage = createComponent<TermsPageOptions>({
  props: {},
}, props => {
  const language = router.currentRoute.value.query.language as string || 'en'
  const terms = rulesMap[language]['terms']
  return () => (
    <div class="size-full overflow-auto">
      <Back />
      <article class="w-full px-3 markdown-body regulations-page share-bottom-35 !bg-[var(--surface-1)] !text-[var(--text-1)]" innerHTML={isIos() ? terms.replaceAll('FreeReels', 'DramaReels') : terms} />
    </div>
  )
})

export default TermsPage
