import { createComponent, tryCall } from '@skynet/shared'
import { Button, SvgIcon } from '@skynet/ui'
import { VNodeChild } from 'vue'
type EmptyOptions = {
  props: {
    desc: VNodeChild | (() => VNodeChild)
    image?: string
    button?: {
      text: string
      onClick: () => void
    }
  }
}
export const Empty = createComponent<EmptyOptions>({
  props: {
    desc: '暂无数据',
    image: '',
    button: {
      text: '',
      onClick: () => {},
    },
  },
}, props => {
  return () => (
    <x-empty class="block pt-44 text-center">
      <SvgIcon name="ic_empty_group" class="w-[153px] text-[#A1A0A3] h-[121px] mb-5" />
      <x-empty-desc class="block text-[#A1A0A3] font-normal text-sm">{tryCall(props.desc)}</x-empty-desc>
      {props.button.text && <Button class="btn btn-sm btn-ghost mt-5" onClick={props.button.onClick}>{props.button.text}</Button>}
    </x-empty>
  )
})
