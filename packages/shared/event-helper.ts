/* eslint-disable @typescript-eslint/no-explicit-any */
export const on = (e: string, callback: (data: any) => void) => {
// 使用window实现eventEmitter
// callback提取event.detail作为data传递给回调函数
  window.addEventListener(e, (event: Event) => {
    if (event instanceof CustomEvent) {
      callback(event.detail)
    }
  })
}

export const off = (e: string, callback: (data: any) => void) => {
  window.removeEventListener(e, callback)
}

export const emit = (e: string, data?: any) => {
  window.dispatchEvent(new CustomEvent(e, { detail: data }))
}

export const once = (e: string, callback: (data: any) => void) => {
  const handler = (data: any) => {
    callback(data)
    off(e, handler)
  }
  on(e, handler)
}
