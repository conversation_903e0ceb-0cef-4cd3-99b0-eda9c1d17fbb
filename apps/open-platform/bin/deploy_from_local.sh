#!/bin/sh

set -e

source bin/helper.sh

# 如果 MODE 为空，或者 MODE 不是 test 也不是 prod，则提示 MODE 要么为 test 要么为 prod
if [ -z "$MODE" ] || { [ "$MODE" != "test" ] && [ "$MODE" != "prod" ]; }; then
  error 'Usage: deploy.sh [mode:test|prod]'
  exit 1
fi


if [ ! -f package.json ]; then
  error '当前目录必须包含 package.json 文件'
  exit 1
fi

# 获取当前 git branch
CURRENT_BRANCH=$(git branch --show-current)
# 如果当前分支跟 BRANCH 不相等，则提示 请切换到 $BRANCH 分支
if [ "$CURRENT_BRANCH" != "$BRANCH" ]; then
  error "请切换到 $BRANCH 分支"
  exit 1
fi

# 如果当前分支有未提交的代码，则提示 请 git commit
if [ -n "$(git status -s)" ]; then
  error "请先 git commit"
  exit 1
fi

# 更新当前分支， 如果当前分支跟远程分支不一致，则提示请先同步远程分支
info "git fetch origin $BRANCH"
git fetch origin $BRANCH
if [ "$(git rev-parse origin/$BRANCH)" != "$(git rev-parse HEAD)" ]; then
  warn "检测到本地分支 $CURRENT_BRANCH 与远程分支 $BRANCH 不一致，请同步远程分支"
  exit 1
fi

info "开始部署 $RELATIVE_PATH "

info '创建临时目录'
rm -rf $TEMP
mkdir -p $TEMP

info '生成 dist 目录'
pnpm i
pnpm run "build:$MODE"
tar -cvf dist.tar -C dist .
cp -r dist.tar $TEMP/
cp bin/build.sh $TEMP/

info '创建 deploy_repo'
git clone -b init --depth 1 "$REPO" "$TEMP/deploy_repo"
cd $TEMP/deploy_repo; git switch -c $BRANCH; cd ..

info '复制 dist 到 deploy_repo'
rm -rf $TEMP/deploy_repo/dist
rm -rf $TEMP/deploy_repo/dist.tar
cp -r $TEMP/dist.tar $TEMP/deploy_repo/
cp $TEMP/build.sh $TEMP/deploy_repo/
cd $TEMP/deploy_repo
rm -rf .git
git init
git add *
git commit -m 'deploy'
git remote add origin "$REPO"
git branch -M $BRANCH
info "如果命令卡住，且没有出现 remote:  https://git.tianmai.cn/... 链接"
info "    请手动执行命令 cd $TEMP/deploy_repo ; git push origin -f $BRANCH:$BRANCH ; cd - ; pnpm jenkins:$MODE"
info "如果命令卡住，但出现了 remote:  https://git.tianmai.cn/... 链接"
info "    请手动执行命令 pnpm jenkins:$MODE"
git push origin :$BRANCH || echo "origin/$BRANCH is missing"
git push origin $BRANCH:$BRANCH

if [ "$MODE" == 'test' ]; then
  info '自动触发 jenkins 测试环境部署'
  info "如果命令卡住，请手动执行命令 pnpm jenkins:$MODE"
  curl -X POST "http://yinghang.fang:<EMAIL>:8080/job/$JENKINS_JOB/build"
else
  info '自动触发 jenkins 生产环境部署'
  info "如果命令卡住，请手动执行命令 pnpm jenkins:$MODE"
  curl -X POST "http://yinghang.fang:<EMAIL>:8080/job/$JENKINS_JOB/build"
fi

set +e
echo 'success'
