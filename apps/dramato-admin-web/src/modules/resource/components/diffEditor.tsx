/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, ref, onMounted, onBeforeUnmount, PropType, shallowRef } from 'vue'
import { useLoadMonaco } from 'src/lib/load-monaco-editor'
import * as Monaco from 'monaco-types'

export default defineComponent({
  props: {
    customClass: {
      type: String as PropType<string>,
      required: false,
    },
    code1: {
      type: String as PropType<string>,
      required: false,
      default: '',
    },
    code2: {
      type: String as PropType<string>,
      required: false,
      default: '',
    },
    type: {
      type: String as PropType<string>,
      required: false,
      default: 'plaintext',
    },
  },
  setup(props) {
    const { loadMonacoEditor, monacoLoading } = useLoadMonaco()
    // must be shallowRef, if not, editor.getValue() won't work
    const editorRef = shallowRef()

    const containerRef = ref()

    let _subscription: Monaco.IDisposable | undefined

    onMounted(async () => {
      await loadMonacoEditor()
      const editor = editorRef.value = window.monaco.editor.createDiffEditor(containerRef.value, {
        renderSideBySide: true,
        readOnly: true,
        useInlineViewWhenSpaceIsLimited: false,
      })

      editor.setModel({
        original: window.monaco.editor.createModel(props.code1, props.type),
        modified: window.monaco.editor.createModel(props.code2, props.type),
      })

      editor.getOriginalEditor().onDidScrollChange((e: Monaco.editor.IModelContentChangedEvent) => {
        const scrollTop = editor.getOriginalEditor().getScrollTop()
        editor.getModifiedEditor().setScrollTop(scrollTop)
      })

      editor.getModifiedEditor().onDidScrollChange((e: Monaco.editor.IModelContentChangedEvent) => {
        const scrollTop = editor.getModifiedEditor().getScrollTop()
        editor.getOriginalEditor().setScrollTop(scrollTop)
      })
    })

    onBeforeUnmount(() => {
      if (_subscription)
        _subscription.dispose()
    })

    return () => {
      return (
        monacoLoading.value
          ? (
              <div class="h-[55vh] w-full flex justify-center items-center">
                <span>加载中……</span>
              </div>
            )
          : <div ref={containerRef} class={props.customClass} />
      )
    }
  },
})
