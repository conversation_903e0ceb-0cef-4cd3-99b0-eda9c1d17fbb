import { httpClient } from 'src/lib/http-client'
import { filterIt, splitIt } from '@skynet/shared'

// 角色列表
export const apiGetRoleList = (data: M.IRoleQueryParams) => {
  return httpClient.post<ApiResponse<{
    list: M.IRole[]
  }>>('/backend/role_list', data, {
    transformResponseData: {
      'data.list': [(list: Array<M.IRole>) => {
        return list.map(item => {
          item.access_urls = item.access_urls ? (item.access_urls as string[]).join('\n') : ''
          item.prefix_access_urls = item.prefix_access_urls ? (item.prefix_access_urls as string[]).join('\n') : ''
          item.not_access_urls = item.not_access_urls ? (item.not_access_urls as string[]).join('\n') : ''
          item.prefix_not_access_urls = item.prefix_not_access_urls ? (item.prefix_not_access_urls as string[]).join('\n') : ''
          return item
        })
      }],
    },
  })
}

// 修改用户角色
export const apiUpdateRole = (data: M.IRole) => {
  return httpClient.post<ApiResponse<null>>('/backend/role_edit', data, {
    transformRequestData: {
      access_urls: [splitIt(['\n']), filterIt(Boolean)],
      prefix_access_urls: [splitIt(['\n']), filterIt(Boolean)],
      not_access_urls: [splitIt(['\n']), filterIt(Boolean)],
      prefix_not_access_urls: [splitIt(['\n']), filterIt(Boolean)],
    },
  })
}
