import { ClassName, createComponent, fn, mc } from '@skynet/shared'
import type { ECElementEvent, EChartsType, ElementEvent } from 'echarts'
import { ECBasicOption } from 'echarts/types/dist/shared'
import { onMounted, ref, toRaw, watch } from 'vue'
type EChartOptions = {
  props: {
    option: ECBasicOption | null
    class?: ClassName
  }
  emits: {
    click: (params: ECElementEvent) => void
    select: (params: unknown) => void
    clickEmpty: (params: ElementEvent) => void
  }
}
export const EChart = createComponent<EChartOptions>({
  inheritAttrs: false,
  props: {
    option: null,
    class: '',
  },
  emits: {
    click: fn,
    clickEmpty: fn,
    select: fn,
  },
}, (props, { emit }) => {
  let chart: EChartsType | null = null
  const div = ref<HTMLDivElement | null>(null)
  onMounted(() => {
    void import('echarts').then(({ init }) => {
      if (!div.value) return
      if (chart) return
      chart = init(div.value)
      chart.on('click', params => {
        emit('click', params)
      })
      chart.on('selectchanged', params => emit('select', params))
      chart.getZr().on('click', event => {
        if (!event.target) {
          emit('clickEmpty', event)
        }
      })
      if (props.option) {
        chart.setOption(props.option)
      }
    })
  })
  watch(() => props.option, option => {
    if (!option || !chart) return
    const raw = toRaw(option)
    chart.setOption(raw)
  }, { deep: true })
  return () => (
    <x-chart ref={div} class={mc('block min-h-4 ', props.class)} />
  )
})
