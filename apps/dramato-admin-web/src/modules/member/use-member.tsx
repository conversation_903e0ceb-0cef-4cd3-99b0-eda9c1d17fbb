/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, usePopover, showFailToast } from '@skynet/ui'
import { apiGetMemberList, apiCreateMember, apiUpdateMember, apiUpdateMemberPriority } from './member.api'
import { apiGetAppOptions } from 'src/modules/application/application.api'
import { platformList } from 'src/lib/constant.ts'
import { AxiosError } from 'axios'
import { ref } from 'vue'
import { cloneDeep, get } from 'lodash-es'

export const useMember = () => {
  return {
    Form, defaultData, form, dialogRef, appList, getList, tableLoading, list, queryForm, total, onPageSizeChange, onPageChange, onQuery, onReset, triggerElementRef,
  }
}

const Form = CreateForm<M.EpisodeTheatresChannel.List.Request>()
const defaultData = {
  id: undefined,
  app_id: undefined,
  app_name: '',
  product_type: 'membership',
  title: '',
  slogan: '',
  tips: '',
  discount_desc: '',
  description: '',
  currency: 'USD',
  price: undefined,
  discount_price: undefined,
  platform: '',
  store: '',
  status: 2,
  props: [],
  member_limited: 1,
  delivery_details: {
    quanity: 1,
    bonus: undefined,
    daily_bonus: undefined,
    period: 'weekly',
  },
  sku_id: '',
  priority: undefined,
  first_recharge: 0,
  has_discount: 0,
  time_limit: undefined,
}
const form = ref<M.MemberLevel>(cloneDeep(defaultData))
const dialogRef = ref(() => {})
const appList = ref<{ value: number, label: string, platform: string }[]>([])
const list = ref<M.MemberLevel[]>([])
const tableLoading = ref(false)
const total = ref(0)
const queryForm = ref<M.QueryMemberLevel>({
  page_size: 100,
  next: '',
  has_more: true,
  app_id: undefined,
  title: '',
  membership_type: 'vip',
  store: '',
  status: 0,
})
const getList = async () => {
  try {
    tableLoading.value = true
    const res = await apiGetMemberList({
      ...queryForm.value,
      app_id: '' + queryForm.value.app_id,
    })
    list.value = res.data?.items as unknown as M.MemberLevel[] || []
    queryForm.value.has_more = res.data?.page_info.has_more || false
    queryForm.value.next = res.data?.page_info.next || ''
    total.value = res.data?.total || 0
  } catch (e) {
    const error = e as AxiosError
    showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
  } finally {
    tableLoading.value = false
  }
}

const onPageSizeChange = (page_size: number) => {
  queryForm.value.next = ''
  queryForm.value.page_size = page_size
  void onQuery()
}
const onPageChange = (page: number) => {
  queryForm.value.page = page
  queryForm.value.next = page - 1 > 0 ? `${(page - 1) * queryForm.value.page_size}` : ''

  void getList()
}
const onQuery = async () => {
  queryForm.value.page = 1
  queryForm.value.next = ''
  queryForm.value.has_more = true
  await getList()
}
const onReset = () => {
  queryForm.value = {
    app_id: queryForm.value.app_id || appList.value[0].value,
    page_size: 100,
    next: '',
    has_more: true,
    membership_type: 'vip',
    title: '',
    store: '',
    status: 0,
  }
  void getList()
}

const triggerElementRef = ref()
usePopover({
  triggerElement: triggerElementRef,
  content: () => (
    <x-tips class="flex flex-col gap-y-2">
      {
        [
          'Unlimited access to all series for 1 week',
          'Unlimited access to all series for 7 days',
          'Unlimited access to all series for 1 month',
          'Unlimited access to all series for 1 year',
        ].map(i => <x-tip class="cursor-pointer" onClick={() => form.value.description = i}>{i}</x-tip>)
      }
    </x-tips>
  ),
  placement: 'bottom-start',
  class: 'overflow-visible',
  offset: 10,
  arrowVisible: false,
  triggerType: 'hover',
  wrapperClass: 'p-2 bg-white border-[1px] border-[#eee] border-[solid] rounded-xs z-popover-in-dialog',
})
