import { mc, tryCall } from '@skynet/shared'
import dayjs from 'dayjs'
import { get, omit } from 'lodash-es'
import { h } from 'vue'
import { Checkbox, CheckboxGroup, DatetimePicker, Icon, MoneyInput, RadioGroup } from '../ui'
import { CreateFormMultiSelect } from './form-multi-select'
import { FormItem, FormItemArray, FormItemGroup, FormItemObject, InputOption } from './form-types'
import { isInputRender, useFormStore } from './use-form-store'

const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()
export const Empty = () => {
  return <span v-html={'&nbsp;'} />
}

export const isFormItemGroup = (item: FormItemObject | FormItemGroup | FormItemArray): item is FormItemGroup =>
  Array.isArray(item) && (Array.isArray(item[1]) || item.length <= 2)
export const isFormItemArray = (item: FormItemObject | FormItemGroup | FormItemArray): item is FormItemArray =>
  Array.isArray(item) && typeof item[0] === 'string' && typeof item[1] === 'string'
export const isFormItemObject = (item: FormItemObject | FormItemGroup | FormItemArray): item is FormItemObject =>
  !isFormItemGroup(item) && !isFormItemArray(item)

const parse0xToHex = (hexWith0x: string) => {
  if (!hexWith0x) {
    return ''
  }
  if (hexWith0x.includes('#')) return hexWith0x
  const hex = hexWith0x.slice(4) // 移除 Alpha 通道
  return `#${hex}`
}
type RenderInputParams = {
  item: FormItemObject
  id: string
  data: object
  error: string | undefined
  onChange: (value: unknown) => void
}
export const renderInput = ({ item, id, data, error, onChange }: RenderInputParams) => {
  const onNativeInput = (e: Event) => onChange(display2raw((e.target as HTMLSelectElement).value, item))
  const onCustomInput = (value: unknown) => {
    onChange(display2raw(value, item))
  }
  const { renders } = useFormStore()
  const onClear = (e: Event) => onChange(display2raw(undefined, item) ?? '')
  switch (item.input.type) {
    case 'image':
      return isInputRender(renders.value.image) ? renders.value.image({
        onInput: onCustomInput,
        item: item,
        value: raw2display(data, item),
        error: error,
      }) : h(renders.value.image, {
        onInput: onCustomInput,
        value: raw2display(data, item),
        error: error,
      })
    case 'date':
      return (
        <div class="input-bordered input input-sm flex items-center gap-2">
          <input
            {...item.input}
            id={id}
            class={mc('grow', item.input.class, error ? 'is-error' : '')}
            type="date"
            value={raw2display(data, item)}
            onInput={onNativeInput}
          />
          <Icon class="size-4 cursor-pointer opacity-70" name="icon-park-outline:close" onClick={onClear} />
        </div>
      )
    case 'datetime':
      return (
        <div class={mc('input input-bordered input-sm flex items-center gap-2', item.input.class)}>
          <input
            {...item.input}
            class={mc('grow', item.input.class, error ? 'is-error' : '')}
            type="datetime-local"
            step={1}
            value={raw2display(data, item)}
            onInput={onNativeInput}
          />
          <Icon class="size-4 cursor-pointer opacity-70" name="icon-park-outline:close" onClick={onClear} />
        </div>
      )
    case 'date2':
      return (
        <div class={mc('input input-bordered input-sm flex items-center gap-2', item.input.class)}>
          <DatetimePicker {...item.input} enableTime={false} modelValue={raw2display(data, item)} onUpdate:modelValue={onCustomInput} />
          <Icon class="size-4 cursor-pointer opacity-70" name="icon-park-outline:close" onClick={onClear} />
        </div>
      )
    case 'datetime2':
      return (
        <div class={mc('input input-bordered input-sm flex items-center gap-2', item.input.class)}>
          <DatetimePicker {...item.input} modelValue={raw2display(data, item)} onUpdate:modelValue={onCustomInput} />
          <Icon class="size-4 cursor-pointer opacity-70" name="icon-park-outline:close" onClick={onClear} />
        </div>
      )
    case 'checkbox':
      return (
        <Checkbox
          {...item.input}
          disabled={!!item.input.disabled}
          label={item.input.label ?? <Empty />}
          modelValue={raw2display(data, item)}
          onUpdate:modelValue={onCustomInput}
        />
      )
    case 'checkbox-group':
      return (
        <CheckboxGroup
          {...item.input}
          disabled={!!item.input.disabled}
          class={mc(item.input.class, error ? 'is-error' : '')}
          modelValue={raw2display(data, item)}
          onUpdate:modelValue={onCustomInput}
        />
      )
    case 'select':
      return (
        <select
          id={id}
          class={mc('w-full select-bordered select-sm select', item.input.class, error ? 'is-error' : '')}
          value={raw2display(data, item)}
          onInput={onNativeInput}
          disabled={item.input.disabled}
        >
          {item.input.autoInsertEmptyOption !== false && <option value="">- 空 -</option>}
          {item.input.options.map((option: InputOption) => (
            <option value={option.value}>{option.label}</option>
          ))}
        </select>
      )
    case 'multi-select':{
      return (
        <FormMultiSelect
          {...item.input}
          maxlength={item.input.maxlength ? parseInt(item.input.maxlength.toString()) : undefined}
          class={mc(item.input.class, error ? 'is-error' : '')}
          modelValue={raw2display<Array<string | number | boolean>>(data, item)}
          onUpdate:modelValue={onCustomInput}
        />
      )
    }
    case 'radio-group':
    case 'radio': {
      return (
        <RadioGroup
          {...item.input}
          disabled={!!item.input.disabled}
          class={mc(item.input.class, error ? 'is-error' : '')}
          modelValue={raw2display(data, item)}
          onUpdate:modelValue={onCustomInput}
        />
      )
    }
    case 'textarea':
      return (
        <x-textarea-wrapper class={mc('block w-full grow shrink', item.input.wrapperClass, error ? 'is-error' : '')}>
          {tryCall(item.input.prefix)}
          <textarea
            {...item.input}
            class={mc('block textarea textarea-bordered w-full', item.input.class, error ? 'is-error' : '')}
            value={raw2display(data, item)}
            onInput={onNativeInput}
          />
          {tryCall(item.input.suffix)}
        </x-textarea-wrapper>
      )
    case 'custom':
      return item.input.render({
        onInput: onCustomInput,
        item: item,
        value: raw2display(data, item),
        error: error,
      })
    case 'money': {
      return (
        <span class={mc('input input-bordered flex items-center gap-1 h-8', item.input.wrapperClass)}>
          {tryCall(item.input.prefix)}
          <MoneyInput id={id}
            {...omit(item.input, ['type', 'prefix', 'suffix', 'wrapperClass'])}
            class={mc('grow-1 w-full shrink-1', item.input.class, error ? 'is-error' : '')}
            modelValue={raw2display(data, item)}
            onUpdate:modelValue={onCustomInput}
          />
          {tryCall(item.input.suffix)}
        </span>
      )
    }
    case 'number':
      return (
        <span class={mc('input input-bordered flex items-center gap-1 h-8', item.input.wrapperClass)}>
          {tryCall(item.input.prefix)}
          <input
            {...item.input}
            id={id}
            class={mc('grow-1 w-full shrink-1', item.input.class, error ? 'is-error' : '')}
            value={raw2display(data, item)}
            onInput={onNativeInput}
          />
          {tryCall(item.input.suffix)}
        </span>
      )
    case 'color':
      return (
        <div class="flex items-center gap-x-2 pl-1">
          <input
            {...item.input}
            value={parse0xToHex(raw2display(data, item))}
            onInput={e => {
              if ('hasAlpha' in item.input && item.input.hasAlpha) {
                const value = display2raw((e.target as HTMLSelectElement).value, item)
                const hex = value.replace('#', '')
                const eightDigit = hex.length === 6 ? 'FF' + hex : hex // 补 Alpha
                const result = `0x${eightDigit.toUpperCase()}` // 组合成目标格式

                onChange(result)
              } else {
                onNativeInput(e)
              }
            }}
            class={mc(
              'w-[30px] h-[30px] rounded-lg cursor-pointer focus:outline-none appearance-none border-0 bg-transparent shadow-none',
              item.input.class,
            )}

          />
          <input {...item.input}
            type="text"
            class={mc(item.input.class, error ? 'is-error' : '')}
            value={raw2display(data, item)}
            onInput={onNativeInput}
          />
        </div>
      )
    default:
      return (
        <x-input-wrapper class={mc('input input-bordered flex items-center gap-1 h-8', item.input.wrapperClass, error ? 'is-error' : '')}>
          {tryCall(item.input.prefix)}
          <input
            {...item.input}
            id={id}
            class={mc('grow', item.input.class, error ? 'is-error' : '')}
            value={raw2display(data, item)}
            onInput={onNativeInput}
          />
          {tryCall(item.input.suffix)}
        </x-input-wrapper>
      )
  }
}
export const raw2display = <T extends unknown>(data: object, item: FormItemObject): T => {
  const path = item.path
  const transform = item.transform?.[0]
  const raw = get(data, path)
  const display = transform ? transform(raw, item) : raw
  return display ?? undefined
}

export const display2raw = (display: unknown, item: FormItemObject) => {
  const transform = item.transform?.[1]
  return transform ? transform(display, item) : display
}

export const transformStringArray = [
  (raw?: unknown) => Array.isArray(raw) ? raw.join(',') : raw?.toString() ?? '',
  (display: string): string[] => display.split(/,|\n/g).map(item => item.trim()),
] as const
export const transformNumberArray = [
  (raw?: unknown) => Array.isArray(raw) ? raw.join(',') : raw?.toString() ?? '',
  (display: string): number[] => display.split(/,|\n/g).map(item => Number(item.trim())).filter(item => !Number.isNaN(item)),
] as const
export const transformNumber = [
  (raw?: unknown) => raw === undefined ? '' : String(raw),
  (display: string) => !display || display === '' ? undefined : Number(display),
] as const
export const transformNumber3 = [
  (raw?: unknown) => raw === undefined || !raw ? '' : String(raw),
  (display: string) => !display || display === '' ? undefined : Number(display),
] as const
export const transformNumber2 = [
  (raw?: unknown) => raw === undefined ? '' : String(raw).match(/\d+\.?\d{0,2}/)?.[0],
  (display: string) => {
    return display === '' ? undefined : Number(display)
  },
] as const
export const transformInteger = [
  (raw?: unknown) => { return raw === undefined ? '' : Number(raw) },
  (display: string) => { return display === '' ? undefined : Number(display.toString().replace(/\D/g, '')) },
] as const
export const transformBool2 = [
  (raw?: unknown) => raw === undefined ? false : !!raw,
  (display: string) => display === 'true',
] as const
export const transformBool = (defaultValue?: boolean) => [
  (raw: unknown) => raw === undefined ? defaultValue : !!raw,
  (display: string) => display === 'true',
] as const
export const transformMultiIntegerSelect = [
  (raw: unknown[], i: FormItem) =>
    raw !== undefined
      /* @ts-expect-error never mind */
      ? Array.isArray(raw) && raw.map(item => ({ label: i.input.options.find(o => o.value === item)?.label, value: +item }))
      : [],
  (display: Array<{ label: unknown, value: unknown }>, _item: FormItem) =>
    display.map(item => Number(item.value || '')),
] as const
export const transformDatetime = [
  (raw: string, i: FormItemObject) => raw === undefined
    ? ''
    : dayjs(raw).format('displayFormat' in i.input ? i.input.displayFormat : 'YYYY-MM-DD HH:mm:ss'),
  (display: string, i: FormItemObject) => display === undefined
    ? ''
    : dayjs(display).format('rawFormat' in i.input ? i.input.rawFormat : 'YYYY-MM-DDTHH:mm:ss+08:00'),
] as const

export const transformTimestamp = [
  (raw: number | undefined, i?: FormItemObject) => raw === undefined || !raw ? undefined : dayjs(raw * 1000).format((i && 'displayFormat' in i.input) ? i.input.displayFormat : 'YYYY-MM-DD HH:mm'),
  (display: string | undefined, i?: FormItemObject) => !display ? undefined : dayjs(display).unix(),
] as const
