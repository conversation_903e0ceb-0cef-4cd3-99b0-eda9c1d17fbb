import { computed, MaybeRef, onUnmounted, ref, watch } from 'vue'
import { usePopoverStore } from './use-popover-store'
import { unrefElement } from '@vueuse/core'
import { PopoverOptions } from './popover'
import { OptionsToProps } from '@skynet/shared'

type Props = OptionsToProps<PopoverOptions>
export const usePopover = (options: Omit<Props, 'id'> & { beforeOpen?: () => boolean }) => {
  const { triggerType = 'hover', beforeOpen } = options
  const popoverStore = usePopoverStore()
  const id = ref<string | number>()
  let timer: null | number = null

  watch([options.triggerElement], () => {
    const ele = unrefElement(options.triggerElement as MaybeRef<HTMLElement>)
    if (!ele) return
    if (triggerType === 'hover') {
      ele.addEventListener('mouseenter', () => {
        open()
      })
      ele.addEventListener('mouseleave', () => {
        asyncClose()
      })
    } else if (triggerType === 'click') {
      ele.addEventListener('click', () => {
        toggle()
      })
    } else if (triggerType === 'focus') {
      ele.addEventListener('focus', () => {
        open()
      })
      ele.addEventListener('blur', () => {
        asyncClose()
      })
    }
  })

  const visible = computed(() => id.value !== undefined)

  const open = () => {
    cancelAsyncClose()
    if (id.value) return
    if (beforeOpen && beforeOpen() === false) return
    id.value = popoverStore.addPopover({
      onCancelAsyncClose: cancelAsyncClose,
      onRemove: remove,
      ...options,
    })
  }
  const remove = () => {
    cancelAsyncClose()
    if (id.value === undefined) return
    popoverStore.removePopover(id.value as string)
    id.value = undefined
  }

  const toggle = () => {
    if (id.value) {
      remove()
    } else {
      open()
    }
  }

  const cancelAsyncClose = () => {
    if (timer) {
      window.clearTimeout(timer)
      timer = null
    }
  }

  const asyncClose = (delay = 200) => {
    if (id.value === undefined) return
    timer = window.setTimeout(remove, delay)
  }
  const close = () => {
    remove()
  }

  onUnmounted(() => {
    close()
  })

  return {
    visible,
    open,
    close,
    remove,
    toggle,
    cancelAsyncClose,
    asyncClose,
  }
}
