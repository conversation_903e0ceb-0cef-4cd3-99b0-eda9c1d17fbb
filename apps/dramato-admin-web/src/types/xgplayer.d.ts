declare module 'xgplayer' {
  export interface IPlayerOptions {
    el?: HTMLDivElement
    url?: string
    poster?: {
      isEndedShow?: boolean
      hideCanplay?: boolean
      poster?: string
    }
    icons?: {
      startPlay?: string
      startPause?: string
    }
    mediaType?: string
    height?: string
    width?: string
    isMobileSimulateMode?: string
    autoplay?: boolean
    videoFillMode?: string
    playsinline?: boolean
    closeVideoDblclick?: boolean
    closeVideoTouch?: boolean
    closeVideoPreventDefault?: boolean
    closeVideoStopPropagation?: boolean
    controls?: boolean
    start?: {
      mode?: string
    }
    play?: {
      disable?: boolean
    }
    volume?: {
      disable?: boolean
    }
    playbackrate?: {
      disable?: boolean
    }
    definition?: {
      disable?: boolean
      list?: unknown[]
    }
    replay?: {
      disable?: boolean
    }
    fullscreen?: {
      disable?: boolean
    }
    'x5-video-player-fullscreen'?: boolean
    'x5-video-player-type'?: string
    commonStyle?: {
      progressColor?: string
      cachedColor?: string
      playedColor?: string
    }
  }

  export enum Events {
    // eslint-disable-next-line no-unused-vars
    ENDED = 'ended',
  }

  export default class Player {
    constructor(options: IPlayerOptions)
    on(event: Events, callback: () => void): void
    play(): void
  }
}
