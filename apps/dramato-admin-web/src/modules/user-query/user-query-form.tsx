/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { ref } from 'vue'
import { z } from 'zod'
import { Uploader } from '../common/uploader/uploader'
import { apiGetUserInfo } from './user-query-api'
import { httpClient } from 'src/lib/http-client'
type UserQueryFormOptions = {
  props: {
    user: M.UserQuery.Response
  }
  emits: {
    hide: () => void
  }
}

type FormValues = {
  in_user_id?: number
  credential_image: string
}
export const UserQueryForm = createComponent<UserQueryFormOptions>({
  props: {
    user: {
      user_info: {
        user_id: 0, // 用户id
        uid: 0, // uid
        user_type: 1, // 0 匿名 1 facebook,2 google 3.appstore
        third_party_account: '',
        user_status: 1, // 状态： 1:正常 2:注销
      },
      user_wallet: {
        cash_balance: 0,
        bonus_balance: 0,
        vip_level: 0, // 0-非Vip，1-vip
        vip_expire: 0, // vip到期时间
        vip_period: '', // weekly,monthly,yearly
      },
    },
  },
  emits: {
    hide: () => { },
  },
}, (props, { emit }) => {
  const Form = CreateForm<FormValues>()

  const formData = ref<FormValues>({
    in_user_id: undefined,
    credential_image: '',
  })

  const in_user = ref<M.UserQuery.Response>()

  const formRules = z.object({
    in_user_id: z.number().min(1, '请输入档位名称'),
    credential_image: z.string().min(1, '请上传凭证图片'),
  })

  const { error, validateAll } = useValidator(formData, formRules)

  return () => (

    <div>
      <div class="flex flex-col gap-2 px-4">
        <h1 class="font-medium">转出用户</h1>
        <x-item>用户ID: {props.user.user_info.user_id}</x-item>
        <x-item>用户UID: {props.user.user_info.uid}</x-item>
        <x-item>金币余额(Coins): {props.user.user_wallet.cash_balance}</x-item>
        <x-item>奖励币余额(Reward coins): {props.user.user_wallet.bonus_balance}</x-item>
        <x-item>会员权益: {props.user.user_wallet.vip_level === 1 ? '会员/' + props.user.user_wallet.vip_period : '否'}</x-item>

        <Form
          class="grid gap-y-3 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(formData.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('转入用户UID'),
              path: 'in_user_id',
              input: {
                type: 'number',
                placeholder: '请输入用户UID',
              },
              transform: transformNumber,
              hint: () => (
                <Button class="btn btn-sm btn-primary mt-2" onClick={() => {
                  void apiGetUserInfo({ user_id: formData.value.in_user_id }).then(res => {
                    in_user.value = res.data
                  }).catch((error: any) => {
                    showAlert(error.response.data.err_msg, 'error')
                  })
                }}
                >查询
                </Button>
              ),
            },
            !!in_user.value && {
              label: '',
              path: 'in_user_info',
              input: {
                type: 'custom',
                render: () => (
                  <x-in-user-info class="flex flex-col gap-2 p-4 bg-slate-200 rounded-sm">
                    <h1 class="font-medium">用户资料</h1>
                    <x-item>用户ID: {in_user.value?.user_info.user_id}</x-item>
                    <x-item>UID: {in_user.value?.user_info.uid}</x-item>
                    <x-item>三方账号: {in_user.value?.user_info.third_party_account}</x-item>
                    <h1 class="font-medium">用户资产</h1>
                    <x-item>金币余额(Coins): {in_user.value?.user_wallet.cash_balance}</x-item>
                    <x-item>奖励币余额(Reward coins): {in_user.value?.user_wallet.bonus_balance}</x-item>
                    <x-item>会员权益: {in_user.value?.user_wallet.vip_level === 1 ? '会员/' + in_user.value?.user_wallet.vip_period : '否'}</x-item>
                  </x-in-user-info>
                )
              },
            },
            {
              label: requiredLabel('上传截图'),
              path: 'credential_image',
              input: {
                type: 'custom',
                render: () => (
                  <x-upload-cover class="grid gap-y-2">
                    <Uploader
                      accept="png,jpg,jpeg"
                      maxsize={1024 * 1024 * 10}
                      class="size-[200px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
                      onUploadSuccess={d => {
                        formData.value.credential_image = d.temp_path || ''
                      }}
                      isImage={true}
                      uploadUrl="/banner/upload/cover"
                    >
                      {
                        formData.value.credential_image
                          ? <img src={formData.value.credential_image.includes('https://') ? formData.value.credential_image : 'https://static-v1.mydramawave.com/banner/cover/' + formData.value.credential_image} class="size-full object-cover" />
                          : <span class="size-full flex items-center justify-center">上传凭据</span>
                      }
                    </Uploader>
                    <x-upload-cover-tip class="text-gray-600 text-sm">png,jpg,jpeg格式，大小限制10M</x-upload-cover-tip>
                  </x-upload-cover>

                ),
              },
            },
          ]}
          data={formData.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={() => emit('hide')}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            return
          }
          void httpClient.post<ApiResponse<M.UserQuery.Response>>('/wallet/asset/transfer', {
            ...formData.value,
            out_user_id: props.user.user_info?.user_id,
          }).then(rs => {
            console.log('rs', rs)

            if (rs.code === 200) {
              emit('hide')
              showAlert('转移成功')
            }
          })
        }}
        >确定
        </Button>
      </div>
    </div>
  )
})
