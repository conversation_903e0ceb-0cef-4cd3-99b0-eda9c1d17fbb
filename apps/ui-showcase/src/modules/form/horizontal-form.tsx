import { createComponent } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { FormItem } from '@skynet/ui/form/form-types'
import { ref } from 'vue'
export const HorizontalForm = createComponent(null, () => {
  type Data = { name: string, age: number }
  const Form = CreateForm<Data>()
  const data = ref<Data>({ name: '张三', age: 18 })
  const items: FormItem[] = [
    ['name', 'name', { type: 'text' }],
    ['age', 'age', { type: 'number' }],
  ]
  return () => (
    <Form data={data.value} items={items} />
  )
})

export default HorizontalForm
