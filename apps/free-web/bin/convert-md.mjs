
import fs from 'fs/promises';
import path from 'path';
import MarkdownIt from 'markdown-it';

const md = new MarkdownIt();
const map = {
  privacy: 'Privacy Policy',
  terms: 'Terms of Service'
}

function throwError(msg) {
  throw new Error(msg);
}

async function getMarkdownFiles(dir) {
  let files = [];
  const entries = await fs.readdir(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      const subFiles = await getMarkdownFiles(fullPath);
      files = files.concat(subFiles);
    } else if (entry.isFile() && path.extname(entry.name) === '.md') {
      files.push(fullPath);
    }
  }
  return files;
}

async function processMarkdownFiles() {
  const baseDir = 'bin/public';
  const outputDir = 'public';
  const platformMdTemplatePath = 'bin/md-template-platform.html';
  const mdTemplatePath = 'bin/md-template.html';
  const mdTemplate = await fs.readFile(mdTemplatePath, 'utf-8');
  const platformMdTemplate = await fs.readFile(platformMdTemplatePath, 'utf-8');

  const mdFiles = await getMarkdownFiles(baseDir);

  for (const mdFilePath of mdFiles) {
    const relativePath = path.relative(baseDir, mdFilePath);
    const outputFilePath = path.join(outputDir, relativePath.replace(/\.md$/, '.html'));

    const content = await fs.readFile(mdFilePath, 'utf-8');
    const htmlContent = md.render(content);
    const fileName = path.basename(mdFilePath, '.md');
    const title = map[fileName];
    let finalHtml;
    if (mdFilePath.includes('dramareels')) {
      finalHtml = platformMdTemplate.replace('__content__', htmlContent).replace('__title__', title);
    } else {
      finalHtml = mdTemplate.replace('__content__', htmlContent).replace('__title__', title);
    }

    await fs.mkdir(path.dirname(outputFilePath), { recursive: true });
    await fs.writeFile(outputFilePath, finalHtml, 'utf-8');
  }
}

processMarkdownFiles();