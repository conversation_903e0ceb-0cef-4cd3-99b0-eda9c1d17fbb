/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { useResourceStore } from '../use-resource-publish-store'
import { ElCascader, ElTooltip } from 'element-plus'

type CheckInfoFormOptions = {
  props: {
    editable?: boolean
  }
}

export const CheckInfoForm = createComponent<CheckInfoFormOptions>({
  props: {
    editable: true,
  },
}, props => {
  const { passFormData, validateInfo, firstLabels, secondLabels, rootLabels } = useResourceStore()
  const Form = CreateForm<M.IPassForm>()
  const options = {
    multiple: true,
    expandTrigger: 'hover' as const,
    checkStrictly: true,
    disabled: (data: any) => {
      return data.children && data.children.length > 0
    },
  }
  return () => (
    <>
      <Form
        class={mc('w-full', 'grid m-auto grid-cols-3')}
        error={validateInfo.error.value}
        data={passFormData.value}
        hasAction={false}
        onChange={(path, value) => {
          set(passFormData.value, path, value)
        }}
        items={[
          [
            requiredLabel('一级标签：'),
            'root_label_ids',
            {
              type: 'multi-select',
              search: true,
              maxlength: 1,
              disabled: !props.editable,
              options: rootLabels.value.map(row => {
                return {
                  label: row.content,
                  value: row.label_id,
                }
              }),
            },
            {
              class: 'col-span-2',
            },
          ],
          [
            requiredLabel('二级标签：'),
            'first_label_ids',
            {
              type: 'multi-select',
              search: true,
              maxlength: 1,
              disabled: !props.editable,
              options: firstLabels.value.map(row => {
                return {
                  tooltip: row.meaning,
                  label: row.content,
                  value: row.label_id,
                }
              }),
            },
            {
              class: 'col-span-2',
            },
          ],
          [
            requiredLabel('三级标签：'),
            'second_label_ids',
            {
              type: 'custom',
              render: () => (
                <ElCascader
                  modelValue={passFormData.value.second_label_ids}
                  placeholder="请选择三级标签"
                  collapse-tags-tooltip
                  options={secondLabels.value}
                  disabled={!props.editable}
                  clearable
                  filterable
                  props={options}
                  onChange={e => {
                    set(passFormData.value, 'second_label_ids', e)
                    validateInfo.validate('second_label_ids')
                  }}
                  v-slots={{
                    default: ({ node, data }: any) => {
                      if (node.isLeaf && data.meaning) {
                        return (
                          <ElTooltip
                            content={data.meaning}
                            placement="top"
                            effect="light"
                            popperClass="!max-w-[400px]"
                          >
                            <span>{data.label}</span>
                          </ElTooltip>
                        )
                      }
                      return <span>{data.label}</span>
                    },
                  }}
                />
              ),
            },
            {
              class: 'col-span-2',
              hint: () => (
                <x-label-tip class="mt-2 flex flex-col gap-y-1 text-sm text-gray-600">
                  <x-tip>
                    情节（多选）、角色（多选）、背景（单选）
                  </x-tip>
                </x-label-tip>
              ),
            },
          ],
        ]}
      />
    </>
  )
})
