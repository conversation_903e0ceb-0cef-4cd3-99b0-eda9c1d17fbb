declare namespace M {
  namespace SeriesPackage {
    type PackageType1Config = {
      ios_series_keys?: string[]
      android_series_keys?: string[]
    }

    type PackageType2Config = {
      ios_url?: string
      android_url?: string
    }

    type PackageType3Config = {
      ios_list?: M.Drama[]
      android_list?: M.Drama[]
      ios_new_start_paying_episodes?: number
      android_new_start_paying_episodes?: number
    }

    type PackageType4Config = {
      language_version_code?: string[]
      app_id?: number
      listing_status?: number[]
      resource_type?: number
      free_status?: number
      serialize_status?: number
      release_round?: number
      episode_count_begin?: string
      episode_count_end?: string
      protect_begin?: string
      protect_end?: string
      amount_begin?: string
      amount_end?: string
      spend_begin?: string
      spend_end?: string
      vip_types?: number[]
      spend_avg_begin?: string // 平均花费最小
      spend_avg_end?: string // 平均花费最大
      spend_avg_day?: number // 平均花费天数
      audio_types?: number[] // 空数组=全部，音频类型 0：默认 1：配音上传剧 2：AI配音剧
      spend_pay_avg_begin?: string // 付费投放平均花费最小
      spend_pay_avg_end?: string // 付费投放平均花费最大
      first_list_begin?: string
      first_list_end?: string
    }

    interface PackageType5Config {
      collection_type: number // 集合方式 1 正选 2 反选
      collection_package_ids: number[] // 普通剧包列表
    }

    type PackageConfig = {
      package_type_1_config?: PackageType1Config
      package_type_2_config?: PackageType2Config
      package_type_3_config?: PackageType3Config
      package_type_4_config?: PackageType4Config
      package_type_5_config?: PackageType5Config
    }

    type Details = {
      id?: number
      package_name?: string
      state?: number
      priority?: number
      remark?: string
      scene_type?: number
      import_type?: number
      country_include?: string
      country_exclude?: string
      user_platform?: number // 用户平台 0 all 1 IOS 2 Android
      package_config?: PackageConfig
      created?: number
      created_operator_id?: string
      created_operator_name?: string
      updated?: number
      updated_operator_id?: string
      updated_operator_name?: string
      deleted?: number
      import_type?: number // 添加方式 1 手动导入 2 批量上传 3 筛选导入 4 指定范围 5 集合
      scene_type?: number // 应用场景 1 商业化策略 2 付费卡点策略 3 首页内容 4 活动 5 运营位弹窗
      item_count?: number // 策略数量
      isEdit?: boolean
      language_version_code?: string
      needDisabled?: boolean
    }
  }
}

declare namespace Api {
  namespace SeriesPackage {
    namespace ListQuest {
      type query = {
        page_info?: PageInfo
        id?: number
        state?: 1 | 2 | number // 状态 1 生效 2 失效
        package_name?: string
        // ASC: 从小到大, DESC: 从大到小
        sort_desc?: string
        sort?: string
        sort_type?: string
        scene_type?: 1 | 2 | 3 | 4 | 5 | number // 应用场景 1 商业化策略 2 付费卡点策略 3 首页内容 4 活动 5 运营位弹窗
        import_types?: number[] // 添加方式 1 手动导入 2 批量上传 3 筛选导入 4 指定范围 5 集合
      }

      type Response = {
        /** 总数 */
        total?: number
        /** 列表 */
        list?: M.SeriesPackage.Details[]
      }
    }
  }
}
