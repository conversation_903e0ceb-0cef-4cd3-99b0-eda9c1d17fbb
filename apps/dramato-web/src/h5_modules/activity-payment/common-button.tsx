import { createComponent, fn } from '@skynet/shared'
import { confirmBtn, disabledBtn } from './images/images'
import { Fn } from '@vueuse/core'
import { replaceHelper } from './replace-helper'
type CommonButtonOptions = {
  props: {
    text: string
    disabled?: boolean
  }
  emits: {
    click: Fn
  }
}
export const CommonButton = createComponent<CommonButtonOptions>({
  props: {
    text: '',
    disabled: false,
  },
  emits: {
    click: fn,
  },
}, (props, { emit }) => {
  return () => (
    <x-common-button class={`relative mx--3 block capitalize ${props.disabled ? 'opacity-50' : ''}`} onClick={() => {
      if (props.disabled) {
        return
      }
      emit('click')
    }}>
      <img src={props.disabled ? disabledBtn : confirmBtn} class="relative z-up block h-auto w-full object-cover" />
      <x-content class="absolute inset-0 z-up-up flex size-full items-center justify-center">
        <span class="relative z-up-up text-base font-bold text-white">{props.text}</span>
      </x-content>
    </x-common-button>
  )
})

export default CommonButton
