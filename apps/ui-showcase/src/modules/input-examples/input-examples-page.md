---
name: InputExamplesPage
title: 输入框
---

# attributes

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| type | 输入框类型，可选值为 text、textarea、password、number、date、checkbox、radio | String | text |
| modelValue | 绑定的值 | String | - |
| class | 容器样式 | String | - |
| inputClass | input样式 | String | - |
| rows | 文本域默认行数，仅在 type 为 textarea 时有效 | Number | - |
| max | 最大值，仅在 type 为 number 时有效 | Number | - |
| min | 最小值，仅在 type 为 number 时有效 | Number | - |
| step | 步长，仅在 type 为 number 时有效 | Number | - |
| maxlength | 最大输入长度，仅在 type 为 text 或 textarea 时有效 | Number | - |
| show-word-limit | 是否显示统计字数，仅在 type 为 text 或 textarea 时有效 | Boolean | false |
| error | 错误提示信息 | String | - |
| disabled | 是否禁用 | Boolean | false |
| clearable | 是否可清空 | Boolean | false |
| placeholder | 占位文本 | String | - |
| format | 日期格式，仅在 type 为 date 时有效 | String | YYYY-MM-DD |
| searchable | 是否可搜索，仅在 type 为 text 时有效 | Boolean | false |
| results | 搜索结果，是一个组件render出来的内容 | VNode | - |
| options | 选项，仅在 type 为 checkbox 或 radio 时有效 | Array | - |
| slots | 插槽 | Object | - |
| suffix | 后缀插槽 | SlotFn | - |
| prefix | 前缀插槽 | SlotFn | - |

### type

<Demo component="/src/modules/input-examples/input-examples-type.tsx" />

### show-word-limit

<Demo component="/src/modules/input-examples/input-examples-word-limit.tsx" />

### disabled

<Demo component="/src/modules/input-examples/input-examples-disabled.tsx" />

### clearable

<Demo component="/src/modules/input-examples/input-examples-clearable.tsx" />

### searchable

<Demo component="/src/modules/input-examples/input-examples-searchable.tsx" />

### prefix & suffix

<Demo component="/src/modules/input-examples/input-examples-prefix.tsx" />
