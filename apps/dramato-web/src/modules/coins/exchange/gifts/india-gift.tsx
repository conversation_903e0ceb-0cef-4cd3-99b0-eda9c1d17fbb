import { createComponent, getQuery, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { coins, indiaCard } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { ref, watch } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { useRedeem } from '../use-redeem'
import { useRouter } from 'vue-router'
type GiftOptions = {
  props: {
    gift?: GiftType
  }
}
export const IndiaGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      type: 'india',
    },
  },
}, props => {
  const { t } = useLocale(getQuery('language', 'en'))
  const tipsRef = ref<HTMLDivElement>()
  const { reachLimit } = useRedeem(props.gift)
  const router = useRouter()

  const handleClick = () => {
    if (props.gift?.can_exchange) {
      if (props.gift?.button_txt && props.gift?.button_link) {
        window.location.href = props.gift.button_link
        return
      }
      if (reachLimit.value) {
        void router.push({
          path: '/coins/exchange/redeem/india',
          query: {
            ...router.currentRoute.value.query,
            amount: props.gift?.amount,
            id: props.gift?.id,
          },
        })
      } else {
        window.location.href = 'dramawave://dramawave.app/rewards_task?enter_from=exchange_page'
      }
    }
  }

  const flag = ref(false)
  watch(() => [tipsRef.value, flag.value], () => {
    flag.value = false
    if (tipsRef.value) {
      const lineHeight = parseFloat(getComputedStyle(tipsRef.value).lineHeight)
      const fontSize = parseFloat(getComputedStyle(tipsRef.value).fontSize)
      if (fontSize <= 8) {
        return
      }
      const maxHeight = lineHeight * 2
      if (tipsRef.value.scrollHeight > maxHeight) {
        tipsRef.value.style.fontSize = `${fontSize - 2 <= 8 ? 8 : fontSize - 2}px`
        flag.value = true
      }
    }
  })

  return () => (
    <x-gift-item class="flex gap-x-3 p-3">
      <x-gift-item-left class={mc('flex max-w-[44px] flex-col gap-1', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-card class="box-border size-11 rounded-lg border border-solid border-[#ece9ec] px-1 py-3">
          <img src={props?.gift?.image_url ?? indiaCard} class="h-auto w-full object-cover" />
        </x-card>
        <x-price class="block w-full break-all text-center text-xs font-bold text-[#4d4e4f]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class={mc('flex flex-1 flex-col gap-1 truncate', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-gift-title class="w-full truncate text-sm text-[#4d4e4f]">{props.gift.name}</x-gift-title>
        {
          props.gift?.can_exchange && (
            <x-gift-coins class="flex items-center text-sm">
              <img src={coins} width={16} height={16} class="mr-[2px] size-4 object-contain" />
              <x-gift-amount class="text-[#fc2763]">{props.gift.user_coins}</x-gift-amount>
              <x-separator class="px-[2px] text-[#a1a2a3]">/</x-separator>
              <x-gift-remains class="text-[#090609]">{props.gift.gift_coins}</x-gift-remains>
            </x-gift-coins>
          )
        }
        {
          props.gift?.can_exchange && (
            <x-gift-progress>
              <Progress class="mt-1 bg-[#ECE9EC]" value={props.gift.ratio} max={1} />
            </x-gift-progress>
          )
        }
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21 justify-center', props.gift?.button_tips ? 'gap-y-1' : '')}>
        <Button class={mc('min-h-8 w-full rounded-lg border-none px-1 pt-[2px] pb-[3px]  text-sm', !props.gift?.can_exchange ? 'bg-[#FFA0B6] !text-[#FDFBFC80]' : 'bg-[#fc2763] !text-white')} onClick={handleClick}>
          <p class="line-clamp-2 h-auto w-full text-wrap break-words text-center leading-4">
            {reachLimit.value ? t('coins.earnings.exchange') : t('coins.exchange.earnCoins')}
          </p>
        </Button>
        {props.gift?.button_tips && <x-tips class={mc('line-clamp-2 h-auto w-full break-words text-center text-[10px] leading-3 text-[#4d4e4f]', props.gift?.can_exchange ? '' : 'opacity-30')} ref={tipsRef}>{props.gift?.button_tips}</x-tips>}
      </x-gift-item-right>
    </x-gift-item>
  )
})
