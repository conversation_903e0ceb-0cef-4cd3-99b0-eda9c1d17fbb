import { r, redirect } from '@skynet/shared'

export const userEngagementRoutes = [
  r('new-user-engagement', '新用户承接', null, [
    r('', '新用户承接', () => import('src/modules/user-engagement/user-engagement-page.tsx')),
    r('create', '新建用户承接', () => import('src/modules/user-engagement/user-engagement-create-layout.tsx'), [
      redirect('', 'target'),
      r('target', '新建用户承接 - 目标条件', () => import('src/modules/user-engagement/user-engagement-create-target-page.tsx')),
      r('strategy', '新建用户承接 - 策略配置', () => import('src/modules/user-engagement/user-engagement-create-strategy-page.tsx')),
    ]),
  ]),
]
