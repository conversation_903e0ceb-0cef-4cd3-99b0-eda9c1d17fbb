// server/utils/lruCache.ts
class LRUCache<T> {
  private cache = new Map<string, T>();
  private max: number;

  constructor(max: number) {
    this.max = max;
  }

  get(key: string) {
    if (!this.cache.has(key)) return undefined;
    // 取出后重新放到末尾，表示最近使用
    const value = this.cache.get(key);
    this.cache.delete(key);
    this.cache.set(key, value);
    return value;
  }

  set(key: string, value: T) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.max) {
      // 删除最早的
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}

const lruCache = new LRUCache(10);
export default lruCache;
