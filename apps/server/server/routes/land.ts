import { Time } from '@skynet/shared/const';
import { landCache } from './_land-cache';
import { translations } from './_land.i18n';
import { htmlTemplateEngine } from '~/utils/html-template-engine';

type CachedItem = { value: JsonValue, createdAt: number }
let html = ''
let compiledHtml: null | ((data: JsonObject) => string) = null

export default defineEventHandler(async event => {
  setResponseHeader(event, 'Content-Type', 'text/html');
  setResponseStatus(event, 200);
  const config = useRuntimeConfig()
  const query = getQuery(event)
  const { ip } = useIp()
  const { languageAndLocale } = useLanguage()
  const seriesItem = await useSeriesItem()
  const language = seriesItem.original_audio_language as string || languageAndLocale
  const t = translations[language] || translations['en-US']
  if (!html) {
    html = (await useStorage('assets:templates').getItem('land.html'))?.toString() ?? ''
  }
  if (!compiledHtml) {
    compiledHtml = htmlTemplateEngine.compile(html).bind(htmlTemplateEngine)
  }
  const renderedHtml = compiledHtml({ seriesItem, ip, t, })

  return renderedHtml
  async function useSeriesItem() {
    const cachedItem = await landCache.getItem<CachedItem>(query.content_id?.toString() || '')
    if (cachedItem && cachedItem.value && cachedItem.createdAt && Date.now() - cachedItem.createdAt < Time.hour * 6) {
      return cachedItem.value
    }
    const res = await fetch(config.public.apiUrl + '/dm-api/drama/share/series_info?series_id=' + query.content_id, { method: 'GET' })
      .then(res => res.json() as Promise<ApiResponse<{ series_info: JsonValue }>>)
    if (!res.data || !res.data.series_info) {
      return Promise.reject(new Error('Series item not found'))
    }
    landCache.setItem<CachedItem>(query.content_id?.toString() || '', { value: res.data.series_info, createdAt: Date.now() })
    return res.data.series_info
  }
  /**
   * 从请求头中获取客户端 IP 地址
   */
  function useIp() {
  // 获取请求头
    const headers = event.node.req.headers

    // 尝试从不同的请求头中获取 IP
    let clientIp: string | null = null

    if (headers['x-forwarded-for']) {
      const forwardedFor = headers['x-forwarded-for'].toString()
      clientIp = forwardedFor ? forwardedFor.split(',')[0].trim() : null
    } else if (headers['x-real-ip']) {
      const realIp = headers['x-real-ip'].toString()
      clientIp = realIp ? realIp.split(',')[0].trim() : null
    } else if (headers['remote-addr']) {
      const remoteAddr = headers['remote-addr'].toString()
      clientIp = remoteAddr ? remoteAddr.split(',')[0].trim() : null
    }

    // 如果都获取不到，返回默认值
    return {
      ip: clientIp || 'unknown'
    }
  }
  /**
   * 从 event 中获取语言设置
   */
  function useLanguage() {
    const headers = event.node.req.headers
    const acceptLanguage = headers['accept-language'] || ''
    // 默认语言为英语
    let languageAndLocale = 'en-US'

    if (acceptLanguage) {
    // 从请求头中获取首选语言
      const preferredLanguage = acceptLanguage.split(',')[0]
      if (preferredLanguage) {
        languageAndLocale = preferredLanguage
      }
    }

    // 提取主要语言部分（如 zh-CN 中的 zh）
    const language = languageAndLocale.split('-')[0]

    // 确保 languageAndLocale 包含连字符"-"
    if (!languageAndLocale.includes('-')) {
    // 如果不包含连字符，则添加默认区域设置
    // 为常见语言设置默认区域
      const defaultRegions: Record<string, string> = {
        zh: 'CN', en: 'US', ja: 'JP', ko: 'KR', fr: 'FR', de: 'DE', es: 'ES', ru: 'RU', pt: 'PT', vi: 'VN', th: 'TH',
        id: 'ID', tl: 'PH', it: 'IT', tr: 'TR', ms: 'MY'
      }

      const region = defaultRegions[language] || 'US'
      languageAndLocale = `${language}-${region}`
    }
    return { languageAndLocale, language }
  }
})
