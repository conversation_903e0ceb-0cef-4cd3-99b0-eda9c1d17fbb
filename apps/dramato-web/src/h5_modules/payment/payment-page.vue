<template>
  <div class="min-h-screen bg-[var(--grey-2)] relative">
    <NavBar :title="T.paymentPage.payTitle.ref.value" placeholder fixed class="text-text-1 font-medium" :border="false"
      @click-left="router.back()">
      <template #left>
        <SvgIcon name="ic-chevron-left" class="size-6 text-text-1 " />
      </template>
    </NavBar>
    <div class="bg-[#fff] px-4 pb-5 rounded-b-[20px] ">
      <!-- <h2 class="text-center p-4 text-[#434546] text-[1.375rem] font-normal">
        {{ T.paymentPage.paySubtitle.ref }}
      </h2> -->
      <!-- 充值账号 -->
      <div class="flex justify-between items-center mb-2 p-3">
        <span class="text-base font-medium  text-[#434546]">{{ T.paymentPage.rechargingAccount.ref }}</span>
      </div>
      <div class="border border-line-2 border-solid px-3 rounded-lg">
        <div class="py-4 font-medium text-[#434546]">
          DramaWave UID
        </div>
        <Divider :style="{ color: '#CCCACB', borderColor: '#CCCACB', margin: '0' }" />
        <div class="flex items-center justify-between py-4  ">
          <div v-if="uid" class="flex items-center">
            <Image :src="user.icon || PaymentLogo" class="w-6 h-6 rounded-full overflow-hidden" />
            <span class="ml-2 text-[#434546]">{{ user.user_id }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="px-3">
      <h2 class="mt-5 mb-3 font-medium text-base text-[#434546]">
        {{ T.paymentPage.rechargeOption.ref }}
      </h2>
      <!-- 充值选项 -->
      <Collapse v-model="activeNames">
        <CollapseItem v-for="item in payChannelList" :key="item.pay_channel" :name="item.pay_channel"
          class="mb-2 rounded-lg overflow-hidden" :class="item.pay_channel === 'Apple Store' && !isIos() && 'hidden'">
          <template #title>
            <div class="flex items-center gap-4">
              <Image :src="item.icon" class="w-[4.375rem] h-[1.25rem]" />
              <div class="flex flex-col">
                <span class="text-base font-medium text-text-1">{{ item.pay_channel }}</span>
                <span class="text-brand-6 text-xs flex items-center">{{ unitPriceAmount }}{{
                  item.product_list[0]?.currency }} ≈
                  <SvgIcon name="ic-coin" class="size-3 ml-1" /> {{ unitPriceCoins }}
                </span>
              </div>
            </div>
          </template>
          <div v-for="option in item.product_list" :key="option.sku_id" class="flex items-center justify-between p-3"
            :class="{ 'border-2 border-primary': selectedOption === option }">
            <div class="flex items-center">
              <SvgIcon name="ic-coin" class="size-5" />
              <!-- <img :src="Crown" class="size-5" /> -->
              <div class="ml-2">
                <div class="flex items-center">
                  <span class="text-base font-medium text-text-1 ">{{ option.delivery_details.quanity }}</span>
                  <span v-if="option.delivery_details.bonus" class="ml-2 text-xs text-brand-6">+{{
                    option.delivery_details.bonus }} Bonus</span>
                </div>
              </div>
            </div>
            <span class="text-brand-6 w-[6.5rem] h-8 rounded-lg bg-[#FFF0F3] flex items-center justify-center"
              @click="() => handleProductClick(item.pay_channel, option.product_id, item.payment_channel, item.sub_payment_channel)">{{
                option.currency_symbol }}{{
                option.price / 100 }}</span>
          </div>
        </CollapseItem>
      </Collapse>

      <!-- 支付方式 -->
      <div class="grid grid-cols-8 gap-auto my-5">
        <SvgIcon v-for="(card, index) in paymentCards" :key="index" :name="card"
          class="h-6 w-[2.2rem] object-contain text-white" />
      </div>

      <!-- 用户须知 -->
      <div class="text-base font-medium text-[#434546] mb-3">
        <h3 class="font-medium mb-2">
          {{ T.paymentPage.notice.ref }}
        </h3>
        <ol class="px-4">
          <li v-for="(notice, index) in notices" :key="index" class="list-decimal text-sm font-normal leading-4 mb-2 ">
            {{ notice }}
          </li>
        </ol>
        <p>
          {{ T.paymentPage.contact.ref }}
        </p>
      </div>

      <!-- 服务条款 -->
      <div class="mt-7 text-center text-xs pb-4 text-blue-600">
        <span class="mx-2 underline" @click="gotoRules('terms')">{{ T.paymentPage.termsOfUse.ref }}</span>
        <span>·</span>
        <span class="mx-2 underline" @click="gotoRules('privacy')">{{ T.paymentPage.privacyPolicy.ref }}</span>
      </div>
    </div>
    <SvgIcon v-if="payLoading" name="payment/loading"
      class="size-8 animate-spin fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
    <Overlay :show="showTip" @click="showTip = false">
      <div class="flex justify-center items-center flex-col h-full" @click.stop>
        <Image class="w-[78%]" :src="Dialog" />
        <SvgIcon name="ic-close-circle" class="size-8" @click="showTip = false" />
      </div>
    </Overlay>
    <Popup v-model:show="showPop" :destroy-on-close="true" :close-on-click-overlay="true" position="bottom"
      :style="{ height: '220px' }" class="rounded-t-lg">
      <PayThirdButton :payChannel="payChannel" :paySubChannel="paySubChannel" :type="thirdType" :product-id="productId"
        :from="route.query.return_url as string || '/'" @clickRule="gotoRules" />
    </Popup>
  </div>
</template>

<script setup lang="ts">
import { SvgIcon } from '@skynet/ui'
import { ref, onBeforeMount, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NavBar, Divider, Image, Collapse, CollapseItem, Overlay, Popup } from 'vant'
import { PaymentLogo, Dialog, Crown, paypal, apple, google, visa } from './images/images';
import { usePayment } from './use-payment';
import { getUser, setUser } from 'src/h5_modules/common/user'
import PayThirdButton from './payment-third-button.vue'
import { T } from './payment-page.i18n'
import { gotoRules } from 'src/h5_modules/common/common'
import { setIOSTheme } from 'src/lib/set-ios-theme'
import { isIos } from 'src/lib/ua'
import { h5Track } from 'src/lib/h5-track';

const activeNames = ref(['1'])
const showTip = ref(false)
const router = useRouter()
const route = useRoute()
const { getProductList, productData, uid, handlePay, thirdType, showPop, productId, selectedPayChannel, payChannel, paySubChannel } = usePayment()
const selectedOption = ref(null)
const user = ref()
const payLoading = ref(false)

const unitPriceAmount = computed(() => {
  return productData.value?.unit_price_amount
})

const unitPriceCoins = computed(() => {
  return productData.value?.unit_price_coins
})

const payChannelList = computed(() => {
  return productData.value?.channel_list?.map(item => ({
    ...item,
    icon: item.pay_channel === 'Paypal' ? paypal : item.pay_channel === 'Apple Store' ? apple : item.pay_channel === 'Google Play' ? google : visa,
    product_list: productData.value?.recharge_list
  }))
})

onBeforeMount(async () => {
  const { auth_key, auth_secret, icon, user_id, user_type, name } = route.query
  if (user_id) {
    setUser({
      auth_key: auth_key as string,
      auth_secret: auth_secret as string,
      icon: icon as string,
      name: name as string,
      user_id: Number(user_id),
      user_type: Number(user_type)
    })
  }

  user.value = getUser()
  uid.value = user.value.user_id
  await getProductList()
})

onMounted(() => {
  // 设置状态栏颜色
  setIOSTheme('#fff')
})

onUnmounted(() => {
  setIOSTheme('#0b080b')
})


const handleProductClick = async (channel: string, id: number, pay_channel: string, sub_payment_channel: string) => {
  h5Track('h5', 'payment', 'source', {
    iap_pop: true,
  })
  productId.value = id;
  selectedPayChannel.value = channel
  payChannel.value = pay_channel
  paySubChannel.value = sub_payment_channel
  handlePay({
    from: route.query.return_url as string || '/',
    series_id: route.query.series_id as string,
    video_id: route.query.video_id as string,
    video_type: route.query.video_type as string,
  })
}


const paymentCards = [
  'payment/visa',
  'payment/mastercard',
  'payment/jcb',
  'payment/amex',
  'payment/unionpay',
  'payment/apple',
  'payment/google',
  'payment/paypal'
]

const notices = computed(() => {
  return T.paymentPage.noticeDetail.ref.value.split('\n').map(item => item.replace(/^\d./g, ''))
})

</script>

<style scoped>
:deep(.van-nav-bar__title) {
  color: var(--text-1);
  font-weight: 500;
}

:deep(.van-nav-bar) {
  --van-nav-bar-background: #ffffff;
}

:deep(.van-cell) {
  --van-cell-vertical-padding: 12px;
  --van-cell-horizontal-padding: 12px;
  align-items: center;
}

:deep(.van-collapse-item__wrapper) {
  --van-collapse-item-content-padding: 0px;
}

:deep(.van-collapse-item--border:after) {
  border: none;
}

:deep(.van-collapse-item__title--expanded:after) {
  left: 0;
  right: 0;
}

:deep(.van-collapse) {
  --van-border-width: 0px;
}

:deep(.van-popup) {
  --van-popup-background: #fff
}
</style>