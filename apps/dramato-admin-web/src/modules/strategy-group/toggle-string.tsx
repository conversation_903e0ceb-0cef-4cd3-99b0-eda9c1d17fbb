import { createComponent, fn, mc } from '@skynet/shared'
import { Checkbox, MergeClass } from '@skynet/ui'
import { set } from 'lodash-es'
import { ref } from 'vue'
type ProfileItemValue = string[]
type ToggleStringOptions = {
  props: {
    modelValue?: ProfileItemValue
    inputClass?: string
  }
  emits: {
    'update:modelValue': (value: ProfileItemValue) => void
  }
}
export const ToggleString = createComponent<ToggleStringOptions>({
  props: {
    modelValue: ['invalid'],
    inputClass: '',
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const onChange = (path: string | number, value: string | number | null) => {
    const clone = [...props.modelValue] as ProfileItemValue
    set(clone, path, value)
    emit('update:modelValue', clone)
  }

  const disabled = ref(['/strategy-group/create/target'].includes(location.pathname))

  return () => (
    <MergeClass baseClass="flex gap-4 items-center">
      <Checkbox modelValue={props.modelValue[0] === 'valid'} disabled={disabled.value} label="使用" onUpdate:modelValue={v =>
        onChange(0, v ? 'valid' : 'invalid')} class="shrink-0"
      />
      <input type="text" v-model={props.modelValue[1]} disabled={disabled.value} class={mc('input input-bordered input-sm flex-1', props.inputClass)} />
    </MergeClass>
  )
})
