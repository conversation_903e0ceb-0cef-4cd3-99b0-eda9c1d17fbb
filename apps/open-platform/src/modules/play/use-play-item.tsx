import { createCachedFn } from '@skynet/shared'
import { ref } from 'vue'
import { playApi } from './play-api'
import { ElMessage } from 'element-plus'
import { keepError } from 'src/lib/http-client'
import { useI18n } from 'vue-i18n'
const defaultFormData = (): M.Play => ({
  play_key: '',
  title: '',
  label_ids: [],
  description: '',
  play_path: '',
  cover: '',
  audit_failed_reason: '',
  audit_failed_reason_en: '',
})

export const usePlayItem = createCachedFn((play_key: string) => {
  const { t } = useI18n()
  const formData = ref<M.Play>(defaultFormData())
  const resetFormData = () => {
    formData.value = defaultFormData()
  }
  const fetchItem = async () => {
    const data = await playApi.getItem(play_key)
    formData.value = data
  }
  const save = async () => {
    const data = await playApi.createOrUpdate(formData.value)
      .catch(keepError(error => ElMessage.error(error.message || t('operationFailed'))))
    ElMessage.success(t('operationSuccessful'))
    formData.value = data
  }
  const submitForAudit = async () => {
    await playApi.submitForAudit(formData.value.play_key)
      .catch(keepError(error => ElMessage.error(error.message || t('operationFailed'))))
    ElMessage.success(t('operationSuccessful'))
  }
  return {
    formData,
    resetFormData,
    save,
    fetchItem,
    submitForAudit,
  }
})
