@import './reset.css';
@import './vars.css';
@import './h5-color-scheme.css';
@import './components.css';
@import './animate.css';
@import './vant.css';
@unocss;

:root {
  color-scheme: light; /* 强制使用浅色 */
}

body {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  background-color:#0B080B;
  color: white;
  padding: 0 !important;
}

* {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    SF Pro,
    Arial,
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    sans-serif;
}


a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.no-tap-color {
  -webkit-tap-highlight-color: transparent;
  /* iOS Chrome */
  -ms-tap-highlight-color: transparent;
  /* IE */
  -moz-tap-highlight-color: transparent;
  /* Firefox */
  -o-tap-highlight-color: transparent;
  /* Opera */
}

.tm-overflow-hidden {
  overflow: hidden;
}

/** 处理 message-content 组件自带最小高问题  */
.tiptap {
  min-height: auto !important;
}
.van-theme-dark .van-cell {
  color: #fdfbfc;
  background-color: black;
}
.van-theme-dark .van-cell:after {
  border-bottom: 1px solid #000
}
/* 隐藏滚动条 */
.scrollbar-hide {
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.right-arrow {
  background: url(/src/h5_modules/images/ic-chevron-right.webp) no-repeat right center/ 1rem 1rem;
}

.loading-animation {
  position: relative;
  overflow: hidden;
}
.loading-animation::before,
.loading-animation::after {
  content: '';
  position: absolute;
  top: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(253, 251, 252, 0.6) 0%,
    rgba(253, 251, 252, 0) 100%
  );
  animation: loadingSpread 1.5s ease-in-out infinite;
}
.loading-animation::before {
  right: 50%;
  transform-origin: right center;
  background: linear-gradient(
    -90deg,
    rgba(253, 251, 252, 0.6) 0%,
    rgba(253, 251, 252, 0) 100%
  );
}
.loading-animation::after {
  left: 50%;
  transform-origin: left center;
}
@keyframes loadingSpread {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}
