{"name": "ui-showcase", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --mode development --port 5170 --no-clearScreen --open", "build": "vite build --mode production", "build:test": "vite build --mode staging", "build:prod": "vite build --mode production", "preview": "vite preview --host --base=./", "deploy:test": "bin/deploy.sh test", "deploy:prod": "bin/deploy.sh prod"}, "dependencies": {"@skynet/client-track": "workspace:*", "@skynet/preset": "workspace:*", "@skynet/shared": "workspace:*", "@skynet/ui": "workspace:*", "@tiptap/core": "catalog:", "@tiptap/extension-document": "catalog:", "@tiptap/extension-mention": "catalog:", "@tiptap/extension-paragraph": "catalog:", "@tiptap/extension-placeholder": "catalog:", "@tiptap/extension-text": "catalog:", "@tiptap/pm": "catalog:", "@tiptap/starter-kit": "catalog:", "@tiptap/suggestion": "catalog:", "@tiptap/vue-3": "catalog:", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "ali-oss": "catalog:", "axios": "catalog:", "callapp-lib": "catalog:", "choices.js": "catalog:", "core-js": "catalog:", "dayjs": "catalog:", "echarts": "catalog:", "github-markdown-css": "catalog:", "highlight.js": "11.11.1", "html2canvas": "catalog:", "js-cookie": "catalog:", "js-md5": "catalog:", "lodash-es": "catalog:", "lottie-web": "catalog:", "markdown-it": "catalog:", "markmap-lib": "catalog:", "markmap-view": "catalog:", "pdfjs-dist": "3.11.174", "pinia": "catalog:", "pinia-plugin-persistedstate": "^3.2.1", "prosemirror-state": "^1.4.3", "qrcode": "^1", "sortablejs": "^1.15.2", "swiper": "^11.1.9", "swrv": "1.0.4", "tippy.js": "^6.3.7", "update-browserslist-db": "1.1.2", "vite-bundle-analyzer": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "zod": "catalog:"}, "devDependencies": {"@iconify/vue": "catalog:", "@skynet/vite-plugin-svg-icons": "workspace:*", "@types/lodash-es": "catalog:", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "autoprefixer": "10.4.20", "markdown-it-anchor": "9.2.0", "markdown-it-prism": "2.3.0", "markdown-it-table-of-contents": "0.9.0", "postcss": "catalog:", "postcss-import": "catalog:", "postcss-nested": "catalog:", "tailwindcss": "3.4.10", "typescript": "catalog:", "vconsole": "catalog:", "vite": "catalog:", "vite-plugin-markdown": "catalog:", "vite-plugin-md": "0.21.5", "vite-plugin-svg-icons": "catalog:", "vue-tsc": "catalog:", "zx": "catalog:"}, "browserslist": ["defaults"]}