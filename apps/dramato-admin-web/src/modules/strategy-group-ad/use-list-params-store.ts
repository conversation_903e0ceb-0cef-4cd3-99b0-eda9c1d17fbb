import { CreateForm, CreateTableOld } from '@skynet/ui'
import { ref } from 'vue'

export const useListParamsStore = () => {
  return {
    Form,
    listParams,
    Table,
    list,
    initListParams,
  }
}
const initListParams = {
  strategy_id: '',
  status: undefined,
  pay_mode: '',
  platform: '',
  sort: {
    // created: 'DESC',
    // id: 'DESC',
  },
}
type ListParams = Omit<Api.AdStrategyGroup.Request.List, 'page_info'>
const Form = CreateForm<ListParams>()
const listParams = ref<ListParams>({ ...initListParams })
const Table = CreateTableOld<M.StrategyGroup>()
const list = ref<M.StrategyGroup[]>([])
