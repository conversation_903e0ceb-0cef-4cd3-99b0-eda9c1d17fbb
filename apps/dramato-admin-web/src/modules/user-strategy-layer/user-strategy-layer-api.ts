/* eslint-disable @typescript-eslint/no-explicit-any */

import { httpClient } from 'src/lib/http-client'

export const apiCreateUserStrategyLayer = (data: M.UserStrategyLayer.UserStrategy) =>
  httpClient.post<ApiResponse<boolean>>('/strategy-group/layer/save', data)

export const apiEditUserStrategyLayer = (data: M.UserStrategyLayer.UserStrategy) =>
  httpClient.post<ApiResponse<boolean>>('/strategy-group/layer/save', data)

export const apiEditUserStrategyLayerStatus = (data: {
  id: number
  operation: number // 操作 1 上架 2 下架
}) =>
  httpClient.post<ApiResponse<boolean>>('/strategy-group/layer/status/update', data)

export const apiEditUserStrategyLayerPriority = (data: {
  id: number
  priority: number // 操作 1 上架 2 下架
}) =>
  httpClient.post<ApiResponse<boolean>>('/strategy-group/layer/priority/update', data)

export const apiGetUserProfileItem = () =>
  httpClient.get<ApiResponse<{
    list: M.UserStrategyLayer.ProfileItem[]
    series_package_list: M.UserStrategyLayer.ProfileItem[]
  }>>('/strategy-group/layer/user-profile-item', {})

export const popupApi = {
  updatePersona: (d: M.UserStrategyLayer.UserStrategy) => {
    return httpClient.post<ApiResponse<null>>('/strategy-group/layer/save', d, {
      // transformRequestData: transformSetUserConfigMap,
    })
  },
  getPersona: (id: string) =>
    httpClient.get<ApiResponse<M.UserStrategyLayer.UserStrategy>>('/strategy-group/layer/detail', { id }, {
      // transformResponseData: transformGetUserConfigMap,
    }),
}

export const apiStrategyGroupLayerEstimate = (data: {
  layer_id: number
}) =>
  httpClient.post<ApiResponse<boolean>>('/strategy-group/layer/estimate', data)
