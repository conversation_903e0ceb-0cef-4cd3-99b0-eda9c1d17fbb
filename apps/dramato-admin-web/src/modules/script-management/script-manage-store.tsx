/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, TableColumnOld, DateTime, Tooltip, showFailToast } from '@skynet/ui'
import { onBeforeMount, ref, useId } from 'vue'
import { apiGetScriptManageList } from './script-manage-api'
import { RouterLink } from 'vue-router'
import { statusDesc } from './util'
import { apiGetLabels } from './script-manage-detail-api'
import { get_k_sso_token } from 'src/lib/device-id'
import dayjs from 'dayjs'
import { checkEpisodeTitleOrId } from './util'

export const useScriptManageStore = () => {
  return {
    Form,
    params,
    Table,
    list,
    columns,
    loading,
    total,
    onPageChange,
    onPageSizeChange,
    onQuery,
    getList,
    onReset,
    currentRoles,
    isExporting,
    handleExport,
    getLabels
  }
}

const Form = CreateForm<Api.ScriptManage.Request.Params>()
const labels = ref<Api.Label.Item[]>([])
const currentRoles = ref<number[]>([])

const defaultParams = {
  page_index: 1,
  page_size: 20,
  title_or_key_list: '',
  openplatform_principal: '',
  audit_status: 0,
  audit3_rating: '',
  principal: '',
  created_start: undefined,
  created_end: undefined,
}

const params = ref<Api.ScriptManage.Request.Params>({
  ...defaultParams,
})

const columns: TableColumnOld<M.IScriptManageListItem>[] = [
  ['剧ID', 'play_key', { class: 'w-[120px]' }],
  ['剧名', row => {
    return <RouterLink class="link-primary" to={`/script/detail/${row.play_key}`}>{row.title}</RouterLink>
  }, { class: 'w-[160px]' }],
  ['公司', 'openplatform_principal', { class: 'w-[140px]' }],
  ['审核状态', row => statusDesc(row.audit_status), { class: 'w-[140px]' }],
  ['最终评级', 'audit_rating', { class: 'w-[100px]' }],
  ['责编', 'principal', { class: 'w-[130px]' }],
  ['合同状态', row => !row.contract_path
    ? <div class="badge-default badge">未签约</div> : <div class="badge badge-primary">已签约</div>, { class: 'w-[100px]' }],
  ['短剧标签', row => {
    const uniqueId = useId()
    const labelIds = row.audit_label_ids || row.label_ids
    const labelIdArr = (labelIds as string)?.split(',').map(Number)
    const labelContent = labelIdArr.map(id => labels.value.find(label => label.label_id === +id)?.content).join(',')
    return (
      <Tooltip key={uniqueId} popWrapperClass="!max-w-[400px]" popContent={() => labelContent}>
        <div class="line-clamp-1">
          {labelContent || '-'}
        </div>
      </Tooltip>
    )
  }, { class: 'w-[100px]' }],
  ['创建时间', row => <DateTime value={row.created * 1000} />, { class: 'w-[150px]' }],
  ['过审时间', row => <DateTime value={row.audit_pass_time * 1000} />, { class: 'w-[150px]' }],
  ['完本时间', row => <DateTime value={row.finish_time * 1000} />, { class: 'w-[150px]' }],
]

const Table = CreateTableOld<M.IScriptManageListItem>()
const list = ref<M.IScriptManageListItem[]>([])
const loading = ref<boolean>(false)
const total = ref<number>(1)
const isExporting = ref(false)

const exportExcel = (data: any) =>
  fetch(`${import.meta.env.VITE_DRAMA_API_URL}/open_platform_in/play/play_data_export`, {
    method: 'post',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      Device: 'Web',
      Token: get_k_sso_token() || '',
    },
    body: JSON.stringify(data),
  })

const handleExport = async () => {
  try {
    isExporting.value = true
    try {
      const blob =  await exportExcel({
        ...params.value,
        title_or_key_list: checkEpisodeTitleOrId(params.value.title_or_key_list as string),
      }).then(response => response.blob())
      if (blob.size < 20) {
        throw new Error('导出失败')
      }
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `剧本审核列表_${dayjs(Date.now()).format('YYYYMMDDHHmmss')}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error: any) {
      showFailToast('导出失败')
    }
  } catch (error: any) {
    showFailToast(error.response.data.message || '导出失败')
  } finally {
    isExporting.value = false
  }
}
const getList = async () => {
  loading.value = true
  const res = await apiGetScriptManageList({
    ...params.value,
    created_start: params.value.created_start ? (params.value.created_start == '' ? undefined : params.value.created_start) : undefined,
    created_end: params.value.created_end ? (params.value.created_end == '' ? undefined : params.value.created_end) : undefined,
  })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.total || 0
  currentRoles.value = res.data?.open_roles || []
}

const onPageChange = (page: number) => {
  params.value.page_index = page
  void getList()
}
const onPageSizeChange = (size: number) => {
  params.value.page_size = size
  params.value.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_index = 1
  void getList()
}

const onReset = () => {
  params.value = {
    ...defaultParams,
  }
  void getList()
}

const getLabels = async () => {
  const labelRes = await apiGetLabels({ language_code: 'zh-CN' })
  labels.value = labelRes.data?.list || []
}
