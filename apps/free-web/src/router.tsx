import { r } from '@skynet/shared'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from './layouts/main-layout'
import { HomePage } from './modules/home/<USER>'
import { customerServiceCenterRoutes } from './modules/customer-service-center/customer-service-center-routes'
import { regulationsRoutes } from './modules/regulations/regulations-routes'
import { coinsRoutes } from './modules/coins/coins-routes'
import { myWalletRoutes } from './modules/my-wallet/my-wallet-routes'
import { useLoadingStore } from './modules/common/loading/use-loading-store'
import { shareRoutes } from './modules/share/share-routes'
import { mealCheckInRoutes } from './modules/meal-check-in/meal-check-in-routes'
import { sleepCheckInRoutes } from './modules/sleep-check-in/sleep-check-in-routes'
import { NotFoundPage } from './modules/special-pages/not-found-page'

declare module 'vue-router' {
  interface RouteMeta {
    title?: string
  }
}

/**
 * 公开路由，无需登录即可访问
 * 路由一经发布，不得修改，只能新增和重定向
 */
export const publicRoutes: RouteRecordRaw[] = [
  r('/', '', MainLayout, [
    r('', '', HomePage),
    r('account-deletion', '', () => import('src/modules/account-deletion/account-deletion-page')),
    ...customerServiceCenterRoutes,
    ...regulationsRoutes,
    ...coinsRoutes,
    ...myWalletRoutes,
    ...mealCheckInRoutes,
    ...sleepCheckInRoutes,
    r('top-up', '', () => import('src/spa/top-up/top-up-page')),
    ...shareRoutes,
    r('/:pathMatch(.*)*', 'Not Found', NotFoundPage),
  ]),
]

const router = createRouter({
  history: createWebHistory(),
  routes: publicRoutes,
})
const { pageLoading } = useLoadingStore()
router.beforeEach((to, from, next) => {
  if ((to.path === '/my-wallet' && from.path === '/') || to.path === '/coins/exchange') {
    pageLoading.value = true
  }
  next()
})

export default router
