import { createComponent, getQueries, JsonSimpleValue } from '@skynet/shared'
import { Button, Input } from '@skynet/ui'
import { Back } from 'src/modules/common/back/back'
import { computed, ref, watch, watchEffect } from 'vue'
import { dialogBg, redeemBg, redeemDialog, successDialog } from '../images/images'
import { openDialog } from 'src/modules/common/dialog'
import { useCoinsStore } from '../coins-store'
import { apiRedeemGift } from '../coins-api'
export const RedeemIndiaPage = createComponent(null, () => {
  const { account, getLastRedeemAccount } = useCoinsStore()
  void getLastRedeemAccount('india')

  const { amount, id } = getQueries({
    amount: '',
    id: '',
  })

  const formData = ref({
    account: '',
    full_name: '',
    mobile: '',
    ifsc: '',
    email: '',
    address: '',
  })

  watchEffect(() => {
    if (account.value) {
      formData.value = {
        account: account.value?.account || '',
        full_name: account.value?.full_name || '',
        mobile: account.value?.mobile || '',
        ifsc: account.value?.ifsc || '',
        email: account.value?.email || '',
        address: account.value?.address || '',
      }
    }
  })

  const error = ref({
    account: '',
    full_name: '',
    mobile: '',
    ifsc: '',
    email: '',
    address: '',
  })

  const validateFormItem = (key: string, rules: string) => {
    error.value[key as keyof typeof formData.value] = ''
    if (!formData.value[key as keyof typeof formData.value]) {
      error.value[key as keyof typeof formData.value] = 'Please enter'
      return
    }
    if (rules) {
      const regex = new RegExp(rules)
      if (!regex.test(formData.value[key as keyof typeof formData.value])) {
        error.value[key as keyof typeof formData.value] = 'Please re-enter'
      }
    }
  }

  const commonFormItem = (label: string, key: string, placeholder: string, rules: string, required = true, type: 'text' | 'textarea' = 'text', maxlength?: number) => (
    <x-form-item class="flex flex-col gap-y-2">
      <x-label class="mb-1 mt-3 flex items-center justify-start gap-x-1 text-sm font-bold leading-[16.80px] text-[#0b080b]">
        {label}
        {required && <div class="text-[#fc2763]">*</div>}
      </x-label>
      {
        type === 'text' ? (
          <Input
            v-model={formData.value[key as keyof typeof formData.value]}
            placeholder={placeholder}
            type="text"
            maxlength={maxlength}
            inputClass={`text-sm bg-[#f3f0f3] h-12 text-[#0b080b] placeholder-[#a1a0a3] ${key === 'mobile' ? 'pl-6' : ''}`}
            onFocus={() => {
              setTimeout(() => {
                window?.scroll({ top: document.body.scrollHeight, behavior: 'smooth' })
              }, 1000)
            }}
            onBlur={() => validateFormItem(key, rules)}
            v-slots={{
              prefix: () => key === 'mobile' ? (
                <div class="flex h-12 w-4 items-center justify-center bg-[#f3f0f3] text-sm text-[#0b080b]">
                  91
                </div>
              ) : null,
            }}
          />
        ) : (
          <Input
            v-model={formData.value[key as keyof typeof formData.value]}
            placeholder={placeholder}
            type="textarea"
            maxlength={maxlength}
            class="rounded-lg bg-[#f3f0f3] py-4"
            inputClass="h-21 bg-[#f3f0f3] text-[#0b080b] text-sm placeholder-[#a1a0a3] py-0"
            onBlur={() => validateFormItem(key, rules)}
          />
        )
      }
      {error.value[key as keyof typeof formData.value] && <div class="-mt-2 py-1 text-sm text-[#ff2d3e]">{error.value[key as keyof typeof formData.value]}</div>}
    </x-form-item>
  )

  const formItems = [
    { label: 'Account Number', key: 'account', placeholder: 'Required (9-18 numbers)', rules: '^[0-9]{9,18}$', required: true, minlength: 9, maxlength: 18 },
    { label: 'Full Name', key: 'full_name', placeholder: 'Requied (Max 100 characters)', rules: '^[a-zA-Z0-9\\s\\-\\.,]{1,100}$', required: true, maxlength: 100 },
    { label: 'Mobile Number', key: 'mobile', placeholder: 'Required (10 numbers)', rules: '^[0-9]{10}$', required: true, maxlength: 10 },
    { label: 'IFSC Code', key: 'ifsc', placeholder: 'Required (Max 11 characters)', rules: '^[A-Za-z]{4}0[A-Za-z0-9]{6}$', required: true, maxlength: 11 },
    { label: 'Email', key: 'email', placeholder: 'Required', rules: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$', required: true },
    { label: 'Address', key: 'address', placeholder: 'Requied (Max 200 characters)', rules: '^[a-zA-Z0-9\\s\\-\\.,]{1,200}$', type: 'textarea', maxlength: 200 },
  ]

  const validateAll = () => {
    formItems.forEach(item => validateFormItem(item.key, item.rules))
    return Object.values(error.value).every(item => item === '')
  }

  const isDisabled = computed(() => {
    return !formItems.every(item => {
      if (item.required && !formData.value[item.key as keyof typeof formData.value]) {
        return false
      }
      if (item.minlength && formData.value[item.key as keyof typeof formData.value].length < item.minlength) {
        return false
      }
      if (item.maxlength && formData.value[item.key as keyof typeof formData.value].length > item.maxlength) {
        return false
      }
      return true && Object.values(error.value).every(item => item === '')
    })
  })

  const submit = () => {
    if (validateAll()) {
      void apiRedeemGift({
        id,
        type: 'india',
        ...formData.value,
        mobile: `91${formData.value.mobile}`,
      }).then(res => {
        if (res.code === 200) {
          openRedeemSuccessDialog()
        }
      }).catch(() => {
        openRedeemFailDialog()
      })
    }
  }

  const openRedeemFailDialog = () => { // 提现失败
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: () => (
        <x-title class="relative block h-[68px] w-full">
          <img src={redeemDialog} width={100} height={68} class="absolute left-1/2 top-1/2 h-full w-auto -translate-x-1/2 object-cover" />
        </x-title>
      ),
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="text-center text-lg font-bold leading-snug text-[#090609]">Redemption failed</x-title>
            <x-content class="mb-6 mt-2 w-full text-center text-sm font-normal text-[#ff2d3e]">
              There was an error. Please try again later.
            </x-content>
            <x-btn class="w-full rounded-lg border-none bg-[#fc2763] px-3 py-2.5 text-base font-bold text-white" onClick={() => closeDialog()}>
              <p class="line-clamp-3 w-full break-all text-center">Got it</p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openRedeemSuccessDialog = () => { // 提现成功
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: () => (
        <x-title class="relative block h-[94px] w-full">
          <img src={successDialog} width={94} height={94} class="absolute left-1/2 top-1/2 h-full w-auto -translate-x-1/2 object-cover" />
        </x-title>
      ),
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 mt-2 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="text-center text-lg font-bold leading-snug text-[#090609]">We're sending the money to your UPI！</x-title>
            <x-content class="mb-6 mt-2 w-full text-center text-sm font-normal text-[#4d4e4f]">
              Your money will arrive within 5 minutes to 72 hours.
            </x-content>
            <x-btn class="w-full rounded-lg border-none bg-[#fc2763] px-3 py-2.5 text-base font-bold text-white" onClick={() => closeDialog()}>
              <p class="line-clamp-3 w-full break-all text-center">Got it</p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  return () => (
    <div class="pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] relative mx-auto min-h-full w-full bg-[var(--white)]">
      <img src={redeemBg} class="z-up absolute left-0 top-0 h-auto w-full object-cover" />
      <Back title={`Redeem ${amount}`} isWhite={true} class="shrink-0 bg-transparent" />
      <form class="z-up-up relative mt-[10px] flex max-h-[calc(100vh-54px)] w-full flex-col gap-y-2 overflow-y-auto px-3 pb-40">
        {formItems.map(item => commonFormItem(item.label, item.key, item.placeholder, item.rules, item.required, item.type as 'text' | 'textarea', item.maxlength))}
      </form>
      <div class="z-up-up pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] h-25 fixed bottom-0 left-1/2 w-full -translate-x-1/2 bg-[var(--white)]">
        <Button
          disabled={isDisabled.value}
          onClick={submit}
          class="pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] fixed bottom-11 left-1/2 h-11 w-[calc(100%-1.5rem)] -translate-x-1/2 rounded-lg border-none bg-[#fc2763] text-base leading-tight text-[var(--text-5)] disabled:!bg-[#ffa0b6] disabled:!text-[#fdfbfc]/50"
        >
          Submit
        </Button>
      </div>
    </div>
  )
})

export default RedeemIndiaPage
