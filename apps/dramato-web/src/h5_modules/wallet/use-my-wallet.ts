import { ref } from 'vue'
import { apiWalletMy } from '../profile/profile-api'
import { WalletDataInfo } from '../profile/profile'
import { bindLoading } from '@skynet/shared'

const fetchingWallet = ref(false)
const fetchWallet = async () => {
  const response = await bindLoading(apiWalletMy(), fetchingWallet)
  if (!response?.data) return
  wallet.value = response.data
}
const wallet = ref<WalletDataInfo>()
export const useMyWallet = () => {
  return {
    fetchWallet,
    wallet,
  }
}
