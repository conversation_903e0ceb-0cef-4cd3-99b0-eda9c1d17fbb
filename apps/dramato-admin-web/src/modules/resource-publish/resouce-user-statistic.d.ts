declare namespace Api {
  namespace ResourceUserStatistic {
    type Param = {
      page_index?: number
      page_size?: number
      start_time: number
      end_time: number
      username: string
      user_role?: number // 14 资源验收组长 15 多语言信息校验组长 16 多语言字幕校验组长 17 终审发布组长 18 资源验收组员 19 多语言信息校验组员 20 多语言字幕校验组员 21 终审发布组员
    }

    interface Item {
      id: number
      username: string
      user_roles: string
      cal_time: number | string // 日期
      check_count: number // 审核量
      rejected_count: number // 被驳回数
      check_count_avg_7: number // 平均7天通过量
      rejected_count_avg_7: number // 平均7天被驳回量
      check_count_first_round: number // 首轮剧审核数量
      pass_count_first_round: number // 首轮剧通过数量
      rejected_count_first_round: number // 首轮剧被驳回数量
      check_count_second_round: number // 二轮审核数量
      pass_count_second_round: number // 二轮剧通过数量
      rejected_count_second_round: number // 二轮剧被驳回数量

    }

    type List = Item[]
  }
}
