import { createComponent, mc } from '@skynet/shared'
import { TableColumnOld, CreateForm, CreateTableOld, Pager, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { requiredLabel } from 'src/lib/required-label'
import { useHomeConfig } from './use-home-config'
import dayjs from 'dayjs'
import { HomePreview } from './home-preview'
import { ref, watch } from 'vue'
import { apiSortHomeConfig } from './home-config-api'
import { useAppAndLangOptions } from '../options/use-app-options'
type HomeConfigPageOptions = {
  props: {}
}
export const HomeConfigPage = createComponent<HomeConfigPageOptions>({
  props: {},
}, props => {
  const Form = CreateForm<M.HomeConfigSearchProps>()
  const Table = CreateTableOld<M.HomeItemConfig>()
  const currentSortNo = ref<number>(1)

  const typeMap: Record<number, string> = {
    1: '横排专栏',
    2: '竖排专栏',
    3: '榜单',
    4: '3纵列',
  }

  const columns: TableColumnOld<M.HomeItemConfig>[] = [
    ['ID', 'id', { class: 'w-[80px]' }],
    ['数据源key', 'key', { class: 'w-[160px]' }],
    ['序号', row => (
      <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
        <input
          type="number"
          class={mc('grow w-100px')}
          value={row.sort_no}
          onFocus={() => {
            currentSortNo.value = row.sort_no || 1
          }}
          onInput={(e: Event) => {
            currentSortNo.value = Number((e.target as HTMLInputElement).value) || 0
          }}
          onKeydown={(e: KeyboardEvent) => {
            if (e.key !== 'Enter') {
              return
            }
            if (currentSortNo.value === row.sort_no) {
              return
            }
            void apiSortHomeConfig({
              app_id: searchForm.value.app_id!,
              language: searchForm.value.language!,
              curr_row_id: row.id!,
              to_sort_no: currentSortNo.value,
            }).then(() => onSearch())
          }}
          onBlur={() => {
            if (currentSortNo.value === row.sort_no) {
              return
            }
            void apiSortHomeConfig({
              app_id: searchForm.value.app_id!,
              language: searchForm.value.language!,
              curr_row_id: row.id!,
              to_sort_no: currentSortNo.value,
            }).then(() => onSearch())
          }}
        />
      </label>
    ), { class: 'w-[100px]' }],
    ['模块名称', 'name', { class: 'w-[180px]' }],
    ['是否展示模块名称', row => ['展示', '展示', '不展示'][row.show_title || 0], { class: 'w-[180px]' }],
    ['内容类型', row => (
      <div class="flex items-center space-x-1">
        {typeMap[row.type!]}
      </div>
    ), { class: 'w-[180px]' }],
    ['状态',
      row => (
        <div class="space-x-1 flex items-center">
          {
            row.state === 1
              ? (
                  <>
                    <div class="badge bg-green-600 badge-xs" />
                    <div>已发布</div>
                  </>
                )
              : row.state === 10
                ? (
                    <>
                      <div class="badge bg-gray-300 badge-xs" />
                      <div>已下线</div>
                    </>
                  )
                : row.state === 5
                  ? (
                      <>
                        <div class="badge bg-yellow-600 badge-xs" />
                        <div>待发布</div>
                      </>
                    )
                  : '--'
          }
        </div>
      ), { class: 'w-[120px]' },
    ],
    [
      '更新时间',
      row => !!row.updated ? dayjs(+row.updated * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
      { class: 'w-[180px]' },
    ],
    ['更新人', 'u_user_name', { class: 'w-[160px]' }],
    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap">
            <button class="btn btn-sm btn-link btn-primary" onClick={() => edit(row)}>
              编辑
            </button>
            {
              row.state === 1
                ? (
                    <button class="btn btn-sm btn-link btn-primary" onClick={() => confirmOffline(row)}>
                      下线
                    </button>
                  )
                : (
                    <button class="btn btn-sm btn-link btn-primary" onClick={() => confirmPublish(row)}>
                      发布
                    </button>
                  )
            }
          </div>
        )
      },
      { class: 'w-[180px]' },
    ],
  ]
  const { searchForm, onSearch, onReset, loading, list, total, add, edit, confirmOffline, confirmPublish } = useHomeConfig()

  const { appOptions, languageOptions } = useAppAndLangOptions(() => searchForm.value.app_id, {
    onSuccess: onSearch,
  })

  watch(appOptions, () => {
    if (appOptions.value.length > 0 && !searchForm.value.app_id) {
      searchForm.value.app_id = appOptions.value[0].value
    }
  })

  watch(() => searchForm.value.app_id, id => {
    searchForm.value.language = id ? (languageOptions.value[0]?.value) : ''
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>
        {{
          nav: () => (
            <ul>
              <li>首页模块配置</li>
            </ul>
          ),
          form: () => (
            <Form
              onSubmit={() => {onSearch()}}
              onReset={onReset}
              data={searchForm.value}
              onChange={(path, value) => {
                set(searchForm.value, path, value)
              }}
              items={[
                {
                  label: requiredLabel('应用'),
                  path: 'app_id',
                  transform: transformNumber,
                  input: {
                    type: 'select',
                    class: 'w-[240px]',
                    autoInsertEmptyOption: true,
                    options: appOptions.value,
                  },
                },
                { label: requiredLabel('语言'),
                  path: 'language',
                  input: {
                    type: 'select',
                    autoInsertEmptyOption: true,
                    options: languageOptions.value,
                  },
                },
              ]}
            />
          ),
          tableActions: () => (
            <x-table-actions class="flex justify-between items-center">
              <span>应用列表</span>
              <button class="btn btn-primary btn-sm" onClick={add}>新建模块</button>
            </x-table-actions>
          ),
          table: () => (
            <div class="flex">
              <HomePreview />
              <div class="flex overflow-hidden flex-1 justify-center">
                <Table
                  list={list.value || []}
                  class="w-full tm-table-fix-last-column"
                  columns={columns}
                  loading={loading.value}
                />
              </div>
            </div>
          ),
          pager: () => (
            <Pager class="justify-end" v-model:page={searchForm.value.page_info.page_index} v-model:size={searchForm.value.page_info.page_size} total={total.value} onUpdate:page={() => onSearch(false)} onUpdate:size={() => onSearch(false)} />
          ),
        }}
      </NavFormTablePager>
    </div>
  )
})

export default HomeConfigPage
