import { onMounted } from 'vue'
import { createComponent } from '@skynet/shared'
// import { useMealCheckInStore } from './meal-check-in-store'
import morning_bg from './images/morning_bg.webp'
import afternoon_bg from './images/afternoon_bg.webp'
import night_bg from './images/night_bg.webp'
import star_day from './images/star_day.webp'
import star_and_coin from './images/star_and_coin.webp'
import star_night from './images/star_night.webp'
import qa_bg from './images/q.webp'
import { jsBridge } from 'src/lib/jsbridge'
import coin_bg from './images/coin.webp'
import coin_toast_bg from 'src/modules/meal-check-in/images/coin_bg.webp'
import { useLocale } from 'src/lib/use-locale'
import toastIcon from 'src/modules/meal-check-in/images/toast_bg.webp'
import { Back } from 'src/modules/common/back/back'
import Loading from 'src/modules/common/loading/loading'
import { Button } from '@skynet/ui'
import { openDialog } from 'src/modules/common/dialog'
import { useSleepCheckInStore } from './sleep-check-in-store'
import { ApiPostWelfareReceive, apiPostWelfareReclaimReceive, ApiPostWelfareADReceive } from 'src/modules/meal-check-in/meal-check-in-api'
import { showToast } from 'src/modules/common/toast/toast'
import { DialogInfo } from 'src/modules/meal-check-in/meal-check-in.d'
import { useStretchScroll } from 'src/lib/use-stretch-scroll'
import { ApiPostWelfareActivate } from './sleep-check-in-api'
import router from 'src/router'

export const SleepCheckInPage = createComponent(null, () => {
  const { t } = useLocale()
  const { sleepDetails, getSleepDetails } = useSleepCheckInStore()

  onMounted(() => {
    // document.title = t('coins.earnings.title')
    // track('myearnings', 'page', 'show')
    void getSleepDetails()
  })

  const mainBtnClick = async () => {
    if (!sleepDetails.value) return
    const { status } = sleepDetails.value
    let res
    const req = {
      welfare_id: sleepDetails.value.welfare_id,
    }
    switch (status) {
      case 0: // 0: 等待开启
        break
      case 1: // 1:可领取
        res = await ApiPostWelfareReceive(req)
        void getSleepDetails()
        // const res = {
        //   code: 200,
        //   message: 'success',
        //   data: {
        //     ad_extra: {
        //       text: 'Watch Video for 1024 More Coins',
        //       extra: '{"welfare_id":3,"welfare_key":"daily_earnings_bonus","ad_step":1,"ad_gold":200}',
        //     },
        //     dialog: {
        //       reward_num: 100,
        //       title: 'Congratulations! you\'ve earned',
        //       reward_text: '100 Coins',
        //       sub_title: 'Congradulation！',
        //       add_coins_txt: '9999',
        //       major_btn_img: '', // 主按钮图标
        //       major_btn_txt: 'Watch another video to earn X coins', // 主按钮文案
        //       major_link: '',
        //       minor_btn_txt: 'Exit Directly', // 副按钮文案
        //     },
        //     toast: {
        //       icon: 'https://static-v1.mydramawave.com/welfare/icon/sign_gold.png',
        //       text: '+100',
        //     },
        //   },
        // }
        if (res && res.data) {
          openExchangeSuccessDialog(res.data.dialog, res.data.ad_extra.extra)
        }
        break
      case 2: // 2: 待领取
        break
      case 3: // 3: 领取完成
        break
      case 4: // 4:可补领 直接掉起广告
        void jsBridge<any | null>('playAd', {}).then(consent => {
          void apiPostWelfareReclaimReceive(req).then(res => {
            void getSleepDetails()
            if (res && res.data) {
              showToast(
                <div>
                  <img class="w-16 h-12" src={toastIcon} />
                  <p class="text-5 font-bold">{res.data.toast.text}</p>
                </div>,
                'success',
                { class: 'rounded-4 p-5' }, // 可选的其他属性
              )
            }
          })
        })
        break
      case 5: // 5:可开启
        res = await ApiPostWelfareActivate(req)
        void getSleepDetails()
        if (res && res.data && res.data.toast) {
          showToast(res.data.toast.text)
        }
        break
    }
  }
  const openActiveRulesDialog = () => {
    const closeDialog = openDialog({
      title: t('meal_check_in.rules_title'),
      closeVisible: false,
      mainClass: '',
      body: (
        <x-explain-dialog>
          <x-explain class="block mt-1 px-[26px] h-[200px] font-normal text-[var(--text-1)] text-sm break-words overflow-hidden">
            <x-wrapper id="explain-wrapper" class="block pb-7" style="-webkit-overflow-scrolling: touch;">
              <p class="block mb-2">{t('meal_check_in.rules_1')}</p>
              <p class="block mb-2">{t('meal_check_in.rules_2')}</p>
              <p class="block">{t('meal_check_in.rules_3')}</p>
            </x-wrapper>
          </x-explain>
          <x-footer class="relative flex flex-col bg-white">
            <x-mask class="-top-[38px] absolute bg-gradient-to-b from-[rgba(255,_255,_255,_0)] to-[rgba(255,_255,_255,_1)] w-full h-10" />
            <x-got-it>
              <Button class="bg-[var(--brand-7)] mx-4 mt-4 mb-6 no-tap-color border-none rounded-lg w-[calc(100%-2rem)] h-11 text-[var(--text-5)] text-base outline-none"
                onClick={() => closeDialog()}
              >{t('coins.earnings.gotIt')}
              </Button>
            </x-got-it>
          </x-footer>
        </x-explain-dialog>
      ),
    })
    setTimeout(() => {
      useStretchScroll(document.getElementById('explain-wrapper') as HTMLElement)
    }, 200)
  }
  const openExchangeSuccessDialog = (dialogMessage: DialogInfo, extra: string, isLast?: true) => {
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${coin_toast_bg})`, backgroundSize: '100% auto' }} class="pt-51.5 min-h-[266px] w-full  shadow flex flex-col bg-no-repeat mb-4">
          <x-body-main class="flex w-full px-4 flex-col gap-y-4 bg-[var(--fill-5)] rounded-lg">
            <x-title class="text-lg w-full pt-5 font-bold text-[#0b080b] text-center line-clamp-2 break-all ">{dialogMessage.title || dialogMessage.sub_title}</x-title>
            <x-explain class="flex flex-col items-center text-center">
              <x-price class="block mx-[6px] py-3 text-6 font-bold text-[var(--brand-6)] max-w-[calc(100%-12px)] truncate">{dialogMessage.reward_text}</x-price>
            </x-explain>
            <x-btn class="w-(100% - 4) px-3 py-2.5 mx-2 mb-6 no-tap-color text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg" onClick={() => closeDialog()}>
              <p class="w-full line-clamp-2 break-all text-center" onClick={() => {
                void openAdHandle(extra, isLast)
              }}
              >{dialogMessage.major_btn_txt}
              </p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }
  const openAdHandle = (ad_extra: string, isLast?: boolean) => {
    void jsBridge<any | null>('playAd', {}).then(consent => {
      void ApiPostWelfareADReceive({ ad_extra }).then(res => {
        if (res && res.data) {
          if (isLast) {
            showToast(
              <div>
                <img class="w-16 h-12" src={toastIcon} />
                <p class="text-5 font-bold">{res.data.toast.text}</p>
              </div>,
              'success',
              { class: 'rounded-4 p-5' }, // 可选的其他属性
            )
          } else if (res.data.dialog) {
            openExchangeSuccessDialog(res.data.dialog, res.data.ad_extra.extra, true)
          }
        }
      })
    })
  }

  return () => (
    !sleepDetails.value
      ? <Loading />
      : (
          <x-sleep-check-in-page
            style={{
              backgroundImage: `url(${
              sleepDetails.value.time_period === 1
                ? morning_bg
                : sleepDetails.value.time_period === 2
                ? afternoon_bg
                : night_bg
            })`,
              backgroundSize: '100% auto',
            }}
            class="relative overflow-y-auto flex flex-col gap-y-4 bg-[#F2E1D9] text-[var(--text-5)] size-full"
          >
            <Back isWhite={true} isCloseWebview={true} title={sleepDetails.value.top_show.title}
              class={`z-100 px-3 flex items-center justify-center top-0 h-11 shrink-0 ${
                sleepDetails.value.time_period === 1
                  ? 'bg-[#78acf8]'
                  : sleepDetails.value.time_period === 2
                  ? 'bg-[#77D5FC]'
                  : sleepDetails.value.time_period === 3
                  ? 'bg-[#2D4C7B]'
                  : ''
              }`}
            />
            <x-tips class="text-[var(-text-5)] w-full break_all px-8 block text-center">
              <x-greetings class="line-clamp-2 mb-2 w-full text-8 font-bold">{sleepDetails.value.greeting_show.title}</x-greetings>
              <x-time class="line-clamp-3 w-full mb-1 text-base">{sleepDetails.value.greeting_show.sub_title}</x-time>
              <x-friendly_tips class="line-clamp-3 w-full text-sm">{sleepDetails.value.greeting_show.bottom_tips}</x-friendly_tips>
            </x-tips>
            <x-sleeping-link-wrap onClick={() => {
              void router.push({ path: '/meal-check-in' })
            }} class="z-up flex flex-col items-end pr-6 pt-[44%]"
            >
              <img class="size-9" src={coin_bg} />
              <x-sleeping-link class="w-full line-clamp-4 text-2.5 text-[#fe5c00] mr--2 mt--1.5  text-center w-13.5 pt-0.6 px-1 font-bold bg-[#feebdf] rounded-lg">
                {t('sleep_check_in.earn_link')}
              </x-sleeping-link>
            </x-sleeping-link-wrap>
            <x-rules-link-wrap class="z-up flex flex-col items-end pr-6">
              <x-rules-img class="z-up"><img class="size-9" src={qa_bg} /></x-rules-img>
              <x-rules-link onClick={openActiveRulesDialog} class="z-up w-13.5 py-1 line-clamp-4 mr--2 rounded-lg bg-[#feebdf] text-2.5 mt--3 font-['SF Pro'] text-[#fe5c00] text-center w-12 font-bold px-1">
                {t('meal_check_in.rules_title')}
              </x-rules-link>
            </x-rules-link-wrap>
            <x-main-txt
              class="mt-[-34%] ml-[9%]"
            >
              <x-txt class="relative w-46 block ml-5">
                <p class="text-[#fe2d00] p-4 text-center bg-[var(--fill-5)] rounded-12 w-full text-xs line-clamp-5">
                  {sleepDetails.value.interactive_broadcast_widget.widget_text}
                </p>
                <x-dot class="w-0 h-0 border-l-[8px] rotate-90 border-r-[8px] border-b-[16px] border-solid border-transparent border-b-[var(--fill-5)] absolute left-28 bottom--2" />
              </x-txt>
              <img
                class="block h-auto w-67.5"
                src={
                  sleepDetails.value.status === 2 || sleepDetails.value.status === 4
                    ? star_and_coin
                    : sleepDetails.value.time_period === 3
                      ? star_night
                      : star_day
                }
              />
            </x-main-txt>
            <x-main-footer class="fixed bottom-[10%] w-full">
              <x-main-tips onClick={mainBtnClick} class={`px-3 py-2.5 mx-8 w-[100%-16] shrink-0 text-[var(--text-5)] font-bold text-base rounded-50 flex justify-center items-center gap-2.5 ${
    [0, 2, 3].includes(sleepDetails.value.status)
      ? 'bg-[#ffab7b] pointer-events-none'
      : 'bg-[#f6510b]'
  }`}
              >
                <p class="w-full text-center line-clamp-3">{sleepDetails.value.button_show.btn_txt}</p>
              </x-main-tips>
              <x-care-notice class="flex mt-2 px-8 items-center break-all flex-col">
                <p class="text-[var(--text-5)] w-full text-center line-clamp-3 text-base">{sleepDetails.value.bottom_show.title}</p>
              </x-care-notice>
            </x-main-footer>
          </x-sleep-check-in-page>
        ))
})

export default SleepCheckInPage
