/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc, on } from '@skynet/shared'
import { Button, Checkbox, CreateForm, CreateTableOld, DateTime, Icon, openDialog, Pager, showAlert, transformInteger } from '@skynet/ui'
import { set, omit } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { apiDeleteStrategyGroup, apiListStrategyGroup, apiExportStrategyGroupExcel, apiUpdateStrategyGroupPriority, apiUpdateStrategyGroupState, strategyGroupApi } from 'src/modules/strategy-group/strategy-group-api'
import { onMounted, ref, watch } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { useStrategyGroupStore } from './strategy-group-store'
import Cookies from 'js-cookie'
import qs from 'qs'

import { showFailToast } from '@skynet/ui'

import axios from 'axios'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
type StrategyGroupPageOptions = {
  props: {
    checkedItem: string[] // 选中的项
    showCheckbox: boolean // 是否显示复选框
    showColumns: string[] // 显示的列
  }
  emits: {
    add: (data: M.StrategyGroup) => void
    remove: (data: M.StrategyGroup) => void
  }
}

const initListParams = {
  strategy_id: '',
  name: '',
  status: undefined,
  is_abtest: undefined,
  sort: {
    // created: 'DESC',
    // id: 'DESC',
    // priority: 'DESC',
  },
}

const statusColor = {
  1: '--error-6',
  2: '--green-6',
  3: '--line-1',
  4: '--text-6',
}
export const StrategyGroupPage = createComponent<StrategyGroupPageOptions>({
  props: {
    checkedItem: [],
    showCheckbox: false,
    showColumns: [],
  },
  emits: {
    add: fn,
    remove: fn,
  },
}, (props, { emit }) => {
  type ListParams = Omit<Api.StrategyGroup.Request.List, 'page_info'>
  const Form = CreateForm<ListParams>()
  const listParams = ref<ListParams>({ ...initListParams })
  const Table = CreateTableOld<M.StrategyGroup>()
  const list = ref<M.StrategyGroup[]>([])
  const router = useRouter()
  const currentPriority = ref<number>(0)

  const { resetFormData } = useStrategyGroupStore()

  onMounted(() => {
    void search(1)
    on('refresh:strategyGroup', () => {
      void search(1)
    })
  })
  const loading = ref<boolean>(false)
  const search = async (_page?: number) => {
    _page = _page || page.value + 1
    loading.value = true
    const sort = {}
    if (listParams.value.sortType) {
      const k = listParams.value.sortType.split('-')[0]
      const v = listParams.value.sortType.split('-')[1]
      set(sort, k, v)
    }
    const res = await apiListStrategyGroup({ ...listParams.value, sort, page_info: { offset: (_page - 1) * pageSize.value, size: pageSize.value } })
      .finally(() => {
        loading.value = false
      })
    list.value = res.data?.list || []
    total.value = res.data?.page_info.total || 0
    page.value = _page
  }

  const get_k_sso_token = () => {
    return Cookies.get('k-sso-token') || Cookies.get('k_sso_token') || localStorage.getItem('k-sso-token') || localStorage.getItem('k_sso_token')
  }
  const exportExcel = async () => {
    const sort = {}
    const result = JSON.parse(JSON.stringify(listParams.value))

    if (!result.status) {
      result.status = ''
    }

    if (!result.is_abtest) {
      result.is_abtest = ''
    }

    if (result.sortType) {
      const k = result.sortType.split('-')[0]
      const v = result.sortType.split('-')[1]
      set(sort, k, v)
    }
    // delete result['sort']

    try {
      const queryString = qs.stringify({ ...result, sort }).toString()
      // const queryString = encodeURIComponent({ ...listParams.value, sort})

      // 发送请求到服务器
      const response = await fetch(`${import.meta.env.VITE_DRAMA_API_URL}/strategy-group/export?${queryString}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Device: 'Web',
          Token: get_k_sso_token() || '',
        },
      })
      // 检查响应是否成功
      if (!response.ok) {
        showFailToast('下载失败')
        return
      }
      // 获取文件流
      const blob = await response.blob()
      // 检查 Blob 是否有效
      if (blob.size === 0) {
        showFailToast('下载的文件为空')
        return
      }
      // 创建一个下载链接并触发下载
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `IAP策略组.xlsx` // 设置下载的文件名
      document.body.appendChild(link)
      link.click() // 自动点击链接以开始下载
      document.body.removeChild(link) // 清理链接
      URL.revokeObjectURL(link.href) // 释放内存
    } catch (error) {
      showFailToast('下载失败')
    }
  }

  const makeExcel = (data: string[][], name: string) => {
    return new Promise(resolve => {
      // 将数据转换为 CSV 格式，确保每个值都被双引号包裹
      const csvContent = data.map((row: string[]) =>
        row.map(value => `"${String(value).replace(/"/g, '""')}"`).join(','),
      ).join('\n')

      // 添加 UTF-8 BOM
      const bom = '\uFEFF'
      const finalContent = bom + csvContent

      // 创建一个 Blob 对象
      const blob = new Blob([finalContent], { type: 'text/csv;charset=utf-8;' })

      // 创建下载链接
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', name)
      document.body.appendChild(link)
      link.click()

      // 释放 URL 资源并解决 Promise
      URL.revokeObjectURL(url)
      resolve('')
    })
  }

  const copyBtn = async (row: M.StrategyGroup) => {
    try {
      const rs = await strategyGroupApi.getStrategyGroupDetail(row.id + '')
      if (!rs.data) {
        return
      }
      let d = rs.data
      d = {
        ...omit(d, ['id', 'status']),
        save_type: 1,
        name: d.name + '副本',
      }
      await strategyGroupApi.updateStrategyGroupSave(d)
      showAlert('复制成功')
      void search(page.value)
    } catch (error: any) {
      showAlert(error.response.data.message || error.response.data.err_msg || '复制失败')
    }
  }
  watch(() => listParams.value.sort, () => {
    void search(1)
  }, { deep: true })
  const page = ref<number>(0)
  const pageSize = ref<number>(20)
  const total = ref<number>(1)

  const {
    list: strategyLayerList,
    ...other
  } = useUserStrategyLayer()

  onMounted(() => {
    other.page.value = 1
    other.pageSize.value = 9999
    void other.search(other.page.value)
  })
  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>策略组列表</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value: any) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              console.log('onReset')

              listParams.value = { ...initListParams }
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)} data={listParams.value} items={[
              ['策略ID', 'strategy_id', { type: 'text' }],
              ['策略名称', 'name', { type: 'text' }],
              [
                '分层画像',
                'strategy_layer_id',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: strategyLayerList.value.map((n, index) => {
                    return { value: n.id, label: `${n.id}/${n.name}` }
                  }),
                  maxlength: 1,
                },
                {
                  transform: [
                    (raw?: unknown) => raw ? [+raw] : [],
                    (display: string[]): number => display[0] ? +(display[0] || '') : 0,
                  ] as const,
                },
              ],
              ['状态', 'status', {
                type: 'select',
                options: [
                  { label: '已启用', value: 1 },
                  // { label: '草稿', value: 2 },
                  { label: '未启用', value: 3 },
                  // { label: '冻结', value: 4 },
                ],
              }, { transform: transformInteger }],
              ['是否AB实验', 'is_abtest', {
                type: 'select',
                options: [
                  { label: '否', value: 1 },
                  { label: '是', value: 2 },
                ],
              }, { transform: transformInteger }],
              ['排序', 'sortType', {
                type: 'select',
                options: [
                  { label: '创建时间：从新到旧', value: 'created-DESC' },
                  { label: '创建时间：从旧到新', value: 'created-ASC' },
                  { label: 'ID：从大到小', value: 'id-DESC' },
                  { label: 'ID：从小到大', value: 'id-ASC' },
                  { label: '优先级：从大到小', value: 'priority-DESC' },
                  { label: '优先级：从小到大', value: 'priority-ASC' },
                  // { label: '冻结', value: 4 },
                ],
              }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex justify-between items-center">
            <div class="flex items-center gap-2">
              IAP策略
            </div>
            {!!props.showCheckbox || (
              <x-hide-when-in-dialog class="flex items-center gap-2">
                <Button class="btn-primary btn btn-sm" onClick={
                  () => {
                  // resetFormData()
                    page.value = 0
                    void router.push('/strategy-group/create')
                  }
                }
                >新建策略组
                </Button>
                {/* <Button class="btn btn-primary btn-sm " onClick={() => exportExcel()}>导出Excel</Button> */}
              </x-hide-when-in-dialog>
            )}
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            [
              '',
              row => {
                const id = row.id
                return (
                  <x-show-when-in-dialog>
                    <Checkbox
                      label=""
                      disabled={!!!id}
                      modelValue={props.checkedItem.includes(id!)}
                      onUpdate:modelValue={(value: unknown) => {
                        if (value) {
                          if (!props.checkedItem.includes(id!)) {
                            const found = list.value.find(i => i.id === id)
                            if (!found) return
                            emit('add', found)
                          }
                        } else {
                          const rowIndex = props.checkedItem.indexOf(id!)
                          if (rowIndex !== -1) {
                            emit('remove', row)
                          }
                        }
                      }}
                    />
                  </x-show-when-in-dialog>
                )
              },
              { class: mc('w-[30px]', props.showCheckbox ? '' : 'hidden') },
            ],
            ['策略组ID', 'id', { class: mc('w-[100px]', !props.showCheckbox || props.showColumns.includes('id') ? '' : 'hidden') }],
            ['策略组名称', 'name', { class: mc('w-[200px]', !props.showCheckbox || props.showColumns.includes('name') ? '' : 'hidden') }],
            ['用户分层名称', 'strategy_layer_names', { class: mc('w-[200px]', !props.showCheckbox || props.showColumns.includes('strategy_layer_names') ? '' : 'hidden') }],
            ['优先级', row => (
              <x-input class="flex flex-row items-center gap-x-2">
                <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                  <input
                    type="number"
                    class={mc('grow w-100px')}
                    value={row.priority}
                    disabled={!row.isEdit}
                    // ant-design:edit-filled
                    onFocus={() => {
                      currentPriority.value = +(row.priority || '')
                    }}
                    onInput={(e: Event) => {
                      currentPriority.value = +((e.target as HTMLInputElement).value || '')
                    }}
                    onKeydown={(e: KeyboardEvent) => {
                      if (e.key !== 'Enter') {
                        return
                      }
                      if (currentPriority.value === row.priority) {
                        return
                      }
                      void apiUpdateStrategyGroupPriority({
                        id: row.id + '' || '',
                        priority: currentPriority.value,
                      }).then(() => {
                        void search(page.value)
                      })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                    onBlur={() => {
                      if (currentPriority.value === row.priority) {
                        return
                      }
                      void apiUpdateStrategyGroupPriority({
                        id: row.id + '' || '',
                        priority: currentPriority.value,
                      }).then(() => {
                        void search(page.value)
                      })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                  />
                </label>
                <Icon name="ant-design:edit-filled" class="size-4 cursor-pointer" onClick={() => {
                  row.isEdit = true
                }}
                />
              </x-input>
            ), { class: mc('w-[200px]', !props.showCheckbox || props.showColumns.includes('priority') ? '' : 'hidden') }],
            ['是否AB实验', row => (
              <>
                {row.is_abtest === 1 ? '是' : '否'}
              </>
            ), { class: mc('w-[100px]', !props.showCheckbox || props.showColumns.includes('is_abtest') ? '' : 'hidden') }],
            ['上架状态', row => (
              <span class={`badge badge-outline whitespace-nowrap text-[var(${statusColor[row.status]}]`}>{[
                { label: '启用', value: 1 },
                { label: '未启用', value: 2 },
                { label: '未启用', value: 3 },
                { label: '未启用', value: 4 },
              ].find(item => item.value === row.status)?.label ?? row.status}
              </span>
            ), { class: mc('w-[100px]', !props.showCheckbox || props.showColumns.includes('status') ? '' : 'hidden') }],
            ['创建时间', row => (<DateTime value={(row?.created || 0) * 1000} />), { class: mc('w-[150px]', !props.showCheckbox || props.showColumns.includes('created') ? '' : 'hidden') }],
            ['创建人', 'created_operator_name', { class: mc('w-[150px]', !props.showCheckbox || props.showColumns.includes('created_operator_name') ? '' : 'hidden') }],
            ['更新时间', row => (<DateTime value={(row?.updated || 0) * 1000} />), { class: mc('w-[150px]', !props.showCheckbox || props.showColumns.includes('updated') ? '' : 'hidden') }],
            ['更新人', 'updated_operator_name', { class: mc('w-[150px]', !props.showCheckbox || props.showColumns.includes('updated_operator_name') ? '' : 'hidden') }],
            [<span class="px-3">操作</span>, row => (
              <x-hide-when-in-dialog class="flex gap-x-2">
                <Button
                  class={mc('btn btn-outline btn-xs', [2, 4].includes(row.status || 0) ? 'hidden' : '')}
                  onClick={() => {
                    if (!row.id) return
                    void apiUpdateStrategyGroupState({
                      id: row.id,
                      operation: row.status === 3 ? 1 : 2,
                    })
                      .then(() => {
                        void search(page.value)
                      })
                      .catch((error: any) => {
                        showAlert(error.response.data.message, 'error')
                      })
                  }}
                >
                  {row.status === 3 ? '启用' : '下架'}
                </Button>
                <RouterLink to={`/strategy-group/create?id=${row.id}`} class={mc('btn btn-outline btn-xs', [2, 3].includes(row.status || 0) ? '' : 'hidden')}>修改</RouterLink>
                <RouterLink to={`/strategy-group/create?id=${row.id}&mode=view`} class={mc('btn btn-outline btn-xs', row.status === 1 ? '' : 'hidden')}>查看</RouterLink>
                <Button
                  class="btn btn-outline btn-xs"
                  onClick={() => {
                    const hideDeleteDialog = openDialog({
                      title: '删除',
                      mainClass: 'pb-0 px-5',
                      body: (
                        <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <x-delete-episode-body>确认删除 策略【{row.name}】吗？</x-delete-episode-body>
                          <x-delete-episode-footer class="w-full flex justify-end gap-x-[10px]">
                            <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
                            <button class="btn btn-primary btn-sm" onClick={() => {
                              void apiDeleteStrategyGroup({
                                ids: [row.id || ''],
                              })
                                .then(() => {
                                  void search(page.value)
                                  hideDeleteDialog()
                                })
                                .catch((error: any) => {
                                  showAlert(error.response.data.message, 'error')
                                })
                            }}
                            >确定
                            </button>
                          </x-delete-episode-footer>
                        </x-delete-episode-confirm-dialog>
                      ),
                    })
                  }}
                >
                  删除
                </Button>
                <Button class="btn-outline btn btn-xs" onClick={() => copyBtn(row)}>
                  复制
                </Button>
              </x-hide-when-in-dialog>
            ), {
              class: mc('w-[225px] hide-when-in-dialog', props.showCheckbox ? 'hidden' : ''),
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default StrategyGroupPage
