/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiEditSpendBoard, apiGetSpendBoardList } from './spend_board-api'

export const useSpendBoard = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    total,
    search,
    onEdit,
    isUpdating,
  }
}

const Form = CreateForm<M.SpendBoard.params>()
const params = ref<M.SpendBoard.params>({
  page_index: 1,
  page_size: 20,
})

const Table = CreateTableOld<M.SpendBoard.ListItem>()
const list = ref<M.SpendBoard.ListItem[]>([])
const loading = ref<boolean>(false)
const total = ref<number>(1)
const isUpdating = ref(false)

const search = async (_page?: number) => {
  _page = _page || (params.value?.page_index || 0) + 1
  loading.value = true
  const res = await apiGetSpendBoardList({
    ...params.value,
    deploy_task_time_start: params.value.deploy_task_time_start || 0,
    deploy_task_time_end: params.value.deploy_task_time_end || 0,
  })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.total_count || 0
  params.value.page_index = _page
}

const onEdit = async (s: {
  column_type: 1 | 2 | 3 | number // . 1 comment  2:business_rating 3: content_rating
  content: string
  series_resource_id: string
}) => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditSpendBoard(s)
    showAlert('编辑成功')
    void search(params.value?.page_index)
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '编辑失败', 'error')
  }
}
