import { onMounted, ref } from 'vue'
import { createComponent } from '@skynet/shared'
import { Checkbox, CheckboxGroup } from '@skynet/ui'

type INormalValueLabel = { value: string, label: string }

type ColFilterOptions = {
  props: {
    columns: INormalValueLabel[]
    selectCols: string[]
  }
  emits: {
    change: (value: string[]) => void
  }
}
export const ColFilter = createComponent<ColFilterOptions>({
  props: {
    columns: [],
    selectCols: []
  },
  emits: {
    change: () => {}
  }
}, (props, { emit }) => {
  const checkedAll = ref<boolean>(true)
  const checked = ref<string[]>([])

  onMounted(() => {
    checked.value = props.selectCols
    checkedAll.value = checked.value.length === props.columns.length
  })

  return () => (
    <div>
      <Checkbox label="全选" modelValue={checkedAll.value} onUpdate:modelValue={(e: boolean) => {
        checkedAll.value = e
        if (e) {
          checked.value = props.columns.map(row => row.value)
        } else {
          checked.value = []
        }
        emit('change', checked.value)
      }}
      />
      <CheckboxGroup class="-mt-1.5 flex flex-col" modelValue={checked.value} onUpdate:modelValue={e => {
        checked.value = e as string[]
        if (e.length === props.columns.length) {
          checkedAll.value = true
        } else {
          checkedAll.value = false
        }
        emit('change', checked.value)
      }} options={props.columns}
      />
    </div>
  )
})
