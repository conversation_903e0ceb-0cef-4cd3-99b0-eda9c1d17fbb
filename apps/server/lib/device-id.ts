import Cookies from 'js-cookie'
 
import { md5 } from "js-md5";

export const get_k_device_hash_from_cookie = () => {
  //如果是服务器则从cookie中获取，如果是客户端则从localStorage中获取
  if  (import.meta.server) {
    const k_device_hash = useCookie('k_device_hash')
    return k_device_hash.value
  }
  if (import.meta.client) {
    // 仅在客户端执行的代码，比如访问 window、document
      const domain1 = location.hostname // 先获取当前访问的全域名
      const rootDomain = domain1.substring(domain1.indexOf('.') + 1)
      let data = Cookies.get('k_device_hash')
      if (data) {
        Cookies.set('k_device_hash', data ?? '', { expires: 365, path: '/', domain: rootDomain })
        return data
      }
      data = md5(navigator.userAgent + setRandomNumberWay())
      Cookies.set('k_device_hash', data, { expires: 365, path: '/', domain: rootDomain })
      return data
  }
}

export function generate_k_device_hash(user_agent: string) {
  return md5(user_agent + setRandomNumberWay())
}

// 随机生成字符串函数
function setRandomNumberWay() {
  let r
  const data = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z']
  let result = ''
  for (let i = 0; i < 16; i++) {
    r = Math.floor(Math.random() * 36)
    result += data[r]
  }
  return result
}

export const get_k_sso_token = () => {
  return Cookies.get('k-sso-token') || Cookies.get('k_sso_token') || localStorage.getItem('k-sso-token') || localStorage.getItem('k_sso_token')
}
