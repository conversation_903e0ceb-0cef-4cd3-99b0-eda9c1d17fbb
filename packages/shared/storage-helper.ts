export const tryGetLocalString = (key: string, fallback: string): string => {
  try {
    const value = localStorage.getItem(key)
    if (!value) return fallback
    return value
  } catch (_) {
    return fallback
  }
}

export const tryGetLocalJson = <T>(key: string, fallback: T): T => {
  try {
    const value = localStorage.getItem(key)
    if (!value) return fallback
    const result = JSON.parse(value)
    if (result === null || result === undefined) return fallback
    return result
  } catch (_) {
    return fallback
  }
}
