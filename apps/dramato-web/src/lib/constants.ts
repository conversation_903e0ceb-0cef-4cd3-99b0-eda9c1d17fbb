export const appName = typeof window === 'undefined' ? '' : window.location.hostname.indexOf('thedramabuzz') >= 0 ? 'dramabuzz' : 'dramawave'
export const AppName = typeof window === 'undefined' ? '' : window.location.hostname.indexOf('thedramabuzz') >= 0 ? 'Dramabuzz' : 'DramaWave'

export const breakpoints = {
  phone: { min: 0, max: 720 },
  pad: { min: 720, max: 1046 },
  pc: { min: 1046, max: Infinity },
}
export const fileTypeMap: Record<number, [string, string]> = {
  0: ['文件', 'pdf'],
  1: ['文件', 'doc'],
  2: ['文件', 'txt'],
  3: ['网页', 'url'],
}
