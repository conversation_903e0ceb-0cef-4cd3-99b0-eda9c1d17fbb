declare namespace M {
    type ActivityButton = {
      button_id: number
      index: number
      /**
       * 跳转类型
       * - 1 是商品
       * - 2 是链接
       */
      target_type: 1 | 2 | number
      target_content: string
      pay_product_sku?: {
        delivery_details: {
          daily_bonus: number
          period: string
          quanity: number
        }
        product_id: number
        pay_channel: string
        sku_id: string
        period: string
        tips: string
        description: string
        discount_price: number
        product_type: string
        currency: string
        has_discount: number
        r_info: string
      } | null
    }
}
declare namespace Api {
  namespace Activity {
    namespace FetchConfig {
      type Response = ApiResponse<{
        event_id: number
        button_list: Array<M.ActivityButton>
      }>
    }
  }
}
