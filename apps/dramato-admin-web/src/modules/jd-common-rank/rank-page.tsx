/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, DateTime } from '@skynet/ui'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useJDCommonRank } from './use-rank'
import { requiredLabel } from 'src/lib/required-label'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useEpisodeTags } from '../episode-tags/use-episode-tags'
import { onMounted, ref, watch } from 'vue'
import { lang } from 'src/lib/constant'
import { rankApi } from './rank-api'

type JDRankOptions = {
  props: {}
}
export const JDRank = createComponent<JDRankOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    search,
    onCreateBtnClick,
    onEditBtnClick,
  } = useJDCommonRank()

  const { getTags } = useEpisodeTags()

  const { appOptions } = useAppAndLangOptions(() => '', {
    onSuccess: () => {
      params.value.language_version = appOptions.value[0].language[0]
      void search()
    },
    isFree: true,
  })

  watch(() => params.value.language_version, () => {
    if (!params.value.language_version) return
    void getTags(params.value.language_version || '')
  }, { deep: true, immediate: true })

  const tabList = ref<string[]>([])

  watch(() => params.value.language_version, async () => {
    const result = await rankApi.getTabList({ language_version: params.value.language_version || '', pid: '' })
    if (!result.data) return
    tabList.value = result.data.list
    // 如果选中的 tab 不存在于列表中，则选中第一个
    if (params.value.tab && !tabList.value.includes(params.value.tab)) {
      params.value.tab = tabList.value[0]
    }
  }, { immediate: true, deep: true })
  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li class="flex items-center gap-x-2">
              普通tab运营资源位配置
            </li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            resetText=""
            onSubmit={() => {
              void search()
            }}
            data={params.value}
            items={[
              { label: requiredLabel('语言'),
                path: 'language_version',
                input: {
                  type: 'select',
                  autoInsertEmptyOption: false,
                  options: appOptions.value && appOptions.value[0]
                    ? appOptions.value[0].language.map((item: string) => ({
                      label: lang.find(i => i.value === item)?.label || '- 空 -',
                      value: item,
                    }))
                    : [],
                },
              },
              [
                'Tab',
                'tab',
                {
                  type: 'select',
                  options: tabList.value.map(item => ({
                    label: item,
                    value: item,
                  })),
                },
              ],
            ]}
          />
        ),
        tableActions: () => (
          <div class="w-full flex flex-row justify-between items-start">
            普通tab运营资源位配置
            <x-button-wrap class="flex justify-between items-center">
              <Button class="btn-primary btn btn-sm" onClick={onCreateBtnClick}>新增</Button>
            </x-button-wrap>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['坑位', 'priority', { class: 'w-[100px]' }],
            ['模块类型', row => ['-空-', '榜单', '剧单'][row?.subtype || 0], { class: 'w-[120px]' }],
            ['ID', 'id', { class: 'w-[100px]' }],
            ['上架状态', row => (
              Date.now() < (row?.start || 0) * 1000 || Date.now() > (row?.end || 0) * 1000 ? '未上架' : '已上架'
            ), { class: 'w-[80px]' }],
            ['生效时间', row => (
              <x-time class="flex items-center gap-x-2">
                <DateTime value={row?.start ? row?.start * 1000 : 0} />-
                <DateTime value={row?.end ? row?.end * 1000 : 0} />
              </x-time>
            ), { class: 'w-[300px]' }],
            ['创建时间', row => <DateTime value={row?.created ? row?.created * 1000 : 0} />, { class: 'w-[150px]' }],
            ['创建人', row => row?.create_user_name, { class: 'w-[100px]' }],
            ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : 0} />, { class: 'w-[150px]' }],
            ['修改人', row => row?.update_user_name, { class: 'w-[100px]' }],
            [<span class="px-3">操作</span>, row => (
              <div class="flex gap-x-2">
                <Button class="btn-outline btn btn-xs" onClick={() => onEditBtnClick(row)}>编辑</Button>
              </div>
            ), {
              class: 'w-[60px]',
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
      }}
    </NavFormTablePager>
  )
})

export default JDRank
