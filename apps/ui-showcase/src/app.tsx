import { createComponent } from '@skynet/shared'
import { Popover, usePopoverStore } from '@skynet/ui'
import { useAlertStore } from '@skynet/ui/alert/use-alert-store'
import { usePopover2Store } from '@skynet/ui/popover/use-popover-2-store'
import { RouterView } from 'vue-router'
type AppOptions = {
  props: {}
}
export const App = createComponent<AppOptions>({
  props: {},
}, () => {
  const { renderPopovers } = usePopoverStore()
  const { renderPopovers: renderPopovers2 } = usePopover2Store()
  const { renderAlerts } = useAlertStore()
  return () => (
    <x-app class="block">
      <RouterView />
      {renderPopovers()}
      { renderPopovers2() }
      <div class="fixed space-y-4 top-[100px] z-alert left-1/2 transform -translate-x-1/2 items-center justify-center flex-col flex">
        {renderAlerts()}
      </div>
    </x-app>
  )
})
