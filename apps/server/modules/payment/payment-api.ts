import { encryptedHttpClient } from '~/lib/encrypted-http-client'

export const getProductList = (params: { uid: string }) => {
  return encryptedHttpClient.get<ApiResponse<M.Payment.PaymentResponse>>('/h5-api/wallet/pay/product/list', params)
}

export const getPaypalOrder = (params: M.Payment.PaypalPaymentParams) => {
  return encryptedHttpClient.post<ApiResponse<M.Payment.PaypalPaymentResponse>>('/h5-api/wallet/paypal/purchase', params)
}

export const apiGetAliwallexCardOrder = (data: M.Payment.AliwallexPaymentParams) => {
  return encryptedHttpClient.post<ApiResponse<M.Payment.AliwallexPaymentResponse>>('/h5-api/wallet/aliwallex-card/purchase', data)
}

export const paypalPaymentConfirm = (data: M.Payment.PaypalPaymentConfirmParams) => {
  return encryptedHttpClient.post<ApiResponse<null>>('/h5-api/wallet/paypal/purchase-result', data)
}

export const getAliwallexOrder = (data: M.Payment.AliwallexPaymentParams) => {
  return encryptedHttpClient.post<ApiResponse<M.Payment.AliwallexPaymentResponse>>('/h5-api/wallet/aliwallex/purchase', data)
}

export const apiGetAliwallexOrderStatus = (params: { order_id: string }) => {
  return encryptedHttpClient.post<ApiResponse<M.Payment.AliwallexPaymentStatusResponse>>('/h5-api/wallet/aliwallex/purchase-result', params)
}
