/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, getQueries, getQueriesWithParser, mc, queryParsers, useValidator } from '@skynet/shared'
import { But<PERSON>, showAlert, transformInteger } from '@skynet/ui'
import { set } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { profileDefaultValues } from '../strategy-group/profile-custom-renders'
import { usePopupConditionStore } from './use-popup-condition-store'
import { keepError } from 'src/lib/http-client'
import { popupApi } from './user-strategy-layer-api'
import { RouterLink, useRouter } from 'vue-router'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import ProfileItemsInput from './profile-item-input'
import { FormItem } from '@skynet/ui/form/form-types'
import { useUserStrategyLayer } from './use-user-strategy-layer'
import { useSeriesPackageStore } from '../series-package-new/use-series-package-store'

type PopupConditionOptions = {
  props: {}
}

export const PopupCondition = createComponent<PopupConditionOptions>({
  props: {},
}, (props, { emit }) => {
  const router = useRouter()
  const { persona_type } = getQueries({ persona_type: 0 })

  const { userProfileList, series_package_list, fetchUserProfileList, formData, Form } = usePopupConditionStore()
  const { params } = useUserStrategyLayer()

  const loading = ref(false)
  const { id } = getQueriesWithParser({ id: queryParsers.string() })

  const formRules = z.object({
    // desc: z.string().min(1, '请输入'),
    priority: z.number({ message: '请选择序号' }).max(99999, '最大99999').min(1, '最小1'),
    name: z.string().min(1, '请输入'),
    series_package_ids: z.array(z.number()).min(1, '请选择'),
  })

  const { error, validateAll } = useValidator(formData, formRules)

  //   AdValueLevel
  // :
  // (4) ['valid', 'VAL==2', 'VAL==3', 'VAL==4']
  // CampaignType
  // :
  // (3) ['valid', 'Contains(VAL,"free")', 'Contains(VAL,"paid")']
  // Device
  // :
  // (3) ['valid', 'VAL=="ios"', 'VAL=="android"']
  // Region
  // :
  // (3) ['valid', 'VAL=="T4"', 'VAL=="T3"']
  // UserType
  // :
  // (4) ['valid', 'VAL==1', 'VAL==2', 'VAL==3']

  const switchParamToFormData = () => {
    if (!params.value.query_condition) {
      return
    }

    if (Object.keys(params.value.query_condition).length <= 0) {
      return
    }

    Object.keys(params.value.query_condition).forEach(key => {
      const value: string[] = params.value.query_condition[key] || []
      if (value.length > 0) {
        const switchedValue = ['valid']
        const profile = userProfileList.value[0].type_list.find(v => v.item_code === key)
        value.forEach(v => {
          switchedValue.push(`${profile?.value_prefix || ''}${v}${profile?.value_suffix || ''}`)
        })
        formData.value.user_persona[key] = switchedValue
      }
    })
  }

  onMounted(async () => {
    loading.value = true
    console.log('params', params.value)

    console.log('>>> form', formData.value)

    const [_, response] = await Promise.all([fetchUserProfileList(), id ? popupApi.getPersona(id) : null])
    console.log(userProfileList.value)
    // 填充默认值
    if (!id) {
      switchParamToFormData()
      formData.value.persona_type = persona_type
    }
    if (!response) return loading.value = false
    if (!response.data) return loading.value = false
    // 用户填写的值
    formData.value = response.data
    loading.value = false
  })

  const seriesPackageStore = useSeriesPackageStore()

  onMounted(() => {
    seriesPackageStore.page.value = 1
    seriesPackageStore.pageSize.value = 9999
    seriesPackageStore.listParams.value.import_types = [1, 2, 3, 4, 5]
    void seriesPackageStore.search(seriesPackageStore.page.value, [1, 2, 3, 4, 5])
  })

  const onChange = (path: string, value: unknown) => {
    set(formData.value, path, value)
  }

  const onSubmit = async () => {
    const exclude = []
    if (formData.value.persona_type === 0) exclude.push('series_package_ids')
    // console.log('>>> form', formData.value)
    // return
    if (!validateAll({ exclude })) return
    // userProfileList.value.forEach(i => {
    //   i.type_list.forEach(item => {
    //     const userPersona = formData.value.user_persona[item.item_code]
    //     const options = item.item_child?.map(i => item.value_prefix + i.value + item.value_suffix) ?? []
    //     if (userPersona && userPersona.filter(i => !['valid', 'invalid'].includes(i)).length > (item.can_custom ? 1 : 0)) {
    //       formData.value.user_persona[item.item_code] = userPersona.filter((v: string) => options.concat('invalid', 'valid').includes(v))
    //     }
    //   })
    // })
    // console.log('origin formData.value', formData.value.user_persona)
    if (formData.value.persona_type === 0 && formData.value.series_package_user_persona) {
      delete formData.value.series_package_user_persona
    }
    Object.keys(formData.value.user_persona).forEach(key => {
      const v = formData.value.user_persona[key]
      if (v.length === 0 || (v.length === 1 && v[0] === 'invalid')) {
        delete formData.value.user_persona[key]
      }
    })
    console.log('formData.value', formData.value.user_persona)
    // return
    await popupApi.updatePersona(formData.value).catch(keepError(() => showAlert('提交失败')))
    showAlert('提交成功')
    void router.push('/user-strategy-layer')
  }
  return () => (
    <x-creation-or-edition>
      <section class="breadcrumbs text-sm">
        <ul>
          <li><RouterLink to="/user-strategy-layer">分层列表</RouterLink></li>
          <li> {id ? '编辑分层' : '创建分层'} </li>
        </ul>
      </section>
      <main class="flex flex-col rounded-lg bg-white px-8 py-6 ">
        {
          (loading.value)
            ? <span>Loading...</span>
            : (
                <>
                  <Form data={formData.value} class="flex-col"
                    onChange={onChange}
                    onSubmit={onSubmit}
                    error={error.value}
                    actions={(
                      <x-actions class="flex gap-x-2">
                        <Button class="btn  btn-sm" onClick={() => {
                          void router.push('/user-strategy-layer')
                        }}
                        >取消
                        </Button>
                        <Button class="btn btn-primary btn-sm" type="submit">提交</Button>
                      </x-actions>
                    )}
                    items={
                      [
                        () => (
                          <div class="rounded-lg bg-gray-200 p-4 text-red-700">注意事项：
                            <ol>
                              <li>
                                1. 充值相关的值均支持输入<strong>2位小数</strong>
                              </li>
                              <li>2. 天数、次数相关的值仅支持输入<strong>整数</strong></li>
                              <li>
                                3. 若区间值的两项中有一项未填，则该选项即使启用也<strong>无法生效</strong>
                              </li>
                              <li>4. 若未选中“启用”，则后置选项即使选中也不生效</li>
                              <li>5. 数值、天数、次数相关区间值填写时，后面的值需大于前面的值，否则创建后该条件不生效</li>
                              <li>6. 所有条件需在“是否下发给正式用户”条件里勾选“是”才会生效；若只需下发给白名单用户，则勾选“否”</li>
                            </ol>
                          </div>
                        ),
                        [
                          [requiredLabel('分层名称'), 'name', { type: 'text' }, { class: mc('col-span-1') }],
                          [requiredLabel('排序'), 'priority', { type: 'number' }, { class: mc('col-span-1'), transform: transformInteger }],
                        ],
                        {
                          label: '是否下发给正式用户',
                          path: 'send_formal_user',
                          transform: transformInteger,
                          input: {
                            type: 'radio',
                            options: [
                              { value: 1, label: '是' },
                              { value: 0, label: '否' },
                            ],
                          },
                        },
                        ['白名单 - APP的UID 仅支持英文逗号分隔', 'whitelist', { type: 'textarea', rows: 4 }, { class: mc('col-span-1') }],
                        ['黑名单 - APP的UID 仅支持英文逗号分隔', 'blacklist', { type: 'textarea', rows: 4 }, { class: mc('col-span-1') }],
                        <x-lint class="w-full h-[1px] bg-[#eee]" />,
                        <h2 class="w-full text-red-500 mt-2 mb-2">主要for首页内容推荐侧使用</h2>,
                        {
                          label: requiredLabel('是否为剧维度画像'),
                          path: 'persona_type',
                          transform: transformInteger,
                          input: {
                            type: 'radio',
                            options: [
                              { value: 1, label: '是' },
                              { value: 0, label: '否' },
                            ],
                          },
                        },
                        formData.value.persona_type === 1 && [
                          requiredLabel('剧包ID'),
                          'series_package_ids',
                          {
                            type: 'multi-select',
                            search: true,
                            options: seriesPackageStore.list.value.map((n, index) => {
                              return { value: n.id, label: `${n.id}/${n.package_name}` }
                            }),
                          },
                        ],
                        formData.value.persona_type === 1 && formData.value.series_package_ids && formData.value.series_package_ids?.length > 0 && [
                          'flex flex-col gap-8',
                          ...series_package_list.value.map<FormItem>(option =>
                            [
                              <div class={mc('block bg-gray-100 text-[1.2em] font-medium pl-6 py-3 w-[100%] items-center gap-x-2 rounded-lg', option.type_name == '观剧信息' && 'bg-[#14CCE0]')}>{option.type_name} </div>,
                              '',
                              {
                                type: 'custom',
                                render: () => (
                                  option.type_list.map(item => (
                                    <div class={mc(formData.value.series_package_user_persona[item.item_code]?.[0] === 'valid'
                                      ? 'bg-blue-100 p-4 rounded-lg'
                                      : 'bg-gray-100 p-4 rounded-lg', item.item_name == 'Campaign' && '!bg-red-100')}
                                    >
                                      <x-title class="inline-flex items-center gap-x-2 rounded-lg bg-gray-100 px-2 py-1">{item.item_name} <span class="text-[12px] text-gray-400">{item.item_description}</span></x-title>
                                      <ProfileItemsInput
                                        modelValue={formData.value.series_package_user_persona[item.item_code]} valueLength={item.item_type}
                                        valueType={item.value_type === 1 ? 'string' : 'number'}
                                        valuePrefix={item.value_prefix} valueConcat={item.value_concat} valueSuffix={item.value_suffix}
                                        namePrefix={item.name_prefix} nameSuffix={item.name_suffix} nameConcat={item.name_concat}
                                        canCustom={item.can_custom === 1}
                                        options={item.item_child.map(i => ({
                                          name: i.name || i.value,
                                          value: i.value,
                                          group_name: i.group_name || '',
                                        }))}
                                        optionsType={item.item_child[0]?.group_name != undefined ? true : false}
                                        onUpdate:modelValue={value => {
                                          formData.value.series_package_user_persona[item.item_code] = value
                                        }}
                                      />
                                    </div>
                                  ))
                                ),
                              },
                              { errorVisible: false, class: 'w-[100%] overflow-auto border-gray-400 flex flex-col flex-wrap bg-[#eee] rounded-[20px]' },
                            ],
                          ),
                        ],
                        <x-lint class="w-full h-[1px] bg-[#eee]" />,
                        [
                          'flex flex-col gap-8',
                          ...userProfileList.value.map<FormItem>(option =>
                            [
                              <div class={mc('block bg-gray-100 text-[1.2em] font-medium pl-6 py-3 w-[100%] items-center gap-x-2 rounded-lg', option.type_name == '预设分层条件' && 'bg-[#14CCE0]')}>{option.type_name} </div>,
                              '',
                              {
                                type: 'custom',
                                render: () => (
                                  option.type_list.map(item => formData.value.persona_type === 1 && formData.value.series_package_ids && formData.value.series_package_ids?.length > 0
                                  && ['WatchDeep', 'WatchSeriesStatus', 'WatchSeriesCount', 'WatchEpisodeNum'].includes(item.item_code) ? null : (
                                      <div class={mc(formData.value.user_persona[item.item_code]?.[0] === 'valid'
                                        ? 'bg-blue-100 p-4 rounded-lg'
                                        : 'bg-gray-100 p-4 rounded-lg', item.item_name == 'Campaign' && '!bg-red-100')}
                                      >
                                        <x-title class="inline-flex items-center gap-x-2 rounded-lg bg-gray-100 px-2 py-1">{item.item_name} <span class="text-[12px] text-gray-400">{item.item_description}</span></x-title>
                                        <ProfileItemsInput
                                          modelValue={formData.value.user_persona[item.item_code]} valueLength={item.item_type}
                                          valueType={item.value_type === 1 ? 'string' : 'number'}
                                          valuePrefix={item.value_prefix} valueConcat={item.value_concat} valueSuffix={item.value_suffix}
                                          namePrefix={item.name_prefix} nameSuffix={item.name_suffix} nameConcat={item.name_concat}
                                          canCustom={item.can_custom === 1}
                                          options={item.item_child.map(i => ({
                                            name: i.name || i.value,
                                            value: i.value,
                                            group_name: i.group_name || '',
                                          }))}
                                          optionsType={item.item_child[0]?.group_name != undefined ? true : false}
                                          onUpdate:modelValue={value => {
                                            formData.value.user_persona[item.item_code] = value
                                          }}
                                        />
                                      </div>
                                    ))
                                ),
                              },
                              { errorVisible: false, class: 'w-[100%] overflow-auto border-gray-400 flex flex-col flex-wrap bg-[#eee] rounded-[20px]' },
                            ],
                          ),
                        ],
                        ['描述', 'desc', { type: 'textarea', rows: 4 }, { class: mc('col-span-1') }],
                      ] as any
                    }
                  />
                </>
              )
        }
      </main>
    </x-creation-or-edition>

  )
})

export default PopupCondition
