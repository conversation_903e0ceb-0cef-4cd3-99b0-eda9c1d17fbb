import { bindLoading, when } from '@skynet/shared'
import { authInfo } from 'src/lib/get-auth'
import { ref } from 'vue'
import { apiGetWalletStoreList } from './top-up-api'
import { currencyList } from 'src/lib/get-currency'

export const useTopUpStore = () => {
  const exchangeList = ref<TopUp.ExchangeProduct[]>([])
  const rechargeList = ref<TopUp.RechargeProduct[]>([])
  const loading = ref(false)

  const getWalletStoreList = () => {
    void when(() => authInfo).then(async () => {
      const res = await bindLoading(apiGetWalletStoreList(), loading)
      if (!res.data) return
      exchangeList.value = res.data.exchange_list
      rechargeList.value = res.data.recharge_list.map(item => {
        if (currencyList && currencyList.length > 0) {
          const currency = currencyList?.find(c => c.sku_id == item.sku_id)
          return {
            ...item,
            formatted_price: currency?.formatted_price ?? '',
          }
        }
        return item
      })
    })
  }

  return {
    exchangeList,
    rechargeList,
    getWalletStoreList,
    loading,
  }
}
