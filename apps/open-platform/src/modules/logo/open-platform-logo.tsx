import { ClassName, createComponent, mc } from '@skynet/shared'
import { MergeClass, SvgIcon } from '@skynet/ui'
import { ElDivider } from 'element-plus'
import { app } from '../common/constants'
import { RouterLink } from 'vue-router'
import { useI18n } from 'vue-i18n'
type Options = {
  props: {
    size?: 'small' | 'large'
    class?: ClassName
  }
}
export const OpenPlatformLogo = createComponent<Options>({
  props: {
    size: 'small',
    class: '',
  },
}, props => {
  const { t } = useI18n()
  return () => (
    <RouterLink class={mc('text-black flex justify-start items-center', props.class)} to="/">
      <SvgIcon name="ic_nav_icon" class={mc('mr-2', props.size === 'small' ? 'h-[33px] w-[22px]' : 'w-[34px] h-[50px]')} />
      <SvgIcon noColor name="ic_nav_text" class={mc('', props.size === 'small' ? 'w-[119px] h-4' : 'w-[178px] h-[23px]')} />
      <ElDivider direction="vertical" class={mc('inline-block !mx-3', props.size === 'small' ? '!h-4' : '!h-6')} />
      <span class={mc('font-medium', props.size === 'small' ? 'text-sm' : 'text-lg')}>{t('openCollaborationPlatform')}</span>
    </RouterLink>
  )
})

export default OpenPlatformLogo
