export const GENDERS = [
  {
    value: 0,
    label: '未知',
    roles: [
      { value: '系统', label: '系统' },
      { value: '机器人', label: '机器人' },
      { value: '科幻玄幻生命体', label: '科幻玄幻生命体' },
      { value: '神', label: '神' },
      { value: '无性别角色', label: '无性别角色' }
    ]
  },
  {
    value: 1,
    label: '男',
    roles: [
      { value: '霸总', label: '霸总' },
      { value: '高冷CEO', label: '高冷CEO' },
      { value: 'Alpha', label: 'Alpha' },
      { value: '赘婿', label: '赘婿' },
      { value: '战神', label: '战神' },
      { value: '隐藏大佬', label: '隐藏大佬' },
      { value: '偏执狂', label: '偏执狂' },
      { value: '病娇', label: '病娇' },
      { value: '冷血教授', label: '冷血教授' },
      { value: '商界精英', label: '商界精英' },
      { value: '电竞大神', label: '电竞大神' },
      { value: '黑道大佬', label: '黑道大佬' },
      { value: '落魄少爷', label: '落魄少爷' },
      { value: '复仇者', label: '复仇者' },
      { value: '温柔竹马', label: '温柔竹马' },
      { value: '腹黑帝王', label: '腹黑帝王' },
      { value: '温柔男二', label: '温柔男二' },
      { value: '白月光', label: '白月光' },
      { value: '反派', label: '反派' },
      { value: '保镖', label: '保镖' },
      { value: '黑道小弟', label: '黑道小弟' },
      { value: '主持人', label: '主持人' },
      { value: '记者', label: '记者' },
      { value: '萌宝', label: '萌宝' },
      { value: '系统', label: '系统' },
      { value: '学生', label: '学生' },
      { value: '龙套', label: '龙套' }
    ]
  },
  {
    value: 2,
    label: '女',
    roles: [
      { value: '傻白甜', label: '傻白甜' },
      { value: '落难千金', label: '落难千金' },
      { value: '复仇大女主', label: '复仇大女主' },
      { value: '作精', label: '作精' },
      { value: '绿茶', label: '绿茶' },
      { value: '美人', label: '美人' },
      { value: '神医', label: '神医' },
      { value: '明星', label: '明星' },
      { value: '飒爽御姐', label: '飒爽御姐' },
      { value: '娇妻', label: '娇妻' },
      { value: '心机女二', label: '心机女二' },
      { value: '恶毒千金', label: '恶毒千金' },
      { value: '白莲花', label: '白莲花' },
      { value: '恶毒后妈', label: '恶毒后妈' },
      { value: '假千金', label: '假千金' },
      { value: '反派女boss', label: '反派女boss' },
      { value: '怪物', label: '怪物' },
      { value: '主持人', label: '主持人' },
      { value: '记者', label: '记者' },
      { value: '萌宝', label: '萌宝' },
      { value: '系统', label: '系统' },
      { value: '学生', label: '学生' },
      { value: '龙套', label: '龙套' }
    ]
  }
]

export const AGES = [
  {
    label: '青年（18-35）',
    value: '青年（18-35）'
  },
  {
    label: '中年（36-55）',
    value: '中年（36-55）'
  },
  {
    label: '青少年（13-17）',
    value: '青少年（13-17）'
  },
  {
    label: '婴幼儿（0-3）',
    value: '婴幼儿（0-3）'
  },
  {
    label: '儿童（4-12）',
    value: '儿童（4-12）'
  },
  {
    label: '中老年（56-65）',
    value: '中老年（56-65）'
  },
  {
    label: '老年（66+）',
    value: '老年（66+）'
  }
]

export const TIMBRE_FEATURE = [
  { label: '低沉磁性', value: '低沉磁性' },
  { label: '沙哑厚重', value: '沙哑厚重' },
  { label: '冷感', value: '冷感' },
  { label: '杀气', value: '杀气' },
  { label: '清润明朗', value: '清润明朗' },
  { label: '温暖', value: '温暖' },
  { label: '阳光', value: '阳光' },
  { label: '阴郁暗哑', value: '阴郁暗哑' },
  { label: '清脆甜美', value: '清脆甜美' },
  { label: '略带鼻音', value: '略带鼻音' },
  { label: '冷冽御姐音', value: '冷冽御姐音' },
  { label: '略带烟嗓', value: '略带烟嗓' },
  { label: '尖细娇嗲', value: '尖细娇嗲' },
  { label: '语速快', value: '语速快' },
  { label: '柔软脆弱', value: '柔软脆弱' },
  { label: '中音沉稳', value: '中音沉稳' },
  { label: '优雅', value: '优雅' },
  { label: '阴冷滑腻', value: '阴冷滑腻' },
  { label: '沙哑老练', value: '沙哑老练' },
  { label: '尖锐刻薄', value: '尖锐刻薄' }
]