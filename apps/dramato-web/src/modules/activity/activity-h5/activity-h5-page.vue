<template>
  <div class="px-3 py-3" v-if="Object.keys(activityData).length == 0">
    <div class="loading rounded-lg h-44 mb-9"></div>
    <div v-for="item in 3">
      <div class="loading rounded w-[30%] h-8 mt-6"></div>
      <div class="loading rounded w-full h-4 mt-5"></div>
      <div class="loading rounded w-full h-4 mt-3"></div>
      <div class="loading rounded w-[60%] h-4 mt-3"></div>
    </div>
  </div>
  <div v-else class="min-h-[100vh] w-full" :style="'background: ' + activityData.background_color + ';'">
    <div :class="'bg-[' + activityData.background_color + ']'">
      <div class="w-full min-h-8">
        <img class="w-full" :src="getBanner()" />
      </div>

      <div class="min-h-[80vh]">
        <div v-for="(item, key) in activityData.activity_cards" :key="key">
          <div class="my-4 rounded-xl mx-4 px-4 py-5" v-if="item && item.title && item.content"
            :style="'background: ' + item.background_color">
            <div v-if="item.card_type == 1">
              <div class="flex items-center mb-4">
                <img class="w-7 h-7 mr-2" src="./image/card-icon.png" />
                <p class="text-lg text-[#0B080B] font-bold">{{ item.title }}</p>
              </div>
              <div class="text-sm text-[#434546] ">
                <div style="word-break: break-word;" v-html="item.content"></div>
              </div>
            </div>
            <div v-else>
              <div class="flex items-center mb-4">
                <img class="w-7 h-7 mr-2" src="./image/card-icon.png" />
                <p class="text-lg text-[#0B080B] font-bold">{{ item.title }}</p>
              </div>
              <div class="text-sm text-[#434546] ">
                <div class="flex gap-3 text-base font-medium text-[#0B080B] mb-3">
                  <div class="flex-1">Name</div>
                  <div class="flex-1">Prize</div>
                </div>
                <div class="flex gap-3 text-sm text-[#434546] mb-3" v-for="(inItem, inKey) in getCardContentReward(item)"
                  :key="inKey">
                  <div class="flex-1 whitespace-normal">{{ inItem[0] }}</div>
                  <div class="flex-1 whitespace-normal">{{ inItem[1] }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>

      <div class="mt-7 pb-8">
        <div class="flex justify-center">
          <img class="h-8" src="./image/logo.png" />
        </div>
        <div class="flex justify-center mt-4 text-[#434546]">
          {{ isIos() ? 'This event is not affiliated with Apple Inc' : 'This event is not affiliated with Google inc' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router'
import { apiActivityInfo } from './activity-h5-api'
import { track } from 'src/lib/track'
import { isIos } from 'src/lib/ua.ts'


const route = useRoute()
const router = useRouter()

const currentId = ref()
const currentLang = ref()

const activityData = ref({})


const getCardContentReward = (item: any) => {
  let arr = []
  let contArr = item.content.split('\n')
  for (let j in contArr) {
    let contItem = contArr[j].split('/')
    arr.push(contItem)
  }
  return arr
}

const getCardTitle = (item: any) => {
  let text = ''
  for (let i in item.title) {
    text = item.title[i]
  }
  return text
}
const getBanner = () => {
  let src = ''
  for (let i in activityData.value.activity_cards) {
    src = activityData.value.activity_image
  }
  return src
}

const getActivityData = () => {
  if (route.query.id) {
    currentId.value = String(route.query.id || '')
    currentLang.value = String(route.query.language || '')
    apiActivityInfo(currentId.value, currentLang.value).then(res => {
      let resData = res.data || [{ activity_cards: [] }]
      resData.activity_cards.sort((a, b) => a.index - b.index);
      activityData.value = res.data
    }).then(() => {
      activityData.value.activity_cards = [...activityData.value.activity_cards].sort((a, b) => a.index - b.index);
    })


  }
}
onMounted(() => {

  track('comment_activity', 'page', 'show', {
    user_id: route.query.user_id,
    activity_id: route.query.id,
    from: route.query.from,
  })
  getActivityData()
  // activityData.activity_cards = [...activityData.activity_cards].sort((a, b) => a.index - b.index);

})

</script>


<style scoped>
.loading {
  background-color: rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.08) 50%,
      rgba(255, 255, 255, 0) 100%);
  animation: shine 1s infinite linear;
}

@keyframes shine {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(100%);
  }
}
</style>