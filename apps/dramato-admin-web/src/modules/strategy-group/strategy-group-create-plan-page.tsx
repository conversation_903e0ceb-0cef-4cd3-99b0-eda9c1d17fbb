import { createComponent } from '@skynet/shared'
import { Button, CreateTableOld, DialogFooter, openDialog, TableColumnOld, transformInteger, transformNumber, transformTimestamp } from '@skynet/ui'
import dayjs from 'dayjs'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { apiSaveStrategyGroup } from 'src/modules/strategy-group/strategy-group-api'
import { useStrategyGroupStore } from 'src/modules/strategy-group/strategy-group-store'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { EpisodePage } from '../episode/episode-page'

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] h-[80vh] overflow-hidden px-4'

export const StrategyGroupCreatePlanPage = createComponent(null, props => {
  const { formData, Form, resetTaskFormData, stepThreeError, validateAllSteps, stepOneError, stepTwoError, needShowSaveDragDialog } = useStrategyGroupStore()

  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')

  const Table = CreateTableOld<M.Episode>()
  const checkedEpisodeItem = ref<M.Episode[]>([])

  const episodeColumns: TableColumnOld<M.Episode>[] = [
    ['剧集ID', 'series_key', { class: 'w-[120px]' }],
    ['封面', row => <img src={row.cover} class="size-[100px] object-cover" />, { class: 'w-[120px]' }],
    ['剧名', 'title', { class: 'w-[320px]' }],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              if (!formData.value.task_config) {
                return
              }
              (formData.value.task_config.drama_list || []).splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[80px]' },
    ],
  ]

  const showImportDramaDialog = () => {
    if (formData.value.task_config && formData.value.task_config.drama_list) {
      checkedEpisodeItem.value = [...formData.value.task_config.drama_list]
    }
    const closeDialog = openDialog({
      title: '导入指定生效短剧',
      mainClass: dialogMainClass,
      body: (
        <x-import-recharge-level class="flex-1 flex flex-col overflow-hidden gxp-y-4">
          <x-episode-list class="flex-1  overflow-y-auto">
            <EpisodePage
              hasNav={false}
              hasActions={false}
              hasCheckItem
              checkedItem={checkedEpisodeItem.value}
              onAdd={item => {
                checkedEpisodeItem.value?.push({ ...item, drama_id: item.id })
              }}
              onRemove={item => {
                checkedEpisodeItem.value = checkedEpisodeItem.value?.filter(i => i.id !== item.id)
              }}
            />
          </x-episode-list>
          <footer class="w-full flex justify-end gap-x-2 pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.task_config) {
                return
              }
              formData.value.task_config.drama_list = [
                ...(checkedEpisodeItem.value || []),
              ]
              closeDialog()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-800px overflow-hidden',
      beforeClose: () => {
        checkedEpisodeItem.value = []
      },
    })
  }
  const router = useRouter()
  const onSubmit = async () => {
    if (!validateAllSteps()) {
      openDialog({
        title: '出错提示',
        body: (
          <div>请检查表单<br />
            {JSON.stringify(stepOneError.value)}
            {JSON.stringify(stepTwoError.value)}
            {JSON.stringify(stepThreeError.value)}
          </div>
        ),
      })
      return
    }
    if (formData.value.user_config?.active_users_config?.start_days && formData.value.user_config?.active_users_config?.end_days) {
      if (formData.value.user_config?.active_users_config?.start_days > formData.value.user_config?.active_users_config?.end_days) {
        // 交换起止时间
        const temp = formData.value.user_config.active_users_config.start_days
        formData.value.user_config.active_users_config.start_days = formData.value.user_config.active_users_config.end_days
        formData.value.user_config.active_users_config.end_days = temp
      }
    }
    const response = await apiSaveStrategyGroup({ ...formData.value, save_type: 1 })
    if (!response.data) return
    needShowSaveDragDialog.value = false
    formData.value = response.data
    setTimeout(() => {
      void router.push({ path: route.path, query: { ...route.query, has_edit: 'false' } }).then(() => {
        const closeDialog = openDialog({
          title: '提示',
          body: (
            <div>保存成功
              <DialogFooter okText="返回列表页" onOk={() => {
                closeDialog()
                void router.push('/strategy-group')
              }} cancelText="留在当前页" onCancel={() => closeDialog()}
              />
            </div>
          ),
        })
      })
    })
  }

  return () => (
    <div class="flex flex-col gap-4 p-8">
      <Form
        class="space-y-4 block"
        data={formData.value}
        error={stepThreeError.value}
        onChange={(path, value) => {
          if (['task_config.auto_up'].includes(path) && value === 'Invalid Date') {
            set(formData.value || {}, path, undefined)
            return
          }
          set(formData.value, path, value)
        }}
        onReset={() => resetTaskFormData(false)}
        onSubmit={onSubmit}
        items={[
          [
            [
              requiredLabel(<span>任务优先级<small class="text-gray-500 pl-1">1最高</small></span>),
              'task_config.priority',
              {
                type: 'number',
                disabled: isViewMode.value,
              },
              { transform: transformInteger },
            ],
            [
              requiredLabel('策略持续时间'),
              'task_config.duration',
              {
                type: 'number',
                placeholder: '填 -1 表示不限',
                suffix: '天',
                min: -1,
                disabled: isViewMode.value,
              },
              { transform: transformNumber, class: 'w-[8em]' },
            ],
            [
              <span>自动上架时间<small class="text-gray-500 pl-1">北京时间，不填表示不限制</small></span>,
              'task_config.auto_up',
              {
                type: 'datetime',
                min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                displayFormat: 'YYYY-MM-DD HH:mm:ss',
                disabled: isViewMode.value,
              },
              {
                transform: transformTimestamp,
              },
            ],
          ],

          [
            '指定生效短剧',
            'task_config.drama_list',
            {
              type: 'custom',
              render: ({ onInput, value }) => {
                return (
                  <section class="bg-gray-100 p-4 rounded-lg">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportDramaDialog()}>导入配置
                      </Button>
                    </div>
                    <hr class="my-4" />
                    <Table
                      list={!formData.value.task_config?.drama_list ? [] : formData.value.task_config.drama_list}
                      columns={episodeColumns}
                    />
                  </section>
                )
              },
            },
          ],
        ]}
        actions={() => (
          <>
            <hr class="mb-4" />
            <div class="flex justify-between gap-x-2">
              <Button class="btn btn-sm" onClick={() => {
                void router.push({ path: `/strategy-group/create/product?id=${formData.value.id}`, query: { ...route.query } })
              }} type="button"
              >上一步
              </Button>
              {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button>}
              {isViewMode.value ? null : <Button class="btn btn-primary btn-sm" type="submit">保存</Button>}
            </div>
          </>
        )}
      />

    </div>
  )
})
export default StrategyGroupCreatePlanPage
