<template>
  <v-group>
    <template v-for="(graduation, index) in graduations" :key="index">
      <v-line :config="{
        points: [graduation.x, 0, graduation.x, graduation.height],
        stroke: graduation.color,
        strokeWidth: graduation.strokeWidth
      }" />
      <v-text v-if="graduation.showText" :config="{
        x: graduation.x - 10,
        y: 20,
        text: graduation.time,
        fontSize: 12,
        fill: '#6B7280'
      }" />
    </template>
  </v-group>
</template>

<script lang="tsx" setup>
import { computed } from 'vue';

const props = defineProps<{
  duration: number;
  width: number;
}>();

// 计算刻度位置和时间
const graduations = computed(() => {
  const result = [];
  const pixelsPerSecond = props.width / props.duration; // 每秒占用的像素数

  for (let i = 0; i <= props.duration; i++) {
    const isMinute = i % 60 === 0;
    const isTenSeconds = i % 10 === 0;

    result.push({
      x: i * pixelsPerSecond,
      height: isMinute ? 20 : (isTenSeconds ? 16 : 8),
      strokeWidth: isMinute ? 2 : 1,
      color: isMinute ? '#6B7280' : '#D1D5DB',
      showText: isMinute || isTenSeconds,
      time: formatTime(i)
    });
  }
  return result;
});

// 格式化时间显示 (mm:ss)
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
};
</script>
