export const time = {
  hour: 3600000,
  day: 86400000,
}
export const supportedLocaleStrings
  = [
    'en-US', 'zh-TW', 'de-DE', 'es-ES', 'fr-FR',
    'id-ID', 'it-IT', 'ja-JP', 'ko-KR', 'ms-MY', 'pt-PT',
    'ru-RU', 'th-TH', 'tl-PH', 'tr-TR', 'vi-VN',
  ] as const
export type SupportedLocale = (typeof supportedLocaleStrings)[number]

export const supportedLocaleList: { shortCode: string, name: string, language: SupportedLocale, timezone: string, country: string }[] = [
  { shortCode: 'de', name: 'Deuts<PERSON>', language: 'de-DE', timezone: 'Europe/Berlin', country: 'DE' },
  { shortCode: 'en', name: 'English', language: 'en-US', timezone: 'America/New_York', country: 'US' },
  { shortCode: 'es', name: '<PERSON>spaño<PERSON>', language: 'es-ES', timezone: 'Europe/Madrid', country: 'ES' },
  { shortCode: 'fr', name: 'Français', language: 'fr-FR', timezone: 'Europe/Paris', country: 'FR' },
  { shortCode: 'id', name: 'Bahasa Indonesia', language: 'id-ID', timezone: 'Asia/Jakarta', country: 'ID' },
  { shortCode: 'it', name: 'Italiano', language: 'it-IT', timezone: 'Europe/Rome', country: 'IT' },
  { shortCode: 'ja', name: '日本語', language: 'ja-JP', timezone: 'Asia/Tokyo', country: 'JP' },
  { shortCode: 'ko', name: '한국어', language: 'ko-KR', timezone: 'Asia/Seoul', country: 'KR' },
  { shortCode: 'ms', name: 'Bahasa Melayu', language: 'ms-MY', timezone: 'Asia/Kuala_Lumpur', country: 'MY' },
  { shortCode: 'pt', name: 'Português', language: 'pt-PT', timezone: 'Europe/Lisbon', country: 'PT' },
  { shortCode: 'ru', name: 'Русский', language: 'ru-RU', timezone: 'Europe/Moscow', country: 'RU' },
  { shortCode: 'th', name: 'ไทย', language: 'th-TH', timezone: 'Asia/Bangkok', country: 'TH' },
  { shortCode: 'tl', name: 'Tagalog', language: 'tl-PH', timezone: 'Asia/Manila', country: 'PH' },
  { shortCode: 'tr', name: 'Türkçe', language: 'tr-TR', timezone: 'Europe/Istanbul', country: 'TR' },
  { shortCode: 'vi', name: 'Tiếng Việt', language: 'vi-VN', timezone: 'Asia/Ho_Chi_Minh', country: 'VN' },
  // { shortCode: 'zh-CN', name: '中文（简体）', language: 'zh-CN', timezone: 'Asia/Shanghai', country: 'CN' },
  { shortCode: 'zh-TW', name: '中文（繁體）', language: 'zh-TW', timezone: 'Asia/Taipei', country: 'TW' },
]

export const pageMap = {
  home: '/home',
  library: '/library',
  libraryHistory: '/library/history',
  libraryRecommend: '/library/recommend',
  searchResult: 'search_result',
  searchMid: 'search_mid',
}
