/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiCreateJDRank, apiGetJDRankList } from './rank-api'
import { JDRankForm } from './rank-form'

export const useJDRank = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    search,
    showADDialog,
    closeJDRankDialog,
    currentJDRank,
    onCreateBtnClick,
    onEditBtnClick,
    onCreate,
    onEdit,
    isUpdating,
  }
}

const Form = CreateForm<M.JDRank.params>()
const params = ref<M.JDRank.params>({
  // type: 1,
})

const Table = CreateTableOld<M.JDRank.Rank>()
const list = ref<M.JDRank.Rank[]>([])
const loading = ref<boolean>(false)
const isUpdating = ref(false)

const currentJDRank = ref<M.JDRank.CreateRank>({})

const search = async (_page?: number) => {
  try {
    loading.value = true
    const res = await apiGetJDRankList({ ...params.value })
      .finally(() => {
        loading.value = false
      })
    list.value = (res.data?.list || []).map(i => ({
      ...i,
      series_ids: i.series_info.map(j => j.series_id).join(','),
    }))
  } catch (error) {
    list.value = []
  }
}

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] overflow-hidden'

const closeJDRankDialog = ref()

const showADDialog = () => {
  closeJDRankDialog.value = openDialog({
    title: currentJDRank.value?.id ? '编辑剧单配置' : '新建剧单配置',
    body: () => <JDRankForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px] overflow-hidden',
  })
}

const onCreateBtnClick = () => {
  currentJDRank.value = {
    // series_info: [{}],
    // type: 2,
  }
  showADDialog()
}

const onEditBtnClick = (r: M.JDRank.Rank) => {
  currentJDRank.value = {
    ...r,
  }
  showADDialog()
}

const onEditSuccess = (isCreate?: boolean) => {
  isUpdating.value = false
  closeJDRankDialog.value && closeJDRankDialog.value()
  if (isCreate) {
    void search(1)
    return
  }
  void search()
}

const switchRequestParams = () => ({
  ...currentJDRank.value,
  language_version: params.value.language_version,
  type: 1,
  series_info: (currentJDRank.value.series_ids || '')?.split(',').map(i => ({ series_id: i })),
  // pid: currentJDRankInfo.value?.id,
  // auto_type: !(params.value.type === 2 && currentJDRank.value.type === 2) ? undefined : currentJDRank.value.auto_type,
  // tag_id_list: !(params.value.type === 1 && currentJDRank.value.type === 2) ? [] : currentJDRank.value.tag_id_list
})

const onCreate = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiCreateJDRank(switchRequestParams())
    showAlert('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '创建失败', 'error')
  }
}

const onEdit = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiCreateJDRank(switchRequestParams())
    showAlert('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '编辑失败', 'error')
  }
}
