<template>
  <div class="px-5 py-4">
    <div class="font-bold text-lg mb-4">{{ props.title }}</div>
    <div class="text-sm mb-5">{{ props.message }}</div>
    <div class="flex flex-col gap-3">
      <button @click="() => props.onConfirm()" class="!h-11 !bg-brand-6 !text-white !font-medium !rounded-lg reset">{{
        props.confirmText }}</button>
      <button @click="() => props.onClose()" class="reset !font-medium !text-text-2">{{ props.cancelText }}</button>
    </div>
  </div>
</template>

<script setup lang="ts">

const props = withDefaults(defineProps<{
  title?: string
  message?: string
  confirmText?: string
  cancelText?: string
  onConfirm?: () => void
  onClose?: () => void
}>(), {
  onConfirm: () => { },
  onClose: () => { },
})
</script>

<style scoped>
.reset {
  all: unset;
}

:global(.van-dialog__message) {
  padding: 0 !important;
}

:global(.van-dialog) {
  --van-dialog-width: 295px;
  --van-dialog-background: #2E2F30;
  --van-text-color: #FDFBFC;
  --van-dialog-radius: 12px;
}
</style>
