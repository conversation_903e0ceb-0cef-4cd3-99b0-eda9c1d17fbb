declare namespace M {

  interface IRoleUserParams {
    source: 0 | 1 | 2 // 0 所有 1 内部用户 2 外部用户
    // role: number // 0 所有 1 超管 2 普通用户 3 只能查看资源管理，修改字幕 4 外部合作方 6 高级运营 6 初级运营
    username: string
    open_roles: number[]
    page_index?: number
    page_size?: number
  }

  interface IUserRole {
    user_ids: string[]
    role: number
    open_roles: number[]
  }
  interface IRoleUser {
    user_id: string
    username: string
    role: number
    open_roles: number[]
    source: number
    status: number
  }

  interface ICurrentUser {
    users?: IRoleUser[]
    user_id?: string[]
    role?: number
    open_roles?: number[]
  }

  interface IUser {
    username: string
    access_key?: string
    source: number // 2 外部用户 只能新建外部用户
    open_roles: number[] // 支持的角色列表
    operate_type?: 1 | 2 | 3 | 4 // 1 新建 2 更新 3 注销 4 撤销注销
    company?: string
    department?: string
    password?: string
  }
  interface IUserMenuItem {
    id: number
    parent_id: number
    path: string
    title: string
    children?: IUserMenuItem[]
  }

}
