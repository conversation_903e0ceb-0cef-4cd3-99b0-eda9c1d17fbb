import { r } from '@skynet/shared'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from './layouts/main-layout'
import { activityRoutes } from './modules/activity/activity-routes'
import { bindEmailRoutes } from './modules/bind-email/bind-email-routes'
import { coinsRoutes } from './modules/coins/coins-routes'
import { customerServiceCenterRoutes } from './modules/customer-service-center/customer-service-center-routes'
import { mealCheckInRoutes } from './modules/meal-check-in/meal-check-in-routes'
import { rechargeRoutes } from './modules/recharge/recharge-routes'
import { regulationsRoutes } from './modules/regulations/regulations-routes'
import { shareRoutes } from './modules/share/share-routes'
import { sleepCheckInRoutes } from './modules/sleep-check-in/sleep-check-in-routes'
import { NotFoundPage } from './modules/special-pages/not-found-page'
import { ttkolRoutes } from './modules/ttkol/ttkol-routes'

declare module 'vue-router' {
  interface RouteMeta {
    title?: string
  }
}
/**
 * 公开路由，无需登录即可访问
 * 路由一经发布，不得修改，只能新增和重定向
 */
export const publicRoutes: RouteRecordRaw[] = [
  r('/', '', MainLayout, [
    r('', '', () => import('./layouts/old-layout'), [
      r('', '', () => import('./modules/home/<USER>')),
      r(['account-deletion', 'delete_account'], 'Account Deletion', () => import('./modules/home/<USER>')),
      shareRoutes,
      customerServiceCenterRoutes,
      rechargeRoutes,
      activityRoutes,
      regulationsRoutes,
      bindEmailRoutes,
      coinsRoutes,
      mealCheckInRoutes,
      sleepCheckInRoutes,
      ttkolRoutes,
      r('/:pathMatch(.*)*', 'Not Found', NotFoundPage),
    ]),
  ]),
]

const history = createWebHistory()
const router = createRouter({
  history,
  routes: publicRoutes,
})

export default router
