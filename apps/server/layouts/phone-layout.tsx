import { createComponent } from '@skynet/shared'
import { storeToRefs } from 'pinia'
import vhCheck from 'vh-check'
import { onMounted, onUnmounted, type VNodeChild } from 'vue'
import { useRouter } from 'vue-router'
import { useDialogStore } from '~/modules/common/dialog'
import { useYlAd } from '~/modules/yl-ad/yl-ad'
import { initDefaultLang } from '~/init/init-default-lang'
import { initH5I18n } from '~/init/init-h5-i18n'
import { initPinia } from '~/init/init-pinia'
// import { initFaceBookYLSdk } from '~/init/init-third-party'
import { initVant } from '~/init/init-vant'
import 'vant/lib/index.css'
import '~/init/init-v-console'
import { useHomePageStore } from '~/modules/home/<USER>'
import { useScrollPositionStore } from '~/modules/home/<USER>'
// /* @ts-expect-error never mind */
// import('virtual:svg-icons-register')
// /* @ts-expect-error never mind */
// import('virtual:svg-no-colored-icons-register')
type PhoneLayoutOptions = {
  slots: {
    default: () => VNodeChild
  }
}

export const PhoneLayout = createComponent<PhoneLayoutOptions>(null, (props, { slots }) => {
  const { renderDialogs } = useDialogStore('h5-dialog')

  initH5I18n()
  initVant()
  initPinia()
  // void initFacebookSdk() // TODO: 播放页面暂时不需要初始化Facebook和google
  // void initGoogleSdk()
  // void initFaceBookYLSdk()

  initDefaultLang()
  const scrollPositionStore = useScrollPositionStore()
  const { needChangeBgColor } = storeToRefs(useHomePageStore())
  const router = useRouter()
  function getFromPage(path: string) {
    if (path === '/') {
      return 'home'
    }
    if (path === '/my-list') {
      return 'library'
    }
    if (path === '/watch-history') {
      return 'library/history'
    }
    return undefined
  }
  router.beforeEach((to, from, next) => {
    const path = to.path
    let extraQuery: Record<string, string | undefined> = { }
    // 如果跳转到播放页，那么点击后推的时候要返回原页面
    if (to.path.startsWith('/series/') && to.path.indexOf('/art') === -1) {
      if (!from.path.startsWith('/series/') && !to.query.return_to) {
        extraQuery = {
          return_to: from.fullPath,
          from_page: to.query.from_page?.toString() || getFromPage(from.path),
        }
        next({ ...to, path, query: { ...to.query, ...extraQuery } })
        return
      }
    }
    next()
  })

  const handleScroll = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    if (scrollTop > 0) {
      needChangeBgColor.value = true
    } else {
      needChangeBgColor.value = false
    }
    scrollPositionStore.setScrollTop(scrollTop)
  }

  router.afterEach((to, from) => {
    const state = { ...window.history.state, fromProject: true }
    window.history.replaceState(state, '')
  })

  const handlePopState = (event: PopStateEvent) => {
    console.log('popstate', event)
    if (!event.state || !event.state.fromProject) {
      // 清理所有的历史记录
      // window.history.pushState(null, '', '/')
      void router.replace('/')
    }
  }

  useYlAd() // yl投放

  onMounted(() => {
    window.addEventListener('popstate', handlePopState)
    vhCheck()
    window.addEventListener('scroll', handleScroll)
  })

  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
    window.removeEventListener('popstate', handlePopState)
  })

  return () => (
    <x-phone-layout class="block">
      {/* <RouterView /> */}
      {slots.default ? slots.default() : null}
      <div class="size-full" id="YlBox">
        { renderDialogs() }
      </div>
    </x-phone-layout>
  )
})

export default PhoneLayout
