import { transformBool } from '@skynet/ui'

export const en = {
  // 通用
  common: {
    cancel: 'Cancel',
  },
  // 分享页
  share: {
    watchNow: 'Watch Now',
  },
  'customer-service-center': {
    title: 'Customer Service Center',
    description: 'Description',
    'description-placeholder': 'Provide details to help us understand the problem',
    'email-placeholder': 'Enter your email to receive feedback updates',
    'submit-feedback': 'Submit',
    'enter-feedback': 'Other issues feedback',
    'upload-pictures': 'Upload pictures',
    feedback: 'Feedback',
    faq: {
      question1: 'Why does it say \'Ads Not Ready Yet\'?',
      answer1: 'Sometimes, unstable server or advertiser connections can cause issues. We recommend retrying later and not clicking "Watch Ads" too often. Ad limits may apply due to third-party policies or your country\'s restrictions. We\'re constantly working with our ad providers to enhance your ad-watching experience.',
      question2: 'Why does it say \'Network Unavailable\' for ads?',
      answer2: 'Please try to restart the app by closing it completely and then reopening it. If the ads are still not loading, wait a while before trying again, and avoid clicking too often. If the issue persists, you may reach out through our Feedback system.We\'re also working with our ad team to enhance the viewing experience. Thank you for your patience.',
    },
  },
  toast: {
    'upload-failed': 'Upload failed, please upload again',
    'submit-success': 'Submit successfully',
    'email-incorrect': 'The email format is incorrect',
  },
  not_found: {
    title: 'The page you are looking for does not exist',
  },
  regulations: {
    howUseData: 'How We Use Your Data',
    content: `If you select "Allow" on the App Tracking Transparency permission request, we will use your information and data to optimize our services and provide you with the best experience, otherwise we will only collect the data and will not conduct any tracking activities.

If you agree to allow DramaWave and our partners and suppliers to use data from your device to analyze how you use the application, personalize content recommendations and provide more information related services, please click "Agree and Continue" to continue.
`,
    acceptAll: 'Accept All',
    dataSettings: 'Data Settings',
    next: 'Next',
    confirm: 'Confirm',
    aboutYourPrivacy: 'About Your Privacy',
    personalDataName: 'Personalised Data',
    personalDataDescription: 'We use this information to remember your choices and to provide  personalised advertising and content recommendations, notifications and  reminders, etc.',
    advertisementDataName: 'Advertisement Data',
    advertisementDataDescription: 'Cookies, identifiers of your  device, or other information may be stored or accessed on your device  for the purpose of online advertisements. User data may also be shared  with our advertising partners.',
    behaviorsDataName: 'Behavioural Data',
    behaviorsDataDescription: 'Data related to analytics, such as cookies or device identifiers, may be  stored for the purpose of measuring and analyzing usage, behavior, and  advertising performance.',
    privacyAndTerms: 'Privacy Policy and Terms of Service',
    referToPrivacy: 'Please refer to the Privacy Policy',
    referToTerms: 'Please refer to the Terms of Service',
    agreeAndSave: 'Agree & Save',
  },
  coins: {
    earnings: {
      title: 'My Earnings',
      total: 'Total Coins',
      exchange: 'Exchange',
      earningsHistory: 'Earnings History',
      exchangeHistory: 'Exchange History',
      noRecords: 'No Record',
      last50earnings: 'Only the last 50 earnings are displayed',
      last50exchanges: 'Only the last 50 exchange records are displayed',
      benefits: 'Benefits',
      gotIt: 'Got it',
    },
    exchange: {
      title: 'Exchange Zone',
      notOpen: 'Not yet opened, please stay tuned',
      receiveBtn: 'Happily Receive',
      earnCoins: 'Earn Coins',
      redeemRequiredXCoins: 'Redeem Required {X} Coins',
      exchangeSuccess: 'Exchange Successfully',
      cardId: 'Card ID',
      exchangeFailed: 'Exchange Failed',
      transferFailedInsufficientStock: 'Out of stock. Please come early tomorrow.',
      gotItBtn: 'Got it',
      transferFailedRiskControl: 'Account unusual, temporarily unable to exchange.',
      tryAgainBtn: 'Try again Later',
      transferConfirm: 'Transfer Confirm',
      paypalAccountEmail: 'Paypal Account email',
      confirmPaypalAccountEmail: 'Confirm Paypal Account email',
      transferX: 'Transfer {X}',
      transferXConfirm: 'Transfer {X} Confirm',
      transferToYourAccountX: 'Transfer to your account {X}',
      confirmBtn: 'Confirm',
      transferSuccessTitle: 'We’re sending the money to your paypal account!',
      transferSuccessContentX: 'your money will arrive within 5 days to the account of {X}',
      copySuccess: 'Copy Success',
      enterEmailError: 'Please enter a valid email address',
      enterEmailValidError: 'Input error, please re-enter',
      emailNotMatchError: 'The email address does not match, please re-enter',
    },
  },
  my_wallet: {
    title: 'My Wallet',
    help_dialog: {
      title: 'Ticket Usage Rules',
      content1: `1. 1 Purchased Ticket = 1 Purchased Reward Ticket = 1 AD Reward Ticket = 1 Coin Exchange Ticket – All of these can be used to unlock episodes.`,
      content2: `2. All tickets will never expire, but the viewing rights unlocked by Purchased Reward Tickets have an expiration date and will become invalid once expired.`,
      content3: `3. Purchased Tickets can be acquired through direct purchase.`,
      content4: `4. Purchased Reward Tickets can only be obtained during specific promotional periods through purchase.`,
      content5: `5. AD Reward Tickets can be earned by completing ad-viewing tasks.`,
      content6: `6. Coin Exchange Tickets can be obtained by exchanging coins.`,
    },
    account_balance: 'Account Balance',
    purchased_tickets: 'Purchased Tickets',
    purchase_reward_tickets: 'Purchased Reward Tickets',
    coin_exchange_tickets: 'Coin Exchange Tickets',
    ad_reward_tickets: 'AD Reward Tickets',
    top_up: 'Top Up',
    purchase_history: 'Transaction History',
    rewards_history: 'Reward History',
    exchange_history: 'Exchange History',
    consumption_records: 'Consumption Records',
    auto_unlock: 'Episodes on Auto-unlock',
  },
  top_up: {
    exchange_title: 'Coin Exchange',
    purchase_title: 'Purchase',
    tickets: 'Tickets',
    coins: 'Coins',
    balance: 'Balance',
    title: 'Store',
    tips: 'Tips',
    tips_1: '1. Our platform offers both free short dramas and premium early access dramas.',
    tips_2: '2. To watch early access content in advance, you need to redeem it by Tickets. These Tickets can be obtained through purchase, coin exchange, or by watching ads.',
    tips_3: '3. All tickets will never expire, but the viewing rights unlocked by Purchased Reward Tickets have an expiration date and will become invalid once expired.',
    tips_4: '4. Purchased Reward Tickets will be used first when unlocking episodes. If the balance is insufficient, your regular tickets will be used automatically.',
    tips_5: '5. If your top-up payment is successful but your balance has not been updated, please try refreshing your wallet.',
    tips_6: '6. For any other questions, feel free to contact us via "Profile" -> "Customer Service Center" -> "Feedback".',
    exchange_dialog_title: 'Are you sure you want to exchange?',
    redeem_required: 'Redeem Required',
    exchange_success: 'Successfully exchanged xxx Tickets',
    balance_not_enough: 'Balance not enough',
    exchange_failed: 'Exchange failed',
  },
  my_wallet_in: {
    transaction_history: 'Transaction History',
    EPnum: 'EP.{num}',
    Expired: 'Expired',
    AD: 'AD',
    Expires_on_num: 'Expires on {num}',
    Thats_all_for_now: 'That’s all for now',
    consumption_records: 'Consumption Records',
  },
  meal_check_in: {
    sleeping_link: 'Earn Coins While Sleeping',
    rules_title: 'Activity Rules',
    benefits_title: 'Benefits',
    rules_1: '1. Every day, there are four time slots for you to check in your meals: Breakfast: 07:00-10:00; Lunch: 11:00-14:00; Dinner: 17:00-20:00; Midnight Snack: 21:00-24:00.',
    rules_2: '2. If you miss the corresponding check-in time, you can watch a video to reclaim the reward.',
    rules_3: '3. Rewards reset every day at midnight, so don\'t forget to claim your rewards.',
  },
  sleep_check_in: {
    earn_link: 'Record meals to earn',
    benefits_title: 'Benefits',
    rules_1:
      '1. From 7 PM to 12 AM every night, you can activate the \'Sleep to Earn\' mode.',
    rules_2:
      '2. After activating it, the next day, after sleeping for 8 hours, you can claim the sleep reward between 8 AM and 12 PM.',
    rules_3:
      '3. If you miss the reward claim time, you can watch a video between 12 PM and 7 PM the next day to reclaim the reward.',
  },
}
