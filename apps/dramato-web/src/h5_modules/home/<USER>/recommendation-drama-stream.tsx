import { createComponent } from '@skynet/shared'
import { useHomePageStore } from '../use-home-page-store'
import RecommendationDramaCard from './recommendation-drama-card'
import InfinityScroll from 'src/h5_modules/common/infinity-scroll/infinity-scroll'
import { T } from '../home.i18n'
import { storeToRefs } from 'pinia'
type RecommendationDramaStreamOptions = {
  props: {}
}
export const RecommendationDramaStream = createComponent<RecommendationDramaStreamOptions>({
  props: {},
}, props => {
  const { dramaFeeds } = storeToRefs(useHomePageStore())
  return () => dramaFeeds.value && dramaFeeds.value?.length > 0
    ? (
        <x-recommendation-drama-stream class="">

          <x-title class="text-4.5 block pb-3 pt-4 font-bold text-white">{T.feeds.title()}</x-title>
          <x-recommendation-drama-stream-content class="grid grid-cols-2 gap-x-2">
            <x-recommendation-drama-stream-content class="flex flex-col gap-y-2">
              { dramaFeeds.value?.filter((i, index) => index % 2 === 0).map((drama, index) => <RecommendationDramaCard key={`RecommendationDramaCard-${drama.key}-${index}`} drama={drama} />)}
            </x-recommendation-drama-stream-content>
            <x-recommendation-drama-stream-content class="flex flex-col gap-y-2">
              { dramaFeeds.value?.filter((i, index) => index % 2 === 1).map((drama, index) => <RecommendationDramaCard key={`RecommendationDramaCard-${drama.key}-${index}`} drama={drama} />)}
            </x-recommendation-drama-stream-content>
          </x-recommendation-drama-stream-content>
          {/* </InfinityScroll> */}
        </x-recommendation-drama-stream>
      )
    : null
})

export default RecommendationDramaStream
