import { httpClient } from 'src/lib/http-client'
import { trim } from 'lodash-es'
const toNumber = (str: string) => {
  if (!trim(str)) return ''
  return +trim(str)
}

export const apiGetAiVoiceCharacterList = (data: Api.VoiceoverRole.ListReqParams) =>
  httpClient.post<ApiResponse<Api.VoiceoverRole.VoiceoverRoleListResp>>('/aivoice/aivoice_characters', data, {
    transformRequestData: {
      series_resource_id: [toNumber],
    },
    transformResponseData: {
      'data.character_list': [(arr: Api.VoiceoverRole.VoiceoverRoleItem[]) => {
        return arr.map(item => {
          item.role_labels = item.role_labels ? (item.role_labels as string).split(',') : []
          item.voice_labels = item.voice_labels ? (item.voice_labels as string).split(',') : []
          return item
        })
      }],
    },
  })

export const apiUpdateAiVoiceCharacter = (data: Api.VoiceoverRole.UpdateReqParams) =>
  httpClient.post<ApiResponse<null>>('/aivoice/character_timbre_edit', data)

export const apiUpdateCharacterFeature = (data: Api.VoiceoverRole.CharacterFeature) =>
  httpClient.post<ApiResponse<null>>('/aivoice/character_feature_edit', data)
