import { ref } from 'vue'
import { MyEarnings } from './my-earnings/my-earnings'
import { apiGetCoins, apiGetExchangeDetails, apiGetLastRedeemAccount, apiGetMyEarnings, apiGetMyExchange } from './coins-api'
import { ExchangeDetails, MyExchange } from './exchange/exchange'
import { bindLoading, when } from '@skynet/shared'
import { authInfo } from 'src/lib/get-auth'
import { useLoadingStore } from '../common/loading/use-loading-store'

const exchangePageParams = ref<ExchangeDetails>()

export const useCoinsStore = () => {
  const coins = ref<number>()
  const defaultMyEarnings = {
    list: [],
    page: 1,
    page_size: 10,
    has_more: false,
  }
  const gettingMyEarnings = ref(false)
  const gettingMyExchange = ref(false)
  const phone = ref('')

  const defaultMyExchange = {
    list: [],
    page: 1,
    page_size: 10,
    has_more: false,
  }

  const myEarnings = ref<{
    list: MyEarnings[]
    has_more: boolean
    page: number
    page_size: number
    last_id?: number
  }>(defaultMyEarnings)

  const myExchange = ref<{
    list: MyExchange[]
    has_more: boolean
    page: number
    page_size: number
    last_id?: number
  }>(defaultMyExchange)

  const getCoins = () => {
    void when(() => authInfo).then(async () => {
      const res = await apiGetCoins()
      if (!res.data) return
      coins.value = res.data.coins.amount
    })
  }

  const getMyEarnings = (page: number, page_size: number, last_id?: number) => {
    void when(() => authInfo).then(async () => {
      const res = await bindLoading(apiGetMyEarnings({ page, page_size, last_id }), gettingMyEarnings)
      if (!res.data || !res.data.gold_flow_list) return
      // 过滤掉myEarnings.value.list重复的id
      const uniqueList = res.data.gold_flow_list.filter(item => !myEarnings.value.list.some(t => t.id === item.id))
      myEarnings.value = {
        list: [...myEarnings.value.list, ...uniqueList],
        has_more: res.data?.has_more,
        page,
        page_size,
        last_id: res.data?.last_id, // 取最后一个id
      }
    })
  }

  const getMyExchange = (page: number, page_size: number, last_id?: number) => {
    void when(() => authInfo).then(async () => {
      const res = await bindLoading(apiGetMyExchange({ page, page_size, last_id }), gettingMyExchange)
      if (!res.data) return
      // 过滤掉myExchange.value.list重复的id
      const uniqueList = res.data.list.filter(item => !myExchange.value.list.some(t => t.id === item.id))
      myExchange.value = {
        list: [...myExchange.value.list, ...uniqueList],
        has_more: res.data?.has_more,
        page,
        page_size,
        last_id: res.data?.last_id, // 取最后一个id
      }
    })
  }

  const resetMyEarnings = () => {
    myEarnings.value = defaultMyEarnings
  }

  const resetMyExchange = () => {
    myExchange.value = defaultMyExchange
  }

  const { pageLoading } = useLoadingStore()

  const getExchangeDetails = () => {
    void when(() => authInfo).then(async () => {
      const res = await bindLoading(apiGetExchangeDetails(), pageLoading)
      if (!res.data) return
      exchangePageParams.value = res.data
    })
  }

  const getLastRedeemAccount = () => {
    void when(() => authInfo).then(async () => {
      const res = await bindLoading(apiGetLastRedeemAccount(), pageLoading)
      if (!res.data) return
      phone.value = res.data.account
    })
  }

  return {
    coins,
    gettingMyEarnings,
    gettingMyExchange,
    myEarnings,
    myExchange,
    exchangePageParams,
    getCoins,
    getMyEarnings,
    getMyExchange,
    resetMyEarnings,
    resetMyExchange,
    getExchangeDetails,
    getLastRedeemAccount,
    phone,
    pageLoading,
  }
}
