import { createComponent } from '@skynet/shared'
import { onMounted } from 'vue'
type ActivityTipsOptions = {
  props: {}
}
export const ActivityTips = createComponent<ActivityTipsOptions>({
  props: {},
}, props => {
  onMounted(() => {
    document.title = 'Unsubscribe'
  })
  return () => (
    <x-activity-tips class="size-100% flex flex-col items-center justify-center px-6 ">
      <x-title class="mb-8">
        <img
          src="https://static.mydramawave.com/frontend_static/email-images/dramawave-logo.png"
          alt="Dramawave Logo"
          width="160"
          height="35"
          style="display: block; margin: 0 auto; max-width: 160px; height: auto; border: 0; line-height: 100%; outline: none; -ms-interpolation-mode: bicubic;"
        />
      </x-title>
      <h1 class="text-center mb-2 text-5 text-[#0B080B]">Unsubscibe Successful!</h1>
      <p class="text-center text-[#434546] text-3.75 leading-5">You have been successfully unsubscribed from email communications.</p>
    </x-activity-tips>
  )
})

export default ActivityTips
