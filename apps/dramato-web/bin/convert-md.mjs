// 将bin/public目录下所有的md文件转换为html文件(任意层级)，存放在public目录(保留目录结构)
// 使用markdown-it将此文件由markdown转换为html字符串，记为content，读取md-template.html记为mdTemplate,
// 将mdTemplate中的__content__替换为content，将mdTemplate中的__title__替换为文件名（去掉后缀.md），首字母大写
// 在public目录下生成同名的html文件
import fs from 'fs/promises';
import path from 'path';
import MarkdownIt from 'markdown-it';

const md = new MarkdownIt();
// 这里的key要跟目录名一致
const map = {
  'en-US': {
    'issue': 'Frequently Asked Question',
    'privacy': 'Privacy Policy',
    'terms': 'Terms of Service'
  },
  'es-MX': {
    'issue': 'Preguntas Frecuentes',
    'privacy': 'Política de Privacidad',
    'terms': 'Términos de Servicio'
  },
  'ko-KR': {
    'issue': 'FAQ',
    'privacy': '개인정보 보호정책',
    'terms': '서비스 이용 약관'
  },
  'ja-JP': {
    'issue': 'よくある質問',
    'privacy': 'プライバシーポリシー',
    'terms': '利用規約'
  },
  'pt-PT': {
    'issue': 'Perguntas frequentes',
    'privacy': 'Política de privacidade',
    'terms': 'Termos de serviço'
  },
  'vi-VN': {
    'issue': 'Câu hỏi thường gặp',
    'privacy': 'Chính sách bảo mật',
    'terms': 'Điều khoản dịch vụ'
  },
  'th-TH': {
    'issue': 'คำถามที่พบบ่อย',
    'privacy': 'นโยบายความเป็นส่วนตัว',
    'terms': 'ข้อตกลงในการให้บริการ'
  },
  'id-ID': {
    'issue': 'Pertanyaan yang Sering Diajukan',
    'privacy': 'Kebijakan privasi',
    'terms': 'Ketentuan Layanan'
  },
  'tl-PH': {
    'issue': 'Madalas Itanong',
    'privacy': 'Patakaran sa Privacy',
    'terms': 'Mga Tuntunin ng Serbisyo'
  },
  'fr-FR': {
    'issue': 'Questions Posées Fréquemment',
    'privacy': 'Politique de confidentialité',
    'terms': 'Conditions d’Utilisation'
  },
  'de-DE': {
    'issue': 'Häufig gestellte Fragen',
    'privacy': 'Datenschutzrichtlinie',
    'terms': 'Nutzungsbestimmungen'
  },
  'it-IT': {
    'issue': 'Domande Frequenti',
    'privacy': 'Informativa sulla Privacy',
    'terms': "Condizioni d'Uso"
  },
  'ru-RU': {
    'issue': 'Часто Задаваемые Вопросы',
    'privacy': 'Политика Конфиденциальности',
    'terms': 'Условия Обслуживания'
  },
  'tr-TR': {
    'issue': 'Sıkça Sorulan Sorular',
    'privacy': 'Gizlilik Politikası',
    'terms': 'Hizmet Şartları'
  },
  'ms-MY': {
    'issue': 'Soalan Lazim',
    'privacy': 'Kebijakan Privasi',
    'terms': 'Syarat Perkhidmatan'
  },
  'zh-TW': {
    'issue': '常见问题',
    'privacy': '隱私政策',
    'terms': '使用條款'
  },
  'zh-CN': {
    'issue': '常见问题',
    'privacy': '隐私政策',
    'terms': '使用条款'
  },
  'hi-IN': {
    'issue': 'सामान्य प्रश्न',
    'privacy': 'गोपनीयता नीति',
    'terms': 'सेवा की शर्तें'
  }
}

async function getMarkdownFiles(dir) {
  let files = [];
  const entries = await fs.readdir(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      const subFiles = await getMarkdownFiles(fullPath);
      files = files.concat(subFiles);
    } else if (entry.isFile() && path.extname(entry.name) === '.md') {
      files.push(fullPath);
    }
  }
  return files;
}

async function processMarkdownFiles() {
  const baseDir = 'bin/public';
  const outputDir = 'public';
  const mdTemplatePath = 'bin/md-template.html';
  const mdTemplate = await fs.readFile(mdTemplatePath, 'utf-8');

  const mdFiles = await getMarkdownFiles(baseDir);

  for (const mdFilePath of mdFiles) {
    const relativePath = path.relative(baseDir, mdFilePath);
    const outputFilePath = path.join(outputDir, relativePath.replace(/\.md$/, '.html'));

    const content = await fs.readFile(mdFilePath, 'utf-8');
    const htmlContent = md.render(content);
    const fileName = path.basename(mdFilePath, '.md');
    // 根据map和文件路径生成标题
    // 获取文件上一级文件名，作为map的键，获取文件名去掉md作为map的值的键
    const title = map[path.dirname(mdFilePath).split('/rules/')[1] ?? 'en']?.[fileName];

    const finalHtml = mdTemplate.replace('__content__', htmlContent).replace('__title__', title);

    await fs.mkdir(path.dirname(outputFilePath), { recursive: true });
    await fs.writeFile(outputFilePath, finalHtml, 'utf-8');
  }
}

processMarkdownFiles();