import { isFacebook, isIos } from './ua'

let timer: string | number | undefined

/**
 * 检测应用是否安装
 * 唤端成功后，打开app并跳转到对应的详情页
 * 如果唤端失败，2s后执行fallback回调函数
 * @param urlScheme 应用 URL Scheme
 * @param fallback 应用未安装时的回调函数
 */
export const goToApp = (urlScheme: string) => {
  const googlePlayLink = 'https://play.google.com/store/apps/details?id=com.dramawave.app'
  if (isIos()) {
    // iOS使用Universal Link
    window.location.href = urlScheme
  } else {
    // facebook直接打开google play
    if (isFacebook()) {
      window.location.href = googlePlayLink
    } else {
      window.location.href = urlScheme
      timer = window.setTimeout(() => {
        window.location.href = googlePlayLink
      }, 3000)

      // 监听页面的 visibilitychange 事件
      // 监听页面的可见性变化
      document.addEventListener('visibilitychange', handleVisibilityChange)
    }
  }
}

const handleVisibilityChange = () => {
  clearTimeout(timer) // 如果页面变为可见，清除定时器
}
