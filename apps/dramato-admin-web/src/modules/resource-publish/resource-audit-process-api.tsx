import { httpClient } from 'src/lib/http-client'
import dayjs from 'dayjs'
import { checkResourceTitleOrId, checkEpisodeTitleOrId } from './util'

const dateFormat = (time: number) => {
  if (!time) return '-'
  return dayjs(time * 1000).format('YYYY-MM-DD HH:mm')
}

export const apiGetResourceAuditProcess = (data: Api.ResourceAuditProcess.Param) =>
  httpClient.post<ApiResponse<{
    list: M.IResourceAuditProcess.AuditList
    total: number
  }>>('/resource_stat/resource_audit_list', data, {
    transformRequestData: {
      series_key_or_title_list: [checkEpisodeTitleOrId],
      resource_id_or_title_list: [checkResourceTitleOrId],
    },
    transformResponseData: {
      'data.list': [(list: M.IResourceAuditProcess.AuditItem[]) => {
        return list.map(item => {
          item.entering_time =  item.entering_time ? dateFormat(item.entering_time as number) : '-'
          item.check_time =  item.check_time ? dateFormat(item.check_time as number) : '-'
          item.info_check_time =  item.info_check_time ? dateFormat(item.info_check_time as number) : '-'
          item.final_check_time = item.final_check_time ? dateFormat(item.final_check_time as number) : '-'

          item.publish_compound_check_user = item.publish_compound_check_user || []
          item.shelve_user = item.shelve_user || []
          item.subtitle_check_user = item.subtitle_check_user || []

          item.subtitle_check_time = item.subtitle_check_time?.map(item => {
            return {
              ...item,
              time: item.time ? dateFormat(item.time as number) : '-'
            }
          }) || []

          item.publish_source_check_time = item.publish_source_check_time?.map(item => {
            return {
              ...item,
              time: item.time ? dateFormat(item.time as number) : '-'
            }
          }) || []

          item.publish_compound_check_time = item.publish_compound_check_time?.map(item => {
            return {
              ...item,
              time: item.time ? dateFormat(item.time as number) : '-'
            }
          }) || []

          item.shelve_time = item.shelve_time?.map(item => {
            return {
              ...item,
              time: item.time ? dateFormat(item.time as number) : '-'
            }
          }) || []
          return item
        })
      }],
    },
  })

export const apiUploadBatchResource = (data: {
  file: File
}) =>
  httpClient.post<ApiResponse<null>>('/resource_stat/business_batch_replace', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
