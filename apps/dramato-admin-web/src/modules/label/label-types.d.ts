declare namespace Api {
  namespace Label {
    type LabelType = 1 | 2 | 3 | number
    type ContentType = 1 | 2 | 3 | 4 | number
    type LangContent = {
      language_code: string
      content: string | undefined
      meaning: string | undefined
    }

    interface Param {
      page_index: number
      page_size: number
      content?: string
      label_type?: LabelType // 标签级别  1 一级 2 二级 3 三级
      content_type?: ContentType // 内容维度 1 受众 2 类型 3 情节 4 角色
      language_code?: string
    }

    namespace Response {
      type List = ApiResponse<{ list: Item[], total: number }>
    }

    interface Item {
      label_id?: number
      label_type?: LabelType // 标签级别  1 一级 2 二级 3 三级
      content_type?: ContentType // 内容维度 1 受众 2 类型 3 情节 4 角色
      lang_content?: LangContent[]
      resource_count?: number
      content?: string
      meaning?: string
      [key: string]: string | number | undefined | LangContent[]
    }
  }
}
