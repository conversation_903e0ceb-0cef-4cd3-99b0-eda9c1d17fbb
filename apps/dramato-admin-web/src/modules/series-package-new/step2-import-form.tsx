import { createComponent, mc } from '@skynet/shared'
import { useSeriesPackageAddFormStore } from './use-series-package-add-form-store'
import { set } from 'lodash-es'
import { transformStringArray } from '@skynet/ui'
import { requiredLabel } from 'src/lib/required-label'
type Step2ImportFormOptions = {
  props: {}
}
export const Step2ImportForm = createComponent<Step2ImportFormOptions>({
  props: {},
}, props => {
  const {
    formData,
    Form,
    curTab,
    twoStepError,
  } = useSeriesPackageAddFormStore()

  return () => (
    <Form
      class="flex flex-1 flex-col gap-y-3"
      hasAction={false}
      error={twoStepError.value}
      onChange={(path, value) => {
        set(formData.value || {}, path, value)
      }}
      items={[
        [
          requiredLabel('短剧ID：多个ID之间用英文逗号分隔'),
        `package_config.package_type_1_config.${curTab.value === 'ios_config' ? 'ios' : 'android'}_series_keys`,
        {
          type: 'textarea',
          placeholder: '请输入短剧ID（series_key）',
          rows: 3,
          disabled: formData.value.needDisabled,
        },
        {
          class: mc('col-span-1'),
          transform: transformStringArray,
        },
        ],
      ]}
      data={formData.value}
    />
  )
})

export default Step2ImportForm
