/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, showFailToast } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetIncomeList } from './resource-income-api'
import dayjs from 'dayjs'
import { get_k_sso_token } from 'src/lib/device-id'

const formatNumber = (num: number) => {
  return `${(num).toFixed(2)}`
}

export const useResourceIncome = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    onQuery,
    onReset,
    columns,
    total,
    onPageChange,
    onPageSizeChange,
    updateTime,
    getList,
    handleExport,
    isExporting,
    defaultParams
  }
}

const defaultParams = {
  page_index: 1,
  page_size: 20,
  month: dayjs().subtract(1, 'month').toDate()
}

const Form = CreateForm<Api.ResourceIncome.Params>()
const params = ref<Api.ResourceIncome.Params>({...defaultParams})
const total = ref<number>(0)
const isExporting = ref<boolean>(false)

const Table = CreateTableOld<M.IResIncomeItem>()
const list = ref<M.IResIncomeItem[]>([])
const loading = ref<boolean>(false)
const updateTime = ref('')

interface ColumnType {
  prop: string
  label: string
  minWidth?: number
  fixed?: boolean
  sortable?: 'custom' | null
  sortOrders?: ('ascending' | 'descending' | null)[]
  width?: number
  render?: (params: { row: any }) => any
}

const columns = ref<ColumnType[]>([
  { prop: 'res_id', label: '资源ID', minWidth: 120, fixed: true },
  { prop: 'partner_name', label: '合作方', minWidth: 140 },
  { prop: 'res_title', label: '资源名称', minWidth: 200 },
  { prop: 'play_pv', label: '累计播放', minWidth: 140, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'play_duration', label: '累计播放时长/分', minWidth: 160, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'play_finish_ration', label: '完播率', minWidth: 140, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'follow_cnt', label: '累计关注人数', minWidth: 140, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'all_revenue', label: '总收入', minWidth: 140, sortable: 'custom', sortOrders: ['descending', null] },
  // { prop: 'pay_revenue', label: '剧集收入', minWidth: 140, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'subscribe_pay_revenue', label: '订阅收入', minWidth: 140, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'recharge_distribute_revenue', label: '充值摊销收入', minWidth: 140, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'subscribe_distribute_revenue', label: '续订摊销收入', minWidth: 140, sortable: 'custom', sortOrders: ['descending', null] },
  { prop: 'ad_revenue', label: '广告收入', minWidth: 140, sortable: 'custom', sortOrders: ['descending', null] },
])

const onReset = () => {
  params.value = {...defaultParams}
  void onQuery()
}

const exportExcel = (data: any) =>
  fetch(`${import.meta.env.VITE_DRAMA_API_URL}/resincome/month_export`, {
    method: 'post',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      Device: 'Web',
      Token: get_k_sso_token() || '',
    },
    body: JSON.stringify(data),
  })

const handleExport = async () => {
  try {
    isExporting.value = true
    try {
      const blob =  await exportExcel({
        ...params.value,
        month: +dayjs(params.value.month).format('YYYYMM')
      }).then(response => response.blob())
      if (blob.size < 20) {
        throw new Error('导出失败')
      }
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `收入分账汇总_${dayjs(params.value.month).format('YYYYMM')}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error: any) {
      showFailToast('导出失败')
    }
  } catch (error: any) {
    showFailToast(error.response.data.message || '导出失败')
  } finally {
    isExporting.value = false
  }
}

const getList = async () => {
  try {
    loading.value = true
    const res = await apiGetIncomeList({
      ...params.value,
      month: +dayjs(params.value.month).format('YYYYMM')
    })
    list.value = res.data?.list.map(item => {
      return {
        ...item,
        play_duration: formatNumber(Number(item.play_duration || 0)),
        all_revenue: formatNumber(Number(item.all_revenue || 0)),
        pay_revenue: formatNumber(Number(item.pay_revenue || 0)),
        ad_revenue: formatNumber(Number(item.ad_revenue || 0)),
        subscribe_pay_revenue: formatNumber(Number(item.subscribe_pay_revenue || 0)),
        recharge_distribute_revenue: formatNumber(Number(item.recharge_distribute_revenue || 0)),
        subscribe_distribute_revenue: formatNumber(Number(item.subscribe_distribute_revenue || 0)),
        play_finish_ration: item.play_finish_ration ? `${(((item.play_finish_ration as number) || 0) * 100).toFixed(2)}%` : '-'
      }
    }) || []
    total.value = res.data?.total || 0
    updateTime.value = res.data?.last_update_time || ''
  } catch (error: any) {
    list.value = []
    showFailToast(error.response.data.message || '获取资源收入列表失败')
  } finally {
    loading.value = false
  }
}

const onPageChange = (page: number) => {
  params.value.page_index = page
  void getList()
}

const onPageSizeChange = (size: number) => {
  params.value.page_size = size
  params.value.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_index = 1
  void getList()
}
