import { httpClient } from 'src/lib/http-client'
import { checkResourceTitleOrId, checkEpisodeTitleOrId } from './util'

const dateCheck = (value?: number) => {
  if (!value) return 0
  return value
}

export const apiGetInfoModifyList = (data: Api.InfoModify.Request) =>
  httpClient.post<Api.InfoModify.Response>('/series_resource_v2/info_list', data, {
    transformRequestData: {
      create_start_time: [dateCheck],
      create_end_time: [dateCheck],
      series_key_or_title_list: [checkEpisodeTitleOrId],
      resource_id_or_title_list: [checkResourceTitleOrId],
    },
  })

export const apiGetColors = () =>
  httpClient.post<ApiResponse<{
    list: M.IColor[]
  }>>('/series_resource_v2/color_style_list')
