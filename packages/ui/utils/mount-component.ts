import { createApp, reactive, Component } from 'vue'
import { extend } from './basic'
import { useExpose } from '../use/use-expose'

export function usePopupState() {
  const state = reactive<{
    show: boolean
    [key: string]: unknown
  }>({
    show: false,
  })

  const toggle = (show: boolean) => {
    state.show = show
  }

  const open = (props: Record<string, unknown>) => {
    extend(state, props, { transitionAppear: true })
    toggle(true)
  }

  const close = () => toggle(false)

  useExpose({ open, close, toggle })

  return {
    open,
    close,
    state,
    toggle,
  }
}

export function mountComponent(RootComponent: Component) {
  const app = createApp(RootComponent)
  const root = document.createElement('div')

  const mountRoot = document.getElementById('mountRoot')
  const container = mountRoot || document.body
  container.appendChild(root)

  return {
    instance: app.mount(root),
    unmount: () => {
      app.unmount()
      document.body.removeChild(root)
    },
  }
}
