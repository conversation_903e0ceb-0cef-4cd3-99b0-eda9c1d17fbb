import { createComponent } from '@skynet/shared'
import { SvgIcon } from '@skynet/ui'
import { FunctionalComponent, onMounted, ref, toRaw, watch } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import s from './left-aside.module.css'
import { useMenu } from 'src/modules/menu/use-menu.tsx'

type LeftAsideOptions = {
  props: {}
}
export const LeftAside = createComponent<LeftAsideOptions>({
  props: {},
}, () => {
  const router = useRouter()
  const currentFullPath = ref('')

  const highlightMenu = () => {
    if (!aside.value) return
    // const target = aside.value.querySelector('.router-link-active') as HTMLDivElement
    // console.log(target);
    
    // if (!target) return
    // const top = target.offsetTop - aside.value.offsetTop
    // aside.value.scrollTo({ top, behavior: 'smooth' })
    currentFullPath.value = toRaw(router).currentRoute.value.fullPath
    leftSideMenu.value = expandMenu(leftSideMenu.value); // 选中菜单展开
  }
  router.afterEach(() => {
    setTimeout(() => highlightMenu(), 100)
  })
  const aside = ref<HTMLElement>()
  onMounted(() => {
    highlightMenu()
    // setTimeout(() => {
    //   if (!aside.value) return
    //   const target = aside.value.querySelector('.router-link-active') as HTMLDivElement
    //   console.log(target);
    //   if (!target) return
    //   const top = target.offsetTop - aside.value.offsetTop
    //   console.log(target.clientTop, aside.value.clientHeight);
      
    //   console.log(top);
      
    //   aside.value.scrollTo({ top, behavior: 'smooth' })
    // }, 3000)
  })
  
  const expandMenu = (arr: M.IMenuItem[]) => {
    arr.forEach(row => {
      if (row.children && row.children.length > 0)
        row.children = expandMenu(row.children)
    })
    arr.forEach(row => {
      if (row.path == currentFullPath.value)
        leftSideMenu.value.forEach((i, index) => {
          if (row.parent_id === i.id) {
            i.current_menu = 1 // 数组的上一级
          }
        })
    })
    return arr
  }

  const { getMenuInfo, leftSideMenu } = useMenu()

  watch(() => leftSideMenu.value, () => {
    leftSideMenu.value = expandMenu(leftSideMenu.value); // 选中菜单展开
  }, { deep: true })

  // 获取菜单
  void getMenuInfo()
  const renderNavMenu = (menuList: M.IMenuItem[]) => {
    return menuList?.map(row => {
      return row.children && row.children.length > 0
        ? (
            <>
              <ul class="menu rounded-box">
                <li class="w-full">
                  {row.current_menu === 1
                    ? (
                        <>
                          <details open class="w-full">
                            <summary class="py-3 !pl-2 hover:bg-[#434A50] [&.router-link-active]:bg-[#434A50] [&.router-link-active]:text-white"><Title>{row.title}</Title></summary>
                            <ol class="bg-[#454c53] rounded-[8px]">
                              {
                                row.children.map(item => <Item path={item.path!} title={item.title!}>{item.title}</Item>)
                              }
                            </ol>
                          </details>
                        </>
                      )
                    : (
                        <>
                          <details class="w-full">
                            <summary class="py-3 !pl-2 hover:bg-[#434A50] [&.router-link-active]:bg-[#434A50] [&.router-link-active]:text-white"><Title>{row.title}</Title></summary>
                            <ol class="bg-[#454c53] rounded-[8px]">
                              {
                                row.children.map(item => <Item path={item.path!} title={item.title!}>{item.title}</Item>)
                              }
                            </ol>
                          </details>
                        </>
                      )}
                </li>
              </ul>
            </>
          )
        : null
    })
  }
  return () => (
    <x-left-aside ref={aside} class={['block bg-[#545c64] text-[#fff] h-full sticky left-0 top-0 overflow-auto min-w-[12em] scrollbar-none', s.wrapper]}>
      <nav>
        {leftSideMenu.value && leftSideMenu.value.length > 0
          ? renderNavMenu(leftSideMenu.value)
          : (
              <div class="flex justify-center pt-20">
                <SvgIcon class="loading loading-spinner size-4" name="ic_loading" />
              </div>
            )}
      </nav>
    </x-left-aside>
  )
})

const Item: FunctionalComponent<{ path: string, title: string }> = (props, { slots }) => {
  return (
    <li title={(props.title)?.toString()}>
      <RouterLink
        class="block w-full py-3 px-2 truncate hover:bg-[#434A50] focus:text-white focus:bg-transparent cursor-pointer
          [&.router-link-active]:text-[#ffd04b]
        "
        to={props.path}
      >
        {slots.default?.()}
      </RouterLink>
    </li>
  )
}
const Title: FunctionalComponent = (_props, { slots }) => {
  return <h2 class="text-white font-medium text-[16px]">{slots.default?.()}</h2>
}
