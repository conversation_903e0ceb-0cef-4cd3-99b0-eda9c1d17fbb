/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Button, MergeClass } from '@skynet/ui'
import { useSeriesPackageAddFormStore } from './use-series-package-add-form-store'
import { useAppAndLangOptions } from '../options/use-app-options'
import Step2ImportForm from './step2-import-form'
import Step2CommonForm from './step2-common-form'
import Step2SearchImportForm from './step2-search-import-form'
import Step2BatchUploadForm from './step2-batch-upload-form'
import Step1Form from './step1-form'
import { watch } from 'vue'
import { useSeriesPackageStore } from './use-series-package-store'
import SeriesPackageBox from './series-package-box'

const navList = [
  ['目标条件', 'target'],
  ['剧包配置', 'product'],
]

type SeriesPackageAddFormOptions = {
  props: {}
}
export const SeriesPackageAddForm = createComponent<SeriesPackageAddFormOptions>({
  props: {},
}, props => {
  const {
    formData,
    curTab,
    curStep,
    onUpStepClick,
    onDownStepClick,
    onSubmit,
    resetOneForm,
    resetTwoForm,
    appId,
    checkedEpisodeItem,
    isSubmitting,
    closeAddDialog,
  } = useSeriesPackageAddFormStore()

  const { isCommon } = useSeriesPackageStore()

  const { appOptions } = useAppAndLangOptions(() => undefined, {
    onSuccess: () => {
      const value = appOptions.value
        ?.find(i => i.label === ([0, 1].includes(formData.value?.user_platform || 0) ? 'Dramawave_iOS' : 'Dramawave_Android'))?.value || 0
      appId.value = value
    },
  })

  watch(() => formData.value.user_platform, v => {
    curTab.value = [0, 1].includes(formData.value?.user_platform || 0) ? 'ios_config' : 'android_config'
    const key = curTab.value === 'ios_config' ? 'Dramawave_iOS' : 'Dramawave_Android'
    appId.value = appOptions.value?.find(i => i.label === key)?.value || 0
    console.log('>>> 2')
  }, { deep: true, immediate: true })

  //
  return () => (
    <x-series-package-add-form class="flex h-[800px] w-full flex-col overflow-hidden">
      <div class="relative flex  flex-1 items-center justify-end divide-x overflow-hidden">
        {isCommon.value ? (
          <aside class="flex h-full min-h-40 w-40 shrink-0 grow-0 flex-col items-center justify-start bg-white py-10"
          >
            <x-list class="block">
              {navList.map(([name], i) => (
                [
                  i !== 0
                  && <hr class="ml-4 h-10 w-0 border-y-0 border-l-0 border-r border-solid border-gray-500" />,
                  <MergeClass tag="x-item" baseClass="flex items-center gap-2">
                    <span class={mc('size-8 bg-blue-500 rounded-full flex items-center justify-center text-white',
                      curStep.value === i + 1 ? 'bg-red-600' : '')}
                    >
                      {i + 1}
                    </span>

                    {name}
                  </MergeClass>,
                ]
              )).flat()}
            </x-list>
          </aside>
        ) : null }
        <main class="grow-1  shrink-1 size-full overflow-y-auto bg-white px-4">
          {
            isCommon.value && curStep.value === 1 && <Step1Form />
          }

          { isCommon.value && curStep.value === 2 && formData.value.import_type !== 4 && (
            <div role="tablist" class="tab-sm tabs-boxed tabs mb-4 flex gap-x-2 bg-white">
              {
                [
                  [0, 1].includes(formData.value.user_platform || 0) ? 'ios_config' : '',
                  [0, 2].includes(formData.value.user_platform || 0) ? 'android_config' : '',
                ].filter(i => !!i).map(item => (
                  <span
                    role="tab"
                    onClick={() => {
                      curTab.value = item
                      checkedEpisodeItem.value = []
                      console.log('appOptions.value', appOptions.value, item)
                      const key = item === 'ios_config' ? 'Dramawave_iOS' : 'Dramawave_Android'
                      console.log('>>> key', key)

                      appId.value = appOptions.value?.find(i => i.label === key)?.value || 0
                    }}
                    class={mc('tab bg-gray-300 text-black', curTab.value === item ? 'tab-active' : '')}
                  >
                    {{ ios_config: 'iOS配置', android_config: 'Android配置' }[item]}
                  </span>
                ))
              }
            </div>
          )}

          {
            isCommon.value && curStep.value === 2 && formData.value.import_type === 1 && <Step2ImportForm />
          }

          {
            isCommon.value && curStep.value === 2 && formData.value.import_type === 2 && <Step2BatchUploadForm />
          }

          {
            isCommon.value && curStep.value === 2 && formData.value.import_type === 3 && <Step2SearchImportForm />
          }

          {
            isCommon.value && curStep.value === 2 && formData.value.import_type === 4 && <Step2CommonForm />
          }

          {
            !isCommon.value && <SeriesPackageBox />
          }

        </main>
      </div>
      <div class="btn-group flex w-full items-center justify-end gap-3 px-5 py-4">
        {
          isCommon.value && curStep.value === 1 && (
            <>
              {formData.value.needDisabled ? null : (
                <Button class="btn btn-outline btn-sm"
                  onClick={resetOneForm}>
                  重置
                </Button>
              ) }
              <Button class="btn btn-primary btn-sm"
                onClick={onDownStepClick}>
                下一步
              </Button>
            </>
          )
        }
        {
          isCommon.value && curStep.value === 2 && (
            <>

              <Button class="btn btn-outline btn-sm"
                onClick={onUpStepClick}>
                上一步
              </Button>
              {formData.value.needDisabled ? null : (
                <>
                  <Button class="btn btn-outline btn-sm"
                    onClick={resetTwoForm}>
                    重置
                  </Button>
                  <Button class="btn btn-primary btn-sm"
                    onClick={onSubmit} disabled={isSubmitting.value}>
                    {isSubmitting.value ? '提交中...' : '提交'}
                  </Button>
                </>
              )}
            </>
          )
        }
        {
          !isCommon.value && (
            <>
              <Button class="btn btn-outline btn-sm"
                onClick={() => closeAddDialog.value()}>
                取消
              </Button>
              <Button class="btn btn-primary btn-sm"
                onClick={onSubmit} disabled={isSubmitting.value}>
                {isSubmitting.value ? '提交中...' : '提交'}
              </Button>
            </>
          )
        }
      </div>
    </x-series-package-add-form>
  )
})

export default SeriesPackageAddForm
