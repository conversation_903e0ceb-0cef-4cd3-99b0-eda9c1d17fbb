import { createComponent, removeIndent } from '@skynet/shared'
import markdownIt from 'markdown-it'
import { MergeClass } from '../ui'
import { ref, watch } from 'vue'

type MarkdownOptions = {
  props: {
    content: string
  }
}
export const md = markdownIt({
  breaks: true,
})
export const Markdown = createComponent<MarkdownOptions>({
  props: {
    content: '',
  },
}, props => {
  const html = ref('')

  watch(() => props.content, () => html.value = md.render(removeIndent(props.content)), {
    immediate: true,
    deep: true,
  })
  return () => (
    <MergeClass v-html={html.value} />
  )
})
