/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { CreateForm, Icon, showFailToast, showSuccessToast, transformBool2, transformNumber } from '@skynet/ui'
import { computed, ref, watch } from 'vue'
import { z } from 'zod'
import { GENDERS, AGES, TIMBRE_FEATURE } from 'src/modules/voiceover-role/constant'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { useRoute } from 'vue-router'
import { apiUpdateOriginalTerminology } from 'src/modules/resource-publish/resource-publish-api'
import { ElSelect, ElOption } from 'element-plus'
type TermEditDialogOptions = {
  props: {
    term?: M.ITerminology
  }
  emits: {
    hideDialog: () => void
    termSave: () => void
  }
}
export const TermEditDialog = createComponent<TermEditDialogOptions>({
  props: {
    term: {},
  },
  emits: {
    hideDialog: () => {},
    termSave: () => {}
  },
}, (props, { emit }) => {
  const route = useRoute()
  const btnLoading = ref(false)
  const Form = CreateForm<M.ITerminology>()

  const formRules = z.object({
    term: z.string().min(1, '请填写术语名称'),
    age: z.string().min(1, '请选择年龄'),
    gender: z.number().min(0, '请选择性别'),
    voice_labels: z.array(z.string()).min(1, '请选择'),
    role_labels: z.array(z.string()).min(1, '请选择'),
  })

  const form = ref<M.ITerminology & { isCharacter?: boolean }>({
    voice_labels: [],
    role_labels: [],
    new_character_sync_term: 0,
    isCharacter: true,
    category: 'character'
  })

  const isEditable = computed(() => {
    return !!form.value.term_id || !!form.value.character_id
  })

  watch(() => props.term, () => {
    if (props.term.character_id || props.term.term_id) {
      form.value = {
        ...props.term,
        voice_labels: props.term.voice_labels ? (props.term.voice_labels as string).split(',') : [],
        role_labels: props.term.role_labels ? (props.term.role_labels as string).split(',') : [],
        new_character_sync_term: props.term.new_character_sync_term || 0,
        isCharacter: !!props.term.character_id,
      }
    }
  }, {
    immediate: true,
  })

  const roleLabelOptions = ref<{
    value: string
    label: string
  }[]>(GENDERS.find(item => item.value === form.value.gender)?.roles || [])
  const { error, validateAll } = useValidator(form, formRules)

  return () => (
    (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>
          <Form
            data={form.value}
            class="flex-col"
            error={error.value}
            onChange={(path, value: any) => {
              if (path === 'gender') {
                roleLabelOptions.value = GENDERS.find(item => item.value === value)?.roles || []
                form.value.role_labels = []
              }
              if (path === 'isCharacter') {
                if (value) {
                  set(form.value, 'category', 'character')
                } else {
                  set(form.value, 'category', '')
                }
              }
              if (path === 'category' && value === 'character') {
                set(form.value, 'isCharacter', true)
              }
              set(form.value, path, value)
            }}
            actions={[]}
            items={[
              [
                requiredLabel('是否角色'),
                'isCharacter',
                {
                  type: 'select',
                  disabled: isEditable.value,
                  options: [{
                    value: false,
                    label: '否',
                  }, {
                    value: true,
                    label: '是',
                  }],
                  autoInsertEmptyOption: false,
                },
                {
                  class: 'col-span-1',
                  transform: transformBool2,
                },
              ],
              [
                requiredLabel('术语类型'),
                'category',
                {
                  type: 'text',
                  disabled: isEditable.value || form.value.category === 'character',
                },
                {
                  class: 'col-span-1',
                },
              ],
              [
                requiredLabel('术语名称'),
                'term',
                {
                  type: 'text',
                },
                {
                  class: 'col-span-1',
                },
              ],
              [
                '官方名',
                'official_name',
                {
                  type: 'text',
                },
                {
                  class: mc('col-span-1', !form.value.isCharacter ? 'hidden' : ''),
                },
              ],
              [
                requiredLabel('年龄'),
                'age',
                {
                  type: 'select',
                  options: AGES,
                },
                {
                  class: mc('col-span-1', !form.value.isCharacter ? 'hidden' : ''),
                },
              ],
              [
                requiredLabel('性别'),
                'gender',
                {
                  type: 'select',
                  options: GENDERS,
                },
                {
                  class: mc('col-span-1', !form.value.isCharacter ? 'hidden' : 'flex'),
                  transform: transformNumber,
                },
              ],
              [
                requiredLabel('角色人设'),
                'role_labels',
                {
                  type: 'custom',
                  render: () => (
                    <ElSelect
                      v-model={form.value.role_labels}
                      multiple
                      placeholder='请选择角色人设'
                      clearable
                    >
                      {roleLabelOptions.value.map((item, index) => {
                        return <ElOption key={item.value} value={item.value} label={item.label} />
                      })}
                    </ElSelect>
                  ),
                },
                {
                  class: mc('col-span-1', !form.value.isCharacter ? 'hidden' : 'flex'),
                },
              ],
              [
                requiredLabel('音色特征'),
                'voice_labels',
                {
                  type: 'custom',
                  render: () => (
                    <ElSelect
                      v-model={form.value.voice_labels}
                      multiple
                      placeholder='请选择音色特征'
                      clearable
                    >
                      {TIMBRE_FEATURE.map((item, index) => {
                        return <ElOption key={item.value} value={item.value} label={item.label} />
                      })}
                    </ElSelect>
                  ),
                },
                {
                  class: mc('col-span-1', !form.value.isCharacter ? 'hidden' : 'flex'),
                },
              ],
              [
                requiredLabel('同步术语'),
                'new_character_sync_term',
                {
                  type: 'radio',
                  options: [
                    { label: '同步', value: 1 },
                    { label: '不同步', value: 0 },
                  ],
                },
                {
                  class: mc('col-span-1', !form.value.isCharacter || form.value.character_id || form.value.term_id ? 'hidden' : 'flex'),
                  hint: () => (
                    <div>
                      <div>
                        若新增的角色术语在全剧字幕中曾出现过，需要点“同步”；
                      </div>
                      <div>
                        若为自创角色（例如：路人1、女配1等），则保持“不同步”
                      </div>
                    </div>
                  )
                },
              ],
            ]}
          />
        </x-status-body>
        <x-status-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => emit('hideDialog')}>取消</button>
          <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
            const exclude: string[] = []
            if (!form.value.isCharacter) {
              exclude.push('age')
              exclude.push('gender')
              exclude.push('voice_labels')
              exclude.push('role_labels')
              form.value.type === 2
            } else {
              form.value.type === 1
            }
            if (!validateAll({
              exclude
            })) return
            try {
              btnLoading.value = true
              await apiUpdateOriginalTerminology({
                series_resource_id: +route.params.id,
                terms: [{
                  ...form.value,
                  type: form.value.isCharacter ? 1 : 2,
                  voice_labels: (form.value.voice_labels as string[]).join(','),
                  role_labels: (form.value.role_labels as string[]).join(','),
                }],
              })
              emit('termSave')
              showSuccessToast('操作成功')
            } catch (error: any) {
              showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
            } finally {
              btnLoading.value = false
            }
          }}
          >
            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
            保存
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    )
  )
})

export default TermEditDialog
