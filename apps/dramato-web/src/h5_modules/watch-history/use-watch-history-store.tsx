import { ref } from 'vue'
import { watchHistoryApi } from './watch-history-api'

const dramaFeeds = ref<M.Drama[]>([])
const dramaFeedsNext = ref<string>('offset=0')
const hasMoreDramaFeeds = ref<boolean>(true)
const loadingDramaFeeds = ref<boolean>(false)
const mainRef = ref()
const isFirstLoading = ref<boolean>(true)

/** 获取首页- tab-Feed */
const getDramaFeeds = async (init = false) => {
  if (!hasMoreDramaFeeds.value) return
  if (loadingDramaFeeds.value) return
  loadingDramaFeeds.value = true
  const res = await watchHistoryApi.getList({ next: dramaFeedsNext.value })
  isFirstLoading.value = false

  if (init) {
    dramaFeeds.value = []
  }

  dramaFeeds.value.push(...(res.data?.items || []).filter(item => !!item.id))
  dramaFeedsNext.value = res.data?.page_info.next || ''
  hasMoreDramaFeeds.value = res.data?.page_info.has_more || false
  loadingDramaFeeds.value = false

  if (init) {
    void getDramaFeeds()
  }
}

export const useWatchHistoryStore = () => {
  return {
    mainRef,
    dramaFeeds,
    getDramaFeeds,
    loadingDramaFeeds,
    dramaFeedsNext,
    hasMoreDramaFeeds,
    isFirstLoading,
  }
}
