
import { httpClient } from 'src/lib/http-client'

export const apiGetRechargeLevels = (data: M.FreeRechargeLevelSearchParams) =>
  httpClient.post<ApiResponse<{
    items: M.FreeRechargeLevel[]
    total: number
    page_info: {
      next: string
      has_more: boolean
    }
  }>>('/wallet_free/product/recharge/list', data)

export const apiCreateRechargeLevel = (data: M.FreeRechargeLevel) =>
  httpClient.post<ApiResponse<{
    items: M.FreeRechargeLevel[]
    total: number
    page_info: {
      next: string
      has_more: boolean
    }
  }>>('/wallet_free/product/create', data)

export const apiEditRechargeLevel = (data: M.FreeRechargeLevel) =>
  httpClient.post<ApiResponse<{
    items: M.FreeRechargeLevel[]
    total: number
    page_info: {
      next: string
      has_more: boolean
    }
  }>>('/wallet_free/product/edit', data)

export const apiUpdateRechargeLevelStatus = (data: { id: number, status: number }) =>
  httpClient.post<ApiResponse<null>>('/wallet_free/product/status', data)

export const apiUpdateRechargeLevelPriority = (data: { id: number, priority: number }) =>
  httpClient.post<ApiResponse<null>>('/wallet_free/product/priority', data)
