declare namespace Api {
  namespace AuditScript {
    namespace Request {
      interface Params {
        page_index: number
        page_size: number
        title_or_key_list: string | string[] | number[]
        openplatform_principal: string
        audit_status: number // 审核状态 1 一审待审核 2 一审拒绝 3 一审放弃 4 一审通过 5 二审待审核 6 二审拒绝 7 二审放弃 8 二审通过 9 三审待审核 10 三审拒绝 11 三审放弃 12 三审通过
        created_start?: number | undefined | string
        created_end?: number | undefined | string
      }
    }
    namespace Response {
      type List = ApiResponse<{ list: M.IScriptManageListItem[], open_roles: number[], current_user: string, total: number }>
    }
  }
  namespace AuditScriptFirstDetail {
    namespace Request {
      interface Params {
        play_key: string
      }
    }
    namespace Response {
      type Detail = ApiResponse<{
        detail: {
          play_path: string
          cover: string
          status: 1 | 2 | 3 | 4 // 1 待审核 2 审核拒绝 3 放弃 4 审核通过
          rejected_reason: string
        }
        play_item?: M.IScriptItemDetail
      }>
    }
  }
  // 责编一审审核
  namespace AuditScriptStep1 {
    namespace Request {
      interface Params {
        play_key: string
        data: {
          status: 1 | 2 | 3 | 4 | number // 1 待审核 2 审核拒绝 3 放弃 4 审核通过
          rejected_reason?: string
          rejected_reason_en?: string
          principal?: string
          list?: IScriptEvaluate[]
          score?: number
          rating?: string
        }
      }
    }
  }

  namespace AuditScriptFirstMajorDetail {
    namespace Request {
      interface Params {
        play_key: string
      }
    }
    namespace Response {
      type IScriptEvaluate = {
        audit_user: string
        status: 1 | 2 | 3 | 4 // 1 待审核 2 审核拒绝 3 放弃 4 审核通过
        rejected_reason: string
      }
      type majorAuditDetail = {
        main_detail: {
          audit_user?: string
          principal?: string
          rejected_reason?: string
          status: 0 | 1 | 2 | 3 | 4 // 1 待审核 2 审核拒绝 3 放弃 4 审核通过
        }
        list: IScriptEvaluate[]
        cover: string
        play_path: string
        play_item?: M.IScriptItemDetail
      }
      type Detail = ApiResponse<majorAuditDetail>
    }
  }

  namespace AuditScriptSecond {
    namespace Request {
      interface Params {
        page_index: number
        page_size: number
        title_or_key_list: string | string[] | number[]
        openplatform_principal?: string
        audit_status?: number // 审核状态 1 待审核 2 审核拒绝 3 放弃 4 审核通过
        audit3_rating?: string
        principal?: string
      }
    }
    namespace Response {
      type List = ApiResponse<{ list: M.IAuditScriptSecondListItem[], current_user: string, total: number, open_roles: number[] }>
    }
  }
  // 二审责编详情
  namespace AuditScriptSecondDetail {
    namespace Request {
      interface Params {
        play_key: string
      }
    }
    namespace Response {
      type Detail = ApiResponse<M.IAuditScriptSecondDetail>
    }
  }
  // 责编二审审核
  namespace AuditScriptStep2 {
    namespace Request {
      interface Params {
        play_key: string
        data: {
          list: IScore[]
          score: number // 责编打分
          rating: string
          description?: string
          audit_label_ids?: string | number[]
        }
      }
    }
  }
  // 责编一审审核
  namespace AuditScriptStep2Major {
    namespace Request {
      interface Params {
        play_key: string
        data: {
          status: 1 | 2 | 3 | 4 | number // 1 待审核 2 审核拒绝 3 放弃 4 审核通过
          rejected_reason?: string
          rejected_reason_en?: string
          rating: string
          audit_label_ids?: string | number[]
        }
      }
    }
  }

  namespace MajorAuditScriptSecondDetail {
    namespace Request {
      interface Params {
        play_key: string
      }
    }
    namespace Response {
      type Detail = ApiResponse<M.IMajorAuditScriptSecondDetail>
    }
  }

  namespace AuditScriptFinalDetail {
    namespace Request {
      interface Params {
        play_key: string
      }
    }
    namespace Response {
      type Detail = ApiResponse<{
        play_item?: M.IScriptItemDetail
        main_detail: M.IScripScore
        cover: string
        play_path: string
        list?: M.IScripScore[]
      }>
    }
  }

  namespace AuditScriptStep3 {
    namespace Request {
      interface Params {
        play_key: string
        data: {
          status: 1 | 2 | 3 | 4 | number // 1 待审核 2 审核拒绝 3 放弃 4 审核通过
          rejected_reason?: string
          rating: string
          audit_label_ids?: string | number[]
        }
      }
    }
  }

  namespace AuditScriptStep3Minor {
    namespace Request {
      interface Params {
        play_key: string
        data: {
          list: IScore[]
          score: number // 责编打分
          rating: string
          description?: string
          audit_label_ids?: string | number[]
        }
      }
    }
  }
}

declare namespace M {
  interface IScriptManageListItem {
    play_key: string
    title: string
    openplatform_principal: string
    audit_status: number
    principal: string
    created: number
    audit_process: string
    audit_rating: string
    description: string
    play_path: string
    cover: string
    label_ids: string
    audit_failed_reason: string
    process_status: 0 | 1
    audit_pass_time: number
    contract_status: 0 | 1
    audit_label_ids: string | number[]
    finish_time: number
    principal_audited: boolean
    contract_path: string
    audit1_minor_editor?: string
  }
  interface IAuditUserList {
    username: string
  }

  interface IScore {
    item?: string
    content?: string
    desc?: string
    score?: string // 打分
    isOption?: boolean
  }

  interface IScoreStep1 {
    item?: string
    content?: string
    desc?: string
    score?: number // 打分
    isOption?: boolean
  }

  interface IAuditScriptSecondDetail {
    detail: {
      list: IScore[]
      score: number // 责编打分
      rating: string
      description?: string
    }
    play_item?: M.IScriptItemDetail
    cover: string
    play_path: string
  }
  interface IMajorAuditScriptSecondDetail {
    list?: M.IScripScore[]
    main_detail?: M.IScripScore
    cover: string
    play_path: string
    play_item?: M.IScriptItemDetail
  }

  interface ScoreTableData {
    id: string
    category: string
    subCategory?: string
    criteria: string
    score: number | null
    desc?: string
    isOption?: boolean
  }

  interface ScoreTableDataStep1 {
    id: string
    category: string
    subCategory?: string
    criteria: string
    score: number
    desc?: string
    isOption?: boolean
  }

  interface IScriptItemDetail {
    play_key: string
    title: string
    openplatform_principal: string
    audit_status: 1 | 2 | 3 | 4 | number
    principal: string
    created: number
    audit_process: string
    audit_rating: string
    description: string
    play_path: string
    cover: string
    label_ids: string // '4,5,6'
    audit_failed_reason?: string
    audit_label_ids: string | number[]
  }

  type IScripScore = {
    audit_user?: string
    status: 1 | 2 | 3 | 4 | number// 1 待审核 2 审核拒绝 3 放弃 4 审核通过
    rejected_reason?: string
    rating?: string
    description?: string
  }
}
