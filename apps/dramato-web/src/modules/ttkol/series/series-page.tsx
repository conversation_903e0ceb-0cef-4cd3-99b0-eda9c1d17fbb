import { createComponent, getQueries } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { apiSeriesInfo, SeriesInfo } from '../ttkol-api'
import { ref } from 'vue'
import { click, dramabuzz, dramabuzzLogo, dramawave, dramawaveLogo } from '../image/image'
import { goToApp } from 'src/lib/go-to-app'
import { isIos } from 'src/lib/ua'
import { appName } from 'src/lib/constants'
import { useLocale } from 'src/lib/use-locale'
import { useClipboard } from '@vueuse/core'

export const SeriesPage = createComponent(null, () => {
  const series = ref<SeriesInfo>()
  const { id, pid, language, c, af_channel } = getQueries({
    id: '',
    pid: 'ttkol',
    language: 'en',
    c: '',
    af_channel: '',
  })
  const loading = ref<boolean>(true)
  const width = ref<number>()
  const height = ref<number>()
  const { t } = useLocale(language)
  const deepLink = `dramawave://dramawave.app?redirect=${encodeURIComponent('/detail?id=' + id)}`
  const universalLink = `https://${appName === 'dramabuzz' ? 'thedramabuzz' : 'mydramawave'}.com?redirect=${encodeURIComponent('/detail?id=' + id)}`
  const { copy } = useClipboard()

  void apiSeriesInfo(id, language).then(res => {
    if (!res.data) return
    if (res.data.series_info && res.data.series_info.cover) {
      const image = new Image()
      image.src = res.data.series_info.cover
      image.onload = () => {
        width.value = image.width
        height.value = image.height
        loading.value = false
        series.value = res.data!.series_info
      }
    }
  })
  const handleClick = () => {
    const clipboardText = `1 https://mydramawave.com?redirect=%2Fdetail%3Fid%3D${id} pid=${pid}&af_channel=${af_channel}`
    void copy(clipboardText)
    // 使用 JavaScript 超时机制，检测应用是否已安装，已安装跳转到app，未安装跳转到appStoreLink
    // 实现H5唤端功能，当IOS时，使用universalLink,当Android时使用 deepLink 唤端
    goToApp(isIos() ? universalLink : deepLink)
  }

  return () => (
    !loading.value && (
      <div class="relative size-full bg-[#0b080b]">
        <div class="z-0 left-0 top-0 absolute w-full h-auto">
          <img src={series.value?.cover} class="z-0 object-cover h-auto w-full relative" />
          <div class="absolute z-1 size-full left-0 top-0 backdrop-blur-[5px] bg-gradient-to-b from-[#0B080B1A] to-[#0b080b]" />
        </div>
        <div class="px-3 w-full z-up flex flex-col items-center relative py-26">
          <div class="absolute top-10 h-10 left-1/2 -translate-x-1/2 w-full flex items-center justify-center">
            <img src={appName === 'dramabuzz' ? dramabuzzLogo : dramawaveLogo} class="h-full w-auto object-cover" />
            <img src={appName === 'dramabuzz' ? dramabuzz : dramawave} class="h-[80%] w-auto object-cover" />
          </div>
          <div class="flex-1 w-full flex justify-center items-center">
            <img src={series.value?.cover} class="w-[60%] object-cover h-auto rounded-lg border-solid border border-[rgba(253,_251,_252,_0.2)]" width={width.value} height={height.value} />
          </div>
          <div class="w-full flex flex-col items-center gap-3">
            <div class="text-center text-[#fdfbfc] text-lg leading-snug mt-8">{series.value?.name}</div>
            <div class="text-[#cccacb] text-sm font-normal line-clamp-5 break-words">{series.value?.desc}</div>
          </div>
          <Button class="fixed bottom-12 pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] w-[calc(100%-1.5rem)] left-1/2 -translate-x-1/2 h-11 bg-[var(--brand-6)] rounded-lg border-none text-[rgba(253,_251,_252,_0.9)] text-base leading-tight" onClick={handleClick}>
            {t('share.watchNow')}
            <img src={click} class="animate-[blink_.8s_infinite] absolute w-11 h-9 right-4 -bottom-[10px] object-cover" />
          </Button>
        </div>
      </div>
    )
  )
})

export default SeriesPage
