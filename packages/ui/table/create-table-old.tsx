import { ClassName, createComponent, mc, tryCall } from '@skynet/shared'
import { VNodeChild } from 'vue'
import { MergeClass } from '../ui'
export type TableColumnOld<D> = [
  name: string | VNodeChild | ((column: TableColumnOld<D>, data: D[]) => VNodeChild),
  render: keyof D | ((row: D, rowIndex: number, data: D[]) => VNodeChild),
  {
    class?: ClassName | ((row: D | null, rowIndex: number, data: null | D[]) => ClassName)
  },
] | [
  name: string | VNodeChild | ((column: TableColumnOld<D>, data: D[]) => VNodeChild),
  render: keyof D | ((row: D, rowIndex: number, data: D[]) => VNodeChild),
]
type TableOptions<D> = {
  props: {
    list: D[] | null
    columns: Array<TableColumnOld<D>>
    loading?: boolean
  }
}
const resolve = <D extends object>(maybeString: string | VNodeChild | ((column: TableColumnOld<D>, data: D[]) => VNodeChild), column: TableColumnOld<D>, data: D[]) => {
  return typeof maybeString === 'function' ? maybeString(column, data) : maybeString
}
export const CreateTableOld = <D extends object>() => createComponent<TableOptions<D>>({
  props: {
    list: null,
    columns: [],
    loading: false,
  },
}, props => {
  return () => (
    <MergeClass baseClass="block overflow-x-auto max-w-full break-all">
      {props.loading
        ? <div class="py-8 text-center"><span class="loading loading-spinner size-4" /></div>
        : !props.loading && (!props.list || props.list.length === 0)
            ? <div class="py-8 text-center">数据为空</div>
            : (
                <table class="table-zebra table max-w-full table-fixed">
                  <colgroup>
                    {props.columns.map(column => <col class={mc('w-20', tryCall(column[2]?.class, null, -1, null))} />)}
                  </colgroup>
                  {/* head */}
                  <thead>
                    <tr>
                      {props.columns.map(column => (
                        <th class={mc('w-20', tryCall(column[2]?.class, null, -1, null))}>
                          {resolve(column[0], column, props.list!)}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {props.list.map((row, rowIndex, array) => (
                      <tr>
                        {props.columns.map(column => (
                          <td class={mc('w-20', tryCall(column[2]?.class, row, rowIndex, array))}>
                            {typeof column[1] === 'function' ? column[1](row, rowIndex, array) : row[column[1]]}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
    </MergeClass>

  )
})
