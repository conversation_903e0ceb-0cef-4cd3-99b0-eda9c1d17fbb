import { ClassName, createComponent, fn, mc } from '@skynet/shared'
type SwitchOptions = {
  props: {
    class?: ClassName
    modelValue: boolean
    labels?: {
      on: string
      off: string
    }
  }
  emits: {
    'update:modelValue': (value: boolean) => void
  }
}
export const Switch = createComponent<SwitchOptions>({
  inheritAttrs: false,
  props: {
    class: '',
    modelValue: false,
    labels: {
      on: '✔️',
      off: '✖️',
    },
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const onClick = () => {
    emit('update:modelValue', !props.modelValue)
  }
  return () => (
    <button class={mc(`inline-flex h-8 w-[56px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent
      transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
      focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 `, props.class, [
      props.modelValue && 'bg-primary',
      !props.modelValue && 'bg-[#e4e4e7]',
    ])} onClick={onClick}
    >
      <span class={['pointer-events-none h-6 w-6 flex items-center justify-center rounded-full bg-white shadow-lg ring-0 transition-transform text-xs',
        props.modelValue && 'translate-x-6',
        !props.modelValue && 'translate-x-1',
      ]}
      >
        {props.modelValue ? props.labels.on : props.labels.off}
      </span>
    </button>
  )
})
