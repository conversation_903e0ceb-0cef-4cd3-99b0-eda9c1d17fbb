import { ref, watchEffect } from 'vue'

const uploadMap = ref<Map<string, {
  materialId: number
  uploadStatus: number
}>>(new Map())

export const useUploadMaterialStore = () => {
  const leavePage = (event: Event) => {
    const message = '您确定要离开此页面吗？未保存的更改将丢失。'
    // @ts-expect-error never mind
    event.returnValue = message
    return message
  }

  watchEffect(() => {
    const uploadingList: number[] = Array.from(uploadMap.value.entries()).map(([, value]) => value.uploadStatus === 1 ? value.materialId : 0).filter(Boolean)
    localStorage.setItem('uploadingList', JSON.stringify(uploadingList))
    if (Array.from(uploadMap.value.entries()).length > 0) {
      window.addEventListener('beforeunload', leavePage)
    } else {
      window.removeEventListener('beforeunload', leavePage)
    }
  })

  return {
    uploadMap,
  }
}
