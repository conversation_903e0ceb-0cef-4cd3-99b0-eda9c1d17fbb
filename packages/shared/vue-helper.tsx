import { h, onBeforeUnmount, onMounted, ref } from 'vue'
import { Renderable } from './typings'
export const render = (x?: Renderable | null) => x ? h(x) : null

export const useBool = (initial = false) => {
  const value = ref(initial)
  const toggle = (v?: boolean) => {
    if (typeof v === 'boolean') {
      value.value = v
    } else {
      value.value = !value.value
    }
  }
  return [value, toggle] as const
}

export const removeIndent = (content: string) => {
  const lines = content.split('\n')
  let index = 0
  while (index < lines.length && lines[index].trim() === '') {
    index++
  }
  const firstLine = lines[index]
  const [indent] = firstLine.match(/^\s*/g) ?? []
  const r = new RegExp(`^${indent}`, 'g')
  return lines.map(line => line.replace(r, '')).join('\n').trim()
}

export const tryCall = (maybeFn: unknown, ...args: unknown[]) => {
  if (typeof maybeFn === 'function') {
    return maybeFn.call(null, ...args)
  }
  return maybeFn
}

type LoadScriptOptions = {
  id?: string
  async?: boolean
  crossorigin?: string
  onLoad?: (e: Event) => void
  onError?: (e: Event | string) => void
}
export const loadScript = (url: string, options: LoadScriptOptions = {}) => {
  try {
    const script = document.createElement('script')
    script.src = url
    if (options.async) script.async = true
    if (options.crossorigin) script.crossOrigin = options.crossorigin
    if (options.id) script.id = options.id
    if (options.onLoad) script.onload = options.onLoad
    if (options.onError) script.onerror = options.onError
    document.head.appendChild(script)
  } catch (e) {
    console.error(e)
  }
}

export function useAutoOverflowBody() {
  const bodyOverflow = ref('')
  onMounted(() => {
    bodyOverflow.value = document.body.style.overflow
    document.body.style.overflow = 'hidden'
  })
  onBeforeUnmount(() => {
    document.body.style.overflow = bodyOverflow.value
  })
}
