@import './reset.css';
@import './vars.css';
@import './dark-mode-for-app.css';
@import './color-scheme.css';
@import './components.css';
@import './animate.css';
@unocss;

/* 禁用 color-scheme 自动切换 */
 /* :root {
  color-scheme: light dark;
} */

html, body, #app {
  max-height: 100vh;
}

#app > div:nth-child(1) {
  height: 100vh;
}


body {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
}

:where(.tm-no-dark) {
  @apply bg-[var(--fill-4)] text-[var(--text-1)];
}


* {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    SF Pro,
    Arial,
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    sans-serif;
}

html.dark {
  .markmap {
    --markmap-text-color: #ffffff;
  }
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.no-tap-color {
  -webkit-tap-highlight-color: transparent;
  /* iOS Chrome */
  -ms-tap-highlight-color: transparent;
  /* IE */
  -moz-tap-highlight-color: transparent;
  /* Firefox */
  -o-tap-highlight-color: transparent;
  /* Opera */
}

.tm-shadow {
  box-shadow: 0 10px 15px -3px #0a66c220, 0 4px 6px -4px #0a66c220;
}

.no-dark {
  background-color: #fff !important;
  color: #141414 !important;

  * {
    background-color: #fff !important;
    color: #141414 !important;
  }
}

.tm-overflow-hidden {
  overflow: hidden;
}

/** 处理 messgae-content 组件自带最小高问题  */
.tiptap {
  min-height: auto !important;
}

.share-bottom-8 {
  @apply pb-8;
}

.share-bottom-8-4 {
  @apply pb-8;
}

.share-bottom-35 {
  @apply pb-35;
}

.share-bottom-2 {
  @apply pb-2;
}

@supports (bottom: constant(safe-area-inset-bottom)) or (bottom:env(safe-area-inset-bottom)) {

  .share-bottom-8 {
    padding-bottom: calc(constant(safe-area-inset-bottom) + 2rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
  }

  .share-bottom-8-4 {
    padding-bottom: calc(constant(safe-area-inset-bottom) + 4rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 4rem);
  }

  .share-bottom-35 {
    padding-bottom: calc(constant(safe-area-inset-bottom) + 8.75rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 8.75rem);
  }

  .share-bottom-2 {
    padding-bottom: calc(constant(safe-area-inset-bottom) + 0.5rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 0.5rem);
  }
}


.nav-top-8 {
  @apply pt-8
}

.nav-top-11 {
  @apply pt-11
}

@supports (top: constant(safe-area-inset-top)) or (top: env(safe-area-inset-top)) {
  .nav-top-8 {
    padding-top: calc(constant(safe-area-inset-top) + 0.5rem);
    padding-top: calc(env(safe-area-inset-top) + 0.5rem);
  }

  .nav-top-11 {
    padding-top: max(calc(constant(safe-area-inset-top) + 0.5rem), 44px);
    padding-top: max(calc(env(safe-area-inset-top) + 0.5rem), 44px);
  }
}