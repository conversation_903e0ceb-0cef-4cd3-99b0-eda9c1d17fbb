import { report, performanceData } from '@skynet/client-track'
import { when } from '@skynet/shared'
import { deviceInfo } from './get-device-info'
import { authInfo } from './get-auth'
import { inApp } from './jsbridge'

export const track = (origin: string, entity: string, event: 'show' | 'click' | 'successful' | 'load', extra?: object) => {
  if (inApp) {
    void when(() => deviceInfo && authInfo).then(() => {
      console.log('埋点payload', {
        event: `${origin}_${entity}_${event}`,
        device_hash: deviceInfo?.device_hash ?? deviceInfo?.device_id ?? 'unknown',
        app_version: deviceInfo?.app_version ?? 'unknown',
        user_id: authInfo?.uid ? authInfo.uid : 'unknown',
        user_source: 'app',
        device_manufacturer: deviceInfo?.device_manufacturer ?? deviceInfo?.manufacturer ?? 'unknown',
        device_name: deviceInfo?.device_model ?? deviceInfo?.model ?? 'unknown',
        app_package: deviceInfo?.app_name ?? 'com.freereels.app',
        event_info: JSON.stringify({
          ...performanceData(),
          origin,
          entity,
          event,
          ...extra,
        }),
      })
      report({
        event: `${origin}_${entity}_${event}`,
        device_hash: deviceInfo?.device_id ?? 'unknown',
        app_version: deviceInfo?.app_version ?? 'unknown',
        user_id: authInfo?.uid ? authInfo.uid : 'unknown',
        user_source: 'app',
        // @ts-expect-error never mind
        device_manufacturer: deviceInfo?.device_manufacturer ?? deviceInfo?.manufacturer ?? 'unknown',
        device_name: deviceInfo?.device_model ?? deviceInfo?.model ?? 'unknown',
        app_package: deviceInfo?.app_name ?? 'com.freereels.app',
        event_info: JSON.stringify({
          ...performanceData(),
          origin,
          entity,
          event,
          ...extra,
        }),
      })
    }).catch(() => {
      // do nothing
      report({
        event: `${origin}_${entity}_${event}`,
        user_source: 'web',
        device_hash: 'unknown',
        app_version: 'unknown',
        user_id: 'unknown',
        event_info: JSON.stringify({
          ...performanceData(),
          origin,
          entity,
          event,
          ...extra,
        }),
      })
    })
  } else {
    report({
      event: `${origin}_${entity}_${event}`,
      user_source: 'web',
      device_hash: 'unknown',
      app_version: 'unknown',
      user_id: 'unknown',
      event_info: JSON.stringify({
        ...performanceData(),
        origin,
        entity,
        event,
        ...extra,
      }),
    })
  }
}
