export const useStretchScroll = (dom: HTMLElement, direction: 'vertical' | 'horizontal' = 'vertical') => {
  if (!dom) return
  let startX = 0
  let startY = 0
  let currentX = 0
  let currentY = 0

  const handleTouchStart = (e: TouchEvent) => {
    startX = e.touches[0].clientX
    startY = e.touches[0].clientY
  }

  const handleTouchMove = (e: TouchEvent) => {
    e.preventDefault()
    dom.style.transition = 'none'
    const deltaY = e.touches[0].clientY - startY
    const deltaX = e.touches[0].clientX - startX
    currentY += deltaY // 更新当前Y位置
    currentX += deltaX

    if (direction === 'horizontal') {
      dom.style.transform = `translateX(${currentX}px)` // 应用平移效果
      startX = e.touches[0].clientX // 更新起始位置
    } else {
      dom.style.transform = `translateY(${currentY}px)` // 应用平移效果
      startY = e.touches[0].clientY // 更新起始位置
    }
  }

  const handleTouchEnd = () => {
    // 添加弹性回弹效果
    if (direction === 'vertical') {
      if (currentY > 0) {
        currentY = 0 // 向下滚动超过边界时回弹
      } else if (currentY < dom.parentElement!.clientHeight - dom.clientHeight) {
        currentY = dom.parentElement!.clientHeight - dom.clientHeight > 0 ? 0 : dom.parentElement!.clientHeight - dom.clientHeight // 向上滚动超过边界时回弹
      }
    } else {
      if (currentX > 0) {
        currentX = 0 // 向右滚动超过边界时回弹
      } else if (currentX < dom.parentElement!.clientWidth - dom.clientWidth) {
        currentX = dom.parentElement!.clientWidth - dom.clientWidth > 0 ? 0 : dom.parentElement!.clientWidth - dom.clientWidth // 向左滚动超过边界时回弹
      }
    }
    dom.style.transform = `translate${direction === 'vertical' ? 'Y' : 'X'}(${direction === 'vertical' ? currentY : currentX}px)`
    dom.style.transition = 'transform 200ms'
  }

  dom.removeEventListener('touchstart', handleTouchStart)
  dom.removeEventListener('touchmove', handleTouchMove)
  dom.removeEventListener('touchend', handleTouchEnd)

  dom.addEventListener('touchstart', handleTouchStart)
  dom.addEventListener('touchmove', handleTouchMove)
  dom.addEventListener('touchend', handleTouchEnd)
}
