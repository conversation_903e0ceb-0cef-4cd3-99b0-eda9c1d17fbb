import { createComponent, getQueries } from '@skynet/shared'
import { background_pc, email_logo, logo, verify_success } from './images/images'
import { onMounted, ref } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { apiBindEmail } from './bind-email-api'
import { showToast } from '../common/toast/toast'
import Loading from '../common/loading/loading'
export const BindEmailResultPage = createComponent(null, () => {
  const { user_info, verification_code, language, from, email } = getQueries({
    user_info: '',
    verification_code: '',
    language: 'en',
    from: '',
    email: '',
  })
  const { t } = useLocale(language)
  const verifySuccess = ref(false)
  const loading = ref(true)

  onMounted(() => {
    document.title = t('bind_email.title')
    const user_id = (parseInt(user_info, 16) - 89999999) << 8 | 0xFF
    console.log(user_id)
    // 绑定邮箱
    void apiBindEmail({
      userInfo: user_info,
      verificationCode: verification_code,
    }).then(() => {
      verifySuccess.value = true
    }).catch((error?: { response?: { data?: { message?: string } } }) => {
      if (error?.response?.data?.message) {
        showToast(error.response.data.message)
      }
    }).finally(() => {
      loading.value = false
    })
  })

  return () => (
    loading.value
      ? (
          <Loading />
        )
      : (
          verifySuccess.value && (
            <div class="w-full h-full relative bg-gradient-to-b from-[#ff3950] to-[#ff4850] overflow-auto">
              <div class="relative w-full h-auto min-h-full">
                <img width={1920} height={1080} class="absolute z-up block w-full h-full object-cover" src={background_pc} />
                <div class="relative w-full z-up-up flex flex-col items-center pb-10">
                  <img src={logo} width={307} height={63} class="pc:mt-[62px] mt-5 pc:max-w-[200px] max-w-1/2 h-auto" />
                  <img src={email_logo} width={389} height={265} class="mt-4 pc:mt-[52px] pc:max-w-[352px] w-2/3 h-auto object-cover" />
                  <x-container class="w-[calc(100%-24px)] pc:w-[1000px] mt-5 pc:py-10 pt-2 pb-5 px-4 mx-3 bg-white rounded-lg flex-col justify-start items-center gap-4 flex">
                    <x-verify-success class="w-full flex flex-col gap-y-4 items-center justify-center">
                      <img class="block pc:w-[100px] pc:h-[100px] w-15 h-15 object-cover" src={verify_success} />
                      <p class="text-center text-[#1cc15b] text-lg pc:text-3xl font-bold">{t('bind_email.verify_success')}</p>
                    </x-verify-success>
                    <x-list class="flex flex-col items-start pc:gap-y-3 gap-y-1 w-auto pc:max-w-[600px] max-w-[calc(100%-40px)] text-[#434546] text-sm pc:text-xl font-normal">
                      {
                        [
                          t('bind_email.list_1'),
                          t('bind_email.list_2'),
                          t('bind_email.list_3'),
                        ].map(item => (
                          <x-list-item class="flex items-start gap-1"><div class="flex items-center justify-center h-full w-4 shrink-0">•</div> {item}</x-list-item>
                        ))
                      }
                    </x-list>
                  </x-container>
                </div>
              </div>
            </div>
          )
        )
  )
})

export default BindEmailResultPage
