import { createComponent } from '@skynet/shared'
import { Storage } from '@skynet/shared/const'
import { useFormStore } from '@skynet/ui/form/use-form-store'
import { Uploader } from 'src/modules/common/uploader/uploader'
import { computed, ref } from 'vue'

const { renders } = useFormStore()

type ImageInputOptions = {
  props: {
    value?: string
    onInput?: null | ((value?: unknown) => void)
    error?: string
    uploadUrl?: string
  }
}
export const ImageInput = createComponent<ImageInputOptions>({
  props: {
    value: '',
    onInput: null,
    error: '',
    uploadUrl: '/popup/upload/image',
  },
}, props => {
  const previewUrl = ref('')
  const imageSrc = computed(() => previewUrl.value || props.value || '')
  return () => (
    <x-image-input class="block">
      <Uploader isImage accept="png,jpg,jpeg" maxsize={10 * Storage.MB} class="size-40 cursor-pointer"
        uploadUrl={props.uploadUrl}
        onUploadSuccess={d => { previewUrl.value = d.url ?? ''; props.onInput?.(d.temp_path) }}
      >
        {imageSrc.value
          ? (
              <x-preview class="flex w-full h-full justify-center items-center bg-gray-200">
                <img src={imageSrc.value} alt="预览" class="max-w-full max-h-full" />
              </x-preview>
            )
          : <span class="flex size-full items-center justify-center bg-gray-200">上传图片</span>}
      </Uploader>
    </x-image-input>
  )
})

renders.value.image = ImageInput
