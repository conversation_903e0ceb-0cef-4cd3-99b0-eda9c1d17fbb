type Config = {
  path?: string
  query?: Record<string, string>
  params?: Record<string, unknown>
}

// 抄自 vue-router 的 NavigationFailure
interface NavigationFailure extends Error {
  type: number
  from: object
  to: object
}
/**
 * 解决 router.push 会覆盖原有 query 的问题
 * @param router
 * @param config
 * @returns
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const betterPush = (router: any, config: Config): Promise<NavigationFailure | void | undefined> => {
  return router.push({
    ...config,
    query: { ...router.currentRoute.value.query, ...config.query },
  })
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const betterReplace = (router: any, config: Config): Promise<NavigationFailure | void | undefined> => {
  return router.replace({
    ...config,
    query: { ...router.currentRoute.value.query, ...config.query },
  })
}
