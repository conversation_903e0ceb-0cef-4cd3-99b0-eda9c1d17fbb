export const ru = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: 'Популярные варианты',
    },
  },
  myList: {
    watchHistory: `История Просмотра`,
    myList: '<PERSON><PERSON><PERSON>о<PERSON>',
    seeAll: 'Посмотреть Все',
    noData: 'Нет связанных результатов',
  },
  // 分享页
  share: {
    watchNow: 'Смотреть Сейчас',
    episodeDialog: {
      title: 'Присоединяйся ко мне и получи дополнительные монеты!',
      description: 'Я подсел на этот сериал!',
      tips: 'Рекомендация',
      button: 'Смотреть весь сериал',
      limit24: 'Ограниченное время: 24h',
    },
    toast: {
      takeOffShelf: 'Контент Недоступен',
    },
  },
  'customer-service-center': {
    title: 'Центр обслуживания клиентов',
    description: 'Описание',
    'description-placeholder': 'Предоставьте сведения, которые помогут нам понять проблему',
    'email-placeholder': 'Введите свой адрес электронной почты, чтобы получать обновления отзывов',
    'submit-feedback': 'Отправить',
    'enter-feedback': 'Другие проблемы с отзывами',
    'upload-pictures': 'Загрузить изображения',
    feedback: 'Обратная связь',
    'problem-type': 'Тип проблемы',
    'problem-type-placeholder': 'Пожалуйста, выберите',
    'payment-issue-placeholder': 'Предоставьте удостоверение личности и квитанцию о заказе: Google: https://pay.google.com/ (номер заказа начинается с GPA...) iOS: App Store → Apple ID → История покупок (ID заказа).',
    'content-issue-placeholder': 'Укажите название сериала, номера эпизодов и отсутствующие языки субтитров.',
    'ads-issue-placeholder': 'Укажите модель вашего устройства, страну и языковые настройки.',
    'technical-issue-placeholder': 'Укажите модель вашего устройства, страну и языковые настройки.',
    'other-issue-placeholder': 'Укажите больше информации, включая модель устройства и страну.',
    'i-want-to-give-feedback': 'Оставить отзыв',
    'are-you-satisfied-with-this-reply': 'Вы довольны ответом?',
    unsatisfied: 'Нет',
    satisfied: 'Да',
    myFeedback: 'Мой отзыв',
    'reply-to-feedback': 'Ответить',
    'description-min-length': 'Введите описание проблемы (не менее 6 символов)',
    'problem-type-not-choose': 'Выберите тип проблемы',
    'description-empty': 'Это окно не может быть пустым',
  },
  terminate: {
    question: 'Как управлять подпиской?',
    desc: 'Нажмите кнопку ниже, чтобы перейти на страницу управления договором. Вы можете управлять подпиской с помощью соответствующих настроек.',
    title: 'Управление договором',
    expiration: 'Дата окончания',
    manageDesc: 'Договорные отношения',
  },
  toast: {
    'upload-failed': 'Загрузка не удалась, пожалуйста, загрузите снова',
    'submit-success': 'Отправить успешно',
    'email-incorrect': 'Неправильный формат электронной почты',
  },
  not_found: {
    title: 'Страница, которую вы ищете, не существует',
  },
  regulations: {
    howUseData: 'Как мы используем ваши данные',
    content: `Если вы выберете "Разрешить" в запросе разрешения на прозрачность отслеживания приложений, мы будем использовать вашу информацию и данные для оптимизации наших услуг и предоставления вам наилучшего опыта, в противном случае мы будем только собирать данные и не будем проводить никаких действий по отслеживанию.

Если вы согласны разрешить DramaWave и нашим партнерам и поставщикам использовать данные с вашего устройства для анализа того, как вы используете приложение, персонализации рекомендаций по контенту и предоставления дополнительных услуг, связанных с информацией, пожалуйста, нажмите "Согласиться и продолжить", чтобы продолжить.`,
    acceptAll: 'Принять все',
    dataSettings: 'Настройки данных',
    next: 'Далее',
    cancel: 'Отмена',
    aboutYourPrivacy: 'О вашей конфиденциальности',
    personalDataName: 'Персонализированные данные',
    personalDataDescription: 'Мы используем эту информацию, чтобы запомнить ваши выборы и предоставлять персонализированную рекламу и рекомендации по контенту, уведомления и напоминания и т.д.',
    advertisementDataName: 'Рекламные данные',
    advertisementDataDescription: 'Файлы cookie, идентификаторы вашего устройства или другая информация могут храниться или быть доступны на вашем устройстве для целей онлайн-рекламы. Данные пользователей также могут быть переданы нашим рекламным партнерам.',
    behaviorsDataName: 'Поведенческие данные',
    behaviorsDataDescription: 'Данные, связанные с аналитикой, такие как файлы cookie или идентификаторы устройств, могут храниться для измерения и анализа использования, поведения и эффективности рекламы.',
    privacyAndTerms: 'Политика конфиденциальности и условия использования',
    referToPrivacy: 'Пожалуйста, ознакомьтесь с Политикой конфиденциальности',
    referToTerms: 'Пожалуйста, ознакомьтесь с Условиями использования',
    agreeAndSave: 'Согласиться и сохранить',
  },
  coins: {
    earnings: {
      title: 'Мои доходы',
      total: 'Всего алмазов',
      exchange: 'Обмен',
      earningsHistory: 'История доходов',
      exchangeHistory: 'История обмена',
      noRecords: 'Нет записей',
      last50earnings: 'Отображаются только последние 50 доходов',
      last50exchanges: 'Отображаются только последние 50 записей обмена',
      benefits: 'Преимущества',
      gotIt: 'Понял',
    },
    exchange: {
      title: 'Зона обмена',
      notOpen: 'Еще не открыто, пожалуйста, ожидайте',
      receiveBtn: 'С радостью принять',
      earnCoins: 'Заработать алмазы',
      redeemRequiredXCoins: 'Для обмена требуется {X} алмазов.',
      exchangeSuccess: 'Погашение успешно',
      cardId: 'Идентификатор карты',
      exchangeFailed: 'Погашение не удалось',
      transferFailedInsufficientStock: 'Нет в наличии, приходите завтра пораньше.',
      gotItBtn: 'знал',
      transferFailedRiskControl: 'Аккаунт является ненормальным и не может быть временно погашен.',
      tryAgainBtn: 'Пожалуйста, повторите попытку позже',
      transferConfirm: 'Подтвердить перевод',
      paypalAccountEmail: 'Электронная почта учетной записи PayPal',
      confirmPaypalAccountEmail: 'Подтвердите адрес электронной почты учетной записи PayPal',
      transferX: 'Перевести {X}',
      transferXConfirm: 'Подтвердить перевод {X}',
      transferToYourAccountX: 'Переведите {X} на свой счет',
      confirmBtn: 'подтверждать',
      redeemFailed: 'Ошибка активации',
      redeemFailedContent: 'Ошибка. Попробуйте позже.',
      transferSuccessTitle: 'Мы отправили деньги!',
      transferSuccessContent: 'Они поступят в промежутке от 5 минут до 72 часов.',
      copySuccess: 'Копирование успешно',
      enterEmailError: 'Пожалуйста, введите действительный адрес электронной почты',
      enterEmailValidError: 'Ошибка ввода, пожалуйста, введите снова',
      emailNotMatchError: 'Адрес электронной почты не совпадает, пожалуйста, введите снова',
    },
  },
  meal_check_in: {
    sleeping_link: 'Вы можете зарабатывать алмазы во время сна',
    rules_title: 'Правила активности',
    benefits_title: 'Benefits',
    rules_1: '1. Каждый день есть четыре времени для записи ваших приемов пищи: Завтрак: 07:00-10:00; Обед: 11:00-14:00; Ужин: 17:00-20:00; Полночный перекус: 21:00-24:00.',
    rules_2: '2. Если вы пропустили время записи, вы можете посмотреть видео, чтобы получить свою награду.',
    rules_3: '3. Награды сбрасываются каждый день в полночь, не забудьте забрать свою награду.',
  },
  sleep_check_in: {
    earn_link: 'Записывайте приемы пищи, чтобы зарабатывать',
    benefits_title: 'Benefits',
    rules_1: '1. Вы можете активировать режим «Спать, чтобы зарабатывать» каждый вечер с 19:00 до 24:00.',
    rules_2: '2. После активации на следующий день, после 8 часов сна, вы можете получить вознаграждение за сон с 8:00 до 12:00.',
    rules_3: '3. Если вы пропустили время для получения вознаграждения, вы можете посмотреть видео с 12:00 до 19:00 на следующий день, чтобы заново получить вознаграждение.',
  },
  bind_email: {
    title: 'Введите ваш адрес электронной почты.',
    list_1: 'Привяжите вашу электронную почту, чтобы получить эксклюзивные монеты.',
    list_2: 'Получайте уведомления о предстоящих событиях и выгодных предложениях.',
    list_3: 'Будьте в курсе новых эпизодов первыми. ',
    email_placeholder: 'Введите свой адрес электронной почты.',
    verify: 'Подтвердить',
    email_error: 'Введите действительный адрес электронной почты.',
    verify_progress: 'Электронная почта проверяется.',
    verify_success: 'Получилось!',
    re_enter: 'Введите ещё раз',
    email_repeat: 'Эта электронная почта уже участвовала в акции!',
  },
  infinityScroll: {
    end: 'Конец',
    loading: 'Загрузка...',
  },
}
