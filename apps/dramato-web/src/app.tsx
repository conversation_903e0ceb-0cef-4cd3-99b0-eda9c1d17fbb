import { createComponent } from '@skynet/shared'
import { RouterView } from 'vue-router'
import { AppName } from './lib/constants'
import { useToastStore } from './modules/common/toast/use-toast-store'
import { useDialogStore } from './modules/common/dialog'

export const App = createComponent(null, () => {
  const { renderToasts } = useToastStore()
  const { renderDialogs } = useDialogStore()
  if (AppName === 'DramaWave') {
    document.title = 'DramaWave'
    const icon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
    icon.href = '/dramawave.ico'
  } else {
    document.title = 'Dramabuzz'
    const icon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
    icon.href = '/dramabuzz.ico'
  }

  return () => (
    <>
      <RouterView />
      <div class="size-full" id="mountRoot">
        { renderDialogs() }
        {
          renderToasts().length > 0 && (
            <div class="size-screen fixed left-1/2 top-0 z-toast flex -translate-x-1/2 flex-col items-center justify-center space-y-4">
              {renderToasts()}
            </div>
          )
        }
      </div>
    </>
  )
})
