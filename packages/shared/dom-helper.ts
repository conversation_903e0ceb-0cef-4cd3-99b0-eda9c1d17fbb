import { createUuid } from './id-helper'
import { polyfillForPromiseAllSettled } from './promise-helper'

export function parseHtml(html?: string | null) {
  if (!html) return null
  const t = document.createElement('template')
  t.innerHTML = html
  return t.content
}

type Config = {
  removeFill?: boolean | ((path: string, content: string) => boolean)
}
export function insertSvgSymbols(modules: Record<string, { default: string }>, config?: Config) {
  const id = createUuid('svg-symbols-')
  const wrapper = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
  wrapper.setAttribute('id', id)
  wrapper.setAttributeNS('http://www.w3.org/2000/xmlns/', 'xmlns:xlink', 'http://www.w3.org/1999/xlink')
  wrapper.setAttribute('xmlns', 'http://www.w3.org/2000/svg')
  wrapper.setAttribute('xmlns:link', 'http://www.w3.org/1999/xlink')
  wrapper.setAttribute('aria-hidden', 'true')
  wrapper.setAttribute('style', 'position: absolute; width: 0px; height: 0px;')

  polyfillForPromiseAllSettled()
  void Promise.allSettled(Object.entries(modules).map(([path, mod]) => [path, mod.default])).then(results => {
    results.forEach(item => {
      const { status } = item
      if (status !== 'fulfilled') return
      const [path, content] = item.value
      const fragment = tryParseHtml(content)
      if (!fragment) return
      const svg = fragment.children[0]
      if (!svg) return
      if (config?.removeFill) {
        if (typeof config.removeFill === 'function') {
          if (config.removeFill(path, content)) {
            removeFill(svg)
          }
        } else {
          removeFill(svg)
        }
      }
      const viewBox = svg.getAttribute('viewBox')
      const symbolTag = document.createElementNS('http://www.w3.org/2000/svg', 'symbol')
      symbolTag.setAttribute('viewBox', viewBox || '')
      symbolTag.setAttribute('id', path.split('/').pop()?.split('.')[0] || '')
      symbolTag.innerHTML = svg?.innerHTML || ''
      wrapper.insertAdjacentElement('afterbegin', symbolTag)
    })
    document.body.insertAdjacentElement('afterbegin', wrapper)
  })

  return () => wrapper.remove()
}

export async function lazyInsertSvgSymbols(modules: Record<string, () => Promise<{ default: string }>>) {
  polyfillForPromiseAllSettled()
  const result = await Promise.allSettled(
    Object.entries(modules).map(([path, fn]) => {
      return fn().then(mod => [path, mod] as const)
    }),
  )
  const mods = Object.fromEntries(result.filter(item => item.status === 'fulfilled').map(item => item.value))
  return insertSvgSymbols(mods)
}

export function tryParseHtml(html?: string | null) {
  if (!html) return null
  try {
    return parseHtml(html)
  } catch (e) {
    console.error('parse html error', e)
    return null
  }
}

/**
 * 在祖先元素中查找第一个匹配的元素（查找范围包含当前元素）
 */
export function findAncestor(el: HTMLElement, selector: string) {
  let parent: HTMLElement | null = el
  while (parent) {
    if (parent.matches(selector)) {
      return parent
    }
    parent = parent.parentElement
  }
  return null
}

export function getWindowHeight() {
  return document.documentElement.clientHeight, window.innerHeight || 0
}
export function getWindowHeightPx() {
  return getWindowHeight() + 'px'
}

function removeFill(el: Element) {
  if (el.hasAttributes()) {
    el.removeAttribute('fill')
  }
  Array.from(el.children).forEach(child => removeFill(child))
}
