import { createComponent, fn } from '@skynet/shared'
import flatpickr from 'flatpickr'
import 'flatpickr/dist/flatpickr.css'
import 'flatpickr/dist/l10n/zh'
import { onMounted, ref } from 'vue'

const input = ref<HTMLDivElement>()

type DatetimePickerOptions = {
  props: {
    modelValue?: string | number
    displayFormat?: string
    enableTime?: boolean
  }
  emits: {
    'update:modelValue': (value: string | number) => void
  }
}
export const DatetimePicker = createComponent<DatetimePickerOptions>({
  props: {
    modelValue: '',
    enableTime: true,
    displayFormat: 'Y-m-d H:i',
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit, attrs }) => {
  onMounted(() => {
    if (!input.value) return
    flatpickr(input.value, {
      enableTime: props.enableTime,
      locale: 'zh',
      hourIncrement: 1,
      time_24hr: true,
      dateFormat: props.displayFormat.replace(/YYYY/g, 'Y').replace(/MM/g, 'm').replace(/DD/g, 'd').replace(/HH/g, 'H').replace(/mm/g, 'i').replace(/SS/g, 'S'),
      enableSeconds: false,
      defaultHour: 0,
    })
    console.log('2:', 2)
  })
  const onChange = (e: Event) => {
    const value = (e.target as HTMLInputElement).value
    emit('update:modelValue', value)
  }
  return () => (
    <input {...attrs} type="text" ref={input} value={props.modelValue} onChange={onChange} />
  )
})
