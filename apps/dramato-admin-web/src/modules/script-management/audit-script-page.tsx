import { createComponent } from '@skynet/shared'
import { useAuditStore } from './audit-script-store'
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { set } from 'lodash-es'
import { Pager, transformNumber, transformTimestamp } from '@skynet/ui'
import { auditStatus } from './constant'

type AuditScriptPageOptions = {
  props: {}
}
export const AuditScriptPage = createComponent<AuditScriptPageOptions>({
  props: {},
}, props => {
  const { onQuery, list, loading, total, Form, params, onReset, Table, columns, onPageChange, onPageSizeChange, getLabels } = useAuditStore()
  const route = useRoute()
  const getQueryParams = () => {
    const query = route.query
    if (query.audit_status) {
      params.value.audit_status = Number(query.audit_status)
    }
    if (query.title_or_key_list) {
      params.value.title_or_key_list = String(query.title_or_key_list)
    }
  }
  onMounted(() => {
    getQueryParams()
    void getLabels()
    void onQuery()
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>剧本审核</li>
          </ul>
        ),
        form: () => (
          <Form
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={params.value}
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            items={[
              ['剧名/ID', 'title_or_key_list', { type: 'text', placeholder: '请输入剧名/ID' }],
              ['公司', 'openplatform_principal', { type: 'text', placeholder: '请输入公司名称' }],
              ['审核状态', 'audit_status', { type: 'select', options: Object.entries(auditStatus).map(([value, label]) => ({ label, value })), placeholder: '请选择状态' }, { transform: transformNumber }],
              [
                ['开始时间：', 'created_start', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
                ['结束时间：', 'created_end', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
              ],
            ]}
          />
        ),
        table: () => (
          <Table
            list={list.value || []}
            columns={columns}
            loading={loading.value}
            class="tm-table-fix-first-column tm-table-fix-last-column"
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={params.value.page_index} v-model:size={params.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default AuditScriptPage
