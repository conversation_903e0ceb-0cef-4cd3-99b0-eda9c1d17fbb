import { ref } from 'vue'
import { StsSign } from './upload-types'
import { apiGetOssSign } from './upload-api'
import axios from 'axios'
import { showFailToast } from '@skynet/ui'

const ossData = ref<StsSign>()
const ossDataLoading = ref(false)
const isUploading = ref<boolean>(false)

const getOssData = async () => {
  if (ossDataLoading.value) return ossData.value
  ossDataLoading.value = true
  try {
    const rs = await apiGetOssSign()
    ossData.value = rs.data
  } catch (error) {
    // TODO: 处理错误
  } finally {
    ossDataLoading.value = false
  }
}

const resetOssData = () => {
  ossData.value = undefined
  ossDataLoading.value = false
}

let failCount = 0

const uploadRequest = (data: FormData, errorTips: string) => {
  if (failCount !== 0) console.log('重试次数', failCount)
  isUploading.value = true
  return new Promise((resolve, reject) => {
    void axios.post(ossData.value?.host || '', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(res => {
      isUploading.value = false
      resolve(null)
    }).catch((error: unknown) => {
      // @ts-expect-error never mind
      if (error.response.status === 403) {
        void getOssData().then(() => uploadRequest(data, errorTips)).then(resolve)
        return
      }
      if (failCount === 3) {
        showFailToast(errorTips)
        isUploading.value = false
        reject()
        return
      }
      failCount = failCount + 1
      void uploadRequest(data, errorTips).then(resolve)
    })
  })
}

export const useUploader = () => {
  return {
    ossData,
    getOssData,
    resetOssData,
    uploadRequest,
    isUploading,
  }
}
