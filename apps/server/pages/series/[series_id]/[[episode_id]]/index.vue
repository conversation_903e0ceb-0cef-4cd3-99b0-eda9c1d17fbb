<template>
  <div>
    <img id="__server-cover-img__" class="w-full absolute object-cover top-1/2 -translate-y-1/2 left-0"
      :src="getCurrentCover(seriesItem?.info, episode_id)">
    <ClientOnly fallback-tag="span">
      <ClientContent :data="seriesItem?.info" :wallet="wallet" />
      <template #fallback>
        <x-back
          class="z-100 nav-top-8 px-3 fixed  left-0 top-0 z-up w-full shrink-0  bg-transparent font-normal text-[#fdfbfc]">
          <x-back-content class="h-11 flex items-center justify-start">
            <img :src="back" class="size-6 shrink-0 ml-0">
            {{ seriesItem?.info?.name }}
          </x-back-content>
        </x-back>
        <x-loading
          class="h-safe-screen absolute z-up left-0 top-0 flex w-full items-center justify-center bg-black/50 text-white">
          <x-loading-circle class="flex size-[120px] items-center justify-center ">
            <img :src="loading" class="h-[100px] w-[66px] animate-pulse">
          </x-loading-circle>
        </x-loading>

        <!-- <x-back
            class="z-100 nav-top-8 px-3 h-11 fixed flex left-0 top-0 z-up w-full shrink-0 items-center justify-start bg-transparent font-normal text-[#fdfbfc]">
            <img :src="back" class="size-6 shrink-0 ml-0" />
            {{ seriesItem?.info.name }}
          </x-back> -->
      </template>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import ClientContent from './ClientContent'
import lruCache from '~/utils/lruCache';
import loading from '~/assets/icons/ic-video-loading.svg'
import back from '~/assets/icons/back.svg'
import { useApi } from '~/composables/useApi'
import type { WalletDataInfo } from '~/modules/profile/profile';

definePageMeta({
  middleware: [
    'auth',
  ],
});

const route = useRoute()
const series_id = computed(() => route.params.series_id.toString() ?? '')
const episode_id = computed(() => route.params.episode_id.toString() ?? '')

const api = useApi()

const p1 = useAsyncData<{ info: M.Dramato }>(
  'seriesItem',
  async () => {
    const nuxtApp = useNuxtApp()
    const cacheKey = `seriesItem-${series_id.value}`
    const cachedData = lruCache.get(cacheKey)
    // 如果有缓存，且是新用户(上下文设置的auth_params，则认为是新用户)，则返回缓存
    if (cachedData && nuxtApp.$auth_params) {
      console.info('use seriesItem cache:', cacheKey)
      return cachedData as { info: M.Dramato }
    }
    const data = await api.request<{ info: M.Dramato }>(`/h5-api/drama/info?series_id=${series_id.value}`)
    if (nuxtApp.$auth_params) {
      console.info('set seriesItem cache:', cacheKey)
      lruCache.set(cacheKey, data)
    }
    return data || {} as { info: M.Dramato }
  }
)

const p2 = useAsyncData<WalletDataInfo>(
  'wallet',
  async () => {
    const nuxtApp = useNuxtApp()
    const cacheKey = `default-wallet`
    const cachedData = lruCache.get(cacheKey)
    // 如果有缓存，且是新用户(上下文设置的auth_params，则认为是新用户)，则返回缓存
    if (cachedData && nuxtApp.$auth_params) {
      console.info('use wallet cache:', cacheKey)
      return cachedData as WalletDataInfo
    }
    const data = await api.request<WalletDataInfo>('/h5-api/wallet/my')
    if (nuxtApp.$auth_params) {
      console.info('set wallet cache:', cacheKey)
      lruCache.set(cacheKey, data)
    }
    return data || {} as WalletDataInfo
  }
)

const [{ data: seriesItem }, { data: wallet }] = await Promise.all([p1, p2])

// if (!seriesItem.value?.info) {
//   throw createError({
//     statusCode: 404,
//     statusMessage: 'Page Not Found'
//   })
// }

// 服务端更新会有延迟，手动根据episode_id获取当前集的封面
const getCurrentCover = (_seriesItem: M.Dramato, _episode_id: string) => {
  if (!_seriesItem) return _seriesItem.episode?.cover
  const episode = _seriesItem.episode_list.find(e => e.id === _episode_id)
  return episode?.cover
}

useSeoMeta({
  title: seriesItem.value?.info.name,
  description: seriesItem.value?.info.desc,
  ogTitle: seriesItem.value?.info.name,
  ogDescription: seriesItem.value?.info.desc,
  ogImage: seriesItem.value?.info.cover,
})

useHead({
  link: [{
    rel: 'preload',
    as: 'image',
    fetchpriority: 'high',
    href: getCurrentCover(seriesItem.value?.info, episode_id.value),
  }],
  script: [{
    src: 'https://business.yingliangads.com/track_v3.js?pid=135&u1=81&u2=&u3=&u4=&pt=fb&id=548733994342278',
    async: true,
    defer: true,
  }]
})
</script>
