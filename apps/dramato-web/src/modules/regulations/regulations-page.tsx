import { createComponent } from '@skynet/shared'
import logo from './source/logo.webp'
import './custom-markdown.css'
import { Button } from '@skynet/ui'
import { useLocale } from 'src/lib/use-locale'
import { jsBridge } from 'src/lib/jsbridge'
import { MdPreview } from 'md-editor-v3'
import { useRouter } from 'vue-router'

export const RegulationsPage = createComponent(null, props => {
  const router = useRouter()
  const language = router.currentRoute.value.query.language as string || 'en'
  const { t } = useLocale(language)

  const acceptAll = async () => {
    const consent = {
      allow_analytics_storage: true,
      allow_ad_storage: true,
      allow_ad_user_data: true,
      allow_ad_personalization_signals: true,
    }
    window.localStorage.setItem('consent', JSON.stringify(consent))
    if (jsBridge.inDramawaveApp) {
      await jsBridge('google_analytics_consent_status', { ...consent, close_page: true })
    }
  }
  return () => (
    <div class="size-full bg-[var(--black)] text-[var(--white)]">
      <x-logo class="mt-2 flex w-full items-center justify-center">
        <img src={logo} class="size-[70px] object-cover" />
      </x-logo>
      <h1 class="my-4 px-3 py-4 text-center text-lg font-normal">{t('regulations.howUseData')}</h1>
      <MdPreview class="regulations-page share-bottom-35 w-full px-3" modelValue={t('regulations.content')} />
      <x-bottom class="z-1000 pc:w-[calc(var(--phone-page-max-width))] pad:w-[calc(var(--phone-page-max-width))] share-bottom-8 fixed bottom-0 flex w-full flex-col items-center justify-center gap-2 bg-[var(--black)] p-3 text-base">
        <Button class="flex h-11 w-full items-center justify-center rounded-lg border-none bg-[var(--grey-12)] text-[var(--white)]" onClick={() => router.push({ path: 'regulations/data-settings', query: { ...router.currentRoute.value.query, from: 'regulations' } })}>{t('regulations.dataSettings')}</Button>
        <Button class="flex h-11 w-full items-center justify-center rounded-lg border-none bg-[var(--brand-6)] text-[var(--white)]" onClick={acceptAll}>{t('regulations.acceptAll')}</Button>
      </x-bottom>
    </div>
  )
})

export default RegulationsPage
