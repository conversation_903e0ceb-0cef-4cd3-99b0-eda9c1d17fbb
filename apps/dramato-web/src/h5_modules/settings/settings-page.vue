<template>
  <x-settings-page class="setting relative flex size-full flex-col gap-y-2 overflow-y-auto bg-[#0b080b] text-white">
    <Back :is-white="true" :title="T.settingsPage.settingText.ref.value"
      class="z-100 sticky top-0 flex h-11 w-full shrink-0 items-center bg-[#0b080b] font-bold" />
    <van-config-provider theme="dark">
      <van-cell-group>
        <van-cell is-link :title="T.settingsPage.termsOfUse.ref.value" @click="gotoRules('terms')" />
        <van-cell is-link :title="T.settingsPage.privacyPolicy.ref.value" @click="gotoRules('privacy')" />
      </van-cell-group>
      <Button v-if="auth_params.user_type !== 0" class="rounded-lg font-bold" block @click="() => {
        showActionSheet = !showActionSheet
      }">
        {{ T.settingsPage.logOut.ref.value }}
      </Button>
    </van-config-provider>
    <van-action-sheet v-model:show="showActionSheet">
      <div class="pb-8.5 flex flex-col items-center justify-center text-[#fdfbfc]">
        <p class="border-b-solid w-full border-b border-b-[#434546] py-5 text-center text-sm">
         {{ T.settingsPage.logOutTips.ref.value }}
        </p>
        <div class="py-4 text-base leading-tight text-[#ff2351]" @click="async () => {
            showActionSheet = !showActionSheet
            logoOutLoading = true;
            await loginOut();
            logoOutLoading = false;
        }">
          {{ T.settingsPage.logOut.ref.value }}
        </div>
        <div class="h-2 w-full bg-[#0b080b]" />
        <div class="py-4 text-sm" @click="() => {
          showActionSheet = false;
        }">
          {{ T.settingsPage.Cancel.ref.value }}
        </div>
      </div>
    </van-action-sheet>
    <div v-if="logoOutLoading" class="flex items-center justify-center mt-20">
      <Loading color="#ff007a" size="38px" />
    </div>
  </x-settings-page>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { Loading, Cell as VanCell, CellGroup as VanCellGroup, Button, ActionSheet as vanActionSheet } from 'vant';
const showActionSheet = ref(false);
import { getUser } from 'src/h5_modules/common/user'
import { Back } from 'src/h5_modules/common/back/back'
import { openedInDev } from '@skynet/shared';
import { userStore } from 'src/h5_modules/common/user-store'
import { T } from './settings-page.i18n'
import { gotoRules } from 'src/h5_modules/common/common'
const auth_params = getUser();

const {loginOut} = userStore();
const logoOutLoading = ref(false);

</script>

<style scoped>
.setting .van-button {
  border: none;
  margin: 2.5rem 12px 0;
  width: calc(100% - 24px);
  border-radius: 0.5rem;
}

.setting .van-hairline--top-bottom:after {
  border-width: 0;
}
</style>