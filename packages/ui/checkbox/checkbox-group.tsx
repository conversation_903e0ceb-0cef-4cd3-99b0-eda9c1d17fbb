import { createComponent, fn } from '@skynet/shared'
import { MergeClass } from '../ui'
import { Checkbox } from './checkbox'
import { VNodeChild } from 'vue'

export type CheckboxGroupProps = {
  modelValue?: SimpleValue[]
  disabled?: boolean
  options?: CheckboxOption[]
  minLength?: number
  maxLength?: number
  checkIf?: null | ((array: SimpleValue[], value: SimpleValue) => boolean)
}

export type CheckboxGroupOptions = {
  props: CheckboxGroupProps
  emits: {
    'update:modelValue': (value: CheckboxGroupProps['modelValue']) => void
    'add:modelValue': (value: SimpleValue) => void
    'remove:modelValue': (value: SimpleValue) => void
  }
}

export type CheckboxOption = { value: SimpleValue, label: VNodeChild | (() => VNodeChild), suffix?: VNodeChild | (() => VNodeChild), disabled?: boolean }
export type SimpleValue = string | number | boolean

export const CheckboxGroup = createComponent<CheckboxGroupOptions>({
  props: {
    modelValue: [],
    disabled: false,
    options: [],
    minLength: 0,
    maxLength: Infinity,
    checkIf: null,
  },
  emits: {
    'update:modelValue': fn,
    'add:modelValue': fn,
    'remove:modelValue': fn,
  },
}, (props, { emit }) => {
  return () => (
    <MergeClass baseClass="flex flex-row flex-wrap">
      {
        props.options.length > 0
          ? props.options.map(row => (
            <Checkbox
              label={row.label}
              suffix={row.suffix}
              disabled={row.disabled}
              modelValue={props.checkIf ? props.checkIf(props.modelValue, row.value) : props.modelValue.includes(row.value)}
              onUpdate:modelValue={(value: boolean, e: Event) => {
                const modelValue = [...props.modelValue]
                if (value && modelValue.length < props.maxLength) {
                  modelValue.push(row.value)
                  emit('add:modelValue', row.value)
                } else if (modelValue.length > props.minLength) {
                  const index = modelValue.findIndex(value => value === row.value)
                  emit('remove:modelValue', row.value)
                  if (index > -1) {
                    modelValue.splice(index, 1)
                  }
                } else {
                  e.preventDefault()
                }
                emit('update:modelValue', modelValue)
              }}
            />
          ))
          : ''
      }
    </MergeClass>
  )
})
