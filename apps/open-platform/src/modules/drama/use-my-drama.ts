import { computed, ref } from 'vue'
import { apiCreateOrUpdateDrama, apiGetTempPath, apiGetDramaList, apiGetDramaDetail, apiUploadDramaResource, apiDeleteDramaResource, apiSubmitAudit } from './my-drama-api'
import { UploadImage } from '../common/uploader/uploader'
import router from 'src/router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

export const useMyDrama = () => {
  const { t } = useI18n()
  const dramaList = ref<M.MyDrama.DramaListItem[]>([])
  const dramaListTotal = ref(0)
  const dramaListLoading = ref(false)
  const dramaDetailLoading = ref(false)
  const dramaCreateLoading = ref(false)
  const dramaListParams = ref<M.MyDrama.DramaListRequest>({
    page_index: 1,
    page_size: 10,
    title: '',
    audit_status: -1,
    create_start_time: undefined,
    create_end_time: undefined,
    created_order: 0,
  })
  const currentDrama = ref<M.MyDrama.DramaGetDetailResource>({
    resource_key: '',
    title: '',
    description: '',
    language_code: '',
    vertical_cover: '',
    count: undefined,
    subtitle_language_code: '',
    unlocked_episodes: undefined,
    auth_start_time: 0,
    auth_end_time: 0,
    video_type: 1,
    audit_status: -1,
  })
  const createOrUpdateDrama = async (data: M.MyDrama.DramaCreateParams) => {
    dramaCreateLoading.value = true
    try {
      const res = await apiCreateOrUpdateDrama({
        ...data,
        count: Number(data.count),
        unlocked_episodes: Number(data.unlocked_episodes),
        auth_start_time: data.auth_start_time / 1000,
        auth_end_time: data.auth_end_time / 1000,
      })
      return res
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'message' in error) {
        ElMessage.error({
          message: error.message as string,
          plain: true,
        })
      }
    }
    dramaCreateLoading.value = false
  }

  const getTempPath = async (data: M.MyDrama.TempPath) => {
    return await apiGetTempPath(data)
  }

  const getDramaDetail = async (resource_key: string) => {
    dramaDetailLoading.value = true
    try {
      const data = await apiGetDramaDetail({
        resource_key,
      })
      if (data.data) {
        currentDrama.value = {
          ...data.data.resource,
          oss_type: data.data.oss_type,
          auth_end_time: data.data.resource.auth_end_time * 1000,
          auth_start_time: data.data.resource.auth_start_time * 1000,
          vertical_cover_for_show: data.data.resource.vertical_cover,
        }
      }
      dramaDetailLoading.value = false
      return data.data
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'message' in error) {
        ElMessage.error({
          message: error.message as string,
          plain: true,
        })
      }
    }
  }

  const getDramaList = async (reset?: boolean) => {
    dramaListLoading.value = true
    try {
      if (reset) {
        dramaListParams.value.page_index = 1
        dramaListParams.value.page_size = 10
      }
      const data = await apiGetDramaList(dramaListParams.value)
      dramaList.value = data.data.list
      dramaListTotal.value = data.data.total
    } catch (error) {
      console.log(error)
    }
    dramaListLoading.value = false
  }

  const checkIsEditable = (status: number) => {
    return [-1, 0, 2].includes(status)
  }

  const getSerialNumber = (fileName: string) => {
    const match = fileName.match(/\d+/)
    if (match) {
      return Number(match[0])
    }
    return -1
  }

  const getVideoResolution = (file: UploadImage): Promise<{
    temp_path: string
    duration: number
    file_path: string
    file_size: number
    serial_number: number
    file_resolution: string
  }> => {
    return new Promise(resolve => {
      const video = document.createElement('video')
      video.preload = 'metadata'
      video.src = file.url as string
      video.onloadedmetadata = () => {
        const width = video.videoWidth
        const height = video.videoHeight
        const resolution = `${width}x${height}`
        const videoInfo = {
          temp_path: file.temp_path || '',
          duration: Math.round(video.duration),
          file_path: file.temp_path || '',
          file_size: file.file?.size || 0,
          serial_number: getSerialNumber(file.file?.name || ''),
          file_resolution: resolution,
        }
        resolve(videoInfo)
      }
    })
  }

  const uploadVideo = async (file: UploadImage) => {
    try {
      const data = await getVideoResolution(file)
      const res = await apiUploadDramaResource({
        ...data,
        oss_type: file.oss_type,
        resource_key: currentDrama.value.resource_key,
        type: 1,
      })
      return res.data
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'message' in error) {
        ElMessage.error({
          message: error.message as string,
          plain: true,
        })
      }
    }
  }

  const uploadSubtitle = async (file: UploadImage) => {
    try {
      const res = await apiUploadDramaResource({
        file_path: file.temp_path || '',
        file_size: file.file?.size || 0,
        serial_number: getSerialNumber(file.file?.name || ''),
        resource_key: currentDrama.value.resource_key,
        type: 2,
        oss_type: file.oss_type,
      })
      return res.data
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'message' in error) {
        ElMessage.error({
          message: error.message as string,
          plain: true,
        })
      }
    }
  }

  const deleteResource = async (data: M.MyDrama.DramaResourceDeleteRequest) => {
    const res = await apiDeleteDramaResource(data)
    if (res.code === 200) {
      ElMessage.success({
        message: t('deleteSuccessfully'),
        plain: true,
      })
    }
  }

  const submitAudit = async (data: M.MyDrama.DramaAuditRequest) => {
    try {
      const res = await apiSubmitAudit(data)
      if (res.code === 200) {
        ElMessage.success({
          message: t('arraignmentSuccess'),
          plain: true,
        })
        void router.push('/my-drama')
      }
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'message' in error) {
        ElMessage.error({
          message: error.message as string,
          plain: true,
        })
      }
    }
  }

  const isEditable = computed(() => {
    return checkIsEditable(currentDrama.value.audit_status!)
  })

  return {
    currentDrama,
    dramaList,
    dramaListParams,
    dramaListLoading,
    dramaDetailLoading,
    dramaCreateLoading,
    dramaListTotal,
    isEditable,
    createOrUpdateDrama,
    getTempPath,
    getDramaList,
    getDramaDetail,
    checkIsEditable,
    uploadVideo,
    uploadSubtitle,
    deleteResource,
    getSerialNumber,
    submitAudit,
  }
}
