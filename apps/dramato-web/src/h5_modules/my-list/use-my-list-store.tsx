import { ref } from 'vue'
import { MyList<PERSON><PERSON> } from './my-list-api'
import { watchHistoryApi } from '../watch-history/watch-history-api'

const viewHistoryList = ref<M.Drama[]>([])

const dramaFeeds = ref<M.Drama[]>([])
const dramaFeedsNext = ref<string>('offset=0')
const hasMoreDramaFeeds = ref<boolean>(true)
const loadingDramaFeeds = ref<boolean>(false)
const isFirstLoading = ref<boolean>(true)

const getViewHistoryList = async () => {
  const res = await watchHistoryApi.getList({ next: 'offset=0' })
  viewHistoryList.value = res.data?.items || []
}

/** 获取首页- tab-Feed */
const getDramaFeeds = async (init = false) => {
  if (!hasMoreDramaFeeds.value) return
  if (loadingDramaFeeds.value) return
  loadingDramaFeeds.value = true
  const res = await MyListApi.getList({ next: dramaFeedsNext.value })
  if (init) {
    dramaFeeds.value = []
  }

  dramaFeeds.value.push(...(res.data?.items || []).filter(item => !!item.id))
  dramaFeedsNext.value = res.data?.page_info.next || ''
  hasMoreDramaFeeds.value = res.data?.page_info.has_more || false
  loadingDramaFeeds.value = false

  if (init) {
    void getDramaFeeds()
  }
}

export const useMyListStore = () => {
  return {
    dramaFeeds,
    getDramaFeeds,
    getViewHistoryList,
    viewHistoryList,
    hasMoreDramaFeeds,
    loadingDramaFeeds,
    dramaFeedsNext,
    isFirstLoading,
  }
}
