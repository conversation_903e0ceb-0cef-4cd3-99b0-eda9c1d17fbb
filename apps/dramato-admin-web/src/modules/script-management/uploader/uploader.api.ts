import { httpClient } from 'src/lib/http-client'

export type StsSign = {
  accessid: string
  host: string
  expire: number
  signature: string
  policy: string
  dir: string
  callback: string
  oss_static_video_prefix: string
  oss_static_prefix: string
}
export const apiGetOssSign = () => {
  return httpClient.post<ApiResponse<StsSign>>('/series_resource/upload/sign', null, {
    headers: {
      'x-oss-meta-content-disposition': 'inline',
      'Content-Disposition': 'inline',
    },
  })
}
// 北京oss上传后同步到美国
export const apiGetResourceOssSign = () => {
  return httpClient.post<ApiResponse<StsSign>>('/series_resource/upload/sign', null, {
    headers: {
      'x-oss-meta-content-disposition': 'inline',
      'Content-Disposition': 'inline',
    },
  })
}
