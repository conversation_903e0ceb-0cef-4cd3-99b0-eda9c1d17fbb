/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, CreateForm, CreateTableOld, Icon, openDialog, showFailToast, showSuccessToast, SvgIcon, transformNumber } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiGetAiVoiceCharacterList, apiUpdateAiVoiceCharacter, apiUpdateCharacterFeature } from './voiceover-role-api'
import { langKey, langValue } from 'src/modules/resource/constant'
import { apiGetFilterTimbre } from 'src/modules/timbre/timbre-api'
import { AudioPlayer } from './audio-player'
import { cloneDeep, set } from 'lodash-es'
import { GENDERS, AGES, TIMBRE_FEATURE } from './constant'
import { z } from 'zod'
import { useValidator } from '@skynet/shared'
import { requiredLabel } from 'src/lib/required-label'
import { ElPagination } from 'element-plus'
// import { ElSelect, ElOption } from 'element-plus'

export const useVoiceoverRole = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    onQuery,
    onReset,
    columns,
    getList,
    getTimbreList,
    cdnUrl,
  }
}

const Form = CreateForm<Api.VoiceoverRole.ListReqParams>()
const params = ref<Api.VoiceoverRole.ListReqParams>({})
const Table = CreateTableOld<Api.VoiceoverRole.VoiceoverRoleItem>()
const list = ref<Api.VoiceoverRole.VoiceoverRoleItem[]>([])
const loading = ref<boolean>(false)
const timbreList = ref<Api.Timbre.TimbreItem[]>([])
const allTimbreList = ref<Api.Timbre.TimbreItem[]>([])
const cdnUrl = 'https://img.tianmai.cn/'
const timbreListLoading = ref(false)
const pageInfo = ref<Api.PageInfo>({
  page: 1,
  page_size: 20,
  total: 0,
})
const keyword = ref<string>('')

const getTimbreList = async (language: string, gender?: number) => {
  timbreListLoading.value = true
  const res = await apiGetFilterTimbre({
    language: language,
    gender: gender || -1,
    page: pageInfo.value.page,
    page_size: pageInfo.value.page_size,
    desc: keyword.value,
  })
  timbreList.value = res.data?.timbre_list || []
  allTimbreList.value = res.data?.timbre_list || []
  timbreListLoading.value = false
  pageInfo.value.total = res.data?.total || 0
}

const filterMethod = (query: string) => {
  if (query) {
    timbreList.value = allTimbreList.value.filter(row => row.desc?.includes(query) || row.title?.includes(query))
  } else {
    timbreList.value = allTimbreList.value
  }
}

const editRolInfo = (row: Api.VoiceoverRole.VoiceoverRoleItem) => {
  const btnLoading = ref(false)
  const Form = CreateForm<Api.VoiceoverRole.VoiceoverRoleItem>()
  const formRules = z.object({
    age: z.string().min(1, '请选择年龄'),
    gender: z.number().min(0, '请选择性别'),
    voice_labels :z.array(z.string()).min(1, '请选择'),
    role_labels:z.array(z.string()).min(1, '请选择'),
  })

  const form = ref<Api.VoiceoverRole.VoiceoverRoleItem>({ ...row })
  const roleLabelOptions = ref<{
    value: string
    label: string
  }[]>(GENDERS.find(item => item.value === row.gender)?.roles || [])
  const { error, validateAll } = useValidator(form, formRules)

  const hideDialog = openDialog({
    title: '编辑角色信息',
    mainClass: 'pb-0 px-5',
    body: () => (
      <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
        <x-status-body>
          <Form
            data={form.value}
            class="flex-col"
            error={error.value}
            onChange={(path, value: any) => {
              if (path === 'gender') {
                roleLabelOptions.value = GENDERS.find(item => item.value === value)?.roles || []
                form.value.role_labels = []
              }
              set(form.value, path, value)
            }}
            actions={[]}
            items={[
              [
                requiredLabel('角色名称'),
                'name',
                {
                  type: 'text',
                  disabled: true
                },
                {
                  class: 'col-span-1',
                },
              ],
              [
                requiredLabel('年龄'),
                'age',
                {
                  type: 'select',
                  options: AGES,
                },
                {
                  class: 'col-span-1',
                },
              ],
              [
                requiredLabel('性别'),
                'gender',
                {
                  type: 'select',
                  options: GENDERS,
                },
                {
                  class: 'col-span-1',
                  transform: transformNumber
                },
              ],
              [
                requiredLabel('角色人设'),
                'role_labels',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: roleLabelOptions.value,
                },
                {
                  class: 'col-span-1',
                },
              ],
              [
                requiredLabel('音色特征'),
                'voice_labels',
                {
                  type: 'multi-select',
                  search: true,
                  popoverWrapperClass: 'z-popover-in-dialog',
                  options: TIMBRE_FEATURE,
                },
                {
                  class: 'col-span-1',
                },
              ],
            ]}
          />
        </x-status-body>
        <x-status-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
          <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
            if (!validateAll()) return
            try {
              btnLoading.value = true
              await apiUpdateCharacterFeature({
                character_id: form.value.character_id,
                gender: form.value.gender,
                age: form.value.age,
                voice_labels: form.value.voice_labels.join(','),
                role_labels: form.value.role_labels.join(','),
              })
              hideDialog()
              showSuccessToast('操作成功')
              void getList()
            } catch (error: any) {
              showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
            } finally {
              btnLoading.value = false
            }
          }}
          >
            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
            保存
          </button>
        </x-status-footer>
      </x-status-confirm-dialog>
    ),
  })
}

const columns = computed(() => [
  { prop: 'name', label: '角色名称', minWidth: 120, fixed: true },

  { prop: 'age', label: '年龄', minWidth: 100, render: (scope: { row: Api.VoiceoverRole.VoiceoverRoleItem }) => {
    return <div class="link link-primary" onClick={() => editRolInfo(scope.row)}>{ scope.row.age || '-暂无-' }</div>
  } },
  { prop: 'gender', label: '性别', minWidth: 100, render: (scope: { row: Api.VoiceoverRole.VoiceoverRoleItem }) => {
    return <div class="link link-primary" onClick={() => editRolInfo(scope.row)}>{ ['未知', '男', '女'][scope.row.gender || 0] }</div>
  } },
  { prop: 'voice_labels', label: '音色特征', minWidth: 100, render: (scope: { row: Api.VoiceoverRole.VoiceoverRoleItem }) => {
    return <div class="link link-primary" onClick={() => editRolInfo(scope.row)}>{ ((scope.row.voice_labels as string[]) || []).join(',') || '-暂无-' }</div>
  } },
  { prop: 'role_labels', label: '角色人设', minWidth: 100, render: (scope: { row: Api.VoiceoverRole.VoiceoverRoleItem }) => {
    return <div class="link link-primary" onClick={() => editRolInfo(scope.row)}>{ ((scope.row.role_labels as string[]) || []).join(',') || '-暂无-' }</div>
  } },

  { prop: 'official_name', label: 'official_name', minWidth: 140 },
  ...langKey.map(lang => ({
    prop: lang,
    label: langValue[langKey.indexOf(lang)],
    minWidth: 140,
    render: (scope: { row: Api.VoiceoverRole.VoiceoverRoleItem }) => {
      const { row } = scope
      const selectedTimbres = ref(row?.timbres || [])
      const index = ref(selectedTimbres.value.findIndex(timbre => timbre.lang === lang))
      return (
        <div class="link link-primary" onClick={() => {
          timbreList.value = []
          const gender = ref(-1)
          void getTimbreList(lang)
          const btnLoading = ref<boolean>(false)
          timbreList.value = allTimbreList.value
          const closeDialog = openDialog({
            title: '配音音色',
            customClass: '!w-[400px]',
            body: () => (
              <div v-loading={timbreListLoading.value}>
                <div class="flex space-x-2">
                  <select value={gender.value} class="select-sm select select-bordered" onChange={async (e: Event) => {
                    gender.value = +(e.target as HTMLSelectElement).value
                    pageInfo.value.page = 1
                    await getTimbreList(lang, gender.value)
                    // filterMethod(keyword.value)
                  }}>
                    <option value={-1}>全部</option>
                    <option value={0}>未知</option>
                    <option value={1}>男</option>
                    <option value={2}>女</option>
                  </select>
                  <input value={keyword.value} class="input input-sm input-bordered w-[300px]" onChange={(e: Event) => {
                    const query = (e.target as HTMLInputElement).value
                    keyword.value = query
                    pageInfo.value.page = 1
                    void getTimbreList(lang, gender.value)
                  }} placeholder="请输入音色名称或者描述" />
                </div>
                <div class="mt-4 h-[450px] overflow-y-auto">
                  {
                    selectedTimbres.value[index.value]?.timbre_id
                      ? (
                          <div class="mb-4 flex cursor-pointer flex-col border border-gray-200 px-4 py-2 hover:bg-[var(--bg-2)]">
                            <div class="flex flex-col gap-y-2 text-sm font-bold">
                              <div class="flex justify-between">
                                <div>{selectedTimbres.value[index.value]?.timbre_name}</div>
                                <div class="badge badge-primary badge-sm">已选</div>
                              </div>
                              <div class="text-sm text-[var(--text-2)]">{allTimbreList.value.find(item => item.timbre_id === selectedTimbres.value[index.value]?.timbre_id)?.desc}</div>
                              <AudioPlayer key={selectedTimbres.value[index.value]?.timbre_id} src={`${cdnUrl}${selectedTimbres.value[index.value]?.sample_path || ''}`} preload="none" />
                            </div>
                          </div>
                        ) : null
                  }
                  {
                    timbreList.value.length > 0
                      ? timbreList.value.map(item => (
                        <div class="flex cursor-pointer flex-col gap-y-2 border border-gray-200 px-4 py-2 hover:bg-[var(--bg-2)]">
                          <div class="flex items-center justify-between text-sm font-bold">
                            <div class="line-clamp-1 flex-1" title={item.title}>{item.title}</div>
                            <div class="flex w-[70px] justify-end">
                              <Button class="btn btn-ghost btn-xs" disabled={btnLoading.value} onClick={async () => {
                                const selectedTimbreId = ref<number | string>(selectedTimbres.value[index.value]?.timbre_id || '')
                                const samplePath = ref<string>(timbreList.value.find(o => o.timbre_id === selectedTimbres.value[index.value]?.timbre_id)?.sample_path || '')
                                selectedTimbreId.value = item.timbre_id || ''
                                samplePath.value = item.sample_path || ''
                                // 检查是否存在相同的音色
                                const isExistSameTimbre = list.value.some(item =>
                                  item.timbres && item.timbres.some(timbre =>
                                    timbre.lang === lang && timbre.timbre_id === selectedTimbreId.value && item.character_id !== row.character_id
                                  )
                                )

                                if (isExistSameTimbre) {
                                  showFailToast('该音色已被其他角色使用')
                                }
                                const voiceoverLangItem = {
                                  lang: lang,
                                  timbre_id: selectedTimbreId.value as number,
                                  timbre_name: allTimbreList.value.find(item => item.timbre_id === selectedTimbreId.value)?.title || '',
                                  sample_path: samplePath.value,
                                  desc: item.desc
                                }
                                btnLoading.value = true
                                try {
                                  await apiUpdateAiVoiceCharacter({
                                    character_id: row.character_id,
                                    timbres: [voiceoverLangItem],
                                  })
                                  void getList(false)
                                  if (!isExistSameTimbre) {
                                    closeDialog();
                                  } else {
                                    if (index.value === -1) {
                                      selectedTimbres.value.push(voiceoverLangItem)
                                      index.value = selectedTimbres.value.length - 1
                                    } else {
                                      selectedTimbres.value.splice(index.value, 1, voiceoverLangItem)
                                    }
                                  }
                                } catch (error: any) {
                                  showFailToast(error.response.data.err_msg || '操作失败')
                                } finally {
                                  btnLoading.value = false
                                }
                              }}>
                                使用
                              </Button>
                            </div>
                          </div>
                          <div class="text-sm text-[var(--text-2)]">{item.desc}</div>
                          <AudioPlayer key={item.timbre_id} src={`${cdnUrl}${item.sample_path || ''}`} />
                        </div>
                      )) : <div class="h-[60px] leading-[60px] text-center text-[var(--text-2)]"> {timbreListLoading.value ? '加载中……' : '暂无数据'}</div>
                  }
                </div>
                <div class="flex justify-end mt-4">
                  <ElPagination onUpdate:current-page={async (page: number) => {
                    pageInfo.value.page = page
                    await getTimbreList(lang, gender.value)
                  }} onUpdate:page-size={async (size: number) => {
                    pageInfo.value.page_size = size
                    await getTimbreList(lang, gender.value)
                  }} pageSize={pageInfo.value.page_size} total={pageInfo.value.total} layout=" prev, pager, next" />
                </div>
              </div>
            ),
          })
        }}>{row.timbres[index.value]?.timbre_name || '设置音色' }</div>
      )
    },
  })),
])

const onReset = () => {
  params.value = {}
  void onQuery()
}

const getList = async (isLoading = true) => {
  try {
    if (isLoading) loading.value = true
    const res = await apiGetAiVoiceCharacterList(params.value)
    list.value = res.data?.character_list || []
  } catch (error) {
    list.value = []
  } finally {
    loading.value = false
  }
}

const onQuery = () => {
  if (!params.value.series_resource_id) {
    showFailToast('请输入剧集ID')
    return
  }
  void getList()
}
