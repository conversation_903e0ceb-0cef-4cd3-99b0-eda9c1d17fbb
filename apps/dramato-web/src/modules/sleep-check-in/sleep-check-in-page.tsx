import { onMounted, ref } from 'vue'
import { createComponent } from '@skynet/shared'
import { jsBridge } from 'src/lib/jsbridge'
import { useLocale } from 'src/lib/use-locale'
import { Back } from 'src/modules/common/back/back'
import Loading from 'src/modules/common/loading/loading'
import { Button } from '@skynet/ui'
import { openDialog } from 'src/modules/common/dialog'
import { useSleepCheckInStore } from './sleep-check-in-store'
import { ApiPostWelfareReceive, apiPostWelfareReclaimReceive, ApiPostWelfareADReceive } from 'src/modules/meal-check-in/meal-check-in-api'
import { showToast } from 'src/modules/common/toast/toast'
import { DialogInfo } from 'src/modules/meal-check-in/meal-check-in.d'
import { ApiPostWelfareActivate } from './sleep-check-in-api'
import { useBetterScroll } from 'src/lib/use-better-scroll'
import { coin, morning_bg, afternoon_bg, night_bg, star_and_coin, star_day, star_night, qa_bg } from './images/images'
import { toastIcon, coin_bg, diamonds } from '../meal-check-in/images/images'
import { preloadImages } from 'src/lib/preload-images'
import { useRouter } from 'vue-router'

export const SleepCheckInPage = createComponent(null, () => {
  const router = useRouter()
  const { t } = useLocale()
  const { sleepDetails, getSleepDetails, getPrivilege, privilege } = useSleepCheckInStore()
  const wrapper = ref<HTMLDivElement>()
  const { bsInstance: explainBsInstance, init: initExplainBs } = useBetterScroll(wrapper)

  preloadImages([toastIcon, coin_bg])

  onMounted(() => {
    // document.title = t('coins.earnings.title')
    // track('myearnings', 'page', 'show')
    void getSleepDetails()
    void getPrivilege()
  })

  const mainBtnClick = async () => {
    if (!sleepDetails.value) return
    const { status } = sleepDetails.value
    let res
    const req = {
      welfare_id: sleepDetails.value.welfare_id,
    }
    switch (status) {
      case 0: // 0: 等待开启
        break
      case 1: // 1:可领取
        res = await ApiPostWelfareReceive(req)
        void getSleepDetails()
        if (res && res.data) {
          openExchangeSuccessDialog(res.data.dialog, res.data.ad_extra.extra)
        }
        break
      case 2: // 2: 待领取
        break
      case 3: // 3: 领取完成
        break
      case 4: // 4:可补领 直接掉起广告
        void jsBridge('playAd', {}).then(() => {
          void apiPostWelfareReclaimReceive(req).then(res => {
            void getSleepDetails()
            if (res && res.data) {
              showToast(
                <div class="min-w-30 flex flex-col items-center justify-center gap-y-3 px-5 py-4">
                  <img class="size-11" width={44} height={44} src={toastIcon} />
                  <p class="text-5 font-bold">{res.data.toast.text}</p>
                </div>,
                'success',
                { class: 'rounded-4 !p-0' }, // 可选的其他属性
              )
            }
          })
        })
        break
      case 5: // 5:可开启
        res = await ApiPostWelfareActivate(req)
        void getSleepDetails()
        if (res && res.data && res.data.toast) {
          showToast(res.data.toast.text)
        }
        break
    }
  }
  const openActiveRulesDialog = () => {
    const closeDialog = openDialog({
      title: t('meal_check_in.rules_title'),
      closeVisible: false,
      customClass: 'bg-[#FDFBFC] rounded-xl shadow',
      body: () => (
        <x-explain-dialog>
          <x-explain ref={wrapper} class="mt-1 block h-[200px] overflow-hidden break-words px-[26px] text-sm font-normal text-[#090609]">
            <x-wrapper id="explain-wrapper" class="block pb-7" style="-webkit-overflow-scrolling: touch;">
              <p class="mb-2 block">{t('meal_check_in.rules_1')}</p>
              <p class="mb-2 block">{t('meal_check_in.rules_2')}</p>
              <p class="block">{t('meal_check_in.rules_3')}</p>
            </x-wrapper>
          </x-explain>
          <x-footer class="relative flex flex-col bg-white">
            <x-mask class="absolute -top-[38px] h-10 w-full bg-gradient-to-b from-[rgba(255,_255,_255,_0)] to-[rgba(255,_255,_255,_1)]" />
            <x-got-it>
              <Button class="no-tap-color mx-4 mb-6 mt-4 h-11 w-[calc(100%-2rem)] rounded-lg border-none bg-[#fc2763] text-base text-white outline-none"
                onClick={() => {
                  closeDialog()
                  explainBsInstance.value?.destroy()
                }}
              >{t('coins.exchange.gotItBtn')}
              </Button>
            </x-got-it>
          </x-footer>
        </x-explain-dialog>
      ),
    })
    initExplainBs()
  }

  const openExchangeSuccessDialog = (dialogMessage: DialogInfo, extra: string, isLast?: true) => {
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${coin_bg})`, backgroundSize: '100% auto' }} class="pt-42.5 mb-4 flex min-h-[266px] w-full flex-col bg-no-repeat shadow">
          <x-body-main class="flex w-full flex-col rounded-lg rounded-t-none bg-white px-4">
            <x-title class="line-clamp-2 w-full break-words pt-5 text-center text-lg font-bold text-[#0b080b] ">{dialogMessage.title || dialogMessage.sub_title}</x-title>
            <x-explain class="flex flex-col items-center pt-2 text-center">
              <x-price class="mx-[6px] block max-w-[calc(100%-12px)] truncate text-2xl font-bold text-[#FC2763]">{dialogMessage.reward_text}</x-price>
              {
                (!!privilege.value && privilege.value > 0) && (
                  <x-bonus class="mt-0.5 flex w-auto max-w-full items-center justify-center gap-0.5 rounded-[200px] bg-amber-100 px-2 py-0.5 text-sm font-bold text-[#5A280A]">
                    <span class="flex-1 shrink-0 truncate">VIP Bonus</span>
                    <img src={diamonds} class="block size-5 object-cover" />
                    <span class="max-w-60px shrink-0 truncate">+{dialogMessage.reward_num * (privilege.value)}</span>
                  </x-bonus>
                )
              }
            </x-explain>
            <x-btn class="no-tap-color mb-6 mt-4 w-full rounded-lg bg-[#fc2763] px-3 py-2.5 text-base font-bold text-white" onClick={() => closeDialog()}>
              <p class="line-clamp-2 w-full break-words text-center" onClick={() => {
                void openAdHandle(extra, isLast)
              }}
              >{dialogMessage.major_btn_txt}
              </p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openAdHandle = (ad_extra: string, isLast?: boolean) => {
    void jsBridge('playAd', {}).then(() => {
      void ApiPostWelfareADReceive({ ad_extra }).then(res => {
        if (res && res.data) {
          if (isLast) {
            showToast(
              <div class="min-w-30 flex flex-col items-center justify-center gap-y-3 px-5 py-4">
                <img class="size-11" width={44} height={44} src={toastIcon} />
                <p class="text-5 font-bold">{res.data.toast.text}</p>
              </div>,
              'success',
              { class: 'rounded-4 !p-0' }, // 可选的其他属性
            )
          } else if (res.data.dialog) {
            openExchangeSuccessDialog(res.data.dialog, res.data.ad_extra.extra, true)
          }
        }
      })
    })
  }

  return () => (
    !sleepDetails.value
      ? <Loading />
      : (
          <x-sleep-check-in-page
            style={{
              backgroundImage: `url(${
              sleepDetails.value.time_period === 1
                ? morning_bg
                : sleepDetails.value.time_period === 2
                ? afternoon_bg
                : night_bg
            })`,
              backgroundSize: '100% 100%',
              backgroundRepeat: 'no-repeat',
            }}
            class="relative flex size-full flex-col gap-y-4 overflow-y-auto bg-[#F2E1D9] text-white"
          >
            <Back isWhite={true} isCloseWebview={true} title={sleepDetails.value.top_show.title}
              class={`z-100 top-0 flex h-11 shrink-0 items-center justify-center px-3 ${
                sleepDetails.value.time_period === 1
                  ? 'bg-[#78acf8]'
                  : sleepDetails.value.time_period === 2
                  ? 'bg-[#77D5FC]'
                  : sleepDetails.value.time_period === 3
                  ? 'bg-[#2D4C7B]'
                  : ''
              }`}
            />
            <x-tips class="break_all block w-full px-8 text-center text-white">
              <x-greetings class="text-8 mb-2 line-clamp-2 w-full font-bold">{sleepDetails.value.greeting_show.title}</x-greetings>
              <x-time class="mb-1 line-clamp-3 w-full text-base">{sleepDetails.value.greeting_show.sub_title}</x-time>
              <x-friendly_tips class="line-clamp-3 w-full text-sm">{sleepDetails.value.greeting_show.bottom_tips}</x-friendly_tips>
            </x-tips>
            <x-sleeping-link-wrap class="z-up flex flex-col items-end pr-6 pt-[44%]">
              <img class="size-9" src={coin} onClick={() => {
                void router.push({ path: '/meal-check-in' })
              }} />
              <x-sleeping-link class="text-2.5 w-13.5 pt-0.6 mr--2 mt--1.5 line-clamp-4 rounded-lg bg-[#feebdf] px-1 text-center font-bold text-[#fe5c00]" onClick={() => {
                void router.push({ path: '/meal-check-in' })
              }}>
                {t('sleep_check_in.earn_link')}
              </x-sleeping-link>
            </x-sleeping-link-wrap>
            <x-rules-link-wrap class="z-up flex flex-col items-end pr-6">
              <x-rules-img class="z-up"><img class="size-9" src={qa_bg} onClick={openActiveRulesDialog} /></x-rules-img>
              <x-rules-link onClick={openActiveRulesDialog} class="z-up w-13.5 text-2.5 mr--2 mt--3 line-clamp-4 w-12 rounded-lg bg-[#feebdf] p-1 text-center font-['SF_Pro'] font-bold text-[#fe5c00]">
                {t('meal_check_in.rules_title')}
              </x-rules-link>
            </x-rules-link-wrap>
            <x-star-and-button class="fixed bottom-[10%] w-full">

              <x-main-txt
                class="mb-[6%] ml-[9%] block"
              >
                <x-txt class="w-46 relative ml-5 block">
                  <p class="rounded-12 line-clamp-5 w-full bg-white p-4 text-center text-xs text-[#fe2d00]">
                    {sleepDetails.value.interactive_broadcast_widget.widget_text}
                  </p>
                  <x-dot class="absolute bottom--2 left-28 size-0 rotate-90 border-x-8 border-b-[16px] border-solid border-transparent border-b-white" />
                </x-txt>
                <img
                  class="block h-[174px] w-auto"
                  src={
                    sleepDetails.value.status === 2 || sleepDetails.value.status === 4
                      ? star_and_coin
                      : sleepDetails.value.time_period === 3
                        ? star_night
                        : star_day
                  }
                />
              </x-main-txt>
              <x-main-footer>
                <x-main-tips onClick={mainBtnClick} class={`rounded-50 mx-8 flex w-[calc(100%-4rem)] shrink-0 items-center justify-center gap-2.5 border border-solid border-[#ffd5a8] px-3 py-2.5 text-base font-bold text-white ${
    [0, 2, 3].includes(sleepDetails.value.status)
      ? 'pointer-events-none bg-[#ffab7b]'
      : 'bg-[#f6510b]'
  }`}
                >
                  <p class="line-clamp-3 w-full text-center">{sleepDetails.value.button_show.btn_txt}</p>
                </x-main-tips>
                <x-care-notice class="mt-2 flex flex-col items-center break-words px-8">
                  <p class="line-clamp-3 w-full text-center text-base text-white">{sleepDetails.value.bottom_show.title}</p>
                </x-care-notice>
              </x-main-footer>
            </x-star-and-button>
          </x-sleep-check-in-page>
        ))
})

export default SleepCheckInPage
