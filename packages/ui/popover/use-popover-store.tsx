import { createCachedFn, createStringId, Key, OptionsToProps } from '@skynet/shared'
import { ref } from 'vue'
import { Popover, PopoverOptions } from './popover'

type Props = OptionsToProps<PopoverOptions>
export const usePopoverStore = createCachedFn((_key: Key) => {
  const popoverQueue = ref<Array<Props>>([])

  const addPopover = (popover: Props) => {
    const id = createStringId()
    popoverQueue.value.push(popover)
    return id
  }
  /**
   * 删除popover
   * @param id
   */
  const removePopover = (id: string) => {
    const index = popoverQueue.value.findIndex(item => item.id === id)
    popoverQueue.value.splice(index, 1)
  }

  const renderPopovers = () => {
    return popoverQueue.value.map(item => (
      <div class="" key={item.id}>
        <Popover {...item} />
      </div>
    ))
  }

  return {
    popoverQueue,
    addPopover,
    removePopover,
    renderPopovers,
  }
})
