import Cookies from 'js-cookie'
// eslint-disable-next-line
import { md5 } from "js-md5";

export const get_k_device_hash_from_cookie = () => {
  const domain1 = location.hostname // 先获取当前访问的全域名
  const rootDomain = domain1.substring(domain1.indexOf('.') + 1)
  let data = Cookies.get('k_device_hash')
  if (data) {
    Cookies.set('k_device_hash', data ?? '', { expires: 365, path: '/', domain: rootDomain })
    return data
  }

  data = md5(navigator.userAgent + setRandomNumberWay())
  Cookies.set('k_device_hash', data, { expires: 365, path: '/', domain: rootDomain })
  return data
}

// 随机生成字符串函数
function setRandomNumberWay() {
  let r
  const data = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z']
  let result = ''
  for (let i = 0; i < 16; i++) {
    r = Math.floor(Math.random() * 36)
    result += data[r]
  }
  return result
}

export const get_k_sso_token = () => {
  return Cookies.get('k-sso-token') || Cookies.get('k_sso_token') || localStorage.getItem('k-sso-token') || localStorage.getItem('k_sso_token')
}
