import { createComponent, mc } from '@skynet/shared'
import { Tab } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import { usePrizeList } from './use-prize-list'
type DrawPrizePageOptions = {
  props: {}
}
export const DrawPrizePage = createComponent<DrawPrizePageOptions>({
  props: {},
}, () => {
  const { fetchPrizeList, renderTable, renderPager, renderTableSearch } = usePrizeList()

  onMounted(() => {
    void fetchPrizeList()
  })

  return () => (
    <x-draw-prize-page class="block">
      <NavFormTablePager class="p-0 m-0" canExpand={false} tableClass="p-0">{{
        nav: () => (
          <Tab class="gap-4" itemClass={active => mc('text-gray-400 py-2 text-lg', active ? 'text-primary' : '')}
            modelValue="prizeList" items={[
              ['prizeList', () => <RouterLink to="/draw-prize">奖品列表</RouterLink>],
              ['prizeRecords', () => <RouterLink to="/draw-prize/record">中奖记录</RouterLink>],
            ]} />
        ),
        form: () => renderTableSearch(),
        table: () => renderTable(),
        pager: () => renderPager(),
      }}</NavFormTablePager>
    </x-draw-prize-page>
  )
})

export default DrawPrizePage
