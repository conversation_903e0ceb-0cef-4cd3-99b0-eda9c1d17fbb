import { createComponent } from '@skynet/shared'
import logo from './source/logo.webp'
import './custom-markdown.css'
import { Button } from '@skynet/ui'
import router from 'src/router'
import { useLocale } from 'src/lib/use-locale'
import { inApp, jsBridge } from 'src/lib/jsbridge'
import { MdPreview } from 'md-editor-v3'
import { Back } from '../common/back/back'

export const RegulationsPage = createComponent(null, props => {
  const language = router.currentRoute.value.query.language as string || 'en'
  const { t } = useLocale(language)

  const acceptAll = async () => {
    const consent = {
      allow_analytics_storage: true,
      allow_ad_storage: true,
      allow_ad_user_data: true,
      allow_ad_personalization_signals: true,
    }
    if (inApp) {
      await jsBridge('google_analytics_consent_status', { ...consent, close_page: true })
    }
  }
  return () => (
    <div class="size-full text-[var(--text-1)]">
      <Back hasBack={false} />
      <x-logo class="flex pt-2 w-full justify-center items-center">
        <img src={logo} class="size-[70px] object-cover" />
      </x-logo>
      <h1 class="text-center px-3 py-4 my-4 text-lg font-normal">{t('regulations.howUseData')}</h1>
      <MdPreview class="w-full px-3 regulations-page share-bottom-35 !bg-[var(--surface-1)] !text-[var(--text-1)]" modelValue={t('regulations.content')} />
      <x-bottom class="bottom-0 fixed z-1000 pc:w-[calc(var(--phone-page-max-width))] pad:w-[calc(var(--phone-page-max-width))] w-full bg-[var(--surface-1)] gap-2 p-3 flex flex-col justify-center items-center text-base share-bottom-8">
        <Button class="w-full h-11 rounded-lg justify-center text-[var(--brand-6)] items-center flex border-none bg-[var(--fill-2)]" onClick={() => router.push({ path: 'regulations/data-settings', query: { ...router.currentRoute.value.query, from: 'regulations' } })}>{t('regulations.dataSettings')}</Button>
        <Button class="w-full h-11 rounded-lg justify-center text-[var(--text-5)] items-center flex bg-[var(--brand-6)] border-none" onClick={acceptAll}>{t('regulations.acceptAll')}</Button>
      </x-bottom>
    </div>
  )
})

export default RegulationsPage
