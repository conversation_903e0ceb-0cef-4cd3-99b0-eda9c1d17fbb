import { createComponent, fn } from '@skynet/shared'
import { CheckboxGroup, SimpleValue } from '@skynet/ui'
import { isEqual, uniq } from 'lodash-es'
import { ref, watch } from 'vue'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ProfileItem = {
  item_code: string
  item_description: string
  item_name: string
  item_value: string
  item_child: ProfileItem[]
}
type ToggleRegExpOptions = {
  props: {
    modelValue: string[]
    valueType: 'number' | 'string'
    profileItem?: ProfileItem | null
  }
  emits: {
    'update:modelValue': (value: string[]) => void
  }
}
export const ToggleRegExp = createComponent<ToggleRegExpOptions>({
  props: {
    modelValue: [''],
    profileItem: null,
    valueType: 'number',
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { emit }) => {
  const items = ref<Record<string, number | string>>({})
  watch(() => {
    return Object.fromEntries(props.modelValue.filter(item => !item.toLowerCase().startsWith('all')).map(item => {
      const child = props.profileItem?.item_child.find(child => {
        const regexp = new RegExp(normalizeRegExp(child.item_value).replace('${}', '[^)]+'))
        if (regexp.test(item)) { return child }
      })
      const regexp = new RegExp(normalizeRegExp(child?.item_value ?? '').replace('${}', '([^)]+)'))
      const match = item.toString().match(regexp)
      return child ? [child.item_value, items.value[child.item_value] ?? match?.[1] ?? 0] : []
    }).filter(item => item.length > 0))
  }, v => {
    if (isEqual(v, items.value)) return // 防止死循环
    items.value = v
  }, { immediate: true })
  const doEmit = (v: string[]) => {
    const value = uniq(v.filter(item => {
      return !(item.startsWith('all') && item !== 'all')
    }))
    console.log('emit:', value)
    emit('update:modelValue', value)
  }
  const onCheck = (template: SimpleValue) => {
    if (template.toString() === 'all') {
      return doEmit([...props.modelValue, 'all'])
    }
    const item = items.value[template.toString()] ?? 0
    return doEmit([...props.modelValue, template.toString().replace('${}', item.toString())])
  }
  const onUncheck = (v: SimpleValue) => {
    if (v.toString() === 'all') {
      return doEmit(props.modelValue.filter(item => item !== 'all'))
    }
    const item = items.value[v.toString()]
    if (item === undefined) return
    const foundIndex = props.modelValue.findIndex(i => {
      const regexp = new RegExp(normalizeRegExp(v.toString()).replace('${}', '[^)]+'))
      return regexp.test(i.toString())
    })
    if (foundIndex < 0) return
    doEmit(props.modelValue.filter((_, index) => index !== foundIndex))
  }
  const onUpdateTemplate = (key: string, value: number | string, combined: SimpleValue) => {
    const regexp = new RegExp(normalizeRegExp(key).replace('${}', '[^)]+'))
    const foundIndex = props.modelValue.findIndex(item => regexp.test(item.toString()))
    if (foundIndex >= 0) {
      const newValue = props.modelValue.filter((_, index) => index !== foundIndex).concat(combined.toString())
      doEmit(newValue)
    }
  }
  watch(() => items.value, n => {
    Object.entries(n).forEach(([key, value]) => {
      const combined = key.replace('${}', value.toString())
      onUpdateTemplate(key, value, combined)
    })
  }, { deep: true })

  const disabled = ref(['/strategy-group/create/target'].includes(location.pathname))

  return () => (
    <div>
      <CheckboxGroup modelValue={props.modelValue}  disabled={disabled.value} onAdd:modelValue={onCheck} onRemove:modelValue={onUncheck}
        checkIf={(array, template) => {
          if (template.toString() === 'all') return array.includes('all')
          return array.some(item => {
            const regexp = new RegExp(normalizeRegExp(template.toString()).replace('${}', '[^)]+'))
            return regexp.test(item.toString())
          })
        }}
        options={props.profileItem?.item_child.map((child, childIndex) => ({
          value: child.item_value,
          label: child.item_name,
          disabled: disabled.value,
          suffix:
          () => (
            <span>
              {child.item_value === 'all'
                ? null
                : (
                    <input disabled={disabled.value} type={props.valueType === 'number' ? 'number' : 'text'} class="input-bordered w-[6em] input input-sm" min={0} max={99999} step={1}
                      value={items.value[child.item_value] ?? 0}
                      onInput={e => {
                        const cons = props.valueType === 'number' ? Number : String
                        const v = cons((e.target as HTMLInputElement).value)
                        items.value[child.item_value] = v
                      }}
                    />
                  )}
            </span>
          )
          ,
        }))}
      />
    </div>
  )
})

const normalizeRegExp = (regexp: string) => {
  return regexp.replace(/\(/g, '\\(').replace(/\)/g, '\\)').replace(/\[/g, '\\[').replace(/\]/g, '\\]')
}
