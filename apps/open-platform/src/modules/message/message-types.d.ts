declare namespace M {
  namespace Message {
    // 消息类型 0: 全部 1: 剧本审核通过/修改/放弃 2: 短剧审核通过/修改 3: 账号信息认证通过/不通过
    enum MessageType {
      ALL = 0,
      SCRIPT = 1,
      SHORT = 2,
      ACCOUNT = 3,
    }
    interface ListParams {
      type: MessageType
      page_index: number
      page_size: number
    }
    interface ListResponse {
      list: Message[]
      total: number
    }

    interface Message {
      id: number
      title: string
      title_en?: string
      content: string
      content_en?: string
      read_status: number // 读取状态，0:未读 1:已读
      created: number
    }

    interface UnreadCountResponse {
      unread_num: number
    }

    interface ReadMessageRequest {
      ids: number[]
    }
  }
}
