import { ref } from 'vue'
import { activityApi } from './activity-api'
import { bindLoading } from '@skynet/shared'

const config = ref<M.ActivityButton[]>([])
const fetchConfig = async (eventId: number) => {
  const data = await bindLoading(activityApi.fetchConfig(eventId), isFetchingConfig)
  config.value = data
}
const isFetchingConfig = ref(false)
export const useActivityStore = () => {
  return {
    config,
    fetchConfig,
    isFetchingConfig,
  }
}
