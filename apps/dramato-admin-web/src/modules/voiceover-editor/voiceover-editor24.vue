<template>
  <div class="timeline-container" ref="container">
    <canvas ref="canvas" @wheel="handleWheel" @mousedown="handleMouseDown"></canvas>
  </div>
</template>

<script lang="tsx" setup>
import { ref, onMounted, onUnmounted } from 'vue'

const container = ref<HTMLDivElement>()
const canvas = ref<HTMLCanvasElement>()
const ctx = ref<CanvasRenderingContext2D>()

// 缩放和平移状态
const state = {
  scale: 1,
  offsetX: 0,
  isDragging: false,
  lastX: 0
}

// 初始化Canvas
onMounted(() => {
  if (!canvas.value || !container.value) return
  
  // 设置Canvas尺寸
  canvas.value.width = container.value.clientWidth
  canvas.value.height = container.value.clientHeight
  
  ctx.value = canvas.value.getContext('2d')!
  draw()
})

// 绘制函数
const draw = () => {
  if (!ctx.value || !canvas.value) return
  
  // 清空画布
  ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height)
  
  // 保存当前状态
  ctx.value.save()
  
  // 应用变换
  ctx.value.translate(state.offsetX, 0)
  ctx.value.scale(state.scale, 1)
  
  // 绘制网格和刻度
  drawGrid()
  
  // 恢复状态
  ctx.value.restore()
}

// 绘制网格和刻度
const drawGrid = () => {
  if (!ctx.value || !canvas.value) return
  
  const width = canvas.value.width
  const height = canvas.value.height
  
  ctx.value.beginPath()
  ctx.value.strokeStyle = '#ddd'
  
  // 绘制水平线
  for (let y = 0; y < height; y += 50) {
    ctx.value.moveTo(0, y)
    ctx.value.lineTo(width, y)
  }
  
  // 绘制垂直线和刻度
  for (let x = 0; x < width * 2; x += 50) {
    ctx.value.moveTo(x, 0)
    ctx.value.lineTo(x, height)
    
    // 绘制刻度文本
    ctx.value.fillStyle = '#666'
    ctx.value.fillText(x.toString(), x, 10)
  }
  
  ctx.value.stroke()
}

// 处理滚轮缩放
const handleWheel = (e: WheelEvent) => {
  if (!canvas.value) return
  
  e.preventDefault()
  
  // 获取鼠标在Canvas中的位置
  const rect = canvas.value.getBoundingClientRect()
  const mouseX = e.clientX - rect.left - state.offsetX
  
  // 计算缩放前鼠标位置对应的内容坐标
  const contentX = mouseX / state.scale
  
  // 更新缩放比例
  state.scale *= e.deltaY > 0 ? 0.9 : 1.1
  state.scale = Math.max(0.1, Math.min(5, state.scale))
  
  // 计算缩放后鼠标位置对应的内容坐标，并调整偏移量保持鼠标位置不变
  const newContentX = mouseX / state.scale
  state.offsetX += (newContentX - contentX) * state.scale
  
  draw()
}

// 处理鼠标拖动
const handleMouseDown = (e: MouseEvent) => {
  state.isDragging = true
  state.lastX = e.clientX
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!state.isDragging) return
    
    const deltaX = e.clientX - state.lastX
    state.offsetX += deltaX
    state.lastX = e.clientX
    
    draw()
  }
  
  const handleMouseUp = () => {
    state.isDragging = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 清理事件监听
onUnmounted(() => {
  // 清理可能的事件监听器
})
</script>

<style scoped>
.timeline-container {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

canvas {
  width: 100%;
  height: 100%;
}
</style>
