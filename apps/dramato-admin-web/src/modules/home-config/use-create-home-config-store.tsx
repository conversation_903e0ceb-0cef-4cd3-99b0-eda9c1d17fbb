import { cloneDeep } from 'lodash-es'
import { ref } from 'vue'

const defaultFormData: M.HomeItemConfig = {
  name: '',
  app_id: undefined,
  language: undefined,
  timing_type: 1,
  timing_time: -1,
  type: undefined,
  category_id: undefined,
  category_ids: [],
  show_title: 1,
  crowd_info: 0,
}
const formData = ref<M.HomeItemConfig>(cloneDeep(defaultFormData))
export const useCreateHomeConfigStore = () => {
  return {
    formData,
    defaultFormData,
  }
}
