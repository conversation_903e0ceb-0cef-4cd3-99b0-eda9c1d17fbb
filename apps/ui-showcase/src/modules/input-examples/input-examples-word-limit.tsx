import { createComponent } from '@skynet/shared'
import { Input } from '@skynet/ui'
import { ref } from 'vue'

export const InputExamplesWordLimit = createComponent(null, () => {
  const name = ref<string>('')
  return () => (
    <div class="space-y-2 p-2 markdown-body">
      <Input
        placeholder="请输入姓名"
        maxlength={20}
        show-word-limit
        class="w-80 h-10 inline-block"
        v-model={name.value}
      />
    </div>
  )
})

export default InputExamplesWordLimit
