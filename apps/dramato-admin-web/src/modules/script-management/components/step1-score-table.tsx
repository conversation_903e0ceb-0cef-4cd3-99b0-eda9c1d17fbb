/* eslint-disable @typescript-eslint/no-explicit-any */
import { Tooltip } from '@skynet/ui'
import { ElTable, ElTableColumn, ElSelect, ElOption } from 'element-plus'
import { ref, computed } from 'vue'

interface ScoreTableProps {
  data: M.ScoreTableDataStep1[]
  onScoreChange?: (data: M.ScoreTableDataStep1[], total: number, rating: string) => void
  disabled?: boolean
}

interface TableColumn {
  property: string
  [key: string]: any
}

export const Step1ScoreTable = (props: ScoreTableProps) => {
  const tableData = ref<M.ScoreTableDataStep1[]>(props.data)
  const totalScore = computed(() => {
    // 如果是 是 就加一份，如果是 否 不加分
    return tableData.value.reduce((sum, item) => sum + (item.score || 0), 0)
  })

  const rating = computed(() => {
    const score = totalScore.value
    if (score >= 6) return '通过'
    return '不通过'
  })

  const handleScoreChange = (value: number, row: M.ScoreTableDataStep1) => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      const newData = [...tableData.value]
      newData[index] = { ...row, score: value }
      tableData.value = newData
      props.onScoreChange?.(newData, totalScore.value, rating.value)
    }
  }

  const spanMethod = ({ row, column, rowIndex }: {
    row: M.ScoreTableDataStep1
    column: TableColumn
    rowIndex: number
  }) => {
    if (column.property === 'category') {
      const prevRow = tableData.value[rowIndex - 1]
      if (prevRow && prevRow.category === row.category) {
        return { rowspan: 0, colspan: 0 }
      }

      let count = 1
      for (let i = rowIndex + 1; i < tableData.value.length; i++) {
        if (tableData.value[i].category === row.category) {
          count++
        } else {
          break
        }
      }
      return { rowspan: count, colspan: 1 }
    }
  }

  return (
    <div class="w-full">
      <ElTable
        data={[...tableData.value, { id: 'total', category: '总评', criteria: '', score: totalScore.value }]}
        border={true}
        spanMethod={spanMethod}
        class="w-full"
        height={505}
      >
        <ElTableColumn
          prop="category"
          label="评估项目"
          width="120"
          v-slots={{
            default: ({ row }: { row: M.ScoreTableDataStep1 }) => row.id === 'total'
              ? (
                  <div class="font-bold">
                    {row.category}
                  </div>
                )
              : row.category,
          }}
        />
        <ElTableColumn
          prop="criteria"
          label="评估维度"
          minWidth="300"
          v-slots={{
            default: ({ row }: { row: M.ScoreTableDataStep1 }) => (
              row.id === 'total'
                ? (
                    <div class="font-bold">
                      {rating.value}
                    </div>
                  )
                : row.desc
                  ? (
                      <Tooltip
                        popContent={() => row.desc}
                        popWrapperClass="!max-w-[400px]"
                      >
                        <span>{row.criteria}</span>
                      </Tooltip>
                    )
                  : (
                      <span>{row.criteria}</span>
                    )
            ),
          }}
        />

        <ElTableColumn
          prop="score"
          label="评估结果"
          width="120"
          v-slots={{
            default: ({ row }: { row: M.ScoreTableDataStep1 }) => row.id === 'total'
              ? (
                  <div class="font-bold">{row.score}</div>
                )
              : (
                  <ElSelect
                    v-model={row.score}
                    disabled={props.disabled}
                    placeholder="请选择"
                    onChange={(value: number) => handleScoreChange(value, row)}
                  >
                    {
                      [{
                        value: 1,
                        label: '是'
                      }, {
                        value: 0,
                        label: '否'
                      }].map(item => (
                        <ElOption
                          key={item.value}
                          label={item.label}
                          value={item.value}
                        />
                      ))
                    }
                  </ElSelect>
                ),
          }}
        />
      </ElTable>
    </div>
  )
}
