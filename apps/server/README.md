# Nuxt Minimal Starter

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install dependencies:

```bash
pnpm install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
pnpm dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.


## 部署cdn

安装腾讯云oss util
```
sudo -v ; curl https://gosspublic.alicdn.com/ossutil/install.sh
```
增加`~/.ossutilconfig`

```
[Credentials]
endpoint: oss-us-east-1.aliyuncs.com
accessKeyID: LTAI5tJrKZ1RKTzQT6BDaYw6
accessKeySecret: ******************************
```

运行pnpm run deploy:test即可上传至cdn

## 服务器权限申请

https://order.tianmai.cn/


实例写 na-test-dramato-server-1 na-prod-dramato-nodeserver-1 na-prod-dramato-nodeserver-2  sg-prod-dramato-nodeserver-1  sg-prod-dramato-nodeserver-2  
一共5台机器
角色 worker 
时间选最长
平台会自动审批
