import { createComponent } from '@skynet/shared'
import { useLocale } from 'src/lib/use-locale'
import { BackButton } from '../common/button/back-button'
import { useRouter } from 'vue-router'
type NotFoundPageOptions = {
  props: {}
}
export const NotFoundPage = createComponent<NotFoundPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const { t } = useLocale(router.currentRoute.value.query.language as string)
  return () => (
    <div class="pad:max-w-[var(--phone-page-max-width)] pc:max-w-[var(--phone-page-max-width)] relative mx-auto flex h-screen max-w-[var(--phone-page-max-width)] flex-col items-center justify-center
      bg-[#0c0a0d] p-8 text-center
      text-white
    "
    >
      <BackButton class="z-up absolute left-0 top-[max(env(safe-area-inset-top),_44px)]" />
      <h1 class="-mt-20 text-2xl font-bold">{t('not_found.title')}</h1>
    </div>
  )
})
