import { ClassName, createComponent, mc } from '@skynet/shared'
import { jsBridge } from 'src/lib/jsbridge'
import { isIos } from 'src/lib/ua'
import { onMounted, ref, VNodeChild } from 'vue'
import { useRouter } from 'vue-router'
import backLight from './images/back-light.svg'
import back from './images/back.svg'
type BackOptions = {
  props: {
    hasBack?: boolean
    isCloseWebview?: boolean
    title?: string | (() => VNodeChild)
    isWhite?: boolean
    class?: ClassName
    backTo?: string
  }
  slots: {
    default?: () => unknown
  }
}
export const Back = createComponent<BackOptions>({
  props: {
    hasBack: true,
    isCloseWebview: false,
    title: '',
    isWhite: false,
    class: '',
    backTo: '',
  },
}, (props, { slots }) => {
  const router = useRouter()
  const topDistance = ref<number>()
  onMounted(async () => {
    if (isIos() || !jsBridge.inDramawaveApp) return
    const deviceInfo = await jsBridge<{ safe_area_height: number }>('getDeviceInfo')
    topDistance.value = (deviceInfo?.safe_area_height ?? 0) / window.devicePixelRatio
  })
  return () => (
    <x-back class={mc(
      'bg-[var(--surface-1)] z-100 px-3 flex items-center static top-0',
      props.title ? 'justify-center' : 'justify-start', isIos() ? 'h-[calc(env(safe-area-inset-top)+2.75rem)] pt-[env(safe-area-inset-top)]' : 'h-11',
      props.class)} style={isIos() ? '' : `padding-top:${topDistance.value}px; height:${(topDistance.value ?? 0) + 44}px`}>
      {
        props.hasBack && (
          <img src={props.isWhite ? back : backLight} class="z-up-up relative left-0 top-0 size-6 shrink-0 object-cover" onClick={() => {
            if (props.isCloseWebview) {
              void jsBridge('close')
            } else if (props.backTo) {
              void router.push(props.backTo)
            } else {
              router.back()
            }
          }}
          />
        )
      }
      {
        typeof props.title === 'string' && (
          <x-title class="text-4  z-up flex flex-1 items-center justify-start truncate font-medium">
            {props.title}
          </x-title>
        )
      }
      {

        typeof props.title === 'function' && props.title()
      }
      {slots.default?.()}
    </x-back>
  )
})
