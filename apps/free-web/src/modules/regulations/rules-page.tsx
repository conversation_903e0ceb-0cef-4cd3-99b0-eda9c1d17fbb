import { createComponent } from '@skynet/shared'
import router from 'src/router'
import { useLocale } from 'src/lib/use-locale'
import right from './source/right.svg'
import { Button } from '@skynet/ui'
import { inApp, jsBridge } from 'src/lib/jsbridge'
import { Back } from '../common/back/back'
type RulesPageOptions = {
  props: {}
}
export const RulesPage = createComponent<RulesPageOptions>({
  props: {},
}, props => {
  const language = router.currentRoute.value.query.language as string || 'en'
  const { t } = useLocale(language)
  return () => (
    <div class="size-full bg-[var(--surface-1)] text-[var(--text-1)]">
      <Back />
      <div class="px-3 py-5 border-b-[1px] border-b-solid border-b-[var(--line-1)] text-center text-base leading-tight">{t('regulations.privacyAndTerms')}</div>
      <x-nav class="flex flex-col text-sm">
        <x-nav-item class="flex justify-between gap-3 px-3 py-4 border-b-[1px] border-b-solid border-b-[var(--line-1)]" onClick={() => router.push({ path: 'privacy', query: router.currentRoute.value.query })}>
          <span>{t('regulations.referToPrivacy')}</span>
          <img src={right} class="w-5 h-5" />
        </x-nav-item>
        <x-nav-item class="flex justify-between gap-3 px-3 py-4 border-b-[1px] border-b-solid border-b-[var(--line-1)]" onClick={() => router.push({ path: 'terms', query: router.currentRoute.value.query })}>
          <span>{t('regulations.referToTerms')}</span>
          <img src={right} class="w-5 h-5" />
        </x-nav-item>
      </x-nav>
      <x-bottom class="bottom-0 fixed z-1000 pc:w-[calc(var(--phone-page-max-width))] pad:w-[calc(var(--phone-page-max-width))] w-full bg-[var(--surface-1)] gap-2 p-3 flex flex-col justify-center items-center text-base share-bottom-8">
        <Button class="w-full h-11 rounded-lg justify-center text-[var(--text-5)] items-center flex bg-[var(--brand-6)] border-none" onClick={() => {
          if (inApp) {
            const consentStr = window.localStorage.getItem('consent')
            const consentFromStorage = consentStr ? JSON.parse(consentStr) as Consent.Consent : null
            if (consentFromStorage) {
              void jsBridge('google_analytics_consent_status', { ...consentFromStorage, close_page: true })
            } else {
              void jsBridge('close')
            }
          } else {
            router.back()
          }
        }}
        >{t('regulations.agreeAndSave')}
        </Button>
      </x-bottom>
    </div>
  )
})

export default RulesPage
