import { createComponent, isEmptyArray } from '@skynet/shared'
import { Button, openDialog, Pager } from '@skynet/ui'
import { RouterLink } from 'vue-router'
import { Empty } from '../common/empty'
import router from 'src/router'
import { usePlayList } from './use-play-list'
import { h, onMounted } from 'vue'
import { PlayItem } from './play-item'
import { Loading } from '../common/loading'
import { ElPagination } from 'element-plus'
import Audit from '../common/audit'
import { useMe } from '../user/use-me'
import { useI18n } from 'vue-i18n'
export const PlayPage = createComponent(null, () => {
  const { t } = useI18n()
  const { fetchList, list, fetchingList, page, pageSize, total } = usePlayList()
  const { me } = useMe()
  onMounted(() => {
    void fetchList({ page_index: 1, page_size: 10 })
  })

  const handleAddPlay = () => {
    if (me.value.auth_status !== 2) {
      const close = openDialog({
        title: t('tip'),
        body: h(Audit, {
          onConfirm: () => {
            void router.push({ path: '/user' })
            close()
          },
          modelValue: true,
        }),
        closeVisible: true,
        customClass: '!w-[400px]',
      })
      return
    }
    void router.push('/play/add')
  }

  return () => (
    <x-play-page class="block">
      <header class="relative flex items-center gap-2 text-2xl font-medium leading-[34px] text-[var(--text-1)]">
        <h2>{t('myShortPlayScript')}</h2>
        <Button onClick={handleAddPlay} class="btn absolute right-0 top-1/2 -translate-y-1/2">{t('uploadNewScript')}</Button>
      </header>
      <main>
        {fetchingList.value
          ? <Loading />
          : isEmptyArray(list.value)
            ? <Empty desc={t('nothingCreatedYet')} button={{ text: t('toCreate'), onClick: () => void router.push('/play/add') }} />
            : (
                <x-play-list class="my-6 block">
                  {list.value.map(item => <PlayItem item={item} />)}
                </x-play-list>
              )}

        <div class="flex items-center justify-end">
          <span class="mr-4 text-sm text-[#A1A0A3]">共{total.value}条</span>
          <ElPagination
            class="justify-end"
            background
            layout="prev, pager, next"
            total={total.value}
            onChange={page => {
              void fetchList({ page_index: page, page_size: pageSize.value })
            }}
          />
        </div>
      </main>
    </x-play-page>
  )
})

export default PlayPage
