import { bindLoading, createComponent, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { coins, dialogBg, vietnamCard } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { ref, watch } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { openDialog } from 'src/modules/common/dialog'
import { useCoinsStore } from '../../coins-store'
import { apiRedeemGift } from '../../coins-api'
import { useRedeem } from '../use-redeem'
type GiftOptions = {
  props: {
    gift?: GiftType
  }
}
interface TaskEventMap {
  [key: number]: string
}

let closeConfirmAccountDialog: Fn
let closeConfirmDialog: Fn

const customLanguage = {
  firstInputTitle: 'Nhập số điện thoại của tài khoả<PERSON>',
  secondInputTitle: '<PERSON><PERSON><PERSON> nhận số điện thoại <PERSON>alo <PERSON>',
  firstInputPlaceholder: 'Số điện thoại Zalo Pay cần bắt đầu bằng \'0\'',
  secondInputPlaceholder: 'Xác nhận số điện thoại Zalo Pay',
  notMatchError: 'Số điện thoại không đúng, vui lòng thử lại',
  emptyError: 'Số điện thoại không được để trống',
}

export const VietnamGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      tags: [], // 礼品标签
      type: 'vietnam',
    },
  },
}, props => {
  const { t } = useLocale()
  const tipsRef = ref<HTMLDivElement>()
  const confirmPhone = ref('')
  const phoneError = ref('')
  const confirmPhoneError = ref('')
  const redeemLoading = ref(false)

  const { account } = useCoinsStore()
  const { commonTitle, openRedeemSuccessDialog, openRedeemFailDialog, reachLimit } = useRedeem(props.gift)

  const openConfirmDialog = () => { // 确认兑换弹窗
    closeConfirmAccountDialog?.()
    closeConfirmDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      mainClass: 'rounded-sm',
      title: commonTitle,
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="line-clamp-2 w-full text-center text-lg font-bold text-[#090609]">{t('coins.exchange.transferXConfirm', { X: props.gift?.amount })}</x-title>
            <x-content class="mb-6 mt-2 w-full text-center text-sm text-[#4d4e4f]">
              {t('coins.exchange.transferToYourAccountX', { X: account.value })}
            </x-content>
            <Button class="h-11 w-full rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={redeemHandler}>{t('coins.exchange.confirmBtn')}</Button>
            <Button class="mt-3 w-full border-none bg-transparent text-base text-[#A1A0A3]" onClick={() => {
              closeConfirmDialog?.()
              openAccountConfirmDialog()
            }}
            >Go Back
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const redeemHandler = () => {
    if (redeemLoading.value) return
    void bindLoading(apiRedeemGift({
      id: props.gift?.id,
      account: account.value,
      type: props.gift?.type,
    }), redeemLoading).then(res => {
      closeConfirmDialog?.()
      if (res.code === 200) {
        openRedeemSuccessDialog()
      }
    }).catch((e: { response?: { data?: { code?: number } } }) => {
      closeConfirmDialog()
      openRedeemFailDialog()
    })
  }

  const handleClick = () => {
    if (props.gift?.can_exchange) {
      if (props.gift?.button_txt && props.gift?.button_link) {
        window.location.href = props.gift.button_link
        return
      }
      if (reachLimit.value) {
        if (account.value && account.value !== '') {
          openConfirmDialog()
        } else {
          openAccountConfirmDialog()
        }
      } else {
        track('exchangezone', 'earncoins_button', 'click')
        window.location.href = 'dramawave://dramawave.app/rewards_task?enter_from=exchange_page'
      }
    }
    trackHandle('click')
  }

  const flag = ref(false)
  watch(() => [tipsRef.value, flag.value], () => {
    flag.value = false
    if (tipsRef.value) {
      const lineHeight = parseFloat(getComputedStyle(tipsRef.value).lineHeight)
      const fontSize = parseFloat(getComputedStyle(tipsRef.value).fontSize)
      if (fontSize <= 8) {
        return
      }
      const maxHeight = lineHeight * 2
      if (tipsRef.value.scrollHeight > maxHeight) {
        tipsRef.value.style.fontSize = `${fontSize - 2 <= 8 ? 8 : fontSize - 2}px`
        flag.value = true
      }
    }
    trackHandle('show')
  })
  const trackHandle = (event: 'click' | 'load' | 'show' | 'successful') => {
    if (props?.gift?.task_id) {
      const taskEventMap: TaskEventMap = {
        10: 'watchreels',
        11: 'checkin',
      }
      const eventName = taskEventMap[props.gift.task_id]

      if (eventName) {
        track(eventName, 'redeemlevel', event, {
          gift_id: props.gift.id,
        })
      }
    }
  }

  const openAccountConfirmDialog = () => {
    const handleAccountBlur = () => {
      if (!account.value) {
        phoneError.value = customLanguage.emptyError
      } else {
        if (account.value.length < 9 || account.value.length > 14 || !/^\d+$/.test(account.value)) {
          phoneError.value = customLanguage.notMatchError
        } else {
          phoneError.value = ''
        }
      }
    }
    const handleConfirmAccountBlur = () => {
      if (confirmPhone.value !== account.value) {
        confirmPhoneError.value = customLanguage.notMatchError
      } else {
        confirmPhoneError.value = ''
      }
    }
    closeConfirmAccountDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      closeVisible: true,
      body: () => (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col gap-y-4">
            <x-title class="truncate text-center text-lg font-bold text-[#090609]">{t('coins.exchange.transferConfirm')}</x-title>
            <x-form>
              <x-form-item class="block text-sm">
                <x-form-label class="font-bold text-black">{customLanguage.firstInputTitle}</x-form-label>
                <input
                  type="number"
                  value={account.value}
                  onBlur={handleAccountBlur}
                  onInput={(e: Event) => {
                    account.value = (e.target as HTMLInputElement).value
                    confirmPhoneError.value = ''
                    handleAccountBlur()
                  }}
                  placeholder={customLanguage.firstInputPlaceholder}
                  class="dramato-input mt-2 bg-transparent py-3 pl-0 text-black"
                />
                <x-line class="border-top-1 block h-px w-full border-0 border-t border-solid border-[#CCCACB]"> </x-line>
                {phoneError.value && <x-error class="line-clamp-2 w-full pt-1 text-xs text-[#FF3B30]">{phoneError.value}</x-error>}
              </x-form-item>
              <x-form-item class="mt-4 block text-sm">
                <x-form-label class="font-bold text-black">{customLanguage.secondInputTitle}</x-form-label>
                <input
                  type="number"
                  value={confirmPhone.value}
                  onBlur={handleConfirmAccountBlur}
                  onInput={(e: Event) => {
                    confirmPhone.value = (e.target as HTMLInputElement).value
                    confirmPhoneError.value = ''
                  }}
                  placeholder={customLanguage.secondInputPlaceholder}
                  class="dramato-input mt-2 bg-transparent py-3 pl-0 text-black"
                />
                <x-line class="border-top-1 block h-px w-full border-0 border-t border-solid border-[#CCCACB]"> </x-line>
                {confirmPhoneError.value && <x-error class="line-clamp-2 w-full pt-[4px] text-xs text-[#FF3B30]">{confirmPhoneError.value}</x-error>}
              </x-form-item>
            </x-form>
            <Button disabled={
              !account.value
              || !confirmPhone.value
              || !!phoneError.value
              || !!confirmPhoneError.value
            } class="mt-4  h-11 w-full  rounded-lg border-none bg-[#fc2763] text-base font-bold text-white disabled:!bg-[#ffa0b6] disabled:text-[#fdfbfc]/50" onClick={() => {
              handleAccountBlur()
              handleConfirmAccountBlur()
              if (phoneError.value || confirmPhoneError.value) return
              openConfirmDialog()
            }}
            >
              {t('coins.exchange.transferX', { X: props.gift?.amount })}
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  return () => (
    <x-gift-item class="flex gap-x-3 p-3">
      <x-gift-item-left class={mc('flex max-w-[44px] flex-col items-center justify-center gap-1', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-card class="box-border size-11 rounded-lg border border-solid border-[#ece9ec] px-1 py-3">
          <img src={props?.gift?.image_url ?? vietnamCard} class="h-auto w-full object-cover" />
        </x-card>
        <x-price class="block w-full break-all text-center text-xs font-bold text-[#4d4e4f]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class={mc('flex flex-1 flex-col gap-1 truncate', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-gift-title class="w-full truncate text-sm text-[#4d4e4f]">{props.gift.name}</x-gift-title>
        <x-tags>
          {props.gift.tags?.map(tag => (
            <x-tag-item class="mr-1 inline-flex items-center justify-center gap-5 rounded border border-solid border-[#ffd9c4] bg-[#fff7ea] px-2 py-1 text-[10px] text-[#fe7123]">{tag}</x-tag-item>
          ))}
        </x-tags>
        {
          props.gift?.can_exchange
          && (
            <x-gift-coins class="flex items-center text-sm">
              <img src={coins} width={16} height={16} class="mr-[2px] size-4 object-contain" />
              <x-gift-amount class="text-[#fc2763]">{props.gift.user_coins}</x-gift-amount>
              <x-separator class="px-[2px] text-[#a1a2a3]">/</x-separator>
              <x-gift-remains class="text-[#090609]">{props.gift.gift_coins}</x-gift-remains>
            </x-gift-coins>
          )
        }
        {
          props.gift?.can_exchange
          && (
            <x-gift-progress>
              <Progress class="mt-1 bg-[#ECE9EC]" value={props.gift.ratio} max={1} />
            </x-gift-progress>
          )
        }
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21 justify-center', props.gift?.button_tips ? 'gap-y-1' : '')}>
        <Button class={mc('min-h-8 w-full rounded-lg border-none px-1 pt-[2px] pb-[3px]  text-sm', !props.gift?.can_exchange ? 'bg-[#FFA0B6] !text-[#FDFBFC80]' : 'bg-[#fc2763] !text-white')} onClick={handleClick}>
          <p class="line-clamp-2 h-auto w-full text-wrap break-words text-center leading-4">
            {props.gift?.button_txt && props.gift?.button_txt !== '' ? props.gift?.button_txt : (reachLimit.value ? t('coins.earnings.exchange') : t('coins.exchange.earnCoins'))}
          </p>
        </Button>
        {props.gift?.button_tips && <x-tips class={mc('line-clamp-2 h-auto w-full break-words text-center text-[10px] leading-3 text-[#4d4e4f]', props.gift?.can_exchange ? '' : 'opacity-30')} ref={tipsRef}>{props.gift?.button_tips}</x-tips>}
      </x-gift-item-right>
    </x-gift-item>
  )
})
