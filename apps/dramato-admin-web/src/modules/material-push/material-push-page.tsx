import { createComponent, useValidator } from '@skynet/shared'
import { Button, TableColumnOld, CreateForm, CreateTableOld, DateTime, openDialog, Pager, showAlert, Switch, transformNumber, transformTimestamp } from '@skynet/ui'
import { useDebounceFn } from '@vueuse/core'
import { cloneDeep, pick, set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { isOurApp, isThirdPartyApp } from 'src/lib/app-helper'
import { keepError } from 'src/lib/http-client'
import { requiredLabel } from 'src/lib/required-label'
import { ScrollTips } from 'src/modules/common/scroll-tips'
import { apiListAvailableMaterial, apiPushMaterial, apiPushToMoboshort, apiUpdateReleaseStatus, apiUpdateThirdKey } from 'src/modules/material-push/material-push-api'
import { PushForm } from 'src/modules/material-push/push-form'
import { dramaStatusOptionsForPush, releaseStatusOptions, seriesStatusOptions } from 'src/modules/options/options'
import { useAppAndLangOptions } from 'src/modules/options/use-app-options'
import { onMounted, ref, watch } from 'vue'
import { RouterLink } from 'vue-router'
import { z } from 'zod'
import { apiGetLanguageList } from '../episode/episode-api'
type MaterialPushPageOptions = {
  props: {}
}
export const MaterialPushPage = createComponent<MaterialPushPageOptions>({
  props: {},
}, props => {
  type Data = Partial<Api.ListAvailableMaterialRequest>
  const Form = CreateForm<Data>()
  const dataReady = ref(false)
  const Table = CreateTableOld<M.AvailableMaterial>()
  const page = ref(0)
  const pageSize = ref(10)
  const formData = ref<Data>({
    app_id: undefined,
    language: [],
    drama_key: '',
    drama_status: undefined,
    series_status: undefined,
    release_status: undefined,
    pushed: 0,
    series_resource_id: '', // 新增根据资源id查询
  })
  const total = ref(0)
  const searching = ref(false)
  const search = async (pageNum?: number) => {
    formDataSnapshot.value = cloneDeep(formData.value)
    searching.value = true
    const response = await apiListAvailableMaterial({
      ...formData.value as Valid,
      page_info: {
        page_index: pageNum || (page.value + 1),
        page_size: pageSize.value,
      },
    }).finally(() => searching.value = false)
    page.value = pageNum || (page.value + 1)
    if (!response.data) return

    tableData.value = response.data.list
    total.value = response.data.total
  }
  const { appOptions } = useAppAndLangOptions(() => undefined, {
    onSuccess: () => {
      dataReady.value = true
      void search()
    },
  })
  watch(appOptions, options => {
    if (options.length > 0 && !formData.value.app_id) {
      formData.value.app_id = [options[0].value]
    }
  })
  const rules = z.object({
    app_id: z.array(z.number()).min(1),
    language: z.array(z.string()).optional(),
    drama_key: z.string().optional(),
    drama_status: z.number().optional(),
    series_status: z.number().optional(),
    release_status: z.number().optional(),
  })
  type Valid = z.infer<typeof rules>
  const { validateAll, error } = useValidator(formData, rules)
  const tableData = ref<M.AvailableMaterial[]>([])
  const formDataSnapshot = ref<Data>(formData.value)
  const onSubmit = () => {
    if (!validateAll()) return
    void search(1)
  }
  const doPush = (data: M.AvailableMaterial) => {
    const keys = (isOurApp(data.app_name)
      ? ['series_key', 'app_id', 'third_key', 'country', 'listing_time'] as const
      : ['series_key', 'app_id', 'third_key', 'country', 'drama_status', 'listing_time'] as const)

    const d = pick(data, ...keys)
    if (isOurApp(data.app_name)) d.listing_time = 0
    return apiPushMaterial(d)
      .then(() => {
        showAlert('更新成功', 'success')
        void search(page.value)
        // const found = tableData.value.find(item => item.series_key === data.series_key)
        // if (!found) return
        // Object.assign(found, data)
        // found.pushed = 1
      }).catch(keepError(err => {
        showAlert('更新失败', 'error')
      }))
  }

  const onClickPush = (row: M.AvailableMaterial) => {
    if ([2, 3].includes(+row.app_id)) {
      const l = languageList.value.find(item => item.code === row.language)?.name || ''
      if (['英语', '日语', '泰语', '韩语', '西语', '葡语', '法语', '印尼语', '中文繁体', '俄语', '德语', '阿语'].includes(l)) {
        let country = ''
        if (l === '英语') {
          country = 'US'
        }
        if (l === '日语') {
          country = 'JP'
        }
        if (l === '泰语') {
          country = 'TH'
        }
        if (l === '韩语') {
          country = 'KR'
        }
        if (l === '西语') {
          country = 'ES'
        }
        if (l === '葡语') {
          country = 'PT'
        }
        if (l === '法语') {
          country = 'FR'
        }
        if (l === '印尼语') {
          country = 'ID'
        }
        if (l === '中文繁体') {
          country = 'TW'
        }
        if (l === '俄语') {
          country = 'RU'
        }
        if (l === '德语') {
          country = 'DE'
        }
        if (l === '阿语') {
          country = 'SA'
        }
        void doPush({ ...row, country })
        return
      }
    }

    const closeDialog = openDialog({
      title: '确认信息',
      body: () => (
        <PushForm languageList={languageList.value} data={row}
          onOk={async (data: M.AvailableMaterial) => { await doPush(data); closeDialog() }}
          onCancel={() => closeDialog()}
        />
      ),
    })
  }
  const onChangeReleaseStatus = (row: M.AvailableMaterial, v: boolean) => {
    if (!formData.value.app_id) return
    const oldValue = row.release_status
    row.release_status = v ? 1 : 0
    void apiUpdateReleaseStatus({
      app_id: row.app_id,
      series_key: row.series_key,
      release_status: v ? 1 : 0,
    }).then(() => {
      showAlert('更新成功', 'success')
    }).catch(err => {
      showAlert('保存失败', 'error')
      row.release_status = oldValue
    })
  }
  const updateThirdKey = useDebounceFn(
    (row: M.AvailableMaterial, value: string) => {
      if (!formData.value.app_id) return
      void apiUpdateThirdKey({
        app_id: formData.value.app_id,
        series_key: row.series_key,
        third_key: value,
      }).then(() => {
        showAlert('保存成功', 'success')
      }).catch(err => {
        showAlert('保存失败', 'error')
      })
    },
    500,
  )
  const pushToMoboshort = async (row: M.AvailableMaterial) => {
    const res = await apiPushToMoboshort({
      app_id: row.app_id,
      series_key: row.series_key,
    })
    if (res.data?.success) {
      showAlert('推送成功', 'success')
      row.moboshort_pushed = 1
    } else {
      showAlert('推送失败', 'error')
    }
  }

  const onUpdateThirdKey = (e: Event, row: M.AvailableMaterial) => {
    const target = e.target as HTMLInputElement
    const value = target.value
    row.third_key = value
    void updateThirdKey(row, value)
  }
  const onChangeDramaStatus = (row: M.AvailableMaterial, value: string) => {
    void doPush({ ...row, drama_status: Number(value) })
  }
  const onChangeListingTime = (row: M.AvailableMaterial, value: number) => {
    void doPush({ ...row, listing_time: value })
  }
  const columns: TableColumnOld<M.AvailableMaterial>[] = [
    ['剧封面', row => (
      <div class="flex items-center gap-2">
        <img src={row.cover} class="max-h-30" />
      </div>
    ), { class: 'w-[140px]' }],
    ['单集拆解进度', 'single_tip', { class: 'w-[18ex]' }],
    ['全集拆解进度', 'summary_tip', { class: 'w-[18ex]' }],
    ['已推送', row => (
      <div class="text-center">{row.pushed === 1 ? <span class="text-green-700">yes</span> : <span class="text-red-400">no</span>}</div>
    ), { class: 'w-[80px]' }],
    ['投放开关', row => (
      <Switch modelValue={row.release_status === 1} onUpdate:modelValue={v => onChangeReleaseStatus(row, v)} labels={{ on: '开', off: '关' }} />
    ), { class: 'w-[80px]' }],
    ['剧集ID', 'series_key', { class: 'w-[18ex]' }],
    ['三方包剧集ID', row => {
      if (isThirdPartyApp(row.app_name)) {
        return <input type="text" class="input input-xs input-bordered max-w-full" value={row.third_key} onChange={e => onUpdateThirdKey(e, row)} />
      }
      return <div> - </div>
    }, { class: 'w-[18ex]' }],
    ['剧名', 'title', { class: 'w-[30ex]' }],
    ['应用', row => (
      <div>{appOptions.value.find(item => item.value === row.app_id)?.label}</div>
    ), { class: 'w-[24ex]' }],
    ['剧集状态', row => (
      <span class="badge badge-outline whitespace-nowrap">{seriesStatusOptions.find(item => item.value === row.series_status)?.label}</span>
    ), { class: 'w-[80px]' }],
    ['上架状态', row => (
      isOurApp(row.app_name)
        ? <span class="badge badge-outline whitespace-nowrap">{dramaStatusOptionsForPush.find(item => item.value === row.drama_status)?.label} </span>
        : (
            <select class="select select-bordered select-xs w-full" value={row.drama_status} onChange={e => onChangeDramaStatus(row, (e.target as HTMLSelectElement).value)}>
              {dramaStatusOptionsForPush.map(item => (
                <option value={item.value}>{item.label}</option>
              ))}
            </select>
          )), { class: row => (!row || isOurApp(row.app_name)) ? 'w-[100px]' : 'w-[180px]' }],
    ['上架时间', row => (
      <input readonly={isOurApp(row.app_name)} disabled={isOurApp(row.app_name)} type="datetime-local" class="input input-bordered input-sm" value={transformTimestamp[0](row.listing_time ?? 0)}
        onChange={e => onChangeListingTime(row, transformTimestamp[1]((e.target as HTMLInputElement).value) ?? 0)}
      />
    ), { class: 'w-[240px]' }],
    ['内嵌字幕语言', row => (
      <div>{languageList.value.find(item => item.code === row.language)?.name}</div>
    ), { class: 'w-[24ex]' }],
    ['剧简介', row => (
      <div class="max-h-[4em] overflow-hidden text-ellipsis line-clamp-3" title={row.desc}>{row.desc}</div>
    ), { class: 'w-[40ex] overflow-auto' }],
    ['单集数量', 'episode_num', { class: 'w-[80px]' }],
    ['已上传剧集', 'upload_num', { class: 'w-[80px]' }],
    ['创建时间', row => (<DateTime value={row.created * 1000} />), { class: 'w-[150px]' }],
    ['更新时间', row => (<DateTime value={row.updated * 1000} />), { class: 'w-[150px]' }],
    [<span class="px-2">操作</span>, row => (
      <div class="flex gap-4 flex-nowrap">
        <a href={`/episode/${row.series_key}`} target="_blank"><Button class="btn-xs btn-outline btn">查看</Button></a>
        <Button class={['btn-xs btn', row.pushed === 0 ? 'btn-primary' : 'btn-outline']} onClick={() => onClickPush(row)}> {row.pushed === 1 ? '重新推送' : '推送数据'} </Button>
        <Button class={['btn-xs btn btn-outline', row.moboshort_pushed === 0 ? '' : 'btn-disabled']} onClick={() => pushToMoboshort(row)}> 推送给畅读 </Button>
        <a href={`/episode-breakdownPage/${row.series_key}/${encodeURIComponent(row.title)}/${row.episode_num}`} target="_blank"><Button class="btn-xs btn-outline btn">AI拆剧本</Button></a>
        <a href={`/clip-material/${row.series_key}`} target="_blank"><Button class="btn-xs btn-outline btn">AI剪素材</Button></a>
      </div>
    ), { class: 'w-[420px]' }],
  ]

  const languageList = ref<M.EpisodeLanguage[]>([])
  onMounted(async () => {
    const rs = await apiGetLanguageList()
    languageList.value = rs.data?.list || []
  })

  return () => (
    <NavFormTablePager>{{
      nav: () => (
        <ul>
          <li><RouterLink to="/material-push">素材推送</RouterLink></li>
        </ul>
      ),
      form: () => (
        <Form
          error={error.value}
          data={formData.value}
          dataReady={dataReady.value}
          onChange={(path, value) => {
            set(formData.value, path, value)
          }}
          onReset={(e, d) => formData.value = d}
          onSubmit={onSubmit}
          items={[ // 应用、内嵌字幕语言、短剧名称/ID、剧集状态、上架状态
            [requiredLabel('应用'), 'app_id', {
              type: 'multi-select',
              options: appOptions.value,
            },
            ],
            ['内嵌字幕语言', 'language', {
              type: 'multi-select', options: languageList.value?.map(item => ({
                label: item.name,
                value: item.code,
              })),
            }],
            ['短剧名称/ID', 'drama_key', { type: 'text' }],
            ['资源名称/ID', 'series_resource_id', { type: 'text' }],
            ['剧集状态', 'series_status', { type: 'select', options: seriesStatusOptions }, { transform: transformNumber }],
            ['上架状态', 'drama_status', { type: 'select', options: dramaStatusOptionsForPush }, { transform: transformNumber }],
            ['投放开关', 'release_status', { type: 'select', options: releaseStatusOptions }, { transform: transformNumber }],
            ['推送状态', 'pushed', { type: 'select', options: [
              {
                label: 'yes',
                value: 2,
              },
              {
                label: 'no',
                value: 1,
              },
              {
                label: '全部',
                value: 0,
              },
            ] }, { transform: transformNumber }],
          ]}
        />
      ),
      tableActions: () => (
        <ScrollTips />
      ),
      table: () => (
        <Table list={tableData.value} columns={columns} class="tm-table-fix-last-column" loading={searching.value} />
      ),
      pager: () => (
        <Pager total={total.value} class="justify-end"
          page={page.value} onUpdate:page={p => search(p)} v-model:pageSize={pageSize.value}
        />
      ),
    }}
    </NavFormTablePager>
  )
})

export default MaterialPushPage
