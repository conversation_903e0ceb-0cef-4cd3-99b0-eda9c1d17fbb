/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, CreateForm, CreateTableOld, Icon, openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { computed, ref, useId } from 'vue'
import { apiGetVoiceOverList, apiPublishVoiceOver, apiUploadVoiceOver, apiGenerateVoiceOver } from './voice-over-api'
import { langKey, langValue, publishLangKey, publishLangValue } from 'src/modules/resource/constant'
import { RouterLink } from 'vue-router'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { requiredLabel } from 'src/lib/required-label'
import { ElLoading, ElSelect, ElOption } from 'element-plus'

export const useVoiceOver = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    onQuery,
    onReset,
    columns,
    total,
    onPageChange,
    onPageSizeChange,
    getList,
  }
}
const defaultParams = {
  page_index: 1,
  page_size: 20,
}
const Form = CreateForm<Api.VoiceOver.ListReqParams>()
const params = ref<Api.VoiceOver.ListReqParams>({ ...defaultParams })
const total = ref<number>(0)
const Table = CreateTableOld<M.IVoiceOverItem>()
const list = ref<M.IVoiceOverItem[]>([])
const loading = ref<boolean>(false)
const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()
const btnLoading = ref<boolean>(false)
const showAllOptions = ref<Record<number, boolean>>({})

// 网盘解析状态 0: 未开始 1：解析中 2：解析失败 3：解析完成
const publishStatusMap = [
  {
    value: 0,
    label: '未开始',
    color: 'default',
  },
  {
    value: 1,
    label: '解析中',
    color: 'warning',
  },
  {
    value: 2,
    label: '解析失败',
    color: 'danger',
  },
  {
    value: 3,
    label: '解析完成',
    color: 'success',
  },
]
const renderStatus = (netDiskData: M.IVoiceOverNetDiskData[]) => {
  const inAnalysis = netDiskData.filter(item => item.netdisk_status === 1)
  const hasAnalysis = netDiskData.filter(item => item.netdisk_status === 3)
  const analysisError = netDiskData.filter(item => item.netdisk_status === 2)
  const uniqueKey = useId()
  const showDesc = inAnalysis.length > 0 || hasAnalysis.length > 0 || analysisError.length > 0
  return (
    <div>
      {showDesc ? (
        <div class="space-y-1 text-xs text-[var(--text-2)]">
          { inAnalysis.length > 0 ? <div>解析中: {inAnalysis.map(row => langValue[langKey.findIndex(lang => row.language_code === lang)]).join('、') || '暂无'}</div> : null }
          { hasAnalysis.length > 0 ? <div>上传成功: {hasAnalysis.map(row => langValue[langKey.findIndex(lang => row.language_code === lang)]).join('、') || '暂无'}</div> : null }
          { analysisError.length > 0 ? <div>解析失败: {analysisError.map(row => langValue[langKey.findIndex(lang => row.language_code === lang)]).join('、') || '暂无'}</div> : null }
        </div>
      ) : '-'}
    </div>
  )
}

const renderGenerateStatus = (row: M.IVoiceOverItem) => {
  const list_1 = [] as string[]
  const list_2 = [] as string[]
  const list_3 = [] as string[]
  row?.ai_voice_data?.forEach(item => {
    if (item.ai_voice_status === 1) {
      list_1.push(item)
    } else if (item.ai_voice_status === 2) {
      list_2.push(item)
    } else if (item.ai_voice_status === 3) {
      list_3.push(item)
    }
  })
  return (
    <div>
      <div class={showAllOptions.value[row.resource_id] ? '' : 'h-12 overflow-hidden'}>
        {list_3.length > 0 ? <div>生成失败: {list_3.map(item => langValue[langKey.findIndex(lang => lang === item.language_code)]).join(',') || '暂无'}</div> : null}
        {list_1.length > 0 ? <div>生成中: {list_1.map(item => langValue[langKey.findIndex(lang => lang === item.language_code)]).join(',') || '暂无'}</div> : null}
        {list_2.length > 0 ? <div>已生成: {list_2.map(item => langValue[langKey.findIndex(lang => lang === item.language_code)]).join(',') || '暂无'}</div> : null}

      </div>
      {list_3.length > 0 || list_1.length > 0 || list_2.length > 0 ? (
        <div class="flex justify-end -mt-2">
          <Button
            class="btn btn-xs btn-link inline-block"
            onClick={() => {
              showAllOptions.value[row.resource_id] = !showAllOptions.value[row.resource_id]
            }}
          >
            { showAllOptions.value[row.resource_id] ? '收起' : '展开' }
          </Button>
        </div>
      ) : null}
    </div>
  )
}
const renderPublishStatus = (publishData: M.IVoiceOverPublishData[]) => {
  const uniqueKey = useId()
  const hasPublishedList = ref<M.IVoiceOverPublishData[]>([])
  const publishingList = ref<M.IVoiceOverPublishData[]>([])
  hasPublishedList.value = publishData.filter(item => {
    return item.publish_status === 2
  })
  publishingList.value = publishData.filter(item => {
    return item.publish_status === 1
  })
  return (
    <div>
      {
        publishingList.value.length === 0 && hasPublishedList.value.length === 0 ? '暂未发布' : (
          <>
            {hasPublishedList.value.length > 0 ? (
              <div>
                已发布: {hasPublishedList.value.map(item => langValue[langKey.findIndex(lang => lang === item.language_code)]).join(',')}
              </div>
            ) : null}
            {publishingList.value.length > 0 ? (
              <div>
                发布中: {publishingList.value.map(item => langValue[langKey.findIndex(lang => lang === item.language_code)]).join(',')}
              </div>
            ) : null}
          </>
        )
      }
    </div>
  )
}

const columns = computed(() => [
  { prop: 'resource_id', label: '资源ID', minWidth: 100, fixed: true },
  { prop: 'resource_name', label: '资源名称', minWidth: 200, fixed: true },
  { prop: 'publishStatus', label: '配音生成状态', align: 'center', minWidth: 200, render: (scope: { row: M.IVoiceOverItem }) => {
    return renderGenerateStatus({...scope.row})
  } },
  { prop: 'publishStatus', label: '已发布配音', align: 'center', minWidth: 120, render: (scope: { row: M.IVoiceOverItem }) => {
    return renderPublishStatus(scope.row.publish_data)
  } },
  { prop: 'publishStatus', label: '发布等待中', align: 'center', minWidth: 120, render: (scope: { row: M.IVoiceOverItem }) => {
    const queueData = scope.row.queue_data

    return (
      <div>
        {
          queueData?.map(t => {
            return langValue[langKey.findIndex(k => k === t.language_code)]
          }).join(',') || '-'
        }
      </div>
    )
  } },

  { prop: 'netdisk_status', align: 'center', label: '网盘解析状态', minWidth: 140, render: (scope: { row: M.IVoiceOverItem }) => {
    return renderStatus(scope.row.netdisk_data || [])
  } },
  { label: '操作', align: 'center', width: 350, fixed: 'right', render: (scope: { row: M.IVoiceOverItem }) => {
    const language_code = ref<string[]>([])
    const netdisk_url = ref('')
    const publishingList = scope.row.publish_data.filter(item => {
      return item.publish_status === 1
    })
    return (
      <div class="flex justify-center space-x-1">
        <Button class="bnt-primary btn-link btn-xs" onClick={() => {
          const selectLangs = ref<string[]>([])
          const createDialog = openDialog({
            title: '自动配音',
            customClass: '!w-[500px]',
            body: () => (
              <x-confirm-publish-dialog class="space-y-4">
                <x-confirm-publish-body class="space-y-4">
                  
                  <div class="flex items-center gap-x-2">
                    {requiredLabel('选择语言:')}
                    <FormMultiSelect
                      search={true}
                      class="w-[300px]"
                      popoverWrapperClass="z-popover-in-dialog"
                      options={langKey.map((n, index) => {
                        return { value: langKey[index], label: langValue[index] }
                      })}
                      modelValue={selectLangs.value}
                      onUpdate:modelValue={e => {
                        selectLangs.value = e as string[]
                      }}
                    />
                    <Button class="btn-primary btn btn-sm" onClick={() => {
                      selectLangs.value = [...langKey]
                    }}>全选</Button>
                  </div>
                </x-confirm-publish-body>
                <x-confirm-publish-footer class="flex w-full justify-end gap-x-[10px]">
                  <Button class="btn-default btn btn-sm" onClick={() => createDialog()}>取消</Button>
                  <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                    if (selectLangs.value.length === 0) {
                      showFailToast('请选择语言')
                      return
                    }
                    const loading = ElLoading.service({
                      lock: true,
                      text: '执行中...',
                      background: 'rgba(0, 0, 0, 0.7)',
                    })
                    try {
                      await apiGenerateVoiceOver({
                        resource_id: scope.row.resource_id,
                        langs: selectLangs.value
                      })
                      showSuccessToast('提交成功')
                      void getList()
                      createDialog()
                    } catch (error) {
                      showFailToast(error.response.data.err_msg || error.response.data.message || '自动配音失败')
                    } finally {
                      loading.close()
                    }
                  }}
                  >
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    确定
                  </Button>
                </x-confirm-publish-footer>
              </x-confirm-publish-dialog>
            ),
          })




          
        }}>自动配音</Button>
        <Button class="bnt-primary btn-link btn-xs" onClick={() => {
          const createDialog = openDialog({
            title: '上传配音',
            customClass: '!w-[400px]',
            body: () => (
              <x-confirm-publish-dialog class="space-y-4">
                <x-confirm-publish-body class="space-y-4">
                  <div class="flex items-center gap-x-2">
                    {requiredLabel('网盘地址:')}
                    <input type="text" class="input input-sm input-bordered w-[200px]" v-model={netdisk_url.value} />
                  </div>
                  <div class="flex items-center gap-x-2">
                    {requiredLabel('上传配音:')}
                    <FormMultiSelect
                      search={true}
                      class="w-[200px]"
                      maxlength={1}
                      popoverWrapperClass="z-popover-in-dialog"
                      options={langKey.map((n, index) => {
                        return { value: langKey[index], label: langValue[index] }
                      })}
                      modelValue={language_code.value}
                      onUpdate:modelValue={e => {
                        language_code.value = e as string[]
                      }}
                    />
                  </div>
                </x-confirm-publish-body>
                <x-confirm-publish-footer class="flex w-full justify-end gap-x-[10px]">
                  <Button class="btn-default btn btn-sm" onClick={() => createDialog()}>取消</Button>
                  <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                    try {
                      if (!netdisk_url.value) {
                        showFailToast('请输入网盘地址')
                        return
                      }
                      if (language_code.value.length === 0) {
                        showFailToast('请选择发布语言')
                        return
                      }
                      btnLoading.value = true
                      await apiUploadVoiceOver({
                        series_resource_id: scope.row.resource_id,
                        language_code: language_code.value[0],
                        netdisk_url: netdisk_url.value,
                      })
                      createDialog()
                      setTimeout(() => {
                        void getList()
                      }, 2000)
                    } catch (error: any) {
                      showFailToast(error.response.data.err_msg || error.response.data.message || '同步失败')
                    } finally {
                      btnLoading.value = false
                    }
                  }}
                  >
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    确定
                  </Button>
                </x-confirm-publish-footer>
              </x-confirm-publish-dialog>
            ),
          })
        }}>上传配音</Button>
        <Button
          class="bnt-primary btn-link btn-xs"
          onClick={() => {
            const langs = ref<string[]>([])
            const audioType = ref<number>()
            const closeDialog = openDialog({
              mainClass: 'px-5 pb-0',
              title: '发布配音',
              customClass: '!w-[400px]',
              body: () => (
                <x-confirm-publish-dialog class="space-y-[25px]">
                  <x-confirm-publish-body class="space-y-4">
                    <div class="flex items-center gap-x-2">
                      {requiredLabel('发布语言:')}
                      <FormMultiSelect
                        search={true}
                        class="w-[200px]"
                        popoverWrapperClass="z-popover-in-dialog"
                        options={publishLangKey.map((n, index) => {
                          return { value: publishLangKey[index], label: publishLangValue[index] }
                        })}
                        modelValue={langs.value}
                        onUpdate:modelValue={e => {
                          langs.value = e as string[]
                        }}
                      />
                      {/* <label class="ml-2 flex items-center">
                      <span class="text-xs text-[var(--text-2)]">全选</span>
                      <Checkbox
                        label=""
                        modelValue={langs.value.length === publishLangKey.length}
                        onUpdate:modelValue={(value: boolean) => {
                          if (value) {
                            langs.value = [...publishLangKey]
                          } else {
                            langs.value = []
                          }
                        }}
                      />
                    </label> */}
                    </div>
                    <div class="flex items-center gap-x-2">
                      {requiredLabel('配音类型:')}
                      <select
                        class="select select-bordered select-sm w-[200px]"
                        value={audioType.value}
                        onChange={(e: Event) => {
                          audioType.value = +(e.target as HTMLSelectElement).value
                        }}
                      >
                        <option value={1}>人工配音</option>
                        <option value={2}>AI 配音</option>
                      </select>
                    </div>
                  </x-confirm-publish-body>
                  <x-confirm-publish-footer class="flex w-full justify-end gap-x-[10px]">
                    <Button class="btn-default btn btn-sm" onClick={() => closeDialog()}>取消</Button>
                    <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                      try {
                        if (langs.value.length === 0) {
                          showFailToast('请选择发布语言')
                          return
                        }
                        if (!audioType.value) {
                          showFailToast('请选择配音类型')
                          return
                        }
                        btnLoading.value = true
                        const res = await apiPublishVoiceOver({
                          resource_infos: [
                            {
                              resource_id: scope.row.resource_id,
                            },
                          ],
                          langs: langs.value,
                          audio_type: audioType.value,
                        })
                        if (res.code !== 200) {
                          showFailToast(res?.message || '发布失败')
                          return
                        }
                        showSuccessToast('发布成功')
                        closeDialog()
                        setTimeout(() => {
                          void getList()
                        }, 2000)
                      } catch (error: any) {
                        showFailToast(error.response.data.err_msg || error.response.data.message || '发布失败')
                      } finally {
                        btnLoading.value = false
                      }
                    }}
                    >
                      {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                      确定
                    </Button>
                  </x-confirm-publish-footer>
                </x-confirm-publish-dialog>
              ),
            })
          }}>发布配音</Button>
        <RouterLink class="btn-primary btn-link btn-xs" to={`/resource-publish/voice-over/detail/${scope.row.resource_id}`}>查看</RouterLink>
        <RouterLink class="bnt-primary btn-link btn-xs" to={`/resource-publish/voice-over/role-mark/${scope.row.resource_id}`}>角色标注</RouterLink>
      </div>
    )
  } },
])

const onReset = () => {
  params.value = { ...defaultParams }
  void onQuery()
}

const getList = async () => {
  try {
    loading.value = true
    const res = await apiGetVoiceOverList(params.value)
    showAllOptions.value = {}
    list.value = res.data?.list || []
    total.value = res.data?.total || 0
    list.value.forEach(item => {
      showAllOptions.value[item.resource_id] = false
    })
  } catch (error) {
    list.value = []
    showAllOptions.value = {}
  } finally {
    loading.value = false
  }
}

const onPageChange = (page: number) => {
  params.value.page_index = page
  void getList()
}

const onPageSizeChange = (size: number) => {
  params.value.page_size = size
  params.value.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_index = 1
  void getList()
}
