import {
  Navigation<PERSON><PERSON>,
  Navigation<PERSON>uardNex<PERSON>,
  NavigationGuardWithThis,
  RouteComponent,
  RouteLocationNormalized,
  Router,
  RouteRecordRaw,
} from 'vue-router'
import { createNumberId, createStringId } from './id-helper'
import { tryParseJson } from './parse-helper'

const defaultTitle = ''

/**
 * 这里故意不导出是因为推荐使用isPc，遵循mobile first原则
 */
// const isPhone = /mobile|android|iphone|ipod|ipad|iemobile|wpdesktop/i.test(
//   navigator.userAgent.toLowerCase?.() || '',
// )
// 屏幕宽度小于 768px 为手机
const screenWidth = import.meta.client ? window.screen.width : 0
const isPhone = screenWidth < 768
export const isPc = !isPhone
const getComponent = (
  components: SupportedComponent,
) => {
  return components instanceof Array ? (isPc ? components[1] : components[0]) : components
}
type SupportedComponent = RouteComponent | [RouteComponent, RouteComponent]
type SupportedLazyComponent = () =>
| Promise<RouteComponent>
| [Promise<RouteComponent>, Promise<RouteComponent>]
const isSupportedLazyComponent = (component: SupportedComponent | SupportedLazyComponent): component is SupportedLazyComponent => typeof component === 'function'
export const redirect = (
  from: string,
  to: string,
) => ({
  path: from,
  redirect: (x: { path: string }) => {
    return { path: to.startsWith('/') ? to : x.path.endsWith('/') ? `${x.path}${to}` : `${x.path}/${to}` }
  },
})
/**
 * 快速创建路由。
 * 必须保证 children 是最后一个参数，这样可以让路由编写在代码格式化以后更加便于阅读。
 * 如果不满足你的需求，你可以直接手写一个 RouteRecordRaw 对象。
 * 维护者不要随意修改参数的个数和顺序，否则会影响到路由编写者的使用体验。
 *
 * @alias r
 */
export const newRoute = (
  _path: string | string[] | { path: string | string[], name?: string },
  title: string,
  _component: SupportedComponent | SupportedLazyComponent | null,
  children?: Array<RouteRecordRaw | RouteRecordRaw[]>,
) => {
  const getPath = (p: string | string[]) => Array.isArray(p) ? p[0] : p
  const path = (_path instanceof Object && 'path' in _path) ? getPath(_path.path) : getPath(_path)
  const alias = Array.isArray(_path) ? _path.slice(1) : []
  const component = _component && (isSupportedLazyComponent(_component) ? () => getComponent(_component()) : getComponent(_component))
  const name = (_path instanceof Object && 'name' in _path) ? _path.name : createNumberId().toString()
  const currentRoute: RouteRecordRaw = {
    path: path,
    name,
    meta: {
      title: title || defaultTitle,
    },
    component,
    children: children?.flat() ?? [],
    alias,
  }
  return currentRoute
}
export const r = newRoute
/**
 * 快速创建路由 - 多个组件
 */
export const newRouteWithComponents = (
  path: string,
  title: string,
  components: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  children?: Array<RouteRecordRaw | RouteRecordRaw[]>,
) => {
  const name = path.replace(/^\//g, '').trim() || createStringId('route').toString()
  const currentRoute: RouteRecordRaw = {
    path,
    name,
    meta: {
      title: title || defaultTitle,
    },
    components,
    children: children?.flat(),
  }
  return currentRoute
}
/**
 * @alias newRouteWithComponents
 */
export const rs = newRouteWithComponents

export type { NavigationGuard, NavigationGuardWithThis }
export type To = RouteLocationNormalized
export type Next = NavigationGuardNext
export type RouteItem = RouteRecordRaw
export type Routes = RouteItem[]

type GetQuery = {
  (key: string, fallback: string, router?: Router): string
  (key: string, fallback: number, router?: Router): number
  (key: string, fallback: boolean, router?: Router): boolean
}
/**
 * 获取查询参数，通过默认值推测类型
 * @example
 * ```ts
 * const id = getQuery('id', 0, router)
 * ```
 */
export const getQuery: GetQuery = <T>(key: string, fallback: T, router?: Router) => {
  const getValue = (raw?: string | null) => {
    if (raw === null || raw === undefined) {
      return fallback
    } else if (typeof fallback === 'string') {
      return raw
    } else if (typeof fallback === 'number') {
      return parseFloat(raw)
    } else if (typeof fallback === 'boolean') {
      return (raw !== '' && raw !== '0' && raw !== 'false' && raw !== 'null' && raw !== 'undefined')
    } else {
      return raw
    }
  }
  if (router) {
    const query = router.currentRoute.value.query
    const raw = query[key]
    return getValue(raw?.toString())
  } else {
    const search = window.location.search
    const query = new URLSearchParams(search)
    const raw = query.get(key)
    return getValue(raw)
  }
}

export const number = Symbol('number')
export const string = Symbol('string')
export const boolean = Symbol('boolean')

/**
 * 获取多个查询参数，通过默认值对象推测类型
 * @example
 * ```ts
 * const { id, name, isActive } = getQueries({
 *   id: 0,
 *   name: '',
 *   isActive: false
 * })
 * ```
 */
export const getQueries = <T extends Record<string, string | number | boolean | unknown[] | Record<string, unknown>>>(fallback: T): T => {
  const search = window.location.search
  const query = new URLSearchParams(search)
  const result = {} as T

  for (const key in fallback) {
    const raw = query.get(key)
    if (raw === null) {
      result[key] = fallback[key]
    } else if (typeof fallback[key] === 'string') {
      result[key] = raw as T[typeof key]
    } else if (typeof fallback[key] === 'number') {
      result[key] = parseFloat(raw) as T[typeof key]
    } else if (typeof fallback[key] === 'boolean') {
      result[key] = (raw !== '' && raw !== '0' && raw !== 'false' && raw !== 'null' && raw !== 'undefined') as T[typeof key]
    } else if (Array.isArray(fallback[key])) {
      // @ts-expect-error ignore
      result[key] = tryParseJson(raw, [])
    } else if (fallback[key] instanceof Object) {
      // @ts-expect-error ignore
      result[key] = tryParseJson(raw, {})
    } else {
      result[key] = raw as T[typeof key]
    }
  }

  return result
}

type ParserFn<T> = {
  (d: T): (v?: string) => T
  (d?: undefined): (v?: string) => T | undefined
}
type ParserFunction<T> = (v?: string) => T

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Parsers = Record<string, ParserFunction<any>>

type ParsedQueries<T extends Parsers> = {
  [K in keyof T]: ReturnType<T[K]>
}
export const createQueryParser = <T>(parse: (v: string) => T): ParserFn<T> =>
  ((d?: T) => (v?: string): T | undefined => {
    if (v === undefined) return d
    try {
      return parse(v)
    } catch {
      return d
    }
  }) as ParserFn<T>

export const queryParsers = {
  string: createQueryParser(v => v),
  int: createQueryParser(v => parseInt(v, 10)),
  float: createQueryParser(v => parseFloat(v)),
  boolean: createQueryParser(v => v === 'true' || v === '1'),
  date: createQueryParser(v => new Date(v)),
  json: createQueryParser(<T>(v: string): T => JSON.parse(v)),
}

/**
 * 获取多个查询参数，通过解析器推测类型
 * @example
 * ```ts
 * const x = getQueriesWithParser({
 *   id: queryParsers.int(undefined),
 *   name: queryParsers.string(''),
 * })
 * x.name // string
 * x.id // number | undefined
 */
export const getQueriesWithParser = <T extends Parsers>(parsers: T): ParsedQueries<T> => {
  const search = window.location.search
  const query = new URLSearchParams(search)
  const result = {} as ParsedQueries<T>

  for (const key in parsers) {
    const raw = query.get(key) ?? undefined
    const v = parsers[key](raw)
    if (v !== undefined) {
      result[key] = parsers[key](raw)
    }
  }

  return result
}

/**
 * 设置单个查询参数，不会导致页面刷新
 * @param key 参数名
 * @param value 参数值
 * @return 返回新的 URL 对象
 * @example
 * ```ts
 * setQuery('id', 1)
 * ```
 */
export const setQuery = (key: string, value: string | number | boolean) => {
  const url = new URL(window.location.href)
  url.searchParams.set(key, value.toString())
  window.history.pushState({}, '', url.toString())
  return url
}

/**
 * 设置多个查询参数，不会导致页面刷新
 * @param queries 要设置的查询参数对象
 * @returns 返回新的 URL 对象
 * @example
 * ```ts
 * setQueries({ id: 1, name: 'skynet' })
 * ```
 */
export const setQueries = (queries: Record<string, string | number | boolean>) => {
  const url = new URL(window.location.href)
  Object.entries(queries).forEach(([key, value]) => {
    value && url.searchParams.set(key, value.toString())
  })
  window.history.pushState({}, '', url.toString())
  return url
}

/**
 * 通过对象生成查询参数字符串，所有 value 自动转义
 * @param queries 要设置的查询参数对象
 */
export const createQueryString = (queries: Record<string, string | number | boolean>) => {
  const p = new URLSearchParams()
  Object.entries(queries).forEach(([key, value]) => {
    value && p.set(key, value.toString())
  })
  return p.toString()
}
