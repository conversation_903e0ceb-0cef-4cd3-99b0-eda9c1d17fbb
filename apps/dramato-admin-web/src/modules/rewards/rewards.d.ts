declare namespace M {
  type Area = {
    name: string
    type: number
    val: string
  }

  type ResetRewardsConfig = {
    id: number
    app_id: number
    area: 'General' | string // 地区：通用、美国等
    action: 2 // 1:保存，2：重置
    area_show_name: string // 地区显示名称
    area_type: number // 地区类型
  }

 type RewardsConfig = {
   id?: number
   app_id?: number
   area: 'General' | string // 地区：通用、美国等
   is_wait_release?: boolean // 是否待发布
   action?: 1 | 2 | number // 1:保存，2：重置
   area_show_name?: string // 地区显示名称
   area_type?: number // 地区类型
   check_in?: M.RewardsConfig.CheckIn
   special_offers?: {
     list: M.RewardsConfig.SpecialOffer[]
   }
   watch_ad?: {
     list: (M.RewardsConfig.WatchAd & { reward_coins?: number })[]
   }
   white_user?: {
     list: string[]
   }
 }

 namespace RewardsConfig {

    type CheckInBase = {
      day: number
      reward_coins?: number
    }

    type SpecialOffer = Partial<Pick<M.ApplicationDramaItem, 'sort_no' | 'series_key' | 'title' | 'listing_time' | 'episodes_number' | 'episodes_price' | 'start_paying_episodes'>> & {
      discount_rate?: number
      discount_price?: number
    }

    type CheckInExtra = {
      ad_id: number
      ad_name: string
      ad_group: string
      type: string
      coins: number
      mediation_mode: string
      reward_coins?: number
      duration: string
      limit: number
    }

    type WatchAd = {
      sort_no?: number // 排序
      id?: number // 唯一标识
      ad_id: number
      ad_name: string
      ad_group: string
      type: string
      coins: number
      mediation_mode: string
      duration: string
      limit: number
      reward_coins?: number
      ad_type?: string// 广告类型,rewarded，interstitial，native，app_open，banner
      ad_platform?: string// 广告聚合平台，admob,max
      key?: string // 唯一标识
      secondKey?: string // 唯一标识
      ad_unit?: string
      platform?: string
    }

    type CheckIn = {
      base: CheckInBase[]
      extra?: CheckInExtra[]
    }
 }
}
