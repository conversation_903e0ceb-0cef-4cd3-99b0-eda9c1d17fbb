<template>
  <Swipe
    class="my-swipe"
    :autoplay="3000"
    @change="onChange"
  >
    <SwipeItem
      v-for="(item, k) in items"
      :key="k"
    >
      <Banner
        :no-tag="noTag"
        :tags="getTagInfo(item)"
        :name="item.title || ''"
        :image="item.cover || coverDefBg"
        @click="() => goToPlay(item)"
      />
    </SwipeItem>
    <template #indicator="{ active, total }">
      <div class="custom-indicator">
        <div
          :style="{ width: 1 / total * 50 + 'px', transform: `translateX(${(active) / total * 50}px)` }"
          class="inner"
        />
      </div>
    </template>
  </Swipe>
</template>

<script setup lang="ts">
import { Swipe, SwipeItem } from 'vant';
import { useRouter } from 'vue-router';
import Banner from './banner.vue';
import coverDefBg from 'src/h5_modules/images/banner.webp'

const router = useRouter();

const props = defineProps<{
  items: M.Drama[]
  noTag?: boolean
}>()

const emit = defineEmits<{
  (e: 'change', index: number): void
}>()

const onChange = (index: number) => {
  emit('change', index)
}

const goToPlay = (item: M.Drama) => {
  // router.push('/series/' + item.key)
  window.location.href = window.location.origin + '/series/' + item.key + '?return_to=/&from_page=home'
}

const getTagInfo = (item: M.Drama) => {
  return item.content_tags?.map((tag, index) => {
    return {
      name: tag,
      color: index % 2 === 1 ? 'linear-gradient(270deg, #04B587 0.05%, #46D988 100%)' : 'linear-gradient(90deg, #F47040 0%, #F52067 100%)'
    }
  }) || []
}


</script>

<style>
.my-swipe {
  overflow: hidden;
  border-radius: 8px;
}

.my-swipe .van-swipe-item {
  color: #fff;
}

.custom-indicator {
  position: absolute;
  width: 50px;
  height: 3px;
  border-radius: 8px;
  right: 12px;
  bottom: 12px;
  background: #FDFBFC33;

  .inner {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    border-radius: 8px;
    background: #FDFBFC;
    transition: width 0.3s;
  }
}
</style>