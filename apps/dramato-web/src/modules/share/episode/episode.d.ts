declare namespace Episode {
  interface Episode {
    id: string
    name: string
    cover: string
    desc: string
    finish_status: number // // 状态： -1 已下架 1 未上架 2 待上架 3 已上架 4 待下架
    episode_list: [
      {
        id: string
        name: string
        cover: string
        video_url: string
        m3u8_url: string
        video_type: string
        external_audio_h265_m3u8: string
        external_audio_h264_m3u8: string
        subtitle_list: {
          language: string
          type: string
          subtitle: string
        }[]
        region: number[]
      },
    ]
  }
}
