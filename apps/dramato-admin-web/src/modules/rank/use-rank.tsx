/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { apiCreateRank, apiEditRank, apiGetRankInfo, apiGetRankList, apiSaveRankInfo } from './rank-api'
import { RankForm } from './rank-form'

export const useRank = () => {
  return {
    Form,
    params,
    Table,
    list,
    loading,
    search,
    showADDialog,
    closeRankDialog,
    currentRank,
    onCreateBtnClick,
    onEditBtnClick,
    onCreate,
    onEdit,
    isUpdating,
    onGetRankInfo,
    onSaveRankInfo,
    RankInfoForm,
    currentRankInfo,
  }
}

const Form = CreateForm<M.Rank.params>()
const params = ref<M.Rank.params>({
  type: 1,
})

const RankInfoForm = CreateForm<M.Rank.RankInfo>()

const Table = CreateTableOld<M.Rank.Rank>()
const list = ref<M.Rank.Rank[]>([])
const loading = ref<boolean>(false)
const isUpdating = ref(false)

const currentRank = ref<M.Rank.CreateRank>({
  // series_info: [{}],
  type: 2,
})
const currentRankInfo = ref<M.Rank.RankInfo>({})

const search = async (_page?: number) => {
  try {
    loading.value = true
    const res = await apiGetRankList({
      pid: currentRankInfo.value?.id || 0 })
      .finally(() => {
        loading.value = false
      })
    list.value = res.data?.list || []
  } catch (error) {
    list.value = []
  }
}

const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[800px] overflow-hidden'

const closeRankDialog = ref()

const showADDialog = () => {
  closeRankDialog.value = openDialog({
    title: currentRank.value?.id ? '编辑Rank' : '新建Rank',
    body: () => <RankForm />,
    mainClass: dialogMainClass,
    customClass: '!w-[800px] overflow-hidden',
  })
}

const onCreateBtnClick = () => {
  currentRank.value = {
    // series_info: [{}],
    type: 2,
  }
  showADDialog()
}

const onEditBtnClick = (r: M.Rank.Rank) => {
  currentRank.value = {
    ...r,
  }
  showADDialog()
}

const onEditSuccess = (isCreate?: boolean) => {
  isUpdating.value = false
  closeRankDialog.value && closeRankDialog.value()
  if (isCreate) {
    void search(1)
    return
  }
  void search()
}

const switchRequestParams = () => ({
  ...currentRank.value,
  pid: currentRankInfo.value?.id,
  auto_type: !(params.value.type === 2 && currentRank.value.type === 2) ? undefined : currentRank.value.auto_type,
  tag_id_list: !(params.value.type === 1 && currentRank.value.type === 2) ? [] : currentRank.value.tag_id_list
})

const onCreate = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiCreateRank(switchRequestParams())
    showAlert('创建成功')
    onEditSuccess(true)
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '创建失败', 'error')
  }
}

const onEdit = async () => {
  try {
    if (isUpdating.value === true) {
      return
    }
    isUpdating.value = true
    await apiEditRank(switchRequestParams())
    showAlert('编辑成功')
    onEditSuccess()
  } catch (error: any) {
    isUpdating.value = false
    showAlert(error.response.data.message || '编辑失败', 'error')
  }
}

const onGetRankInfo = async (data: { type: number, app_id: number, language_version: string }) => {
  try {
    const rs = await apiGetRankInfo(data)
    currentRankInfo.value = rs.data?.info || {}
  } catch (error: any) {
    // showAlert(error.response.data.message || '', 'error')
    currentRankInfo.value = {}
  }
}

const onSaveRankInfo = async () => {
  try {
    if (!currentRankInfo.value) {
      return
    }
    await apiSaveRankInfo({
      ...currentRankInfo.value,
      app_id: params.value.app_id || 0,
      language_version: params.value.language_version || '',
      type: params.value.type || 1,
    })
    showAlert('更新成功')
    void onGetRankInfo({
      type: params.value.type || 1,
      app_id: params.value.app_id || 0,
      language_version: params.value.language_version || '',
    })
  } catch (error: any) {
    showAlert(error.response.data.message || '更新失败', 'error')
  }
}
