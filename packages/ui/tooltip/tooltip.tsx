import { ClassName, createComponent, required, SlotFn } from '@skynet/shared'
import { isVNode, onUnmounted, ref, Text, VNode } from 'vue'
import { usePopover2 } from '../popover/use-popover-2'
import { useExpose } from '../use/use-expose'

type VNodeChildAtom = VNode | string | number | boolean | null | undefined | void
type TooltipOptions = {
  props: {
    class?: never | null
    popContent: SlotFn
    popWrapperClass?: ClassName
    placement?: 'top' | 'bottom' | 'left' | 'right' | 'bottom-start'
    triggerType?: 'hover' | 'click' | 'focus'
    offset?: number
  }
  slots: {
    default: SlotFn
  }
}
export const Tooltip = createComponent<TooltipOptions>({
  props: {
    popContent: required,
    popWrapperClass: '',
    placement: 'top',
    triggerType: 'hover',
    offset: 10,
    class: null,
  },
}, (props, { slots }) => {
  const triggerRef = ref<HTMLElement | null | VNode>(null)
  const { destroy, toggle } = usePopover2({
    triggerElement: () => {
      const n = triggerRef.value
      if (!n) return null
      return isVNode(n) ? (n.el as HTMLElement) : n
    },
    class: props.popWrapperClass,
    content: props.popContent,
    placement: props.placement,
    offset: props.offset,
    triggerType: props.triggerType,
  })
  onUnmounted(() => {
    destroy()
  })

  useExpose({
    toggle,
  })

  return () => {
    const node = slots.default?.()
    // 如果 node 是数组，则取第一个元素
    const first = (node instanceof Array ? node[0] : node) as VNodeChildAtom
    if (first === undefined || first === null) {
      return null
    } else if (typeof first === 'string' || typeof first === 'number' || typeof first === 'boolean') {
      return <x-tooltip ref={triggerRef}>{node}</x-tooltip>
    } else if (first.type === Text) {
      return <x-tooltip ref={triggerRef}>{first}</x-tooltip>
    } else {
      triggerRef.value = first
      return first
    }
  }
})

export default Tooltip
