import { httpClient } from 'src/lib/http-client'

export const apiGetHomeModuleList = (data: M.HomeConfigSearchProps) =>
  httpClient.post<ApiResponse<{
    list: M.HomeItemConfig[]
    total: number
  }>>('/homepage/module_config/list', data)

export const apiGetHomeConfig = (id: number) =>
  httpClient.get<ApiResponse<{
    module_info: M.HomeItemConfig
  }>>('/homepage/module_config/get?id=' + id)

export const apiHomeConfigRelease = (data: M.HomeConfigRelease) => {
  return httpClient.post<ApiResponse>('/homepage/module_config/release', data)
}

export const apiSaveHomeConfig = (data: M.HomeItemConfig) => {
  return httpClient.post<ApiResponse>('/homepage/module_config/save', data)
}

export const apiSortHomeConfig = (data: M.HomeConfigSort) => {
  return httpClient.post<ApiResponse>('/homepage/module_config/sort/update', data)
}

export const apiGetHomeConfigCategoryList = (data: M.HomeConfigCategorySearchProps) => {
  return httpClient.post<ApiResponse<{
    list: M.ContentTemplate[]
  }>>('/homepage/module_config/category/list', data)
}

export const apiGetHomepagePreview = (data: M.HomeConfigPreviewProps) => {
  return httpClient.post<ApiResponse<{
    list: M.HomepageUnit[]
  }>>('/homepage/module_config/preview', data)
}

export const homeConfigApi = {
  getTabContentKeys: () => httpClient.get<ApiResponse<{
    list: Array<{ label: string, value: string }>
  }>>('/homepage/module_config/tab_content/keys', null, {
    transformResponseData: {
      'data.list': [(list: Array<{ name: string, value: string }>) => {
        return list.map(item => ({ label: item.name, value: item.value }))
      }],
    },
  }),
}
