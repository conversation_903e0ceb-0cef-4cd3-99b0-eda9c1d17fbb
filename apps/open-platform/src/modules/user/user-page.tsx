import { Button, openDialog, SvgIcon } from '@skynet/ui'
import { ElForm, ElFormItem, ElImage, ElInput, ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { computed, defineComponent, onBeforeMount, ref } from 'vue'
import { Uploader } from '../common/uploader/uploader'
import { useMyDrama } from '../drama/use-my-drama'
import { useMe } from './use-me'
import ChangePhoneNumber from 'src/modules/user/change-phone-number'
import { closeDialogIcon } from 'src/modules/common/close-dialog-icon'
import CompanyVerification from 'src/modules/user/company-verification'
import { useI18n } from 'vue-i18n'

interface EditingMe extends M.User {
  avatarForShow?: string
}

export default defineComponent({
  name: 'UserPage',
  setup() {
    const { me, fetchMe, updateMe } = useMe()
    const editingMe = ref<EditingMe>({} as EditingMe)
    const isEdit = ref(false)
    const { getTempPath } = useMyDrama()
    const { t } = useI18n()
    onBeforeMount(() => {
      void fetchMe()
    })

    const startEdit = () => {
      editingMe.value = cloneDeep(me.value)
      isEdit.value = true
    }

    const cancelEdit = () => {
      isEdit.value = false
      editingMe.value = {} as M.User
    }

    const handleSubmit = async () => {
      await updateMe(editingMe.value)
      isEdit.value = false
    }

    const statusMap = {
      0: {
        label: t('uncertified'),
        textColor: '#A1A0A3',
        bgColor: '#F3F0F3',
      },
      1: {
        label: t('uncertified'),
        textColor: '#A1A0A3',
        bgColor: '#F3F0F3',
      },
      2: {
        label: t('certified'),
        textColor: '#1DD864',
        bgColor: '#1DD86426',
      },
      3: {
        label: '审核失败',
        textColor: '#FC2763',
        bgColor: '#FFF0F3',
      },
    }
    const currentStatusConfig = computed(() => {
      return statusMap[me.value.auth_status as keyof typeof statusMap] || {}
    })
    const onChangePhone = () => {
      const closeDialog = openDialog({
        title: '手机号修改',
        body: () => <ChangePhoneNumber />,
        customClose: () => closeDialogIcon(closeDialog),
      })
    }
    const onClickCompanyVerification = () => {
      const closeDialog = openDialog({
        title: '企业认证',
        body: () => (
          <CompanyVerification onSuccess={() => {
            closeDialog()
            ElMessage.success({
              message: '提交成功，工作人员会在24小时内联系您确认信息，请注意接听电话',
              plain: true,
            })
          }} />
        ),
        customClose: () => closeDialogIcon(closeDialog),
      })
    }

    return () => (
      <div class="p-6">
        <div class="mb-8 flex items-center justify-between">
          <h1 class="text-[22px] font-medium">个人信息</h1>
          {!isEdit.value && <Button class="btn btn-sm h-[44px] w-[120px]" onClick={startEdit}>{t('modify')}</Button>}
          {isEdit.value && (
            <div>
              <Button class="btn btn-info btn-sm mr-2 h-[44px] w-[120px]" onClick={cancelEdit}>{t('common.cancel')}</Button>
              <Button class="btn btn-primary btn-sm h-[44px] w-[120px]" onClick={handleSubmit}>{t('common.save')}</Button>
            </div>
          )}
        </div>
        <div class="rounded-lg bg-white py-8 ">
          <ElForm label-position="left" label-width="100">
            <ElFormItem label={t('avatar')} class="items-center">
              <div class="flex items-center gap-4">
                <ElImage
                  src={editingMe.value.avatarForShow || me.value.avatar}
                  class="size-16 rounded-full"
                >
                  {{
                    error: () => <SvgIcon name="ic_avatar" class="size-16" />,
                    placeholder: () => <SvgIcon name="ic_avatar" class="size-16" />,
                  }}
                </ElImage>
                {isEdit.value && (
                  <Uploader
                    accept="png,jpg,jpeg"
                    maxsize={1024 * 1024 * 2}
                    isImage={true}
                    ossKeyType="resource"
                    onUploadSuccess={async d => {
                      editingMe.value.avatar = d.temp_path!
                      const data = await getTempPath({ path: d.temp_path! })
                      editingMe.value.avatarForShow = data.data.path
                    }}
                    showFileList={false}
                  >
                    <Button class="btn btn-sm btn-info font-normal text-[#434546]">{t('upload')}</Button>
                  </Uploader>
                )}
              </div>
            </ElFormItem>

            <ElFormItem label={t('nickname')}>
              {isEdit.value ? <ElInput v-model={editingMe.value.nickname} /> : me.value.nickname}
            </ElFormItem>

            <ElFormItem label={t('contacts')}>
              <div class="flex items-center gap-2">
                {me.value.phone || me.value.email}
              </div>
            </ElFormItem>

            <ElFormItem label={t('authentication')}>
              <div class="flex items-center gap-2">
                <span class="text-gray-700">{me.value.company}</span>
                <div
                  onClick={onClickCompanyVerification}
                  class="flex cursor-pointer items-center rounded p-1 text-xs" style={{ backgroundColor: currentStatusConfig.value.bgColor, color: currentStatusConfig.value.textColor }}>
                  {currentStatusConfig.value.label}
                </div>

              </div>
            </ElFormItem>
          </ElForm>
        </div>
      </div>
    )
  },
})
