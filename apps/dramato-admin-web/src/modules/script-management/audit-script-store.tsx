/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, TableColumnOld, Button, showFailToast } from '@skynet/ui'
import { onBeforeMount, onMounted, ref } from 'vue'
import { RouterLink } from 'vue-router'
import { statusDesc, getAuditPeriod } from './util'
import { apiGetAuditList } from './audit-script-api'
import { useAuditFirstStore } from './audit-script-first-store'
import { useAuditSecondStore } from './audit-script-second-store'
import { useAuditFinalStore } from './audit-script-final-store'
import { apiGetLabels } from './script-manage-detail-api'
import dayjs from 'dayjs'
import { ElTooltip } from 'element-plus'

const { onFirstResponsibleEditorEvaluate, onFirstChiefEditorEvaluate, uploadContract } = useAuditFirstStore()
const { onSecondResponsibleEditorEvaluate, onSecondChiefEditorEvaluate } = useAuditSecondStore()
const { onFinalResponsibleEditorEvaluate, onFinalChiefEditorEvaluate } = useAuditFinalStore()

export const useAuditStore = () => {
  return {
    Form,
    params,
    Table,
    list,
    columns,
    loading,
    total,
    onPageChange,
    onPageSizeChange,
    onQuery,
    getList,
    onReset,
    currentUser,
    currentRoles,
    getLabels
  }
}

export const labels = ref<Api.Label.Item[]>([])

const Form = CreateForm<Api.ScriptManage.Request.Params>()

const defaultParams = {
  page_index: 1,
  page_size: 20,
  title_or_key_list: '',
  openplatform_principal: '',
  audit_status: 0,
  created_start: undefined,
  created_end: undefined,
}

const currentRoles = ref<number[]>([])
export const currentUser = ref('')
const params = ref<Api.AuditScript.Request.Params>({
  ...defaultParams,
})

const columns: TableColumnOld<M.IScriptManageListItem>[] = [
  ['剧ID', 'play_key', { class: 'w-[120px]' }],
  ['剧名', row => {
    return (
      <div class="relative">
        <RouterLink class="link-primary" to={`/script/detail/${row.play_key}`}>{row.title}</RouterLink>
      </div>
    )
  }, { class: 'w-[160px]' }],
  ['公司', 'openplatform_principal', { class: 'w-[140px]' }],
  ['审核状态', row => statusDesc(row.audit_status), { class: 'w-[140px]' }],
  ['签约状态', row => !row.contract_path
    ? <div class="badge-default badge">未签约</div> : <div class="badge badge-primary">已签约</div>, { class: 'w-[100px]' }],
  // TODO 当是 不重要人， 则展示当前人的审核， 当是重要的人， 则展示整体剧本评估进度
  ['进度', row => {
    const { value } = getAuditPeriod(row)
    const isResponsibleEditor = row.principal === currentUser.value
    const isNormalEditor = currentRoles.value.includes(27)
    const isMajorEditor = currentRoles.value.includes(26)
    // const isTotalEditor = currentRoles.value.includes(31)
    // audit_process 1/5
    // process_status 0 待完成 1 已完成
    // 一审 责编 ； 主编
    // 二审 指定责编和主编； 总编
    // 终审 指定责编和主编； 总编
    if (value === 1) {
      if (isNormalEditor) {
        return (
          row.process_status === 1 ? (
            <ElTooltip placement="top" content={row.audit1_minor_editor}>
              <div class={`badge ${row.process_status === 1 ? 'badge-primary' : 'badge-default'}`}>
                { row.process_status === 1 ? '已评估' : '未评估' }
              </div>
            </ElTooltip>
          ) : (
            <div class={`badge ${row.process_status === 1 ? 'badge-primary' : 'badge-default'}`}>
              { row.process_status === 1 ? '已评估' : '未评估' }
            </div>
          )
        )
      } else {
        return (
          <ElTooltip placement="top" content={row.audit1_minor_editor}>
            {row.audit_process}
          </ElTooltip>
        )
      }
    } else if (value === 2 || value === 3) {
      if (isMajorEditor || isResponsibleEditor) {
        return (
          <div class={`badge ${row.process_status === 1 ? 'badge-primary' : 'badge-default'}`}>
            { row.process_status === 1 ? '已评估' : '未评估' }
          </div>
        )
      } else {
        return row.audit_process
      }
    }
    return '-'
  }, { class: 'w-[100px]' }],

  ['创建时间', row => {
    return (
      <div class="relative">
        {dayjs(row.created * 1000).format('YYYY-MM-DD HH:mm:ss')}
      </div>
    )
  }, { class: 'w-[160px]' }],
  ['操作', row => {
    const { value, isAuditable, auditStatus } = getAuditPeriod(row)
    const isResponsibleEditor = row.principal === currentUser.value
    const isNormalEditor = currentRoles.value.includes(27)
    const isMajorEditor = currentRoles.value.includes(26)
    const isTotalEditor = currentRoles.value.includes(31)
    /**
     *  一审: 当前人角色：责编, 是否待审核
        一审: 当前人角色：主编, 是否待审核 5人审核后才可以评估
        二审: 当前人角色：指定责编和主编, 是否待审核
        二审: 当前人角色：总编, 得等到指定责编确认标签后，再审核
        终审: 当前人角色：指定责编和主编, 是否待审核
        终审: 当前人角色：总编, 是否待审核
        每阶段审核 只展示一个评估按钮。
        按钮 展示， 对应角色并且待审核状态 则【评估】或者【审核】 否则 按钮展示【查看】
     */
    if (value === 1) {
      if (isNormalEditor) {
        return (
          <>
            <Button class="btn-link btn btn-xs" onClick={() => onFirstResponsibleEditorEvaluate(row, getList)}>{isAuditable ? '评估' : '查看'}</Button>
            {row.audit_status >= 4 && currentRoles.value.includes(32) ? <Button class="btn-link btn btn-xs" onClick={() => uploadContract(row, getList)}>{row.contract_path ? '修改合同' : '上传合同'}</Button> : null}
          </>
        )
      } else if (isMajorEditor) {
        const hasEvaluateNum = row.audit_process ? Number(row.audit_process.split('/')[0]) : 0
        return (
          <>
            <Button class="btn-link btn btn-xs" onClick={() => onFirstChiefEditorEvaluate(row, hasEvaluateNum, getList)}>{isAuditable && isMajorEditor && hasEvaluateNum >= 5 ? '审核' : '查看'}</Button>
            {row.audit_status >= 4 && currentRoles.value.includes(32) ? <Button class="btn-link btn btn-xs" onClick={() => uploadContract(row, getList)}>{row.contract_path ? '修改合同' : '上传合同'}</Button> : null}
          </>
        )
      } else {
        return (
          <>
            <Button class="btn-link btn btn-xs" onClick={() => onFirstChiefEditorEvaluate(row, 0, getList)}>查看</Button>
            {row.audit_status >= 4 && currentRoles.value.includes(32) ? <Button class="btn-link btn btn-xs" onClick={() => uploadContract(row, getList)}>{row.contract_path ? '修改合同' : '上传合同'}</Button> : null}
          </>
        )
      }
    } else if (value === 2) {
      if (isMajorEditor || isResponsibleEditor) {
        return (
          <>
            <Button class="btn-link btn btn-xs" onClick={() => onSecondResponsibleEditorEvaluate(row, getList, isAuditable && isResponsibleEditor)}>{isAuditable ? '评估' : '查看'}</Button>
            {row.audit_status >= 4 && currentRoles.value.includes(32) ? <Button class="btn-link btn btn-xs" onClick={() => uploadContract(row, getList)}>{row.contract_path ? '修改合同' : '上传合同'}</Button> : null}
          </>
        )
      } else if (isTotalEditor) {
        return  (
          <>
            <Button class="btn-link btn btn-xs" onClick={() => onSecondChiefEditorEvaluate(row, getList)}>{isAuditable && row.principal_audited ? '审核' : '查看'}</Button>
            {row.audit_status >= 4 && currentRoles.value.includes(32) ? <Button class="btn-link btn btn-xs" onClick={() => uploadContract(row, getList)}>{row.contract_path ? '修改合同' : '上传合同'}</Button> : null}
          </>
        )
      } else {
        return (
          <>
            <Button class="btn-link btn btn-xs" onClick={() => onSecondChiefEditorEvaluate(row, getList, false)}>查看</Button>
            {row.audit_status >= 4 && currentRoles.value.includes(32) ? <Button class="btn-link btn btn-xs" onClick={() => uploadContract(row, getList)}>{row.contract_path ? '修改合同' : '上传合同'}</Button> : null}
          </>
        )
      }
    } else if (value === 3) {
      if (isMajorEditor || isResponsibleEditor) {
        return (
          <>
            <Button class="btn-link btn btn-xs" onClick={() => onFinalResponsibleEditorEvaluate(row, getList)}>{isAuditable ? '评估' : '查看'}</Button>
            {row.audit_status >= 4 && currentRoles.value.includes(32) ? <Button class="btn-link btn btn-xs" onClick={() => uploadContract(row, getList)}>{row.contract_path ? '修改合同' : '上传合同'}</Button> : null}
          </>
        )
      } else if (isTotalEditor) {
        return (
          <>
            <Button class="btn-link btn btn-xs" onClick={() => onFinalChiefEditorEvaluate(row, getList)}>{isAuditable ? '审核' : '查看'}</Button>
            {row.audit_status >= 4 && currentRoles.value.includes(32) ? <Button class="btn-link btn btn-xs" onClick={() => uploadContract(row, getList)}>{row.contract_path ? '修改合同' : '上传合同'}</Button> : null}
          </>
        )
      } else {
        return (
          <>
            <Button class="btn-link btn btn-xs" onClick={() => onFinalChiefEditorEvaluate(row, getList)}>查看</Button>
            {row.audit_status >= 4 && currentRoles.value.includes(32) ? <Button class="btn-link btn btn-xs" onClick={() => uploadContract(row, getList)}>{row.contract_path ? '修改合同' : '上传合同'}</Button> : null}
          </>
        )
      }
    }
    return null
  }, {
    class: 'w-[120px] text-center',
  },
  ],
]

const Table = CreateTableOld<M.IScriptManageListItem>()
const list = ref<M.IScriptManageListItem[]>([])
const loading = ref<boolean>(false)
const total = ref<number>(1)

const getList = async () => {
  loading.value = true
  const res = await apiGetAuditList({
    ...params.value,
    created_start: params.value.created_start ? (params.value.created_start == '' ? undefined : params.value.created_start) : undefined,
    created_end: params.value.created_end ? (params.value.created_end == '' ? undefined : params.value.created_end) : undefined,
  })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.total || 0
  currentRoles.value = res.data?.open_roles || []
  currentUser.value = res.data?.current_user || ''
}

const onPageChange = (page: number) => {
  params.value.page_index = page
  void getList()
}
const onPageSizeChange = (size: number) => {
  params.value.page_size = size
  params.value.page_index = 1
  void getList()
}

const onQuery = () => {
  params.value.page_index = 1
  void getList()
}

const onReset = () => {
  params.value = {
    ...defaultParams,
  }
  void getList()
}

const getLabels = async () => {
  const labelRes = await apiGetLabels({ language_code: 'zh-CN' })
  labels.value = labelRes.data?.list || []
}

