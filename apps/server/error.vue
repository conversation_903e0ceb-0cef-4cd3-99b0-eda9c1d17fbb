<script setup lang="ts">
import { noData } from './assets/images/images'
import type { NuxtError } from '#app'
// import { T } from '~/modules/search/search.i18n'
const props = defineProps({
  error: Object as () => NuxtError
})

const goBack = () => {
  window.location.href = window.location.origin
}
</script>

<template>
  <div class="flex h-safe-screen w-full flex-col items-center justify-center bg-black">
    <h1>{{ error.statusCode }}</h1>
    <div class="flex size-full -translate-y-[53px] flex-col items-center justify-center gap-y-5 text-sm text-[#fc2763]">
      <img
        :src="noData"
        class="size-40"
      >
      <div class="text-center text-base text-[#cccacb]">
        No results found
      </div>
      <a @click="goBack">Go back home</a>
    </div>
  </div>
</template>
