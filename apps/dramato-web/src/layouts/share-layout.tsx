import { createComponent } from '@skynet/shared'
import { initPinia } from 'src/init/init-pinia'
import { initVant } from 'src/init/init-vant'
import { RouterView } from 'vue-router'
import 'vant/lib/index.css'
import { initH5I18n } from 'src/init/init-h5-i18n'

export const ShareLayout = createComponent(null, () => {
  initPinia()
  initVant()
  // initH5I18n()

  return () => (
    <div class="pad:max-w-[var(--phone-page-max-width)] pc:max-w-[var(--phone-page-max-width)] mx-auto h-full max-w-[var(--phone-page-max-width)]">
      <RouterView />
    </div>
  )
})

export default ShareLayout
