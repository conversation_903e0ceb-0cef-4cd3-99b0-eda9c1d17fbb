export const vi = {
  // 通用
  common: {
    cancel: 'Hủy',
  },
  // 分享页
  share: {
    watchNow: 'Xem ngay',
    episodeDialog: {
      title: 'Tham gia cùng tôi và nhận thêm xu!',
      description: 'Tôi bị cuốn hút bởi bộ phim này!',
      tips: 'Đề xuất',
      button: 'Xem tất cả các bộ phim truyền hình',
      limit24: 'Giới hạn thời gian: 24h',
    },
    toast: {
      takeOffShelf: 'Không có nội dung nào khả dụng',
    },
  },
  'customer-service-center': {
    title: 'Trung tâm dịch vụ khách hàng',
    description: 'M<PERSON> tả',
    'description-placeholder': 'Cung cấp thông tin chi tiết để giúp chúng tôi hiểu vấn đề',
    'email-placeholder': 'Nhập email của bạn để nhận thông tin cập nhật phản hồi',
    'submit-feedback': 'Gửi',
    'enter-feedback': '<PERSON><PERSON><PERSON> hồi về các vấn đề khác',
    'upload-pictures': '<PERSON><PERSON><PERSON> ảnh lên',
    feedback: 'Phản hồi',
    faq: {
      question1: 'Tại sao lại hiển thị "Quảng cáo chưa sẵn sàng"?',
      answer1: 'Đôi khi, kết nối máy chủ hoặc nhà quảng cáo không ổn định có thể gây ra sự cố. Chúng tôi khuyên bạn nên thử lại sau và không nhấp vào "Xem quảng cáo" quá thường xuyên. Giới hạn quảng cáo có thể áp dụng do chính sách của bên thứ ba hoặc hạn chế của quốc gia bạn. Chúng tôi liên tục làm việc với các nhà cung cấp quảng cáo của mình để nâng cao trải nghiệm xem quảng cáo của bạn.',
      question2: 'Tại sao quảng cáo lại hiển thị "Mạng không khả dụng"?',
      answer2: 'Vui lòng thử khởi động lại ứng dụng bằng cách đóng hoàn toàn rồi mở lại. Nếu quảng cáo vẫn không tải, hãy đợi một lúc trước khi thử lại và tránh nhấp quá thường xuyên. Nếu sự cố vẫn tiếp diễn, bạn có thể liên hệ qua hệ thống Phản hồi của chúng tôi. Chúng tôi cũng đang làm việc với nhóm quảng cáo của mình để nâng cao trải nghiệm xem. Cảm ơn sự kiên nhẫn của bạn.',
    },
  },
  toast: {
    'upload-failed': 'Tải lên không thành công, vui lòng tải lại',
    'submit-success': 'Gửi thành công',
    'email-incorrect': 'Định dạng email không đúng',
  },
  not_found: {
    title: 'Trang bạn đang tìm kiếm không tồn tại',
  },
  regulations: {
    howUseData: 'Cách chúng tôi sử dụng dữ liệu của bạn',
    content: `Nếu bạn chọn "Cho phép" trong yêu cầu quyền minh bạch theo dõi ứng dụng, chúng tôi sẽ sử dụng thông tin và dữ liệu của bạn để tối ưu hóa dịch vụ của chúng tôi và cung cấp cho bạn trải nghiệm tốt nhất, nếu không chúng tôi sẽ chỉ thu thập dữ liệu và sẽ không thực hiện bất kỳ hoạt động theo dõi nào.

Nếu bạn đồng ý cho phép DramaWave và các đối tác và nhà cung cấp của chúng tôi sử dụng dữ liệu từ thiết bị của bạn để phân tích cách bạn sử dụng ứng dụng, cá nhân hóa các đề xuất nội dung và cung cấp thêm các dịch vụ liên quan đến thông tin, vui lòng nhấp vào "Đồng ý và Tiếp tục" để tiếp tục.`,
    acceptAll: 'Chấp nhận tất cả',
    dataSettings: 'Cài đặt dữ liệu',
    next: 'Tiếp theo',
    confirm: 'Xác nhận',
    aboutYourPrivacy: 'Về quyền riêng tư của bạn',
    personalDataName: 'Dữ liệu cá nhân hóa',
    personalDataDescription: 'Chúng tôi sử dụng thông tin này để ghi nhớ các lựa chọn của bạn và cung cấp các đề xuất quảng cáo và nội dung cá nhân hóa, thông báo và nhắc nhở, v.v.',
    advertisementDataName: 'Dữ liệu quảng cáo',
    advertisementDataDescription: 'Cookie, các định danh của thiết bị của bạn hoặc thông tin khác có thể được lưu trữ hoặc truy cập trên thiết bị của bạn cho mục đích quảng cáo trực tuyến. Dữ liệu người dùng cũng có thể được chia sẻ với các đối tác quảng cáo của chúng tôi.',
    behaviorsDataName: 'Dữ liệu hành vi',
    behaviorsDataDescription: 'Dữ liệu liên quan đến phân tích, chẳng hạn như cookie hoặc định danh thiết bị, có thể được lưu trữ để đo lường và phân tích việc sử dụng, hành vi và hiệu suất quảng cáo.',
    privacyAndTerms: 'Chính sách bảo mật và Điều khoản dịch vụ',
    referToPrivacy: 'Vui lòng tham khảo Chính sách bảo mật',
    referToTerms: 'Vui lòng tham khảo Điều khoản dịch vụ',
    agreeAndSave: 'Đồng ý và Lưu',
  },
  coins: {
    earnings: {
      title: 'Thu nhập của tôi',
      total: 'Tổng số tiền xu',
      exchange: 'Trao đổi',
      earningsHistory: 'Lịch sử thu nhập',
      exchangeHistory: 'Lịch sử trao đổi',
      noRecords: 'Không có hồ sơ',
      last50earnings: 'Chỉ hiển thị 50 khoản thu nhập gần nhất',
      last50exchanges: 'Chỉ hiển thị 50 hồ sơ trao đổi gần nhất',
      benefits: 'Lợi ích',
      gotIt: 'Hiểu rồi',
    },
    exchange: {
      title: 'Khu vực trao đổi',
      notOpen: 'Chưa được mở, vui lòng đợi thêm',
      receiveBtn: 'Nhận một cách hân hoan',
      earnCoins: 'Kiếm tiền xu',
      redeemRequiredXCoins: 'Đổi yêu cầu {X} xu',
      exchangeSuccess: 'Đổi thành công',
      cardId: 'Mã thẻ',
      exchangeFailed: 'Đổi không thành công',
      transferFailedInsufficientStock: 'Hết hàng. Vui lòng đến sớm vào ngày mai.',
      gotItBtn: 'Đã hiểu',
      transferFailedRiskControl: 'Tài khoản không bình thường, tạm thời không thể đổi thưởng.',
      tryAgainBtn: 'Thử lại sau',
      transferConfirm: 'Chuyển khoản',
      paypalAccountEmail: 'Email tài khoản PayPal',
      confirmPaypalAccountEmail: 'Xác nhận email tài khoản PayPal',
      transferX: 'Chuyển {X}',
      transferXConfirm: 'Xác nhận chuyển {X}',
      transferToYourAccountX: 'Chuyển vào tài khoản của bạn {X}',
      confirmBtn: 'Xác nhận',
      transferSuccessTitle: 'Chúng tôi đang gửi tiền vào tài khoản PayPal của bạn!',
      transferSuccessContentX: 'Tiền sẽ đến trong vòng 5 ngày vào tài khoản {X}.',
      copySuccess: 'Sao chép thành công',
      enterEmailError: 'Vui lòng nhập địa chỉ email hợp lệ',
      enterEmailValidError: 'Lỗi nhập liệu, vui lòng nhập lại',
      emailNotMatchError: 'Địa chỉ email không khớp, vui lòng nhập lại',
    },
  },
  my_wallet: {
    title: 'Ví của tôi',
    help_dialog: {
      title: 'Quy tắc sử dụng vé',
      content1: `1. 1 vé đã mua = 1 vé thưởng đã mua = 1 vé thưởng AD = 1 vé đổi xu – Tất cả những vé này đều có thể dùng để mở khóa các tập phim.`,
      content2: `2. Tất cả các vé mở khóa quyền xem tập phim vĩnh viễn, nhưng Vé Phần Thưởng Mua sẽ hết hạn sau một khoảng thời gian nhất định.`,
      content3: `3. Vé đã mua có thể mua được thông qua mua trực tiếp.`,
      content4: `4. Vé thưởng đã mua chỉ có thể mua được trong các giai đoạn khuyến mại cụ thể thông qua mua hàng.`,
      content5: `5. Vé thưởng AD có thể kiếm được bằng cách hoàn thành nhiệm vụ xem quảng cáo.`,
      content6: `6. Vé đổi xu có thể kiếm được bằng cách đổi xu.`,
    },
    account_balance: 'Số dư tài khoản',
    purchased_tickets: 'Vé đã mua',
    purchase_reward_tickets: 'Vé thưởng đã mua',
    coin_exchange_tickets: 'Vé trao đổi tiền xu',
    ad_reward_tickets: 'Vé thưởng AD',
    top_up: 'Nạp tiền',
    purchase_history: 'Lịch sử mua hàng',
    rewards_history: 'Lịch sử thưởng',
    exchange_history: 'Lịch sử trao đổi',
    consumption_records: 'Hồ sơ tiêu thụ',
    auto_unlock: 'Tập phim khi Tự động mở khóa',
  },
  top_up: {
    exchange_title: 'Trao đổi xu',
    purchase_title: 'Mua',
    tickets: 'Vé',
    coins: 'Xu',
    balance: 'Số dư',
    title: 'Cửa hàng',
    tips: 'Mẹo',
    tips_1: '1. Nền tảng của chúng tôi cung cấp cả phim truyền hình ngắn miễn phí và phim truyền hình cao cấp truy cập sớm.',
    tips_2: '2. Để xem trước nội dung truy cập sớm, bạn cần đổi bằng Vé. Những Vé này có thể được mua, đổi xu hoặc xem quảng cáo.',
    tips_3: '3. Tất cả các vé mở khóa quyền xem tập phim vĩnh viễn, nhưng Vé Phần Thưởng Mua sẽ hết hạn sau một khoảng thời gian nhất định.',
    tips_4: '4. Vé thưởng đã mua sẽ được sử dụng trước khi mở khóa các tập phim. Nếu số dư không đủ, vé thường của bạn sẽ được tự động sử dụng.',
    tips_5: '5. Nếu thanh toán nạp tiền thành công nhưng số dư của bạn chưa được cập nhật, vui lòng thử làm mới ví của bạn.',
    tips_6: '6. Nếu có bất kỳ câu hỏi nào khác, vui lòng liên hệ với chúng tôi qua ""Hồ sơ"" -> ""Trung tâm dịch vụ khách hàng"" -> ""Phản hồi"".',
    exchange_dialog_title: 'Bạn có chắc chắn muốn trao đổi?',
    redeem_required: 'Yêu cầu đổi quà',
    exchange_success: 'Đổi xxx vé thành công',
    balance_not_enough: 'Số dư không đủ',
    exchange_failed: 'Đổi thất bại',
  },
  my_wallet_in: {
    transaction_history: 'Lịch sử giao dịch',
    EPnum: 'EP.{num}',
    Expired: 'Hết hạn',
    AD: 'Quảng cáo',
    Expires_on_num: 'Hết hạn vào {num}',
    Thats_all_for_now: 'Đó là tất cả cho đến nay.',
    consumption_records: 'Hồ sơ tiêu thụ',
  },
  meal_check_in: {
    sleeping_link: 'Kiếm xu khi ngủ',
    rules_title: 'Quy tắc hoạt động',
    benefits_title: 'Benefits',
    rules_1: '1. Mỗi ngày có bốn khung giờ để bạn ghi lại bữa ăn của mình: Bữa sáng: 07:00-10:00; Bữa trưa: 11:00-14:00; Bữa tối: 17:00-20:00; Bữa khuya: 21:00-24:00.',
    rules_2: '2. Nếu bạn bỏ lỡ thời gian ghi điểm, bạn có thể xem một video để nhận lại phần thưởng.',
    rules_3: '3. Phần thưởng được đặt lại mỗi ngày vào lúc nửa đêm, đừng quên nhận phần thưởng nhé.',
  },
  sleep_check_in: {
    earn_link: 'Ghi lại bữa ăn để kiếm',
    benefits_title: 'Benefits',
    rules_1: '1. Mỗi tối từ 7 giờ tối đến 12 giờ khuya, bạn có thể kích hoạt chế độ \'Ngủ để kiếm tiền\'.',
    rules_2: '2. Sau khi kích hoạt, vào ngày hôm sau, sau khi ngủ đủ 8 tiếng, bạn có thể nhận phần thưởng ngủ từ 8 giờ sáng đến 12 giờ trưa.',
    rules_3: '3. Nếu bạn bỏ lỡ thời gian nhận thưởng, bạn có thể xem một video từ 12 giờ trưa đến 7 giờ tối ngày hôm sau để nhận lại phần thưởng.',
  },
}
