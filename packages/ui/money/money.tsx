import { createComponent, fn } from '@skynet/shared'
import { ref, watch } from 'vue'
import { MergeClass } from '../ui'
type MoneyOptions = {
  props: {
    modelValue?: number
  }
  emits: {
    'update:modelValue': (value: number) => void
  }
}
export const Money = createComponent<MoneyOptions>({
  props: {
    modelValue: NaN,
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { slots }) => {
  return () => (
    Number.isNaN(props.modelValue)
      ? null
      : (<MergeClass tag="x-money" baseClass="inline"> {(props.modelValue / 100).toFixed(2)} </MergeClass>)
  )
})

type MoneyInputOptions = {
  props: {
    modelValue?: number
    id?: string
  }
  emits: {
    'update:modelValue': (v: number) => void
  }
}
export const MoneyInput = createComponent<MoneyInputOptions>({
  props: {
    modelValue: NaN,
    id: '',
  },
  emits: {
    'update:modelValue': fn,
  },
}, (props, { attrs, emit }) => {
  const displayed = ref(valueToDisplayed(props.modelValue))
  watch(() => props.modelValue, value => {
    if (displayedToValue(displayed.value) === value) return
    displayed.value = valueToDisplayed(value)
  })
  const pattern = /^\d*(.\d{0,2})?$/

  return () => (
    <input id={props.id} value={displayed.value} type="number" onInput={onInput} />
  )
  function onInput(e: Event) {
    const target = e.target as HTMLInputElement
    if (!target.value.match(pattern)) {
      target.value = displayed.value
      return
    }
    displayed.value = target.value
    const value = displayedToValue(target.value)
    emit('update:modelValue', value)
  }
})

function valueToDisplayed(value: number): string {
  if (Number.isNaN(value) || value < 0) return ''
  return (value / 100).toFixed(2)
}

function displayedToValue(displayed: string): number {
  const value = parseFloat(displayed)
  if (Number.isNaN(value)) return NaN
  return Math.round(value * 100)
}
