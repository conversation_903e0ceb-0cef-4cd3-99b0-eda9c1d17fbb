import { ClassName, createComponent, fn, mergeClass, required } from '@skynet/shared'
import { computed } from 'vue'

type Options = {
  props: {
    name: string
    class?: ClassName
    noColor?: boolean
  }
  emits: {
    click: (event: MouseEvent) => void
  }
}

export const SvgIcon = createComponent<Options>({
  inheritAttrs: false,
  props: {
    name: required,
    class: '',
    noColor: false,
  },
  emits: {
    click: fn,
  },
}, (props, { emit }) => {
  const name = props.name
  return () => (
    <svg class={mergeClass('no-tap-color inline-block h-5 w-5 fill-current align-middle', props.class)} onClick={e => emit('click', e)}>
      <use xlinkHref={`#${name}`} />
    </svg>
  )
})

export default SvgIcon
