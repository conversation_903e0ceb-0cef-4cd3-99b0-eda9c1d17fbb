import { createApp } from 'vue'
import './assets/style/global.css'
import 'github-markdown-css'
import router from './router.tsx'
import { App } from './app'
import 'src/init/init.ts'
import { Icon } from '@iconify/vue'
import { createPinia } from 'pinia'
import VueKon<PERSON> from 'vue-konva'

/* @ts-expect-error never mind */
import('virtual:svg-icons-register')
/* @ts-expect-error never mind */
import('virtual:svg-no-colored-icons-register')

import Multiselect from 'vue-multiselect' // https://vue-multiselect.js.org/#sub-custom-configuration
import 'vue-multiselect/dist/vue-multiselect.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

const app = createApp(App)
const pinia = createPinia()
app.use(router)
app.use(pinia)
app.use(ElementPlus)
app.use(VueKonva)

app.mount('#app')
app.component('Multiselect', Multiselect)
app.component('Icon', Icon)
