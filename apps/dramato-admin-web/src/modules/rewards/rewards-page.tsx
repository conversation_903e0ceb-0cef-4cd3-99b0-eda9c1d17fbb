import { createComponent, mc } from '@skynet/shared'
import { Button, Checkbox, TableColumnOld, CreateForm, CreateTableOld, DateTime, openDialog, showAlert, transformNumber, transformStringArray } from '@skynet/ui'
import { computed, onMounted, ref, watch } from 'vue'
import { useRewardsStore } from './rewards-store'
import { cloneDeep, pick, set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { AdvertisePage } from '../advertise/advertise-page'
import { ShortDrama } from '../short-drama/short-drama-page'
import { apiDeleteConfig, apiGetAreaList, apiGetRewardConfigList, apiReleaseConfig, apiSaveConfig } from './rewards-api'
import { useAppAndLangOptions } from '../options/use-app-options'

export const periodMap = {
  daily: '每天',
  twelve_hours: '每12小时',
  six_hours: '每6小时',
  three_hours: '每3小时',
  hourly: '每小时',
}

export const RewardsPage = createComponent(null, () => {
  const currentArea = ref<string>('General')
  const tabs = ref<M.Area[]>([{
    name: '通用',
    val: 'General',
    type: 2,
  }])
  const currentSortNo = ref<number>(0)
  const areaList = ref<M.Area[]>([])
  const searchFormData = ref<{ app_id?: number }>({
    app_id: undefined,
  })
  const needRelease = ref<boolean>(false)
  const platforms = [undefined, 'ios', 'android']

  const Form = CreateForm<M.RewardsConfig>()
  const SearchForm = CreateForm<{ app_id?: number }>()
  const { formData, ErrorSpan, validate, error, hasExtra, userWhiteListEnabled, defaultFormData, resetFormData, rewardCoins, discount, previousExtra, previousWhiteUserList, canEditDiscount, canEditExtra } = useRewardsStore()

  const rewardsConfigList = ref<M.RewardsConfig[]>([])

  const BaseTable = CreateTableOld<M.RewardsConfig.CheckInBase>()
  const baseColumns: TableColumnOld<M.RewardsConfig.CheckInBase>[] = [
    ['打卡日', row => (<>Day {row.day}</>), { class: 'w-[120px]' }],
    [requiredLabel('奖励Reward Coins'), (row, index) => (
      <div class="flex flex-col">
        <input class="input input-sm input-bordered w-[100px]" type="number" min={0} max={9999} v-model={row.reward_coins} />
        <ErrorSpan class="text-sm text-[red]" keyName={`check_in.base.${index}.reward_coins`} />
      </div>
    ), { class: 'w-[400px]' }],
  ]

  const checkedExtraItem = ref<M.RewardsConfig.CheckInExtra[]>([])
  const deleteExtra = (row: M.RewardsConfig.CheckInExtra) => {
    checkedExtraItem.value = checkedExtraItem.value.filter(item => item.ad_id !== row.ad_id)
    formData.value.check_in && (formData.value.check_in.extra = formData.value.check_in?.extra?.filter(item => item.ad_id !== row.ad_id))
  }
  const ExtraTable = CreateTableOld<M.RewardsConfig.CheckInExtra>()
  const extraColumns: TableColumnOld<M.RewardsConfig.CheckInExtra>[] = [
    ['广告ID', 'ad_id'],
    ['广告位名称', 'ad_name'],
    ['广告组', 'ad_group', { class: 'w-300px' }],
    ['广告位类型', 'type'],
    ['Coins参考价', 'coins'],
    ['Mediation竞价', 'mediation_mode'],
    ['频次', row => {
      return (
        <x-frequency class="flex flex-col">
          <span>{row.limit}次/{periodMap[row.duration as keyof typeof periodMap] ?? '--'}</span>
          <span class="text-gray-500 text-sm">per user</span>
        </x-frequency>
      )
    }, { class: 'w-[100px]' }],
    ['操作', row => {
      return (
        <Button disabled={!hasExtra.value} class="btn btn-outline btn-xs" onClick={() => deleteExtra(row)}>删除</Button>
      )
    }, { class: 'w-[80px] text-center' }],
  ]

  const checkedDramaList = ref<M.RewardsConfig.SpecialOffer[]>([])
  const SpecialOfferTable = CreateTableOld<M.RewardsConfig.SpecialOffer>()
  const specialOfferColumns: TableColumnOld<M.RewardsConfig.SpecialOffer>[] = [
    ['序号', (row, index) => (
      <input
        type="number"
        class="w-[80px] input-sm input input-bordered h-8"
        value={row.sort_no ?? index + 1}
        onFocus={() => {
          currentSortNo.value = row.sort_no ?? 1
        }}
        onInput={(e: Event) => {
          currentSortNo.value = Number((e.target as HTMLInputElement).value) || 0
        }}
        onBlur={() => {
          if (currentSortNo.value === row.sort_no) {
            return
          }
          row.sort_no = currentSortNo.value
          if (!formData.value.special_offers) {
            formData.value.special_offers = { list: [] }
          }
          formData.value.special_offers.list = formData.value.special_offers.list.sort((a, b) => {
            return a.sort_no! - b.sort_no!
          }).map((item, index) => {
            item.sort_no = index + 1
            return item
          })
        }}
      />
    ), { class: 'w-[100px]' }],
    ['剧集ID', 'series_key', { class: 'w-[120px]' }],
    ['剧名', 'title', { class: 'w-[260px]' }],
    ['上架时间', row => <DateTime value={(row.listing_time ?? 0) * 1000} format="YYYY-MM-DD HH:mm:ss" />, { class: 'w-[300px]' }],
    ['总集数', 'episodes_number'],
    ['开始付费集数', 'start_paying_episodes'],
    ['单集指导价', 'episodes_price'],
    ['折扣后价格', row => row.discount_price?.toFixed(2)],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-outline btn-xs"
            onClick={() => {
              (formData.value.special_offers?.list || []).splice(idx, 1)
              checkedDramaList.value = checkedDramaList.value.filter(item => item.series_key !== row.series_key)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[80px]' },
    ],
  ]

  const checkedAdvertiseItemForWatchAd = ref<M.RewardsConfig.WatchAd[]>([])
  const WatchAdTable = CreateTableOld<M.RewardsConfig.WatchAd & { reward_coins?: number }>()
  const watchAdColumns: TableColumnOld<M.RewardsConfig.WatchAd & { reward_coins?: number }>[] = [
    ['序号', (row, index) => (
      <input
        type="number"
        class="w-[80px] input-sm input input-bordered h-8"
        value={row.sort_no ?? index + 1}
        onFocus={() => {
          currentSortNo.value = row.sort_no || 1
        }}
        onInput={(e: Event) => {
          currentSortNo.value = Number((e.target as HTMLInputElement).value) || 0
        }}
        onBlur={() => {
          if (currentSortNo.value === row.sort_no) {
            return
          }
          row.sort_no = currentSortNo.value
          if (!formData.value.watch_ad) {
            formData.value.watch_ad = { list: [] }
          }
          formData.value.watch_ad.list = formData.value.watch_ad.list.sort((a, b) => {
            return a.sort_no! - b.sort_no!
          }).map((item, index) => {
            item.sort_no = index + 1
            return item
          })
        }}
      />
    ), { class: 'w-[100px]' }],
    ['广告ID', 'ad_id', { class: 'w-[100px]' }],
    ['广告位名称', 'ad_name', { class: 'w-[200px]' }],
    ['广告组', 'ad_group', { class: 'w-[300px]' }],
    ['广告位类型', 'type', { class: 'w-[100px]' }],
    ['Coins参考价', 'coins', { class: 'w-[100px]' }],
    ['Mediation竞价', 'mediation_mode', { class: 'w-[100px]' }],
    ['频次', row => {
      return (
        <x-frequency class="flex flex-col">
          <span>{row.limit}次/{periodMap[row.duration as keyof typeof periodMap] ?? '--'}</span>
          <span class="text-gray-500 text-sm">per user</span>
        </x-frequency>
      )
    }, { class: 'w-[100px]' }],
    ['看完奖励', (row, index) => (
      <div class="flex flex-col">
        <input class="input input-sm input-bordered w-[100px]" type="number" min={0} max={9999} value={row.reward_coins ?? 0} onInput={e => row.reward_coins = Number((e.target as HTMLInputElement).value)} />
        <ErrorSpan class="text-sm text-[red]" keyName={`watch_ad.list.${index}.reward_coins`} />
      </div>
    ), { class: 'w-[100px]' }],
    ['操作', (row, index) => {
      return (
        <Button class="btn btn-outline btn-xs" onClick={() => deleteWatchAd(index)}>删除</Button>
      )
    }, { class: 'w-[80px] text-center' }],
  ]
  const deleteWatchAd = (index: number) => {
    formData.value.watch_ad && formData.value.watch_ad.list && formData.value.watch_ad.list.splice(index, 1)
  }

  const removeTab = () => {
    const currentIndex = tabs.value.findIndex(item => item.val === currentArea.value)
    const id = rewardsConfigList.value.find(item => item.area === currentArea.value)?.id
    if (!id) {
      tabs.value = tabs.value.filter(item => item.val !== currentArea.value)
      currentArea.value = tabs.value[currentIndex - 1 >= 0 ? currentIndex - 1 : 0].val
      return
    }
    const closeDialog = openDialog({
      title: '删除确认',
      mainClass: 'pb-0 px-5',
      body: () => (
        <>
          <div>是否删除此配置，恢复通用配置</div>
          <footer class="w-full sticky bottom-0 left-0 flex justify-end gap-x-2 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              void apiDeleteConfig({ id }).then(() => {
                tabs.value = tabs.value.filter(item => item.val !== currentArea.value)
                currentArea.value = tabs.value[currentIndex - 1 >= 0 ? currentIndex - 1 : 0].val
                closeDialog()
                onSearch()
              })
            }}
            >确定
            </Button>
          </footer>
        </>
      ),

    })
  }

  const showImportAdvertiseDialog = () => {
    checkedExtraItem.value = formData.value.check_in?.extra ? [...formData.value.check_in.extra] : []
    const hide = openDialog({
      title: '导入广告',
      customClass: '!w-2/3',
      mainClass: '[&_.hide-when-in-dialog]:hidden [&_x-hide-when-in-dialog]:hidden [&_x-show-when-in-dialog]:!block pb-0 px-5',
      body: computed(() => (
        <x-import-advertise class="relative">
          <AdvertisePage
            checkedItem={checkedExtraItem.value.map(item => ({
              id: item.ad_id,
            }) as M.Advertise)}
            onAdd={advertise => {
              // 这里要转一次，因为AdvertisePage的advertise是M.Advertise类型，而formData.value.check_in.extra是M.RewardsConfig.CheckInExtra类型
              checkedExtraItem.value = [{
                ad_id: advertise.id!,
                ad_group: advertise.ad_unit,
                ad_name: advertise.name,
                type: advertise.ad_type,
                mediation_mode: advertise.mediation,
                limit: advertise.count_limit!,
                duration: advertise.period,
                coins: advertise.coins!,
                reward_coins: rewardCoins.value ?? 0,
              }]
            }}
            platform={platforms[appOptions.value.find(app => app.value === searchFormData.value.app_id)?.platform ?? 0]}
            onRemove={advertise => {
              const idx = checkedExtraItem.value.findIndex(item => item.ad_id === advertise.id)
              if (idx !== -1) {
                checkedExtraItem.value.splice(idx, 1)
              }
            }}
          />
          <footer class="w-full sticky bottom-0 left-0 flex justify-end gap-x-2 bg-white border-gray-200 border-t pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (checkedExtraItem.value.length > 1) {
                return showAlert('只能选择一个广告', 'info')
              }
              formData.value.check_in && (formData.value.check_in.extra = [...checkedExtraItem.value])
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-advertise>
      )),
    })
  }

  const showImportDramaDialog = () => {
    checkedDramaList.value = [...formData.value.special_offers?.list ?? []]
    const closeDialog = openDialog({
      title: '导入指定生效短剧',
      mainClass: 'flex flex-col flex-auto pb-0 h-[80vh] overflow-hidden px-4 [&_.hide-when-in-dialog]:hidden',
      body: () => (
        <x-import-recharge-level class="flex-1 flex flex-col overflow-hidden gxp-y-4">
          <x-episode-list class="flex-1  overflow-y-auto">
            <ShortDrama
              hasNav={false}
              hasActions={false}
              checkedItems={checkedDramaList.value as unknown as M.ApplicationDramaItem[]}
              fixStatus={3}
              appId={searchFormData.value.app_id}
              onAdd={item => {
                checkedDramaList.value?.push({ ...item })
              }}
              onRemove={item => {
                checkedDramaList.value = checkedDramaList.value?.filter(i => i.series_key !== item.series_key)
              }}
            />
          </x-episode-list>
          <footer class="w-full flex justify-end gap-x-2 pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (checkedDramaList.value.length === 0) return
              if (checkedDramaList.value.length > 3) {
                return showAlert('最多只能选择3部', 'info')
              }
              if (!formData.value.special_offers) {
                formData.value.special_offers = {
                  list: [],
                }
              }
              formData.value.special_offers.list = checkedDramaList.value.map((item, index) => ({
                ...item,
                sort_no: index + 1,
                episodes_price: Number(item.episodes_price),
                discount_rate: discount.value,
                // discount_price向上取整，5的倍数
                discount_price: Math.ceil((item.episodes_price ?? 0) * (100 - (discount.value ?? 0)) / 100 / 5) * 5,
              }))
              closeDialog()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-[80%] overflow-hidden',
    })
  }

  const showImportWatchAdDialog = () => {
    checkedAdvertiseItemForWatchAd.value = []
    const hide = openDialog({
      title: '导入广告',
      customClass: '!w-2/3',
      mainClass: '[&_.hide-when-in-dialog]:hidden [&_x-hide-when-in-dialog]:hidden [&_x-show-when-in-dialog]:!block pb-0 px-5',
      body: computed(() => (
        <x-import-advertise class="relative">
          <AdvertisePage
            onAdd={advertise => {
              // 这里要转一次，因为AdvertisePage的advertise是M.Advertise类型，而formData.value.check_in.extra是M.RewardsConfig.CheckInExtra类型
              checkedAdvertiseItemForWatchAd.value.push({
                ad_id: advertise.id!,
                ad_group: advertise.ad_unit,
                ad_name: advertise.name,
                type: advertise.ad_type,
                mediation_mode: advertise.mediation,
                limit: advertise.count_limit!,
                duration: advertise.period,
                coins: advertise.coins!,
                reward_coins: 0,
              })
            }}
            onRemove={advertise => {
              checkedAdvertiseItemForWatchAd.value = checkedAdvertiseItemForWatchAd.value.filter(item => item.ad_id !== advertise.id!)
            }}
            platform={platforms[appOptions.value.find(app => app.value === searchFormData.value.app_id)?.platform ?? 0]}
          />
          <footer class="w-full sticky bottom-0 left-0 flex justify-end gap-x-2 bg-white border-gray-200 border-t pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!formData.value.watch_ad) {
                formData.value.watch_ad = {
                  list: [],
                }
              }
              formData.value.watch_ad.list = formData.value.watch_ad.list.concat(checkedAdvertiseItemForWatchAd.value.map((item, index) => ({
                ...item,
                reward_coins: item.reward_coins ?? 0,
                sort_no: index + 1,
              })))
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-advertise>
      )),
    })
  }

  const chooseArea = ref<M.Area>()
  const openChooseAreaDialog = () => {
    const closeDialog = openDialog({
      title: '选择国家/地区',
      mainClass: 'flex flex-col flex-auto pb-0 overflow-hidden px-4',
      body: () => (
        <x-choose-area>
          <div>
            国家/地区
            <select class="select select-bordered select-sm ml-4" value={chooseArea.value?.val} onChange={e => chooseArea.value = areaList.value.find(item => item.val === (e.target as HTMLInputElement).value)}>
              {
                areaList.value.map(area => (<option label={area.name} value={area.val} />))
              }
            </select>
          </div>
          <footer class="w-full flex justify-end gap-x-2 pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!chooseArea.value) return
              if (tabs.value.findIndex(item => item.val === chooseArea.value!.val) === -1) {
                tabs.value.push(chooseArea.value)
                currentArea.value = chooseArea.value.val
                rewardsConfigList.value.push({
                  ...cloneDeep(defaultFormData),
                  area: chooseArea.value.val,
                  area_show_name: chooseArea.value.name,
                  area_type: chooseArea.value.type,
                })
              } else {
                showAlert('已存在，请重新选择')
              }
              closeDialog()
            }}
            >确定
            </Button>
          </footer>
        </x-choose-area>
      ),
    })
  }

  const reset = () => {
    void apiSaveConfig({ ...pick(formData.value, ['area', 'id', 'area_show_name', 'area_type']), app_id: searchFormData.value.app_id, action: 2 } as M.ResetRewardsConfig).then(res => {
      if (res.code === 200) {
        showAlert('重置成功')
        onSearch()
      }
    })
  }

  const save = () => {
    const valid = validate()
    console.log(valid, error.value)
    console.log(formData.value)
    if (valid) {
      void apiSaveConfig({ ...formData.value, app_id: searchFormData.value.app_id, action: 1 }).then(res => {
        if (res.code === 200) {
          showAlert('保存成功')
          onSearch()
        }
      })
    }
  }

  const release = () => {
    if (!searchFormData.value.app_id) return showAlert('请先选择应用', 'info')
    void apiReleaseConfig({ app_id: searchFormData.value.app_id }).then(res => {
      if (res.code === 200) {
        showAlert('发布成功')
        onSearch()
      }
    })
  }

  watch(() => hasExtra.value, () => {
    if (!formData.value.check_in) return
    if (!hasExtra.value) {
      previousExtra.value = formData.value.check_in?.extra && [...formData.value.check_in.extra]
      formData.value.check_in.extra = undefined
    } else {
      formData.value.check_in.extra = previousExtra.value && [...previousExtra.value]
      previousExtra.value = undefined
    }
  })

  watch(() => formData.value.white_user?.list, val => {
    if (val && val.length > 0) {
      previousWhiteUserList.value = [...val]
      userWhiteListEnabled.value = true
    }
  }, {
    immediate: true,
  })

  watch(() => userWhiteListEnabled.value, () => {
    if (!userWhiteListEnabled.value) {
      previousWhiteUserList.value = formData.value.white_user?.list ? [...formData.value.white_user.list] : undefined
      formData.value.white_user = undefined
    } else {
      formData.value.white_user = formData.value.white_user ?? { list: [] }
      formData.value.white_user.list = previousWhiteUserList.value ? [...previousWhiteUserList.value] : []
      previousWhiteUserList.value = undefined
    }
  })

  watch(() => currentArea.value, () => {
    resetFormData()
    initFormData()
    error.value = {}
  })

  const initFormData = () => {
    // 这里需要解引用
    const matched = (rewardsConfigList.value.find(item => item.area === currentArea.value) ?? cloneDeep(defaultFormData))
    formData.value = {
      ...matched,
      area: currentArea.value ?? 'General',
      area_show_name: areaList.value.find(item => item.val === currentArea.value)?.name ?? '通用',
      area_type: areaList.value.find(item => item.val === currentArea.value)?.type ?? 2,
    }
    // 获取discount
    discount.value = formData.value.special_offers?.list?.[0]?.discount_rate ?? 0
    previousExtra.value = formData.value?.check_in?.extra ?? []
    rewardCoins.value = formData.value?.check_in?.extra?.[0]?.reward_coins ?? 0
    hasExtra.value = !!(formData.value?.check_in?.extra?.length ?? false)
  }

  const onSearch = (isInit?: boolean) => {
    if (isInit) {
      tabs.value = [{
        name: '通用',
        val: 'General',
        type: 2,
      }]
      currentArea.value = 'General'
    }
    void apiGetRewardConfigList({ ...searchFormData.value }).then(res => {
      if (!res.data) return
      needRelease.value = res.data.is_need_release
      rewardsConfigList.value = res.data.list
      if (!rewardsConfigList.value.find(item => item.area === currentArea.value)) {
        // 重置tabs
        tabs.value = [{
          name: '通用',
          val: 'General',
          type: 2,
        }]
        currentArea.value = 'General'
      }
      if (rewardsConfigList.value.length > 0) {
        rewardsConfigList.value.forEach(item => {
          // 分离tabs
          if (tabs.value.findIndex(tab => tab.val === item.area) === -1) {
            tabs.value.push({ name: item.area_show_name!, val: item.area, type: item.area_type! })
          }
          if (!item.check_in || !item.check_in.base) {
            item.check_in = { base: [], extra: item.check_in?.extra }
          }
          if (item.check_in.base && item.check_in.base.length < 7) {
            // item.check_in.base 需要固定长度7项，其中每一项都有一个day依次从1-7，如果不足七项，则需要在适当的位置插入{day: index, reward_coins: undefined}
            for (let i = 1; i <= 7; i++) {
              if (!item.check_in.base.find(item => item.day === i)) {
                item.check_in.base.splice(i - 1, 0, { day: i, reward_coins: undefined })
              }
            }
          }
        })
      } else if (rewardsConfigList.value.length === 0) {
        rewardsConfigList.value.push({
          ...cloneDeep(defaultFormData),
          area: 'General',
          area_show_name: '通用',
          area_type: 2,
        })
      }
      initFormData()
    })
  }

  const { appOptions } = useAppAndLangOptions(() => searchFormData.value.app_id, {
    onSuccess: onSearch,
  })

  watch(() => appOptions.value, () => {
    if (appOptions.value.length > 0 && !searchFormData.value.app_id) {
      searchFormData.value.app_id = appOptions.value[0]?.value
    }
  }, {
    immediate: true,
  })

  onMounted(() => {
  // 获取国家地区列表
    void apiGetAreaList().then(res => {
      if (!res.data) return
      areaList.value = res.data.list
      chooseArea.value = res.data.list[0]
    })
  })

  return () => (
    <div class="py-8 space-y-4">
      {/* {JSON.stringify(formData.value)} */}
      <x-header class="w-full flex items-center gap-x-4">
        <div class="font-bold text-xl">Rewards页管理</div>
      </x-header>
      <x-actions class="flex items-end bg-white gap-x-4 p-4">
        <SearchForm
          data={searchFormData.value}
          onChange={(path, value) => {
            set(searchFormData.value, path, value)
          }}
          hasAction={false}
          items={[
            {
              label: requiredLabel('应用'),
              path: 'app_id',
              transform: transformNumber,
              input: {
                type: 'select',
                class: 'w-[240px]',
                autoInsertEmptyOption: true,
                options: appOptions.value,
              },
              errorVisible: false,
            },
          ]}
        />
        <Button class="btn btn-sm btn-primary" onClick={() => onSearch(true)}>提交</Button>
        <Button class="btn btn-sm btn-primary" disabled={!needRelease.value} onClick={release}>更新上线</Button>
        {needRelease.value && <x-tips class="text-[red] text-sm">*有保存的修改未与线上同步</x-tips>}
      </x-actions>
      <div class="flex items-start">
        <x-tabs role="tablist" class="tabs tabs-bordered">
          <>
            <x-tab-header class="flex bg-white items-center px-4 pt-4 gap-x-4 flex-wrap">
              {
                tabs.value.map(item => (
                  <x-tab-header-item class="relative">
                    <div role="tab" class={mc('text-nowrap mx-2 tab', currentArea.value === item.val ? 'border-b-[4px] border-primary text-primary' : '')} onClick={() => currentArea.value = item.val}>{item.name}</div>
                    {rewardsConfigList.value.find(config => config.area === item.val)?.is_wait_release && <x-is-new class="absolute top-1 -right-[10px] text-sm text-red-500">New</x-is-new>}
                  </x-tab-header-item>
                ))
              }
              <div class="text-2xl cursor-pointer ml-2" onClick={openChooseAreaDialog}>+</div>
            </x-tab-header>
            <div role="tabpanel" class="py-4 bg-white">
              <x-actions class="flex justify-end items-center bg-white gap-x-4 p-4">
                <x-tips class="text-sm text-gray-500">重置会将修改恢复为线上状态，保存后不会立即生效，需要更新上线</x-tips>
                <button disabled={!formData.value.id} class="btn btn-sm btn-default" onClick={reset}>重置</button>
                <button class="btn btn-sm btn-primary" onClick={save}>保存</button>
                {currentArea.value !== 'General' && <button class="btn btn-sm !bg-red-500 !text-white" onClick={removeTab}>删除</button>}
              </x-actions>
              <Form
                onChange={(path, value) => {
                  set(formData.value, path, value)
                }}
                data={formData.value}
                hasAction={false}
                error={error.value}
                class="flex flex-col gap-4 p-4"
                items={
                  [
                    [
                      <x-title class="block text-md font-bold">{requiredLabel('Check-in')}</x-title>,
                      'check_in.base',
                      {
                        type: 'custom',
                        render: ({ item, value }) => {
                          return (
                            <section class="bg-gray-50 p-4 rounded-lg">
                              <x-tips class="block text-sm text-gray-500 py-4">每轮7天打卡，中断将重新开始；每天打卡可额外增加1次激励广告奖励</x-tips>
                              <BaseTable
                                list={formData.value.check_in?.base ?? []}
                                columns={baseColumns}
                              />
                            </section>
                          )
                        },
                      },
                    ],
                    [
                      <x-title class="block text-md font-bold"><Checkbox label="Extra广告" v-model={hasExtra.value} /></x-title>,
                      'check_in.extra',
                      {
                        type: 'custom',
                        render: ({ item, value }) => {
                          return (
                            <section class={mc('bg-gray-50 p-4 rounded-lg', hasExtra.value ? '' : 'opacity-30')}>
                              <div class="flex justify-between items-center mb-4">
                                <div class="flex gap-x-4 items-center">
                                  <Button class="btn btn-primary btn-xs" disabled={!hasExtra.value} onClick={() => showImportAdvertiseDialog()}>导入广告
                                  </Button>
                                  <x-tips class="text-sm text-gray-500">添加1个广告，只在用户打卡后 extra 1次</x-tips>
                                </div>
                                <div class="flex gap-x-4 items-center">
                                  Extra奖励
                                  <div>
                                    {
                                      canEditExtra.value
                                        ? <input disabled={!hasExtra.value} type="number" class="input input-bordered input-sm w-[100px]" v-model={rewardCoins.value} min={0} max={9999} />
                                        : rewardCoins.value
                                    }
                                  </div>
                                  Reward Coins
                                  <button disabled={!hasExtra.value} class="btn btn-primary btn-xs" onClick={() => {
                                    if (canEditExtra.value) {
                                      if (rewardCoins.value === undefined || rewardCoins.value < 0 || rewardCoins.value > 9999) {
                                        showAlert('请输入正确的值，0~9999', 'error')
                                        return
                                      }
                                      // rewardCoins.value不能为小数
                                      if (rewardCoins.value % 1 !== 0) {
                                        showAlert('不能为小数', 'error')
                                        return
                                      }
                                      if (formData.value.check_in?.extra && formData.value.check_in.extra[0]) {
                                        formData.value.check_in.extra[0].reward_coins = rewardCoins.value ?? 0
                                      }
                                      canEditExtra.value = false
                                    } else {
                                      canEditExtra.value = true
                                    }
                                  }}
                                  >{canEditExtra.value ? '保存' : '修改'}
                                  </button>
                                </div>
                              </div>
                              <ExtraTable
                                list={formData.value.check_in?.extra ?? []}
                                columns={extraColumns}
                              />
                            </section>
                          )
                        },
                      },
                    ],
                    [
                      <x-title class="block text-md font-bold">{requiredLabel('Special Offers')}</x-title>,
                      'special_offers.list',
                      {
                        type: 'custom',
                        render: ({ item, value }) => {
                          return (
                            <section class="bg-gray-50 p-4 rounded-lg mt-2">
                              <div class="flex justify-between items-center mb-4">
                                <div class="flex gap-x-4 items-center">
                                  <Button class="btn btn-primary btn-xs" onClick={() => showImportDramaDialog()}>导入短剧
                                  </Button>
                                  <x-tips class="text-sm text-gray-500">仅可选已上架剧，折扣后价格将取整为5和10的倍数，暂定最多3部</x-tips>
                                </div>
                                <div class="flex gap-x-4 items-center">
                                  折扣率
                                  <div>
                                    {
                                      canEditDiscount.value
                                        ? <input type="number" class="input input-bordered input-sm w-[100px]" v-model={discount.value} min={0} max={99} />
                                        : discount.value
                                    }
                                    %
                                  </div>
                                  <button class="btn btn-primary btn-xs" onClick={() => {
                                    if (canEditDiscount.value) {
                                      if (discount.value === undefined || discount.value < 0 || discount.value > 99) {
                                        showAlert('请输入正确的值:0~99', 'error')
                                        return
                                      }
                                      // discount.value不能为小数
                                      if (discount.value % 1 !== 0) {
                                        showAlert('不能为小数', 'error')
                                        return
                                      }
                                      if (formData.value.special_offers?.list && formData.value.special_offers.list.length > 0) {
                                        formData.value.special_offers.list = formData.value.special_offers.list.map(item => {
                                          return {
                                            ...item,
                                            episodes_price: Number(item.episodes_price),
                                            discount_rate: discount.value,
                                            discount_price: Math.ceil((item.episodes_price ?? 0) * (100 - (discount.value ?? 0)) / 100 / 5) * 5,
                                          }
                                        })
                                      }
                                      canEditDiscount.value = false
                                    } else {
                                      canEditDiscount.value = true
                                    }
                                  }}
                                  >{canEditDiscount.value ? '保存' : '修改'}
                                  </button>
                                </div>
                              </div>
                              <SpecialOfferTable
                                list={formData.value.special_offers?.list ?? []}
                                columns={specialOfferColumns}
                              />
                            </section>
                          )
                        },
                      },
                    ],
                    [
                      <x-title class="block text-md font-bold">{requiredLabel('Watch AD')}</x-title>,
                      'watch_ad.list',
                      {
                        type: 'custom',
                        render: ({ item, value }) => {
                          return (
                            <section class="bg-gray-50 p-4 rounded-lg mt-2">
                              <Button class="btn btn-primary btn-xs mb-4" onClick={() => showImportWatchAdDialog()}>新增</Button>
                              <WatchAdTable
                                list={formData.value.watch_ad?.list ?? []}
                                columns={watchAdColumns}
                              />
                            </section>
                          )
                        },
                      },
                    ],
                    [
                      <x-title class="block text-md font-bold">
                        <Checkbox v-model={userWhiteListEnabled.value} label="下发给白名单用户" />
                      </x-title>, 'white_user.list', { type: 'custom', render: ({ onInput, item, value }) => (
                        <div>
                          <textarea value={value?.toString() ?? ''}
                            onInput={e => onInput((e.target as HTMLTextAreaElement).value)}
                            placeholder="请输入 UID，用逗号或者换行分隔"
                            class="textarea textarea-bordered textarea-sm w-full min-h-[8em]"
                            disabled={!userWhiteListEnabled.value}
                          />
                        </div>
                      ),
                      }, {
                        transform: transformStringArray,
                      }],
                  ]
                }
              />
            </div>
          </>
        </x-tabs>
      </div>
    </div>
  )
})

export default RewardsPage
