#!/bin/sh

set -e

source bin/helper.sh

curl -X POST "http://yinghang.fang:<EMAIL>:8080/job/$JENKINS_JOB/build"

set +e
echo 'success'

# Jenkins 的 Build Command
# export COMMITTER_NAME=$(git log -1 --pretty=format:'%an')
# export AUTH="yinghang.fang:110e0d2f54734f8b257a0427c7e6d1f1b5"
# export JOB_PART="test-frontend/job/test-dramato-admin"
# sh ./build.sh test && {
#   curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"$JOB_BASE_NAME #$BUILD_NUMBER 部署成功 \n\n最后提交: $COMMITTER_NAME \n构建时间: $(date "+%Y-%m-%d %H:%M:%S")\n\"}}" "https://open.feishu.cn/open-apis/bot/v2/hook/b66567cc-3d82-4b30-b4ad-273f2060c2ee"
# } || {
#   curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"$JOB_BASE_NAME #$BUILD_NUMBER 部署失败！ \n\n最后提交: $COMMITTER_NAME \n构建时间: $(date "+%Y-%m-%d %H:%M:%S")\noutput 查看命令: curl -v 'http://$<EMAIL>:8080/job/$JOB_PART/$BUILD_NUMBER/consoleText'\"}}" "https://open.feishu.cn/open-apis/bot/v2/hook/b66567cc-3d82-4b30-b4ad-273f2060c2ee"
#   false
# }
