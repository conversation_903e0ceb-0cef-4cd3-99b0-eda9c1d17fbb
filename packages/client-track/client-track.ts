import { createUuid } from '@skynet/shared/id-helper'
import { UAParser } from 'ua-parser-js'
export interface TrackData {
  [key: string]: undefined | string | number | object // 为了保持灵活性，允许其他可能的字段
  app_version: string
  browser_name?: string
  browser_version?: string
  channel?: string
  client_height?: string
  client_id?: string
  client_width?: string
  device_hash: string
  device_name?: string
  environment: string
  event: string
  href?: string
  os_name: string
  os_version: string
  page_title?: string
  referrer?: string
  screen_height: string
  screen_width: string
  time: string
  type?: string
  user_agent?: string
  user_id?: string
  user_source?: string

  performance?: object
}

interface PerformanceData {
  performance_total_load_time?: number;
  performance_dns_time?: number;
  performance_tcp_time?: number;
  performance_request_response_time?: number;
  performance_dom_parse_time?: number;
  performance_resource_load_time?: number;
}

const reportInterval = 5000
const backendUrl = import.meta.env.VITE_TRACK_URL ?? 'https://trace.mydramawave.com/client_track'
const backendUrlTest = import.meta.env.VITE_TRACK_URL ?? 'https://trace-test.mydramawave.com/client_track'
const getUuid = () => {
  const uuid = localStorage.getItem('client_track_uuid')
  if (uuid) {
    return uuid
  }
  const newUuid = createUuid()
  localStorage.setItem('client_track_uuid', newUuid)
  return newUuid
}
const extra = ()=> {
  if ('flutter_inappwebview' in window || typeof window.dwJsHandler !== 'undefined') {
    const parts = window.navigator.userAgent.split('/')
    const [platformName, platformSystemVersion, platformSystemManufacturer, platformSystemModel, app_version, buildNumber, deviceId, uuid, packageName] = parts
    return {
      os_name: platformName,
      os_version: platformSystemVersion,
      uuid,
      device_manufacturer: platformSystemManufacturer,
      device_name: platformSystemModel,
      app_version,
      browser_name: 'webview',
      browser_version: 'unknown',
      app_package: packageName,
      device_hash: deviceId,
      user_source: 'app'
     }
  }
  const parser = UAParser(window.navigator.userAgent)
  return {
    os_name: parser.os.name ?? 'unknown',
    os_version: parser.os.version ?? 'unknown',
    uuid: getUuid(),
    device_manufacturer: parser.device.vendor ?? 'unknown',
    device_name: parser.device.model ?? 'unknown',
    browser_name: parser.browser.name ?? 'unknown',
    browser_version: parser.browser.version ?? 'unknown',
    user_source: 'web'
  }
}
// 定义获取性能数据的函数
export const performanceData = (): PerformanceData => {
  let performanceDataVal: PerformanceData = {};
  try {
      const navigationEntry = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
          performanceDataVal = {
            ...pick(navigationEntry, ['startTime', 'responseStart', 'responseEnd', 'requestStart', 'requestEnd'] ),
              // 页面从开始导航到完全加载的时间
              performance_total_load_time: parseInt(String(navigationEntry.loadEventEnd - navigationEntry.startTime)),
              // DNS 查询时间
              performance_dns_time: parseInt(String(navigationEntry.domainLookupEnd - navigationEntry.domainLookupStart)),
              // TCP 连接时间
              performance_tcp_time: parseInt(String(navigationEntry.connectEnd - navigationEntry.connectStart)),
              // HTTP 请求响应时间
              performance_request_response_time: parseInt(String(navigationEntry.responseEnd - navigationEntry.requestStart)),
              // DOM 解析时间
              performance_dom_parse_time: parseInt(String(navigationEntry.domInteractive - navigationEntry.responseEnd)),
              // 资源加载时间
              performance_resource_load_time: parseInt(String(navigationEntry.loadEventStart - navigationEntry.domContentLoadedEventEnd))
          };
      }
  } catch (error) {
      console.error('获取性能数据时出错:', error);
  }
  return performanceDataVal;
};


let buffer: TrackData[] = []
let timer: number | null = null

function startReportTimer() {
  if (!timer) {
    timer = window.setInterval(reportNow, reportInterval)
  }
}

type RequiredParams = Required<Pick<TrackData, 'event' | 'device_hash' | 'app_version' >>
type OptionalParams = Partial<Pick<TrackData, 'user_id' | 'channel' | 'user_source' | 'environment' | 'type' | 'event_info' | 'client_country'>>
const getEnv = () => {
  return window.location.hostname.indexOf('test') >= 0
    || window.location.hostname.indexOf('localhost') >= 0
    || window.location.hostname.indexOf('127.0.0.1') >= 0
    ? 'development'
    : 'prod'
}
export function report(data: RequiredParams & OptionalParams) {
  const environment = getEnv()
  buffer.push({
    channel: environment === 'development' ? 'devtest' : 'prod',
    environment,
    href: window.location.href,
    page_title: document.title,
    referrer: document.referrer,
    screen_height: window.screen.height.toString(),
    screen_width: window.screen.width.toString(),
    user_agent: window.navigator.userAgent,
    time: Date.now().toString(),
    client_height: window.innerHeight.toString(),
    client_width: window.innerWidth.toString(),
    ...performanceData(),
    ...extra(),
    ...data,
  })
}

export function reportNowViaBeacon() {
  const environment = getEnv()
  const isDev = environment === 'development'

  if (buffer.length === 0) return

  const dataToSend = [...buffer]
  buffer = []

  const blob = new Blob([JSON.stringify(dataToSend)], { type: 'application/json' })
  const success = navigator.sendBeacon(isDev ? backendUrlTest : backendUrl, blob)

  if (success) {
    console.log('上报成功')
  } else {
    console.error('上报失败')
    // 如果发送失败，将数据放回 buffer 以便重试
    buffer = [...dataToSend, ...buffer]
  }
}

/**使用 fetch 上报所有数据 */
export function reportNow() {
  const environment = getEnv()
  const isDev = environment === 'development'

  if (buffer.length === 0) return

  const dataToSend = [...buffer]
  buffer = []

  const blob = new Blob([JSON.stringify(dataToSend)], { type: 'application/json' })
  fetch(isDev ? backendUrlTest : backendUrl, {
    method: 'POST',
    body: blob,
    headers: {
      'Content-Type': 'application/json'
    }
  }).catch(e => {
    console.error('上报失败', e)
    // 如果发送失败，将数据放回 buffer 以便重试
    buffer = [...dataToSend, ...buffer]
  })
}

startReportTimer()

export const clientTrack = {
  report,
  reportNow,
  performanceData
}

function pick(obj: object, keys: string[]) {
  return Object.fromEntries(
    Object.entries(obj).filter(([key]) => keys.includes(key))
  )
}
