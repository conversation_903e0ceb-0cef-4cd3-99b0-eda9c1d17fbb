import {
  ref,
  watch,
  provide,
  Teleport,
  nextTick,
  computed,
  onMounted,
  Transition,
  onActivated,
  onDeactivated,
  defineComponent,
  type CSSProperties,
  type ExtractPropTypes,
} from 'vue'

// Utils
import { popupSharedProps } from './popup-shared'
import {
  isDef,
  extend,
  makeStringProp,
  callInterceptor,
} from '../utils'

import { useLockScroll } from '../use/use-lock-scroll'
import { POPUP_TOGGLE_KEY } from '../use/on-popup-reopen'
import { useScopeId } from '../use/use-scope-id'

// Components
//   import { Icon } from '../icon';
import Overlay from '../overlay/overlay'

// Types
import type { PopupPosition, PopupCloseIconPosition } from './popup-types'
import { useLazyRender } from '../use/use-lazy-render'
import { useExpose } from '../use/use-expose'
import { useEventListener } from '../use/use-event-listener'
import { useGlobalZIndex } from '../use/use-global-z-index'
import { SvgIcon } from '../ui'

export const popupProps = extend({}, popupSharedProps, {
  round: Boolean,
  position: makeStringProp<PopupPosition>('center'),
  closeIcon: makeStringProp('cross'),
  closeable: Boolean,
  transition: String,
  iconPrefix: String,
  closeOnPopstate: Boolean,
  closeIconPosition: makeStringProp<PopupCloseIconPosition>('top-right'),
  safeAreaInsetTop: Boolean,
  safeAreaInsetBottom: Boolean,
})

export type PopupProps = ExtractPropTypes<typeof popupProps>

//   const [name, bem] = createNamespace('popup');

export const Popup = defineComponent({
  inheritAttrs: false,
  props: popupProps,
  emits: [
    'open',
    'close',
    'opened',
    'closed',
    'keydown',
    'update:show',
    'clickOverlay',
    'clickCloseIcon',
  ],

  setup(props, { emit, attrs, slots }) {
    let opened: boolean
    let shouldReopen: boolean

    const zIndex = ref<number>()
    const popupRef = ref<HTMLElement>()

    const lazyRender = useLazyRender(() => props.show || !props.lazyRender)

    const style = computed(() => {
      const style: CSSProperties = {
        zIndex: zIndex.value,
      }

      if (isDef(props.duration)) {
        const key
          = props.position === 'center'
            ? 'animationDuration'
            : 'transitionDuration'
        style[key] = `${props.duration}s`
      }

      return style
    })

    const open = () => {
      if (!opened) {
        opened = true

        zIndex.value
          = props.zIndex !== undefined ? +props.zIndex : useGlobalZIndex()

        emit('open')
      }
    }

    const close = () => {
      if (opened) {
        callInterceptor(props.beforeClose, {
          done() {
            opened = false
            emit('close')
            emit('update:show', false)
          },
        })
      }
    }

    const onClickOverlay = (event: MouseEvent) => {
      emit('clickOverlay', event)

      if (props.closeOnClickOverlay) {
        close()
      }
    }

    const renderOverlay = () => {
      if (props.overlay) {
        return (
          <Overlay
            v-slots={{ default: slots['overlay-content'] }}
            show={props.show}
            class={props.overlayClass}
            zIndex={zIndex.value}
            duration={props.duration}
            customStyle={props.overlayStyle}
            role={props.closeOnClickOverlay ? 'button' : undefined}
            tabindex={props.closeOnClickOverlay ? 0 : undefined}
            {...useScopeId()}
            // @ts-expect-error 不知道当时为什么要加 onClick
            onClick={onClickOverlay}
          />
        )
      }
    }

    const onClickCloseIcon = (event: MouseEvent) => {
      emit('clickCloseIcon', event)
      close()
    }

    const renderCloseIcon = () => {
      if (props.closeable) {
        return (
          <SvgIcon
            class="w-5 h-5 pointer-cursor absolute top-20px right-20px"
            name={props.closeIcon}
            onClick={onClickCloseIcon}
          />
        )
      }
    }

    // see: https://github.com/youzan/vant/issues/11901
    let timer: number | undefined
    const onOpened = () => {
      if (timer) window.clearTimeout(timer)
      timer = window.setTimeout(() => {
        emit('opened')
      })
    }
    const onClosed = () => emit('closed')
    const onKeydown = (event: KeyboardEvent) => emit('keydown', event)

    const renderPopup = lazyRender(() => {
      const { safeAreaInsetTop, safeAreaInsetBottom } = props

      return (
        <div
          v-show={props.show}
          ref={popupRef}
          style={style.value}
          role="dialog"
          tabindex={0}
          class={[
            {},
            {
              'van-safe-area-top': safeAreaInsetTop,
              'van-safe-area-bottom': safeAreaInsetBottom,
            },
          ]}
          onKeydown={onKeydown}
          {...attrs}
          {...useScopeId()}
        >
          {slots.default?.()}
          {renderCloseIcon()}
        </div>
      )
    })

    const renderTransition = () => {
      const { position, transition, transitionAppear } = props
      const name
        = position === 'center' ? 'van-fade' : `van-popup-slide-${position}`

      return (
        <Transition
          v-slots={{ default: renderPopup }}
          name={transition || name}
          appear={transitionAppear}
          onAfterEnter={onOpened}
          onAfterLeave={onClosed}
        />
      )
    }

    watch(
      () => props.show,
      show => {
        if (show && !opened) {
          open()

          if (attrs.tabindex === 0) {
            void nextTick(() => {
              popupRef.value?.focus()
            })
          }
        }
        if (!show && opened) {
          opened = false
          emit('close')
        }
      },
    )

    useExpose({ popupRef })

    useLockScroll(popupRef, () => props.show && props.lockScroll)

    useEventListener('popstate', () => {
      if (props.closeOnPopstate) {
        close()
        shouldReopen = false
      }
    })

    onMounted(() => {
      if (props.show) {
        open()
      }
    })

    onActivated(() => {
      if (shouldReopen) {
        emit('update:show', true)
        shouldReopen = false
      }
    })

    onDeactivated(() => {
      // teleported popup should be closed when deactivated
      if (props.show && props.teleport) {
        close()
        shouldReopen = true
      }
    })

    provide(POPUP_TOGGLE_KEY, () => props.show)

    return () => {
      if (props.teleport) {
        return (
          <Teleport to={props.teleport}>
            {renderOverlay()}
            {renderTransition()}
          </Teleport>
        )
      }

      return (
        <>
          {renderOverlay()}
          {renderTransition()}
        </>
      )
    }
  },
})

export default Popup
