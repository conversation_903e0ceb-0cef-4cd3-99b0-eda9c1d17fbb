import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { dirname, join } from 'node:path'
import { fileURLToPath } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import Markdown from 'vite-plugin-md'
import Anchor from 'markdown-it-anchor'
import Prism from 'markdown-it-prism'
import Contents from 'markdown-it-table-of-contents'
// @ts-expect-error ignore js
import { createSvgIconsPlugin } from '@skynet/vite-plugin-svg-icons'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const projectDir = __dirname
const project = (path: string) => join(projectDir, path)
const envDir = project('env')

const isCustomElement = (tag: string) => tag.startsWith('x-')
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, envDir)
  return {
    base: env.VITE_CONFIG_BASE,
    envDir,
    plugins: [
      vue({
        include: [/\.vue$/, /\.md$/],
      }),
      vueJsx({
        isCustomElement,
        defineComponentName: ['defineComponent', 'createComponent'],
      }),
      Markdown({
        wrapperClasses: 'markdown-body',
        wrapperComponent: 'wrapper',
        markdownItOptions: {
          html: true,
          linkify: true,
          typographer: true,
        },
        markdownItSetup(md) {
          // add anchor links to your H[x] tags
          md.use(Anchor)
          // add code syntax highlighting with Prism
          md.use(Prism)
          md.use(Contents)
        },
      }),
      createSvgIconsPlugin({
        iconDirs: [project('src/icons/svg')],
        symbolId: '[name]',
      }),
      createSvgIconsPlugin({
        registerName: 'virtual:svg-no-colored-icons-register',
        iconDirs: [project('src/icons/no-colored-svg')],
        symbolId: 'n_[name]',
        svgoOptions: {
          plugins: [
            {
              name: 'removeAttrs',
              params: {
                attrs: ['fill'],
                preserveCurrentColor: true,
              },
            },
          ],
        },
        customDomId: '__svg__no__colored__icons__dom__',
      }),
    ],
    resolve: {
      alias: {
        src: project('src'),
      },
    },
  }
})
