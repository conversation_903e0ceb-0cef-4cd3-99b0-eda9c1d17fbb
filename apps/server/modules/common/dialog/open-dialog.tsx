import { ref } from 'vue'
import type { DialogProps } from './dialog.tsx'
import { useDialogStore } from './use-dialog-store.tsx'

/**
 * 打开对话框
 * @example
 * ```tsx
 * const close = openDialog({
 *   title: "编辑",
 *   body: <YourComponent onClose={() => close()} />,
 * });
 ```
 */
export const openDialog = (options: Omit<DialogProps, 'visible'>) => {
  const dialogStore = useDialogStore('h5-dialog')
  const index = ref<number>()
  const { addDialog, removeDialog } = dialogStore
  const open = () => {
    index.value = addDialog?.(options)
  }
  const close = () => {
    if (index.value === undefined) return
    if (options.beforeClose?.() === false) return
    removeDialog(index.value)
    index.value = undefined
  }
  open()
  return close
}
