import { useValidator } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { ref } from 'vue'
import { z } from 'zod'
const Form = CreateForm<M.UserEngagement.Save.Request>()
const defaultFormData = () => {
  return {
    name: '',
    is_abtest: 0, // 是否AB测试
    platform: 0, // 0 all 1 IOS 2 Android
    priority: undefined,
    strategy_layer_ids: [], // 用户分层ID
    duration: -1, // 持续时间 -1 表示不限
    undertake_type: 2, // 承接类型 1 直接开剧(去掉了);2 推荐弹窗,5秒开剧;3 推荐弹窗,点击开剧; 4 红包弹窗
    drama_source: 1, // 短剧来源，1，算法，2，推荐1，3，推荐2
  } as unknown as M.UserEngagement.Save.Request
}
const formData = ref<M.UserEngagement.Save.Request>(defaultFormData())

const resetFormData = () => {
  formData.value = defaultFormData()
}
const resetTargetFormData = () => {
  const { name, is_abtest, platform, priority, strategy_layer_ids, duration } = defaultFormData()
  formData.value = { ...formData.value, name, is_abtest, platform, priority, strategy_layer_ids, duration }
}
const resetStrategyFormData = () => {
  const { undertake_type, drama_source } = defaultFormData()
  formData.value = { ...formData.value, undertake_type, drama_source }
}

const stepOneRules = z.object({
  name: z.string().min(1, '必填'),
  platform: z.number(),
  priority: z.number(),
})
const { error: stepOneError, validateAll: _validateStepOne } = useValidator(formData, stepOneRules)
const validateStepOne = () => {
  const exclude: string[] = []
  const valid = _validateStepOne({ exclude })
  return valid
}

export const userEngagementStore = () => {
  return {
    formData,
    resetTargetFormData,
    resetStrategyFormData,
    resetFormData,
    stepOneError,
    validateStepOne,
    Form,
  }
}
