declare namespace M {
  namespace TargetedTasks {
    namespace ListRequest {
      interface Item {
        task_id: number
        serial_number: number // 子任务编号
        info_from: number //  1 海外 2 国内
        status: number // 状态 1: 待执行 2: 执行中 3: 执行完毕 4 异常停止
        start_page: number // 默认为1
        end_page: number // 结束页码
        count: number // 入库条数
        err_msg: string // 报错的信息
        search_type: string // 每日查询 每周查询 定向检索等
        period: string // 此字段服务器下发，技能表示定向查询，也能表示定时任务查询
        created: number// 创建时间
        search_key: string // 关键词
        comment: string // 备注
      }
    }

    namespace SaveRequest {
      interface Task {
        info_from?: number //  1 海外 2 国内
        search_key?: string
        search_type?: number // 1: 全部 2： 产品名称 7 短剧名字
        period?: number // 1:全部 2:7天 3: 30天
        start_page?: number // 默认为1
        comment?: string
      }
    }
  }
}
