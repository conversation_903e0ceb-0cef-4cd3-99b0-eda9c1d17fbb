<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8">
  <title>DramaWave</title>
  <link rel="icon" href="/favicon.ico">
  <link rel="apple-touch-icon" href="/favicon.ico">
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">

  <script>window.t0_for_report = Date.now()</script>
  <style>
    [v-cloak] {
      display: none;
    }

    html,
    body {
      background-color: #000000;
      height: 100%;
    }

    * {
      padding: 0;
      margin: 0;
      box-sizing: border-box;
    }

    .bottom-box {
      opacity: 1;
      position: fixed;
      bottom: 1.2rem;
      display: flex;
      width: 100%;
      padding: 0 0.4rem;
      box-sizing: border-box;
      z-index: 20
    }

    .bottom-box .button {
      position: relative;
      display: block;
      width: 100%;
      height: 0.8rem;
      padding-left: 0.4rem;
      padding-right: 0.4rem;
      background: #E12121;
      border-radius: 2rem;
      margin: 0 auto;
      font-weight: 500;
      font-size: .32rem;
      color: #FFFFFF;
      text-align: center;
      line-height: 0.8rem;
      text-decoration: none
    }

    .bottom-box .button:active {
      opacity: .8
    }

    .click {
      position: absolute;
      width: 1.2rem;
      height: 1rem;
      top: 0.8rem;
      right: 0.1rem;
      animation: afterScale 1s linear infinite;
      -webkit-animation: afterScale 1s linear infinite;
      -webkit-animation-duration: 1s;
      animation-duration: 1s;
      -webkit-animation-fill-mode: both;
      animation-fill-mode: both
    }

    @keyframes down-forever {
      0% {
        transform: translateY(0)
      }

      50% {
        transform: translateY(10px)
      }

      100% {
        transform: translateY(0px)
      }
    }

    @-webkit-keyframes down-forever {
      0% {
        transform: translateY(0)
      }

      50% {
        transform: translateY(10px)
      }

      100% {
        transform: translateY(0px)
      }
    }

    #app {
      position: relative;
      min-height: 100vh
    }

    .bg-img {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      z-index: 0;
      width: 100%;
      object-fit: cover;
      filter: blur(4px);
      border: 0;
      height: 100%;
    }

    .bg-mask {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 5;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 1) 51%, #000000 100%)
    }

    .content-box {
      position: relative;
      z-index: 10;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100%
    }

    .logo {
      position: relative;
      display: block;
      margin: 0 auto;
      width: 0.72rem;
      height: 0.72rem;
      z-index: 2;
      box-sizing: content-box;
      border-radius: 0.2rem;
    }

    .cover {
      position: relative;
      display: block;
      margin: 0 auto;
      width: 5.8rem;
      height: 100%;
      border-radius: .2rem;
      border: 0.04rem solid rgba(255, 255, 255, 0.15);
      z-index: 2;
      opacity: 0.8;
    }

    .cover-box {
      position: relative;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 1.7rem 0 0.5rem 0;
    }

    .info {
      padding-bottom: 5rem
    }

    .title {
      position: relative;
      z-index: 2;
      font-weight: bold;
      font-size: .32rem;
      color: #FFFFFF;
      line-height: 0.32rem;
      text-align: center;
      margin-bottom: 0.4rem
    }

    .desc {
      word-wrap: break-word;
      white-space: pre-wrap;
      position: relative;
      z-index: 2;
      font-weight: 400;
      font-size: .28rem;
      color: #FFFFFF;
      line-height: 0.4rem;
      padding: 0 0.4rem;
      color: rgba(255, 255, 255, 0.6);
      ;
      margin: 0 auto
    }

    .ellipsis-5 {
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 8;
      overflow: hidden;
    }

    .more {
      color: #FFFFFF;
      display: none;
      position: absolute;
      right: 0;
      bottom: 0;
      padding-left: 2rem;
      background: linear-gradient(to right, rgba(0, 0, 0, 0), #000, #000)
    }

    .ellipsis-5 .more {
      display: block
    }

    .header_body {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center
    }

    .header_title {
      text-align: left;
      color: #fff;
      font-size: 0.32rem;
      font-family: Inter;
      font-weight: 600;
      word-wrap: break-word;
      margin-left: .2rem;
    }

    .flex-center {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .display-none {
      display: none;
    }

    @keyframes afterScale {

      0%,
      100% {
        top: 0.8rem;
        right: 0.1rem;
      }

      50% {
        top: 0.4rem;
        right: 0.4rem;
      }
    }

    .play-btn {
      position: absolute;
      z-index: 10;
      width: 1.6rem;
      height: 1.6rem;
      border-radius: 2rem;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .play-btn-icon {
      width: 1.2rem;
      height: 1.2rem;
    }

    .download-banner {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 999;
      width: 100%;
      height: 1.46rem;
    }

    .download-banner-content {
      width: 100%;
      height: 1.2rem;
      align-items: center;
      padding: 0 0.32rem;
      background-color: #F0F0F0;
      border-bottom: 0.5px solid #878787;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      justify-content: space-between;
    }

    .banner-content-left {
      flex: 1;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      align-items: center;
      flex-direction: row;
      column-gap: 0.16rem;
    }

    .banner-logo {
      width: 0.72rem;
      height: 0.72rem;
      border-radius: 0.2rem;
    }

    .banner-intro {
      display: flex;
      flex-direction: column;
    }

    .banner-intro .app-name {
      font-weight: bold;
      font-size: 0.32rem;
      color: rgba(0, 0, 0, 0.9);
      line-height: 0.44rem;
    }

    .banner-intro .app-title {
      color: #666;
      height: 0.34rem;
      font-size: 0.26rem;
      font-weight: 400;
      line-height: 0.36rem;
      display: inline-block;
      width: 4rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .download-banner .open-btn {
      display: inline-block;
      width: 1.6rem;
      height: 0.64rem;
      background-color: #FF2B58;
      border-radius: 0.32rem;
      font-size: 0.32rem;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 0.64rem;
      text-align: center;
      white-space: nowrap;
    }

    .download-banner.dark .download-banner-content {
      border-bottom: 0.5px solid #515151;
      background: #0B080B;
    }

    .download-banner.dark .download-banner-content .app-name {
      color: #fff;
    }
  </style>

</head>

<body>
  <script>
    function setPageFontsize() {
      var docEl = document.documentElement;
      var clientWidth = docEl.clientWidth;
      document.documentElement.style.setProperty('--vh', (window.innerHeight / 100) + 'px');
      if (!clientWidth)
        return;
      var isLandscape = window.matchMedia('(orientation: landscape)');

      if (isLandscape.matches && clientWidth > 500) {
        var horizontalFontsize = 100 * (clientWidth / 1624);
        docEl.style.fontSize = horizontalFontsize + "px";
      } else {
        var verticalFontsize = 100 * (clientWidth / 750);
        if (verticalFontsize > 80) {
          verticalFontsize = 80;
        }
        docEl.style.fontSize = verticalFontsize + "px";
      }
    }
    (function (doc, win) {
      var resizeEvt = "orientationchange" in window ? "orientationchange" : "resize";
      setPageFontsize();
      setTimeout(function () {
        setPageFontsize()
      }, 100);
      if (!doc.addEventListener)
        return;
      win.addEventListener(resizeEvt, setPageFontsize, false);
      doc.addEventListener("DOMContentLoaded", setPageFontsize, false);
    }
    )(document, window);
    window.onload = function () {
      setPageFontsize();
    }
  </script>


  <div id="app" class="hasBottomCTA" style="">
    <div id="drama_download_banner" class="download-banner dark">
      <div class="download-banner-content">
        <div class="banner-content-left">
          <img class="banner-logo" alt="" src="https://cdn.usrgrow.com/storage/icons/app_593_1732336814.png">
          <div class="banner-intro">
            <div class="app-name ui-name"></div>
            <span class="app-title" id="bannerTitle" data-i18n="continueToNextEpisode">Continue to next episode</span>
          </div>
        </div>
        <div class="open-btn" id="bannerBtn" data-i18n="open">Open</div>
      </div>
    </div>
    <img id="image" class="bg-img ui-cover" src="" alt="bg" />
    <div class="bg-mask"></div>
    <div class="content-box">
      <div class="cover-box">
        <div class="play-btn">
          <svg xmlns="http://www.w3.org/2000/svg" class="play-btn-icon" width="70" height="70" viewBox="0 0 70 70"
            fill="none">
            <g filter="url(#filter0_d_182_105)">
              <path
                d="M50.9093 31.8006C53.2417 33.2957 53.2417 36.7038 50.9094 38.1989L24.7008 54.9995C22.1716 56.6208 18.85 54.8046 18.85 51.8003L18.85 18.1996C18.85 15.1954 22.1716 13.3792 24.7007 15.0004L50.9093 31.8006Z"
                fill="white" />
            </g>
            <defs>
              <filter id="filter0_d_182_105" x="10.85" y="7.39336" width="49.8087" height="57.2132"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha" />
                <feOffset dy="1" />
                <feGaussianBlur stdDeviation="4" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.56 0" />
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_182_105" />
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_182_105" result="shape" />
              </filter>
            </defs>
          </svg>
        </div>
        <img id="topReading" class="cover ui-cover" src="" alt="material" />
      </div>
      <div class="info">
        <h1 class="title ui-name"></h1>
        <div class="desc ui-desc"></div>
      </div>
    </div>

    <div class="bottom-box">
      <div id="bottomReading"
        style="background: #D62448; color: #FFFFFF;font-size: 0.36rem;background-image: linear-gradient(to right, rgb(239,148,57), rgb(233,62,79), rgb(227,49,204));line-height: 100%; height: 1rem;line-height: 1rem;display: flex; text-align: center;flex-direction: row;font-weight: 700;"
        class="button">
        <div id="downloadBtnTitle" style="display: flex;height: 100%;width: 100%;justify-content: center;"
          data-i18n="continueWatching">
        </div>
        <img
          src="data:image/png;base64,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"
          class="click" alt="click" />
      </div>
      <div style="position:absolute;left: -20000px;display:none;" id="bottomCopyDiv"></div>
    </div>
  </div>

  <div style="width: 0; height: 0;">
    <iframe id="iframe" src="" width="0" height="0" name="iframe"></iframe>
  </div>
  <!-- i18n starts-->
  <script>
    window.i18n = {
      'en-US': {
        'continueWatching': 'Continue Watching',
        'pickUp': 'Pick up',
        'more': 'More',
        'continueToNextEpisode': 'Next Episode',
        'open': 'Open'
      },
      'zh-CN': {
        'continueWatching': '继续观看',
        'pickUp': '收起',
        'more': '更多',
        'continueToNextEpisode': '继续下一集',
        'open': '打开'
      },
      'zh-TW': {
        'continueWatching': '繼續觀看',
        'pickUp': '收合',
        'more': '更多',
        'continueToNextEpisode': '繼續下一集',
        'open': '開啟'
      },
      'ja-JP': {
        'continueWatching': '続きを見る',
        'pickUp': '折りたたむ',
        'more': 'その他',
        'continueToNextEpisode': '次話を見る',
        'open': '開く'
      },
      'ko-KR': {
        'continueWatching': '이어 보기',
        'pickUp': '접기',
        'more': '더보기',
        'continueToNextEpisode': '다음 회차',
        'open': '열기'
      },
      'es-ES': {
        'continueWatching': 'Continuar viendo',
        'pickUp': 'Contraer',
        'more': 'Más',
        'continueToNextEpisode': 'Siguiente episodio',
        'open': 'Abrir'
      },
      'pt-PT': {
        'continueWatching': 'Continuar a ver',
        'pickUp': 'Recolher',
        'more': 'Mais',
        'continueToNextEpisode': 'Próximo Episódio',
        'open': 'Abrir'
      },
      'vi-VN': {
        'continueWatching': 'Tiếp tục xem',
        'pickUp': 'Thu gọn',
        'more': 'Thêm',
        'continueToNextEpisode': 'Tập tiếp theo',
        'open': 'Mở'
      },
      'th-TH': {
        'continueWatching': 'ดูต่อ',
        'pickUp': 'พับเก็บ',
        'more': 'เพิ่มเติม',
        'continueToNextEpisode': 'ตอนต่อไป',
        'open': 'เปิด'
      },
      'id-ID': {
        'continueWatching': 'Lanjutkan menonton',
        'pickUp': 'Sembunyikan',
        'more': 'Lainnya',
        'continueToNextEpisode': 'Episode Selanjutnya',
        'open': 'Buka'
      },
      'tl-PH': {
        'continueWatching': 'Magpatuloy sa Panonood',
        'pickUp': 'Tiklop',
        'more': 'Marami pa',
        'continueToNextEpisode': 'Susunod na Episode',
        'open': 'Buksan'
      },
      'fr-FR': {
        'continueWatching': 'Continuer à regarder',
        'pickUp': 'Réduire',
        'more': 'Plus',
        'continueToNextEpisode': 'Épisode suivant',
        'open': 'Ouvrir'
      },
      'de-DE': {
        'continueWatching': 'Weiter schauen',
        'pickUp': 'Einklappen',
        'more': 'Mehr',
        'continueToNextEpisode': 'Nächste Folge',
        'open': 'Öffnen'
      },
      'it-IT': {
        'continueWatching': 'Continua a guardare',
        'pickUp': 'Riduci',
        'more': 'Altro',
        'continueToNextEpisode': 'Prossimo Episodio',
        'open': 'Apri'
      },
      'ru-RU': {
        'continueWatching': 'Продолжить просмотр',
        'pickUp': 'Свернуть',
        'more': 'Ещё',
        'continueToNextEpisode': 'Следующий эпизод',
        'open': 'Открыть'
      },
      'tr-TR': {
        'continueWatching': 'İzlemeye Devam Et',
        'pickUp': 'Daralt',
        'more': 'Daha Fazla',
        'continueToNextEpisode': 'Sonraki Bölüm',
        'open': 'Aç'
      },
      'ms-MY': {
        'continueWatching': 'Teruskan menonton',
        'pickUp': 'Tutup',
        'more': 'Lagi',
        'continueToNextEpisode': 'Episod Seterusnya',
        'open': 'Buka'
      }
    }


  </script>
  <!-- i18n ends -->

  <!-- main starts -->
  <script>
    const eventReported = {
      pageView: false,
      viewContent: false,
      startTrial: false,
    }
    window.closeClipboard = true
    let timer
    let clipboardData = ''
    let buffer = []
    let episodeId = ''
    let w2a_key = ''
    let needReportViewContent = false
    let reportNowTimer = null
    let createScriptTimer = null
    const environment = window.location.hostname.indexOf('test') >= 0 ? 'development' : 'prod'
    const backendUrl = 'https://trace.mydramawave.com/client_track'
    const backendUrlTest = 'https://trace-test.mydramawave.com/client_track'
    const search = window.location.search.replace(/^\?/, '')
    const language = navigator.language || 'en-US'
    const t = getTranslation(language)
    const data = [encodeURIComponent(search + `&click_time=${Date.now()}`)]
    const _query = new URLSearchParams(search)
    const query = Object.fromEntries(_query.entries())
    const pixelId = query['pid'] || '2246766269032711'
    const contentId = query['content_id']
    const tasks = { hmReady: false, seriesReady: false }
    function checkTasks() {
      if (tasks.hmReady && tasks.seriesReady) {
        // 这里可以做一些需要等两个任务都完成后再执行的操作
        const app = document.querySelector('#app')
        app.style.visibility = 'visible'
        addReport({
          event: 'landing_page_visible',
          event_info: JSON.stringify(getReportData()),
          load_time: window.t0_for_report ? Date.now() - window.t0_for_report : undefined
        })
        reportNow()
      }
    }
    fetchSeries(contentId).then(renderUi, () => {
      addReport({
        event: 'landing_page_load_series_failed',
        event_info: JSON.stringify(getReportData())
      })
      reportNow()
    })
      .finally(() => {
        tasks.seriesReady = true
        checkTasks()
      })
    // https://www.web2app.com.cn/developer?id=73
    const web2appDomain = 'luckanalysisdata.com'
    const onError = () => {
      const app = document.querySelector('#app')
      app.style.visibility = 'visible'
      addReport({
        event: 'landing_page_load_sdk_failed',
        event_info: JSON.stringify(getReportData())
      })
      reportNow()
    }
    const t3_for_report = Date.now()
    createScript('https://' + web2appDomain + '/jssdk/v10/dramawave?pid=' + pixelId + '&adt=' + data.join(','), () => {
      addReport({
        event: 'landing_page_load_sdk_time',
        event_info: JSON.stringify(getReportData({ load_time: Date.now() - t3_for_report })),
        load_time: Date.now() - t3_for_report
      })
      reportNow()
      tasks.hmReady = true
      checkTasks()
      window._clipboard(function (value) {
        clipboardData = value.replace(/^w2a_data:/g, 'w2a_data://')
        w2a_key = value.replace(/^w2a_data:/g, '')
        // 要等 w2a_key 获取到之后再上报
        addReport({
          event: 'landing_page_view',
          event_info: JSON.stringify(getReportData())
        })
        reportNow()
        if (needReportViewContent) {
          addReport({
            event: 'landing_view_content',
            event_info: JSON.stringify(getReportData())
          })
          reportNow()
        }
      })
    }, onError)
    window.addEventListener('load', function () {
      if (w2a_key) {
        addReport({
          event: 'landing_view_content',
          event_info: JSON.stringify(getReportData())
        })
        try {
          addReport({
            event: 'landing_performance',
            event_info: JSON.stringify(window.performance.getEntriesByType("navigation")[0])
          })
        } catch (e) { }
        reportNow()
      } else {
        needReportViewContent = true
      }
    })
    const button = document.getElementById('app');
    button.addEventListener('click', function () {
      try {
        writeToClipboard()
        sendEvent('BI_StartTrial')
        addReport({
          event: 'landing_start_trial',
          event_info: JSON.stringify(getReportData())
        })
        reportNow(true)
      } catch (err) { }
      goToApp()
    });
    window.onbeforeunload = function () {
      reportNow()
    }
    const more = document.querySelector('x-more')
    more && more.addEventListener('click', function () {
      const desc = document.querySelector('x-desc')
      desc.classList.remove('folded')
      desc.classList.add('unfolded')
      const more = document.querySelector('x-more')
      more.style.display = 'none'
      const pickUp = document.querySelector('x-pick-up')
      pickUp.style.display = 'block'
    })
    const pickUp = document.querySelector('x-pick-up')
    pickUp && pickUp.addEventListener('click', function () {
      const desc = document.querySelector('x-desc')
      desc.classList.add('folded')
      desc.classList.remove('unfolded')
      const more = document.querySelector('x-more')
      more.style.display = 'block'
      const pickUp = document.querySelector('x-pick-up')
      pickUp.style.display = 'none'
    })
    // 找到页面中所有 class 含有 i18n- 的元素，并设置其 textContent 为 t 的值
    const i18nElements = document.querySelectorAll('[data-i18n]')
    i18nElements.forEach(element => {
      element.textContent = t[element.dataset.i18n]
    })

    /************************************************************
     * Helpers
     ************************************************************/

    function writeToClipboard() {
      try {
        ClipboardJS.copy(clipboardData)
      } catch (e) {
        console.error('Error copying to clipboard:', e)
      }
    }

    function isIos() {
      return (
        window.navigator.userAgent
        && (/iP(ad|hone|od)/i.test(window.navigator.userAgent)
          // The new iPad Pro Gen3 does not identify itself as iPad, but as Macintosh.
          // https://github.com/vueuse/vueuse/issues/3577
          || (window.navigator.maxTouchPoints > 2 && /iPad|Macintosh/.test(window.navigator.userAgent)))
      )
    }
    /**
     * 上报事件
     * @return {Promise}
     */
    function sendEvent(eventName) {
      try {
        hm && hm.event(eventName)
      } catch (e) {
        addReport({
          event: 'landing_page_send_event_failed',
          event_info: JSON.stringify(getReportData())
        })
        reportNow()
      }
    }
    function goToApp() {
      const links = generateLinks()
      if (isIos()) {
        // iOS使用Universal Link
        window.location.href = links.universalLink
      } else {
        // document.addEventListener('visibilitychange', handleVisibilityChange)
        // timer = window.setTimeout(() => {
        //   window.open(links.googlePlayLink)
        // }, 2000)
        try {
          checkAndroidAppInstalled();
        } catch (e) {
          window.location.href = links.googlePlayLink
        }
      }
    }
    function handleVisibilityChange() {
      window.clearTimeout(timer) // 如果页面变为可见，清除定时器
    }
    function createScript(src, successCallback, errorCallback) {
      var script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = src;
      script.onload = () => {
        if (createScriptTimer) {
          window.clearTimeout(createScriptTimer)
          createScriptTimer = null
        }
        successCallback()
      };
      script.onerror = errorCallback
      createScriptTimer = window.setTimeout(() => {
        addReport({
          event: 'landing_page_load_sdk_timeout',
          event_info: JSON.stringify(getReportData())
        })
        reportNow()
        errorCallback()
      }, 5000)
      document.head.appendChild(script);
    }
    // https://api-test.mydramawave.com/dm-api/h5/series/info?series_id=QJHm97fn1X
    function fetchSeries(seriesId) {
      const baseUrl = environment === 'development' ? 'https://api-test.mydramawave.com' : 'https://api.mydramawave.com'
      const temp_t = Date.now()
      return fetch(`${baseUrl}/dm-api/drama/share/series_info?series_id=${seriesId}`)
        .then(res => {
          addReport({
            event: 'landing_page_load_series_time',
            event_info: JSON.stringify(getReportData({ load_time: Date.now() - temp_t })),
            load_time: Date.now() - temp_t
          })
          reportNow()
          return res.json()
        })
        .then(res => res.data.series_info)
    }

    function getOsName() {
      const ua = navigator.userAgent && navigator.userAgent.toLowerCase()
      if (ua.indexOf('windows') >= 0) return 'windows'
      if (ua.indexOf('macintosh') >= 0) return 'mac'
      if (ua.indexOf('iphone') >= 0) return 'ios'
      if (ua.indexOf('ipad') >= 0) return 'ios'
      if (ua.indexOf('ipod') >= 0) return 'ios'
      if (ua.indexOf('android') >= 0) return 'android'
      if (ua.indexOf('linux') >= 0) return 'linux'
      return 'unknown'
    }

    function addReport(data) {
      if (eventReported[data.event]) return
      eventReported[data.event] = true
      buffer.push({
        channel: environment === 'development' ? 'devtest' : 'prod',
        environment,
        href: window.location.href,
        page_title: document.title,
        referrer: document.referrer,
        screen_height: window.screen.height.toString(),
        screen_width: window.screen.width.toString(),
        user_agent: window.navigator.userAgent,
        time: Date.now().toString(),
        client_height: window.innerHeight.toString(),
        client_width: window.innerWidth.toString(),
        os_name: getOsName(),
        user_source: 'web',
        ...data,
      })
    }

    function reportNow(force) {
      if (reportNowTimer) {
        window.clearTimeout(reportNowTimer);
      }
      const report = () => {
        reportNowTimer = null
        const isDev = environment === 'development'
        if (buffer.length === 0) return
        const dataToSend = [...buffer]
        buffer = []
        const blob = new Blob([JSON.stringify(dataToSend)], { type: 'application/json' })
        navigator.sendBeacon(isDev ? backendUrlTest : backendUrl, blob)
      }
      if (force) {
        report()
      } else {
        reportNowTimer = window.setTimeout(report, 300)
      }

    }
    function renderUi(data) {
      episodeId = data.episode_list && data.episode_list[0] && data.episode_list[0].id || ''
      const { name, desc, cover, content_tags } = data
      const keys = [
        { key: 'name', kind: 'text' },
        { key: 'desc', kind: 'text' },
        { key: 'cover', kind: 'image' },
        { key: 'content_tags', kind: 'textArray' }
      ]
      keys.forEach(({ key, kind }) => {
        const elements = document.querySelectorAll('.ui-' + key)
        if (!elements.length) return
        const value = data[key]
        elements.forEach(element => {
          if (kind === 'image') {
            const t = Date.now()
            element.src = value ?? ''
            element.onload = function () {
              addReport({
                event: 'landing_page_load_image_time',
                event_info: JSON.stringify(getReportData({
                  load_time: Date.now() - t,
                  total_time: Date.now() - (window.t0_for_report || 0),
                  load_src: element.src
                })),
                load_time: Date.now() - t,
                total_time: Date.now() - (window.t0_for_report || 0),
                load_src: element.src
              })
              reportNow()
            }
          } else if (kind === 'text') {
            element.textContent = value ?? ""
          } else if (kind === 'textArray') {
            if (value) {
              value.forEach(item => {
                const li = document.createElement('li')
                li.textContent = item
                element.appendChild(li)
              })
            } else {
              element.style.display = 'none'
            }
          }
        })
      })
    }
    // 支持 en-US, ja-JP, ko-KR, es-ES, pt-PT, vi-VN, th-TH, id-ID, tl-PH, fr-FR, de-DE, it-IT, ru-RU, tr-TR, ms-MY, zh-TW, zh-Cn
    function getTranslation(language) {
      return window.i18n[language] || i18n['en-US']
    }
    function getReportData(more) {
      more = more || {}
      return {
        ...query,
        pid: pixelId,
        pid_from: query['pid'] ? 'url' : 'default',
        fbc: window.__fbc__,
        fbp: window.__fbp__,
        w2a_key,
        __ip__: window.__ip__,
        __apn__: window.__apn__,
        __pid__: window.__pid__,
        __adv__: window.__adv__,
        __adt__: window.__adt__,
        __dad__: window.__dad__,
        __exid__: window.__exid__,
        ...more,
      }
    }
    /**
     * 通过对象生成查询参数字符串，所有 value 自动转义
     * @param queries 要设置的查询参数对象
     */
    function createQueryString(queries) {
      const p = new URLSearchParams()
      Object.entries(queries).forEach(([key, value]) => {
        value && p.set(key, value.toString())
      })
      return p.toString()
    }

    function checkAndroidAppInstalled() {
      const iframe = document.getElementById("iframe");
      const { deepLink, googlePlayLink } = generateLinks()
      // 再执行谷歌商店跳转
      iframe.src = googlePlayLink;
      // 先自启用
      iframe.src = deepLink;
    }

    function generateLinks() {
      // dramawave://dramawave.app?redirect=/datail?id=MhC8rlRsV&timestamp=1678901234567&campaign id=120216175334570464&
      // adset id=120218590781410617&ad id=120217087272680464&fbp=fb.1.1596403881668.1116446470&fbc=fb.1.1554763741205.AbCdEfGhIiKlMnOpOrStUvWxYz&channel=facebook
      const qs = createQueryString({
        id: contentId,
        w2a_key: 'w2a_data:' + w2a_key,
        timestamp: Date.now(),
        campaign_id: query['hm_cp_id'],
        adset_id: query['hm_adset_id'],
        ad_id: query['hm_ad_id'],
        fbp: window.__fbp__,
        fbc: window.__fbc__,
        channel: query['channel'],
        dpsource: 'W2A',
        pixel_id: pixelId,
      })
      // const deepLink = `intent://push?type=1&playletId=3283&fragmentId=0&callback=dramawave://dramawave.app?redirect=${encodeURIComponent('/detail?' + qs)}#Intent;scheme=dramawave;package=com.dramawave.app;S.browser_fallback_url=market://details?id=com.dramawave.app;end`;
      const deepLink = `intent://dramawave.app?redirect=${encodeURIComponent('/detail?' + qs)}&w2a_key=${encodeURIComponent('w2a_data:' + w2a_key)}#Intent;scheme=dramawave;package=com.dramawave.app;end;`
      // const deepLink = `dramawave://dramawave.app?redirect=${encodeURIComponent('/detail?' + qs)}`
      const universalLink = `https://mydramawave.com?redirect=${encodeURIComponent('/detail?' + qs)}&w2a_key=${encodeURIComponent('w2a_data:' + w2a_key)}`
      // const googlePlayLink = 'https://play.google.com/store/apps/details?id=com.dramawave.app'
      const googlePlayLink = 'market://details?id=com.dramawave.app'
      return { deepLink, universalLink, googlePlayLink }
    }


  </script>
  <!-- main ends -->
  <script>
    window.t1_for_report = Date.now()
    const load_time = window.t1_for_report - window.t0_for_report
    addReport({
      event: 'landing_page_load_time',
      event_info: JSON.stringify(getReportData({ load_time: load_time })),
      load_time: load_time
    })
    reportNow()
  </script>

</body>



</html>