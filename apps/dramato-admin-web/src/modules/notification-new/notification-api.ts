/* eslint-disable @typescript-eslint/no-explicit-any */
import { get_k_sso_token } from 'src/lib/device-id'
import { httpClient } from 'src/lib/http-client'

export const apiGetInfoPushList = (data: M.PushNotificationSearchOptionNew) =>
  httpClient.post<ApiResponse<M.PushNotificationListRequestResponseNew>>('/push/v2/task/list', data)

export const apiGetPushConfig = () =>
  httpClient.get<ApiResponse<M.PushConfigData>>('/push/v2/config')

export const apiTranslate = (data: { text: string, type: M.TranslateType }) =>
  httpClient.post<ApiResponse<{ type: M.TranslateType, translate: Record<string, string> }>>('/push/v2/translate', data)

export const apiSavePush = (data: M.NewCreatePushForm) =>
  httpClient.post<ApiResponse>('/push/v2/task/save', {
    ...data,
    style_config: JSON.stringify(data.style_config || {}),
  })

export const apiDeletePush = (data: { push_task_id_list: number[] }) => {
  return httpClient.post<ApiResponse>('/push/v2/task/delete', data)
}

export const apiDownloadPush = (data: { id_list: number[], type: number, list_type: number }) =>
  httpClient.post<ApiResponse>('/push/v2/list/download', data)

export const apiExportExcel = (data: any) =>
  fetch(`${import.meta.env.VITE_DRAMA_API_URL}/push/v2/list/download`, {
    method: 'post',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      Device: 'Web',
      Token: get_k_sso_token() || '',
    },
    body: JSON.stringify(data),
  })

export const apiGetPrompts = (rec_type: number) => httpClient.post<ApiResponse<{ items: M.Prompt[] }>>('/push/v2/prompt/list', { rec_type })

export const apiSavePrompt = (rec_type: number, prompt: string) => httpClient.post<ApiResponse<{ items: M.Prompt[] }>>('/push/v2/prompt/save', { rec_type, prompt })

export const apiGenPushText = (data: { rec_type: number, prompt_id: number, language: string, list_type: number, id: number }) =>
  httpClient.post<ApiResponse<{ gen_txt: M.PreviewReplaceResult }>>('/push/v2/prompt/gen_txt', data)

export const apiSavePushText = (data: { rec_type: number, prompt_id: number, language: string, list_type: number, id: number, gen_txt: M.PreviewReplaceResult }) =>
  httpClient.post<ApiResponse<{ gen_txt: M.PreviewReplaceResult }>>('/push/v2/prompt/gen_txt/save', data)

export const apiUpdatePushState = (data: { list_type?: number, rec_type?: number, push_task_id: number, language: string, state: number, push_title_content_id?: number }) =>
  httpClient.post<ApiResponse<{ }>>('/push/v2/state/update', data)
