import { createComponent } from '@skynet/shared'
import { commonBg, freeReels, series, logo } from './image/image'
import { ref } from 'vue'
import { Button, Input } from '@skynet/ui'

export const AccountDeletionPage = createComponent(null, () => {
  const account = ref<string>()
  const visible = ref<boolean>(false)
  const error = ref<boolean>(false)

  return () => (
    <div class="size-screen overflow-hidden px-[6.5%] relative pt-[60px] select-none">
      {
        error.value
          ? (
              <div class="absolute z-up top-25 left-1/2 -translate-x-1/2 h-10 px-4 py-3 bg-[#434546] rounded-lg shadow justify-start items-center gap-1 flex">
                <div class="grow shrink basis-0 text-[#fdfbfc] text-sm font-normal font-['SF Pro'] leading-[16.80px]">Please enter the correct account ID !</div>
              </div>
            )
          : null
      }
      {visible.value
        ? (
            <>
              <div class="absolute top-0 left-0 w-full h-full z-up bg-[#0b080b] opacity-50" />
              <div class="absolute left-1/2 top-1/2 z-up-up w-[400px] h-[150px] p-5 bg-[#2e2f30] -translate-1/2 rounded-lg flex-col justify-start items-center gap-8 flex">
                <p class=" text-[#cccacb] text-sm font-normal font-['SF Pro'] leading-[16.80px]">Your deletion request has been received and your account will be deleted in 14 days !</p>
                <Button onClick={() => {
                  visible.value = false
                  account.value = ''
                }} class="w-full h-11 bg-[#fc2763] cursor-pointer rounded-lg justify-center items-center gap-2.5 inline-flex border-none text-[#fdfbfc]/90 text-base font-medium font-['PingFang SC'] leading-relaxed"
                >I know
                </Button>
              </div>
            </>
          )
        : null}
      <img src={commonBg} class="size-full absolute top-0 left-0 object-cover" />
      <img src={series} width={756} height={1080} class="w-auto h-screen absolute top-0 right-[7.8125%] object-contain" />
      <div class="relative size-full z-up flex flex-col items-start">
        <div class="flex items-center w-full gap-8">
          <img src={logo} class="h-auto w-[4.491%] object-cover" />
          <img src={freeReels} class="w-[11.32%] h-auto object-cover" />
        </div>
        <div class="inline-flex flex-col text-white w-[52ex] h-[35.56%] absolute left-0 gap-10 top-[40%] -translate-y-1/2 text-7 font-normal font-['PingFang HK'] leading-8">
          <div class="text-3xl font-semibold font-['PingFang HK'] leading-9">Account Deletion</div>
          <div class="flex items-center">
            Account : <Input inputClass="box-border !bg-transparent text-[#797b7d] text-sm font-normal w-[80%] appearance-none leading-[16.80px] p-3 rounded border border-solid outline-none border-[#434546]" class="inline-block flex-1 ml-4" v-model={account.value} placeholder="e-mail address" />
          </div>
          <div>
            <p class="break-words">
              If you delete your account, you'll can't get your data back(Including any property such as coins, bonus, etc).
            </p>
            <p class="break-words mt-2">Sure you accept all the deletion risk and agree to delete your account?</p>
          </div>
          <Button
            onClick={() => {
              if (account.value && account.value.includes('@')) {
                error.value = false
                visible.value = true
              } else {
                error.value = true
                window.setTimeout(() => {
                  error.value = false
                }, 3000)
              }
            }}
            class="shrink-0 w-[263px] h-11 bg-[#fc2763] cursor-pointer rounded-lg justify-center items-center gap-2.5 inline-flex border-none text-[#fdfbfc] text-[15px] font-medium font-['PingFang SC'] leading-relaxed"
          >Confirm
          </Button>
        </div>
      </div>
    </div>
  )
})

export default AccountDeletionPage
