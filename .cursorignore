.git
# Ignore all node_modules directories
node_modules

# Ignore the .cursorignore file itself
.cursorignore

# Ignore build directories
dist
build

# Ignore log files
*.log

# Ignore environment variable files
.env
.env.local
.env.*.local

# Ignore temporary files
*.tmp
*.temp

# Ignore coverage directories
coverage

# Ignore IDE specific files
.vscode/
.idea/

# Ignore OS specific files
.DS_Store
Thumbs.db

# Ignore lock files
yarn.lock
package-lock.json

# Ignore generated files
*.generated.*

# Ignore test output
test-output/

# Ignore specific files or directories
