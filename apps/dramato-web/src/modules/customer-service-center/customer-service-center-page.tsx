import { createComponent, getQueries, getQuery } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { CollapseItem } from './collapse-item'
import { onMounted, ref } from 'vue'
import { track } from 'src/lib/track'
import { useLocale } from 'src/lib/use-locale'
import { faq } from './all-faq'
import { Back } from 'src/h5_modules/common/back/back'
import { jsBridge } from 'src/lib/jsbridge'
import { rod } from './images/images'
import { apiGetSubscribeStatus, MyFeedbackApi } from './feedback/feedback-api'
import { useRouter } from 'vue-router'
import { isIos } from 'src/lib/ua'

export const CustomerServiceCenterPage = createComponent(null, props => {
  const router = useRouter()
  const language = router.currentRoute.value.query.language as string
  const from = router.currentRoute.value.query.from as string
  const country = getQuery('country_code', '')
  const { showNavigation } = getQueries({ showNavigation: true })

  const { t } = useLocale(language)

  const faqList = (faq as Record<string, { question: string, answer: string }[]>)[`${language}-${country}`] ?? faq['en-US']

  const wrapper = ref<HTMLDivElement>()
  const hasRedDot = ref(false)
  const showBack = ref(false)

  onMounted(() => {
    // 设置不同语言下的title
    document.title = t('customer-service-center.title')
    // 埋点上报
    track('csc', 'page', 'show', {
      from,
    })

    void MyFeedbackApi.hasRedDot().then(res => {
      hasRedDot.value = res.data?.fd_has_red_dot ?? false
    })

    if (jsBridge.inDramawaveApp) {
      showBack.value = !showNavigation
    } else {
      showBack.value = true
    }
  })

  const isSubscribe = ref<boolean>(false)

  onMounted(() => {
    void apiGetSubscribeStatus().then(res => {
      const info = res.data || {
        uid: 0,
        expire_time: 0,
        status: 2, // 状态(0-预备;1-正常; 2-取消; 3-结束),0/1--开，2/3--关
      }
      isSubscribe.value = [0, 1].includes(info.status)
    }).catch(e => {
      if (e.response?.data?.code === 1025) {
        isSubscribe.value = false
      }
    })
  })

  return () => (
    <div ref={wrapper} class="pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] flex h-screen w-full flex-col overflow-y-hidden scroll-smooth bg-[var(--black)]">
      {showBack.value && (
        <Back
          backTo={jsBridge.inDramawaveApp ? () => {
            void jsBridge('close')
          } : undefined}
          title={t('customer-service-center.title')}
          isWhite={true}
          class="shrink-0 bg-transparent">
          <div class="relative size-6" onClick={() => {
            track('feedback', 'myfeedback', 'click')
            void router.push({ path: 'customer-service-center/my-feedback', query: { ...router.currentRoute.value.query, from: 'csc', showBack: showBack.value ? 1 : 0 } })
          }}>
            <img src={rod} class="size-6" />
            {hasRedDot.value && <x-dot class="z-10 absolute right-0 top-0 size-1.5 rounded-full bg-[#FC2763]" />}
          </div>
        </Back>
      ) }
      <x-collapse-list class="pb-23 flex w-full flex-1 flex-col gap-y-2 overflow-auto px-3 pt-5">
        {faqList.map(item => (
          <CollapseItem
            key={item.question}
            title={item.question}
            content={item.answer}
            button=""
          />
        ))}
        {!!isSubscribe.value && (
          <CollapseItem
            key={t('terminate.question')}
            title={t('terminate.question')}
            content={t('terminate.desc')}
            button={t('terminate.title')}
            onClickButton={() => void router.push({ path: 'customer-service-center/terminate', query: { ...router.currentRoute.value.query, from: 'csc', showBack: showBack.value ? 1 : 0 } })}
          />
        )}
      </x-collapse-list>
      <div class={`pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] fixed bottom-0 left-1/2 z-shareButton w-full -translate-x-1/2 bg-[var(--black)] ${jsBridge.inDramawaveApp ? 'h-23' : 'share-bottom-2 flex h-16 items-center pb-2 pt-3'}`}>
        <Button
          class="no-tap-color dramato-button pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] fixed  left-1/2 z-shareButton box-border h-11 w-[calc(100%-1.5rem)] -translate-x-1/2 cursor-pointer text-nowrap rounded-lg border-none bg-[var(--brand-6)] text-base font-medium leading-tight text-[var(--text-5)] outline-none"
          onClick={() => {
            track('csc', 'feedback', 'click')
            void router.push({ path: 'customer-service-center/feedback', query: { ...router.currentRoute.value.query, from: 'csc', showBack: showBack.value ? 1 : 0 } })
          }}
        >{t('customer-service-center.i-want-to-give-feedback')}
        </Button>
      </div>
    </div>
  )
})

export default CustomerServiceCenterPage
