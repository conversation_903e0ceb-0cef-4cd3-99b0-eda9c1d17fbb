/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, useValidator, mc } from '@skynet/shared'
import { Button, CreateForm, transformInteger, transformNumber2, transformNumber, Icon, RadioGroup, openDialog, showAlert, showSuccessToast, showFailToast } from '@skynet/ui'
import { z } from 'zod'
import { AxiosError } from 'axios'
import { requiredLabel } from 'src/lib/required-label'
import { set, get, cloneDeep } from 'lodash-es'
import { periodList, currencyList } from 'src/lib/constant.ts'
import { onMounted, ref } from 'vue'
import { apiCreateMember, apiUpdateMember } from './member.api'
import { useMember } from './use-member.tsx'
export const MemberForm = createComponent(null, () => {
  const Form = CreateForm<M.MemberLevel>()
  const btnLoading = ref(false)
  const numbers = Array.from({ length: 12 }, (_, index) => ({
    value: index + 1,
    label: String(index + 1),
  }))
  const {
    defaultData,
    form,
    dialogRef,
    appList,
    onQuery,
    triggerElementRef,
  } = useMember()
  const formRules = z.object({
    title: z.string().min(1, '请输入商品名称'),
    title_admin: z.string().min(1, '请输入档位说明'),
    app_id: z.number().min(1, {
      message: '请选择应用名称',
    }),
    discount_desc: z.string().min(1, {
      message: '请输入折扣说明',
    }),
    priority: z.number().min(1, {
      message: '请输入正确序号',
    }).int({
      message: '请输入正确序号 1-12',
    }).max(12, {
      message: '请输入正确序号 1-12',
    }),
    delivery_details: z.object({
      quanity: z.number().min(1, {
        message: '请输入兑换金币数',
      }).int({
        message: '请输入1-99999之前的整数',
      }).max(99999, {
        message: '请输入1-99999之前的整数',
      }),
      bonus: z.number().min(1),
      daily_bonus: z.number().min(1),
      period: z.string().min(1, {
        message: '请选择订阅周期单位',
      }),
    }),
    price: z.number().min(0.01, {
      message: '请输入价格',
    }).refine((value: number) => {
      const regex = /^\d+(\.\d{1,2})?$/
      return regex.test('' + value)
    }, {
      message: '金额必须为正数，且最多包含两位小数',
    }),
    discount_price: z.number().min(0, {
      message: '请输入价格',
    }).refine((value: number) => {
      const regex = /^\d+(\.\d{1,2})?$/
      return regex.test('' + value)
    }, {
      message: '金额必须为正数，且最多包含两位小数',
    }),
    membership_ad_config: z.object({
      watch_ad_times: z.number().min(0, {
        message: '请输入观看广告次数',
      }).int({
        message: '请输入0-999之前的整数',
      }).max(999, {
        message: '请输入0-999之前的整数',
      }),
      ad_units: z.object({
        max: z.string().min(1, {
          message: '请输入广告时长单位',
        }),
        admob: z.string().min(1, {
          message: '请输入广告时长单位',
        }),
      }),
      upgrade_product_id: z.number().min(1),
      upgrade_tips: z.string().min(1, '请输入提示'),
      upgrade_desc: z.string().min(1, '请输入升级提示'),
    }),
    sku_id: z.string().min(1, '请输入商品ID'),
  })
  const { error, validateAll } = useValidator(form, formRules)
  onMounted(async () => {
  })

  const onClose = () => {
    form.value = cloneDeep(defaultData)
    dialogRef.value()
  }
  const onSave = async () => {
    const exclude: string[] = []
    if (form.value.status !== 1) {
      exclude.push('priority')
      form.value.priority = 0
    }
    //  不打折的时候
    if (form.value.first_recharge !== 1) {
      exclude.push('discount_price')
      exclude.push('discount_desc')
    }
    if (form.value?.membership_type !== 'ad') {
      exclude.push('membership_ad_config')
    }
    if (form.value?.membership_type !== 'coins_pass') {
      exclude.push('delivery_details.bonus')
      exclude.push('delivery_details.daily_bonus')
    }
    if (validateAll({ exclude: exclude })) {
      try {
        let res
        const params = cloneDeep(form.value)
        if (form.value.first_recharge === 1) { // 有折扣信息
          params.has_discount = 1
        } else {
          params.has_discount = 0
        }
        btnLoading.value = true
        params.price = params.price ? +(params.price * 100).toFixed(2) : 0
        params.discount_price = params.discount_price ? +(params.discount_price * 100).toFixed(2) : 0
        if (form.value.first_recharge !== 1) {
          params.discount_price = params.price
          params.discount_desc = ''
        }
        if (form.value.membership_type !== 'ad') {
          delete params.membership_ad_config
        }
        if (form.value.membership_type !== 'coins_pass') {
          params.delivery_details!.bonus = undefined
          params.delivery_details!.daily_bonus = undefined
        }
        delete params.member_limited
        if (form.value.platform === 'h5') {
          params.platform = ''
          params.store = ''
        }
        if (!form.value.id) {
          res = await apiCreateMember(params)
        } else {
          res = await apiUpdateMember(params)
        }
        if (res.code === 200) {
          showSuccessToast('操作成功！')
        } else {
          showFailToast(res?.message || '服务忙碌，稍后再试')
        }
        await onQuery()
      } catch (e) {
        const error = e as AxiosError
        showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
      } finally {
        btnLoading.value = false
      }
    } else {
      btnLoading.value = false
      console.log(error, 'error')
      return
    }
    onClose()
  }

  const calcCoins = () => {
    if (!form.value.delivery_details?.bonus) {
      return showAlert('请输入赠送补给', 'error')
    }
    form.value.delivery_details.daily_bonus = Math.ceil(form.value.delivery_details.bonus / (form.value.delivery_details.periodDay || 1))
  }

  return () => (
    <>
      <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
        <Form
          class="flex w-full flex-col flex-nowrap"
          data={form.value}
          onChange={(path, value) => {
            if (path === 'app_id') {
              form.value.app_name = appList.value.find(row => row.value === value)?.label
            }
            if (path === 'membership_type') {
              set(form.value, path, value)
              set(form.value, 'delivery_details.quanity', 1)
              if (form.value.membership_type === 'coins_pass') {
                set(form.value, 'delivery_details.periodDay', 7)
                set(form.value, 'delivery_details.quanity', undefined)
              }
            }
            if (path === 'delivery_details.period') {
              set(form.value, path, value)
              set(form.value, 'delivery_details.periodDay', periodList.find(item => item.value === form.value?.delivery_details?.period)?.days)
            }
            set(form.value, path, value)
          }}
          hasAction={false}
          error={error.value}
          items={[
            <h1 class="border-l-solid border-l-2 border-l-primary pl-[10px] font-medium">商店信息</h1>,
            { label: requiredLabel('上架商店'),
              path: 'store',
              input: {
                type: 'select',
                disabled: true,
                autoInsertEmptyOption: false,
                options: [
                  {
                    label: 'Apple Store',
                    value: 'Apple Store',
                  },
                  {
                    label: 'Google Play',
                    value: 'Google Play',
                  },
                ],
              },
              class: mc(form.value.store == 'h5' ? 'hidden' : ''),
            },
            { label: requiredLabel('权益类型'),
              path: 'membership_type',
              input: {
                type: 'radio',
                autoInsertEmptyOption: false,
                options: [
                  {
                    label: '普通会员',
                    value: 'vip',
                  },
                  {
                    label: 'Pro权益会员',
                    value: 'pro',
                  },
                  {
                    label: '广告会员',
                    value: 'ad',
                  },
                  {
                    label: '金币通行证',
                    value: 'coins_pass',
                  },
                ],
                class: 'gap-2',
              },
            },
            { label: requiredLabel('商品ID（product_id）'), path: 'sku_id', input: { type: 'text' } },
            { label: requiredLabel('商品名称'), path: 'title', input: { type: 'text' } },
            {
              label: requiredLabel('币种'),
              path: 'currency',
              input: {
                type: 'select',
                options: currencyList,
                autoInsertEmptyOption: false,
                disabled: !!form.value.id,
              },
            },
            {
              label: requiredLabel('价格'),
              path: 'price',
              input: {
                type: 'number',
                suffix: <>元</>,
                min: '0',
                step: '0.01',
                placeholder: '支持小数点后两位',
              },
              transform: transformNumber2,
            },
            { label: requiredLabel('会员限时'), path: 'member_limited', input: { type: 'number', placeholder: '可输入整数1-99999', disabled: true }, transform: transformNumber, class: mc(form.value.membership_type == 'coins_pass' ? '' : 'hidden') },
            { label: requiredLabel('会员限时'), path: 'delivery_details.quanity', input: { type: 'number', placeholder: '可输入整数1-99999' }, transform: transformNumber, class: mc(form.value.membership_type == 'coins_pass' ? 'hidden' : '') },
            { label: requiredLabel('时限周期'), path: 'delivery_details.period', input: { type: 'select', options: periodList, autoInsertEmptyOption: false } },
            {
              label: requiredLabel('档位属性'),
              path: 'first_recharge',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  { label: '常规档位', value: 0 },
                  { label: '首订优惠', value: 1 },
                  { label: '再订阅', value: 2 },
                ],
              },
              hint: '首订优惠仅会展示给第一次订阅的用户，而再订阅反之',
            },
            form.value.first_recharge === 1 && <h1 class={mc('font-medium border-l-[2px] border-l-solid border-l-primary pl-[10px]')}>折扣信息</h1>,
            form.value.first_recharge === 1 && {
              label: requiredLabel('折扣价'),
              path: 'discount_price',
              input: {
                type: 'number',
                suffix: <>元</>,
                min: '0',
                step: '0.01',
                placeholder: '支持小数点后两位',
                // disabled: !!form.value.id,
              },
              transform: transformNumber2,
            },
            form.value.first_recharge === 1 && { label: requiredLabel('折扣说明'), path: 'discount_desc', input: { type: 'text' } },
            form.value.membership_type === 'coins_pass' && <h1 class={mc('border-l-solid border-l-primary border-l-2 pl-[10px] font-medium')}>金币兑换和补给</h1>,
            form.value.membership_type === 'coins_pass' && { label: requiredLabel('兑换金币数（Coins）'), path: 'delivery_details.quanity', input: { type: 'number', placeholder: '可输入整数1-99999' }, transform: transformNumber },
            form.value.membership_type === 'coins_pass' && { label: requiredLabel('赠送补给（Bonus）'), path: 'delivery_details.bonus', input: { type: 'number', placeholder: '可输入整数1-99999' }, transform: transformNumber },
            form.value.membership_type === 'coins_pass' && { label: requiredLabel('赠送天数'), path: 'delivery_details.periodDay', input: { type: 'number', placeholder: '正整数', disabled: true }, transform: transformNumber },
            form.value.membership_type === 'coins_pass' && {
              label: <x-title class="flex items-center justify-between"><div class="flex items-end">{requiredLabel('每日补给')}<span class="ml-2 text-[12px] text-gray-400">请先填写Bonus和天数</span></div><span class="cursor-pointer text-sm text-blue-400" onClick={calcCoins}>计算</span></x-title>,
              path: 'delivery_details.daily_bonus',
              input: {
                type: 'number',
                placeholder: '请输入整数1-99999',
                min: 1,
                max: 99999,
              },
              transform: transformInteger,
            },

            <h1 class="border-l-solid border-l-2 border-l-primary pl-[10px] font-medium">档位配置</h1>,
            { label: requiredLabel('档位说明'), path: 'title_admin', input: { type: 'text' } },
            { label: requiredLabel('生效应用：'), path: 'app_id', input: { type: 'select', options: appList.value.filter(i => i.platform === (form.value.store === 'Google Play' ? 'Android' : form.value.store === 'Apple Store' ? 'iOS' : 'h5')), autoInsertEmptyOption: false, disabled: !!form.value.id }, transform: transformNumber },
            { label: requiredLabel('支付面板展示：'), path: 'status', input: { type: 'radio', options: [
              {
                value: 1,
                label: '展示',
              },
              {
                value: 2,
                label: '不展示',
              },
            ] }, transform: transformInteger },
            form.value.status === 1 && {
              label: requiredLabel('序号：'),
              path: 'priority', input: { type: 'select', options: numbers, autoInsertEmptyOption: false }, transform: transformNumber,
            },
            form.value?.membership_type === 'ad' && [
              requiredLabel('用户每观看多少分钟后，展示一次广告'),
              'membership_ad_config.watch_ad_times',
              {
                type: 'number',
                min: 0,
                suffix: '分',
              },
              {
                transform: transformNumber,
              },
            ],
            form.value?.membership_type === 'ad' && [
              requiredLabel('广告单元-max'),
              'membership_ad_config.ad_units.max',
              {
                type: 'textarea',
                placeholder: '多个广告ID请用英文逗号隔开',
              },
            ],
            form.value?.membership_type === 'ad' && [
              requiredLabel('广告单元-admob'),
              'membership_ad_config.ad_units.admob',
              {
                type: 'textarea',
                placeholder: '多个广告ID请用英文逗号隔开',
              },
            ],
            form.value?.membership_type === 'ad' && [
              requiredLabel('升级档位'),
              'membership_ad_config.upgrade_product_id',
              {
                type: 'number',
                min: 0,
                placeholder: '请输入升级的档位ID',
              },
              {
                transform: transformNumber,
              },
            ],
            form.value?.membership_type === 'ad' && [
              requiredLabel('主文案'),
              'membership_ad_config.upgrade_tips',
              {
                type: 'textarea',
              },
            ],
            form.value?.membership_type === 'ad' && [
              requiredLabel('升级提示'),
              'membership_ad_config.upgrade_desc',
              {
                type: 'textarea',
              },
            ],
            { label: <x-title class="flex items-center justify-between">权益说明 <span class="cursor-pointer text-sm text-blue-400" ref={triggerElementRef}>快捷填入</span></x-title>, path: 'description', input: { type: 'text' } },
            { label: '提示文案：', path: 'tips', input: { type: 'text' } },
            { label: '角标文案：', path: 'slogan', input: { type: 'text' } },
            [requiredLabel('展示平台：'), 'show_platform', { type: 'radio', options: [
              {
                value: 'ios',
                label: 'iOS',
              },
              {
                value: 'h5',
                label: 'H5',
              },
            ] }, {
              class: mc(form.value.store == 'h5' ? '' : 'hidden'),
            }],
          ] as any}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button disabled={btnLoading.value} class="btn btn-primary btn-sm" onClick={() => {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          onSave()
        }}
        >
          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
          确定
        </Button>
      </div>
    </>
  )
})
