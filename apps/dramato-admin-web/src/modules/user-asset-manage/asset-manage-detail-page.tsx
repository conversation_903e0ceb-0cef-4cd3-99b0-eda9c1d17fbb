import { createComponent, mc } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { Wrapper } from 'src/layouts/wrapper'
import { AssetSureForm } from './asset-sure-form'
import { AssetFinistForm } from './asset-finish-form'
import { useUserAssetManage } from './use-user-asset-manage'

type AssetManageDetailPageOptions = {
  props: {}
}
export const AssetManageDetailPage = createComponent<AssetManageDetailPageOptions>({
  props: {},
}, props => {
  const { btnList } = useUserAssetManage()
  const route = useRoute()
  const router = useRouter()
  const step = ref(1)
  btnList.value = []
  const setStep = (stepNum: number) => {
    if (stepNum === step.value) return
    step.value = stepNum
  }

  onMounted(async () => {
    // void getCommodityList()
  })

  return () => (
    <div class="pt-4">
      <div class="text-sm breadcrumbs">
        <ul>
          <li><RouterLink to="/user-asset-manage">用户资产管理</RouterLink></li>
          <li>{step.value < 2 ? '资产转移确认' : '转移完成' }</li>
        </ul>
      </div>
      <Wrapper>
        <div class="">
          <aside class="w-full">
            <ul class="flex steps">
              <li class={mc('step w-[100px] h-[100px]', 'step-primary')}>用户查询</li>
              <li class={mc('step w-[100px] h-[100px]', step.value > 0 ? 'step-primary' : '')}>资产确认</li>
              <li class={mc('step w-[100px] h-[100px]', step.value > 1 ? 'step-primary' : '')}>转移完成</li>
            </ul>
          </aside>
          <div class="flex-1">
            {
              step.value === 1
                ? <AssetSureForm onStep={setStep} />
                : <AssetFinistForm onStep={setStep} />
            }
          </div>
        </div>
      </Wrapper>
    </div>
  )
})

export default AssetManageDetailPage
