import Hex from 'crypto-js/enc-hex'
import Utf8 from 'crypto-js/enc-utf8'
import MD5 from 'crypto-js/md5'
import Cookies from 'js-cookie'

export const get_authorization = () => {
  const params = JSON.parse(Cookies.get('params') ?? '{}')
  if (!params.appSecret || !params.secret || !params.key) return
  const rawSignature = `${params.appSecret}&${params.secret}`
  const signature = MD5(rawSignature).toString()
  return `oauth_signature=${signature},oauth_token=${params.key},ts=${new Date().getTime()}`
}

export const get_authorization_for_server = () => {
  const ts = Date.now()
  const auth_params = useCookie<{
    user_id: string
    auth_key: string
    auth_secret: string
  }>('auth_params')
  const nuxtApp = useNuxtApp()
  if (!auth_params.value?.user_id) {
    auth_params.value = nuxtApp.$auth_params as {
      user_id: string
      auth_key: string
      auth_secret: string
    }
  }


  const APP_SECRET = '8IAcbWyCsVhYv82S2eofRqK1DF3nNDAv'

  // 计算 MD5
  // console.log('auth_params', auth_params)
  const hash = MD5(
    Utf8.parse(`${APP_SECRET}&${auth_params.value.auth_secret}`),
  )
  const signed = hash.toString(Hex)

  return `oauth_signature=${signed},oauth_token=${auth_params.value.auth_key},ts=${ts}`
}

export const get_authorization_for_browser = () => {
  const ts = Date.now()
  const auth_params = JSON.parse(Cookies.get('auth_params') ?? '{}')
  const APP_SECRET = '8IAcbWyCsVhYv82S2eofRqK1DF3nNDAv'

  // 计算 MD5
  // console.log('auth_params', auth_params)
  const hash = MD5(
    Utf8.parse(`${APP_SECRET}&${auth_params.auth_secret}`),
  )
  const signed = hash.toString(Hex)

  return `oauth_signature=${signed},oauth_token=${auth_params.auth_key},ts=${ts}`
}
