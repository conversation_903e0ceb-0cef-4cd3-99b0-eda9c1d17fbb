import { use<PERSON><PERSON><PERSON><PERSON> } from '@skynet/shared'
import { cloneDeep } from 'lodash-es'
import { ref } from 'vue'
import { z } from 'zod'

const hasExtra = ref<boolean>(false)
const userWhiteListEnabled = ref<boolean>(false)
const rewardCoins = ref<number>(0)
const discount = ref<number>(0)
const previousExtra = ref<M.RewardsConfig.CheckInExtra[]>()
const previousWhiteUserList = ref<string[] | undefined>()
const canEditExtra = ref<boolean>(false)
const canEditDiscount = ref<boolean>(false)

const defaultFormData = {
  area: 'General', // 地区：通用、美国等
  action: 1, // 1:保存，2：重置
  area_show_name: '通用', // 地区显示名称
  check_in: {
    base: [{
      day: 1,
      reward_coins: undefined,
    }, {
      day: 2,
      reward_coins: undefined,
    }, {
      day: 3,
      reward_coins: undefined,
    }, {
      day: 4,
      reward_coins: undefined,
    }, {
      day: 5,
      reward_coins: undefined,
    }, {
      day: 6,
      reward_coins: undefined,
    }, {
      day: 7,
      reward_coins: undefined,
    }] as M.RewardsConfig.CheckInBase[],
    extra: [] as M.RewardsConfig.CheckInExtra[],
  },
  special_offers: {
    list: [] as M.ApplicationDramaItem[],
  },
  watch_ad: {
    list: [] as M.RewardsConfig.WatchAd[],
  },
  white_user: {
    list: [] as string[],
  },
} as M.RewardsConfig

const formData = ref<M.RewardsConfig>(defaultFormData)

const formRules = z.object({
  check_in: z.object({
    base: z.array(z.object({
      day: z.number().min(1).max(7),
      reward_coins: z.number().int('必须为整数').min(0).max(9999),
    })).length(7, '必须填写7项'),
    extra: z.array(z.object({
      reward_coins: z.number().int('必须为整数').min(0).max(9999),
    })).min(1, '必填').max(1, '最多1项'),
  }),
  special_offers: z.object({
    list: z.array(z.any()).min(1, '必填').max(3, '最多3项'),
  }),
  watch_ad: z.object({
    list: z.array(z.object({
      reward_coins: z.number().int('必须为整数').min(0).max(9999),
    })).min(1, '必填'),
  }),
  white_user: z.object({
    list: z.array(z.any()).min(1, '必填'),
  }),
})

const { ErrorSpan, error, validateAll } = useValidator(formData, formRules)

const validate = () => {
  const exclude = [
    !hasExtra.value && ['check_in.extra', 'check_in.extra.0.reward_coins'],
    !userWhiteListEnabled.value && ['white_user', 'white_user.list'],
  ].filter(Boolean).flat() as string[]
  const valid = validateAll({ exclude })
  return valid
}

const resetFormData = () => {
  formData.value = cloneDeep(defaultFormData)
  rewardCoins.value = 0
  discount.value = 0
  hasExtra.value = false
  userWhiteListEnabled.value = false
  previousExtra.value = []
  canEditExtra.value = false
  canEditDiscount.value = false
}

export const useRewardsStore = () => {
  return {
    formData,
    error,
    rewardCoins,
    discount,
    validate,
    ErrorSpan,
    hasExtra,
    userWhiteListEnabled,
    resetFormData,
    defaultFormData,
    previousExtra,
    previousWhiteUserList,
    canEditExtra,
    canEditDiscount,
  }
}
