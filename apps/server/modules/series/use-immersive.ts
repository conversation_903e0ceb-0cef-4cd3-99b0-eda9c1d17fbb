import { ref, shallowRef, watch } from 'vue'
import { createCachedFn } from '@skynet/shared'
import { useSeriesItem } from './use-series-item'

export const useImmersive = createCachedFn((series_id: string) => {
  const immersive = ref(false)
  const immersiveTimer = shallowRef<null | number>(null)
  const {
    unlockOverlayVisible,
    productPanelVisible,
    manualUnlockPanelVisible,
    faqPanelVisible,
    isPlayingEpisode,
    episodeSelectMenuVisible,
  } = useSeriesItem(series_id)
  // 如果不是沉浸式，那么3秒后变成沉浸式
  // 排除有弹框的情况
  function resetImmersiveTimer() {
    cancelImmersiveTimer()
    immersiveTimer.value = window.setTimeout(() => {
      if (unlockOverlayVisible.value || productPanelVisible.value || manualUnlockPanelVisible.value || faqPanelVisible.value
        || episodeSelectMenuVisible.value
      ) {
        return
      }
      if (isPlayingEpisode.value === false) {
        return
      }
      immersive.value = true
    }, 3000)
  }
  function cancelImmersiveTimer() {
    if (immersiveTimer.value) {
      window.clearTimeout(immersiveTimer.value)
      immersiveTimer.value = null
    }
  }
  return {
    immersive,
    immersiveTimer,
    resetImmersiveTimer,
    cancelImmersiveTimer,
  }
})
