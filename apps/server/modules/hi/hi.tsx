import { NuxtLink } from '#components'
import { createComponent } from '@skynet/shared'
import { Button } from '@skynet/ui'
type HiOptions = {
  props: {}
}
export const Hi = createComponent<HiOptions>({
  props: {},
}, props => {
  return () => (
    <x-hi class="block">
      Hi
      <NuxtLink to="/custom">Custom</NuxtLink>
      <Button> hi</Button>
      222

    </x-hi>
  )
})

export default Hi
