import { Button, openDialog, SvgIcon } from '@skynet/ui'
import { ElImage, ElOption, ElSelect, ElPagination, ElMessage } from 'element-plus'
import { defineComponent, h, onBeforeMount, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useMyDrama } from './use-my-drama'
import { Empty } from '../common/empty'
import { useMe } from '../user/use-me'
import { Audit } from '../common/audit'
import { useI18n } from 'vue-i18n'
import { i18n } from 'src/locales'

export default defineComponent({
  name: 'MyDrama',
  setup() {
    const router = useRouter()
    const { dramaList, dramaListParams, getDramaList, dramaListLoading, checkIsEditable, dramaListTotal } = useMyDrama()
    const { me } = useMe()
    const { t } = useI18n()

    onBeforeMount(async () => {
      await getDramaList()
    })

    const onEditDrama = (id: string) => {
      void router.push(`/my-drama/edit/${id}`)
    }

    const statusMap = {
      0: {
        label: t('draft'),
        textColor: '#A1A0A3',
        bgColor: '#F3F0F3',
      },
      1: {
        label: t('pendingReview'),
        textColor: '#FF7809',
        bgColor: '#FFF2E6',
      },
      2: {
        label: t('reviewNotPassed'),
        textColor: '#FC2763',
        bgColor: '#FFF0F3',
      },
      3: {
        label: t('releasing'),
        textColor: '#0766FF',
        bgColor: '#0766FF1A',
      },
      4: {
        label: t('published'),
        textColor: '#1DD864',
        bgColor: '#1DD86426',
      },
    }

    const handleCreateDrama = () => {
      if (me.value.auth_status !== 2) {
        const close = openDialog({
          title: t('tip'),
          body: h(Audit, {
            onConfirm: () => {
              void router.push({ path: '/user' })
              close()
            },
          }),
          closeVisible: true,
          customClass: '!w-[400px]',
        })
        return
      }
      void router.push('/my-drama/add')
    }

    return () => (
      <div class="p-6">
        <h1 class="mb-6 text-2xl font-bold">{t('myShortPlay')}</h1>
        <div class="mb-6 flex items-center justify-between">
          <div class="flex basis-[492px] gap-4">
            <ElSelect
              v-model={dramaListParams.value.audit_status}
              placeholder={t('allStates')}
              class="!w-[200px]"
              onChange={() => getDramaList(true)}
            >
              {/* 审核状态 -1 全部 0 草稿 1待审核 2 审核不通过 3 发布中 4 已发布 */}
              <ElOption label={t('all')} value={-1} />
              <ElOption label={t('draft')} value={0} />
              <ElOption label={t('pendingReview')} value={1} />
              <ElOption label={t('reviewNotPassed')} value={2} />
              <ElOption label={t('releasing')} value={3} />
              <ElOption label={t('published')} value={4} />
            </ElSelect>

            <ElSelect
              v-model={dramaListParams.value.created_order}
              placeholder="时间排序"
              class="!w-[200px]"
              onChange={() => getDramaList(true)}
            >
              <ElOption label={t('timeNearToFar')} value={0} />
              <ElOption label={t('timeFarToNear')} value={1} />
            </ElSelect>
          </div>
          <Button onClick={handleCreateDrama} class="btn btn-sm w-[120px] py-3">{t('publishDrama')}</Button>
        </div>
        {!dramaList.value?.length && !dramaListLoading.value && <Empty desc={t('nothingCreatedYet')} button={{ text: t('toUpload'), onClick: handleCreateDrama }} />}
        <div v-loading={dramaListLoading.value} class="min-h-80">
          {!!dramaList.value?.length && dramaList.value.map(item => (
            <x-drama-card key={item.resource_key} class="mb-3 block overflow-hidden rounded-lg bg-[#FDFBFC] p-5">
              <x-drama-card-main class="flex gap-5 ">
                <div class="h-[164px] w-[123px] ">
                  <ElImage
                    src={item.vertical_cover}
                    class="size-full overflow-hidden rounded-lg object-cover"
                  >
                    {{ error: () => (
                      <SvgIcon name="video-card" class="size-full text-transparent " />
                    ),
                    placeholder: () => (
                      <SvgIcon name="video-card" class="size-full text-transparent " />
                    ) }}
                  </ElImage>
                </div>
                <div class="flex-1 space-y-4">
                  <h3 class="flex items-center gap-1 text-lg font-medium text-[#0B080B]">
                    {item.title}
                    <span class="rounded px-1 py-0.5 text-xs" style={{ backgroundColor: statusMap[item.audit_status as keyof typeof statusMap].bgColor, color: statusMap[item.audit_status as keyof typeof statusMap].textColor }}>
                      {statusMap[item.audit_status as keyof typeof statusMap].label}
                    </span>
                  </h3>

                  <div class="min-h-20 break-all text-sm text-[#A1A0A3]">
                    {item.description}
                  </div>
                  <p class="flex justify-between text-sm text-[#0B080B]">
                    <div>{t('estimatedEpisodes')}：{item.count}</div>
                    <div class="">
                      {!checkIsEditable(item.audit_status) && <Button class="btn btn-sm btn-info btn-ghost mr-3" onClick={() => onEditDrama(item.resource_key)}>{t('view')}</Button>}
                      {checkIsEditable(item.audit_status) && <Button class="btn btn-sm btn-ghost" onClick={() => onEditDrama(item.resource_key)}>{t('modify')}</Button>}
                    </div>
                  </p>

                </div>
              </x-drama-card-main>
              {(item.audit_failed_reason || item.audit_failed_reason_en) && (
                <x-failed-reason class="mt-3 block rounded-lg bg-[#FFF0F3] p-3 text-xs text-[#FC2763]">
                  <p class="mb-2 leading-4">
                    <SvgIcon name="ic-fail-fill" class="mr-1  size-4" />
                    {t('reviewFeedback')}：
                  </p>
                  <p class="pl-5">
                    {i18n.global.locale.value == 'en-US' ? (item.audit_failed_reason_en || item.audit_failed_reason) : item.audit_failed_reason}
                  </p>
                </x-failed-reason>
              )}
            </x-drama-card>
          ))}

        </div>
        {!!dramaList.value?.length && (
          <div class="flex items-center justify-end">
            <span class="mr-4 text-sm text-[#A1A0A3]">共{dramaListTotal.value}条</span>
            <ElPagination
              class="justify-end"
              background
              layout="prev, pager, next"
              total={dramaListTotal.value}
              onChange={page => {
                dramaListParams.value.page_index = page
                void getDramaList()
              }}
            />
          </div>
        )}
      </div>
    )
  },
})
