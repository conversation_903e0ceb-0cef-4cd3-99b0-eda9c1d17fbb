import { r, redirect } from '@skynet/shared'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'
import { track } from './lib/track.ts'
import MainLayout from './modules/layout/main-layout.tsx'
import { useMe } from './modules/user/use-me.tsx'
import { computed } from 'vue'
import { publicPaths } from './modules/common/constants.tsx'
import { useSession } from './modules/session/use-session.tsx'

declare module 'vue-router' {
  interface RouteMeta {
    title?: string
  }
}

const { me } = useMe()
const { token } = useSession()

/**
 * 公开路由，无需登录即可访问
 * 路由一经发布，不得修改，只能新增和重定向
 */
export const publicRoutes: RouteRecordRaw[] = [
  r('/sign_in', '登录', () => import('src/modules/session/sign-in-page.tsx')),
  r('/sign_up', '注册', () => import('src/modules/session/sign-up-page.tsx')),
  r('/', '管理面板', MainLayout, [
    r('', '工作台', () => import('./modules/workbench/workbench-page.tsx')),
    r('user', '账号设置', () => import('./modules/user/user-page.tsx')),
    r('play', '剧本', null, [
      r('', '剧本', () => import('./modules/play/play-page.tsx')),
      r('add', '上传新剧本', () => import('./modules/play/edit-play-page.tsx')),
      r(':id', '修改剧本', () => import('./modules/play/edit-play-page.tsx')),
    ]),
    r('message', '消息', () => import('./modules/message/message-page.tsx')),
    r('my-drama', '我的短剧', null, [
      r('', '我的短剧', () => import('./modules/drama/my-drama-page.tsx')),
      r('add', '新建', () => import('./modules/drama/edit-drama-page.tsx')),
      r('edit/:id', '编辑', () => import('./modules/drama/edit-drama-page.tsx')),
      r('upload/:id', '上传', () => import('./modules/drama/upload-drama-page.tsx')),
    ]),
    r('wallet', '收益分析', () => import('./modules/wallet/wallet-page.tsx')),
    r('workbench', '工作台', () => import('./modules/workbench/workbench-page.tsx')),
    r('data-center', '数据中心', () => import('./modules/data-center/data-center-page.tsx')),
  ]),
]

const router = createRouter({
  history: createWebHistory(),
  routes: publicRoutes,
})

// 路由守卫
// 未登录时，重定向到登录页
router.beforeEach((to, from, next) => {
  if (!token.value && !publicPaths.includes(to.path)) {
    const returnTo = ['/sign_in'].includes(to.path) ? '/' : encodeURIComponent(to.fullPath)
    next(`/sign_in?return_to=${returnTo}`)
  } else {
    next()
  }
})

router.afterEach((to, from) => {
  document.title = to.meta.title ? to.meta.title + ' - ' + '开放平台' : '开放平台'
  track('open-platform', 'page', 'show', {
    path: to.path,
    from: from.path,
  })
})

export default router

let goingToErrorPage = false
export const goToErrorPage = async (fromPath?: string, message?: string) => {
  if (goingToErrorPage) return
  goingToErrorPage = true
  fromPath = fromPath || router.currentRoute.value.fullPath
  await router.push(`/error-page?fromPath=${encodeURIComponent(fromPath)}&message=${message ?? ''}`).finally(() => {
    goingToErrorPage = false
  })
}
