import { createComponent } from '@skynet/shared'
import { SvgIcon } from '@skynet/ui'
import { useImagePreviewStore } from '@skynet/ui/image/use-preview-store'

type UploadImagePreviewOptions = {
  props: {
    image: string
    deleteAble?: boolean
  }
  emits: {
    delete: () => void
  }
}
export const UploadImagePreview = createComponent<UploadImagePreviewOptions>({
  props: {
    image: '',
    deleteAble: true,
  },
  emits: {
    delete: () => {},
  },
}, (props, { emit }) => {
  const {
    showImagePreviewDialog,
  } = useImagePreviewStore()
  return () => (
    <div class="hover group flex size-full space-x-0 justify-center items-center relative">
      <img src={props.image} class="size-full object-contain" />
      <div class="absolute space-x-4 left-0 right-0 bottom-0 top-0 z-up-up size-full flex items-center justify-center group-hover:opacity-100 opacity-0 bg-mask-1">
        <SvgIcon class="size-6 cursor-pointer" name="ic_preview" onClick={e => {
          e.preventDefault()
          e.stopPropagation()
          showImagePreviewDialog({
            imageList: [{
              src: props.image,
              width: 2000,
            }],
            canEscClose: true,
          })
        }}
        />
        {props.deleteAble && (
          <SvgIcon class="size-6 cursor-pointer" name="ic_trash" onClick={e => {
            emit('delete')
            e.preventDefault()
            e.stopPropagation()
          }}
          />
        )}
      </div>
    </div>
  )
})

export default UploadImagePreview
