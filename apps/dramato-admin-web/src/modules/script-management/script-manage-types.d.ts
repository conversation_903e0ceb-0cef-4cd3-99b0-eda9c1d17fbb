declare namespace Api {
  namespace ScriptManage {
    namespace Request {
      interface Params {
        page_index: number
        page_size: number
        title_or_key_list: string | string[] | number[]
        audit3_rating?: string
        principal?: string
        openplatform_principal: string
        audit_status: number // 审核状态 1 一审待审核 2 一审拒绝 3 一审放弃 4 一审通过 5 二审待审核 6 二审拒绝 7 二审放弃 8 二审通过 9 三审待审核 10 三审拒绝 11 三审放弃 12 三审通过
        created_start?: number | undefined | string
        created_end?: number | undefined | string
      }
    }
    namespace Response {
      type List = ApiResponse<{ list: M.IScriptManageListItem[], total: number, open_roles: number[] }>
    }
  }
}

declare namespace M {
  interface IScriptManageListItem {
    play_key: string
    title: string
    openplatform_principal: string
    audit_status: number // 审核状态 1 一审待审核 2 一审拒绝 3 一审放弃 4 一审通过 5 二审待审核 6 二审拒绝 7 二审放弃 8 二审通过 9 三审待审核 10 三审拒绝 11 三审放弃 12 三审通过
    audit3_rating: string
    principal: string
    created: number // 创建时间
  }
}
