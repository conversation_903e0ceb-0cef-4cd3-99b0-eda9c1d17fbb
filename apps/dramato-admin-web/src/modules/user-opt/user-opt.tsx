import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { DateTime, Pager, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { useUserOpt } from './use-user-opt'
import { useImagePreviewStore } from '@skynet/ui/image/use-preview-store'
type UserOptOptions = {
  props: {}
}
export const UserOpt = createComponent<UserOptOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
  } = useUserOpt()

  const {
    showImagePreviewDialog,
  } = useImagePreviewStore()

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>资产转移操作记录</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { }
              page.value = 0
              pageSize.value = 20
              void search(0)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              ['转出用户UID', 'out_user_id', { type: 'number' }, { transform: transformNumber }],
              ['转入用户UID', 'in_user_id', { type: 'number' }, { transform: transformNumber }],
            ]}
          />
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['日期/时间', row => (<DateTime value={(row?.created || 0) * 1000} />), { class: 'w-[150px]' }],
            ['操作类型', 'operate_type', { class: 'w-[100px]' }],
            ['转出用户ID', 'out_user_id', { class: 'w-[100px]' }],
            ['转入用户UID', 'in_user_id', { class: 'w-[100px]' }],
            ['Coins数额', 'out_user_cash_balance', { class: 'w-[100px]' }],
            ['Reward coins数额', 'out_user_bonus_balance', { class: 'w-[100px]' }],
            ['会员权益', 'out_user_vip_type', { class: 'w-[100px]' }],
            ['凭据', row => (
              <x-image-list>
                <x-image-content class="flex items-center gap-x-2">
                  <img class="w-[40px] object-cover" referrerpolicy="no-referrer" src={row.credential_image.replace('https://static-v1.mydramawave.com/', 'https://static-v1.mydramawave.com/banner/cover/')} />
                  <x-see
                    class="btn btn-link btn-xs"
                    onClick={() => showImagePreviewDialog({
                      imageList: [{
                        src: row.credential_image.replace('https://static-v1.mydramawave.com/', 'https://static-v1.mydramawave.com/banner/cover/'), width: 2000,
                      }],
                    })}
                  >查看
                  </x-see>
                </x-image-content>
              </x-image-list>
            ), { class: 'w-[200px]' }],
            ['操作人', 'create_user', { class: 'w-[100px]' }],
          ]}
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})
