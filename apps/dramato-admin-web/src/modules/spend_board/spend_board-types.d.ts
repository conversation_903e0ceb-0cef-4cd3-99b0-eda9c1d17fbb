declare namespace M {
  namespace SpendBoard {
    interface params {
      series_resource_id?: string // 资源id或者资源名（模糊查询）
      series_key?: string // 资源id或者资源名
      resource_round?: number // 发行轮次
      release_status?: number // 发行状态
      start_time?: number // 开始时间
      end_time?: number// 结束时间
      deploy_task_time_start?: number // 开始时间
      deploy_task_time_end?: number // 开始时间
      page_index?: number
      page_size?: number
    }
    interface DeployCalendarItem {
      id: number
      title: string
      deploy_time: number
      status: number
    }
    interface DeployCalendar {
      list: DeployCalendarItem[]
      total: number
    }

    interface Spend {
      state: number
      spend: number
      recall: number
      roi: number
      series_key: string
      title: string
    }

    interface ListItem {
      series_resource_id: number
      name: string
      release_round: number// 发行轮次
      resource_type: number// 本土 翻译
      auth_platform: string[]
      deploy_comment: string // 备注
      content_rating: string // 商务评级
      editor_rating: string // 剪辑拉片评级
      outer_infos: string[]
      en_spend: Spend // state必备，其他可选
      ja_spend: Spend //
      ko_spend: Spend //
      es_spend: Spend //
      fr_spend: Spend //
      pt_spend: Spend //
      id_spend: Spend //
      ru_spend: Spend //
      de_spend: Spend //
      vi_spend: Spend //
      th_spend: Spend //
      it_spend: Spend //
      tr_spend: Spend //
      tl_spend: Spend //
      ms_spend: Spend //
      ordered_money: number // 授权话费
      real_money: number // 已话费
      recycle_money: number // 回收
      roi: number
      deploy_task_time: number
      deploy_recommend: number // 任务状态1推荐 2不推荐 0默认
      langs?: string[]
    }

    interface ListResponse {
      list: ListItem[]
      total_count: number
    }
  }
}
