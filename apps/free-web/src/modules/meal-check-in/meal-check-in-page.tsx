import { onMounted, nextTick, ref } from 'vue'
import { createComponent } from '@skynet/shared'
import { useMealCheckInStore } from './meal-check-in-store'
import Loading from 'src/modules/common/loading/loading'
import background from './images/bg.webp'
import star from './images/star.webp'
import coin from './images/coin.webp'
import freeLogo from './images/free-logo.webp'
import qa_bg from './images/q.webp'
import coin_bg from './images/coin_bg.webp'
import { useLocale } from 'src/lib/use-locale'
import { jsBridge } from 'src/lib/jsbridge'
import { Back } from 'src/modules/common/back/back'
import { Button } from '@skynet/ui'
import toastIcon from './images/toast_bg.webp'
import { openDialog } from 'src/modules/common/dialog'
import { showToast } from 'src/modules/common/toast/toast'
import { DialogInfo } from './meal-check-in.d'
import { useStretchScroll } from 'src/lib/use-stretch-scroll'
import router from 'src/router'
import { ApiPostWelfareReceive, ApiPostWelfareADReceive, apiPostWelfareReclaimReceive } from './meal-check-in-api'

export const MealCheckInPage = createComponent(null, () => {
  const { t } = useLocale()
  const { eatDetails, getEatDetails } = useMealCheckInStore()
  const wrapper = ref<HTMLDivElement>()

  onMounted(() => {
    // document.title = t('coins.earnings.title')
    // track('myearnings', 'page', 'show')
    void getEatDetails()
  })
  const openActiveRulesDialog = () => {
    const closeDialog = openDialog({
      title: t('meal_check_in.rules_title'),
      closeVisible: false,
      mainClass: '',
      body: (
        <x-explain-dialog>
          <x-explain class="mt-1 block h-[200px] overflow-hidden break-words px-[26px] text-sm font-normal text-[var(--text-1)]">
            <x-wrapper id="explain-wrapper" class="block pb-7" style="-webkit-overflow-scrolling: touch;">
              <p class="mb-2 block">{t('meal_check_in.rules_1')}</p>
              <p class="mb-2 block">{t('meal_check_in.rules_2')}</p>
              <p class="block">{t('meal_check_in.rules_3')}</p>
            </x-wrapper>
          </x-explain>
          <x-footer class="relative flex flex-col bg-white">
            <x-mask class="absolute -top-[38px] h-10 w-full bg-gradient-to-b from-[rgba(255,_255,_255,_0)] to-[rgba(255,_255,_255,_1)]" />
            <x-got-it>
              <Button class="no-tap-color mx-4 mb-6 mt-4 h-11 w-[calc(100%-2rem)] rounded-lg border-none bg-[var(--brand-7)] text-base text-[var(--text-5)] outline-none"
                onClick={() => closeDialog()}
              >{t('coins.earnings.gotIt')}
              </Button>
            </x-got-it>
          </x-footer>
        </x-explain-dialog>
      ),
    })
    setTimeout(() => {
      useStretchScroll(document.getElementById('explain-wrapper') as HTMLElement)
    }, 200)
  }

  const openExchangeSuccessDialog = (dialogMessage: DialogInfo, extra: string, isLast?: true) => {
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${coin_bg})`, backgroundSize: '100% auto' }} class="pt-51.5 mb-4 flex  min-h-[266px] w-full flex-col bg-no-repeat shadow">
          <x-body-main class="flex w-full flex-col gap-y-4 rounded-lg bg-[var(--fill-5)] px-4">
            <x-title class="line-clamp-2 w-full break-all pt-5 text-center text-lg font-bold text-[#0b080b] ">{dialogMessage.title || dialogMessage.sub_title}</x-title>
            <x-explain class="flex flex-col items-center text-center">
              <x-price class="text-6 mx-[6px] block max-w-[calc(100%-12px)] truncate py-3 font-bold text-[var(--brand-6)]">{dialogMessage.reward_text}</x-price>
            </x-explain>
            <x-btn class="w-(100% - 4) no-tap-color mx-2 mb-6 rounded-lg bg-[var(--brand-6)] px-3 py-2.5 text-base font-bold text-[var(--text-5)]" onClick={() => closeDialog()}>
              <p class="line-clamp-2 w-full break-all text-center" onClick={() => {
                void openAdHandle(extra, isLast)
              }}
              >{dialogMessage.major_btn_txt}
              </p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }
  const openAdHandle = (ad_extra: string, isLast?: boolean) => {
    void jsBridge<any | null>('playAd', {}).then(consent => {
      void ApiPostWelfareADReceive({ ad_extra }).then(res => {
        if (res && res.data) {
          if (isLast) {
            showToast(
              <div>
                <img class="h-12 w-16" src={toastIcon} />
                <p class="text-5 font-bold">{res.data.toast.text}</p>
              </div>,
              'success', // type
              { class: 'rounded-4 p-5' }, // 可选的其他属性
            )
          } else if (res.data.dialog) {
            openExchangeSuccessDialog(res.data.dialog, res.data.ad_extra.extra, true)
          }
        }
      })
    })
  }
  const cardClickHandle = async () => {
    if (eatDetails.value) {
      const { status } = eatDetails.value
      const { welfare_id } = eatDetails.value
      if (status === 2 || status === 3) {
        return
      }
      if (status === 1) {
        // 可领取
        const res = await ApiPostWelfareReceive({ welfare_id })
        void getEatDetails()
        if (res && res.data) {
          openExchangeSuccessDialog(res.data.dialog, res.data.ad_extra.extra)
        }
      } else if (status === 4) { // 可补领
        void jsBridge<any | null>('playAd', {}).then(consent => {
          void apiPostWelfareReclaimReceive({ welfare_id }).then(res => {
            if (res && res.data) {
              showToast(
                <div>
                  <img class="h-12 w-16" src={toastIcon} />
                  <p class="text-5 font-bold">{res.data.toast.text}</p>
                </div>,
                'success', // type
                { class: 'rounded-4 p-5' }, // 可选的其他属性
              )
            }
            void getEatDetails()
          })
        })
      }
    }
  }

  return () => (
    !eatDetails.value
      ? (
          <Loading />
        )
      : (
          <x-meal-check-in-page class="relative flex size-full flex-col gap-y-2 overflow-y-auto bg-[#F2E1D9] text-[var(--text-5)]">
            <img src={background} class="absolute left-0 top-0 z-0 h-auto w-full" />
            <Back isWhite={false} isCloseWebview={true} class="z-100 sticky top-0 flex h-11 shrink-0 items-center bg-[#f0d4c7] px-3" />
            <div class="z-up flex h-[35%] flex-col">
              <x-meal-check-in-title class="z-up w-(100% - 14) text-8 mx-7 flex items-center justify-center font-bold text-[#f94f06]">
                <p class="line-clamp-2 w-full text-center">{eatDetails.value.top_show.title}</p>
              </x-meal-check-in-title>

              <x-tips class="w-(100% - 34) mx-17 z-up flex  items-center justify-start text-sm text-[#f94f06] ">
                <p class="line-clamp-2 w-full text-center">{eatDetails.value.top_show.sub_title}</p>
              </x-tips>
              <x-star class="z-up mr-7 mt-8 flex justify-end"><img class="size-14" src={star} /></x-star>
              <x-sleeping-link-wrap class="z-up flex justify-end">
                <x-sleeping-link onClick={() => {
                  void router.push({ path: '/sleep-check-in' })
                }} class="text-2.5 w-22 pt-0.6 rounded-50 mr-3 mt--3 line-clamp-3 w-full bg-[#6532f7] p-1 text-center font-bold"
                >
                  {t('meal_check_in.sleeping_link')}
                </x-sleeping-link>
              </x-sleeping-link-wrap>
              <x-rules-img class="mr-4.5 z-up mt-[12%] flex justify-end"><img class="size-9" src={qa_bg} /></x-rules-img>
              <x-rules-link-wrap class="z-up flex justify-end">
                <x-rules-link onClick={openActiveRulesDialog} class="text-2.5 font-['SF Pro'] mr-3 mt--2 line-clamp-4 w-12 w-full rounded-lg bg-[#fe5c00] px-1 py-0.5 text-center font-bold">
                  {t('meal_check_in.rules_title')}
                </x-rules-link>
              </x-rules-link-wrap>
            </div>
            <x-card className="z-up mb-2 mt-14 flex justify-between px-3">
              {eatDetails.value.eta_item_list.map((item, index) => {
                // 公共样式
                const cardItemClass = `w-20.25 h-30 py-[6px] justify-between flex flex-col shadow-[0px_2px_6px_0px_rgba(247,82,11,0.30)] rounded-xl border items-center ${
                  item.status === 3
                    ? 'opacity-50 bg-[#ff7f48]'
                    : item.is_curr_node
                    ? 'bg-[#ff7f48]'
                    : 'bg-[var(--fill-5)]'
                }`

                const cardTitleClass = `flex w-full px-1 h-7 items-center justify-center text-center text-xs ${
      item.is_curr_node ? 'font-bold text-[var(--text-5)]' : item.status === 3 ? 'text-[var(--text-5)]' : 'text-[var(--text-1)]'
    }`

                const cardBtnClass = `flex mt-2 text-center h-7.5 max-h-7.5 w-full line-clamp-2 rounded-lg px-1 py-1.5 text-2.5 items-center justify-center ${
      item.status === 3
        ? 'text-[var(--text-1)]'
        : item.is_curr_node
        ? 'bg-[#f7df00] text-[var(--text-1)] font-bold'
        : 'bg-[#fff2c7] text-[#ff4c00] font-bold'
    }`

                return (
                  <x-card-item key={index} class={cardItemClass}>
                    <x-card-title class={cardTitleClass}>
                      <p class="line-clamp-2 w-full break-all leading-3">{item.name}</p>
                    </x-card-title>
                    <x-card-bottom className="h-23 flex w-full flex-col items-center px-1">
                      <img src={coin} class="w-7.5 h-7.7 text-xs" />
                      <x-coin class={`text-xs font-bold ${item.status === 3 || item.is_curr_node ? 'text-[var(--text-5)] ' : 'text-[#ff3b30]'}`}>{item.gold}</x-coin>
                      <x-card-btn class={cardBtnClass}>
                        <p class="line-clamp-2 w-full break-all">
                          {item.bottom_show_text}
                        </p>
                      </x-card-btn>
                    </x-card-bottom>
                  </x-card-item>
                )
              })}
            </x-card>
            <x-main-tips onClick={cardClickHandle} class={`${eatDetails.value.status === 3 ? 'bg-[#ffab7b]' : 'bg-[#f6510b]'
  } p-2.6 w-(100% - 6) rounded-50 mx-3 flex shrink-0 items-center justify-center text-base font-bold text-[var(--text-5)]`}
            >
              <p class="line-clamp-2 w-full break-all text-center">{eatDetails.value.button_show.btn_txt}</p>
            </x-main-tips>
            <x-care-notice class="flex flex-col items-center justify-center px-3">
              <p class="line-clamp-3 w-full break-all text-center text-base text-[#863e00]">{eatDetails.value.bottom_show.title}</p>
              <p class="mt-2 line-clamp-3 w-full break-all text-center text-xs text-[#ae6b32]">{eatDetails.value.bottom_show.sub_title}</p>
            </x-care-notice>
            <x-footer-logo class="h-15 mb-6 flex w-full items-center justify-center "><img class="h-6 w-auto" src={freeLogo} /></x-footer-logo>
          </x-meal-check-in-page>
        )
  )
})

export default MealCheckInPage
