/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, exportAsCsv } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Pager, DateTime, Button, Icon, showFailToast, showSuccessToast, openDialog, transformNumber, transformTimestamp, SvgIcon, RadioGroup } from '@skynet/ui'
import { ref } from 'vue'
import { set, trim } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { statusDesc } from './util'
import { apiGetResourcePublishList, apiUploadResourcePublishExcel, apiCommitResourcePublishAudit } from './resource-publish-api'
import { statusOptions, cancelAbleStatus, releaseRounds } from './constant'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { langKey, langValue } from '../resource/constant'
import { useImagePreviewStore } from '@skynet/ui/image/use-preview-store'

type ResourceEntryPageOptions = {
  props: {}
}
export const ResourceEntryPage = createComponent<ResourceEntryPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const loading = ref(false)
  const list = ref<M.IResourcePublishListItem[]>([])
  const btnLoading = ref(false)
  const defaultParams: M.IResourcePublishQueryParams = {
    title: '',
    id: undefined,
    partner_name: '',
    create_start_time: undefined, // 录入开始时间
    create_end_time: undefined, // 录入结束时间
    check_status: -1,
    series_key_or_title_list: '',
    resource_id_or_title_list: '',
    audit_status_list: [],
    task_status: 0,
    shelf_status: 0
  }
  const form = ref<M.IResourcePublishQueryParams>({ ...defaultParams })
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const Table = CreateTableOld<M.IResourcePublishListItem>()
  const QueryForm = CreateForm<M.IResourcePublishQueryParams>()
  const { showImagePreviewDialog } = useImagePreviewStore()
  const getList = async () => {
    const params = {
      ...form.value,
      ...pageInfo.value,
    }
    loading.value = true
    list.value = []
    try {
      const res = await apiGetResourcePublishList(params)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const columns: TableColumnOld<M.IResourcePublishListItem>[] = [
    ['资源ID', 'id', { class: 'w-[100px]' }],
    ['资源名称', row => {
      return (
        <div class="relative">
          {row.release_round === 1 ? <SvgIcon class="absolute -left-4 -top-4 size-8" name="ic_release" /> : null}
          {row.recycle_status === 4 ? <span class="text-red-500">【已暂缓】</span> : row.recycle_status === 5 ? <span class="text-red-500">【已退剧】</span> : null}
          <span>{row.title}</span>
          {![4, 5].includes(row.recycle_status || 0) && row.preview_status === 2 ? <span class="text-red-500">【可预告】</span> : null}
        </div>
      )
    }, { class: 'w-[180px]' }],
    ['录入人', 'entry_user', { class: 'w-[140px]' }],
    ['合作方', 'partner_name', { class: 'w-[140px]' }],
    ['审核状态', row => {
      if (row.check_status === 2) {
        return (
          <div class="tooltip flex items-center gap-x-1" data-tip={row.check_reason}>
            <div class="tooltip line-clamp-1 flex-1 overflow-hidden text-red-500">
              <span class="text-[var(--text-3] text-xs">审核驳回：{row.check_reason}</span>
            </div>
            {row.reject_image_list && row.reject_image_list.length > 0 ? (
              <div class="size-[60px]">
                <img class="size-full cursor-pointer object-contain" src={row.reject_image_list?.[0]} alt="" onClick={() => {
                  showImagePreviewDialog({
                    imageList: row.reject_image_list?.map(imageUrl => ({
                      src: imageUrl,
                      width: 2000,
                    })) || [],
                    canEscClose: true,
                  })
                }} />
              </div>
            ) : null}
          </div>
        )
      } else {
        return statusDesc(row.check_status)
      }
    }, { class: 'text-center w-[240px]' }],
    ['上架状态', row => {
      if (row.shelf_status === 1) {
        return '已上架'
      }
      return '未上架'
    }, { class: 'text-center w-[100px]' }],
    ['创建时间', row => <DateTime value={(+row?.created || 0) * 1000} />, { class: 'text-center w-[130px]' },
    ],
    ['更新时间', row => <DateTime value={(+row?.updated || 0) * 1000} />, { class: 'text-center w-[130px]' },
    ],
    ['短剧名称2', 'title2', { class: 'w-[140px]' }],
    ['短剧名称3', 'title3', { class: 'w-[140px]' }],
    ['操作', (row, index) => (
      <div class="flex justify-center gap-x-2">
        <Button
          class="btn btn-link btn-xs"
          onClick={() => {
            void router.push(`/resource-publish/entry-detail/${row.id}`)
          }}
        >
          查看
        </Button>
        {
          ![4, 5].includes(row.recycle_status || 0) && cancelAbleStatus.includes(row.check_status)
            ? (
                <Button
                  class="btn btn-link btn-xs"
                  onClick={() => {
                    const btnLoading = ref(false)
                    const hideDialog = openDialog({
                      title: '提示',
                      mainClass: 'pb-0 px-5',
                      body: () => (
                        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <x-status-body>是否确定撤销审核?</x-status-body>
                          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
                            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                              try {
                                btnLoading.value = true
                                await apiCommitResourcePublishAudit({
                                  ids: [row.id],
                                  audit_operate: 2,
                                })
                                onQuery()
                                hideDialog()
                                showSuccessToast('操作成功')
                                btnLoading.value = false
                              } catch (error: any) {
                                btnLoading.value = false
                                showFailToast(error.response.data.message || '操作失败')
                              }
                            }}
                            >
                              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                              确定
                            </button>
                          </x-status-footer>
                        </x-status-confirm-dialog>
                      ),
                    })
                  }}
                >
                  撤回
                </Button>
              )
            : null
        }
      </div>
    ), {
      class: 'sticky right-0 bg-white',
    },
    ],
  ]

  const exportCsv = async (fn: () => void) => {
    const params = {
      ...form.value,
      page_index: 1,
      page_size: 20000,
    }
    btnLoading.value = true
    try {
      const res = await apiGetResourcePublishList(params)
      const list = res.data?.list || []
      const data = [
        ['创建时间',
          '资源id',
          '资源名称',
          '合作方',
          '短剧简介',
          '一级标签',
          '二级标签',
          '三级标签',
          '授权开始时间',
          '授权结束时间',
          '可预告时间',
          '最早可免费时间',
          '可上线时间',
          '可投放时间',
          '总集数',
          '付费第1集',
          '网盘链接',
          '发行轮次',
          '资源类型',
          '视频内嵌人声语言',
          '是否可免费',
          '商务评级',
          '视频质量评级',
          '授权价格',
          '审核状态',
          '审核意见',
          '演员信息',
        ],
      ].concat(
        list?.map((item: M.IResourcePublishListItem) => [
          dayjs(item.created * 1000).format('YYYY-MM-DD HH:mm:ss'),
          item.id,
          item.title,
          item.partner_name,
          trim(item.description),
          item.root_labels || '-',
          item.first_labels || '-',
          item.second_labels || '-',
          dayjs(item.auth_start_time * 1000).format('YYYY-MM-DD HH:mm:ss'),
          dayjs(item.auth_end_time * 1000).format('YYYY-MM-DD HH:mm:ss'),
          item.preview_time ? dayjs(item.preview_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
          item.free_start_time ? dayjs(item.free_start_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
          item.online_time ? dayjs(item.online_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
          item.deploy_time ? dayjs(item.deploy_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
          item.count,
          item.unlocked_episodes,
          item.netdisk_url,
          releaseRounds.find(o => o.value === +item.release_round)?.label,
          {
            1: '本土剧',
            2: '翻译剧',
          }[item.resource_type || -1] || '',
          langValue[langKey.findIndex(o => o === item.language_code)],
          {
            1: '可免费',
            2: '不可免费',
            3: '不可收费',
          }[item.free_status || -1] || '',
          item.external_content_rating ? item.external_content_rating : '-',
          item.content_rating ? item.content_rating : '-',
          item.auth_price ? item.auth_price : '-',
          ['草稿', '待审核', '审核驳回', '审核通过', '审核通过'][item.check_status],
          item.check_reason,
          // 导出excel 如何在cell里换行
          item?.actor_info_list?.map((o: any) => `${o.name}: ${o.link}`).join(';'),
        ].map(i => JSON.stringify(i))) ?? [],
      )
      exportAsCsv(data, '资源录入.csv')
    } finally {
      btnLoading.value = false
      fn && fn()
    }
  }

  const onExportExcel = () => {
    const hideDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-status-body>继续导出?</x-status-body>
          <x-status-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
              void exportCsv(hideDialog)
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-status-footer>
        </x-status-confirm-dialog>
      ),
    })
  }

  const showDealTable = (list: M.ImportErrorItem[], file: File, cb: () => void) => {
    const btnLoading = ref(false)
    const DupTable = CreateTableOld<M.ImportErrorItem>()
    const dupColumns: TableColumnOld<M.ImportErrorItem>[] = [
      ['资源名', 'id', { class: 'w-[80px]' }],
      ['存量资源信息', row => {
        return (
          <div class="flex space-x-1">
            <div>{row.id}</div>
            <div>{row.title}</div>
          </div>
        )
      }],
      ['操作', row => (
        <div class="w-full">
          <RadioGroup
            options={[
              {
                value: 1,
                label: '覆盖',
              },
              {
                value: 2,
                label: '打回修改',
              },
            ]}
            modelValue={row.deal_type}
            onUpdate:modelValue={(e: unknown) => row.deal_type = e as 2 | 1}
          />
        </div>
      ), { class: 'w-[100px]' }],
    ]
    const closeDialog = openDialog({
      title: '重复资源处理',
      mainClass: 'pb-0 px-5',
      body: () => (
        <div>
          <DupTable
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list}
            columns={dupColumns}
          />
          <x-batch-import-dialog class="mt-4 flex flex-col gap-y-[25px]">
            <x-batch-import-footer class="flex w-full justify-end gap-x-[10px]">
              <button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</button>
              <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                if (list.some(item => !item.deal_type)) {
                  showFailToast('请选择重复资源处理操作')
                  return
                }
                try {
                  btnLoading.value = true
                  await apiUploadResourcePublishExcel({
                    file,
                    req_data: JSON.stringify({
                      req_time: 2,
                      deal_list: list,
                    }),
                  })
                  cb()
                  showSuccessToast('操作成功！')
                  onQuery()
                  closeDialog()
                } catch (error: any) {
                  showFailToast(error.response.data.message || '操作失败！')
                } finally {
                  btnLoading.value = false
                }
              }}
              >
                {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                确定
              </button>
            </x-batch-import-footer>
          </x-batch-import-dialog>
        </div>
      ),
    })
  }

  const onUploadBusinessExcel = () => {
    const uploading = ref(false)
    const inputRef = ref<HTMLInputElement>()
    const fileChange = async (e: Event) => {
      const files = (e.target as HTMLInputElement)?.files || []
      const fileList = Array.from(files)
      if (fileList && fileList.length > 0) {
        const file = fileList[0]
        const ext = file.name.split('.').pop()?.toLowerCase() || ''
        if (ext !== 'csv' && ext !== 'xlsx') {
          showFailToast('仅支持上传csv、xlsx文件');
          (inputRef.value as HTMLInputElement).value = ''
          return
        }
        try {
          uploading.value = true
          const res = await apiUploadResourcePublishExcel({ file, req_data: JSON.stringify({ req_time: 1 }) })
          if (res.data?.repeat_list && res.data?.repeat_list.length > 0) {
            showDealTable(res.data?.repeat_list, file, createDialog)
          } else {
            showSuccessToast('操作成功！')
            onQuery()
            createDialog()
          }
        } catch (error: any) {
          showFailToast(error.response.data.message || '操作失败！');
          (inputRef.value as HTMLInputElement).value = ''
        } finally {
          uploading.value = false
        }
      }
    }
    const createDialog = openDialog({
      title: '批量新增',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[400px]',
      body: () => (
        <div>
          <div>
            上传商务信息：
            <a class="link" target="_blank" href="https://rg975ojk5z.feishu.cn/wiki/PP6HwEnqMi2y2NkjTAtcA51BnEb?sheet=b82d7d">跳转模板</a>
          </div>
          <div class="mt-4">
            <input ref={inputRef} disabled={uploading.value} class="file-input file-input-primary file-input-xs w-full max-w-xs" type="file" multiple={false} accept="csv,xlsx" onChange={fileChange} />
            <div class="mt-2 text-xs">{requiredLabel('仅支持上传.csv，.xlsx文件')}</div>
          </div>
        </div>
      ),
    })
  }

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = { ...defaultParams }
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }

  onQuery()

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>资源录入</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              ['资源名称/ID：', 'resource_id_or_title_list', { type: 'text', placeholder: '请输入资源名称或者id' }],
              ['剧集名称/ID：', 'series_key_or_title_list', { type: 'text', placeholder: '请输入资源名称' }],
              ['发行轮次：', 'release_round', { type: 'select', options: [
                {
                  value: 0,
                  label: '全部',
                },
                {
                  value: 1,
                  label: '首发',
                },
                {
                  value: 2,
                  label: '二轮',
                },
              ], placeholder: '请选择发行轮次', autoInsertEmptyOption: false }, {
                transform: transformNumber,
              }],
              ['存在演员信息：', 'has_actor_info', { type: 'select', options: [
                {
                  value: 0,
                  label: '全部',
                },
                {
                  value: 1,
                  label: '有',
                },
                {
                  value: 2,
                  label: '无',
                },
              ], placeholder: '请选择是否有演员信息', autoInsertEmptyOption: false }, {
                transform: transformNumber,
              }],

              ['录入人：', 'entry_user', { type: 'text', placeholder: '请输入录入人' }],
              ['合作方：', 'partner_name', { type: 'text', placeholder: '请输入合作方' }],
              [
                ['录入时间-开始：', 'create_start_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
                ['录入时间-结束：', 'create_end_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
              ],
              ['审核状态：', 'check_status', { type: 'select', options: statusOptions, autoInsertEmptyOption: false }, { transform: transformNumber }],
              ['上架状态：', 'shelf_status', { type: 'select', options: [
                {
                  value: 0,
                  label: '全部',
                },
                {
                  value: 1,
                  label: '已上架',
                },
                {
                  value: 2,
                  label: '未上架',
                },
              ], autoInsertEmptyOption: false }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex w-full items-center justify-end space-x-2">
            {/* <Button class="btn btn-primary btn-sm" onClick={onExportExcel}>
              导出
            </Button> */}
            <Button class="btn btn-primary btn-sm" onClick={onUploadBusinessExcel}>批量添加</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              void router.push(`/resource-publish/entry-detail`)
            }}
            >创建剧目
            </Button>
          </x-table-actions>
        ),
        table: () => (
          <Table
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ResourceEntryPage
