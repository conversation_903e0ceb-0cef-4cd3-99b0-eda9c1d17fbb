export interface RechargeListInfo {
  items: OrderItem[];
  page_info: PageInfo;
}

interface OrderItem {
  order_id: string;
  title: string;
  amount: number;
  product_type: 'recharge' | 'membership';
  pay_finish_time: number;
  order_vip_expired: boolean;
  business_data: BusinessData;
  txn_amount: number;
  txn_currency: string;
}

interface BusinessData {
  bonus: number;
  period: 'weekly' | 'monthly' | 'yearly' | '';
  quanity: number;
  vip_expire_time: number;
  product_title: string;
}

interface PageInfo {
  next: string;
  has_more: boolean;
}
export interface ConsumptionListInfo {
  items: TransactionItem[];
  page_info: PageInfo;
}

interface TransactionItem {
  txn_id: string;
  title: string;
  amount: number;
  flow_type: number;
  pay_finish_time: number;  // 时间戳（单位：秒）
  bonus_expire_time: number; // 时间戳（单位：秒）
  business_data: BusinessDataInfo;
  txn_type: 'ad_unlock' | 'coupon_unlock';
}

interface BusinessDataInfo {
  id: string;
  name: string;
  cover: string;
  episode_index: number;
  expire_time: number;       // 时间戳（单位：秒）
}

export interface RewardsListInfo {
  items: RewardTransactionItem[];
  page_info: PageInfo;
}

interface RewardTransactionItem {
  txn_id: string;
  title: string;
  amount: number;
  flow_type: number;
  pay_finish_time: number;  // 时间戳（单位：秒）
  bonus_expire_time: number; // 时间戳（单位：秒）
  business_data: string;     // 需要JSON解析的字符串数据
}