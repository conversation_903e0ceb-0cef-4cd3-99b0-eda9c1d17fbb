import { defineComponent } from 'vue'
import { useRouter } from 'vue-router'
import { drama, play } from './images/workbench-images'
import { useMe } from '../user/use-me'
import { computed } from 'vue'

import { createComponent } from '@skynet/shared'
import { openDialog } from '@skynet/ui'
import Audit from '../common/audit'
import { useI18n } from 'vue-i18n'
type WorkbenchPageOptions = {
  props: {}
}
export const WorkbenchPage = createComponent<WorkbenchPageOptions>({
  props: {},
}, props => {
  const { t } = useI18n()
  const router = useRouter()
  const { me } = useMe()

  const greeting = computed(() => {
    const hour = new Date().getHours()
    if (hour < 12) return t('goodMorning')
    if (hour < 18) return t('goodAfternoon')
    return t('goodEvening')
  })
  const onClick = (path: string) => {
    if (me.value.auth_status !== 2) {
      const close = openDialog({
        title: t('tip'),
        body: () => (
          <Audit onConfirm={() => {
            close()
            void router.push('/user')
          }} />
        ),
      })
      return
    }
    void router.push(path)
  }
  return () => (
    <x-workbench class=" flex flex-col !bg-transparent !p-0">
      <div class="mb-3 flex  flex-col gap-2 rounded-xl bg-white p-6">
        <h1 class="text-[24px] font-medium">👋 {greeting.value}, {me.value.nickname || me.value.phone}</h1>
        <span class="text-sm text-gray-400">{t('welcomeMessage')}</span>
      </div>

      <x-actions class="grid flex-1 grid-cols-2 gap-6 rounded-xl bg-white p-6">
        <x-action
          class="flex cursor-pointer flex-col items-center justify-center rounded-lg bg-[#FDFBFC] p-6 transition-shadow hover:shadow-lg"
          onClick={() => onClick('/play/add')}
        >
          <div class="flex flex-col items-center justify-between">
            <img src={play} alt={t('scriptSubmission')} class="w-[250px]" />
            <h3 class="my-3 mb-2 text-center text-lg font-medium">{t('scriptSubmission')}</h3>
          </div>
          <button class="rounded-lg bg-gray-100 px-4 py-2 hover:bg-gray-200">
            {t('uploadScript')}
          </button>
        </x-action>

        <x-action
          class="flex cursor-pointer flex-col items-center justify-center  rounded-lg bg-[#FDFBFC] p-6 transition-shadow hover:shadow-lg"
          onClick={() => onClick('/my-drama/add')}
        >
          <div class="flex flex-col items-center justify-between">
            <img src={drama} alt={t('publishDrama')} class="w-[250px]" />
            <h3 class="my-3 mb-2 text-center text-lg font-medium">{t('publishDrama')}</h3>
          </div>
          <button class="rounded-lg bg-gray-100 px-4 py-2 hover:bg-gray-200">
            {t('releaseNow')}
          </button>
        </x-action>
      </x-actions>
    </x-workbench>
  )
})

export default WorkbenchPage
