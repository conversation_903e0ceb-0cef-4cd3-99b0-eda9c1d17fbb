import { SvgIcon, Tooltip } from '@skynet/ui'
import { auditStatusShow } from './constant'
import { trim } from 'lodash-es'

export const statusDesc = (status: 0 | -1 | 1 | 2 | 3 | 4) => {
  const color = ['', 'badge-primary', 'text-red-500', 'text-green-500', 'text-green-500'][status]
  const text = ['草稿', '待审核', '审核驳回', '审核通过', '审核通过'][status]
  return <div class={`badge  badge-outline ${color}`}>{text}</div>
}

export const renderTitle = (row: M.IResourcePublishListItem | M.IResourceCheckItem | M.IResourcePublishItem | M.InfoModify.Resource | M.MultiSubtitle.Resource) => {
  return (
    <div class="relative">
      {row.release_round === 1 ? <SvgIcon class="size-8 absolute -top-4 -left-4" name="ic_release" /> : null}
      <span>{row.title}</span>
      {row.preview_status === 2 ? <span class="text-red-500">【可预告】</span> : null}
    </div>
  )
}

export const renderReleaseTitle = (row: M.Episode) => {
  return (
    <div class="relative">
      {row.release_round === 1 ? <SvgIcon class="size-8 absolute -top-4 -left-4" name="ic_release" /> : null}
      <span>{row.series_resource_title}</span>
    </div>
  )
}

export const renderStatusDesc = (row: M.IResourcePublishListItem | M.IResourceCheckItem | M.IResourcePublishItem | M.InfoModify.Resource | M.MultiSubtitle.Resource, showDetailStatus = false) => {
  const label = auditStatusShow.find(item => item.value === row.audit_status)?.label || '无'
  const color = {
    0: '',
    1: 'text-red-500',
    2: 'text-green-500',
    99: 'badge-primary',
  }[row.audit_status || 0] || ''

  if (row.audit_status === 99) {
    return (
      <Tooltip popContent={() => row.audit_reason}>
        <div class={`badge  badge-outline ${color}`}>{label}</div>
        <div class="overflow-hidden text-primary tooltip line-clamp-1">
          <span class="text-[var(--text-3] text-xs">待确认：{row.audit_reason}</span>
        </div>
      </Tooltip>
    )
  } else if (row.audit_status === 1) {
    return (
      <Tooltip popContent={() => row.audit_reason}>
        <div class="overflow-hidden text-red-500 tooltip line-clamp-1">
          <span class="text-[var(--text-3] text-xs">审核驳回：{row.audit_reason}</span>
        </div>
      </Tooltip>
    )
  } else if (row.audit_status === 0 && showDetailStatus) {
    const auditToStatus = row.audit_to_status
    return (
      <div class="flex justify-center">
        <div class={`badge  badge-outline h-auto ${color}`}>{label}{ auditToStatus === 1 ? '-驳回' : auditToStatus === 2 ? '-已修改' : null } </div>
      </div>
    )
  } else {
    return <div class={`badge  badge-outline ${color}`}>{label}</div>
  }
}

export const trimSplitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`)).map(str => str.replace(new RegExp(`^[${[' '].join('')}]+|[${[' '].join('')}]+$`, 'g'), ''))

/**
 * @description 检查资源名称或id
 * @param value
 * @returns
 */
export const checkResourceTitleOrId = (value: string) => {
  if (!value) return []
  const trimSplit = trimSplitIt([',', ';', '，', ' ', '；', '\t', '\n'])
  // 如果数组中list每个元素都为数字，且大于 资源id大于等于9900，如果有一个符合，则 返回符合规则的，如果都不符合规则，则返回
  const list = trimSplit(value)
  const isResourceIds = list.filter(item => {
    return /^\d+$/.test(item) && +item >= 9900
  })
  if (isResourceIds.length > 0) {
    return isResourceIds
  } else {
    return [trim(value)]
  }
}

export const checkEpisodeTitleOrId = (value: string) => {
  if (!value) return []
  // 如果数组中list每个元素都为数字，且大于 剧集id符合是长度为10的字符串 没有特殊符号
  const trimSplit = trimSplitIt([',', ';', '，', ' ', '；', '\t', '\n'])
  const list = trimSplit(value)
  const isEpisodeIds = list.filter(item => {
    return /^[a-zA-Z0-9]{10}$/.test(item)
  })
  if (isEpisodeIds.length > 0) {
    return isEpisodeIds
  } else {
    return [trim(value)]
  }
}

function pad(num: number) {
  return num.toString().padStart(2, '0');
}

export function formatSecondsToString(seconds: number) {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;

  return `${pad(h)}:${pad(m)}:${pad(s)}`;
}
