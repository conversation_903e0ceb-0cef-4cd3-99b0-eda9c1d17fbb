import { createComponent, exportAsCsv, mc } from '@skynet/shared'
import { Button, Icon, Pager, showAlert, transformNumber, transformTimestamp, openDialog } from '@skynet/ui'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref, watch } from 'vue'
import { useSpendBoard } from './use-spend_board'
import { useClipboard } from '@vueuse/core'
import { apiEditSpendBoardTaskStatus, apiJuDanSpendBoard } from './spend_board-api'
import dayjs from 'dayjs'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { ElTable, ElTableColumn, ElPagination } from 'element-plus'
import { useRouter } from 'vue-router'
type SpendBoardOptions = {
  props: {}
}
export const SpendBoard = createComponent<SpendBoardOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    total,
    search,
    onEdit,
  } = useSpendBoard()
  const router = useRouter()
  const MultiSelect = CreateFormMultiSelect<string>()

  const { copy, copied } = useClipboard()

  watch(() => copied.value, () => {
    if (copied.value) {
      showAlert('复制成功')
    }
  })

  const onCopy = (s: M.SpendBoard.Spend) => {
    void copy(`
      剧集ID：${s.series_key}
      剧集名称：${s.title}
    `)
  }

  onMounted(() => {
    void search(1)
  })

  const currentCommit = ref('')
  const currentContentRating = ref('')

  const onClickExport = () => {
    const data = [
      ['资源ID', '资源名称', '资源轮次', '资源类型', '授权平台',
        '备注', '商务内容评价', '剪辑拉片评价', '站外信息监控',
        ...getCsvLanguageItemTitle('英语-en'),
        ...getCsvLanguageItemTitle('日语-ja'),
        ...getCsvLanguageItemTitle('韩语-ko'),
        ...getCsvLanguageItemTitle('西班牙语-es'),
        ...getCsvLanguageItemTitle('法语-fr'),
        ...getCsvLanguageItemTitle('葡萄牙语-pt'),
        ...getCsvLanguageItemTitle('印尼语-id'),
        ...getCsvLanguageItemTitle('俄语-ru'),
        ...getCsvLanguageItemTitle('德语-de'),
        ...getCsvLanguageItemTitle('越南语-vi'),
        ...getCsvLanguageItemTitle('泰语-th'),
        ...getCsvLanguageItemTitle('意大利语-it'),
        ...getCsvLanguageItemTitle('土耳其语-tr'),
        ...getCsvLanguageItemTitle('菲律宾语-tl'),
        ...getCsvLanguageItemTitle('马来西亚语-ms'),
        '授权价格',
        '投放总花费',
        '投放总回收',
        '总ROI（DO）'],
    ].concat(
      list.value?.map(item => [
        item.series_resource_id,
        item.name,
        item.release_round,
        ['', '本土', '翻译'][item.resource_type],
        item.auth_platform.join(','),
        item.deploy_comment,
        item.content_rating,
        item.editor_rating,
        item.outer_infos.join(','),
        ...getCsvLanguageItem(item.en_spend),
        ...getCsvLanguageItem(item.ja_spend),
        ...getCsvLanguageItem(item.ko_spend),
        ...getCsvLanguageItem(item.es_spend),
        ...getCsvLanguageItem(item.fr_spend),
        ...getCsvLanguageItem(item.pt_spend),
        ...getCsvLanguageItem(item.id_spend),
        ...getCsvLanguageItem(item.ru_spend),
        ...getCsvLanguageItem(item.de_spend),
        ...getCsvLanguageItem(item.vi_spend),
        ...getCsvLanguageItem(item.th_spend),
        ...getCsvLanguageItem(item.it_spend),
        ...getCsvLanguageItem(item.tr_spend),
        ...getCsvLanguageItem(item.tl_spend),
        ...getCsvLanguageItem(item.ms_spend),
        item.ordered_money.toFixed(2),
        item.real_money.toFixed(2),
        item.recycle_money.toFixed(2),
        (item.roi * 100).toFixed(2) + '%',
      ].map(i => JSON.stringify(i))) ?? [],
    )
    exportAsCsv(data, '短剧投放监控.csv')
  }

  const getCsvLanguageItemTitle = (l: string) => {
    return [
      `${l}-剧集ID`,
      `${l}-剧集名称`,
      `${l}-投放状态`,
      `${l}-投放进度`,
    ]
  }

  const getCsvLanguageItem = (spend: M.SpendBoard.Spend) => {
    return [
           `${spend.series_key}`,
           `${spend.title}`,
           `${['', '投放中', '待投放', '待上架'][spend.state || 0]}`,
           `${spend.state === 1 ? (spend.roi * 100).toFixed(2) + '%，(' + spend.spend + '/' + spend.recall + ')' : '-暂未进度-'}`,
    ]
  }

  const renderLanguage = (spend: M.SpendBoard.Spend) => {
    return (
      <x-outer-info class="flex gap-y-2 flex-col items-center justify-center">
        <x-copy class="flex items-center gap-x-2">
          {spend.series_key}
          <Icon onClick={() => onCopy(spend)} name="ant-design:copy-filled" class="cursor-pointer" />
        </x-copy>
        <x-copy title={spend.title} class="truncate w-full">
          {spend.title}
        </x-copy>
        {spend.state === 1 && <x-copy class="text-green-300">投放中（{(spend.roi * 100).toFixed(2)}%）</x-copy>}
        {spend.state === 1 && <x-copy>{spend.recall}/{spend.spend}</x-copy>}
        {spend.state === 2 && <x-copy class="text-gray-400">待投放</x-copy>}
        {spend.state === 3 && <x-copy class="text-red-500">待上架</x-copy>}
      </x-outer-info>
    )
  }

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>短剧投放监控</li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { page_index: 1, page_size: 20 }
              void search(0)
            }}
            onSubmit={() => search(1)}
            data={params.value}
            items={[
              [
                '资源名/资源ID',
                'series_resource_id',
                {
                  type: 'text',
                },
              ],
              [
                '剧名/剧ID',
                'series_key',
                {
                  type: 'text',
                },
              ],
              [
                '发行轮次',
                'release_round',
                {
                  type: 'select',
                  options: [
                    { value: 1, label: '首发' },
                    { value: 2, label: '二轮' },
                  ],
                },
                {
                  transform: transformNumber,
                },
              ],
              [
                '资源类型',
                'resource_type',
                {
                  type: 'select',
                  options: [
                    { value: 1, label: '本土' },
                    { value: 2, label: '翻译' },
                  ],
                },
                {
                  transform: transformNumber,
                },
              ],
              // ['筛选日期', 'start_time', { type: 'datetime', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD HH:mm' }, {
              //   transform: transformTimestamp,
              // }],
              // [<div class="w-1 h-6" />, '', { type: 'custom', render: () => <div class="text-gray-400 h-[34px] leading-[34px]">至</div> }],
              // [<div class="w-1 h-6" />, 'end_time', { type: 'datetime', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD HH:mm' }, {
              //   transform: transformTimestamp,
              // }],
              [
                '推荐状态',
                'deploy_recommend',
                {
                  type: 'select',
                  options: [
                    { value: 1, label: '推荐' },
                    { value: 2, label: '不推荐' },
                  ],
                },
                {
                  transform: transformNumber,
                },
              ],
              ['剧单时间', 'deploy_task_time_start', { type: 'datetime', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD HH:mm' }, {
                transform: transformTimestamp,
              }],
              [<div class="w-1 h-6" />, '', { type: 'custom', render: () => <div class="text-gray-400 h-[34px] leading-[34px]">至</div> }],
              [<div class="w-1 h-6" />, 'deploy_task_time_end', { type: 'datetime', placeholder: '选择时间', displayFormat: 'YYYY-MM-DD HH:mm' }, {
                transform: transformTimestamp,
              }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex justify-between items-center">
            短剧投放监控

            {/* <Button class="btn btn-primary btn-sm" onClick={onClickExport}>导出数据</Button> */}
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['资源ID', 'series_resource_id', { class: 'w-[100px]' }],
            ['资源名称', 'name', { class: 'w-[250px]' }],
            ['发行轮次', row => ['', '首发', '二轮'][row.release_round], { class: 'w-[100px]' }],
            ['资源类型', row => ['', '本土', '翻译'][row.resource_type], { class: 'w-[100px]' }],
            ['授权平台', row => (
              <x-outer-info class="flex gap-y-2 flex-col">
                {
                  row.auth_platform.map(i => <x-outer-info-item>{i}</x-outer-info-item>)
                }
              </x-outer-info>
            ), { class: 'w-[200px]' }],
            ['备注', row => (
              <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                <input
                  type="text"
                  class={mc('grow w-100px')}
                  value={row.deploy_comment}
                  onFocus={() => {
                    currentCommit.value = row.deploy_comment || ''
                  }}
                  onInput={(e: Event) => {
                    currentCommit.value = (e.target as HTMLInputElement).value || ''
                  }}
                  onKeydown={(e: KeyboardEvent) => {
                    if (e.key !== 'Enter') {
                      return
                    }
                    if (currentCommit.value === row.deploy_comment) {
                      return
                    }
                    void onEdit({
                      series_resource_id: row.series_resource_id + '' || '',
                      column_type: 1,
                      content: currentCommit.value,
                    })
                  }}
                  onBlur={() => {
                    if (currentCommit.value === row.deploy_comment) {
                      return
                    }
                    void onEdit({
                      series_resource_id: row.series_resource_id + '' || '',
                      column_type: 1,
                      content: currentCommit.value,
                    })
                  }}
                />
              </label>
            ), { class: 'w-[100px]' }],
            ['商务内容评价', 'content_rating', { class: 'w-[100px]' }],
            ['剪辑拉片评价', row => (
              <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                <input
                  type="text"
                  class={mc('grow w-100px')}
                  value={row.editor_rating}
                  onFocus={() => {
                    currentContentRating.value = row.editor_rating || ''
                  }}
                  onInput={(e: Event) => {
                    currentContentRating.value = (e.target as HTMLInputElement).value || ''
                  }}
                  onKeydown={(e: KeyboardEvent) => {
                    if (e.key !== 'Enter') {
                      return
                    }
                    if (currentContentRating.value === row.editor_rating) {
                      return
                    }
                    void onEdit({
                      series_resource_id: row.series_resource_id + '' || '',
                      column_type: 3,
                      content: currentContentRating.value,
                    })
                  }}
                  onBlur={() => {
                    if (currentContentRating.value === row.editor_rating) {
                      return
                    }
                    void onEdit({
                      series_resource_id: row.series_resource_id + '' || '',
                      column_type: 3,
                      content: currentContentRating.value,
                    })
                  }}
                />
              </label>
            ), { class: 'w-[100px]' }],
            ['剧单日期', row => (
              <label class={mc('input input-bordered flex items-center gap-1 h-8')}>
                <input
                  type="date"
                  class={mc('grow w-[160px]')}
                  value={row.deploy_task_time === undefined || !row.deploy_task_time ? '' : dayjs(row.deploy_task_time * 1000).format('YYYY-MM-DD')}
                  onInput={(e: Event) => {
                    const t = dayjs((e.target as HTMLInputElement).value || '').unix()
                    void apiJuDanSpendBoard({
                      series_resource_id: row.series_resource_id + '' || '',
                      deploy_task_time: t,
                    })
                  }}
                />
              </label>
            ),
            { class: mc('w-[240px]') },
            ],
            ['是否为推荐', row => (
              <x-recommend class="flex gap-x-2">
                <label class={mc('h-8')}>
                  <select
                    class="select select-bordered select-sm "
                    value={row.deploy_recommend}
                    onInput={(e: Event) => {
                      row.deploy_recommend = Number((e.target as HTMLInputElement).value) || 0
                      void apiEditSpendBoardTaskStatus({
                        series_resource_id: row.series_resource_id + '' || '',
                        recommend: Number((e.target as HTMLInputElement).value) || 0,
                        langs: row.langs || [],
                      })
                    }}
                  >
                    <option value={0}>-空-</option>
                    <option value={1}>推荐</option>
                    <option value={2}>不推荐</option>
                  </select>
                </label>
                {
                  Number(row.deploy_recommend) === 1
                  && (
                    <MultiSelect
                      class="w-[300px]"
                      search={true}
                      popoverWrapperClass="z-popover-in-dialog"
                      options={Object.keys(row).filter(key => key.endsWith('_spend') && (row[key as keyof typeof row] as M.SpendBoard.Spend).series_key !== '').map(key => {
                        return {
                          label: key.replace('_spend', ''),
                          value: key.replace('_spend', ''),
                        }
                      })}
                      modelValue={row.langs}
                      onUpdate:modelValue={e => {
                        row.langs = e
                        void apiEditSpendBoardTaskStatus({
                          series_resource_id: row.series_resource_id + '' || '',
                          recommend: Number(row.deploy_recommend) || 0,
                          langs: row.langs,
                        })
                      }}
                    />
                  )
                }
              </x-recommend>
            ),
            { class: mc('w-[300px]') },
            ],
            ['站外信息监控', row => (
              <x-outer-info class="flex gap-y-2 flex-col">
                {
                  row.outer_infos.map(i => <x-outer-info-item>{i}</x-outer-info-item>)
                }
              </x-outer-info>
            ), { class: 'w-[200px]' }],
            ['英语-en', row => renderLanguage(row.en_spend), { class: 'w-[250px] text-center' }],
            ['日语-ja', row => renderLanguage(row.ja_spend), { class: 'w-[250px] text-center' }],
            ['韩语-ko', row => renderLanguage(row.ko_spend), { class: 'w-[250px] text-center' }],
            ['西班牙语-es', row => renderLanguage(row.es_spend), { class: 'w-[250px] text-center' }],
            ['法语-fr', row => renderLanguage(row.fr_spend), { class: 'w-[250px] text-center' }],
            ['葡萄牙语-pt', row => renderLanguage(row.pt_spend), { class: 'w-[250px] text-center' }],
            ['印尼语-id', row => renderLanguage(row.id_spend), { class: 'w-[250px] text-center' }],
            ['俄语-ru', row => renderLanguage(row.ru_spend), { class: 'w-[250px] text-center' }],
            ['德语-de', row => renderLanguage(row.de_spend), { class: 'w-[250px] text-center' }],
            ['越南语-vi', row => renderLanguage(row.vi_spend), { class: 'w-[250px] text-center' }],
            ['泰语-th', row => renderLanguage(row.th_spend), { class: 'w-[250px] text-center' }],
            ['意大利语-it', row => renderLanguage(row.it_spend), { class: 'w-[250px] text-center' }],
            ['土耳其语-tr', row => renderLanguage(row.tr_spend), { class: 'w-[250px] text-center' }],
            ['菲律宾语-tl', row => renderLanguage(row.tl_spend), { class: 'w-[250px] text-center' }],
            ['马来西亚语-ms', row => renderLanguage(row.ms_spend), { class: 'w-[250px] text-center' }],
            ['授权价格', row => (row.ordered_money).toFixed(2), { class: 'w-[100px]' }],
            ['投放总花费', row => (row.real_money).toFixed(2), { class: 'w-[100px]' }],
            ['投放总回收', row => (row.recycle_money).toFixed(2), { class: 'w-[100px]' }],
            ['总ROI（DO）', row => (row.roi * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
          ]} class="tm-table-fix-last-column"
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={params.value.page_index}
                  v-model:size={params.value.page_size}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(params.value.page_index)
                  }}
                  onUpdate:size={() => {
                    void search(params.value.page_index)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default SpendBoard
