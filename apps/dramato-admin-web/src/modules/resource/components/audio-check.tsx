/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { SvgIcon } from '@skynet/ui'
import { onMounted, ref } from 'vue'

type AudioCheckOptions = {
  props: {
    videoPath?: string
    audioPath: string
    subtitle?: string
  }
}
export const AudioCheck = createComponent<AudioCheckOptions>({
  props: {
    videoPath: '',
    audioPath: '',
    subtitle: ''
  },
}, props => {
  const video = ref()
  const audio = ref()
  const playBtn = ref()
  const pauseBtn = ref()
  const progress = ref()
  const btnDisabled = ref(false)
  const videoCanplay = ref(false)
  const audioCanplay = ref(false)
  const isPlaying = ref(false)
  // const durationDisplay = ref('')
  // const currentTimeDisplay = ref('00:00')
  // 格式化时间为 MM:SS
  // function formatTime(seconds: number) {
  //   const minutes = Math.floor(seconds / 60);
  //   const secs = Math.floor(seconds % 60);
  //   return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  // }

  onMounted(() => {
    btnDisabled.value = true
    const loadPromises = [
      new Promise(resolve => audio.value.onloadedmetadata = resolve),
    ]

    if (props.videoPath) {
      loadPromises.push(new Promise(resolve => video.value.onloadedmetadata = resolve))
    }

    // 确保视频和音频加载完成
    void Promise.all(loadPromises).then(() => {
      btnDisabled.value = false
      videoCanplay.value = true
      audioCanplay.value = true
      progress.value.max = audio.value.duration || Infinity
      // durationDisplay.value = formatTime(audio.value.duration)

      if (!props.videoPath) {
        return
      }

      const playMedia = () => {
        if (!isPlaying.value) return
        if (!videoCanplay.value || !audioCanplay.value) return
        video.value?.play()
        audio.value?.play()
      }

      // 同步播放和暂停控制
      playBtn.value.addEventListener('click', () => {
        isPlaying.value = true
        playMedia()
      })

      pauseBtn.value.addEventListener('click', () => {
        isPlaying.value = false
        video.value.pause()
        audio.value.pause()
      })

      // 同步进度条
      audio.value.addEventListener('timeupdate', () => {
        // currentTimeDisplay.value = formatTime(audio.value.currentTime)
        progress.value = audio.value?.currentTime
      })

      // 进度条控制视频和音频播放位置
      progress.value.addEventListener('input', (e: Event) => {
        videoCanplay.value = false
        audioCanplay.value = false
        const time = parseFloat((e.target as HTMLInputElement).value); // 确保从 DOM 元素的 value 属性获取值
        if (!isNaN(time)) {
          video.value.currentTime = time
          audio.value.currentTime = time
        }
      })

      video.value.addEventListener("waiting", () => {
        videoCanplay.value = false
        video.value.pause()
        audio.value.pause()
      });
      audio.value.addEventListener("waiting", () => {
        audioCanplay.value = false
        video.value.pause()
        audio.value.pause()
      });

      video.value.addEventListener("canplay", () => {
        videoCanplay.value = true
        playMedia()
      });
      audio.value.addEventListener("canplay", () => {
        audioCanplay.value = true
        playMedia()
      });

      // 确保播放和暂停时同步
      video.value.addEventListener('play', () => audio.value?.play())
      video.value.addEventListener('pause', () => audio.value?.pause())
    })
    // 处理加载错误
    // video.value.onerror = audio.value.onerror = (e: any) => {
    //   console.log(e)
    //   console.error('加载视频或音频时出错！')
    // }
  })

  return () => (
    <div>
      {
        props.videoPath
          ? <video ref={video} muted width="300" controls={false} src={props.videoPath} />
          // ? <video ref={video} muted width="300" controls src={props.videoPath} />
          : (
              <div class="size-[300px] bg-gray-200 flex-col flex items-center justify-center">
                <SvgIcon class="size-20" name="ic_empty" />
                <div class="text-[var(--text-3)] mt-2">视频不存在</div>
              </div>
            )
      }
      <audio ref={audio} src={props.audioPath} class={`${!props.videoPath ? 'mt-4' : ''}`} controls={!props.videoPath} />
      {/* <audio ref={audio} src={props.audioPath} class={`${!props.videoPath ? 'mt-4' : ''}`} controls /> */}

      {props.videoPath
        ? (
            <div class="space-x-2 py-4">
              <button class="btn btn-primary btn-sm" disabled={btnDisabled.value} ref={playBtn}>播放</button>
              <button class="btn btn-sm" disabled={btnDisabled.value} ref={pauseBtn}>暂停</button>
              <input type="range" disabled={btnDisabled.value} class="progress w-40" ref={progress} min="0" value="0" step="0.1" />
              {/* <span class="text-xs text-[var(--text-3)]">{currentTimeDisplay.value} / {durationDisplay.value}</span> */}
            </div>
          )
        : null}
    </div>
  )
})
