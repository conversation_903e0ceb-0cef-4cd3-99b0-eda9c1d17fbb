import { httpClient } from 'src/lib/http-client'
import { WelfareEatDetails, ReceiveRes, ReclaimReceiveRes } from './meal-check-in.d'

// 吃饭详情
export const apiGetWelfareEatDetails = () => {
  return httpClient.get<ApiResponse<WelfareEatDetails>>('/dm-api/welfare/eat-detail')
}
// 领取奖励
export const ApiPostWelfareReceive = (data: {
  welfare_id: number
  schedule?: number
}) => {
  return httpClient.post<ApiResponse<ReceiveRes>>('/dm-api/welfare/receive', data)
}
// 活动补领
export const apiPostWelfareReclaimReceive = (data: {
  welfare_id: number
  schedule?: number
}) => {
  return httpClient.post<ApiResponse<ReclaimReceiveRes>>('/dm-api/welfare/reclaim-receive', data)
}
// 看激励视频后领取广告奖励
export const ApiPostWelfareADReceive = (data: {
  ad_extra: string
}) => {
  return httpClient.post<ApiResponse<ReceiveRes>>('/dm-api/welfare/ad-receive', data)
}

// 获取权益弹窗
export const apiGetPrivilege = () => {
  return httpClient.get<ApiResponse<{
    popup: number
  }>>('/dm-api/welfare/privilege')
}
