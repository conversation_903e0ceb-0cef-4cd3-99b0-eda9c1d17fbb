/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, CreateForm, Icon, showFailToast, showSuccessToast, transformNumber } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { langKey, langValue } from 'src/modules/resource/constant'
import { z } from 'zod'
import { useValidator } from '@skynet/shared'
import { apiInjectSubtitle } from 'src/modules/resource-publish/resource-publish-api'
import { requiredLabel } from 'src/lib/required-label'

type InjectVideoFormOptions = {
  props: {
    episodeNums: number[]
    resourceId: number | undefined
  }
  emits: {
    save: () => void
    cancel: () => void
  }
}

export const InjectVideoForm = createComponent<InjectVideoFormOptions>({
  props: {
    episodeNums: [],
    resourceId: undefined
  },
  emits: {
    save: () => {},
    cancel: () => {}
  }
}, (props, { emit }) => {
  const btnLoading = ref(false)
  const Form = CreateForm<M.IInjectSubtitleParams>()
  const params = ref<M.IInjectSubtitleParams>({
    lang_list: [],
    force: false,
    series_resource_id: props.resourceId,
    serial_numbers: props.episodeNums,
    audio_type: 1
  })
  const formRules = z.object({
    lang_list: z.array(z.string()).min(1, '请选择语言'),
  })
  const { error, validateAll } = useValidator(params, formRules)
  const onSave = async () => {
    if (!validateAll()) {
      return
    }

    btnLoading.value = true
    try {
      await apiInjectSubtitle(params.value)
      const res = await apiInjectSubtitle(params.value)
      if (res.data?.success) {
        emit('save')
        showSuccessToast(`操作成功, ${res.data?.processing_code || 0}条处理中`)
      } else {
        showFailToast('操作失败')
      }
    } catch (error: any) {
      showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
    } finally {
      btnLoading.value = false
    }
  }
  return () => (
    <x-resource-subtitle-cover class="block">
      <Form
        class="grid grid-cols-1 gap-y-3"
        hasAction={false}
        data={params.value}
        error={error.value}
        onChange={(path, value) => {
          set(params.value, path, value)
        }}
        items={[
          [() => requiredLabel('资源ID：'), 'series_resource_id', { type: 'text', disabled: true, placeholder: '请输入资源ID' }, { transform: transformNumber }],
          [() => requiredLabel('语言：'), 'lang_list', { type: 'multi-select', placeholder: '请选择语言', popoverWrapperClass: 'z-popover-in-dialog', options: langKey.map((key, index) => {
            return {
              value: key,
              label: langValue[index],
            }
          }) }],
          // [() => requiredLabel('重新压制：'), 'force', { type: 'radio', options: [{
          //   value: true,
          //   label: '是',
          // }, {
          //   value: false,
          //   label: '否',
          // }] }],
          [() => requiredLabel('合成集数：'), 'serial_numbers', {
            type: 'custom',
            render: () => <div class="whitespace-pre-wrap break-words">共{props.episodeNums.length}集，集数：{props.episodeNums.join(',')}</div>
          } ]
        ]}
      />
      <div class="mt-4 flex justify-end gap-x-2">
        <Button class="btn btn-sm btn-default" onClick={() => {
          emit('cancel')
        }}>
          取消
        </Button>
        <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={onSave}>
          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
          开始合成
        </Button>
      </div>
    </x-resource-subtitle-cover>
  )
})

export default InjectVideoForm
