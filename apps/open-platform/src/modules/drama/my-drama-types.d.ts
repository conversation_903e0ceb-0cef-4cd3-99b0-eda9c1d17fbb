declare namespace M {
  namespace MyDrama {
    interface DramaCreateParams {
      resource_key?: string
      title: string
      description: string
      language_code: string // 人声语言
      vertical_cover: string
      vertical_cover_for_show?: string
      count?: number
      subtitle_language_code: string // 字幕语言
      unlocked_episodes?: number // 解锁集数
      auth_start_time: number // 授权开始时间
      auth_end_time: number // 授权到期时间
      video_type: number // 视频类型 1 带字幕  2无字幕
      audit_status?: number // 审核状态 -1 全部 0 草稿 1待审核 2 审核不通过 3 发布中 4 已发布
      audit_failed_reason?: string // 审核失败原因
    }

    interface DramaGetDetailRequest {
      resource_key: string
    }

    interface DramaGetDetailResource extends DramaCreateParams {
      oss_type?: string // resource 使用中国oss桶，static 使用美国oss桶
      resource_key: string
      list?: [
        {
          // 字幕
          subtitle: {
            path: string
            size: number
          }
          // 视频
          episode: {
            path: string
            resolution: string
            size: number
          }
          serial_number: number
        },
      ]
    }
    interface DramaGetDetailResponse {
      resource: DramaGetDetailResource
      oss_type?: string // resource 使用中国oss桶，static 使用美国oss桶
    }

    interface DramaListRequest {
      page_index: number
      page_size: number
      title?: string
      audit_status?: number // 审核状态 -1 全部 0 草稿 1待审核 2 审核不通过 3 发布中 4 已发布
      create_start_time?: number
      create_end_time?: number
      created_order?: number // 创建时间排序 1 从远到近 0 从近到远
    }

    interface DramaListItem {
      resource_key: string
      title: string
      description: string
      vertical_cover: string
      audit_status: number // 审核状态 -1 全部 0 草稿 1待审核 2 审核不通过 3 发布中 4 已发布
      audit_failed_reason: string
      audit_failed_reason_en: string
      count: number
    }

    interface DramaListResponse {
      list: DramaListItem[]
      total: number
    }

    interface DramaAuditRequest {
      resource_key: string
      audit_operate: number // /审核操作 1 提交审核
    }

    interface DramaResourceUploadRequest {
      duration?: number // 视频时长
      file_path: string // 文件地址
      file_size: number // 文件大小
      serial_number: number // 序号
      file_resolution?: string // 分辨率
      resource_key: string // 剧本id
      type: number // 数据类型 1 源视频 2 字幕 3 纯净视频 4 音频
      oss_type?: string // resource 使用中国oss桶，static 使用美国oss桶
    }

    interface DramaResourceDeleteRequest {
      serial_number: number // 序号
      resource_key: string // 剧本id
      type: number // 0 全部 数据类型 1 源视频 2 字幕 3 纯净视频 4 音频
    }

    interface TempPath {
      path: string
    }

  }
}
