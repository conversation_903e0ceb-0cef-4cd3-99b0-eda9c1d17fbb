/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Icon, openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiGetResourcePublishDetail, apiGetTerminologyList } from 'src/modules/resource-publish/resource-publish-api'
import { apiGetSingleSubtitle } from 'src/modules/role-mark/role-mark-api'
import { apiGetAiVoiceResource } from 'src/modules/voiceover-resource/voiceover-resource-api'
import { apiGetSubtitleErrors, apiGetSubtitleErrorsDetail, apiGetSubtitleFixes } from 'src/modules/resource-publish/resource-publish-api'
import { apiSubtitleDel, apiSubtitleExtractionV2, apiUploadSaveSubtitle, apiSubtitleTrans } from 'src/modules/resource/resource-api'
import { langKey, defaultClipValue } from 'src/modules/resource/constant'
import { Clip1 } from '../resource/components/clip1'
import { Clip } from '../resource/components/clip'
import { requiredLabel } from 'src/lib/required-label'

export const useInspectionRoleDetailStore = () => {
  return {
    setSeriesId,
    getDetail,
    currentResource,
    characters,
    annotations,
    getCharacters,
    emotions,
    effects,
    emotions_cn,
    effects_cn,
    getAnnotations,
    getRealSubtitle,
    getList,
    list,
    loading,
    defaultLanguageCode,
    getSubtitleErrors,
    subtitleErrorList,
    getSingleSubtitleErrors,
    errDetail,
    getSubtitleFixes,
    subtitleFixesList,
    onCleanSubtitle,
    onExtractSubtitle,
  }
}

const series_resource_id = ref<number>(-1)
const list = ref<Api.VoiceoverResource.IMultipleResource[]>([])
const loading = ref(false)
const characters = ref<M.ITerminology[]>([])
const annotations = ref<Api.RoleMark.IAnnotation[]>([])

const emotions = ref<string[]>([])
const effects = ref<string[]>([])
const emotions_cn = ref<string[]>([])
const effects_cn = ref<string[]>([])

const defaultLanguageCode = ref<string>('')
const cdnUrl = 'https://img.tianmai.cn/'
const currentResource = ref<M.IResourcePublishDetail>()
const subtitleErrorList = ref<M.IResourceSubtitleError[]>([])
const errDetail = ref<M.IResourceSubtitleDetailError[]>([])
const subtitleFixesList = ref<M.IResourceSubtitleDetailError[]>([])

const setSeriesId = (id: number) => {
  series_resource_id.value = id
}

const getList = async (showLoading = true) => {
  if (showLoading) {
    loading.value = true
  }
  const res = await apiGetAiVoiceResource({
    series_resource_id: series_resource_id.value,
  })
  // 将数据按照剧集号(num)重新组织
  const srts = res.data?.srts || []
  const episodeMap = new Map()

  srts.forEach(srt => {
    const language = srt.language
    srt.infos?.forEach(info => {
      const { num } = info
      if (!episodeMap.has(num)) {
        episodeMap.set(num, { num })
      }
      const episode = episodeMap.get(num)
      // 为每种语言添加相关路径信息
      episode[`${language}_ora_video_path`] = info.ora_video_path
      episode[`${language}_pure_video_path`] = info.pure_video_path
      episode[`${language}_srt_path`] = info.srt_path
      episode[`${language}_audio_path`] = info.audio_path
      episode[`${language}_annotation`] = info.annotation
      episode[`${language}_episode_path`] = info.episode_path
      episode[`${language}_voice`] = info.voice
      episode[`${language}`] = srt.language
    })
  })
  // 转换为数组形式
  list.value = Array.from(episodeMap.values()).sort((a, b) => a.num - b.num)
  defaultLanguageCode.value = res.data?.default_language_code || ''
  loading.value = false
}

const getAnnotations = async (num: number) => {
  try {
    const res = await apiGetSingleSubtitle({
      series_resource_id: series_resource_id.value,
      serial_number: num,
    })
    // 已经填入的
    annotations.value = res.data?.annotations || []
    emotions.value = res.data?.emotions || []
    effects.value = res.data?.effects || []
    emotions_cn.value = res.data?.emotions_cn || []
    effects_cn.value = res.data?.effects_cn || []
  } catch (error: any) {
    showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
  } finally {
    loading.value = false
  }
}

const getCharacters = async () => {
  try {
    const res = await apiGetTerminologyList({
      series_resource_id: series_resource_id.value,
    })
    characters.value = res?.data?.term_list || []
  } catch (error: any) {
    showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
  } finally {
    loading.value = false
  }
}

const getDetail = async () => {
  try {
    const res = await apiGetResourcePublishDetail({
      id: series_resource_id.value,
    })
    currentResource.value = res.data || {}
  } catch (error: any) {
    showFailToast(error.response.data.err_msg || '获取详情失败')
  }
}

function getRealSubtitle(path: string) {
  if (!path) return ''
  const url = path.indexOf('http') === 0 ? path : `${cdnUrl}${path}`
  return url
}

const getSubtitleErrors = async () => {
  const res = await apiGetSubtitleErrors({
    series_resource_id: +series_resource_id.value,
  })
  subtitleErrorList.value = (res.data?.list || []).filter((item: M.IResourceSubtitleError) => item.language_code === props.language[0] || item.language_code === 'en') || []
}

const getSingleSubtitleErrors = async (num: number, lang: string) => {
  const res = await apiGetSubtitleErrorsDetail({
    series_resource_id: +series_resource_id.value,
    serial_number: num,
    language_code: lang,
  })
  errDetail.value = res.data?.errs || []
}

const getSubtitleFixes = async (n: number, lang: string, code: string) => {
  try {
    const res = await apiGetSubtitleFixes({
      series_resource_id: +series_resource_id.value,
      serial_number: n,
      language_code: lang,
      srt_content: code,
    })
    subtitleFixesList.value = res.data?.errs || []
  } catch (error: any) {
    showFailToast(error.response.data.err_msg || '获取详情失败')
  }
}

const isValidVideoUrl = (url: string) => {
  return url && url.indexOf('http') === 0
}

const onCleanSubtitle = (row: Api.VoiceoverResource.IMultipleResource, cb?: () => void) => {
  const job_way = ref(2)
  const reuse_detext = ref<0 | 1>(1)
  const btnLoading = ref(false)
  const position = ref<[number, number, number, number][]>([[0, 0, 0, 0]])
  position.value = [defaultClipValue]

  const originPath = getRealSubtitle(row[`${defaultLanguageCode.value}_ora_video_path`])
  const purePath = getRealSubtitle(row[`${defaultLanguageCode.value}_pure_video_path`])

  const playingEpisodePath = computed(() => {
    if (reuse_detext.value === 1) {
      return isValidVideoUrl(purePath) ? purePath : originPath
    }
    return originPath
  })

  const closeDialog = openDialog({
    title: '抹除字幕',
    mainClass: 'pb-0 px-5',
    body: () => (
      <>
        <div class="flex items-center gap-x-2 pb-4">
          <div>抹除方式:</div>
          <select class="select select-bordered select-sm" value={job_way.value} onChange={e => {
            const target = e.target as HTMLSelectElement
            const value = target.value
            job_way.value = +value
          }}
          >
            <option value={3}>字节</option>
            <option value={2}>腾讯</option>
            <option value={1}>阿里</option>
          </select>
          <div>抹除视频:</div>
          <select class="select select-bordered select-sm" value={reuse_detext.value} onChange={e => {
            const target = e.target as HTMLSelectElement
            const value = target.value
            reuse_detext.value = +value as 0 | 1
          }}
          >
            <option value={1}>使用已抹除的视频</option>
            <option value={0}>使用源视频</option>
          </select>
        </div>
        <Clip1 title="抹除坐标" path={playingEpisodePath.value} defaultValue={[defaultClipValue]} onChange={(p: [number, number, number, number][]) => {
          position.value = p
        }}
        />
        <div class="flex items-center justify-end gap-x-2 py-2">
          <Button class="btn-default btn btn-sm" onClick={() => {
            closeDialog()
          }}
          >取消
          </Button>
          <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
            try {
              btnLoading.value = true
              await apiSubtitleDel({
                series_resource_id: series_resource_id.value,
                serial_numbers: [row.num],
                regions: position.value,
                job_way: job_way.value,
                reuse_detext: reuse_detext.value,
              })
              showSuccessToast('操作成功')
              closeDialog()
              cb && cb()
            } catch (error: any) {
              showFailToast(error.response.data.err_msg || '操作失败')
            } finally {
              btnLoading.value = false
            }
          }}
          >
            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
            确认
          </Button>
        </div>
      </>
    ),
    customClass: '!w-[500px]',
  })
}

const onExtractSubtitle = (row: Api.VoiceoverResource.IMultipleResource, cb?: () => void) => {
  const btnLoading = ref(false)
  const language_code = ref(langKey[0])
  const type = ref<1 | 2 | number>(2)
  const position = ref<[number, number, number, number]>([0, 0, 0, 0])
  position.value = defaultClipValue
  const originPath = getRealSubtitle(row[`${defaultLanguageCode.value}_ora_video_path`])

  const closeDialog = openDialog({
    title: '字幕抽取',
    mainClass: 'pb-0 px-5',
    body: () => (
      <>
        <div class="flex space-x-4">
          <div class="flex items-center gap-x-2 pb-4">
            类型:
            <select class="select select-bordered select-sm" value={type.value} onChange={e => {
              const target = e.target as HTMLSelectElement
              const value = target.value
              type.value = +value as unknown as 1 | 2
            }}
            >
              <option value={2}>自研</option>
              {/* <option value={1}>阿里云</option> */}
            </select>
          </div>
        </div>
        <Clip title="提取坐标" defaultValue={defaultClipValue} path={originPath} onChange={(p: [number, number, number, number]) => {
          position.value = p
        }}
        />
        <div>{requiredLabel('字幕提取后，当前字幕将被覆盖')}</div>
        <div class="flex items-center justify-end gap-x-2 py-2">
          <Button class="btn-default btn btn-sm" onClick={() => {
            closeDialog()
          }}
          >取消
          </Button>
          <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
            if (type.value === 2 && !['zh-CN', 'en', 'ja'].includes(language_code.value)) {
              showFailToast('自研提取暂只支持中、英、日语')
              return
            }
            btnLoading.value = true
            try {
              await apiSubtitleExtractionV2({
                series_resource_id: series_resource_id.value,
                serial_numbers: [row.num],
                region: position.value,
                type: type.value,
              })
              showSuccessToast('操作成功')
              closeDialog()
              cb && cb()
            } catch (error: any) {
              showFailToast(error.response.data.err_msg || '操作失败')
            } finally {
              btnLoading.value = false
            }
          }}
          >
            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
            确认
          </Button>
        </div>
      </>
    ),
    customClass: '!w-[500px]',
  })
}
