declare namespace M {
  interface default_product_item {
    product_id?: string
    price?: number
    first_recharge?: number // 首充标识 1.是 0.否 2.再次
    show_bonus?: number
    slogan?: string
    tips?: string
    description?: string
    props?: string[]
    // 0: 全部, 1: iOS, 2: Android
    platform?: string
    package_type?: number
    unlock_num?: number
    discount_rate?: number
    default_product?: unknown
    product_ids?: string[]
  }
  interface produItem {
    price_gt?: number
    price_eq_or_lt?: number
    default_products?: default_product_item[]
    product_id?: number
  }
  interface MemberLevel {
    id?: number
    app_id?: number // 应用ID
    app_name?: string
    product_type?: string // 商品类型：recharge｜membership
    title?: string // 档位名称
    slogan?: string // 角标文案
    tips?: string // 提示文案
    currency?: string // 货币单位
    price?: number // 原价
    discount_price?: number // 现价
    platform?: string
    title_admin?: string // 档位名称
    description?: string
    discount_desc?: string
    member_limited?: number // 会员限时 默认1 只展示暂时不能修改
    delivery_details?: { // 商品详情
      quanity?: number // 金币数量/VIP时长时长
      bonus?: number// 赠送数量，
      daily_bonus?: number // 每日补给
      periodDay?: number // 赠送天数 值同period对应的天数一致
      period: string // VIP时长单位 // weekly/monthly/yearly (product_type=membership 必填)
    }
    store?: string // 上架商店 Apple Store, Google Play
    apple_store?: { // apple 商店配置信息 (apple_store，google_play根据store 二选一)
      apple_id: string
      product_id?: string
      group_identifier: string
    }
    status?: number // 状态：1-启用，2-禁用
    props?: string[] // 属性列表 1.高亮 2.默认推荐
    priority?: number // 排序序号
    first_recharge?: number// 首充标识 1.是 0.否
    create_user?: string // 创建者
    update_user?: string // 更新者
    created?: number
    updated?: number
    has_discount?: number //  1.是 0.否
    time_limit?: number
    membership_type?: string // 会员类型：ad
    membership_ad_config?: {
      watch_ad_times: number // 单位：分钟，看多久弹一次广告
      ad_units: {
        max: string
        admob: string
      } // 加载的广告单元
      upgrade_product_id: number // 升级的产品id
      upgrade_tips: string // 提示文案
      upgrade_desc: string // 升级文案
    }
    one_product?: MemberLevel[]
    more_products?: MemberLevel[]
    package_type?: number // 道具类型 5整剧
    unlock_num?: number // 解锁集数
    discount_rate?: number // 道具基础折扣
    product_ids?: number[] // 关联商品id
    default_product_id?: number // 默认商品id
    // 整剧打包数据
    episode_products?: produItem[]
    show_floating_box_episode?: number // 第几集开始展示多集解锁礼包
    package_description?: string // c 端显示的礼包名称
    product_items?: undefined
    show_in_payment_panel?: number
    show_in_unlock?: number
  }
}
