import { createComponent, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { coins, amazonCard, amazonDialog, copy as copyIcon, dialogBg } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { useClipboard } from '@vueuse/core'
import { showToast } from 'src/modules/common/toast/toast'
import { computed, ref, watch, watchEffect } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { openDialog } from 'src/modules/common/dialog'
type GiftOptions = {
  props: {
    gift?: GiftType
  }
}
export const SpotifyGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      type: 'spotify',
    },
  },
}, props => {
  const { copy, copied } = useClipboard()
  const { t } = useLocale()
  const tipsRef = ref<HTMLDivElement>()

  watch(() => copied.value, () => {
    if (copied.value) {
      showToast('复制成功')
    }
  })

  const reachLimit = computed(() => props.gift?.user_coins >= props.gift?.gift_coins)

  watchEffect(() => {
    if (!reachLimit.value) {
      track('exchangezone', 'earncoins_button', 'show', {
        gift_id: props.gift?.id,
      })
    }
  })
  const openExchangeFailDialog = () => {
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: () => (
        <x-title class="block w-full h-21 relative">
          <img src={amazonDialog} class="absolute h-21 w-auto object-cover left-1/2 -translate-x-1/2 top-1/2" />
        </x-title>
      ),
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="min-h-[260px] mb-4 shadow flex flex-col bg-no-repeat pt-16">
          <x-body-main class="flex mx-4 flex-col gap-y-4">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center truncate">Exchange Failed !</x-title>
            <x-content class="w-full text-[var(--text-1)] text-center line-clamp-2">
              This is an anomaly in your account
            </x-content>
            <Button class="w-[calc(100%-2.5rem)] mx-7 h-11 mb-6 no-tap-color text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={() => closeDialog()}>{t('coins.exchange.tryAgainBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openExchangeSuccessDialog = () => {
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: () => (
        <x-title class="block w-full h-21 relative">
          <img src={amazonDialog} class="absolute h-21 w-auto object-cover left-1/2 -translate-x-1/2 top-1/2" />
        </x-title>
      ),
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="min-h-[260px] mb-4 shadow flex flex-col bg-no-repeat pt-16">
          <x-body-main class="flex mx-4 flex-col gap-y-4">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center truncate">Exchange Succesffully !</x-title>
            <x-explain class="flex flex-col items-center text-center">
              <x-price class="block mx-[6px] text-[32px] font-bold text-[var(--brand-6)] max-w-[calc(100%-12px)] truncate">Card ID:</x-price>
              <x-description class="w-full text-sm font-medium text-[var(--text-2)] text-wrap flex items-center justify-center">
                <span>1274-2948-DFHS-SJAD</span>
                <img src={copyIcon} class="w-4 h-4 ml-1 cursor-pointer no-tap-color" onClick={() => copy('1274-2948-DFHS-SJAD')} />
              </x-description>
            </x-explain>
            <Button class="w-[calc(100%-2.5rem)] mx-7 h-11 mb-6 no-tap-color text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={() => closeDialog()}>{t('coins.exchange.receiveBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const handleClick = () => {
    if (props.gift?.can_exchange) {
      if (reachLimit.value) {
        openExchangeDialog()
      } else {
        track('exchangezone', 'earncoins_button', 'click')
        window.location.href = 'freereels://freereels.app/rewards_task?enter_from=exchange_page'
      }
    }
  }

  const flag = ref(false)
  watch(() => [tipsRef.value, flag.value], () => {
    flag.value = false
    if (tipsRef.value) {
      const lineHeight = parseFloat(getComputedStyle(tipsRef.value).lineHeight)
      const fontSize = parseFloat(getComputedStyle(tipsRef.value).fontSize)
      if (fontSize <= 8) {
        return
      }
      const maxHeight = lineHeight * 2
      if (tipsRef.value.scrollHeight > maxHeight) {
        tipsRef.value.style.fontSize = `${fontSize - 2 <= 8 ? 8 : fontSize - 2}px`
        flag.value = true
      }
    }
  })

  const openExchangeDialog = () => {
    openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: () => (
        <x-title class="block w-full h-21 relative">
          <img src={amazonDialog} class="absolute h-21 w-auto object-cover left-1/2 -translate-x-1/2 top-1/2" />
        </x-title>
      ),
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="min-h-[260px] mb-4 shadow flex flex-col bg-no-repeat pt-16">
          <x-body-main class="flex mx-4 flex-col gap-y-4">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center truncate">Amazon Amazon Amazon Amazon Amazon Amazon</x-title>
            <x-explain class="flex flex-col items-center text-center">
              <x-price class="block mx-[6px] text-[32px] font-bold text-[var(--brand-6)] max-w-[calc(100%-12px)] truncate">$9999999999999999999999</x-price>
              <x-description class="w-full text-sm  font-medium text-[var(--text-2)] text-wrap line-clamp-2">Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard Giftcard</x-description>
            </x-explain>
            <x-content class="w-full text-[var(--text-1)] text-center line-clamp-2">
              Redeem requierd 30000 coins Redeem requierd 30000 coinsrequierd requierd
            </x-content>
            <Button class="w-[calc(100%-2.5rem)] mx-7 h-11 mb-6 no-tap-color text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={openExchangeSuccessDialog}>{t('coins.earnings.exchange')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  return () => (
    <x-gift-item class={mc('flex gap-x-3 p-3', props.gift?.can_exchange ? '' : 'opacity-20')}>
      <x-gift-item-left class="flex flex-col gap-1">
        <x-card class="px-1 py-3 size-11 box-border border-solid border-[1px] border-[var(--line-1)] rounded-lg">
          <img src={props.gift?.image_url ?? amazonCard} class="w-full h-auto object-cover" />
        </x-card>
        <x-price class="block text-center text-xs font-bold text-[var(--text-2)]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class="flex-1 flex flex-col gap-1 truncate">
        <x-gift-title class="w-full text-sm text-[var(--text-2)] truncate">{props.gift.name}</x-gift-title>
        {
          props.gift?.can_exchange && (
            <x-gift-coins class="text-sm flex items-center">
              <img src={coins} class="w-4 h-4 object-cover mr-[2px]" />
              <x-gift-amount class="text-[var(--brand-6)]">{props.gift.user_coins}</x-gift-amount>
              <x-separator class="px-[2px] text-[var(--text-4)]">/</x-separator>
              <x-gift-remains class="text-[var(--text-1)]">{props.gift.gift_coins}</x-gift-remains>
            </x-gift-coins>
          )
        }
        {
          props.gift?.can_exchange && (
            <x-gift-progress>
              <Progress class="mt-1" value={props.gift.ratio} max={1} />
            </x-gift-progress>
          )
        }
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21', props.gift?.button_tips ? 'justify-start gap-y-1' : 'justify-center')}>
        <Button class="min-h-8 border-none px-1 py-[2px] bg-[var(--brand-6)] text-[var(--text-5)] text-sm w-full rounded-lg" onClick={handleClick}>
          <p class="w-full h-auto line-clamp-2 break-words text-wrap text-center leading-4">
            {reachLimit.value ? t('coins.earnings.exchange') : t('coins.exchange.earnCoins')}
          </p>
        </Button>
        {props.gift?.button_tips && <x-tips class="w-full h-6 text-[var(--text-2)] text-center text-[10px] leading-3 line-clamp-2 break-words" ref={tipsRef}>{props.gift?.button_tips}</x-tips>}
      </x-gift-item-right>
    </x-gift-item>
  )
})
