import { AlertProps, MergeClass, showAlert } from '@skynet/ui'
import { useAlertStore } from '@skynet/ui/alert/use-alert-store'
import { FunctionalComponent } from 'vue'

export const AlertDemo1: FunctionalComponent<{}> = (props, { slots }) => {
  const onClick = (type: AlertProps['type']) => () => {
    const _hideAlert = showAlert(<span>Hello World!</span>, type, {
      autoHide: 3000,
    })
  }
  const { renderAlerts } = useAlertStore()
  return (
    <MergeClass tag="div" baseClass="block space-x-4">
      <button class="btn" onClick={onClick('info')}>open info alert</button>
      <button class="btn" onClick={onClick('warning')}>open warning alert</button>
      <button class="btn" onClick={onClick('success')}>open success alert</button>
      <button class="btn" onClick={onClick('error')}>open error alert</button>
      <div class="fixed space-y-4 top-[100px] z-alert left-1/2 transform -translate-x-1/2 transition-[height]">
        {renderAlerts()}
      </div>
    </MergeClass>

  )
}

export default AlertDemo1
