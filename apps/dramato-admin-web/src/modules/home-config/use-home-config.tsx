import { ref } from 'vue'
import { apiGetHomeConfig, apiGetHomeModuleList, apiHomeConfigRelease } from './home-config-api'
import { createNumberId } from '@skynet/shared'
import { HomeConfigDialog } from './home-config-dialog'
import { openDialog } from '@skynet/ui'

const searchForm = ref<M.HomeConfigSearchProps>({
  page_info: {
    page_index: 1,
    page_size: 10,
  },
})
const total = ref<number>(0)
const loading = ref<boolean>(false)
const list = ref<M.HomeItemConfig[]>([])
const refresh = ref<number>(0)

const onSearch = (isInitial?: boolean) => {
  if (isInitial) {
    searchForm.value.page_info = {
      page_index: 1,
      page_size: 10,
    }
  }
  loading.value = true
  refresh.value = createNumberId()
  void apiGetHomeModuleList(searchForm.value).then(res => {
    if (!res.data) return
    list.value = res.data.list
    total.value = res.data.total
  }).finally(() => {
    loading.value = false
  })
}

const onReset = () => {
  searchForm.value = {
    app_id: undefined,
    language: undefined,
    page_info: {
      page_index: 1,
      page_size: 10,
    },
  }
  onSearch(true)
}

const edit = (row: M.HomeItemConfig) => {
  if (!row.id) return
  const close = openDialog({
    title: '编辑模块',
    body: (
      <HomeConfigDialog configId={row.id} onCancel={() => close()} onSave={() => {
        close()
        onSearch()
      }}
      />
    ),
  })
}

const add = () => {
  const close = openDialog({
    title: '新建模块',
    body: (
      <HomeConfigDialog type="add" onCancel={() => close()} onSave={() => {
        close()
        onSearch()
      }}
      />
    ),
  })
}

const confirmOffline = (row: M.HomeItemConfig) => {
  const close = openDialog({
    mainClass: 'px-5 pb-0',
    title: '下线确认',
    body: (
      <x-confirm-offline-dialog class="space-y-[25px]">
        <x-confirm-offline-body>请确认是否下线，下线后用户侧App将立即生效，不再显示当前模块</x-confirm-offline-body>
        <x-confirm-offline-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-default btn-sm" onClick={() => close()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            if (!row.id) return
            void apiHomeConfigRelease({
              module_ids: [row.id],
              release_type: 10,
            }).then(() => {
              close()
              onSearch()
            })
          }}
          >确定
          </button>
        </x-confirm-offline-footer>
      </x-confirm-offline-dialog>
    ),
  })
}

const confirmPublish = (row: M.HomeItemConfig) => {
  const close = openDialog({
    mainClass: 'px-5 pb-0',
    title: '发布确认',
    body: (
      <x-confirm-publish-dialog class="space-y-[25px]">
        <x-confirm-publish-body>请确认是否发布，发布后用户侧App将立即生效显示当前模块</x-confirm-publish-body>
        <x-confirm-publish-footer class="w-full flex justify-end gap-x-[10px]">
          <button class="btn btn-default btn-sm" onClick={() => close()}>取消</button>
          <button class="btn btn-primary btn-sm" onClick={() => {
            if (!row.id) return
            void apiHomeConfigRelease({
              module_ids: [row.id],
              release_type: 1,
            }).then(() => {
              close()
              onSearch()
            })
          }}
          >确定
          </button>
        </x-confirm-publish-footer>
      </x-confirm-publish-dialog>
    ),
  })
}

export const useHomeConfig = () => {
  return {
    searchForm,
    onSearch,
    onReset,
    loading,
    list,
    total,
    refresh,
    add,
    edit,
    confirmPublish,
    confirmOffline,
  }
}
