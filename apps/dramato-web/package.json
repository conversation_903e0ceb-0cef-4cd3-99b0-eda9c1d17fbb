{"name": "dramato-web", "private": true, "version": "1.0.1", "type": "module", "scripts": {"dev": "vite --host --mode development --port 5174 --no-clearScreen", "build": "vite build --mode production", "build:test": "vite build --mode staging", "build:prod": "vite build --mode production", "preview": "vite preview --host --base=./", "deploy:test": "bin/deploy.sh test", "deploy:prod": "bin/deploy.sh prod", "jenkins:test": "bin/jenkins.sh test", "jenkins:prod": "bin/jenkins.sh prod", "i18nGen": "node bin/i18n.mjs"}, "dependencies": {"@airwallex/components-sdk": "^1.18.0", "@better-scroll/core": "^2.5.1", "@skynet/client-track": "workspace:*", "@skynet/preset": "workspace:*", "@skynet/shared": "workspace:*", "@skynet/ui": "workspace:*", "@tweenjs/tween.js": "catalog:", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "axios": "catalog:", "crypto-js": "catalog:", "dayjs": "catalog:", "github-markdown-css": "5.6.1", "imagemin": "^9.0.0", "imagemin-webp": "^8.0.0", "js-cookie": "catalog:", "js-md5": "catalog:", "lodash-es": "catalog:", "lottie-web": "^5.12.2", "marked": "^15.0.1", "md-editor-v3": "^5.0.1", "pinia": "^3.0.1", "shaka-player": "catalog:", "tcplayer.js": "5.3.2", "vant": "4.9.17", "vh-check": "catalog:", "vite-bundle-analyzer": "catalog:", "vue": "catalog:", "vue-i18n": "^10.0.1", "vue-router": "catalog:", "zod": "catalog:"}, "devDependencies": {"@skynet/vite-plugin-svg-icons": "workspace:*", "@types/ali-oss": "catalog:", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "catalog:", "@types/lodash-es": "catalog:", "@unocss/postcss": "0.61.3", "@unocss/preset-wind": "0.61.3", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "csv-parse": "^5.6.0", "gray-matter": "^4.0.3", "markdown-it": "catalog:", "postcss": "catalog:", "postcss-import": "catalog:", "postcss-nested": "catalog:", "typescript": "catalog:", "unocss": "catalog:", "vconsole": "catalog:", "vite": "catalog:", "vite-plugin-markdown": "2.2.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "catalog:"}, "browserslist": ["defaults"]}