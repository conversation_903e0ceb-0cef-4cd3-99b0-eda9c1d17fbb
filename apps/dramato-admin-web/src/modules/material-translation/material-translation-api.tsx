import { httpClient } from 'src/lib/http-client'

export const apiGetList = (data: M.MaterialTranslation.Params) =>
  httpClient.post<M.MaterialTranslation.Response>('/material/translation/material_task_list', data)

export const apiGetDetail = (data: M.MaterialTranslation.Params) =>
  httpClient.get<M.MaterialTranslation.Response>('/material/translation/material_task_detail', data)

export const apiEditTask = (data: M.MaterialTranslation.Params) =>
  httpClient.post<M.MaterialTranslation.Response>('/material/translation/material_task_op', data)

export const apiConfirmMaterial = (data: M.MaterialTranslation.Params) =>
  httpClient.post<M.MaterialTranslation.Response>('/material/translation/material_result_confirmed', data)

export const apiSaveData = (data: M.MaterialTranslation.Params, action: string) =>
  httpClient.post<M.MaterialTranslation.Response>('/material/translation/task_create?action=' + action, data)

export const apiGetDraftTaskDetail = (data: M.MaterialTranslation.Params) =>
  httpClient.get<M.MaterialTranslation.Response>('/material/translation/task_info', data)

export const apiGetTaskTypeList = () =>
  httpClient.get<M.MaterialTranslation.Response>('/material/translation/task_type_list')

export const apiSetOperator = (data: M.MaterialTranslation.Params) =>
  httpClient.post<M.MaterialTranslation.Response>('/material/translation/material_set_operator', data)

export const apiGetMemberList = (data: M.MaterialTranslation.Params) =>
  httpClient.get<M.MaterialTranslation.Response>('/material/translation/material_member_list', data)
