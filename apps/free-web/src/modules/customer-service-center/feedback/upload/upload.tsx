import { createComponent, fn, mc, when } from '@skynet/shared'
import { addLight, delLight, failLight, loadingLight } from '../../images/images'
import { MergeClass } from '@skynet/ui'
import { inApp, jsBridge } from 'src/lib/jsbridge'
import { onMounted, ref } from 'vue'
import { useUploader } from './use-upload'
import router from 'src/router'
import { useLocale } from 'src/lib/use-locale'
import { authInfo } from 'src/lib/get-auth'

type UploadOptions = {
  props: {
    list: string[]
  }
  emits: {
    remove: (index: number) => void
    add: (item: string) => void
  }
}

export const Upload = createComponent<UploadOptions>({
  props: {
    list: [],
  },
  emits: {
    remove: fn,
    add: fn,
  },
}, (props, { emit }) => {
  const { ossData, getOssData, uploadRequest, isUploading } = useUploader()
  const uploadError = ref<boolean>(false)

  const language = router.currentRoute.value.query.language as string ?? 'en'
  const { t } = useLocale(language)

  const pickImage = () => {
    console.log('pickImage', 'isUploading', isUploading.value)
    if (isUploading.value) return
    console.log('pickImage', 'inApp', inApp)
    if (inApp) {
      uploadError.value = false
      console.log('pickImage')
      void jsBridge<{ rawData: string | null }>('pickImage').then(res => {
        if (!res) return
        if (!ossData.value) return
        if (res.rawData) {
          try {
            isUploading.value = true
            // rawData是一个base64字符串，转成bin data
            const data = atob(res.rawData.split(',')[1])
            const len = data.length
            const bytes = new Uint8Array(len)
            for (let i = 0; i < len; i++) {
              bytes[i] = data.charCodeAt(i)
            }
            const type = res.rawData.split(',')[0].split(':')[1].split(';')[0]
            const blob = new Blob([bytes], { type })
            // 用时间戳生成文件名
            const fileName = `${Date.now()}.${type.split('/')[1]}`
            const file = new File([blob], fileName, { type })
            const formData = new FormData()
            formData.append('key', ossData.value.dir + '/' + fileName)
            formData.append('policy', ossData.value.policy || '')
            formData.append('OSSAccessKeyId', ossData.value.access_id || '')
            formData.append('success_action_status', '200')
            formData.append('signature', ossData.value.signature || '')
            formData.append('file', file)
            void uploadRequest(formData, t('toast.upload-failed')).then(() => {
              emit('add', ossData.value?.dir + '/' + fileName)
            }).catch(e => {
            // uploadError.value = true
            }).finally(() => {
              isUploading.value = false
              emit('add', ossData.value?.dir + '/' + fileName)
            })
          } catch (e) {
            isUploading.value = false
            uploadError.value = true
            console.log('pickImage error', e)
          }
        }
      }).catch(() => {
        isUploading.value = false
      })
    }
  }

  onMounted(() => {
    void when(() => authInfo).then(() => {
      void getOssData()
    })
  })

  return () => (
    <MergeClass tag="div" baseClass="w-full flex justify-start items-center gap-x-3">
      {props.list?.map((item, index) => (
        <x-image-wrapper class="size-[88px] bg-[var(--fill-1)] rounded-lg border border-solid border-[var(--line-1)] relative cursor-pointer">
          <img src={ossData.value?.static_domain + '/' + item} class="size-full rounded-lg absolute z-up top-0 left-0 object-cover" />
          <img onClick={() => emit('remove', index)} src={delLight} class="size-5 rounded-full absolute z-up-up -top-[10px] -right-[10px]" />
        </x-image-wrapper>
      ))}
      {
        props.list.length < 3
        && (
          <x-upload-add onClick={pickImage} class={mc('no-tap-color size-[88px] p-8 bg-[var(--fill-1)] rounded-lg border border-solid border-[var(--line-1)] relative flex justify-center items-center cursor-pointer', uploadError.value && 'border-[var(--brand-6)]')}>
            {!isUploading.value && (
              <x-add class="w-6 flex flex-col gap-y-1 justify-center items-center">
                <img src={addLight} class="w-6 h-6 object-cover" />
                <span class="text-[var(--text-4)] text-xs">{props.list.length}/3</span>
              </x-add>
            )}
            {isUploading.value && <img src={loadingLight} class="size-5 rounded-full animate-spin" />}
            {uploadError.value && <img src={failLight} class="size-5 rounded-full absolute z-up-up -top-[10px] -right-[10px]" />}
          </x-upload-add>
        )
      }
    </MergeClass>
  )
})
