import { createComponent, when } from '@skynet/shared'
import { Button, Input } from '@skynet/ui'
import { useFeedbackStore } from './feedback-store'
import { Upload } from './upload/upload'
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
import router from 'src/router'
import { track } from 'src/lib/track'
import { apiSubmitFeedback } from './feedback-api'
import { showToast } from '../../common/toast/toast'
import { inApp, jsBridge } from 'src/lib/jsbridge'
import { useLocale } from 'src/lib/use-locale'
import { deviceInfo } from 'src/lib/get-device-info'
import { authInfo } from 'src/lib/get-auth'
type FeedbackPageOptions = {
  props: {}
}
export const FeedbackPage = createComponent<FeedbackPageOptions>({
  props: {},
}, props => {
  const language = router.currentRoute.value.query.language as string ?? 'en'
  const from = router.currentRoute.value.query.from as string
  const series_id = router.currentRoute.value.query.series_id as string
  const episode_id = router.currentRoute.value.query.episode_id as string
  const wrapper = ref<HTMLDivElement>()
  const isSubmitting = ref(false)
  const { t } = useLocale(language)
  const { formData, resetFormData } = useFeedbackStore()
  const canSubmit = computed(() => {
    return formData.value.description !== '' && isSubmitting.value === false
  })

  const validateAll = () => {
    // description 必填
    if (formData.value.description === '') {
      return false
    }
    // email 邮箱格式校验
    if (formData.value.email && !validateEmail(formData.value.email.trim())) {
      showToast(t('toast.email-incorrect'))
      return false
    }
    return true
  }

  const validateEmail = (email: string) => {
    const reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
    return reg.test(email)
  }

  const submit = () => {
    if (!canSubmit.value) return
    track('feedback', 'submit', 'click', {
      from,
      series_id,
      episode_id,
    })
    if (!validateAll()) return
    isSubmitting.value = true
    void when(() => deviceInfo && authInfo).then(() => {
      void apiSubmitFeedback({ ...formData.value, email: formData.value.email.trim(), series_id, episode_id }).then(() => {
        isSubmitting.value = true
        showToast(t('toast.submit-success'))
        setTimeout(() => {
          if (inApp) {
            void jsBridge('close')
          }
        }, 2000)
      }).finally(() => {
        isSubmitting.value = false
      })
    })
  }

  onBeforeUnmount(() => {
    resetFormData()
  })

  onMounted(() => {
    track('feedback', 'page', 'show', {
      from,
      series_id,
      episode_id,
    })
    document.title = t('customer-service-center.feedback')
  })
  return () => (
    <div ref={wrapper} class="size-full bg-[var(--surface-4)] px-3">
      <form class="w-full flex flex-col gap-y-5 pt-4 pb-25 text-[var(--text-1)] text-sm font-normal">
        <x-form-item>
          <x-label class="flex items-center justify-between">
            <div class="flex gap-x-1 items-center">
              {t('customer-service-center.description')}
              <div class="text-[var(--brand-6)]">*</div>
            </div>
            <div class="text-right text-[var(--text-4)] text-xs font-normal">{formData.value.description.length}/300</div>
          </x-label>
          <x-description-area class="no-tap-color flex flex-col w-full px-3 pt-4 pb-3 bg-white rounded-lg gap-y-4 mt-3">
            <Input
              v-model={formData.value.description}
              placeholder={t('customer-service-center.description-placeholder')}
              type="textarea"
              class="no-tap-color"
              maxlength={300}
              inputClass="h-[150px] text-sm bg-white p-0"
            />
            <Upload list={formData.value.image_list} onRemove={(index: number) => formData.value.image_list.splice(index, 1)} onAdd={(item: string) => formData.value.image_list.push(item)} />
          </x-description-area>
        </x-form-item>
        <x-form-item>
          <x-label class="flex gap-x-1 items-center justify-start">
            Email
          </x-label>
          <Input
            v-model={formData.value.email}
            placeholder={t('customer-service-center.email-placeholder')}
            type="text"
            inputClass="text-sm"
            class="mt-3"
            onFocus={() => {
              setTimeout(() => {
                window?.scroll({ top: document.body.scrollHeight, behavior: 'smooth' })
              }, 1000)
            }}
          />
        </x-form-item>
      </form>
      <div class="fixed bottom-0 z-shareButton pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] w-[100%] left-1/2 -translate-x-1/2 h-25 bg-[var(--surface-4)]">
        <Button
          disabled={!canSubmit.value}
          class="fixed bottom-11 z-shareButton pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] w-[calc(100%-1.5rem)] left-1/2 -translate-x-1/2 h-11 bg-[var(--brand-6)] rounded-lg border-none text-[var(--text-5)] text-base leading-tight"
          onClick={submit}
        >
          {t('customer-service-center.submit-feedback')}
        </Button>
      </div>
    </div>
  )
})

export default FeedbackPage
