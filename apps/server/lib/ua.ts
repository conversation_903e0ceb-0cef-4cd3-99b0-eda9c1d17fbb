import { jsBridge } from './jsbridge'

const ua = window.navigator.userAgent.toLowerCase()
export const isWechat = () => /MicroMessenger/i.test(ua)
export const isQQ = () => ua.includes('nettype') && (ua.includes('qbwebview') || ua.includes('mqqbrowser'))
export const isWeibo = () => ua.includes('weibo')
export const isWebview = () => ua.includes('webview')
const isClient = typeof window !== 'undefined' && typeof document !== 'undefined'
// 判断是否是Android环境
export const isAndroid = () => (/Android/i.test(ua))
// mac系统判断
export const macSystemStatus = () => {
  return /macintosh|mac os x/i.test(ua)
}

// 判断是否是iOS设备
export const isIos = () => {
  if (jsBridge.inDramawaveApp) {
    const parts = window.navigator.userAgent.split('/')
    return parts[0].toLowerCase().trim() === 'ios' || /iP(ad|hone|od)/i.test(window.navigator.userAgent)
  }
  return (
    isClient
    && window?.navigator?.userAgent
    && (/iP(ad|hone|od)/i.test(window.navigator.userAgent)
    // The new iPad Pro Gen3 does not identify itself as iPad, but as Macintosh.
    // https://github.com/vueuse/vueuse/issues/3577
    || (window?.navigator?.maxTouchPoints > 2 && /iPad|Macintosh/.test(window?.navigator.userAgent)))
  )
}
export const isIpoxIos = () => /(iPhone|iPad|iPod|iOS)/i.test(ua) && window.screen.height >= 812

// 判断Android中各个厂家Addr
export const getAndroidAddr = () => {
  if (/vivo/gi.test(ua)) return 'isVivo'
  else if (/huawei|honor/gi.test(ua)) return 'isHuawei'
  else if (/HM|RedMi|Mi/gi.test(ua)) return 'isMi'
  else if (/oppo/i.test(ua)) return 'isOppo'
  else return 'other'
}

export const isFacebook = () => /FBAN|FBAV/i.test(ua)

export const isTikTok = () => /ByteLocale/i.test(ua)
