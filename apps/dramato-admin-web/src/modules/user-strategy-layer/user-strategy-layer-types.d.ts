/* eslint-disable @typescript-eslint/no-explicit-any */
declare namespace M {
  namespace UserStrategyLayer {
    interface params {
      page_info?: {
        offset?: number
        size?: number// 选填参数
      }
      layer_id?: string
      status?: number // 状态 1 线上 2 未上架
      persona_type?: number

      query_condition?: {
        CampaignType?: string[]
        UserType?: string[]
        Region?: string[]
        AdValueLevel?: string[]
        HasFirstRecharge?: string[]
        Device?: string[]
      }
    }

    interface ListItem {
      id: number
      priority: 1 // 优先级
      name: string
      desc: string
      status: number // 状态 1 线上 2 未上架
      created: number// 创建时间
      updated: number// 更新时间
      updated_operator_name: string
    }

    interface ListResponse {
      list: ListItem[]
      page_info: PageInfo2
    }

    interface UserStrategy {
      id?: number
      priority?: number // 优先级
      name: string
      desc: string
      send_formal_user?: number
      custom_series_ids: ? number[]
      series_package_id: number
      save_type?: number
      user_persona: Record<string, string[]>
      series_package_user_persona: Record<string, string[]>
      series_package_ids?: number[]
      persona_type?: number
    }

    interface ProfileItem {
      type_list: typeListItem[]
      type_name: string
      type_code: string
    }
    interface typeListItem {
      item_code: string
      item_name: string
      item_description: string
      item_type: number
      value_type: number
      value_prefix: string
      value_suffix: string
      value_concat: string
      name_concat: string
      name_prefix: string
      name_suffix: string
      can_custom: number
      item_child: Array<{ value: string, name?: string, group_name?: string }>
    }
  }
}
