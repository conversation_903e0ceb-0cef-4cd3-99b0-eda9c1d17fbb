<template>
  <div class="meterial-translate">
    <div class="mt-2 mb-2">素材翻译</div>
    <div class="bg-base-100 rounded-lg px-4 pt-4 pb-1">
      <ElForm inline >
        <ElFormItem label="任务ID">
          <ElInput v-model.number="searchForm.task_id" clearable placeholder="请输入任务ID" />
        </ElFormItem>
        <ElFormItem label="资源ID">
          <ElInput v-model.number="searchForm.resource_id" clearable placeholder="请输入资源ID" />
        </ElFormItem>
        <ElFormItem label="素材名">
          <ElInput v-model.number="searchForm.name" clearable placeholder="请输入素材名" />
        </ElFormItem>
        <ElFormItem label="素材ID">
          <ElInput v-model.number="searchForm.material_id" clearable placeholder="请输入素材ID" />
        </ElFormItem>
        <ElFormItem label="创建时间">
          <ElDatePicker v-model="searchForm.create_time" value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
        </ElFormItem>
        <ElFormItem label="任务类型">
          <ElSelect clearable class="!w-[150px]" v-model="searchForm.task_type" placeholder="任务类型">
            <ElOption v-for="(item, key) in taskTypeList" :key="key" :value="item.value" :label="item.label" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="任务状态">
          <ElSelect clearable class="!w-[120px]" v-model="searchForm.task_status" placeholder="任务状态">
            <ElOption v-for="(item, key) in taskStatusList" :key="key" :value="item.value" :label="item.label" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="素材确认">
          <ElSelect clearable class="!w-[120px]" v-model="searchForm.material_confirmed" placeholder="审核结果">
            <ElOption v-for="(item, key) in materialConfirmedList" :key="key" :value="item.value" :label="item.label" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="创建人">
          <ElInput v-model="searchForm.operator" placeholder="创建人" />
        </ElFormItem>
        <ElFormItem>
          <ElButton @click="handleReset">重置</ElButton>
          <ElButton type="primary" @click="handleSearch">搜索</ElButton>
        </ElFormItem>
      </ElForm>
    </div>
    <div class="bg-base-100 rounded-lg px-4 pt-4 pb-1 mt-4">
      <div class="flex justify-between mb-2 items-center">
        <div>
          <span class="text-sm text-gray-500">排队中任务数：{{ queueCount }}</span>
        </div>
        <ElButton type="primary" @click="handleAdd">创建任务</ElButton>
      </div>
      <ElTable :data="tableData" :loading="searchLoading" max-height="68vh">
        <ElTableColumn prop="material_id" label="ID" width="80" />
        <ElTableColumn prop="task_id" label="任务ID" width="100" />
        <ElTableColumn prop="material_name" label="素材名" width="150" />
        <ElTableColumn prop="resource_id" label="资源ID" width="100" />
        <ElTableColumn prop="series_key" label="剧集ID" width="150" />
        <ElTableColumn prop="audio_type" label="配音类型" width="100">
          <template #default="scope">
            <span>{{ audioTypeList.find(item => item.value === scope.row.audio_type)?.label }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="task_type" label="任务类型" width="100">
          <template #default="scope">
            <span>{{ taskTypeList.find(item => item.value === scope.row.task_type)?.label }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="series_langs" label="剧集语言" width="200">
          <template #default="scope">
            <span>{{ scope.row.series_langs.map(item => lanngList.find(lang => lang.value === item)?.label).join('、') }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="target_langs" label="目标语言" width="200">
          <template #default="scope">
            <span>{{ scope.row.target_langs.map(item => lanngList.find(lang => lang.value === item)?.label).join('、') }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="task_status" label="任务状态" width="100">
          <template #default="scope">
            <span>{{ taskStatusList.find(item => item.value === scope.row.task_status)?.label }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="material_confirmed" label="素材确认" width="100">
          <template #default="scope">
            <span>{{ materialConfirmedList.find(item => item.value === scope.row.material_confirmed)?.label }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="operator" label="创建人" width="150" />
        <ElTableColumn prop="create_dt" label="创建时间" width="220" />
        <ElTableColumn fixed="right" width="180" label="操作">
          <template #default="scope">
            <ElButton type="text" @click="handleEdit(scope.row)" v-if="scope.row.task_status == 1">编辑</ElButton>
            <template v-else>
              <ElButton type="text" @click="handleDetail(scope.row)">查看详情</ElButton>
              <ElButton type="text" @click="handleAssignTask(scope.row)" v-if="checkUserRole()">分配任务</ElButton>
            </template>
          </template>
        </ElTableColumn>
      </ElTable>
      <div class="flex justify-end py-4">
        <el-pagination
          layout="sizes, prev, pager, next"
          :total="searchForm.total"
          :page-sizes="[10, 20, 30, 50, 100, 200, 1000]"
          :page-size="searchForm.page_info.page_size"
          :current-page="searchForm.page_info.page_index"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>

      <ElDialog :close-on-click-modal="false" v-model="dialogCreateVisible" title="创建任务" width="60%">
        <div class=" overflow-y-auto" style="max-height: 70vh;">
          <ElForm :model="createForm" :rules="createRules" label-width="160px" ref="createFormRef">
            <ElFormItem label="资源ID" prop="resource_id">
              <ElInput style="width: 360px;" v-model="createForm.resource_id" placeholder="请输入资源ID" />
            </ElFormItem>
            <ElFormItem label="剧集配音类型" prop="audio_type">
              <ElSelect style="width: 360px;" v-model="createForm.audio_type" placeholder="请选择剧集配音类型">
                <ElOption v-for="(item, key) in audioTypeList" :key="key" :value="item.value" :label="item.label" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="剧集语种" :required="[1,2].includes(createForm.audio_type)" prop="series_langs">
              <ElSelect multiple style="width: 360px;" v-model="createForm.series_langs" placeholder="请选择剧集语种">
                <ElOption v-for="(item, key) in lanngList" :key="key" :value="item.value" :label="item.label" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="任务类型" prop="task_type">
              <ElSelect style="width: 360px;" v-model="createForm.task_type" placeholder="请选择任务类型">
                <ElOption v-for="(item, key) in filteredTaskTypeList" :key="key" :value="item.value" :label="item.label" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="目标语种" prop="target_langs">
              <ElSelect clearable multiple style="width: 360px;" v-model="createForm.target_langs" placeholder="请选择目标语种">
                <ElOption v-for="(item, key) in lanngList" :key="key" :value="item.value" :label="item.label" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem required label="视频素材上传">
              <div>
                <ElTable :data="metUrlObj" >
                  <ElTableColumn prop="name" header-align="center" align="center" label="素材名" width="250">
                    <template #default="scope">
                      <ElInput v-model="scope.row.name" placeholder="请输入素材名" />
                    </template>
                  </ElTableColumn>
                  <ElTableColumn prop="url" header-align="center" align="center" label="素材Url" width="250" >
                    <template #default="scope">
                      <ElInput v-model="scope.row.url" placeholder="请输入素材Url" />
                    </template>
                  </ElTableColumn>
                  <!-- <ElTableColumn width="100" label="操作">
                    <template #default="scope">
                      <ElButton type="danger" link size="small" @click="handleDeleteMaterial(scope.$index)">删除</ElButton>
                    </template>
                  </ElTableColumn> -->
                </ElTable>
                <div class="flex justify-end my-2">
                  <ElButton type="primary" size="small" @click="handleAddMaterial">添加</ElButton>
                </div>
                <!-- <uploadWrapper /> -->
                <!-- <ElUpload
                  drag
                  multiple
                  accept="mp4,mp3,wav"
                  action=""
                  :show-file-list="false"
                  :on-progress="handleUploadProgress"
                  :on-success="handleUploadSuccess"
                  :before-upload="handleBeforeUpload"
                  :http-request="uploadHandler"
                >
                  <ElIcon  style="height: 50px;width: 50px; opacity: 0.5;">
                    <UploadFilled style="width: 50px; height: 50px;" />
                  </ElIcon>
                  <div class=" mt-1">点击或拖拽上传视频至此处</div>
                  <div class=" mt-1 text-gray-500 text-xs">支持同时上传10个视频文件</div>
                  <ElButton type="primary" class="mt-2" >选择文件</ElButton>
                </ElUpload> -->
              </div>
            </ElFormItem>
            <h5 class="mt-4 font-bold">素材上传进度与细节补充</h5>
            <div class="mt-2">
              <ElTable :data="createForm.orig_materials" max-height="80vh">
                <ElTableColumn prop="name" header-align="center" align="center" label="素材名" width="150" />
                <ElTableColumn prop="length" header-align="center" align="center" label="素材大小" width="150" />
                <ElTableColumn prop="progress" header-align="center" align="center" label="上传进度" width="150">
                  <template #default="scope">
                    <!-- <span v-if="scope.row.progress == 0">上传中...</span> -->
                    <ElProgress :percentage="scope.row.progress" />
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="series_range" header-align="center" align="center" label="剧集范围" width="150" />
                <ElTableColumn fixed="right" width="220" label="操作">
                  <template #default="scope">
                    <ElButton type="danger" link size="small" @click="handleDeleteMaterial(scope.$index)">删除</ElButton>
                    <ElButton type="primary" link size="small" @click="chooseSeriesRange(scope.$index, scope.row)">选择剧集范围</ElButton>
                  </template>
                </ElTableColumn>
              </ElTable>
            </div>
          </ElForm>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogCreateVisible = false">取消</el-button>
            <el-button type="primary" @click="createSaveFun(1)">提交</el-button>
            <el-button type="primary" @click="createSaveFun(2)">保存草稿</el-button>
          </span>
        </template>
      </ElDialog>
      <ElDialog :close-on-click-modal="false" v-model="dialogSeriesRangeVisible" title="选择剧集范围" width="300px">
        <div class="flex items-center gap-2">
          <div>剧集范围</div>
          <ElInput style="width: 80px;" v-model="seriesRangeData.start" placeholder="开始" />
          <div>至</div>
          <ElInput style="width: 80px;" v-model="seriesRangeData.end" placeholder="结束" />
        </div>
        <div class="mt-2 text-gray-500 text-xs">注：集数范围越精准，处理速度越快</div>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogSeriesRangeVisible = false">取消</el-button>
            <el-button type="primary" @click="confirmSeriesRange">确认</el-button>
          </span>
        </template>
      </ElDialog>


      <ElDialog :close-on-click-modal="false" v-model="renamePop" title="重命名" width="600px">
      
        <div>
          <ElForm inline :model="dragUploadList" ref="renameFormRef">
            <div class="flex items-center" v-for="(item, key) in dragUploadList" :key="key">
              <ElFormItem label="素材名" prop="name">
                <ElInput v-model="item.name" placeholder="请输入素材名" />
              </ElFormItem>
              <ElFormItem label="原文件名" prop="url">
                <ElInput v-model="item.url" placeholder="请输入原文件名" />
              </ElFormItem>
            </div>
          </ElForm>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="renamePop = false">取消</el-button>
            <el-button type="primary" @click="confirmRename">确认</el-button>
          </span>
        </template>
      </ElDialog>

      <ElDialog :close-on-click-modal="false" v-model="detailPop"  title="资源详情" width="60%">
        <div v-loading="detailLoading">
          <ElForm inline>
            <div class="flex justify-between">
              <div>
                <span>资源ID：{{ detailData.resource_id }}</span>
                <span class="ml-2" v-if="detailData.series_langs">{{ detailData.series_langs.join(',') }}{{ audioTypeList.find(item => item.value === detailData.audio_type)?.label }}</span>
              </div>
              <div class="flex items-center gap-2">
                <span>任务状态</span><span class="block px-3 py-1 rounded-2xl bg-blue-100 text-blue-500">{{ taskStatusList.find(item => item.value === detailData.task_status)?.label }}</span>
              </div>
            </div>
            <div class="flex items-center gap-x-6 flex-wrap mt-4">
              <ElFormItem label="素材ID：">
                <span>{{ detailData.material_id }}</span>
              </ElFormItem>
              <ElFormItem label="素材名称：">
                <span>{{ detailData.material_name }}</span>
              </ElFormItem>
              <ElFormItem label="素材剧集范围：">
                <span>{{ detailData.series_range }}</span>
              </ElFormItem>
              <ElFormItem label="任务类型：">
                <span>{{ taskTypeList.find(item => item.value === detailData.task_type)?.label }}</span>
              </ElFormItem>
              <ElFormItem label="创建时间：">
                <span>{{ detailData.created_dt }}</span>
              </ElFormItem>
              <ElFormItem label="完成时间：">
                <span>{{ detailData.finished_dt }}</span>
              </ElFormItem>
            </div>
            <div>
              <ElButton type="primary" @click="stopTask" :disabled="detailData.task_status !== 4">暂停任务</ElButton>
              <ElButton type="primary" @click="redoTask" :disabled="![6,7,8].includes(detailData.task_status)">重新生成</ElButton>
            </div>
            <div class="mt-2">
              <ElTable :data="detailData.target" max-height="80vh">
                <ElTableColumn align="center" prop="lang" label="生产语种" />
                <ElTableColumn align="center" prop="material" label="生产素材">
                  <template #default="scope">
                    <div v-if="scope.row.err_msg">
                      <span>{{ scope.row.err_msg }}</span>
                    </div>
                    <div v-else>
                      <ElButton link type="primary" @click="handlePreview(scope.row)">预览</ElButton>
                      <ElButton link type="primary" @click="handleDownload(scope.row)">下载</ElButton>
                    </div>
                  </template>
                </ElTableColumn>
                <ElTableColumn align="center" prop="orig_duration" label="素材时长-原素材(ms)">
                  <template #default="scope">
                    <span>{{ timestampToTime(scope.row.orig_duration) }}</span>
                  </template>
                </ElTableColumn>
                <ElTableColumn align="center" prop="duration" label="素材时长-生成素材(ms)">
                  <template #default="scope">
                    <span>{{ timestampToTime(scope.row.duration) }}</span>
                  </template>
                </ElTableColumn>
              </ElTable>
            </div>

          </ElForm>
        </div>
        <template #footer>
          <div class="dialog-footer flex justify-end">
            <div class="flex justify-between" style="width: 68%;">
              <div>
                <ElButton type="primary" @click="downloadAllMaterial(2)">导出下载</ElButton>
                <ElButton type="primary" @click="downloadAllMaterial(1)">批量下载</ElButton>
                <ElButton type="primary" :disabled="detailLoading" @click="confirmMaterialPop">素材确认</ElButton>
              </div>
              <div>
                <ElButton @click="detailPop = false">取消</ElButton>
              </div>
            </div>
          </div>
        </template>
      </ElDialog>
      <ElDialog :close-on-click-modal="false" v-model="previewVisible" @close="stopAllVideo" title="预览" width="700px">
        <div>
          <div class="flex justify-center gap-2 w-full">
            <div class="flex-1">
              <div class="text-center">原素材</div>
              <video class="w-full" ref="oldVideo" @play="videoPlayOld" @pause="videoPauseOld" :src="previewData.old_url" controls />
            </div>
            <div class="flex-1">
              <div class="text-center">生成的素材</div>
              <video class="w-full" ref="newVideo" @play="videoPlayNew" @pause="videoPauseNew" :src="previewData.url" controls />
            </div>
          </div>
        </div>
      </ElDialog>
      <ElDialog :close-on-click-modal="false" v-model="confirmVisible" title="素材确认"  width="500px">
        <div>
          <ElForm label-width="100px">
            <ElFormItem label="可用情况">
              <ElSelect v-model="confirmForm.material_confirmed" placeholder="请选择素材确认">
                <ElOption v-for="(item, key) in materialConfirmedList" :key="key" :value="item.value" :label="item.label" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="备注" :required="confirmForm.material_confirmed == 2">
              <ElInput type="textarea" v-model="confirmForm.desc" placeholder="请输入备注" />
            </ElFormItem>
          </ElForm>
        </div>
        <template #footer>
          <div class="dialog-footer">

            <ElButton @click="confirmVisible = false">取消</ElButton>
            <ElButton type="primary" @click="confirmMaterial">确认</ElButton>
          </div>
        </template>
      </ElDialog>
      <ElDialog :close-on-click-modal="false" v-model="hasExistPop" @close="hasExistFiles = []" title="以下文件已存在" width="500px">
        <div>
          <div v-for="(item, key) in hasExistFiles" :key="key">
            <span>{{ item }}</span>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">

            <ElButton type="primary" @click="hasExistPop = false">确认</ElButton>
          </div>
        </template>
      </ElDialog>
      <ElDialog :close-on-click-modal="false" v-model="assignTaskPop" title="素材责任人" width="500px">
        <div>
          <ElSelect
          filterable
          :loading="searchNameLoading"
          v-model="assignDetail.set_operator" 
          placeholder="请选择负责人">
            <ElOption v-for="item in adminUserList" :value="item.name" :label="item.name" />
          </ElSelect>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <ElButton @click="assignTaskPop = false">取消</ElButton>
            <ElButton type="primary" @click="confirmAssignTask">确认</ElButton>
          </div>
        </template>
      </ElDialog>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, onMounted, onUnmounted, watch, defineComponent, computed } from 'vue'
// import { useRoute, useRouter } from 'vue-router'
import { Uploader } from '../common/uploader/uploader'
import { useRoute } from 'vue-router'
import { ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElIcon, ElLoading, ElMessageBox, ElMessage, ElUpload } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { usePopupScene } from '../ac-operation-position/use-popup-scene'
import { apiGetDetail, apiEditTask, apiConfirmMaterial, apiGetList, apiSaveData, apiGetDraftTaskDetail, apiGetTaskTypeList, apiSetOperator, apiGetMemberList } from './material-translation-api'
import { useUploader } from '../common/uploader/use-uploader'
import { useMenu } from '../menu/use-menu'
import { track } from "../../lib/track.ts";

const { ossData, ossDataLoading, ossDataBjLoading, getOssData, getResourceOssData, resetOssData, ossDataBj } = useUploader()
const {
    config,
    getConfig,
  } = usePopupScene()

const route = useRoute()
const envType = ref('')
const { getUserRoles } = useMenu()
const userRoles = getUserRoles()
const assignTaskPop = ref(false)
const queueCount = ref(0)
const assignDetail = ref({
  material_id: 0,
  set_operator: '',
})
const taskStatusList = [
  { value: 1, label: '草稿' },
  { value: 2, label: '已提交' },
  { value: 3, label: '素材上传中' },
  { value: 4, label: '排队中' },
  { value: 5, label: '进行中' },
  { value: 6, label: '任务成功' },
  { value: 7, label: '任务失败' },
  { value: 8, label: '暂停' },
  { value: 9, label: '下载失败' },
]

const taskTypeList = [
  { value: 1, label: '多语种复刻' },
  { value: 2, label: '小语种翻转复刻' },
  { value: 3, label: '竞品复刻' },
  { value: 4, label: '爆款翻转复刻' },
]
const adminUserList = ref([])

const popTaskTypeList = ref([1,2,3,4])

const materialConfirmedList = [
  { value: 1, label: '直接使用' },
  { value: 2, label: '暂时使用' },
  { value: 3, label: '不可用' },
]

const audioTypeList = [
  { value: 0, label: '无配音' },
  { value: 1, label: '配音剧(人工配音)' },
  { value: 2, label: 'ai配音剧' },
]
const dialogSeriesRangeVisible = ref(false)
const detailPop = ref(false)
const detailLoading = ref(false)
const seriesRangeData = ref({
  start: '',
  end: '',
  index: 0,
})
const createFromInit = ref<M.MaterialTranslation.createItem>({
  resource_id: undefined,
  series_key: '', // 剧集ID
  audio_type: 0, // 配音类型: 0-无配音 1-配音剧(人工配音) 2-ai配音剧
  series_langs: [], // 剧集语种(配音剧必填、多选)
  task_type:1, // 任务类型:1-多语种复刻;2-小语种翻转复刻;3-竞品复刻;4-爆款翻转复刻
  target_langs: [], // 目标语种(支持多选)配音剧:与剧集语种一致；非配音剧:自由选择
  material_ids: [], // 素材列表，通过素材上传接口上传后返回的id
  custom_list:[{name:'', url: ''}],
  orig_materials: [],
  task_status: 1,
  task_id: 0,
})
const metUrlObj = ref([{
  name: '',
  url: ''
}])

const confirmForm = ref({
  material_id: 0,
  material_confirmed: 1,
  desc:''
})

const oldVideo = ref()
const newVideo = ref()
const dragUploadList = ref({})
const dragUploadedList = ref({})

const createForm = ref<M.MaterialTranslation.createItem>({})

const detailData = ref<M.MaterialTranslation.detailData>({})

const lanngList = ref<string[]>([])

const createRules = reactive<M.MaterialTranslation.createItem>({
  resource_id: [
    { required: true, message: '请输入资源ID', trigger: 'blur' },
  ],
  audio_type: [
    { required: true, message: '请选择剧集配音类型', trigger: 'blur' },
  ],
  task_type: [
    { required: true, message: '请选择任务类型', trigger: 'blur' },
  ],
  target_langs: [
    { required: true, message: '请选择目标语种', trigger: 'blur' },
  ],
})

const searchLoading = ref(false)
const dialogCreateVisible = ref(false)
const previewVisible = ref(false)
const confirmVisible = ref(false)
const createFormRef = ref()
const previewData = ref({
  url: '',
  old_url: '',
})

const searchForm = ref({
  task_id: undefined,
  resource_id: undefined,
  task_type: undefined,
  task_status: undefined,
  material_confirmed: undefined,
  operator: undefined,
  name: undefined,
  material_id: undefined,
  create_time: undefined,
  page_info: {
    page_index: 1,
    page_size: 20,
  },
  total: 0,
})

const renamePop = ref(false)
const renameForm = ref({
  name: '',
  url: '',
  index: 0,
})

const tableData = ref<M.MaterialTranslation.listItem[]>([])
const handleSearch = () => {
  // tableData.value.push({
  //   task_id: '1',
  //   material_id: '1',
  //   resource_id: '1',
  //   task_type: '1',
  //   task_status: '1',
  //   audit_result: '1',
  //   operator: '1',

  // })
  getListData()
  console.log(searchForm.value)
}

const checkUserRole = () => {
  return [59, 61, 62].some(item => userRoles.includes(item))
}

const handleRename = (index: number) => {
  renamePop.value = true
  renameForm.value.index = index
  renameForm.value.name = createForm.value.orig_materials[index].name
  renameForm.value.url = createForm.value.orig_materials[index].url
}

const confirmRename = () => {
  for(let i in dragUploadList.value) {
    if(!dragUploadList.value[i].name) {
      ElMessage.error('请输入素材名')
      return false
    }
  }
  for(let i in dragUploadList.value) {
    if(dragUploadedList.value[i]) {
      addMaterialToList({...dragUploadedList.value[i], name: dragUploadList.value[i].name})
    }else{
      addMaterialToList(dragUploadList.value[i])
    }
  }
  renamePop.value = false
  dragUploadList.value = {}

}

const videoPlayOld = () => {
  newVideo.value.play()
}

const videoPauseOld = () => {
  newVideo.value.pause()
}

const videoPlayNew = () => {
  oldVideo.value.play()
}

const videoPauseNew = () => {
  oldVideo.value.pause()
}

const videoSeekOld = (e: any) => {
  console.log(e)
  newVideo.value.currentTime = e.target.currentTime
}

const videoSeekNew = (e: any) => {
  // oldVideo.value.currentTime = e.target.currentTime
}


const confirmMaterialPop = () => {
  confirmForm.value.material_id = detailData.value.material_id
  confirmForm.value.material_confirmed = 1
  confirmForm.value.desc = ''
  confirmVisible.value = true
}

const confirmMaterial = () => {
  if(confirmForm.value.material_confirmed == 2 && !confirmForm.value.desc) {
    ElMessage.error('请输入备注')
    return
  }
  const loading = ElLoading.service({
    lock: true,
    text: '执行中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  apiConfirmMaterial({
    material_id: confirmForm.value.material_id,
    material_confirmed: confirmForm.value.material_confirmed,
    desc: confirmForm.value.desc,
  }).then((res: any) => {
    ElMessage.success('操作成功')
    confirmVisible.value = false
    getListData()
    console.log(res)
  }).finally(() => {
    loading.close()
  })
}

const makeExcel = (data: any, name: string) => {
  return new Promise(resolve => {
    // 将数据转换为 CSV 格式
    const csvContent = data.map((e: any) => e.join(',')).join('\n')
    // 添加 UTF-8 BOM
    const bom = '\uFEFF'
    const finalContent = bom + csvContent

    // 创建一个 Blob 对象
    const blob = new Blob([finalContent], { type: 'text/csv;charset=utf-8;' })

    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', name)
    document.body.appendChild(link)
    link.click()
    resolve('')
  })
}

const onDownloadBatchRes = async (data: any, dataObj: any) => {
    try {
      const tableHeader = [
        '资源id',
        '素材名',
        '素材语种',
        '素材url'
      ]
      const tableData: any = []

      data.map((item: any) => {
        tableData.push([
          dataObj.resource_id,
          dataObj.material_name,
          lanngList.value.find(lang => lang.value == item.lang)?.label,
          item.material,
        ])
      })

      await makeExcel([
        tableHeader,
        ...tableData,
      ], '' + Date.now())
    } catch (error) {
      console.log(error)
    }
  }

const handleAssignTask = (row: any) => {
  assignTaskPop.value = true
  assignDetail.value.material_id = row.material_id
  assignDetail.value.set_operator = ''
}

const confirmAssignTask = () => {
  if(!assignDetail.value.set_operator) {
    ElMessage.error('请选择责任人')
    return
  }
  const loading = ElLoading.service({
    lock: true,
    text: '执行中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  apiSetOperator({
    material_id: assignDetail.value.material_id,
    set_operator: assignDetail.value.set_operator,
  }).then((res: any) => {
    ElMessage.success('操作成功')
    assignTaskPop.value = false
    getListData()
  }).finally(() => {
    loading.close()
  })
}

const getListData = () => {
  const loading = ElLoading.service({
    lock: true,
    text: '请求中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  const timeRange = {
    created_begin: searchForm.value.create_time?.[0] || undefined,
    created_end: searchForm.value.create_time?.[1] || undefined,
  }
  apiGetList({
    ...searchForm.value,
    create_time: undefined,
    task_id: searchForm.value.task_id || undefined,
    resource_id: searchForm.value.resource_id || undefined,
    ...timeRange
  }).then((res: any) => {
    tableData.value = res.data.list
    queueCount.value = res.data.queue_count
    searchForm.value.total = res.data.total
  }).finally(() => {
    loading.close()
  })
}

const handlePreview = (row: any) => {
  previewVisible.value = true
  console.log(row)
  previewData.value = {
    url: row.material,
    old_url: detailData.value.oss_path,
  }
}

const createSaveFun = (type: number) => {
  if(!createFormRef.value){
    return false
  }
  if(createForm.value.orig_materials.length == 0) {
    ElMessage.error('请添加素材')
    return false
  }
  if(createForm.value.orig_materials.length > 0) {
    for(let i in createForm.value.orig_materials) {
      // 验证文件名格式
      const namePattern = /^[a-zA-Z0-9\u4e00-\u9fa5\-_]+$/
      //对不符合正则匹配的所有符号替换成_
      createForm.value.orig_materials[i].name = createForm.value.orig_materials[i].name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5\-_]/g, '_')
      // if(!namePattern.test(createForm.value.orig_materials[i].name)) {
      //   ElMessage.error('素材名只能包含英文字母、中文汉字、数字、英文减号和下划线')
      //   return false
      // }
    }
  }
  createFormRef.value.validate((valid) => {
    if (valid) {
      const loading = ElLoading.service({
        lock: true,
        text: '保存中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const action = type == 1 ? 'submit' : 'draft'
      const submitData = {
        ...createForm.value,
        action: action,
        resource_id: Number(createForm.value.resource_id),
        task_id: Number(createForm.value.task_id),
        env: envType.value || 'prod'
      }
      apiSaveData(submitData, action).then((res: any) => {
        console.log(res)
        ElMessage.success(type == 1 ? '提交成功' : '保存成功')
        dialogCreateVisible.value = false
        handleSearch()
      }).catch((err: any) => {
        console.log(err)
        ElMessage.error(err?.response?.data?.err_msg || err?.response?.data?.message || '操作失败')
      }).finally(() => {
        loading.close()
      })
    } else {
      console.log('error submit!')
      return false
    }
  })
}

const handleDownload = async (row: any) => {
  await downloadFile(row.material)

  track('admin', 'ads', 'download', {
    material_id: detailData.value.material_id,
    material_name: detailData.value.material_name,
    lang: row.lang
  })
}

const downloadFile = async (url: string) => {
  const downloadUrl = url.includes('?') 
    ? `${url}&response-content-disposition=attachment`
    : `${url}?response-content-disposition=attachment`
  console.log(downloadUrl)
  window.location.href = downloadUrl
}

const timestampToTime = (timestamp: number) => {
  const hours = Math.floor(timestamp / 3600000)
  const minutes = Math.floor((timestamp % 3600000) / 60000)
  const seconds = Math.floor((timestamp % 60000) / 1000)
  const ms = timestamp.toString().slice(-3)
  return `${hours}:${minutes}:${seconds}.${ms}`
}

const downloadAllMaterial = (type: number) => {
  if(type == 1) {
    const list = detailData.value.target.map((item: any) => item.material)
    for(let i = 0; i < list.length; i++) {
      setTimeout(() => {
        downloadFile(list[i])
      }, 1000 * i)
    }

    
    track('admin', 'ads', 'batch_download', {
      material_id: detailData.value.material_id,
      material_name: detailData.value.material_name,
      count: detailData.value.target.length
    })
  }else{
    onDownloadBatchRes(detailData.value.target, detailData.value)

    track('admin', 'ads', 'batch_download', {
      material_id: detailData.value.material_id,
      material_name: detailData.value.material_name,
      count: detailData.value.target.length
    })
  }
}

const handleEdit = (row: any) => {
  console.log(row)
  apiGetDraftTaskDetail({
    task_id: row.task_id,
  }).then((res: any) => {
    createForm.value = res.data
    for(let i in createForm.value.orig_materials) {
      createForm.value.orig_materials[i].progress = 100
    }
    dialogCreateVisible.value = true
  })
}

const stopAllVideo = () => {
  if(oldVideo.value) {
    oldVideo.value.pause()
  }
  if(newVideo.value) {
    newVideo.value.pause()
  }
}

const handleDetail = (row: any) => {
  detailPop.value = true

  detailData.value.material_id = row.material_id

  getMeterialDetail()
}

const stopTask = () => {
  console.log('stopTask')
  ElMessageBox.confirm('确定要暂停吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '暂停中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    apiEditTask({
      material_id: detailData.value.material_id,
      action: 1,
    }).then((res: any) => {
      ElMessage.success('操作成功')
      detailPop.value = false
      getListData()
      console.log(res)
    }).finally(() => {
      loading.close()
    })
  })
}

const redoTask = () => {
  ElMessageBox.confirm('确定要重新生成吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '执行中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    apiEditTask({
      material_id: detailData.value.material_id,
      action: 2,
    }).then((res: any) => {
      ElMessage.success('操作成功')
      detailPop.value = false
      getListData()
    }).finally(() => {
      loading.close()
    })
  })
}

const getMeterialDetail = () => {
  detailLoading.value = true
  apiGetDetail({
    material_id: detailData.value.material_id,
  }).then((res: any) => {
    console.log(res)
    detailData.value = res.data
  }).finally(() => {
    detailLoading.value = false
  })
}


const chooseSeriesRange = (index: number, row: any) => {
  seriesRangeData.value.index = index
  seriesRangeData.value.start = row.start
  seriesRangeData.value.end = row.end
  dialogSeriesRangeVisible.value = true
}

const confirmSeriesRange = () => {
  if(!seriesRangeData.value.start || !seriesRangeData.value.end) {
    ElMessage.error('请输入剧集范围')
    return
  }
  createForm.value.orig_materials[seriesRangeData.value.index].series_range = seriesRangeData.value.start+'-'+seriesRangeData.value.end

  dialogSeriesRangeVisible.value = false
  console.log(seriesRangeData.value)
}



const handleUploadSuccess = async (response: any, file: any, fileList: any) => {
  console.log('handleUploadSuccess', response, file, fileList)
  
  if(!uploadSuccessedList.value.includes(file.name)) {
    uploadSuccessedList.value.push(file.name)
  }
  console.log('file',file)
  const fileSize = file.size
  // const audioUrl = URL.createObjectURL(file.raw)
  // const audio = new Audio(audioUrl)
  
  // await new Promise((resolve) => {
  //   audio.onloadedmetadata = () => resolve(null)
  // })

  // const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
  // const arrayBuffer = await file.raw.arrayBuffer()
  // const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
  // const duration = parseInt(Number(audioBuffer.duration.toFixed(3))*1000)
  // console.log('duration',duration)

  dragUploadedList.value[file.name] = {
    name: '',
    url: file.name,
    material_type: 2,
    length: fileSize,
    oss_path: file.raw.path,
    duration: 0,
    progress: 100,
    series_range: '',
    material_id: 0,
    task_id: 0,
  }
  console.log('dragUploadedList匹配文件完成---'+file.name)


  for(let i in createForm.value.orig_materials){
    if(createForm.value.orig_materials[i].url == file.name) {
      createForm.value.orig_materials[i].progress = 100
      createForm.value.orig_materials[i].length = fileSize
      createForm.value.orig_materials[i].oss_path = file.raw.path
      createForm.value.orig_materials[i].duration = 0
      // break
    }
  }
  console.log('orig_materials匹配文件完成---'+file.name)
}

const handleUploadProgress = (event: any, file: any, fileList: any) => {
  console.log('handleUploadProgress', event, file, fileList)
  if(dragUploadList.value[file.name]) {
    dragUploadList.value[file.name].progress = parseInt(file.percentage)
  }
  if(dragUploadedList.value[file.name]) {
    dragUploadedList.value[file.name].progress = parseInt(file.percentage)
  }
  for(let i in createForm.value.orig_materials) {
    if(createForm.value.orig_materials[i].url == file.name) {
      createForm.value.orig_materials[i].progress = parseInt(file.percentage)
      // break
    }
  }
}

const handleUploadChange = (file: any, fileList: any) => {
  console.log('handleUploadChange', file, fileList)
}

const hasExistFiles = ref([])
const hasExistPop = ref(false)
const handleBeforeUpload = (file: any) => {
  console.log('handleBeforeUpload', file)
  console.log('file beforeUpload',file)
  let fileName = ''
  let fileNameArr = file.name.split('.')
  let hasFile = false
  for(let i in createForm.value.orig_materials) {
    if(createForm.value.orig_materials[i].url == file.name) {
      hasFile = true
    }
  }
  if(hasFile) {
    // ElMessage.error(file.name+'文件已存在')
    hasExistFiles.value.push(file.name)
    hasExistPop.value = true
    return false
  }
  fileNameArr.pop()
  fileName = fileNameArr.join('.')
  
  dragUploadList.value[file.name] = {
    name: fileName,
    url: file.name,
    progress: 0,
    series_range: '',
    material_id: 0,
    task_id: 0,
    material_type: 2,
  }
  renamePop.value = true
  console.log('123123')
  return true
}

function getBody(xhr: XMLHttpRequest): XMLHttpRequestResponseType {
  const text = xhr.responseText || xhr.response
  if (!text) {
    return text
  }

  try {
    return JSON.parse(text)
  } catch {
    return text
  }
}

function getError(
  action: string,
  option: UploadRequestOptions,
  xhr: XMLHttpRequest,
) {
  let msg: string
  if (xhr.response) {
    msg = `${xhr.response.error || xhr.response}`
  } else if (xhr.responseText) {
    msg = `${xhr.responseText}`
  } else {
    msg = `fail to ${option.method} ${action} ${xhr.status}`
  }

  return new UploadAjaxError(msg, xhr.status, option.method, action)
}

const uploadHandler = (options: any) => {
  const data = ossDataBj.value
  const file = options.file as File & { path: string, url: string }
  // 创建文件对象并添加到列表中
  const uploadFile: UploadUserFile = {
    name: file.name,
    size: file.size,
    uid: Date.now(),
    status: 'ready' as UploadStatus,
    percentage: 0,
    raw: file as any,
  }

  const formData = new FormData()
  const path = data?.dir + '/' + file.name
  formData.append('key', path)
  formData.append('policy', data?.policy || '')
  formData.append('OSSAccessKeyId', data?.accessid || '')
  formData.append('success_action_status', '200')
  formData.append('signature', data?.signature || '')
  formData.append('Content-Type', file.type || 'image/jpeg')
  formData.append('file', file)
  file.path = path
  const xhr = new XMLHttpRequest()

  if (xhr.upload) {
    xhr.upload.addEventListener('progress', evt => {
      // console.log('progress', evt)
      const progressEvt = evt as UploadProgressEvent
      progressEvt.percent = evt.total > 0 ? (evt.loaded / evt.total) * 100 : 0
      uploadFile.percentage = progressEvt.percent
      options?.onProgress && options?.onProgress(progressEvt)
    })
  }

  xhr.addEventListener('load', () => {
    if (xhr.status < 200 || xhr.status >= 300) {
      uploadFile.status = 'error' as UploadStatus
      return options.onError && options.onError(getError(data?.host || '', options, xhr))
    }
    uploadFile.status = 'success' as UploadStatus
    uploadFile.url = file.name
    options.onSuccess && options.onSuccess(getBody(xhr))

    // emit('success', {
    //   file: uploadFile,
    // })
  })

  xhr.addEventListener('error', () => {
    uploadFile.status = 'error' as UploadStatus
    options.onError && options.onError(getError(data?.host || '', options, xhr))
  })

  xhr.open('POST', data?.host || '')
  xhr.send(formData)
  return xhr
}

const uploadSuccessedList = ref([])

const uploadWrapper = defineComponent({
  setup(props, { emit }) {
    return () => (
      <Uploader
        accept="mp4,mp3,wav"
        ossKeyType="resource"
        isImage={false}
        showFileList={false}
        maxsize={1024 * 1024 * 1024 * 2}
        onUploadFailed={() => {
          ElMessage.error('上传失败')
        }}
        beforeUpload={async (file: any) => {
          console.log('file beforeUpload',file)
          dragUploadList.value = {}
          file.files.map((item: any) => {
            let fileName = ''
            let fileNameArr = item.name.split('.')
            fileNameArr.pop()
            fileName = fileNameArr.join('.')
            dragUploadList.value[item.name] = {
              name: fileName,
              url: item.name,
              progress: 0,
              series_range: '',
              material_id: 0,
              task_id: 0,
              material_type: 2,
            }
          })
          renamePop.value = true
        }}
        onUploadSuccess={async (file: any) => {
          if(!uploadSuccessedList.value.includes(file.file.name)) {
            uploadSuccessedList.value.push(file.file.name)
          }else{
            return
          }
          console.log('file',file)
          const fileSize = file.file.size
          const audioUrl = URL.createObjectURL(file.file)
          const audio = new Audio(audioUrl)
          
          await new Promise((resolve) => {
            audio.onloadedmetadata = () => resolve(null)
          })

          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
          const arrayBuffer = await file.file.arrayBuffer()
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
          const duration = parseInt(Number(audioBuffer.duration.toFixed(3))*1000)
          console.log('duration',duration)

          dragUploadedList.value[file.file.name] = {
            name: '',
            url: file.file.name,
            material_type: 2,
            length: fileSize,
            oss_path: file.temp_path,
            duration: duration,
            progress: 100,
            series_range: '',
            material_id: 0,
            task_id: 0,
          }
          for(let i in createForm.value.orig_materials){
            if(createForm.value.orig_materials[i].url == file.file.name) {
              createForm.value.orig_materials[i].progress = 100
              createForm.value.orig_materials[i].length = fileSize
              createForm.value.orig_materials[i].oss_path = file.temp_path
              createForm.value.orig_materials[i].duration = duration
              break
            }
          }
          {/* const inData = {
            name: file.file.name,
            url: '',
            material_type: 2,
            length: fileSize,
            oss_path: file.temp_path,
            duration: duration,
            series_range: '',
            material_id: 0,
            task_id: 0,
          }
          if(dragUploadList.value[file.file.name]) {
            addMaterialToList(inData)
          } */}

        }}
      >
        <x-uploader-wrapper class="block">
          <div class="flex flex-col items-center border border-dashed border-gray-300 rounded-lg p-4 bg-gray-100 py-10">
            <ElIcon  style="height: 50px;width: 50px; opacity: 0.5;">
              <UploadFilled style="width: 50px; height: 50px;" />
            </ElIcon>
            <div class=" mt-1">点击或拖拽上传视频至此处</div>
            <div class=" mt-1 text-gray-500 text-xs">支持同时上传10个视频文件</div>
            <ElButton type="primary" class="mt-2" >选择文件</ElButton>
          </div>
        </x-uploader-wrapper>
      </Uploader>
    )
  }
})

const handleAddMaterial = () => {
  // createForm.value.custom_list.push({name:'', url: ''})
  if(!metUrlObj.value[0].name){
    ElMessage.error('请输入素材名')
    return false
  }
  if(!metUrlObj.value[0].url){
    ElMessage.error('请输入素材Url')
    return false
  }

  let hasFile = false
  for(let i in createForm.value.orig_materials) {
    if(createForm.value.orig_materials[i].name == metUrlObj.value[0].name) {
      hasFile = true
    }
  }
  if(hasFile) {
    // ElMessage.error(file.name+'文件已存在')
    hasExistFiles.value.push(metUrlObj.value[0].name)
    hasExistPop.value = true
    return false
  }
  
  const inData = {
    name: metUrlObj.value[0].name,
    url: metUrlObj.value[0].url,
    material_type: 1,
    length: 0,
    oss_path: '',
    duration: 0,
    series_range: '',
    material_id: 0,
    task_id: 0,
    progress: 100,
  }
  addMaterialToList(inData)
  metUrlObj.value[0].name = ''
  metUrlObj.value[0].url = ''
  ElMessage.success('添加成功')

}

const addMaterialToList = (data) =>{
  const index = createForm.value.orig_materials.findIndex((item: any) => item.url == data.url)
  if(index > -1) {
    createForm.value.orig_materials[index] = data
  }else{
    createForm.value.orig_materials.push(data)
  }
}

const handleDeleteMaterial = (index: number) => {
  console.log(createForm.value.orig_materials)
  const name = createForm.value.orig_materials[index].url
  const nameIndex = uploadSuccessedList.value.indexOf(name)
  if(nameIndex > -1) {
    uploadSuccessedList.value.splice(nameIndex, 1)
  }
  createForm.value.orig_materials.splice(index, 1)
  delete dragUploadedList.value[name]
}

const handleSizeChange = (size: number) => {
  searchForm.value.page_info.page_size = size
  getListData()
}

const handlePageChange = (page: number) => {
  searchForm.value.page_info.page_index = page
  getListData()
}

const handleReset = () => {
  searchForm.value = {
    task_id: undefined,
    resource_id: undefined,
    task_type: undefined,
    task_status: undefined,
    material_confirmed: undefined,
    operator: undefined,
    name: undefined,
    material_id: undefined,
    create_time: undefined,
    page_info: {
      page_index: 1,
      page_size: 20,
    },
  }
}
const handleAdd = () => {
  createForm.value = JSON.parse(JSON.stringify(createFromInit.value))
  dialogCreateVisible.value = true
}

watch(() => config.value.lang_items, () => {
  formatLangs()
})

const lanTransObj = ref({
  'de-DE': '德语',
  'en-US': '英语',
  'fr-FR': '法语',
  'it-IT': '意大利语',
  'es-MX': '西班牙语',
  'pt-PT': '葡萄牙语',
  'ru-RU': '俄语',
  'ja-JP': '日语',
  'ko-KR': '韩语',
  'ar-SA': '阿拉伯语',
  'ms-MY': '马来西亚语',
  'hi-IN': '印地语',
  'tr-TR': '土耳其语',
  'vi-VN': '越南语',
  'tl-PH': '菲律宾语',
  'th-TH': '泰语',
  'id-ID': '印尼语',
  'zh-CN': '简体中文',
  'zh-TW': '繁体中文',
})

const getLanTrans = (key: string) => {
  if(!key.includes('zh')) {
    return ' ('+lanTransObj.value[key]+')'
  }
  return ''
}

const formatLangs = () => {
  if(lanngList.value.length > 0) return
  for(const key in config.value.lang_items) {
    lanngList.value.push({label: config.value.lang_items[key] + getLanTrans(key), value: key.includes('zh') ? key : key.split('-')[0]})
  }
}

const getTaskTypeList = () => {
  apiGetTaskTypeList().then((res: any) => {
    console.log(res)
    if(res?.data?.supported_task_types?.length > 0) {
      popTaskTypeList.value = res.data.supported_task_types || [1,2,3,4]
    }
  })
}

const filteredTaskTypeList = computed(() => {
  return taskTypeList.filter(item => popTaskTypeList.value.includes(item.value))
})
const searchNameLoading = ref(false)
const getAdminUserList = (query: string) => {
  apiGetMemberList().then((res) => {
    adminUserList.value = res.data.list
  })
}

onMounted(() => {
  getAdminUserList()
  getTaskTypeList()
  getConfig()
  handleSearch()
  getResourceOssData().then(() => {
    console.log('ossData', ossDataBj.value)
  })
  envType.value = route.query.type as string
})
</script>

<style scoped>
  .search-section{
    background: #fff;
    margin: 10px 0;
    padding: 10px 10px 0 10px;
    border-radius: 4px;
  }
</style>
