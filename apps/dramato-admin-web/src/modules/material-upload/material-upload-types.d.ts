declare namespace M {
  interface QueryMaterialUploadListParams {
    title: string
    series_key: string
    material_name: string
    material_artist_id?: string[]
    display_my_assets?: number
    upload_time?: number // 用户选中的上传时间范围
    upload_done_time?: number // 用户选中的上传时间范围
    upload_status?: number
    page_info: {
      page_index: number
      page_size: number
    }
  }

  interface MaterialUploadItem {
    material_id: number
    series_id: number
    series_key: string
    title: string
    material_id: number
    material_name: string
    material_url: string
    requester_name: string
    requester_id: number
    ad_account: string
    material_artist_name: string
    material_artist_id: number
    upload_time: number
    upload_status: number
    compression_status: number
    created_at: number
    updated_at: number
  }

  interface UploadMaterialParams {
    series_key: string
    title: string
    material_id?: number
    material_name: string
  }

  interface UploadFileParams {
    material_id: number
    duration?: number
    file_path: string
    file_size?: number
    file_resolution?: string
  }
}
