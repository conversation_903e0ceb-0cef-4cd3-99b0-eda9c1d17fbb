import { mergeI18n } from 'src/h5_modules/i18n/i18n'

// 请优先使用 CT 中的翻译，尽量不要重复翻译相同的单词
export const T = mergeI18n({
  'en-US': {
    infinityScroll: {
      end: 'The End',
      loading: 'Loading...',
    },
  },
  'ja-JP': {
    infinityScroll: {
      end: 'The End',
      loading: '読み込み中...',
    },
  },
  'ko-KR': {
    infinityScroll: {
      end: 'The End',
      loading: '로드 중...',
    },
  },
  'es-ES': {
    infinityScroll: {
      end: 'Fin',
      loading: 'Cargando...',
    },
  },
  'pt-PT': {
    infinityScroll: {
      end: 'Fim',
      loading: 'Carregando...',
    },
  },
  'vi-VN': {
    infinityScroll: {
      end: 'Kết Thúc',
      loading: 'Đang tải...',
    },
  },
  'th-TH': {
    infinityScroll: {
      end: 'สิ้นสุด',
      loading: 'กำลังโหลด...',
    },
  },
  'id-ID': {
    infinityScroll: {
      end: 'Selesai',
      loading: 'Memuat...',
    },
  },
  'tl-PH': {
    infinityScroll: {
      end: 'The End',
      loading: 'Naglo-load...',
    },
  },
  'fr-FR': {
    infinityScroll: {
      end: 'La Fin',
      loading: 'Chargement...',
    },
  },
  'de-DE': {
    infinityScroll: {
      end: 'Das Ende',
      loading: 'Laden...',
    },
  },
  'it-IT': {
    infinityScroll: {
      end: 'Fine',
      loading: 'Caricamento...',
    },
  },
  'ru-RU': {
    infinityScroll: {
      end: 'Конец',
      loading: 'Загрузка...',
    },
  },
  'tr-TR': {
    infinityScroll: {
      end: 'Son',
      loading: 'Yükleniyor...',
    },
  },
  'ms-MY': {
    infinityScroll: {
      end: 'Akhirnya',
      loading: 'Memuatkan...',
    },
  },
  'zh-TW': {
    infinityScroll: {
      end: '無',
      loading: '加載中...',
    },
  },
  'zh-CN': {
    infinityScroll: {
      end: '无',
      loading: '加载中...',
    },
  },
})
