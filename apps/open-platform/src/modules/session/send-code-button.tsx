import { createComponent, fn, required } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { useInterval } from '@vueuse/core'
import { ElMessage } from 'element-plus'
import { keepError } from 'src/lib/http-client'
import { computed, ref, watch } from 'vue'
import { sendCodeInterval } from '../common/constants'
import { sessionApi } from './session-api'
import { useI18n } from 'vue-i18n'
type SendCodeButtonOptions = {
  props: {
    phone: string
  }
  emits: {
    success?: (code: string) => void
    error?: (error: Error) => void
  }
}
export const SendCodeButton = createComponent<SendCodeButtonOptions>({
  props: {
    phone: required,
  },
  emits: {
    success: fn,
    error: fn,
  },
}, (props, { emit }) => {
  const { t } = useI18n()
  const { counter, reset, pause, resume } = useInterval(1000, { controls: true, immediate: false })
  const finalCount = computed(() => {
    const n = sendCodeInterval - counter.value
    return n >= 0 ? n : 0
  })
  watch(() => finalCount.value, value => {
    if (value < 1) {
      pause()
      reset()
      disabled.value = false
    }
  })
  const disabled = ref(false)
  const onClickCode = async () => {
    resume()
    disabled.value = true
    ElMessage.success(t('verificationCodeSentToPhone'))
    const onError = (error: Error) => {
      pause()
      reset()
      emit('error', error)
    }
    const code = await sessionApi.sendCode(props.phone).catch(keepError(onError))
    if (code) emit('success', code)
  }
  return () => (
    <Button disabled={disabled.value} onClick={onClickCode}>{disabled.value ? `${t('retryInNSeconds', { n: finalCount.value })}` : t('sendVerificationCode')}</Button>
  )
})

export default SendCodeButton
