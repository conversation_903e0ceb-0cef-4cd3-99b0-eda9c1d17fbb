import { r } from '@skynet/shared'
import ShareLayout from 'src/layouts/share-layout'

export const regulationsRoutes = [r('regulations', '', ShareLayout, [
  r('', '', () => import('./regulations-page')),
  r('data-settings', '', () => import('./data-settings-page')),
  r('rules', '', () => import('./rules-page')),
  r('terms', '', () => import('./terms-page')),
  r('privacy', '', () => import('./privacy-page')),
])]
