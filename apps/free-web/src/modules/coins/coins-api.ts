import { httpClient } from 'src/lib/http-client'
import { MyEarnings } from './my-earnings/my-earnings'
import { ExchangeDetails, MyExchange } from './exchange/exchange'

export const apiGetMyEarnings = (data: {
  page: number
  page_size: number
  last_id?: number
}) => {
  return httpClient.get<ApiResponse<{
    gold_flow_list: MyEarnings[]
    last_id: number
    has_more: boolean
  }>>('/fr-api/welfare/gold-flow', data)
}

export const apiGetMyExchange = (data: {
  page: number
  page_size: number
  last_id?: number
}) => {
  return httpClient.get<ApiResponse<{
    list: MyExchange[]
    last_id: number
    has_more: boolean
  }>>('/fr-api/welfare/exchange/history_list', data)
}

export const apiGetCoins = () => {
  return httpClient.get<ApiResponse<{ coins: { amount: number } }>>('/fr-api/welfare/wallet')
}

export const apiGetBroadcast = () => {
  return httpClient.get<ApiResponse<{ list: string[][] }>>('/fr-api/welfare/exchange/broadcast/list')
}

export const apiGetExchangeDetails = () => {
  return httpClient.get<ApiResponse<ExchangeDetails>>('/fr-api/welfare/exchange/detail')
}

export const apiExchangeGift = (data: {
  id: string
  type: string
}) => {
  return httpClient.post<ApiResponse<{ gift_card_secret?: string }>>('/fr-api/welfare/exchange/gift', data)
}

export const apiRedeemGift = (data: {
  id: string
  type: string
  account: string
}) => {
  return httpClient.post<ApiResponse>('/fr-api/welfare/exchange/transfer', data)
}

export const apiGetLastRedeemAccount = () => {
  return httpClient.post<ApiResponse<{ account: string }>>('/fr-api/welfare/exchange/get_account')
}
