import * as CryptoJS from 'crypto-js'
import Cookies from 'js-cookie'
import { openedInDev } from '@skynet/shared'

export const get_authorization = () => {
  const params = JSON.parse(Cookies.get('params') ?? '{}')
  if (!params.appSecret || !params.secret || !params.key) return
  const rawSignature = `${params.appSecret}&${params.secret}`
  const signature = CryptoJS.MD5(rawSignature).toString()
  return `oauth_signature=${signature},oauth_token=${params.key},ts=${new Date().getTime()}`
}

export const get_authorization_for_browser = () => {
  const ts = Date.now()
  const auth_params = JSON.parse(Cookies.get('auth_params') ?? '{}')
  const APP_SECRET = '8IAcbWyCsVhYv82S2eofRqK1DF3nNDAv'

  // 计算 MD5
  const hash = CryptoJS.MD5(
    CryptoJS.enc.Utf8.parse(`${APP_SECRET}&${auth_params.auth_secret}`),
  )
  const signed = hash.toString(CryptoJS.enc.Hex)

  return `oauth_signature=${signed},oauth_token=${auth_params.auth_key},ts=${ts}`
}
