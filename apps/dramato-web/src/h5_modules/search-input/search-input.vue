<template>
  <div class="flex items-center rounded-lg bg-[#FDFBFC3B] p-2 py-[6.5px] text-[#FDFBFCB2]">
    <SvgIcon
      name="ic-search"
      class="mr-2 size-[1.35rem]"
    />
    <input
      class="unstyled-input text-sm"
      type="text"
      placeholder=""
      @click="() => $router.push('/search')"
    >
  </div>
</template>

<script setup lang="ts">
import { SvgIcon } from '@skynet/ui'
</script>

<style scoped>
.unstyled-input {
  all: unset;


}

.unstyled-input::placeholder {
  color: #FDFBFCB2;
}
</style>