import { httpClient } from 'src/lib/http-client'

export const apiListSeriesPackage = (params: Api.SeriesPackage.ListQuest.query) => {
  return httpClient.post<ApiResponse<Api.SeriesPackage.ListQuest.Response>>('/seriespackage/list', params)
}

export const apiDeleteSeriesPackage = (params: { id: number }) => {
  return httpClient.post<unknown>('/seriespackage/delete', params)
}

const switchSaveParams = (params: Partial<M.SeriesPackage.Details>) => {
  const package_type_1_config = {
    ios_series_keys: [],
    android_series_keys: [],
  }

  const package_type_2_config = {
    ios_url: '',
    android_url: '',
  }

  const package_type_3_config = {
    ios_list: [],
    android_list: [],
  }

  const package_type_4_config = {
    language_version_code: ['all'],
    listing_status: [],
    // episode_count_type: 0, // 剧集数量类型 0 全部 1 0-50 2 50-100 3 100+
    resource_type: 0, // 资源类型 1 本土 2 翻译
    free_status: 0, // 0 全部 1 免费 2 付费
    serialize_status: 0, // 连载状态 1 未完结 2 已完结
    release_round: 0, // 0 全部 1 首发 2 二轮
    episode_count_begin: '',
    episode_count_end: '',
    protect_begin: '',
    protect_end: '',
    amount_begin: '',
    amount_end: '',
    spend_begin: '',
    spend_end: '',
  }
  if (params.import_type === 1) {
    params.package_config = {
      ...params.package_config,
      package_type_2_config,
      package_type_3_config,
      package_type_4_config,
    }
  }
  if (params.import_type === 2) {
    params.package_config = {
      ...params.package_config,
      package_type_1_config,
      package_type_3_config,
      package_type_4_config,
    }
  }
  if (params.import_type === 3) {
    params.package_config = {
      ...params.package_config,
      package_type_1_config,
      package_type_2_config,
      package_type_4_config,
    }
  }
  if (params.import_type === 4) {
    params.package_config = {
      ...params.package_config,
      package_type_1_config,
      package_type_2_config,
      package_type_3_config,
    }
  }

  if (params.import_type === 5) {
    params.package_config = {
      package_type_5_config: params.package_config?.package_type_5_config,
    }
  }
  // TODO: switch params
  return params
}

export const apiCreateSeriesPackage = (params: Partial<M.SeriesPackage.Details>) => {
  return httpClient.post<ApiResponse<M.SeriesPackage.Details>>('/seriespackage/create', switchSaveParams(params))
}

export const apiEditSeriesPackage = (params: Partial<M.SeriesPackage.Details>) => {
  return httpClient.post<ApiResponse<M.SeriesPackage.Details>>('/seriespackage/update', switchSaveParams(params))
}

/** operation 状态 1 线上 3 未上架  */
export const apiUpdateSeriesPackageState = (params: { id: number, state: number }) => {
  return httpClient.post<unknown>('/seriespackage/set_state', params)
}

export const apiUpdateSeriesPackagePriority = (params: { id: number, priority: number }) => {
  return httpClient.post<unknown>('/seriespackage/set_priority', params)
}
