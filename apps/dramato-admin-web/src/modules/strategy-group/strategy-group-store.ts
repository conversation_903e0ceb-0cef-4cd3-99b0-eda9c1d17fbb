import { use<PERSON><PERSON><PERSON><PERSON> } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { ref } from 'vue'
import { z } from 'zod'
const Form = CreateForm<M.StrategyGroup>()
const defaultFormData = () => {
  return {
    name: '',
    id: '',
    is_abtest: 0,
    user_type: 1,
    user_platform: 0,
    platform: undefined,
    scene: ['purchase'],
    product_config: {
      ios_config: {
        pay_model: 'IAP',
        application: ['pay', 'store'],
        recharge_option: [],
        item_package_option: [],
        member_vip_option: [],
      },
      android_config: {
        pay_model: 'IAP',
        application: ['pay', 'store'],
        recharge_option: [],
        item_package_option: [],
        member_vip_option: [],
      },
    },
    task_config: {
      duration: -1,
    },
    drama_list: undefined,
    iap_strategys: undefined,
    send_pro_user: 1,
    iap_strategy_ids: undefined,
    user_config: {
      active_users_config: {
        latest_days: 7,
        start_days: '',
        end_days: '',
      },
      custom_users_config: {
      },
      af_ad_config: {
        adset_list: [{
          code: '',
          name: '',
          id: '',
        }],
      },
    },
    panel_type: 0,
    panel_config: {
      highlight_ad: false,
      hide_ad: false,
      hide_recharge: false,
      hide_membership: false,
      custom_panel_config: ['show_ad', 'show_recharge', 'show_membership'],
    },
  } as M.StrategyGroup
}
const formData = ref<M.StrategyGroup>(defaultFormData())
const activeUserType = ref<'latest_days' | 'start_and_end'>('latest_days')
const needShowSaveDragDialog = ref(true)

const resetFormData = () => {
  formData.value = defaultFormData()
  console.log('formData.value', formData.value)
}

const resetUserFormData = () => {
  const { name, user_type, user_platform, platform, user_config, task_config } = defaultFormData()
  formData.value = {
    ...formData.value,
    name,
    user_type,
    user_platform,
    user_config,
    platform,
    task_config,
  }
}

const resetProductFormData = (isIAA?: boolean) => {
  if (isIAA) {
    formData.value = {
      ...formData.value,
      drama_list: [],
      ad_id: undefined,
      unlock_nodes: [],
      ad_info: undefined,
    }
  } else {
    formData.value.product_config = defaultFormData().product_config
  }
}

const resetAssociateFormData = () => {
  formData.value = {
    ...formData.value,
    name: '',
    ad_id: undefined,
    iap_strategy_ids: [],
    ad_info: undefined,
    iap_strategys: [],
  }
}

const resetTaskFormData = (isIAA?: boolean) => {
  if (isIAA) {
    formData.value.task_config = undefined
  } else {
    formData.value.task_config = defaultFormData().task_config
  }
}

const stepOneRules = z.object({
  name: z.string().min(1),
  user_platform: z.number(),
  // user_type: z.number(),
  // strategy_layer_id: z.number(),
})

const stepOneRulesForIAA = z.object({
  name: z.string().min(1, '必填'),
  platform: z.string().min(1, '必填'),
  // user_type: z.number(),
  user_config: z.object({
    active_users_config: z.optional(z.object({
      latest_days: z.number().min(1).max(99),
      start_days: z.string().min(1),
      end_days: z.string().min(1),
    })),
    custom_users_config: z.record(z.string(), z.array(z.string())),
    af_ad_config: z.object({
      adset_list: z.array(z.object({
        id: z.string().min(1),
        code: z.string().min(1),
        name: z.string().min(1),
      })).min(1, { message: '必填' }),
    }),
  }),
})

const stepRulesForAssociate = z.object({
  name: z.string().min(1, '必填'),
  ad_id: z.number().min(1, '必填'),
  iap_strategy_ids: z.array(z.string()).min(1, '必填'),
  auto_up: z.number().min(-1).optional(),
  platform: z.string().min(1, '必填'),
})

const { error: stepOneError, validateAll: _validateStepOne } = useValidator(formData, stepOneRules)
const { error: stepOneErrorForIAA, validateAll: _validateStepOneForIAA } = useValidator(formData, stepOneRulesForIAA)
const { error: stepErrorForAssociate, validateAll: validateStepAssociate } = useValidator(formData, stepRulesForAssociate)

const validateStepOne = () => {
  const exclude = [
    // formData.value.user_type === 1 && ['user_config.custom_users_config', 'user_config.af_ad_config'],
    // formData.value.user_type === 2 && ['user_config.active_users_config', 'user_config.af_ad_config'],
    // formData.value.user_type === 3 && ['user_config.active_users_config', 'user_config.custom_users_config'],
    // activeUserType.value === 'latest_days' && ['user_config.active_users_config.start_days', 'user_config.active_users_config.end_days'],
    // activeUserType.value === 'start_and_end' && ['user_config.active_users_config.latest_days'],
    // formData.value.is_abtest === 1 && ['strategy_layer_id'],
  ].filter(Boolean).flat() as string[]
  const valid = _validateStepOne({ exclude })
  stepStatusList.value[0] = valid ? 'valid' : 'invalid'
  console.log('stepOneError', stepOneError.value)
  return valid
}

const validateStepOneForIAA = () => {
  const exclude = [
    !formData.value.user_type && ['user_config', 'user_config.active_users_config', 'user_config.af_ad_config'],
    formData.value.user_type === 1 && ['user_config.custom_users_config', 'user_config.af_ad_config'],
    formData.value.user_type === 2 && ['user_config.active_users_config', 'user_config.af_ad_config'],
    formData.value.user_type === 3 && ['user_config.active_users_config', 'user_config.custom_users_config'],
    activeUserType.value === 'latest_days' && ['user_config.active_users_config.start_days', 'user_config.active_users_config.end_days'],
    activeUserType.value === 'start_and_end' && ['user_config.active_users_config.latest_days'],
  ].filter(Boolean).flat() as string[]
  const valid = _validateStepOneForIAA({ exclude })
  stepStatusList.value[0] = valid ? 'valid' : 'invalid'
  console.log('stepOneError', stepOneErrorForIAA.value)
  return valid
}

const stepTwoRules = z.object({
  // panel_config: z.object({
  //   custom_panel_config: z.array(z.string()).min(1, { message: '必填' }),
  // }),
  product_config: z.object({
    ios_config: z.object({
      recharge_option: z.array(z.object({
        id: z.number(),
      })).min(1, { message: '必填' }),
      member_vip_option: z.array(z.object({
        id: z.number(),
      })).min(1, { message: '必填' }),
    }).optional(),
    android_config: z.object({
      recharge_option: z.array(z.object({
        id: z.number(),
      })).min(1, { message: '必填' }),
      member_vip_option: z.array(z.object({
        id: z.number(),
      })).min(1, { message: '必填' }),
    }).optional(),
  }),
})

const stepTwoRulesForIAA = z.object({
  ad_id: z.number().min(1, '必填'),
  unlock_nodes: z.array(z.any()).min(1, '必填'),
})

const { error: stepTwoError, validateAll: _validateStepTwo } = useValidator(formData, stepTwoRules)
const { error: stepTwoErrorForIAA, validateAll: _validateStepTwoForIAA } = useValidator(formData, stepTwoRulesForIAA)

const validateStepTwo = () => {
  const exclude = [
    formData.value.user_platform === 1 && 'product_config.android_config',
    formData.value.user_platform === 2 && 'product_config.ios_config',
  ].filter(Boolean).flat() as string[]
  const valid = _validateStepTwo({ exclude })
  stepStatusList.value[1] = valid ? 'valid' : 'invalid'
  return valid
}

const validateStepTwoForIAA = () => {
  const valid = _validateStepTwoForIAA()
  stepStatusList.value[1] = valid ? 'valid' : 'invalid'
  return valid
}

const stepThreeRules = z.object({
  task_config: z.object({
    priority: z.number().min(1),
    duration: z.number().min(-1),
    // auto_up: z.number().min(-1).optional(),
  }),
})

const stepThreeRulesForIAA = z.object({
  task_config: z.object({
    priority: z.number().min(1),
    duration: z.number().min(-1),
    // auto_up: z.number().min(-1).optional(),
  }),
})
const { error: stepThreeError, validateAll: _validateStepThree } = useValidator(formData, stepThreeRules)
const { error: stepThreeErrorForIAA, validateAll: _validateStepThreeForIAA } = useValidator(formData, stepThreeRulesForIAA)
const validateStepThree = () => {
  const valid = _validateStepThree()
  stepStatusList.value[2] = valid ? 'valid' : 'invalid'
  return valid
}
const validateStepThreeForIAA = () => {
  const valid = _validateStepThreeForIAA()
  stepStatusList.value[2] = valid ? 'valid' : 'invalid'
  return valid
}

const validateAllSteps = () => {
  return validateStepOne() && validateStepTwo() && validateStepThree()
}

const validateAllStepsForIAA = () => {
  return validateStepOneForIAA() && validateStepTwoForIAA() && validateStepThreeForIAA()
}

const validateStepOneForIAP = () => validateStepOne() && validateStepThree()

const stepStatusList = ref<Array<'invalid' | 'valid'>>(['invalid', 'invalid', 'invalid'])

const userProfileList = ref<Api.StrategyGroupUserProfile.Item[]>([])
const fetchingUserProfileList = ref(false)

const platformMap: Record<number, string> = {
  0: 'All',
  1: 'IOS',
  2: 'Android',
}
export const useStrategyGroupStore = () => {
  return {
    platformMap,
    formData,
    resetFormData,
    Form,
    stepStatusList,
    stepOneError,
    stepTwoError,
    stepOneErrorForIAA,
    stepTwoErrorForIAA,
    stepThreeErrorForIAA,
    stepErrorForAssociate,
    validateStepOneForIAA,
    validateStepTwoForIAA,
    validateStepThreeForIAA,
    validateStepAssociate,
    stepThreeError,
    validateStepOne,
    validateStepOneForIAP,
    validateStepTwo,
    validateStepThree,
    validateAllSteps,
    validateAllStepsForIAA,
    activeUserType,
    resetProductFormData,
    resetTaskFormData,
    resetUserFormData,
    resetAssociateFormData,
    needShowSaveDragDialog,
    userProfileList,
    fetchingUserProfileList,
  }
}
