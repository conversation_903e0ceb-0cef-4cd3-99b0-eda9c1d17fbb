import { createComponent, getQuery } from '@skynet/shared'
import { Button, Input, openDialog } from '@skynet/ui'
import { useLoginStore } from './use-login-store'
import { reactive, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElLoading } from 'element-plus'
import { useQRCode } from '@vueuse/integrations/useQRCode'
import registerImage from './image/regist-tip.png'
import forgetPassword from './image/forget-password.png'

export const LoginForm = createComponent(null, () => {
  const loginStore = useLoginStore()
  const { requestLogin, requestExternalLogin, requestSSOLogin, isValidUser, userInfo } = loginStore
  const router = useRouter()
  const returnQuery = getQuery('returnTo', '')
  const loginForm = reactive<loginForm>({
    username: '',
    password: '',
    usernameError: '',
    mfa: '',
    passwordError: '',
    mfaError: '',
    isThirdUser: localStorage.getItem('isThirdUser') === 'yes' ? 2 : 3,
  })
  // 监听 isThirdUser 的变化，存到 localStorage
  watch(() => loginForm.isThirdUser, value => {
    if (value === 2) {
      localStorage.setItem('isThirdUser', 'yes')
    } else {
      localStorage.setItem('isThirdUser', 'no')
    }
  })

  const tips = computed(() => {
    loginForm.passwordError = ''
    loginForm.usernameError = ''
    if (loginForm.isThirdUser === 1) {
      return {
        usernameLabel: 'ldap账号',
        passwordLabel: 'ldap密码',
        usernamePlaceholder: '请输入ldap账号, 如：san.zhang',
        passwordPlaceholder: '请输入ldap密码',
        usernameValidateMsg: 'ldap账号不能为空',
        passwordValidateMsg: 'ldap密码不能为空',
      }
    } else if (loginForm.isThirdUser === 2) {
      return {
        usernameLabel: '账号',
        passwordLabel: '密码',
        usernamePlaceholder: '请输入账号',
        passwordPlaceholder: '请输入密码',
        usernameValidateMsg: '账号不能为空',
        passwordValidateMsg: '密码不能为空',
        mfaLabel: 'MFA',
        mfaPlaceholder: '已扫码绑定账号，请输入MFA密码',
      }
    } else {
      return {
        usernameLabel: '账号',
        passwordLabel: '密码',
        usernamePlaceholder: '请输入账号',
        passwordPlaceholder: '请输入密码',
        usernameValidateMsg: '账号不能为空',
        passwordValidateMsg: '密码不能为空',
      }
    }
  })

  const validateUsername = () => {
    if (!loginForm.username) {
      // 提示手机号不能为空
      loginForm.usernameError = tips.value?.usernameValidateMsg
      return false
    }

    loginForm.usernameError = ''
    return true
  }

  const validatePassword = () => {
    if (!loginForm.password) {
      // 提示验证码不能为空
      loginForm.passwordError = tips.value?.passwordValidateMsg
      return false
    }

    loginForm.passwordError = ''
    return true
  }

  const showTipDialog = () => {
    // loginForm.isThirdUser 3 内部 2 外部
    const hideDeleteDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[450px]',
      body: (
        <>
          {
            loginForm.isThirdUser === 3 ? (
              <x-register-tip-confirm-dialog key="register" class="flex flex-col gap-y-[25px]">
                <x-register-tip-body class="block space-y-4">
                  <div>
                    昆仑员工如需访问短剧管理后台，请在“飞书—工作台—审批—短剧平台LDAP账号管理”提交申请
                  </div>
                  <img class="w-full" src={registerImage} alt="" />
                </x-register-tip-body>
                <x-register-tip-footer class="flex w-full justify-end gap-x-[10px]">
                  <Button
                    class="w-full h-[48px] rounded-[200px] border border-[var(--brand-6)] font-medium bg-[var(--brand-6)] text-[16px] text-[var(--text-6)] hover:bg-[var(--brand-5)] active:bg-[var(--brand-7)] active:outline-none"
                    onClick={() => {
                      hideDeleteDialog()
                      window.open('https://applink.feishu.cn/T8WjlfuiMzqr')
                    }}
                  >
                    发起申请
                  </Button>
                </x-register-tip-footer>
              </x-register-tip-confirm-dialog>
            ) : (
              <x-register-tip-confirm-dialog class="flex flex-col gap-y-[25px]">
                <x-register-tip-body class="block space-y-4">
                  <div>
                    外部小伙伴如需访问短剧管理后台，请联系对应的昆仑接口人，获取帐号密码，非常感谢！
                  </div>
                </x-register-tip-body>
                <x-register-tip-footer class="flex w-full justify-end gap-x-[10px]">
                  <Button
                    class="w-full h-[48px] rounded-[200px] border border-[var(--brand-6)] font-medium bg-[var(--brand-6)] text-[16px] text-[var(--text-6)] hover:bg-[var(--brand-5)] active:bg-[var(--brand-7)] active:outline-none"
                    onClick={() => {
                      hideDeleteDialog()
                    }}
                  >
                    OK
                  </Button>
                </x-register-tip-footer>
              </x-register-tip-confirm-dialog>
            )
          }
        </>
      ),
    })
  }

  const forgetPasswordDialog = () => {
    const hideDeleteDialog = openDialog({
      title: '提示',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[450px]',
      body: (
        <>
          {
            loginForm.isThirdUser === 3 ? (
              <x-register-tip-confirm-dialog key="register" class="flex flex-col gap-y-[25px]">
                <x-register-tip-body class="block space-y-4">
                  <div>
                    如忘记密码，请在“飞书—工作台—审批—短剧平台LDAP账号管理”提交申请，类型选择“密码重置”
                  </div>
                  <img class="w-full" src={forgetPassword} alt="" />
                </x-register-tip-body>
                <x-register-tip-footer class="flex w-full justify-end gap-x-[10px]">
                  <Button
                    class="w-full h-[48px] rounded-[200px] border border-[var(--brand-6)] font-medium bg-[var(--brand-6)] text-[16px] text-[var(--text-6)] hover:bg-[var(--brand-5)] active:bg-[var(--brand-7)] active:outline-none"
                    onClick={() => {
                      hideDeleteDialog()
                      window.open('https://applink.feishu.cn/T8WjlfuiMzqr')
                    }}
                  >
                    发起申请
                  </Button>
                </x-register-tip-footer>
              </x-register-tip-confirm-dialog>
            ) : (
              <x-register-tip-confirm-dialog class="flex flex-col gap-y-[25px]">
                <x-register-tip-body class="block space-y-4">
                  <div>
                    外部小伙伴如需访问短剧管理后台，请联系对应的昆仑接口人，获取帐号密码，非常感谢！
                  </div>
                </x-register-tip-body>
                <x-register-tip-footer class="flex w-full justify-end gap-x-[10px]">
                  <Button
                    class="w-full h-[48px] rounded-[200px] border border-[var(--brand-6)] font-medium bg-[var(--brand-6)] text-[16px] text-[var(--text-6)] hover:bg-[var(--brand-5)] active:bg-[var(--brand-7)] active:outline-none"
                    onClick={() => {
                      hideDeleteDialog()
                    }}
                  >
                    OK
                  </Button>
                </x-register-tip-footer>
              </x-register-tip-confirm-dialog>
            )
          }
        </>
      ),
    })
  }

  const handleLogin = async () => {
    if (!validateUsername()) {
      return
    }
    if (!validatePassword()) {
      return
    }
    const loading = ElLoading.service({
      lock: true,
      text: '请求中',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    // 外部用户登录
    if (loginForm.isThirdUser === 2) {
      const success = await requestExternalLogin(loginForm.username, loginForm.password, loginForm.mfa)
      loading.close()
      if (!success) return
      if (success && userInfo.value?.auth_url) {
        const qrcode = useQRCode(userInfo.value?.auth_url)
        openDialog({
          title: '用户绑定',
          customClass: '!w-[250px]',
          body: () => (
            <>
              <img src={qrcode.value} alt="" />
            </>
          ),
        })
      } else {
        if (returnQuery) {
          if (returnQuery.startsWith('http')) {
            location.href = returnQuery
          } else {
            void router.push(returnQuery)
          }
        } else {
          void router.push('/')
        }
      }
    } else {
      // 天工内部员工登录
      let loginFn = requestLogin
      if (loginForm.isThirdUser === 3) {
        loginFn = requestSSOLogin
      }
      const success = await loginFn(loginForm.username, loginForm.password)
      loading.close()
      if (success) {
        if (loginForm.isThirdUser === 3) {
          void router.push('/sso-redirect?returnTo=' + returnQuery)
        } else if (returnQuery) {
          if (returnQuery.startsWith('http')) {
            location.href = returnQuery
          } else {
            void router.push(returnQuery)
          }
        } else {
          void router.push('/')
        }
      }
    }
  }

  return () => (
    <div class="w-[500px] bg-[var(--fill-4)] rounded-lg mx-auto">
      <div key="form" class={!isValidUser.value && 'hidden'}>
        <div class="">
          <div class="text-2xl px-[32px]">
            <div>Hello !</div>
            <div>欢迎来到短剧管理后台 !</div>
          </div>
          <form class="px-[32px] leading-140% text-[14px]" onSubmit={e => e.preventDefault()}>
            <div class="mt-[12px]">
              <div class="py-[8px] text-[var(--text-2)]">用户类型：</div>
              <div class="flex items-center justify-between gap-x-2">
                <Input
                  type="radio"
                  class="tm-radio"
                  options={[
                    {
                      value: 3,
                      label: '昆仑员工',
                    },
                    {
                      value: 2,
                      label: '外部伙伴',
                    },
                  ]}
                  modelValue={loginForm.isThirdUser}
                  onUpdate:modelValue={value => loginForm.isThirdUser = value as number}
                />
              </div>
            </div>
            <div class="w-full mt-[16px] relative">
              <div class="py-[8px] text-[var(--text-2)]">{tips.value?.usernameLabel}</div>
              <input
                v-model={loginForm.username}
                class={`block border-box w-full h-[54px] px-[16px] font-medium text-[16px] text-[var(--text-1)] placeholder-[var(--text-3)] bg-[var(--fill-2)] border border-solid rounded-lg focus:outline-none ${loginForm.usernameError ? 'border-[var(--error-6)]' : 'border-[var(--fill-2)]'}`}
                placeholder={tips.value?.usernamePlaceholder}
                aria-label={tips.value?.usernamePlaceholder}
                onBlur={validateUsername}
              />
              {loginForm.usernameError && <p class="absolute top-[8px] right-0 text-[var(--error-6)]">{loginForm.usernameError}</p>}
            </div>
            <div class="w-full mt-[12px] relative">
              <div class="py-[8px] text-[var(--text-2)]">{tips.value?.passwordLabel}</div>
              <div class="flex items-center justify-between gap-x-2">
                <input
                  v-model={loginForm.password}
                  class={`block border-box w-full h-[54px] px-[16px] font-medium text-[16px] text-[var(--text-1)] placeholder-[var(--text-3)] bg-[var(--fill-2)] border border-solid rounded-lg focus:outline-none ${loginForm.passwordError ? 'border-[var(--error-6)]' : 'border-[var(--fill-2)]'}`}
                  placeholder={tips.value?.passwordPlaceholder}
                  aria-label={tips.value?.passwordPlaceholder}
                  type="password"
                  onBlur={validatePassword}
                />
              </div>
              {loginForm.passwordError && <p class="absolute top-[8px] right-0 text-[var(--error-6)]">{loginForm.passwordError}</p>}
            </div>
            {
              loginForm.isThirdUser === 2 ? (
                <div class="w-full mt-[12px] relative">
                  <div class="py-[8px] text-[var(--text-2)]">{tips.value?.mfaLabel}</div>
                  <div class="flex items-center justify-between gap-x-2">
                    <input
                      v-model={loginForm.mfa}
                      class="block border-box w-full h-[54px] px-[16px] font-medium text-[16px] text-[var(--text-1)] placeholder-[var(--text-3)] bg-[var(--fill-2)] border border-solid rounded-lg focus:outline-none border-[var(--fill-2)]}"
                      placeholder={tips.value?.mfaPlaceholder}
                      aria-label={tips.value?.mfaPlaceholder}
                      type="text"
                    />
                  </div>
                  {loginForm.mfaError && <p class="absolute top-[8px] right-0 text-[var(--error-6)]">{loginForm.mfaError}</p>}
                  {/* <div class="my-2 flex justify-end">
                    <a href="https://rg975ojk5z.feishu.cn/wiki/O8dqwSusTiagzHkXYBdcsIPTnlc" target="_blank" class="link link-primary">第三方登录说明</a>
                  </div> */}
                </div>
              ) : null
            }
            <div class="flex justify-end">
              <span class="py-4 cursor-pointer" onClick={forgetPasswordDialog}>忘记密码</span>
            </div>
            <Button
              class="mt-1 w-full h-[48px] rounded-[200px] border border-[var(--brand-6)] font-medium bg-[var(--brand-6)] text-[16px] text-[var(--text-6)] hover:bg-[var(--brand-5)] active:bg-[var(--brand-7)] active:outline-none"
              onClick={handleLogin}
            >
              登录
            </Button>
            <Button
              class="mt-[20px] btn w-full h-[48px] rounded-[200px] font-medium text-[16px]"
              onClick={showTipDialog}
            >
              申请账号
            </Button>
          </form>
        </div>
      </div>
    </div>
  )
})
export default LoginForm
