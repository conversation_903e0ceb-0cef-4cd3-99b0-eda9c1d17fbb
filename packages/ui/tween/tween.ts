import { Tween, Easing } from '@tweenjs/tween.js'
import { EasingFunction } from '@vueuse/core'
type Position = {
  x: number
  y: number
}
type Config = {
  from: Position
  to: Position
  duration?: number
  easing?: EasingFunction
  onUpdate?: (position: Position) => void
  onComplete?: (position: Position) => void
  autoPlay?: boolean
}
export function useTween(config: Config) {
  const { autoPlay = true } = config
  const tween = new Tween(config.from)
  tween.to(config.to, config.duration)
    .easing(config.easing || Easing.Linear.InOut)
    .onComplete(config.onComplete)
    .onUpdate(config.onUpdate)
    .start()

  function play() {
    function _animate() {
      if (!tween.isPlaying()) return
      window.requestAnimationFrame(_animate)
      tween.update()
    }
    _animate()
  }
  if (autoPlay) {
    play()
  }
  return {
    play,
    stop() {
      tween.stop()
    },
  }
}
