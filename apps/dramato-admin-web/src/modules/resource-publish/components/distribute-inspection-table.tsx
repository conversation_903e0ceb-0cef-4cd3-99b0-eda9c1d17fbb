/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { ElSelect, ElOption } from 'element-plus'
import { ref } from 'vue'
import { Button, Icon, showFailToast, showSuccessToast } from '@skynet/ui'
import { apiDistributeTask } from '../resource-publish-api'

type DistributeInspectionTableOptions = {
  props: {
    resourceIds: number[]
    type: 1 | 2 | 3 | 4 | number
    users: string[]
  }
  emits: {
    cancel: () => void
    select: () => void
  }
}
export const DistributeInspectionTable = createComponent<DistributeInspectionTableOptions>({
  props: {
    resourceIds: [],
    type: -1,
    users: [],
  },
  emits: {
    cancel: () => {},
    select: () => {},
  },
}, (props, { emit }) => {
  const selected = ref<string>('')
  const btnLoading = ref(false)

  return () => (
    <x-distribute-table class="block">
      <ElSelect
        class="w-full"
        modelValue={selected.value}
        onUpdate:modelValue={(e: string) => {
          selected.value = e
        }}
        placeholder="请选择人员"
        clearable
      >
        {props.users.map(user => <ElOption value={user} label={user}>{user}</ElOption>)}
      </ElSelect>
      <x-distribute-table-footer class="mt-4 flex w-full justify-end gap-x-[10px]">
        <Button class="btn btn-ghost btn-sm" onClick={() => emit('cancel')}>取消</Button>
        <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
          if (!selected.value) {
            showFailToast('请选择人员')
            return
          }
          try {
            btnLoading.value = true
            await apiDistributeTask({
              distribution_infos: [
                {
                  claim_name: selected.value,
                  resource_ids: props.resourceIds,
                  lang_code: ''
                }
              ],
              type: props.type,
            })
            showSuccessToast('操作成功')
            emit('select')
          } catch (error: any) {
            showFailToast(error.response.data.err_msg || '操作失败')
          } finally {
            btnLoading.value = false
          }
        }}
        >
          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
          确定
        </Button>
      </x-distribute-table-footer>

    </x-distribute-table>
  )
})

export default DistributeInspectionTable
