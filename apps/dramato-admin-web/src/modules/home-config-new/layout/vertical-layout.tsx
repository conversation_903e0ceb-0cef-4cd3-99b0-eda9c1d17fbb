import { createComponent } from '@skynet/shared'
import play from '../image/button.png'
import free from '../image/free.png'
type VerticalLayoutOptions = {
  props: {
    title: string
    list: M.DramaItem[]
    show_title?: boolean
  }
}
export const VerticalLayout = createComponent<VerticalLayoutOptions>({
  props: {
    title: '',
    list: [],
    show_title: true,
  },
}, props => {
  const dramaItem = (drama: M.DramaItem) => {
    return (
      <div class="flex flex-row gap-x-3 w-full relative z-1">
        {drama.free && <img src={free} class="absolute right-[calc(50%+4px)] top-1 w-40px h-auto object-cover shadow-sm" />}
        <img class="object-cover rounded-md w-1/2" src={drama.cover} />
        <div class="flex flex-col gap-y-3 py-3 overflow-hidden px-2">
          <div class="text-[#cccacb] text-md font-['SF Pro'] line-clamp-2">{drama.title}</div>
          <div class="text-[#797b7d] text-sm font-normal font-['SF Pro'] line-clamp-3">{drama.desc}</div>
          <div class="flex flex-wrap gap-1 flex-row mt-3 flex-1">
            {
              drama.series_tag.map(tag => <div class="h-[18px] px-2 py-0.5 bg-[#242526] rounded justify-center items-center gap-2.5 inline-flex text-[#6e7071] text-xs font-normal">{tag}</div>)
            }
          </div>
          <img src={play} class="w-full h-auto object-cover" />
        </div>
      </div>
    )
  }
  return () => (
    props.list && (
      <x-column-vertical class="w-full flex flex-col gap-2 overflow-x-auto">
        {props.show_title && <x-column-vertical-title class="w-full h-[50px] pt-4 pb-3 text-[#fdfbfc] text-lg font-bold font-['SF Pro'] leading-snug">{props.title}</x-column-vertical-title>}
        <x-column-vertical-content class="flex flex-col gap-2 w-full items-center justify-around">
          {
            props.list.map(item => {
              return (
                <x-column-vertical-drama class="shrink-0 w-full relative">
                  <div class="z-0 left-0 top-0 absolute size-full">
                    <img src={item?.cover} class="z-0 object-cover size-full relative rounded-md" />
                    <div class="absolute z-1 size-full left-0 top-0 backdrop-blur-[5px] bg-gradient-to-b from-[#0B080B1A] to-[#0b080b] rounded-md" />
                  </div>
                  {dramaItem(item)}
                </x-column-vertical-drama>
              )
            })
          }
        </x-column-vertical-content>
      </x-column-vertical>
    )
  )
})
