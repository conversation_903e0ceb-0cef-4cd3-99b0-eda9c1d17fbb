import { bindLoading, createComponent, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { arrow, coins, coinsSuccess, exchangeCoins, ovoCard, realCoins, vipDialog, vipTag } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { ref, watch } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track, trackNative } from 'src/lib/track'
import { openDialog } from 'src/modules/common/dialog'
import { useCoinsStore } from '../../coins-store'
import { apiExchangeCoins, apiRedeemGift } from '../../coins-api'
import { useRedeem } from '../use-redeem'
import { showToast } from 'src/modules/common/toast/toast'
import { jsBridge } from 'src/lib/jsbridge'
type GiftOptions = {
  props: {
    gift?: GiftType & {
      status?: number
      welfare_key?: string
    }
  }
}

let closeConfirmDialog: Fn

export const CoinsGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      tags: [], // 礼品标签
      type: 'coins',
      status: 1,
      welfare_key: '',
    },
  },
}, props => {
  const { t } = useLocale()
  const { getExchangeDetails } = useCoinsStore()
  // const tipsRef = ref<HTMLDivElement>()
  // const redeemLoading = ref(false)

  // const { reachLimit } = useRedeem(props.gift)

  // const openConfirmDialog = () => { // 确认兑换弹窗
  //   track('redeemconfirm', 'popup', 'show', {
  //     brand: props.gift?.type,
  //   })
  //   closeConfirmDialog = openDialog({
  //     customClass: 'bg-transparent shadow-none gap-y-0',
  //     mainClass: 'rounded-sm',
  //     title: () => <></>,
  //     closeVisible: true,
  //     body: () => (
  //       <x-exchange-body class="relative mb-4 flex flex-col rounded-xl bg-cover bg-no-repeat">
  //         <img src={vipDialog} class="absolute inset-0 h-auto w-full" />
  //         <x-body-main class="z-up-up mt-109px relative flex w-full flex-col rounded-b-xl bg-white px-4 py-5">
  //           <x-title class="line-clamp-2 w-full text-center text-lg font-bold text-[#0B080B]">Confirm Exchange?</x-title>
  //           <x-content class="my-5 flex h-7 w-full items-center justify-center gap-x-0.5">
  //             <img src={coins} class="size-7 object-cover" />
  //             <span class="text-2xl font-bold leading-7 text-[#FC2763]">-30000</span>
  //             <img src={arrow} class="h-3.5 w-5 object-cover" />
  //             <img src={realCoins} class="mb-[3px] size-7 object-cover" />
  //           </x-content>
  //           <x-tips class="text-center text-xs text-[#A1A0A3]">X Diamonds will be deducted,The VIP bonus does not include cash withdrawal privileges</x-tips>
  //           <Button class="mt-2 h-11 w-full rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={redeemHandler}>Confirm Exchange</Button>
  //         </x-body-main>
  //       </x-exchange-body>
  //     ),
  //   })
  // }

  // const goToWatchReels = () => {
  //   window.location.href = props.gift?.cash_success_btn_link ?? ''
  // }

  // const openRedeemSuccessDialog = () => { // 提现成功
  //   const closeRedeemSuccessDialog = openDialog({
  //     customClass: 'bg-transparent shadow-none gap-y-0',
  //     title: () => <></>,
  //     closeVisible: true,
  //     body: () => (
  //       <x-exchange-body class="relative mb-4 flex flex-col rounded-xl bg-cover bg-no-repeat">
  //         <img src={coinsSuccess} class="absolute inset-0 h-auto w-full" />
  //         <x-body-main class="z-up-up mt-109px relative flex w-full flex-col rounded-b-xl bg-white px-4 py-5">
  //           <x-title class="line-clamp-2 w-full text-center text-lg font-bold leading-snug text-[#0B080B]">Received X Coins</x-title>
  //           <x-content class="mt-5 flex h-7 w-full items-center justify-center gap-x-0.5 text-xs text-[#A1A0A3]">Valid for 7 days</x-content>
  //           <Button class="mt-2 h-11 w-full rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={goToWatchReels}>{props.gift?.cash_success_btn_txt}</Button>
  //           <Button class="mt-3 w-full border-none bg-transparent text-base text-[#A1A0A3]" onClick={() => {
  //             closeConfirmDialog?.()
  //             closeRedeemSuccessDialog?.()
  //           }}
  //           >Later
  //           </Button>
  //         </x-body-main>
  //       </x-exchange-body>
  //     ),
  //   })
  // }

  // const redeemHandler = () => {
  //   if (redeemLoading.value) return
  //   track('redeemconfirm', 'popup', 'click', {
  //     brand: props.gift?.type,
  //   })
  //   void bindLoading(apiExchangeCoins({
  //     type: props.gift?.type,
  //     check_enough: true,
  //   }), redeemLoading).then(res => {
  //     if (res.code === 200) {
  //       closeConfirmDialog?.()
  //       openRedeemSuccessDialog()
  //     }
  //   }).catch((e: { response: { data: { code: number } } }) => {
  //     closeConfirmDialog?.()
  //     if (e.response.data.code === 1040) {
  //       showToast('Daily Exchange Limit 200 Coins')
  //       return
  //     }
  //     showToast('Redemption failed. Please try again later.')
  //   })
  // }

  const handleClick = () => {
    if (props.gift?.status === 1 || props.gift?.status === undefined) {
      if (props.gift.user_coins >= props.gift.gift_coins) {
        void jsBridge('showExchangeConfirmDialog', {
          welfare_key: props.gift?.welfare_key,
          diamonds_count: props.gift?.gift_coins,
        }).then(res => {
          if (res === 'success') {
            void getExchangeDetails()
          }
        })
      } else {
        // earn more
        location.href = 'dramawave://dramawave.app/rewards_task?enter_from=exchange_page'
      }
    } else if (props.gift?.status === 2) {
      // daily exchange limit
      showToast('Batas Penukaran Harian 200 Koin')
    }
  }

  return () => (
    <x-gift-item class="flex gap-x-3 p-3">
      <x-gift-item-left class={mc('flex max-w-[44px] flex-col items-center justify-center gap-1', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-card class="box-border size-11 rounded-lg border border-solid border-[#ece9ec] px-1 py-3">
          <img src={exchangeCoins} class="h-auto w-full object-cover" />
        </x-card>
        <x-price class="block w-full break-all text-center text-xs font-bold text-[#4d4e4f]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class={mc('flex flex-1 flex-col gap-1 truncate', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-gift-title class="w-full truncate text-sm text-[#4d4e4f]">{props.gift.name}</x-gift-title>
        <x-gift-coins class="flex items-center text-sm">
          <img src={coins} width={16} height={16} class="mr-[2px] size-4 object-contain" />
          <x-gift-amount class="text-[#fc2763]">{props.gift.user_coins}</x-gift-amount>
          <x-separator class="px-[2px] text-[#a1a2a3]">/</x-separator>
          <x-gift-remains class="text-[#090609]">{props.gift.gift_coins}</x-gift-remains>
        </x-gift-coins>
        <x-gift-progress>
          <Progress class="mt-1 bg-[#ECE9EC]" value={props.gift.ratio} max={1} />
        </x-gift-progress>
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21 justify-center', props.gift?.button_tips ? 'gap-y-1' : '')}>
        <Button class={mc('min-h-8 w-full rounded-lg border-none px-1 pt-[2px] pb-[3px]  text-sm', !props.gift?.can_exchange ? 'bg-[#FFA0B6] !text-[#FDFBFC80]' : 'bg-[#fc2763] !text-white')} onClick={handleClick}>
          <p class="line-clamp-2 h-auto w-full text-wrap break-words text-center leading-4">
            { props.gift?.button_txt }
          </p>
        </Button>
        {/* {props.gift?.button_tips && <x-tips class="line-clamp-2 h-auto w-full break-words text-center text-[10px] leading-3 text-[#4d4e4f]" ref={tipsRef}>{props.gift?.button_tips}</x-tips>} */}
      </x-gift-item-right>
    </x-gift-item>
  )
})
