import { bindLoading, createComponent, getQ<PERSON>y, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { coins, amazonCard, amazonDialog, copy as copyIcon, dialogBg, successDialog } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { Fn, useClipboard } from '@vueuse/core'
import { showToast } from 'src/modules/common/toast/toast'
import { computed, onMounted, ref, watch, watchEffect } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track, trackNative } from 'src/lib/track'
import { openDialog } from 'src/modules/common/dialog'
import { apiExchangeGift } from '../../coins-api'
import { useCoinsStore } from '../../coins-store'
type GiftOptions = {
  props: {
    gift?: GiftType
  }
}
export const AmazonGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      type: 'amazon',
    },
  },
}, props => {
  const { copy, copied } = useClipboard()
  const { t } = useLocale(getQuery('language', 'en'))
  const tipsRef = ref<HTMLDivElement>()
  let closeExchangeDialog: Fn
  const { getExchangeDetails } = useCoinsStore()
  const exchangeLoading = ref(false)

  watch(() => copied.value, () => {
    if (copied.value) {
      showToast(t('coins.exchange.copySuccess'))
    }
  })

  const reachLimit = computed(() => props.gift?.user_coins >= props.gift?.gift_coins)

  const commonTitle = () => (
    <x-title class="h-21 relative block w-full">
      <img src={amazonDialog} class="h-21 absolute left-1/2 top-1/2 w-auto -translate-x-1/2 object-cover" />
    </x-title>
  )

  watchEffect(() => {
    if (!reachLimit.value) {
      track('exchangezone', 'earncoins_button', 'show', {
        gift_id: props.gift?.id,
      })
    }
  })

  const openExchangeFailByInsufficientStock = () => {
    closeExchangeDialog?.()
    track('exchangefail', 'popup', 'show', {
      brand: props.gift?.type,
      scene: 'stock',
    })
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex flex-col bg-contain bg-no-repeat pb-6 pt-16">
          <x-body-main class="mx-4 flex flex-col gap-y-0">
            <x-title class="truncate text-center text-lg font-bold text-[#090609]">{t('coins.exchange.exchangeFailed')}</x-title>
            <x-content class="mt-3 w-full text-center text-[#090609]">
              {t('coins.exchange.transferFailedInsufficientStock')}
            </x-content>
            <Button class="mx-3 mt-5 h-11 w-[calc(100%-1.5rem)] rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={() => {
              closeDialog()
              track('exchangefail', 'popup', 'click', {
                brand: props.gift?.type,
                scene: 'stock',
              })
            }}>{t('coins.exchange.gotItBtn')}
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openExchangeFailByRiskControl = () => {
    closeExchangeDialog?.()
    track('exchangefail', 'popup', 'show', {
      brand: props.gift?.type,
      scene: 'risk',
    })
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex min-h-[260px] flex-col bg-no-repeat pt-16">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="truncate text-center text-lg font-bold text-[#090609]">{t('coins.exchange.exchangeFailed')}</x-title>
            <x-content class="mb-5 mt-3 w-full text-center text-[#090609]">
              {t('coins.exchange.transferFailedRiskControl')}
            </x-content>
            <Button class="mx-3 mb-6 h-11 w-[calc(100%-1.5rem)] rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={() => {
              closeDialog()
              track('exchangefail', 'popup', 'click', {
                brand: props.gift?.type,
                scene: 'risk',
              })
            }}
            >{t('coins.exchange.tryAgainBtn')}
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openExchangeSuccessDialog = (giftCardSecret: string) => {
    track('exchangesuccess', 'popup', 'show', {
      brand: props.gift?.type,
    })
    closeExchangeDialog?.()
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: () => (
        <x-title class="relative block h-[94px] w-full">
          <img src={successDialog} width={94} height={94} class="absolute left-1/2 top-1/2 h-full w-auto -translate-x-1/2 object-cover" />
        </x-title>
      ),
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 mt-2 flex flex-col bg-no-repeat pb-6 pt-16">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="truncate text-center text-lg font-bold text-[#090609]">{t('coins.exchange.exchangeSuccess')}</x-title>
            <x-explain class="mb-5 mt-3 flex flex-col items-center text-center">
              <x-price class="block max-w-[calc(100%-12px)] truncate text-sm font-medium text-[#4d4e4f]">{t('coins.exchange.cardId')}:</x-price>
              <x-description class="flex w-full items-center justify-center text-wrap text-sm font-medium text-[#4d4e4f]">
                <span>{giftCardSecret}</span>
                <img src={copyIcon} class="no-tap-color ml-1 size-4 cursor-pointer" onClick={() => copy(giftCardSecret)} />
              </x-description>
            </x-explain>
            <Button class="mx-3 h-11 w-[calc(100%-1.5rem)] rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={() => {
              void getExchangeDetails()
              closeDialog()
              track('exchangesuccess', 'popup', 'click', {
                brand: props.gift?.type,
              })
            }}
            >{t('coins.exchange.receiveBtn')}
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const handleClick = () => {
    if (props.gift?.can_exchange) {
      if (reachLimit.value) {
        track('exchangezone', 'exchange_button', 'click', {
          brand: props.gift?.type,
        })
        openExchangeDialog()
      } else {
        void trackNative('exchangezone', 'earncoins_button', 'click')
        window.location.href = 'dramawave://dramawave.app/rewards_task?enter_from=exchange_page'
      }
    }
  }

  const exchange = () => {
    if (exchangeLoading.value) return
    track('exchangeconfirm', 'popup', 'click', {
      brand: props.gift?.type,
    })
    void bindLoading(apiExchangeGift({
      id: props.gift?.id,
      type: props.gift?.type,
    }), exchangeLoading).then(res => {
      if (res.data?.gift_card_secret) {
        openExchangeSuccessDialog(res.data.gift_card_secret)
      }
    }).catch((e: { response?: { data?: { code?: number } } }) => {
      if (e?.response?.data?.code === 60011) {
        openExchangeFailByInsufficientStock()
      } else if (e?.response?.data?.code === 60012) {
        openExchangeFailByRiskControl()
      }
    })
  }

  const flag = ref(false)
  watch(() => [tipsRef.value, flag.value], () => {
    flag.value = false
    if (tipsRef.value) {
      const lineHeight = parseFloat(getComputedStyle(tipsRef.value).lineHeight)
      const fontSize = parseFloat(getComputedStyle(tipsRef.value).fontSize)
      if (fontSize <= 8) {
        return
      }
      const maxHeight = lineHeight * 2
      if (tipsRef.value.scrollHeight > maxHeight) {
        tipsRef.value.style.fontSize = `${fontSize - 2 <= 8 ? 8 : fontSize - 2}px`
        flag.value = true
      }
    }
  })

  onMounted(() => {
    if (props.gift?.can_exchange && reachLimit.value) {
      track('exchangezone', 'exchange_button', 'show', {
        brand: props.gift?.type,
      })
    }
  })

  const openExchangeDialog = () => {
    track('exchangeconfirm', 'popup', 'show', {
      brand: props.gift?.type,
    })
    closeExchangeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex min-h-[260px] flex-col bg-no-repeat pt-16">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="truncate text-center text-lg font-bold text-[#090609]">Amazon</x-title>
            <x-explain class="mt-3 flex flex-col items-center text-center">
              <x-price class="mx-[6px] block max-w-[calc(100%-12px)] truncate text-[32px] font-bold text-[#fc2763]">{props.gift?.amount}</x-price>
              <x-description class="line-clamp-2 w-full  text-wrap text-sm font-medium text-[#4d4e4f]">{props.gift.name}</x-description>
            </x-explain>
            <x-content class="mb-5 mt-3 line-clamp-2 w-full text-center text-[#090609]">
              {t('coins.exchange.redeemRequiredXCoins', { X: props.gift?.gift_coins })}
            </x-content>
            <Button class="mx-3 mb-6 h-11 w-[calc(100%-1.5rem)] rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={exchange}>{t('coins.earnings.exchange')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  return () => (
    <x-gift-item class="flex gap-x-3 p-3">
      <x-gift-item-left class={mc('flex max-w-[44px] flex-col gap-1', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-card class="box-border size-11 rounded-lg border border-solid border-[#ece9ec] px-1 py-3">
          <img src={props?.gift?.image_url ?? amazonCard} class="h-auto w-full object-cover" />
        </x-card>
        <x-price class="block w-full break-all text-center text-xs font-bold text-[#4d4e4f]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class={mc('flex flex-1 flex-col gap-1 truncate', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-gift-title class="w-full truncate text-sm text-[#4d4e4f]">{props.gift.name}</x-gift-title>
        {
          props.gift?.can_exchange && (
            <x-gift-coins class="flex items-center text-sm">
              <img src={coins} width={16} height={16} class="mr-[2px] size-4 object-contain" />
              <x-gift-amount class="text-[#fc2763]">{props.gift.user_coins}</x-gift-amount>
              <x-separator class="px-[2px] text-[#a1a2a3]">/</x-separator>
              <x-gift-remains class="text-[#090609]">{props.gift.gift_coins}</x-gift-remains>
            </x-gift-coins>
          )
        }
        {
          props.gift?.can_exchange && (
            <x-gift-progress>
              <Progress class="mt-1 bg-[#ECE9EC]" value={props.gift.ratio} max={1} />
            </x-gift-progress>
          )
        }
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21 justify-center', props.gift?.button_tips ? 'gap-y-1' : '')}>
        <Button class={mc('min-h-8 w-full rounded-lg border-none px-1 pt-[2px] pb-[3px]  text-sm', !props.gift?.can_exchange ? 'bg-[#FFA0B6] !text-[#FDFBFC80]' : 'bg-[#fc2763] !text-white')} onClick={handleClick}>
          <p class="line-clamp-2 h-auto w-full text-wrap break-words text-center leading-4">
            {reachLimit.value ? t('coins.earnings.exchange') : t('coins.exchange.earnCoins')}
          </p>
        </Button>
        {props.gift?.button_tips && <x-tips class={mc('line-clamp-2 h-auto w-full break-words text-center text-[10px] leading-3 text-[#4d4e4f]', props.gift?.can_exchange ? '' : 'opacity-30')} ref={tipsRef}>{props.gift?.button_tips}</x-tips>}
      </x-gift-item-right>
    </x-gift-item>
  )
})
