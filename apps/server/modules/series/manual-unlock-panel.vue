<template>
  <van-action-sheet
    v-model:show="show"
    class="unlock-custom-popup text-[#888] z-dialog"
    title=""
  >
    <div class="custom-title flex items-center justify-between p-3.5 pl-4">
      <div class="text-xs flex  text-[#cccacb]">
        <p class="mr-3">
          {{ T.productPanel.thisEpisode.ref.value }} <span
            class="text-xm bg-left-center bg-[url('assets/images/coins-dark.webp')] bg-[length:0.9rem] bg-no-repeat  pl-4"
          >{{
            episode }}</span>
        </p>
        <p>
          {{ T.productPanel.balance.ref.value }} <span
            class="ext-xm bg-left-center bg-[url('assets/images/coins-dark.webp')] bg-[length:0.9rem] bg-no-repeat  pl-4"
          >{{
            myWallet }}</span>
        </p>
      </div>
      <SvgIcon
        name="ic_close_dark"
        class="size-4.5 shrink-0"
        @click="() => {
          show = false;
        }"
      />
    </div>
    <div class="content p-3 pb-15 flex flex-col items-center justify-center">
      <div
        class="bg-[#fc2763] mb-5 w-full text-center  text-white py-3 rounded-lg text-base"
        @click="unlockClick"
      >
        {{ T.productPanel.playThisEpisode.ref.value }}
      </div>
      <p
        class="text-[#797b7d] text-sm flex items-center"
        @click="() => {
          const val = !checked;
          checked = val
        }"
      >
        <span
          v-if="!checked"
          class="dynamic-circle mr-2"
        />
        <span
          v-else
          class="checked-dynamic-circle dynamic-circle mr-2"
        />
        {{ T.productPanel.autoplayNextVideo.ref.value }}
      </p>
    </div>
  </van-action-sheet>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ActionSheet as vanActionSheet, Loading as vanLoading } from 'vant';
import { SvgIcon } from '@skynet/ui'
import { useMyWallet } from '../wallet/use-my-wallet'
// import { apiWalletMy } from 'src/h5_modules/profile/profile-api'
import { T } from './product-panel.i18n'
const { wallet } = useMyWallet()

const checked = ref(false);

const props = defineProps({
  unlockPanelVisible: {
    type: Boolean,
    required: false
  },
  episode: {
    type: Number,
    required: true
  }
});

const myWallet = ref(0);
const emit = defineEmits(['update:unlockPanelVisible', 'unlockClick']);

const unlockClick = () => {
  emit('unlockClick', checked.value)
}

const show = ref(props.unlockPanelVisible);

watch(() => props.unlockPanelVisible, newVal => {
  show.value = newVal
  if (newVal) {
    getWalletMy();
  }
});

const getWalletMy = async () => {
  myWallet.value = wallet.value?.cash_balance + wallet.value?.bonus_balance
}

watch(show, newVal => {
  emit('update:unlockPanelVisible', newVal);
});
</script>

<style>
.unlock-custom-popup {
  background: #000 url(/assets/images/goods-bg.webp) no-repeat center top/ 100% 83px;
}
</style>

<style scoped>
.dynamic-circle {
  display: block;
  width: 16px;
  height: 16px;
  border: 2px solid #797b7d;
  border-radius: 50%;
  background: transparent;
}
.checked-dynamic-circle {
  border-color: #fc2763;
  border-width: 4px;
}
</style>
