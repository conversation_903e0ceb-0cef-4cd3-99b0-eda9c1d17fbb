import { Button, CreateForm, CreateTable, DateTime, Money, openDialog, Pager, showAlert, transformInteger } from '@skynet/ui'
import { set } from 'lodash-es'
import { httpClient, keepError } from 'src/lib/http-client'
import { usePaged } from 'src/lib/use-paged'
import { ref } from 'vue'
import AddAvailableInventory from './add-available-inventory'
import { prizeTypeMap } from './const'
import PrizeForm from './prize-form'

const { items: prizeList, loading: fetchingPrizeList, fetch: fetchPrizeList, page, pageSize, total, query: tableQuery }
= usePaged<M.Prize>({
  initialPage: 1,
  initialPageSize: 20,
  fetch: ({ page, page_size, ...query }) => {
    return httpClient.post<ApiPagedResponse<M.Prize>>('/draw/prize/list', { offset: (page - 1) * page_size, size: page_size, ...query })
  },
})

type Query = {
  prize_name?: string
  prize_level?: number
  id?: M.Prize['id']
}
const TableForm = CreateForm<Query>()
const Table = CreateTable<M.Prize>()

const editionFormData = ref<Partial<M.Prize>>(generateDefaultPrize())

export function usePrizeList() {
  return {
    prizeList, fetchPrizeList, fetchingPrizeList, renderTableSearch, renderTable, renderPager, generateDefaultPrize,
    generateDefaultTableQuery, editionFormData, tableQuery, resetTableQuery, addAvailableInventory,
    findPrize,
  }
}

const getRoundNumberMap = (n: number) => {
  if (n === 0) return '不限轮次'
  return `第 ${n} 轮`
}

function onCreate() {
  const close = openDialog({
    title: '新增奖品',
    body: () => <PrizeForm onAfterSave={() => { close(); void fetchPrizeList() }} />,
    onClickOutside() { close() },
  })
}
function renderTableSearch() {
  return (
    <TableForm
      onChange={(path, value) => set(tableQuery.value, path, value)}
      submitText="搜索"
      onReset={() => {
        tableQuery.value = generateDefaultTableQuery()
        void fetchPrizeList({ page: 1 })
      }}
      onSubmit={() => fetchPrizeList()} data={tableQuery.value} items={[
        ['奖品ID', 'id', { type: 'number' }, { transform: transformInteger }],
        ['奖品名称', 'prize_name', { type: 'text' }],
        ['奖品等级', 'prize_level', { type: 'number' }, { transform: transformInteger }],
      ]}
    />
  )
}
function renderTable() {
  return (
    <Table
      class="w-full [&_x-cell.odd]:bg-gray-100"
      list={prizeList.value}
      loading={fetchingPrizeList.value}
      actions={() => (
        <>
          <Button class="btn btn-sm btn-primary" onClick={onCreate}>新增奖品</Button>
        </>
      )}
      columns={[
        ['ID', 'id'],
        ['奖品名称', 'prize_name', { width: 'auto' }],
        ['奖品图片', row => <img src={row.prize_image} />, { width: '200px' }],
        ['上架状态', row => (
          <select value={row.status} onChange={e =>
            onUpdateStatus(row.id, Number((e.target as HTMLSelectElement).value))}>
            <option value={1}>已上架</option>
            <option value={0}>未上架</option>
          </select>
        )],
        ['供应状态', row => (
          <select value={row.supply_status} onChange={e =>
            onUpdateSupplyStatus(row.id, Number((e.target as HTMLSelectElement).value))}>
            <option value={1}>供应中</option>
            <option value={0}>未开始供应</option>
          </select>
        )],
        ['奖品等级', 'prize_level'],
        ['奖品价值', row => <Money v-model={row.price} />],
        ['奖品类型', row => prizeTypeMap[row.prize_type]],
        ['奖品数量', 'prize_num'],
        ['轮次', row => getRoundNumberMap(row.round_number)],
        ['权重', 'weight'],
        ['每日库存', 'inventory'],
        ['当日可用库存', 'available_inventory'],
        ['库存预警值', 'warning_threshold'],
        ['无限供应', row => row.is_infinite ? '是' : '否'],
        ['更新时间', row => <DateTime value={row.updated * 1000} />],
        ['操作人', 'operator'],
        ['操作', row => (
          <x-actions class="flex gap-2">
            <Button class="btn btn-xs btn-neutral"onClick={() => onEdit(row.id)}>编辑</Button>
            {row.is_infinite ? null
              : <Button class="btn btn-xs btn-primary" onClick={() => onAddAvailableInventory(row.id)}>增加临时库存</Button>}
          </x-actions>
        ), { class: 'sticky right-0 z-up bg-white shadow' }],
      ]}

    />
  )
}

function renderPager() {
  if (total.value <= 0) return null
  return (
    <Pager class="justify-end" page={page.value} size={pageSize.value} total={total.value}
      onUpdate:page={page => fetchPrizeList({ page })}
      onUpdate:size={page_size => fetchPrizeList({ page_size })}
    />
  )
}

function generateDefaultTableQuery(): Query {
  return {
    prize_name: undefined,
    prize_level: undefined,
  }
}
function generateDefaultPrize() {
  return {
    round_number: 0,
    prize_name: '',
    prize_image: '',
    prize_level: 0,
    price: 0,
    prize_type: 0,
    prize_num: 0,
    weight: 0,
    inventory: 0,
    available_inventory: 0,
    warning_threshold: 0,
    is_infinite: 0,
    status: 0,
    supply_status: 0,
    updated: 0,
    operator: '',
  }
}

function onAddAvailableInventory(prizeId: M.Prize['id']) {
  const close = openDialog({
    title: '增加临时库存',
    // 再 beforeSave 的时候就可以关闭对话框了，因为乐观更新
    body: () => <AddAvailableInventory prizeId={prizeId} onBeforeSave={close} />,
    onClickOutside: () => close(),
  })
}

function onEdit(prizeId: M.Prize['id']) {
  const close = openDialog({
    title: '编辑奖品',
    body: () => <PrizeForm prizeId={prizeId} onAfterSave={close} />,
    onClickOutside: () => close(),
  })
}

function findPrize(prizeId?: M.Prize['id']) {
  if (!prizeId) return null
  return prizeList.value.find(p => p.id === prizeId)
}

async function addAvailableInventory(id: M.Prize['id'], available_inventory: number) {
  const prize = findPrize(id)
  const old_available_inventory = prize?.available_inventory || 0
  if (prize) prize.available_inventory += available_inventory

  showAlert('临时库存已更新', 'success')
  await httpClient.post<ApiResponse>('/draw/prize/inventory/add', { id, inventory: available_inventory })
    .catch(keepError(() => {
      if (prize) { prize.available_inventory = old_available_inventory }
      showAlert('临时库存更新失败，请稍后再试', 'error')
    }))
}

function resetTableQuery() {
  tableQuery.value = generateDefaultTableQuery()
}

async function onUpdateStatus(prizeId: M.Prize['id'], status: number) {
  const prize = findPrize(prizeId)
  if (!prize) return
  const oldStatus = prize.status
  prize.status = status
  await httpClient.post<ApiResponse>('/draw/prize/status/update', {
    id: prizeId, operate_type: 1, status,
  })
    .catch(keepError(() => {
      prize.status = oldStatus
      showAlert('更新失败，请稍后再试', 'error')
    }))
  showAlert('更新成功', 'success')
}

async function onUpdateSupplyStatus(prizeId: M.Prize['id'], supplyStatus: number) {
  const prize = findPrize(prizeId)
  if (!prize) return
  const oldSupplyStatus = prize.supply_status
  prize.supply_status = supplyStatus
  await httpClient.post<ApiResponse>('/draw/prize/status/update', {
    id: prizeId, operate_type: 2, supply_status: supplyStatus,
  })
    .catch(keepError(() => {
      prize.supply_status = oldSupplyStatus
      showAlert('更新失败，请稍后再试', 'error')
    }))
  showAlert('更新成功', 'success')
}
