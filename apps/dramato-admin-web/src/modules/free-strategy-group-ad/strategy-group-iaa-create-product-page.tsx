import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, CreateTableOld, DateTime, openDialog, showAlert, TableColumnOld, transformNumber } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { z } from 'zod'
import { AdvertisePage } from '../advertise/advertise-page'
import { ShortDrama } from '../short-drama/short-drama-page'
import { apiCheckStrategyGroup } from './strategy-group-ad-api'
import { useStrategyGroupAdStore } from './strategy-group-ad-store'

export const StrategyGroupIaaCreateProductPage = createComponent(null, props => {
  const { formData, Form, validateStepTwo, stepTwoError, resetProductFormData } = useStrategyGroupAdStore()

  const router = useRouter()
  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')

  const AdvertiseTable = CreateTableOld<M.Advertise>()
  const NodeTable = CreateTableOld<M.NSStrategyGroup.UnlockNode>()
  const DramaTable = CreateTableOld<M.ApplicationDramaItem>()
  const checkedEpisodeItem = ref<M.ApplicationDramaItem[]>([])
  const checkedAdvertiseItem = ref<M.Advertise[]>([])
  const currentAdvertiseList = ref<M.Advertise[]>([])
  const disableEpisodes = ref<string[]>([])

  const nodeFormRules = z.object({
    unlock_start_episode: z.number().min(1).refine(val => {
      const endEpisode = nodeFormData.value.unlock_end_episode
      return !endEpisode || endEpisode >= val
    }, { message: '开始集数必须小于等于结束集数' }),
    need_watch_num: z.number().min(1),
    // zod定义规则：unlock_end_episode必须大于等于unlock_start_episode (z.ref报错)
    unlock_end_episode: z.number().max(999, '最大999').refine(val => {
      const startEpisode = nodeFormData.value.unlock_start_episode
      return !startEpisode || val >= startEpisode
    }, { message: '结束集数必须大于等于开始集数' }),
  })
  const NodeForm = CreateForm<M.NSStrategyGroup.UnlockNode>()
  const nodeFormData = ref<M.NSStrategyGroup.UnlockNode>({
    unlock_start_episode: undefined,
    unlock_end_episode: undefined,
    need_watch_num: 1,
  })

  const { error, validateAll } = useValidator(nodeFormData, nodeFormRules)

  const periodMap = {
    daily: '每天',
    twelve_hours: '每12小时',
    six_hours: '每6小时',
    three_hours: '每3小时',
    hourly: '每小时',
  }

  const advertiseColumns: TableColumnOld<M.Advertise>[] = [
    ['广告ID', 'id'],
    ['广告位名称', 'name'],
    ['广告组', 'ad_unit', { class: 'w-300px' }],
    ['广告位类型', 'ad_type'],
    ['Coins价格', 'coins'],
    ['Mediation竞价', 'mediation'],
    ['频次', row => {
      return (
        <x-frequency class="flex flex-col">
          <span>{row.count_limit}次/{periodMap[row.period as keyof typeof periodMap] ?? '--'}</span>
          <span class="text-gray-500 text-sm">per user</span>
        </x-frequency>
      )
    }, { class: 'w-[100px]' }],
    ['操作', row => {
      return (
        <x-hide-when-in-dialog class="flex items-center justify-center">
          <Button class="btn btn-outline btn-xs" disabled={isViewMode.value} onClick={() => deleteAdvertise(row)}>删除</Button>
        </x-hide-when-in-dialog>
      )
    }, { class: 'w-[80px] text-center' }],
  ]

  const dramaColumns: TableColumnOld<M.ApplicationDramaItem>[] = [
    ['剧集ID', 'series_key', { class: 'w-[120px]' }],
    ['剧名', 'title', { class: 'w-[320px]' }],
    ['上架时间', row => <DateTime value={row.listing_time * 1000} format="YYYY-MM-DD HH:mm:ss" />],
    ['总集数', 'episodes_number'],
    ['单集指导价', 'episodes_price'],
    [
      <span class="px-3">操作</span>,
      (row, idx) => (
        <div class="flex flex-nowrap">
          <Button class="btn btn-outline btn-xs"
            disabled={isViewMode.value}
            onClick={() => {
              (formData.value.drama_list || []).splice(idx, 1)
            }}
          >
            删除
          </Button>
        </div>
      ),
      { class: 'w-[80px]' },
    ],
  ]

  const nodeColumns: TableColumnOld<M.NSStrategyGroup.UnlockNode>[] = [
    ['解锁起始集', 'unlock_start_episode', { class: 'w-[120px]' }],
    ['解锁跨度', row => (
      <span>{row.unlock_start_episode} - {row.unlock_end_episode === 999 ? '全部' : row.unlock_end_episode}</span>
    ), { class: 'w-[200px]' }],
    ['需消费广告次数', 'need_watch_num', { class: 'w-[200px]' }],
    ['操作', (row, i) => {
      return (
        <x-hide-when-in-dialog class="flex items-center justify-center space-x-2">
          <Button class="btn btn-outline btn-xs" disabled={isViewMode.value} onClick={() => showNodeDialog(row, i)}>编辑</Button>
          <Button class="btn btn-outline btn-xs" disabled={isViewMode.value} onClick={() => deleteNode(row)}>删除</Button>
        </x-hide-when-in-dialog>
      )
    }, { class: 'w-[80px] text-center' }],
  ]

  const deleteNode = (node: M.NSStrategyGroup.UnlockNode) => {
    if (!formData.value.unlock_nodes) return
    formData.value.unlock_nodes.splice(formData.value.unlock_nodes.indexOf(node), 1)
  }

  const deleteAdvertise = (row: M.Advertise) => {
    checkedAdvertiseItem.value = checkedAdvertiseItem.value.filter(item => item.id !== row.id)
    currentAdvertiseList.value = currentAdvertiseList.value.filter(item => item.id !== row.id)
    formData.value.ad_id = undefined
    formData.value.ad_info = undefined
  }

  const checkSeriesAndStrategy = async () => {
    if (!checkedEpisodeItem.value || checkedEpisodeItem.value.length === 0) return true
    const res = await apiCheckStrategyGroup({
      platform: formData.value.platform!,
      series_keys: checkedEpisodeItem.value.map(item => item.series_key),
      strategy_id: route.query.id as string ? Number(route.query.id) : 0,
    })
    if (!res.data) return false
    if (res.data.series_keys && res.data.series_keys.length > 0) {
      if (disableEpisodes.value.length === 0) {
        disableEpisodes.value.push(...res.data.series_keys)
      } else {
        res.data.series_keys.forEach(item => {
          if (!disableEpisodes.value.includes(item)) {
            disableEpisodes.value.push(item)
          }
        })
      }
      showAlert('以下短剧已存在策略组：' + res.data.series_keys.join(','), 'error')
      return false
    }
    return true
  }

  const checkNodes = (index?: number) => {
    if (!formData.value.unlock_nodes || formData.value.unlock_nodes.length === 0) {
      if (formData.value.ad_info?.count_limit && nodeFormData.value.need_watch_num! > formData.value.ad_info?.count_limit) {
        showAlert('超过ad频次上限', 'error')
        return false
      }
      return true
    }
    const count = formData.value.unlock_nodes.reduce((cur, next, _i) => {
      if (index === _i) return cur
      return cur += next.need_watch_num!
    }, 0)
    if (formData.value.ad_info?.count_limit && (count + nodeFormData.value.need_watch_num!) > formData.value.ad_info.count_limit) {
      showAlert('超过ad频次上限', 'error')
      return false
    }
    // 判断nodeFormData.value的区间和formData.value.unlock_nodes的每一项的区间是否交叉，如果有则返回false，
    let _index = 0
    for (const node of formData.value.unlock_nodes) {
      if (index !== undefined && _index === index) {
        _index++
        continue
      }
      if (nodeFormData.value.unlock_start_episode! <= node.unlock_end_episode! && nodeFormData.value.unlock_end_episode! >= node.unlock_start_episode!) {
        showAlert('解锁区间不能交叉', 'error')
        return false
      }
      _index++
    }
    return true
  }

  const showImportAdvertiseDialog = () => {
    if (formData.value.ad_info) {
      checkedAdvertiseItem.value = [formData.value.ad_info]
    }
    const hide = openDialog({
      title: '导入广告',
      customClass: '!w-2/3',
      mainClass: '[&_.hide-when-in-dialog]:hidden [&_x-hide-when-in-dialog]:hidden [&_x-show-when-in-dialog]:!block pb-0 px-5',
      body: computed(() => (
        <x-import-advertise class="relative">
          <AdvertisePage
            checkedItem={checkedAdvertiseItem.value}
            onAdd={advertise => {
              checkedAdvertiseItem.value = [advertise]
            }}
            onRemove={advertise => {
              const idx = checkedAdvertiseItem.value.findIndex(item => item.id === advertise.id)
              if (idx !== -1) {
                checkedAdvertiseItem.value.splice(idx, 1)
              }
            }}
          />
          <footer class="w-full sticky bottom-0 left-0 flex justify-end gap-x-2 bg-white border-gray-200 border-t pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              currentAdvertiseList.value = checkedAdvertiseItem.value
              formData.value.ad_id = checkedAdvertiseItem.value?.[0]?.id ?? undefined
              formData.value.ad_info = checkedAdvertiseItem.value?.[0]
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-advertise>
      )),
    })
  }

  const showNodeDialog = (node?: M.NSStrategyGroup.UnlockNode, index?: number) => {
    nodeFormData.value = { ...node }
    const hide = openDialog({
      title: node ? '编辑节点' : '添加节点',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-node-form-dialog>
          <x-tips class="block text-sm text-gray-500 mb-5">添加解锁节点，跨度不可间隔跳跃</x-tips>
          <NodeForm
            error={error.value}
            hasAction={false}
            data={nodeFormData.value}
            onChange={(path, value) => {
              set(nodeFormData.value, path, value)
            }}
            items={[
              [requiredLabel('节点起始'), 'unlock_start_episode', {
                type: 'number',
              }, {
                transform: transformNumber,
              }],
              [requiredLabel('节点终点'), 'unlock_end_episode', {
                type: 'number',
              }, {
                transform: transformNumber,
                hint: (
                  <x-tips class="mt-2 text-gray-500 text-sm flex flex-col gap-y-1">
                    只解锁1集请输入相同集；解锁剩余全部，请输入999
                  </x-tips>
                ),
              }],
              [requiredLabel('需消费广告次数'), 'need_watch_num', {
                type: 'number',
              }, {
                transform: transformNumber,
                hint: (
                  <x-tips class="mt-2 text-gray-500 text-sm flex flex-col gap-y-1">
                    不可超过频次上限
                  </x-tips>
                ),
              }],
            ]}
          />
          <footer class="w-full sticky bottom-0 left-0 flex justify-end gap-x-2 bg-white border-gray-200 border-t pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => {
              hide()
            }}
            >取消
            </Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (validateAll()) {
                if (checkNodes(index)) {
                  if (!formData.value.unlock_nodes) {
                    formData.value.unlock_nodes = []
                  }
                  if (index !== undefined) {
                    formData.value.unlock_nodes[index] = { ...nodeFormData.value }
                  } else {
                    formData.value.unlock_nodes.push({ ...nodeFormData.value })
                  }
                  formData.value.unlock_nodes = formData.value.unlock_nodes.sort((a, b) => a.unlock_start_episode! - b.unlock_end_episode!)
                  hide()
                }
              }
            }}
            >提交
            </Button>
          </footer>
        </x-node-form-dialog>
      ),
    })
  }

  const showImportDramaDialog = () => {
    checkedEpisodeItem.value = [...formData.value.drama_list ?? []]
    const closeDialog = openDialog({
      title: '导入指定生效短剧',
      mainClass: 'flex flex-col flex-auto pb-0 h-[80vh] overflow-hidden px-4 [&_.hide-when-in-dialog]:hidden',
      body: () => (
        <x-import-recharge-level class="flex-1 flex flex-col overflow-hidden gxp-y-4">
          <x-episode-list class="flex-1  overflow-y-auto">
            <ShortDrama
              hasNav={false}
              hasActions={false}
              checkedItems={checkedEpisodeItem.value}
              onAdd={item => {
                checkedEpisodeItem.value?.push({ ...item, drama_id: item.drama_id })
              }}
              onRemove={item => {
                checkedEpisodeItem.value = checkedEpisodeItem.value?.filter(i => i.drama_id !== item.drama_id)
              }}
            />
          </x-episode-list>
          <footer class="w-full flex justify-end gap-x-2 pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => closeDialog()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (!checkSeriesAndStrategy()) return
              formData.value.drama_list = [
                ...(checkedEpisodeItem.value.map(item => ({
                  ...item,
                  cover: item.cover_url,
                })) || []),
              ]
              closeDialog()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-[80%] overflow-hidden',
    })
  }

  const currentNodeList = computed(() => {
    if (formData.value.unlock_nodes) {
      return formData.value.unlock_nodes
    }
    return []
  })

  const onSubmit = () => {
    const valid = validateStepTwo()
    if (valid) {
      void router.push({ path: `/free-ad-strategy-group/iaa/create/plan`, query: { ...route.query, id: formData.value.id } })
    }
  }

  watch(() => formData.value.ad_info, () => {
    currentAdvertiseList.value = []
    if (formData.value.ad_info) {
      currentAdvertiseList.value = [formData.value.ad_info]
    }
  }, {
    immediate: true,
  })

  return () => (
    <div class="flex flex-col gap-4 p-8">
      <Form
        class="flex flex-col gap-4"
        data={formData.value}
        error={stepTwoError.value}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        onReset={() => {
          resetProductFormData(true)
        }}
        onSubmit={onSubmit}
        items={[
          [requiredLabel('应用在'), 'scene', {
            type: 'checkbox-group',
            minLength: 1,
            options: [
              { label: '支付解锁', value: 'purchase', disabled: isViewMode.value },
            ],
          }, { class: 'col-span-2', errorVisible: false }],
          [requiredLabel('广告方案'), 'ad_id', {
            type: 'custom',
            render: ({ item, value }) => {
              return (
                <section class="bg-gray-50 p-4 rounded-lg">
                  <x-table-actions class="flex items-center justify-between">
                    <div class="space-x-2">
                      <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportAdvertiseDialog()}>导入广告</Button>
                      <span class="text-sm text-gray-500">添加1个广告</span>
                    </div>
                  </x-table-actions>
                  <hr class="my-4" />
                  <AdvertiseTable
                    list={currentAdvertiseList.value}
                    columns={advertiseColumns}
                    class="table table-zebra"
                  />
                </section>
              )
            },
          }],
          [requiredLabel('解锁节点'), 'unlock_nodes', {
            type: 'custom',
            render: ({ item, value }) => {
              return (
                <section class="bg-gray-50 p-4 rounded-lg">
                  <x-table-actions class="flex items-center justify-between">
                    <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showNodeDialog()}>新增</Button>
                  </x-table-actions>
                  <hr class="my-4" />
                  <NodeTable
                    list={currentNodeList.value}
                    columns={nodeColumns}
                  />
                </section>
              )
            },
          }],
          [requiredLabel('指定生效短剧'), 'drama_list', {
            type: 'custom',
            render: ({ item, value }) => {
              return (
                <section class="bg-gray-50 p-4 rounded-lg">
                  <x-table-actions class="flex items-center justify-between">
                    <Button class="btn btn-primary btn-sm" disabled={isViewMode.value} onClick={() => showImportDramaDialog()}>导入短剧</Button>
                  </x-table-actions>
                  <hr class="my-4" />
                  <DramaTable
                    list={formData.value.drama_list ?? []}
                    columns={dramaColumns}
                  />
                </section>
              )
            },
          }],
        ]}
        actions={() => (
          <div class="flex justify-between items-center gap-x-4">
            <Button class="btn btn-sm" type="button" onClick={() => {
              void router.push({ path: `/free-ad-strategy-group/iaa/create/target`, query: { ...route.query } })
            }}
            >上一步
            </Button>
            {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button> }
            <Button class="btn btn-primary btn-sm" type="submit">下一步</Button>
          </div>
        )}
      />

    </div>
  )
})
export default StrategyGroupIaaCreateProductPage
