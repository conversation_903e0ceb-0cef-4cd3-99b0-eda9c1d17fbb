import { createComponent, getQuery, mc } from '@skynet/shared'
import { Back } from 'src/modules/common/back/back'
import coinsIcon from '../images/coin.webp'
import ticketIcon from '../images/ticket.webp'
import noData from '../images/no-data.svg'
import { computed, onMounted, ref, watch, watchEffect } from 'vue'
import { useSwipe } from '@vueuse/core'
import { useMyWalletStore } from '../use-my-wallet-store'
import dayjs from 'dayjs'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { useStretchScroll } from 'src/lib/use-stretch-scroll'
type MyEarningPagesOptions = {
  props: {}
}
export const PurchaseHistoryPage = createComponent<MyEarningPagesOptions>({
  props: {},
}, props => {
  const wrapper = ref<HTMLDivElement>()
  const loadMore = ref<HTMLDivElement>()
  const language = getQuery('language', 'en')
  const { t } = useLocale(language)
  const { purchaseHistory, getPurchaseHistoryList, gettingPurchaseHistory } = useMyWalletStore()

  const { isSwiping, direction, lengthY } = useSwipe(wrapper)

  const delta = computed(() => {
    if (isSwiping.value === false || direction.value !== 'up' || lengthY.value === 0 || gettingPurchaseHistory.value) return
    if (!loadMore.value) return Infinity
    // 如果 loadMore 在 viewport 下方，距离 viewport 还有 200px，就加载更多
    const rect = loadMore.value.getBoundingClientRect()
    const clientHeight = document.documentElement.clientHeight
    return rect.top - clientHeight
  })

  watch(() => wrapper.value, () => {
    if (wrapper.value) {
      useStretchScroll(wrapper.value)
    }
  })

  onMounted(() => {
    track('purchase_history', 'page', 'show')
    document.title = t('my_wallet_in.transaction_history')
    void getPurchaseHistoryList({ next: purchaseHistory.value.page_info.next })
  })

  watchEffect(() => {
    if (typeof delta.value === 'undefined') return
    if (delta.value < 200 && purchaseHistory.value.page_info.has_more) {
      void getPurchaseHistoryList({ next: purchaseHistory.value.page_info.next })
    }
  })

  return () => (
    <x-my-earning-page class="relative flex flex-col bg-[#fff] text-[var(--text-5)] overflow-hidden size-full">
      <Back title={t('my_wallet_in.transaction_history')} isWhite={false} class="bg-[#fff] text-[#090609] shrink-0" />
      <x-history class="relative z-up flex flex-col flex-1 bg-[#fff] rounded-t-lg overflow-hidden">
        <x-history-area class="flex flex-col flex-1 w-full overflow-hidden">
          {
            purchaseHistory.value?.items?.length > 0
              ? (
                  <x-history-list class="flex flex-col text-[var(--text-1)] overflow-hidden size-full pt-2">
                    <x-scroll-wrapper class="block share-bottom-5" style="-webkit-overflow-scrolling: touch;" ref={wrapper}>
                      {
                        purchaseHistory.value.items.map(earning => (
                          <x-history-item class="flex justify-between items-center gap-1 p-3">
                            <div class="flex flex-col  max-w-[32ex] flex-1 gap-y-2 text-sm">
                              <div class="line-clamp-2 text-base text-[var(--text-1)]">{earning.title}</div>
                              <div class="text-[var(--text-3)] text-xs truncate">{dayjs.unix(earning.pay_finish_time).format('YYYY-MM-DD')}</div>
                            </div>
                            <div class="flex flex-col flex-2 max-w-[20ex] gap-y-2 text-sm items-end">
                              <div class="flex items-center"><span class="mr-1 line-clamp-2 text-sm">{earning.flow_type == 1 ? '+' : '-'}{earning.amount}</span><img src={ticketIcon} class="w-4 h-4" /></div>
                              <div class="max-w-[20ex] text-[var(--black)] flex items-center">

                                {['coin_exchange', 'coin_exchange_reward'].includes(earning.txn_type) && <div class="max-w-[16ex] text-[var(--black)] flex items-center"><span class="mr-1 text-[var(--text-3)] text-xs line-clamp-2">{earning.coins}</span> <img src={coinsIcon} class="w-4 h-4" /></div>}
                                {['purchase'].includes(earning.txn_type)
                                && <span class="mr-1 text-[var(--text-3)] text-xs line-clamp-2">{earning.txn_currency}{earning.txn_amount}</span>}
                                {['purchase_reward'].includes(earning.txn_type)
                                && <span class="mr-1 text-[var(--text-3)] text-xs line-clamp-2">{t('my_wallet_in.Expires_on_num', { num: dayjs.unix(earning.bonus_expire_time).format('YYYY-MM-DD') })}</span>}
                                {['ad_reward'].includes(earning.txn_type) && <span class="mr-1 text-[var(--text-3)] text-xs line-clamp-2">{t('my_wallet_in.AD')}</span>}
                                {['expire'].includes(earning.txn_type) && <span class="mr-1 text-[var(--text-3)] text-xs line-clamp-2">{t('my_wallet_in.Expired')}</span>}
                              </div>
                            </div>
                          </x-history-item>
                        ))
                      }
                      <x-load-more ref={loadMore} class="block w-full h-[1px] invisible" />
                      {
                        !purchaseHistory.value.page_info.has_more
                        && (
                          <x-limit-explain class="block pt-5 pb-3 w-full text-[var(--text-3)] text-center text-sm">
                            {t('my_wallet_in.Thats_all_for_now')}
                          </x-limit-explain>
                        )
                      }
                    </x-scroll-wrapper>
                  </x-history-list>
                )
              : (
                  <x-empty class="relative flex-1 w-full">
                    <div class="top-20 left-1/2 absolute flex flex-col justify-center -translate-x-1/2">
                      <img src={noData} class="w-40 h-40" />
                      <div class="text-[var(--text-4)] text-center">{t('coins.earnings.noRecords')}</div>
                    </div>
                  </x-empty>
                )

          }
        </x-history-area>
      </x-history>
    </x-my-earning-page>
  )
})

export default PurchaseHistoryPage
