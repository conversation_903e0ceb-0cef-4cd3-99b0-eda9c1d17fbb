import { createComponent } from '@skynet/shared'
import InputArea from './input-area'
import SearchHistory from './search-history'
import { TrendingSearches } from './trending-searches'
import { onMounted } from 'vue'
import { h5Track } from 'src/lib/h5-track'

export const SearchPage = createComponent(null, () => {
  onMounted(() => {
    h5Track('search', 'page', 'show')
  })
  return () => (
    <x-search-page class="h-100vh flex w-full flex-col overflow-hidden bg-[#0B080B]">
      <InputArea />
      <div class="flex w-full flex-1 flex-col overflow-y-auto">
        <SearchHistory />
        <TrendingSearches />
      </div>
    </x-search-page>
  )
})

export default SearchPage
