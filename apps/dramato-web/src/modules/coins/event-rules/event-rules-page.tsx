import { createComponent, getQuery } from '@skynet/shared'
import { MdPreview } from 'md-editor-v3'
import './custom-markdown.css'
import { html } from './resource/privacy.md'
export const EventRulesPage = createComponent(null, () => {
  return () => (
    <x-event-rules-page class="event-rules-page">
      <MdPreview
        no-img-zoom-in={true}
        model-value={html}
        class="px-3 pt-4"
      />
    </x-event-rules-page>
  )
})

export default EventRulesPage
