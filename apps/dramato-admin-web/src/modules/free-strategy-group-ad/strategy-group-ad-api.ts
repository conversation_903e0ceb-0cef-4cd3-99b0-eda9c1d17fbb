import { httpClient } from 'src/lib/http-client'
import { transformRequestData } from '../custom-users-config/custom-users-config'

export const apiListStrategyGroupAds = (params: Api.AdStrategyGroup.Request.List) => {
  return httpClient.post<Api.AdStrategyGroup.Response.List>('/ads/strategy-group/free/list', params)
}

// save_type  1 未上架，2 草稿
export const apiSaveStrategyGroupAds = (params: Partial<M.StrategyGroup> & { save_type: 1 | 2 }) => {
  if (params.id === '') {
    delete params.id
  }
  return httpClient.post<ApiResponse<M.StrategyGroup>>('/ads/strategy-group/free/save', params, {
    transformRequestData: {
      'user_config.custom_users_config': [transformRequestData],
    },
  })
}

export const apiGetStrategyGroupAdsDetails = (params: { id: string }) => {
  return httpClient.get<ApiResponse<M.StrategyGroup>>('/ads/strategy-group/free/detail', params)
}

/** operation 1 上架 2 下架 3 冻结 4 解除冻结  */
export const apiUpdateStrategyGroupAdsState = (params: { id: string, operation: number }) => {
  return httpClient.post<unknown>('/ads/strategy-group/free/status/update', params)
}

export const apiCheckStrategyGroup = (params: { platform: string, series_keys: string[], strategy_id: number }) => {
  return httpClient.post<ApiResponse<{
    series_keys: string[]
  }>>('/ads/strategy-group/free/check', params)
}

export const apiGetFreeAds = (scene_type: string, platform: string) => httpClient.post<ApiResponse<{
  list: Api.OriginAD.Item[]
}>>('/ads/free/list', {
  page_info: {
    offset: 0,
    size: 1000,
  },
  scene_type,
  platform,
})

export const apiSaveFreeStrategyGroupAds = apiSaveStrategyGroupAds
