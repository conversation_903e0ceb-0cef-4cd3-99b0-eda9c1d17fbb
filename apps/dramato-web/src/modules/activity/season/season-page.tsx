import { createComponent, getQuery } from '@skynet/shared'
import { track } from 'src/lib/track'
import { BackButton } from 'src/modules/common/button/back-button'
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ActivityBlock } from '../activity-block'
import { useActivityStore } from '../use-activity-store'
import { images } from './images'
import { isIos } from 'src/lib/ua'

const imageToButtonMap: Record<number, number | null> = {
  0: null,
  1: 0,
  2: null,
}

export const SeasonPage = createComponent(null, () => {
  const { config, fetchConfig } = useActivityStore()
  const route = useRoute()
  const language = getQuery('language', 'en')
  const eventId = getQuery('event_id', 0)
  const pictures = images[language][isIos() ? 'ios' : 'android']

  onMounted(() => {
    if (!eventId) return
    void fetchConfig(eventId)
  })

  onMounted(() => {
    track('activity_H5', 'page', 'show', {
      activity_id: route.query.event_id,
      user_id: route.query.user_id,
      from: route.query.from,
    })
  })
  return () => (
    <x-activity-page class="relative block bg-[#0c0a0d]">
      <BackButton class="z-up absolute left-0 top-[max(env(safe-area-inset-top),_44px)]" />
      <x-block-list class="flex flex-col gap-0">
        {pictures.map((i, index) => (
          <ActivityBlock key={index} type="image" src={i} button={config.value[imageToButtonMap[index] ?? -1]} />
        ))}
      </x-block-list>
    </x-activity-page>
  )
})

export default SeasonPage
