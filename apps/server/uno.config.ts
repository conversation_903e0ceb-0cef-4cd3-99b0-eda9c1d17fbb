import type { Theme } from '@unocss/preset-wind';
import presetWind from '@unocss/preset-wind'
import { skynetUnoPreset } from '@skynet/preset/uno'
import { defineConfig } from 'unocss' // 确保导入 defineConfig
import { breakpoints } from '../dramato-web/src/lib/constants'
import tailwindConfig from '../dramato-web/delete-tailwind.config.js'
import config from './.nuxt/uno.config.mjs'

export default defineConfig({
  ...config, // 合并 Nuxt 模块的配置
  presets: [
    presetWind({
      dark: {
        light: '',
        dark: '.dark',
      },
    }),
    skynetUnoPreset,
  ],
  rules: [
    [/^(text|bg|border)-([a-z]+)-(\d+)$/,
      ([, type, variant, d]) => ({
        ...(type === 'text' && { color: `var(--${variant}-${d})` }),
        ...(type === 'bg' && { 'background-color': `var(--${variant}-${d})` }),
        ...(type === 'border' && { 'border-color': `var(--${variant}-${d})` })
      })
    ]
  ],
  extendTheme: (theme: Theme) => ({
    ...theme,
    maxWidth: {
      ...theme.maxWidth,
      pad: 'var(--pad-page-max-width)',
      pc: 'var(--pc-page-max-width)',
    },
    maxHeight: {
      ...theme.maxHeight,
      'safe-screen': 'calc(100vh - var(--vh-offset,0px))',
    },
    height: {
      ...theme.height,
      'top-bar': 'var(--top-bar-height)',
      'safe-screen': 'calc(100vh - var(--vh-offset,0px))',
    },
    backgroundColor: {
      ...theme.backgroundColor,
      'fill-1': 'var(--fill-1)',
      'fill-2': 'var(--fill-2)',
      'fill-3': 'var(--fill-3)',
      'fill-4': 'var(--fill-4)',
      'fill-5': 'var(--fill-5)',
      'fill-6': 'var(--fill-6)',
      'fill-7': 'var(--fill-7)',
      'fill-8': 'var(--fill-8)',
    },
    spacing: {
      'safe-bottom': 'env(safe-area-inset-bottom)',
    },
  }),
  theme: {
    breakpoints: {
      pad: breakpoints.pad.min + 'px',
      pc: breakpoints.pc.min + 'px',
    },
    colors: {
      ...tailwindConfig.theme.colors,
      primary: '#0a66c2',
    },
    zIndex: {
      up: '1',
      'up-up': '2',
      footer: '64',
      dialog: '128',
      'series-menu': '129',
      popover: '1024',
      shareButton: '2048',
      mask: '2560',
      toast: '5120',
    },
  },
  preflights: [
    {
      getCSS: () => `
        :root {
          font-size: calc(16 * 100vw / 375);
        }
        @media screen and (min-width: 768px) {
          :root {
            font-size: 16px;
          }
        }
      `,
    },
  ],
})
