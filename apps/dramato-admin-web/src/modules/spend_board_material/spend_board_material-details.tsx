/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformInteger } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { z } from 'zod'
import { useAdLevel } from './use-spend_board_material'
type AdLevelFormOptions = {
  props: {}
}

export const AdLevelForm = createComponent<AdLevelFormOptions>({
  props: {},
}, props => {
  const {
    currentAd,
    closeAdDialog,
    onCreate,
    onEdit,
    isUpdating,
  } = useAdLevel()

  const Form = CreateForm<M.AdUnit.CreateAd>()
  const formRules = z.object({
    level: z.number().min(1, '请填写档位').max(9999, '支持输入1-9999的整数'),
    country: z.string().min(1, '请填写国家'),
    start: z.number().min(0, '请填写档位').max(9999, '支持输入1-9999的整数'),
    end: z.number().min(0, '请填写档位').max(9999, '支持输入1-9999的整数'),
  })

  const { error, validateAll } = useValidator(currentAd, formRules)
  console.log('currentAd', currentAd.value)

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-1 grid-cols-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(currentAd.value || {}, path, value)
          }}
          items={[
            [requiredLabel('平台'), 'platform', { type: 'select', options: [{ label: 'ios', value: 'ios' }, { label: 'android', value: 'android' }], autoInsertEmptyOption: false }],
            [
              requiredLabel('档位'),
              'level',
              {
                type: 'number',
                max: 9990,
                min: 1,
              },
              {
                transform: transformInteger,
              },
            ],
            [
              requiredLabel('国家组'),
              'country',
              {
                type: 'text',
              },
            ],
            [
              requiredLabel('广告价值区间'),
              '',
              {
                type: 'custom',
                render: (r: any) => {
                  return (
                    <div class={mc('flex items-center gap-2')}>
                      <span class={mc('input input-bordered flex items-center gap-1 flex-1 h-8 text-xs px-1')}>
                        <input
                          type="number"
                          class="grow"
                          value={currentAd.value.start || 0}
                          onInput={(e: any) => {
                            const value = e.target.value
                            currentAd.value.start = +value
                          }}
                        />
                      </span>
                      至
                      <span class={mc('input input-bordered flex items-center gap-1 flex-1 h-8 text-xs px-1')}>
                        <input
                          type="number"
                          class="grow"
                          value={currentAd.value.end || 0}
                          onInput={(e: any) => {
                            const value = e.target.value
                            currentAd.value.end = +value
                          }}
                        />
                      </span>
                    </div>
                  )
                },
              },
              {
                hint: '输入范围0到9999，可带2位小数',
              },
            ],
          ]}
          data={currentAd.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={closeAdDialog.value}>取消</Button>
        <Button
          class={mc('btn btn-primary btn-sm')}
          disabled={isUpdating.value} onClick={() => {
            const next = () => !currentAd.value?.id ? void onCreate() : void onEdit()

            try {
              const exclude: string[] = []

              if (!validateAll({ exclude })) {
                console.log('err', error)

                return
              }

              next()
            } catch (error) {
              console.log('error', error)
            }
          }}
        >
          {isUpdating.value && <span class="loading loading-spinner size-4" />}
          {isUpdating.value ? '提交中' : '确定'}
        </Button>
      </div>
    </>
  )
})
