/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { CreateTableOld, TableColumnOld, Button, Icon, openDialog, showSuccessToast, showFailToast, SvgIcon } from '@skynet/ui'
import { langValue, langKey } from 'src/modules/resource/constant'
import { Uploader } from 'src/modules/common/uploader/uploader'
import { UploadImagePreview } from './upload-image-preview'
import { ref } from 'vue'

type SeriesTableOptions = {
  props: {
    list: M.IEpisodeSeriesInfo[]
    colors: M.IColor[]
  }
  emits: {
    descriptionChange: () => void
  }
}

export const SeriesTable = createComponent<SeriesTableOptions>({
  props: {
    list: [],
    colors: [],
  },
  emits: {
    descriptionChange: () => {}
  }
}, (props, { emit }) => {
  const langMap: { [x: string | number]: string } = {}
  langKey.forEach((key, index) => {
    langMap[key] = langValue[index]
  })
  const Table = CreateTableOld<M.IEpisodeSeriesInfo>()
  function numberToHexColor(number: number) {
    // 确保输入是有效的整数
    if (!Number.isInteger(number)) {
      throw new Error('Invalid number')
    }

    // 提取 RGB 通道
    const r = (number >> 16) & 0xFF
    const g = (number >> 8) & 0xFF
    const b = number & 0xFF

    // 转换为颜色格式 0xffRRGGBB
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
  }

  const columns: TableColumnOld<M.IEpisodeSeriesInfo>[] = [
    ['语言', row => {
      return (
        <div class="space-y-1">
          <div>{langMap[row.language_code]}</div>
          {row.is_sync === 1 ? <div class="text-primary badge badge-outline badge-primary text-xs">已发布</div> : <div class="text-[var(--text-3)] badge badge-outline text-xs">未发布</div>}
        </div>
      )
    }, { class: 'w-[100px]' }],
    ['剧名', row => {
      return (
        <input
          class="w-full input-sm input input-bordered h-8"
          value={row.title}
          maxlength={255}
          dir={row.language_code === 'ar' ? 'rtl' : 'ltl' }
          placeholder="剧名，最多255个字符"
          onInput={(e: Event) => {
            row.title = (e.target as HTMLInputElement).value.replace(/[\r\n]/g, '').replace(/\s{2,}/g, ' ').trim()
          }}
        />
      )
    }, { class: 'w-[200px]' }],
    ['剧名2', row => {
      return (
        <input
          class="w-full input-sm input input-bordered h-8"
          value={row.title2}
          maxlength={255}
          dir={row.language_code === 'ar' ? 'rtl' : 'ltl' }
          placeholder="剧名2，最多255个字符"
          onInput={(e: Event) => {
            row.title2 = (e.target as HTMLInputElement).value.replace(/[\r\n]/g, '').replace(/\s{2,}/g, ' ').trim()
          }}
        />
      )
    }, { class: 'w-[200px]' }],
    ['简介', row => {
      return (
        <textarea
          class="input-sm input input-bordered w-full h-28"
          value={row.description}
          dir={row.language_code === 'ar' ? 'rtl' : 'ltl' }
          maxlength={1000}
          placeholder="简介，最多300个字符"
          onInput={(e: Event) => {
            const description = (e.target as HTMLInputElement).value
            row.description = description
            emit('descriptionChange')
          }}
        />
      )
    }, { class: 'w-[200px]' }],
    ['配色方案', row => {
      return (
        <select
          class="w-full select-bordered select-sm select"
          value={row.color_style}
          onInput={(e: Event) => {
            row.color_style = +((e.target as HTMLInputElement).value || '')
          }}
        >
          {props.colors?.map((option: M.IColor) => (
            <option value={option.id}>{option.name}</option>
          ))}
        </select>
      )
    }, {
      class: 'w-[100px]',
    }],
    ['封面', row => {
      return (
        <Uploader
          maxsize={2 * 1024 * 1024}
          accept="jpg,png,jpeg,gif"
          dimension={[3, 4]}
          multiple={false}
          ossKeyType="resource"
          class="size-[120px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
          onUploadSuccess={d => {
            showSuccessToast('上传成功！')
            row.cover = d.temp_path as string
          }}
          isImage={false}
        >
          {row?.cover
            ? (
                <UploadImagePreview image={
                  row.cover.indexOf('http') > -1 ? row.cover : `https://img.tianmai.cn/${row.cover}`
                } onDelete={() => {
                  const hideDialog = openDialog({
                    title: '提示',
                    mainClass: 'pb-0 px-5',
                    body: () => (
                      <x-image-confirm-dialog class="flex flex-col gap-y-[25px]">
                        <x-image-body>是否删除图片</x-image-body>
                        <x-image-footer class="flex justify-end gap-x-[10px] w-full">
                          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                          <button class="btn btn-primary btn-sm" onClick={() => {
                            row.cover = ''
                            hideDialog()
                          }}
                          >确定
                          </button>
                        </x-image-footer>
                      </x-image-confirm-dialog>
                    ),
                  })
                }}
                />
              )
            : <span class="size-full flex items-center justify-center">上传封面, 3:4</span>}
        </Uploader>
      )
    }, { class: 'w-[180px]' }],

    ['封面2', row => {
      return (
        <Uploader
          maxsize={2 * 1024 * 1024}
          accept="jpg,png,jpeg,gif"
          dimension={[3, 4]}
          multiple={false}
          ossKeyType="resource"
          class="size-[120px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
          onUploadSuccess={d => {
            showSuccessToast('上传成功！')
            row.cover2 = d.temp_path as string
          }}
          isImage={false}
        >
          {row.cover2
            ? (
                <UploadImagePreview image={
                  row.cover2 && row.cover2.indexOf('http') > -1 ? row.cover2 : `https://img.tianmai.cn/${row.cover2}`
                } onDelete={() => {
                  const hideDialog = openDialog({
                    title: '提示',
                    mainClass: 'pb-0 px-5',
                    body: () => (
                      <x-image-confirm-dialog class="flex flex-col gap-y-[25px]">
                        <x-image-body>是否删除图片</x-image-body>
                        <x-image-footer class="flex justify-end gap-x-[10px] w-full">
                          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                          <button class="btn btn-primary btn-sm" onClick={() => {
                            row.cover2 = ''
                            hideDialog()
                          }}
                          >确定
                          </button>
                        </x-image-footer>
                      </x-image-confirm-dialog>
                    ),
                  })
                }}
                />
              )
            : <span class="size-full flex items-center justify-center">上传封面2, 3:4</span>}
        </Uploader>
      )
    }, { class: 'w-[180px]' }],

    ['横图', row => {
      return (
        <Uploader
          maxsize={2 * 1024 * 1024}
          dimension={[16, 9]}
          multiple={false}
          ossKeyType="resource"
          class="size-[120px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
          onUploadSuccess={d => {
            showSuccessToast('上传成功！')
            row.horizontal_cover = d.temp_path as string
          }}
          isImage={false}
        >
          {row?.horizontal_cover
            ? (
                <UploadImagePreview image={
                  row.horizontal_cover.indexOf('http') > -1 ? row.horizontal_cover : `https://img.tianmai.cn/${row.horizontal_cover}`
                } onDelete={() => {
                  const hideDialog = openDialog({
                    title: '提示',
                    mainClass: 'pb-0 px-5',
                    body: () => (
                      <x-image-confirm-dialog class="flex flex-col gap-y-[25px]">
                        <x-image-body>是否删除图片</x-image-body>
                        <x-image-footer class="flex justify-end gap-x-[10px] w-full">
                          <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                          <button class="btn btn-primary btn-sm" onClick={() => {
                            row.horizontal_cover = ''
                            hideDialog()
                          }}
                          >确定
                          </button>
                        </x-image-footer>
                      </x-image-confirm-dialog>
                    ),
                  })
                }}
                />
              )
            : <span class="size-full flex items-center justify-center">上传横图, 16:9</span>}
        </Uploader>
      )
    }, { class: 'w-[180px]' }],

    ['psd资源', row => {
      const loading = ref(false)
      return (
        <Uploader
          maxsize={500 * 1024 * 1024}
          accept="psd"
          multiple={false}
          ossKeyType="resource"
          isImage={false}
          showFileList={false}
          class="size-[118px] border-dashed border-[1px] rounded-md overflow-hidden cursor-pointer"
          beforeUpload={() => {
            loading.value = true
          }}
          onUploadSuccess={d => {
            showSuccessToast('上传成功！')
            loading.value = false
            row.psd_cover = d.temp_path as string
          }}
          onUploadFailed={() => {
            loading.value = false
          }}
        >
          { loading.value
            ? <span class="size-full flex items-center justify-center">上传中……</span>
            : row?.psd_cover
              ? (
                  <div class="hover group relative flex items-center justify-center size-[118px]">
                    <SvgIcon class="w-[80px] h-auto" name="ic_psd" />
                    <div class="absolute space-x-4 left-0 right-0 bottom-0 top-0 z-up-up size-full flex items-center justify-center group-hover:opacity-100 opacity-0 bg-mask-1">
                      <SvgIcon class="size-6 cursor-pointer" name="ic_trash" onClick={e => {
                        e.preventDefault()
                        e.stopPropagation()
                        const hideDialog = openDialog({
                          title: '提示',
                          mainClass: 'pb-0 px-5',
                          body: () => (
                            <x-image-confirm-dialog class="flex flex-col gap-y-[25px]">
                              <x-image-body>是否删除</x-image-body>
                              <x-image-footer class="flex justify-end gap-x-[10px] w-full">
                                <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
                                <button class="btn btn-primary btn-sm" onClick={() => {
                                  row.psd_cover = ''
                                  hideDialog()
                                }}
                                >确定
                                </button>
                              </x-image-footer>
                            </x-image-confirm-dialog>
                          ),
                        })
                      }}
                      />
                      <SvgIcon class="size-6 cursor-pointer text-white" name="ic_download" onClick={e => {
                        e.preventDefault()
                        e.stopPropagation()
                        const link = document.createElement('a')
                        const c = row.psd_cover!
                        link.href = c.indexOf('http') > -1 ? c : `https://img.tianmai.cn/${c}`
                        link.download = row.psd_cover!
                        link.click()
                      }}
                      />
                    </div>
                  </div>
                )
              : <span class="size-full flex items-center justify-center">psd资源, 500M</span>}
        </Uploader>
      )
    }, { class: 'w-[180px]' }],
    ['配色展示', row => {
      const colorObj = props.colors.find(c => c.id === row.color_style)
      if (!colorObj) {
        return `-暂无配色-`
      }
      return (
        <div
          class="relative w-full h-[160px] p-4 transition-opacity duration-500 opacity-100 rounded-md overflow-hidden"
          style={{ backgroundColor: numberToHexColor(colorObj.background) }}
        >
          <div class="flex space-x-4">
            <div class="w-[100px]">
              <img src={row.cover} class="w-[120px] object-cover" />
            </div>
            <div class="flex-1 flex-col space-y-4">
              <div>
                <span class="text-white">{row.title}</span>
                <div class="line-clamp-2" style={{ color: numberToHexColor(colorObj.font) }}>
                  {row.description}
                </div>
              </div>
              <div style={{ backgroundColor: numberToHexColor(colorObj.button), color: '#fff' }} class="btn border-none btn-xs">播放</div>
            </div>
          </div>
        </div>
      )
    },
    { class: 'w-[230px]' },
    ],
    ['推送通知', row => {
      return (
        <x-push-desc-list class="flex flex-col items-center gap-2">
          {
            (row.push_content_list || [])
              .map((i: any, idx: number) => (
                <div class={mc('flex items-center gap-2', i.is_delete ? 'hidden' : '')}>
                  <span class={mc('input input-bordered flex items-center gap-1 h-8 w-[120px] text-xs px-1')}>
                    <input
                      type="text"
                      class="grow"
                      value={i.content || ''}
                      onInput={(e: any) => {
                        const value = e.target.value
                        row.push_content_list[idx].content = value
                      }}
                    />
                  </span>
                  <Icon name="ant-design:delete-filled" class="size-4 cursor-pointer" onClick={() => {
                    if (!i.id) {
                      row.push_content_list.splice(idx, 1)
                      return
                    }
                    row.push_content_list[idx].is_delete = true
                  }}
                  />
                </div>
              ))
          }
          <Button
            class={mc('btn btn-sm btn-outline w-[100px]')}
            onClick={() => {
              if (!row.push_content_list) {
                row.push_content_list = []
              }
              console.log('row.push_content_list', row.push_content_list)

              row.push_content_list.push({
                content: '',
                is_delete: false,
              })
            }}
          >新增
          </Button>
        </x-push-desc-list>
      )
    }, { class: 'w-[130px] text-center' }],
  ]

  return () => (
    <Table list={props.list} columns={columns} class="tm-table-fix-first-column tm-table-fix-last-column" />
  )
})

export default SeriesTable
