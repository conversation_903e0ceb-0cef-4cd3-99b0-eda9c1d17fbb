#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import subprocess
import datetime
import sys
import re
import os
from collections import defaultdict
import io


def get_git_username():
    """获取 git config 中配置的用户名"""
    # 与上文相同
    cmd = ["git", "config", "user.name"]
    result = subprocess.run(cmd, capture_output=True, text=True)

    if result.returncode == 0 and result.stdout.strip():
        return result.stdout.strip()

    # 如果本地没有配置，尝试获取全局配置
    cmd = ["git", "config", "--global", "user.name"]
    result = subprocess.run(cmd, capture_output=True, text=True)

    if result.returncode == 0 and result.stdout.strip():
        return result.stdout.strip()

    return None


def get_local_branches():
    """获取所有本地分支，排除 test 和 main 分支"""
    cmd = ["git", "branch"]
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"获取本地分支失败: {result.stderr}")
        sys.exit(1)

    branches = []
    for line in result.stdout.strip().split('\n'):
        if line:
            # 去除分支名称前的 '* ' 或 '  '
            branch = line[2:] if line.startswith('* ') else line.strip()
            # 排除 test 和 main 分支
            if branch != 'test' and branch != 'main':
                branches.append(branch)

    return branches


def get_user_commits(branch, username, days=7):
    """获取指定用户在指定分支上过去几天内的提交"""
    # 与上文相同
    since_date = (datetime.datetime.now() - datetime.timedelta(days=days)).strftime('%Y-%m-%d')

    # 使用 --author 参数过滤特定用户的提交，--since 参数过滤时间
    cmd = [
        "git", "log", branch,
        f"--author={username}",
        f"--since={since_date}",
        "--pretty=format:%h|%ad|%s",
        "--date=short"
    ]

    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"获取提交信息失败: {result.stderr}")
        return []

    commits = []
    for line in result.stdout.strip().split('\n'):
        if line:
            commit_hash, date, message = line.split('|', 2)
            commits.append({
                'hash': commit_hash,
                'date': date,
                'message': message
            })

    return commits


def get_commit_details(commit_hash):
    """获取提交的详细信息"""
    # 与上文相同
    cmd = ["git", "show", commit_hash, "--name-status", "--pretty=format:''"]
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"获取提交详情失败: {result.stderr}")
        return []

    details = []
    for line in result.stdout.strip().split('\n'):
        if line and not line.startswith("'") and not line.endswith("'"):
            parts = line.split('\t')
            if len(parts) >= 2:
                status = parts[0]
                file_path = parts[1]
                details.append({
                    'status': status,
                    'file': file_path
                })

    return details


def generate_weekly_report(username, days=7):
    """生成周报"""
    # 创建一个 StringIO 对象来捕获输出
    output_buffer = io.StringIO()
    original_stdout = sys.stdout
    sys.stdout = output_buffer

    print(f"正在生成 {username} 过去 {days} 天的工作周报...\n")

    # 获取当前仓库名称
    try:
        cmd = ["git", "config", "--get", "remote.origin.url"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        repo_url = result.stdout.strip()
        repo_name = os.path.basename(repo_url)
        if repo_name.endswith('.git'):
            repo_name = repo_name[:-4]
    except Exception:
        repo_name = os.path.basename(os.getcwd())

    print(f"仓库: {repo_name}\n")

    # 获取所有本地分支
    branches = get_local_branches()

    # 按日期组织提交
    commits_by_date = defaultdict(list)
    file_changes = defaultdict(lambda: {'added': 0, 'modified': 0, 'deleted': 0})

    total_commits = 0
    processed_commits = set()  # 用于过滤重复提交

    for branch in branches:
        commits = get_user_commits(branch, username, days)
        for commit in commits:
            # 避免重复统计同一个提交
            if commit['hash'] in processed_commits:
                continue

            processed_commits.add(commit['hash'])
            commits_by_date[commit['date']].append(commit)
            total_commits += 1

            # 统计文件变更
            details = get_commit_details(commit['hash'])
            for detail in details:
                if detail['status'] == 'A':
                    file_changes[detail['file']]['added'] += 1
                elif detail['status'] == 'M':
                    file_changes[detail['file']]['modified'] += 1
                elif detail['status'] == 'D':
                    file_changes[detail['file']]['deleted'] += 1

    if total_commits == 0:
        print(f"{username} 在过去 {days} 天内没有提交记录")
        sys.stdout = original_stdout
        return output_buffer.getvalue()

    # 生成周报内容
    print(f"## {username} 过去 {days} 天的工作总结")
    print(f"\n总提交数: {total_commits}\n")

    # 按日期显示提交
    print("### 提交日志")
    for date in sorted(commits_by_date.keys()):
        print(f"\n#### {date}")
        for commit in commits_by_date[date]:
            print(f"- {commit['message']} ({commit['hash']})")

    # 显示文件变更统计
    print("\n### 文件变更统计")
    added_files = 0
    modified_files = 0
    deleted_files = 0

    for file_path, changes in file_changes.items():
        if changes['added'] > 0:
            added_files += 1
        if changes['modified'] > 0:
            modified_files += 1
        if changes['deleted'] > 0:
            deleted_files += 1

    print(f"- 新增文件: {added_files}")
    print(f"- 修改文件: {modified_files}")
    print(f"- 删除文件: {deleted_files}")

    # 显示主要工作内容
    print("\n### 主要工作内容")
    print("主要工作内容包括：")

    # 分析提交消息，提取关键工作内容
    work_items = set()
    for date, commits in commits_by_date.items():
        for commit in commits:
            message = commit['message']
            # 跳过以 merge 开头的提交
            if message.lower().startswith('merge'):
                continue
            # 尝试从提交消息中提取主要工作项
            # 移除常见的提交消息前缀
            message = re.sub(r'^(feat|fix|docs|style|refactor|test|chore)(\(.+?\))?: ', '', message)
            work_items.add(message)

    # 输出主要工作项
    for idx, item in enumerate(sorted(work_items), 1):
        print(f"{idx}. {item}")

    # 恢复标准输出并返回捕获的内容
    sys.stdout = original_stdout
    return output_buffer.getvalue()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='生成个人工作周报')
    parser.add_argument('--username', '-u', default=None, help='Git提交作者的用户名，如果不提供则使用 git config 中的用户名')
    parser.add_argument('--days', '-d', type=int, default=5, help='统计的天数，默认为5天')
    parser.add_argument('--output', '-o', default='weekly.log', help='输出文件路径，默认为 weekly.log')

    args = parser.parse_args()

    # 如果没有提供用户名，则尝试从 git config 获取
    username = args.username
    if not username:
        username = get_git_username()
        if not username:
            print("错误: 未提供用户名且无法从 git config 获取用户名")
            sys.exit(1)
        print(f"未提供用户名，使用 git config 中的用户名: {username}")

    # 生成周报
    report_content = generate_weekly_report(username, args.days)

    # 输出到控制台
    print(report_content)

    # 获取脚本所在目录路径
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 将输出文件路径设为脚本所在目录
    output_path = os.path.join(script_dir, args.output)

    # 保存到文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(report_content)

    print(f"\n周报已保存到文件: {output_path}")
