# MergeClass 标签

此标签会将 baseClass 与从外部传入的 class 合并，然后应用到 tag 上。

```tsx
import { MergeClass } from '@skynet/ui'

export const Router: FunctionalComponent<{}> = (props, { slots }) => (
  <MergeClass tag="div" baseClass="block">
    {slots.default?.()}
  </MergeClass>
)
```

# mergeClass 函数

建议优先使用 MergeClass 标签，因为它更加简洁。

mergeClass 接受两个参数，它将第二个参数合并到第一个参数中。
mergeClass 的缩写为 mc。

```tsx
import { mc, mergeClass } from '@skynet/shared'

<div class={mc("block", props.class)} />
```

如果你有三个参数，你可以这样使用：

```tsx
import { mc, mergeClass } from '@skynet/shared'

<div class={mc("block", [props.class, props.class2])} />
```
