import { createComponent, SlotFn } from '@skynet/shared'
import { signInBg, episodesImage } from './images/sign-in-images'
import OpenPlatformLogo from '../logo/open-platform-logo'
export const SessionLayout = createComponent<{
  slots: {
    default: SlotFn
  }
}>(null, (props, { slots }) => {
  return () => (
    <x-session-layout
      style={{ backgroundImage: `url(${signInBg})` }}
      class="block min-h-screen bg-cover "
    >
      <x-inner class="max-w-[var(--pc-page-max-width)] mx-auto flex justify-start items-center ">
        <x-left class="ml-[100px] shrink-0 relative">
          <OpenPlatformLogo size="large" class="absolute top-10 left-0 text-white" />
          <img src={episodesImage} width="649" height="900" class="w-[649px]" />
        </x-left>
        <x-right class="">
          {slots.default()}
        </x-right>
      </x-inner>
    </x-session-layout>
  )
})

export default SessionLayout
