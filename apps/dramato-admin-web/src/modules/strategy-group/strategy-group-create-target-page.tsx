import { bindLoading, createComponent } from '@skynet/shared'
import { Button, Empty, transformInteger, transformNumber } from '@skynet/ui'
import { FormOptions } from '@skynet/ui/form/form-types'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { apiGetStrategyGroupAdChannelList, apiGetStrategyGroupUserProfile } from 'src/modules/strategy-group/strategy-group-api'
import { useStrategyGroupStore } from 'src/modules/strategy-group/strategy-group-store'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
import { profileDefaultValues } from './profile-custom-renders'
type StrategyGroupCreateTargetPageOptions = {
  props: {}
}
export const StrategyGroupCreateTargetPage = createComponent<StrategyGroupCreateTargetPageOptions>({
  props: {},
}, () => {
  const { formData, Form, stepOneError, stepThreeError, validateStepOneForIAP,
    resetUserFormData, userProfileList, fetchingUserProfileList, platformMap } = useStrategyGroupStore()
  const route = useRoute()
  const {
    list,
    page,
    pageSize,
    search,
  } = useUserStrategyLayer()
  const isViewMode = computed(() => route.query.mode === 'view')

  const loadProfileAndSetDefaults = async () => {
    const response = await bindLoading(apiGetStrategyGroupUserProfile({ platform: platformMap[formData.value.user_platform] ?? 'All' }), fetchingUserProfileList)
    if (!response) return
    console.log(response, '>>>response')

    userProfileList.value = response.data?.list ?? []
    console.log(1)

    if (formData.value.id) return
    userProfileList.value.forEach(item => {
      if (formData.value?.user_config?.custom_users_config?.[item.item_code]) return
      set(formData.value, `user_config.custom_users_config.${item.item_code}`, profileDefaultValues[item.item_code] ?? [''])
    })
  }

  watch(() => formData.value.user_platform, loadProfileAndSetDefaults, { immediate: true })

  const userWhiteListEnabled = ref(false)
  const adChannelList = ref<Array<{ name: string, code: string }>>([])
  onMounted(async () => {
    const response = await apiGetStrategyGroupAdChannelList()
    if (!response) return
    adChannelList.value = response.data?.list ?? []
  })
  const router = useRouter()
  const onSubmit = () => {
    if (!validateStepOneForIAP()) return
    void router.push({ path: './product', query: { ...route.query, id: formData.value.id } })
  }
  watch(() => formData.value.user_config?.af_ad_config?.adset_list?.map(item => item.code), () => {
    const list = formData.value.user_config?.af_ad_config?.adset_list ?? []
    list.forEach(item => { item.name = item.code })
  })

  watch(() => formData.value.user_white_list, () => {
    if (formData.value.user_white_list && formData.value.user_white_list.length > 0) {
      userWhiteListEnabled.value = true
    } else {
      userWhiteListEnabled.value = false
    }
  }, {
    immediate: true,
  })

  watch(() => userWhiteListEnabled.value, () => {
    if (!userWhiteListEnabled.value) {
      formData.value.user_white_list = undefined
    }
  })

  // 是否订阅用户级联逻辑
  watch(() => formData.value.user_config?.custom_users_config?.SubscribeStatus, (newV, oldV) => {
    if (!newV || !oldV || !formData.value.user_config?.custom_users_config) return
    const againstList = userProfileList.value.find(item => item.item_code === 'SubscribeStatus')?.item_child?.filter(item => ['All', '是（VIP中）', '从未订阅', '否，曾经是'].includes(item.item_name))?.map(item => item.item_value) ?? []
    const cascadeParentItem = userProfileList.value.find(item => item.item_code === 'SubscribeStatus')?.item_child?.filter(item => ['否，曾经是'].includes(item.item_name))?.map(item => item.item_value)?.[0] ?? undefined
    const cascadeChildList = userProfileList.value.find(item => item.item_code === 'SubscribeStatus')?.item_child?.filter(item => ['曾是Weekly', '曾是Yearly'].includes(item.item_name))?.map(item => item.item_value) ?? []
    const added = againstList.filter(item => newV.includes(item) && !oldV.includes(item))
    const removed = oldV.filter(item => !newV.includes(item))
    if (added.length > 0) {
      // 添加了互斥列表中的项
      if (cascadeParentItem !== undefined && added.includes(cascadeParentItem)) {
        set(formData.value.user_config.custom_users_config, 'SubscribeStatus', newV.filter(item => (againstList.includes(item) && added.includes(item)) || !againstList.includes(item)).concat(cascadeChildList.filter(item => !newV.includes(item))))
      } else {
        set(formData.value.user_config.custom_users_config, 'SubscribeStatus', newV.filter(item => (againstList.includes(item) && added.includes(item)) || !againstList.includes(item)))
      }
    } else if (removed.length > 0) {
      // 移除了级联父项
      if (cascadeParentItem !== undefined && removed.includes(cascadeParentItem)) {
        set(formData.value.user_config.custom_users_config, 'SubscribeStatus', newV.filter(item => !cascadeChildList.includes(item)))
      }
      // 移除了级联子项
      if (cascadeChildList.includes(removed[0])) {
        // 如果级联子项都被移除
        if (newV.filter(item => cascadeChildList.includes(item)).length === 0) {
          set(formData.value.user_config.custom_users_config, 'SubscribeStatus', newV.filter(item => item !== cascadeParentItem))
        }
      }
      if (newV.length === 0) {
        set(formData.value.user_config.custom_users_config, 'SubscribeStatus', [''])
      }
    }
  })

  // 近3天登录状态互斥规则
  watch(() => formData.value.user_config?.custom_users_config?.RecentThreeDaysLoginStatus, (newV, oldV) => {
    if (!newV || !oldV || !formData.value.user_config?.custom_users_config) return
    const againstList = userProfileList.value.find(item => item.item_code === 'RecentThreeDaysLoginStatus')?.item_child?.map(item => item.item_value) ?? []
    const added = againstList.filter(item => newV.includes(item) && !oldV.includes(item))
    if (added.length > 0) {
      set(formData.value.user_config.custom_users_config, 'RecentThreeDaysLoginStatus', newV.filter(item => (againstList.includes(item) && added.includes(item)) || !againstList.includes(item)))
    }
  })

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
  })

  const items = computed(() => [
    [
      [requiredLabel('策略组名称'), 'name', { type: 'text', disabled: isViewMode.value }, { class: 'w-[20em]' }],
      formData.value.id && ['策略组id', 'id', { type: 'text', disabled: true }, { class: 'shrink-0' }],
    ],
    // [
    //   [requiredLabel('是否AB实验'), 'is_abtest', {
    //     type: 'radio', options: [
    //       { label: '是', value: 1, disabled: isViewMode.value },
    //       { label: '否', value: 0, disabled: isViewMode.value },
    //     ],
    //   }],
    // ],

    <h2 class="col-span-2 mb-4 text-lg font-bold"> 目标用户群 </h2>,
    // [
    [<span>用户设备 <small class="ml-2 text-gray-400">配置用户画像时，需保证用户设备与此选项一致</small></span>, 'user_platform', {
      type: 'radio',
      options: [
        { label: '全部', value: 0, disabled: isViewMode.value },
        { label: 'iOS', value: 1, disabled: isViewMode.value },
        { label: 'Android', value: 2, disabled: isViewMode.value },
      ],
    }, { errorVisible: false }],
    //   (formData.value.is_abtest === 0
    //     ? ([
    //         '分层画像',
    //         'strategy_layer_ids',
    //         {
    //           type: 'multi-select',
    //           search: true,
    //           popoverWrapperClass: 'z-popover-in-dialog',
    //           options: list.value.map(n => {
    //             return { value: n.id, label: `${n.id}/${n.name}` }
    //           }),
    //           class: 'w-[400px]',
    //         },
    //       ])
    //     : null),
    // ],
    // [
    //   '资源ID',
    //   'custom_series_ids',
    //   {
    //     type: 'text',
    //     disabled: isViewMode.value || formData.value.series_package_id,
    //   },
    //   { class: 'w-[20em]' },
    // ],
    // [
    //   '剧包筛选',
    //   'series_package_id',
    //   {
    //     type: 'select',
    //     disabled: formData.value.custom_series_ids?.length,
    //     options: [{
    //       value: 1,
    //       label: '首发保护期15天内',
    //     }, {
    //       value: 2,
    //       label: '首发保护期15天外，累计10w',
    //     },
    //     {
    //       value: 3,
    //       label: ' 付费剧',
    //     },
    //     {
    //       value: -1,
    //       label: '其他',
    //     }],
    //     class: 'w-[400px]',
    //   },
    //   {
    //     transform: transformNumber,
    //   },
    // ],
    <h2 class="col-span-2 text-lg font-bold"> 任务计划 </h2>,
    [
      [
        requiredLabel(<span>任务优先级<small class="pl-1 text-gray-500">数字越大越高</small></span>),
        'task_config.priority',
        {
          type: 'number',
          disabled: isViewMode.value,
        },
        { transform: transformInteger },
      ],
      [
        requiredLabel('策略持续时间'),
        'task_config.duration',
        {
          type: 'number',
          placeholder: '填 -1 表示不限',
          suffix: '天',
          min: -1,
          disabled: isViewMode.value,
        },
        { transform: transformNumber, class: 'w-[12em]' },
      ],
    ],
    // <h2 class="col-span-2 font-bold text-lg"> 策略范围 </h2>,
    // [
    //   'flex flex-col gap-4',
    //   ['', 'send_pro_user',
    //     { type: 'checkbox', label: '下发给符合条件的用户', disabled: isViewMode.value },
    //     {
    //       class: 'shrink-0', errorVisible: false, transform: [
    //         (raw: number) => {
    //           return raw ? true : false
    //         },
    //         (display: boolean) => {
    //           return display ? 1 : 0
    //         },
    //       ],
    //     },
    //   ],
    // ],
  ] as FormOptions<FormData>['props']['items'])

  const formError = computed(() => {
    return {
      ...stepOneError.value,
      ...stepThreeError.value,
    }
  })
  return () => (
    <div>
      <Form class="flex flex-col gap-4 p-8" data={formData.value} items={items.value}
        error={formError.value}
        onSubmit={onSubmit}
        onReset={resetUserFormData}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        actions={() => (
          <div class="flex justify-between gap-x-2">
            {isViewMode.value ? <Empty /> : <Button class="btn btn-sm" type="reset">重置</Button>}
            <Button class="btn btn-primary btn-sm" type="submit">下一步</Button>
          </div>
        )}
        actionClass="col-span-2"
      />
    </div>
  )
})
export default StrategyGroupCreateTargetPage
