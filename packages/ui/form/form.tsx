import { createComponent, fn, mc, required, tryCall } from '@skynet/shared'
import { isVNode, shallowRef, toRaw, VNode, watchEffect } from 'vue'
import 'vue-multiselect/dist/vue-multiselect.css'
import { preventDefault } from '../utils'
import { FormOptions } from './form-types'

import { cloneDeep } from 'lodash-es'
import { Empty, isFormItemArray, isFormItemGroup } from './form-helper'
import { FormItem } from './form-item'
import type { FormItems, FormItem as FormItemType } from './form-types'
import { Button, MergeClass } from '../ui'
import { useThrottleFn } from '@vueuse/core'
export * from './form-helper'
export * from './form-item'
export * from './datetime-picker'

export const emptyNumber = Symbol('emptyNumber') as unknown as number

export const CreateForm = <D extends object>() => createComponent<FormOptions<D>>({
  name: 'Form',
  props: {
    data: required,
    dataReady: false,
    error: null,
    items: [],
    actions: null,
    class: '',
    buttonWrapperClass: '',
    actionClass: '',
    labelClass: '',
    hasAction: true,
    submitText: '提交',
    resetText: '重置',
    hideEmptyError: false,
  },
  emits: {
    change: fn,
    submit: fn,
    reset: fn,
  },
}, (props, { emit }) => {
  const initialData = shallowRef(cloneDeep(toRaw(props.data)))
  watchEffect(() => {
    if (props.dataReady) {
      initialData.value = cloneDeep(toRaw(props.data))
    }
  })
  const onSubmit = useThrottleFn((e: Event) => {
    emit('submit', e)
  }, 1000)
  const renderItem = (_item: FormItems[0], index: number) => {
    if (!_item) return null
    if (isVNode(_item)) return _item
    if (_item instanceof Function) return _item()
    if (isFormItemGroup(_item)) {
      return (
        <MergeClass key={index} tag="x-form-item-group" baseClass="flex gap-4" class={_item.find(item => typeof item === 'string')}>
          {(_item.filter(i => i && typeof i !== 'string') as FormItemType[]).map(renderItem)}
        </MergeClass>
      )
    }
    return (
      <FormItem key={index} data={props.data} item={_item} labelClass={props.labelClass} error={props.error} 
        hideEmptyError={props.hideEmptyError} onChange={(path, value) => emit('change', path, cloneDeep(value))}
      />
    )
  }
  return () => {
    return (
      <form
        class={mc('flex flex-wrap gap-x-4 gap-y-3', props.class)}
        onSubmit={e => {
          e.preventDefault()
          void onSubmit(e)
        }}
        onReset={e => {
          preventDefault(e)
          emit('reset', e, initialData.value)
        }}
      >
        {props.items.map(renderItem)}
        {props.hasAction && (
          <x-form-action class={mc('flex flex-col gap-1', props.actionClass)}>
            <x-label class={mc('block', props.labelClass)}><Empty /></x-label>
            <x-form-action-buttons class={mc('space-x-2 justify-end flex', props.buttonWrapperClass)}>
              {tryCall(props.actions) || (
                <>
                  <Button class="btn btn-primary btn-sm" type="submit">{props.submitText}</Button>
                  {props.resetText && <Button class="btn btn-outline btn-sm" type="reset">{props.resetText}</Button>}
                </>
              )}
            </x-form-action-buttons>
          </x-form-action>
        )}

      </form>
    )
  }
})
