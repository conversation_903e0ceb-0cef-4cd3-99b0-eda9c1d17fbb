/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { RouterLink, useRoute } from 'vue-router'
import { apiGetUserFeedbackById, apiCloseOrder, apiReplyIssues } from './user-feedback-api'
import { Button, Icon, openDialog, showFailToast, showSuccessToast, Tooltip } from '@skynet/ui'
import { onMounted, onUnmounted, ref } from 'vue'
import { Wrapper } from 'src/layouts/wrapper'
import { DateTime } from '@skynet/ui'
import { useUserFeedback } from './use-user-feedback'
import { ElImage } from 'element-plus'
import { requiredLabel } from 'src/lib/required-label'
import { FileUploader } from 'src/modules/resource-publish/components/file-uploader'
import { useRouter } from 'vue-router'
type UserFeedbackDetailOptions = {
  props: {}
}
export const UserFeedbackDetail = createComponent<UserFeedbackDetailOptions>({
  props: {},
}, props => {
  const id = +useRoute().params.id
  const list = ref()
  const fileUploaderRef = ref<M.FileUploaderInstance>()
  const loading = ref(true)
  const workOrder = ref<M.UserFeedback.DetailItem | undefined>()
  const router = useRouter()

  const lastQuestionId = ref()
  const btnLoading = ref(false)
  const content = ref('')
  const files = ref<M.UploadUserFileWithPath[]>([])

  const {
    problemTypes,
    getProblemTypes,
  } = useUserFeedback()

  const processFeedbackList = (data: M.UserFeedback.DetailItem[]) => {
    if (!data || !data.length) return []
    // 问题（be_replied_id 为空或0）
    const questions = data.filter(item => (!item.be_replied_id || item.be_replied_id === 0))
      .sort((a, b) => a.created - b.created)

    // 回答（be_replied_id 不为空）
    const answers = data.filter(item => item.be_replied_id && item.be_replied_id !== 0)
      .sort((a, b) => a.created - b.created)

    // 组织问题和回答的关系
    const result: M.UserFeedback.DetailItem[] = []

    questions.forEach((question, index) => {
      result.push(question)

      // 找到对应问题的所有回答
      const relatedAnswers = answers.filter(answer => answer.be_replied_id === question.id)
      result.push(...relatedAnswers)
      if (index === questions.length - 1) {
        lastQuestionId.value = question.id
      }
    })

    workOrder.value = questions.find(q => !q.work_order_id)
    return result
  }

  const getFeedbackDetail = async () => {
    try {
      const res = await apiGetUserFeedbackById({
        id,
      })
      /**
       * @description 1. work_order_id 为空或0 放到第一个 为初始工单
       *              2. be_replied_id 为空或0 为问题，按照created从小到大排序
       *              3. be_replied_id 其余不为空的为回答，按照created从小到大排序 放到对应 id
       */
      list.value = processFeedbackList(res.data?.list || [])
      console.log(list.value, 'list.value')
    } catch (error: any) {
      showFailToast(error.response.data.message || '操作失败')
    } finally {
      loading.value = false
    }
  }

  const handleParse = (e: ClipboardEvent) => {
    const data = e.clipboardData
    if (!data) return
    const blob = data.items[0]?.getAsFile()
    if (!blob) return
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = event => {
      const base64Str = event.target?.result
      if (typeof base64Str !== 'string') return
      const bytes = window.atob(base64Str.split(',')[1])
      const array = []
      for (let i = 0; i < bytes.length; i++) {
        array.push(bytes.charCodeAt(i))
      }
      const newBlob = new Blob([new Uint8Array(array)], { type: 'image/png' })
      const file = new File([newBlob], `paste_${Date.now()}.png`, { type: 'image/png' })
      fileUploaderRef.value?.handleStart(file, newBlob)
    }
  }
  let timer: any = 0

  const pollTask = () => {
    if (timer) clearInterval(timer)
    // 轮询任务，每3秒调用一次接口，共调用3次
    let pollCount = 0
    const maxPollCount = 20
    const pollInterval = 3000 // 3秒

    timer = setInterval(() => {
      if (pollCount >= maxPollCount) {
        clearInterval(timer)
        return
      }
      void getFeedbackDetail()
      pollCount++
    }, pollInterval)
  }

  onUnmounted(() => {
    clearInterval(timer)
  })

  const onCloseFeedback = () => {
    const btnLoading = ref(false)
    const hideDeleteDialog = openDialog({
      title: '关闭反馈',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-feedback-confirm-dialog class="flex flex-col gap-y-[25px]">
          <x-feedback-body>
            是否关闭当前用户反馈
          </x-feedback-body>
          <x-feedback-footer class="flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              try {
                btnLoading.value = true
                await apiCloseOrder({
                  id, // 被回复的问题id
                })
                void router.replace('/user-feedback')
                showSuccessToast('操作成功')
                hideDeleteDialog()
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-feedback-footer>
        </x-feedback-confirm-dialog>
      ),
    })
  }

  onMounted(() => {
    void getProblemTypes()
    void getFeedbackDetail()
    pollTask()
  })

  return () => (
    <x-user-feedback-detail class="block pt-4">
      <div class="breadcrumbs text-sm">
        <ul>
          <li><RouterLink to="/user-feedback">反馈收集</RouterLink></li>
          <li>反馈详情</li>
        </ul>
      </div>
      <Wrapper class="min-h-[600px]" v-loading={loading.value}>
        <x-feedback-question>
          {workOrder.value ? (
            <x-feedback-info class="block space-y-4">
              <div class="flex justify-between">
                <div>
                  <div class="badge badge-primary mr-4">{problemTypes.value.find(item => item.id === workOrder.value?.problem_type)?.problem_type || '-'}</div>
                  工单状态：                {
                    workOrder.value?.feedback_status === 0 ? <div class="badge badge-success badge-outline">待回复</div>
                      : workOrder.value?.feedback_status === 1 ? <div class="badge badge-primary badge-outline">处理中</div>
                        : workOrder.value?.feedback_status === 2 ? <div class="badge badge-info badge-outline">已关闭</div> : '-'
                  }
                </div>
                <div>
                  { workOrder.value?.feedback_status !== 2 ? <Button class="btn btn-primary btn-sm" onClick={onCloseFeedback}>关闭反馈</Button> : null}
                </div>
              </div>
              {/* <x-feedback-title class="flex items-center space-x-2 text-lg font-bold">
                <div class="flex-1">反馈描述：{workOrder.value?.description}</div>
              </x-feedback-title>
              <x-feedback-title class="block text-base">翻译：{workOrder.value?.translated_description}</x-feedback-title>
              <x-feedback-title class="block text-base">英文：{workOrder.value?.en_description}</x-feedback-title> */}

              <div class="flex space-x-4 text-base text-[var(--text-2)]">
                <div class="">UID：{workOrder.value?.user_id}</div>
                <DateTime value={(workOrder.value?.created || 0) * 1000} />
                {
                  workOrder.value?.email ? (
                    <div>
                      邮箱：{workOrder.value?.email || '-' }
                    </div>
                  ) : null
                }
                <div>
                  客户端信息：{workOrder.value?.app_name || '-' }
                </div>
                <Tooltip popContent={() => <div>版本号: {workOrder.value?.app_version}</div>}>
                  {workOrder.value?.app_version}
                </Tooltip>
              </div>
              <div class="flex space-x-4 text-base text-[var(--text-2)]">
                <div>资源ID：{workOrder.value?.resource_id || '-' }</div>
                <div>剧ID：{workOrder.value?.series_key || '-' }</div>
                <div>集ID：{workOrder.value?.episode_key || '-' }</div>
                <div>语言：{workOrder.value?.language_code || '-' }</div>
              </div>
            </x-feedback-info>
          ) : null }
          {/* {
            workOrder.value?.image_list && workOrder.value?.image_list?.length > 0 && (
              <x-feedback-image class="mt-4 flex flex-wrap gap-x-2 !text-base !text-[var(--text-1)]">
                {
                  workOrder.value?.image_list.map((image, index) => (
                    <ElImage class="size-[100px] cursor-pointer object-contain" fit="contain" src={image} initial-index={index} preview-src-list={workOrder.value?.image_list} />
                  ))
                }
              </x-feedback-image>
            )
          } */}
        </x-feedback-question>
        { workOrder.value ? <el-divider content-position="left">反馈详情</el-divider> : null }
        <x-feedback-round class="block space-y-4">
          {
            list.value && list.value.map((row: M.UserFeedback.DetailItem, index: number) => {
              return (
                <div class="space-y-2 text-sm">
                  {
                    !row.be_replied_id ? (

                      <div class="chat chat-start space-y-4">
                        <div class="chat-header">
                          用户反馈：
                          <time class="text-xs opacity-50">
                            <DateTime value={row?.created * 1000 || 0} />
                          </time>
                        </div>
                        <div class={mc('chat-bubble', 'mt-2')}>
                          <x-feedback-title class="flex items-start space-x-2">
                            <div class="flex-1">
                              {row?.description}
                            </div>
                          </x-feedback-title>
                          <x-feedback-title class="mt-4 block">翻译：{row?.translated_description}</x-feedback-title>
                          <x-feedback-title class="mt-4 block">英文：{row?.en_description}</x-feedback-title>
                          {
                            row?.image_list && row?.image_list?.length > 0 && (
                              <x-feedback-image class="mt-4 flex flex-wrap gap-x-2 !text-base !text-[var(--text-1)]">
                                {
                                  row?.image_list.map((image, index) => (
                                    <ElImage class="size-[100px] cursor-pointer object-contain" fit="contain" src={image} initial-index={index} preview-src-list={row?.image_list} />
                                  ))
                                }
                              </x-feedback-image>
                            )
                          }
                        </div>
                      </div>
                    ) : (
                      <div class="chat chat-end space-y-4">
                        <div class="chat-header">
                          {row?.operator}：
                          <time class="text-xs opacity-50">
                            <DateTime value={row?.created * 1000 || 0} />
                          </time>
                        </div>
                        <div class={mc('chat-bubble space-y-4', 'mt-2')}>
                          <div>回复译文：{row.reply_content.content}</div>
                          <div>回复原文：{row.input_content}</div>
                          {
                            row?.reply_content.image_list && row?.reply_content.image_list?.length > 0 && (
                              <x-feedback-image class="mt-4 flex flex-wrap gap-x-2 !text-base !text-[var(--text-1)]">
                                {
                                  row?.reply_content.image_list.map((image, index) => (
                                    <ElImage class="size-[100px] cursor-pointer object-contain" fit="contain" src={image} initial-index={index} preview-src-list={row?.reply_content.image_list} />
                                  ))
                                }
                              </x-feedback-image>
                            )
                          }
                        </div>
                        {list.value.length - 1 === index ? <div class="chat-footer">注：用户端展示回复译文</div> : null}
                      </div>
                    )
                  }
                </div>
              )
            })
          }
        </x-feedback-round>
        {
          workOrder.value?.feedback_status !== 2 ? (
            <x-feedback-reply class="bg-gray-100 rounded-lg p-8">
              <x-feedback-confirm-dialog class="flex flex-col gap-y-[25px]">
                <x-feedback-body>
                  <div class="flex items-start justify-start gap-x-2 py-2">
                    <div class="shrink-0">{requiredLabel('回复内容：')}</div>
                    <textarea
                      class="textarea textarea-bordered textarea-sm w-full"
                      placeholder="请输入回复内容"
                      onPaste={(e: ClipboardEvent) => {
                        const data = e.clipboardData
                        if (!data) return
                        // 检查是否包含图片
                        const hasImage = Array.from(data.items).some(item => item.type.startsWith('image/'))
                        if (hasImage) {
                          // 如果是图片则阻止默认粘贴并上传
                          e.preventDefault()
                          handleParse(e)
                        }
                        // 如果是文本则使用默认粘贴行为
                      }}
                      value={content.value}
                      onInput={(e: Event) => {
                        content.value = (e.target as HTMLTextAreaElement).value
                      }}
                    />
                  </div>
                  <div class="flex items-start justify-start gap-x-2 py-2">
                    <div class="shrink-0">上传图片：</div>
                    <div>
                      <FileUploader
                        ref={fileUploaderRef}
                        onSuccess={({ file }) => {
                          files.value.push(file)
                        }}
                        onRemove={({ file }) => {
                          const fileIndex = files.value.findIndex((item: M.UploadUserFileWithPath) => item?.uid === file?.uid)
                          files.value.splice(fileIndex, 1)
                        }} />
                    </div>
                  </div>
                </x-feedback-body>
                <x-feedback-footer class="flex justify-end gap-x-[10px]">
                  <RouterLink to="/user-feedback">
                    <Button class="btn btn-sm">返回</Button>
                  </RouterLink>
                  <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                    if (!content.value) {
                      showFailToast('请输入回复内容')
                      return
                    }
                    try {
                      btnLoading.value = true
                      await apiReplyIssues({
                        be_replied_id: lastQuestionId.value, // 被回复的问题id
                        reply_content: {
                          content: content.value, // 回复内容
                          image_list: files.value.map((item: M.UploadUserFileWithPath) => item.raw.path), // 图片列表
                        },
                      })
                      await getFeedbackDetail()
                      pollTask()
                      content.value = ''
                      files.value = []
                      fileUploaderRef.value?.clean()
                      showSuccessToast('操作成功')
                    } catch (error: any) {
                      showFailToast(error.response.data.message || '操作失败')
                    } finally {
                      btnLoading.value = false
                    }
                  }}
                  >
                    {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                    确认回复
                  </Button>
                </x-feedback-footer>
              </x-feedback-confirm-dialog>
            </x-feedback-reply>
          ) : null
        }
      </Wrapper>
    </x-user-feedback-detail>
  )
})

export default UserFeedbackDetail
