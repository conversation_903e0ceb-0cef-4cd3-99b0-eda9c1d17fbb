import { computed, Ref, ref, shallowRef, watch } from 'vue'
import { useSeriesItem } from './use-series-item'
import { findAncestor, getWindowHeight } from '@skynet/shared'
import { useTween } from '@skynet/ui'
import { useSwipe } from '@vueuse/core'

export function useSlide(series_id: string, wrapper: Ref<HTMLElement | undefined>, onSlideEnd?: (fromIndex: number, toIndex: number) => void) {
  const translateY = ref(0)
  const { seriesItem, currentEpisodeId, productPanelVisible, episodeSelectMenuVisible, activeIndex, getIndexInActiveEpisodes,
    getCurrentEpisode,
    episodeIdsHavingAdAfterIt, getEpisodesWithAds, currentAdId } = useSeriesItem(series_id)
  watch(() => currentEpisodeId.value, id => {
    if (!id) return
    const index = getIndexInActiveEpisodes(id)
    translateY.value = -index * getWindowHeight()
  }, { immediate: true })
  const deltaY = ref(0)
  const navigating = ref(false)
  const tween = shallowRef<null | ReturnType<typeof useTween>>(null)
  const currentEpisode = computed(() => getCurrentEpisode())
  let validSwipe = false
  // 下滑进入下一episode
  const { direction, lengthY } = useSwipe(wrapper, {
    threshold: 0,
    onSwipeStart(e: TouchEvent) {
      const el = e.target as HTMLElement
      validSwipe = !findAncestor(el, 'x-player-progress')
      deltaY.value = translateY.value
    },
    onSwipe() {
      if (!validSwipe) return
      if (seriesItem.value === null || activeIndex.value === undefined) return
      tween.value?.stop()
      if (episodeSelectMenuVisible.value || productPanelVisible.value) return
      const length = seriesItem.value?.episode_list.length + episodeIdsHavingAdAfterIt.value.length
      if (direction.value === 'up') {
        if (activeIndex.value >= length - 1) return
        // 如果当前剧集没解锁，则不能看下一集
        if (currentEpisode.value?.unlock !== true) return
      }
      if (direction.value === 'down' && activeIndex.value <= 0) return
      translateY.value = deltaY.value - lengthY.value
      if (lengthY.value > getWindowHeight() / 2) {
        navigating.value = true
      }
    },
    onSwipeEnd() {
      if (!validSwipe) return
      deltaY.value = 0
      const index = Math.abs(translateY.value / getWindowHeight())
      const targetIndex = direction.value === 'up'
        ? index - Math.floor(index) > 0.1 ? Math.ceil(index) : Math.floor(index)
        : index - Math.floor(index) > 0.9 ? Math.ceil(index) : Math.floor(index)
      const targetY = getWindowHeight() * targetIndex
      try { onSlideEnd?.(index, targetIndex) } catch (err) {}
      tween.value = useTween({
        from: { x: 0, y: translateY.value },
        to: { x: 0, y: -targetY },
        duration: 150,
        onUpdate({ y }) {
          translateY.value = y
        },
        onComplete() {
          // FIXME: 根据 targetIndex 计算 currentEpisodeId
          const list = getEpisodesWithAds()
          const target = list[targetIndex]
          if (!target) return
          if ('kind' in target) {
            currentEpisodeId.value = null
            currentAdId.value = target.id
          } else {
            currentEpisodeId.value = target.id ?? null
            currentAdId.value = null
          }
        },
      })
    },
  })
  return {
    translateY,
    tween,
  }
}
