<template>
  <div class="py-4">
    <span @click="router.back()" class="cursor-pointer">任务翻译</span>-{{transDetailData.name}}
  </div>
  <div class="bg-white p-4 rounded-md">
    <div>
        <div class="flex justify-end" v-if="route.query.from !== 'main-key'">
          <div class="relative">
            <input type="file" ref="fileInput" class="absolute top-0 left-0 opacity-0 w-full h-full cursor-pointer" accept=".xlsx,.xls" @change="handleFileChange" />
            <ElButton type="primary" @click="saveTranslate">上传Excel</ElButton>
          </div>
          <ElButton type="primary" link @click="downloadExcelTemplate">下载Excel模板</ElButton>
        </div>
        <div class="flex mt-2">
          <div class="w-[350px] bg-gray-200 p-2">
            <div class="flex justify-between items-center gap-2">
              <ElInput v-model="searchValue" placeholder="key" />
              <ElSelect v-model="searchStatus" placeholder="状态">
                <ElOption v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
              </ElSelect>
              <ElButton type="primary" @click="searchKey">查询</ElButton>
              <ElButton type="primary" @click="resetSearch">重置</ElButton>
            </div>
            <div class="mt-2 overflow-y-auto h-[80vh] bg-white p-2">
              <div v-for="item in searchList" :key="item.key_name">
                <div class="text-sm">{{ item.key_name }}---{{ item.completed ? '已完成' : '待翻译' }}<span v-if="checkExist(item.key_name)" class="text-red-500">(已存在)</span></div>
              </div>
            </div>
          </div>
          <div class="flex-1 bg-gray-200 p-2">
            <div class="flex py-2 border-b border-gray-400">
              <div class="w-1/2">源语言</div>
              <div class="w-1/2">目标翻译语言</div>
            </div>
            <div class="overflow-y-auto bg-gray-50 p-2" style="max-height: 70vh;">
                <div v-for="item in searchTableList" :key="item.key_name" class="mb-4">
                  <div class="flex items-center justify-between gap-2">
                    <div class="flex items-center gap-2">
                      <span class="text-[14px] mb-2" v-if="!checkExist(item.key_name)">{{item.key_name}}</span>
                      <ElCheckbox size="large" class="!text-lg" v-else v-model="mutiSelectResult" :label="item.key_name" />
                      <div class="text-red-500" v-if="checkExist(item.key_name)">翻译内容已存在</div>
                    </div>
                    <div>
                      <ElButton type="primary" link @click="deleteItem(item.key_name)" v-if="uploadedKeys[item.key_name]">删除</ElButton>
                    </div>
                  </div>
                  <div class="flex">
                    <div class="flex-1 border border-gray-400 p-2">
                      <div>语言：(en)</div>
                      <div>
                        {{item.content}}
                      </div>
                    </div>
                    <div class="flex-1 border border-gray-400 p-2">
                      <div class="py-2 border-b border-gray-400" v-for="inItem in item.translation" :key="inItem.language_code">
                        <div>语言：({{inItem.language_code}})</div>
                        <div class="mt-2">
                          <ElInput v-model="inItem.translation" type="textarea" />
                        </div>
                        <div class="flex justify-end mt-2">
                          <ElButton type="danger" size="small" @click="inItem.translation = ''">重置</ElButton>
                          <!-- <ElButton type="primary" size="small">保存</ElButton> -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>

            <div class="flex justify-end mt-2">
              <ElPagination layout="total, sizes, prev, pager, next" @size-change="handleSizeChange" v-model="pageInfo" :page-size="pageInfo.pageSize" :total="pageInfo.total" @current-change="handleCurrentChange" />
            </div>
            <div class="flex justify-end mt-2" v-if="route.query.from !== 'main-key'">
              <ElButton type="danger" @click="deleteMutiItem" v-if="Object.keys(uploadedKeys).length > 0">删除</ElButton>
              <ElButton type="primary" @click="saveTranslateDo">保存</ElButton>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElInput, ElButton, ElLoading, ElPagination, ElMessageBox, ElUpload, ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { get_k_sso_token } from 'src/lib/device-id.ts'
import { apiGetTaskDetail, apiGetTaskExcelModel, apiSaveTranslate, apiImportTaskExcel, apiGetMainKeyDetail } from './language-translate-api'
import dayjs from 'dayjs'
import axios from 'axios'
const route = useRoute()
const router = useRouter()
const transDetailData = ref({
  name: '',
  language: [],
})

const searchValue = ref('')
const searchStatus = ref()
const searchList = ref([])
const searchTableList = ref([])
const searchRightList = ref([])
const uploadedKeys = ref({})
const initListData = ref([])
const fileInput = ref()
const pageInfo = ref({
  page: 1,
  pageSize: 10,
  total: 0,
})
const mutiSelectResult = ref([])
const errorMsgList = ref({})

const statusList = ref([
  {
    label: '待翻译',
    value: 1,
  },
  {
    label: '已完成',
    value: 2,
  }
])

const saveTranslate = () => {
  console.log(transDetailData.value)
}

const searchKey = () => {
  filterKey()
}

const resetSearch = () => {
  searchValue.value = ''
  searchStatus.value = undefined
  filterKey()
}

const handleCurrentChange = (page: number) => {
  pageInfo.value.page = page
  getRightTableList()
}

const handleSizeChange = (size: number) => {
  pageInfo.value.pageSize = size
  pageInfo.value.page = 1
  getRightTableList()
}

const deleteMutiItem = () => {
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(() => {
    mutiSelectResult.value.forEach((item) => {
      deleteItemDo(item)
    })
    filterKey()
  })
}

const deleteItemDo = (keyName: string) => {
  // transDetailData.value.items = transDetailData.value.items.filter((item) => item.key_name !== keyName)
  delete uploadedKeys.value[keyName]
  for(let i in transDetailData.value.items) {
    if(transDetailData.value.items[i].key_name === keyName) {
      for(let j in initListData.value) {
        if(initListData.value[j].key_name === keyName) {
          transDetailData.value.items[i] = initListData.value[j]
          console.log(initListData.value[j])
          break
        }
      }
      break
    }
  }
  
}

const deleteItem = (keyName: string) => {
  ElMessageBox.confirm('确定删除任务吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(() => {
    deleteItemDo(keyName)
    filterKey()
  })
}

const saveTranslateDo = () => {
  if(Object.keys(uploadedKeys.value).length > 0) {
    ElMessageBox.confirm('是否覆盖旧翻译文案？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }).then(() => {
      saveTranslateDoSave()
    })
  }else{
    saveTranslateDoSave()
  }
  
}

const saveTranslateDoSave = () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  apiSaveTranslate({
    task_id: Number(route.params.id),
    items: transDetailData.value.items,
  }).then((res) => {
    ElMessage.success('保存成功')
    router.back()
  }).finally(() => {
    loading.close()
  })
}

const downloadExcelTemplate = () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  
  // 获取token
  const token = get_k_sso_token()
  if (!token) {
    ElMessage.error('未登录或token失效')
    loading.close()
    return
  }
  
  fetch(import.meta.env.VITE_DRAMA_API_URL+'/translation/task/excel/model', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'token': token,
      'Accept': '*/*'
    } as HeadersInit,
    body: JSON.stringify({
      task_id: Number(route.params.id)
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.blob()
  })
  .then(blob => {
    if (blob.size === 0) {
      throw new Error('下载的文件为空')
    }
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    const fileName = `${transDetailData.value.name}-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`
    a.download = fileName
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  })
  .catch(err => {
    ElMessage.error(err.message || '下载失败')
    console.error('下载失败:', err)
  })
  .finally(() => {
    loading.close()
  })
}

const filterKey = () => {
  console.log(transDetailData.value.items)
  let newList = []
  for(let i in transDetailData.value.items) {
    if(transDetailData.value.items[i].key_name.includes(searchValue.value)) {
      if(searchStatus.value == 1) {
        console.log(transDetailData.value.items[i].completed)
        if(!transDetailData.value.items[i].completed) {
          newList.push(transDetailData.value.items[i])
        }
      }else if(searchStatus.value == 2) {
        if(transDetailData.value.items[i].completed) {
          newList.push(transDetailData.value.items[i])
        }
      }else{
        newList.push(transDetailData.value.items[i])
      }
    }
  }
  const filterData = newList
  searchList.value = filterData
  console.log(searchList.value)
  pageInfo.value.total = filterData.length
  getRightTableList()
  // searchRightList.value = searchList.value.slice((pageInfo.value.page - 1) * pageInfo.value.pageSize, pageInfo.value.page * pageInfo.value.pageSize)
}

const handleFileChange = (e: any) => {
  const file = e.target.files[0]
  console.log(file)
  const loading = ElLoading.service({
    lock: true,
    text: '上传中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  const formData = new FormData()
  // 确保task_id是字符串类型
  const taskId = typeof route.params.id === 'string' ? route.params.id : String(route.params.id)
  formData.append('task_id', taskId)
  formData.append('file', file)

  axios({
    method: 'post',
    url: import.meta.env.VITE_DRAMA_API_URL+'/translation/task/excel/import',
    data: formData,
    headers: {
      'token': get_k_sso_token(),
      'Accept': '*/*',
      'Content-Type': 'multipart/form-data'
    }
  }).then((res: any) => {
    ElMessage.success('上传成功')
    console.log(res)
    if(res?.data?.data?.items?.length > 0) {
      // getTaskDetail()
      const items = res?.data?.data?.items
      items.forEach((item: any) => {
        if(!uploadedKeys.value[item.key_name]) {
          uploadedKeys.value[item.key_name] = true
        }
        let isExist = false
        for(let i in transDetailData.value.items) {
          if(transDetailData.value.items[i].key_name === item.key_name) {
            transDetailData.value.items[i] = item
            console.log('匹配到-=--'+item.key_name, transDetailData.value.items[i])
            isExist = true
            break
          }
        }
        if(!isExist) {
          transDetailData.value.items.push(item)
        }
      })
      // transDetailData.value.items = transDetailData.value.items.concat(items)
      console.log(transDetailData.value.items)
      console.log(initListData.value)
      filterKey()
      getRightTableList()
    }else{
      ElMessage.error('上传失败')
    }
  }).catch((error: any) => {
    ElMessage.error(error.response?.data?.err_msg || '上传失败')
    console.error('上传失败:', error)
  }).finally(() => {
    loading.close()
    fileInput.value.value = ''
  })
}

const checkExist = (keyName: string) => {
  return initListData.value.some((item: any) => item.key_name === keyName) && uploadedKeys.value[keyName]
}

const getTaskDetail = () => {
  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  const from = route.query.from
  if(from === 'main-key') {
    apiGetMainKeyDetail({
      key_id: Number(route.params.id)
    }).then((res) => {
      transDetailData.value = {
        name: res?.data?.key_name,
        language: [res?.data?.language_code]
      }
      transDetailData.value.items = [res?.data]
      initListData.value = JSON.parse(JSON.stringify([res.data] || []))
      filterKey()
      getRightTableList()
    }).catch((err) => {
      ElMessage.error(err.response?.data?.err_msg || '获取任务详情失败')
      console.error('获取任务详情失败:', err)
    }).finally(() => {
      loading.close()
    })
  }else{
    apiGetTaskDetail({
      task_id: Number(route.params.id),
    }).then((res) => {
      transDetailData.value = res.data
      transDetailData.value.items = res.data?.items || []
      initListData.value = JSON.parse(JSON.stringify(res.data?.items || []))
      console.log(initListData.value)
      filterKey()
      getRightTableList()
    }).catch((err) => {
      ElMessage.error(err.response?.data?.err_msg || '获取任务详情失败')
      console.error('获取任务详情失败:', err)
    }).finally(() => {
      loading.close()
    })
  }
}

const getRightTableList = () => {
  searchTableList.value = searchList.value.slice((pageInfo.value.page - 1) * pageInfo.value.pageSize, pageInfo.value.page * pageInfo.value.pageSize)
}

onMounted(() => {
  getTaskDetail()
})
</script>