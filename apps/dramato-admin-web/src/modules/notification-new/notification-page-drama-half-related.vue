<template>
  <div>
    <div class="my-4 p-4 bg-white rounded-lg shadow">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="push类型">
          <el-select v-model="searchForm.rec_type" style="width: 160px;" placeholder="请选择" clearable>
            <el-option :label="i.desc" :value="i.rec_type" v-for="i in pushConfig?.list_rec_type_list[3]"
              :key="i.rec_type"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="PushID">
          <el-input v-model="searchForm.push_task_id" clearable />
        </el-form-item>
        <el-form-item label="AI/人工">
          <el-select v-model="searchForm.gen_type" style="width: 120px" clearable placeholder="请选择">
            <el-option label="AI" :value="2" />
            <el-option label="人工" :value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="语言">
          <el-input v-model="searchForm.language" clearable />
        </el-form-item>
        <el-form-item label="prompt 编号">
          <el-input v-model="searchForm.prompt_id" type="number" clearable />
        </el-form-item>
        <el-form-item label="开关">
          <el-select v-model="searchForm.state" clearable style="width: 120px" placeholder="请选择">
            <el-option label="开" :value="1" />
            <el-option label="关" :value="60" />
          </el-select>
        </el-form-item>
        <el-form-item label="点击率PV小于%">
          <el-input v-model="searchForm.click_rate_pv_lt" placeholder="点击率(PV)小于%" clearable />
        </el-form-item>
        <el-form-item label="点击率UV小于%">
          <el-input v-model="searchForm.click_rate_uv_lt" placeholder="点击率(UV)小于%" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">筛选</el-button>
          <!-- <el-button @click="onReset">重置</el-button> -->
        </el-form-item>
      </el-form>
    </div>

    <div class="push-task-table p-4 bg-white rounded-lg shadow mb-4">
      <div class="flex justify-end mb-4 border-b-2">
        <!-- <el-button @click="handleBatchDownload"><el-icon>
            <Download class="mr-1" />
          </el-icon>批量下载</el-button> -->
        <el-button type="primary" @click="handleAdd"><el-icon>
            <Plus class="mr-1" />
          </el-icon>新增</el-button>
      </div>
      <el-table @selection-change="handleSelectionChange" :data="list" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="list_type" label="Push类型" width="100">
          <template #default="scope">
            {{pushConfig?.list_rec_type_list[3].find(i => +i.rec_type === +scope.row.rec_type)?.desc}}
          </template>
        </el-table-column>
        <el-table-column prop="push_task_id" label="PushId" width="80" />
        <el-table-column prop="gen_type" label="AI/人工" width="80">
          <template #default="scope">
            {{ scope.row.gen_type === 1 ? '人工' : 'AI' }}
          </template>
        </el-table-column>
        <el-table-column prop="language" label="语言" width="80" />
        <el-table-column prop="style_config.title" label="Push标题" min-width="120" show-overflow-tooltip />
        <el-table-column prop="style_config.body" label="Push内容" min-width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.gen_type === 1 ? scope.row.style_config.body : '/' }}
          </template>
        </el-table-column>
        <el-table-column prop="gen_id" label="GEN ID" width="80" />
        <el-table-column prop="prompt_id" label="Prompt" width="80" />

        <el-table-column prop="created" label="创建时间" width="160">
          <template #default="scope">
            {{ dayjs(scope.row.created * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="create_user_name" label="创建人" width="100" />
        <el-table-column prop="send_success_rate" label="成功率" width="100" />
        <el-table-column prop="send_cnt" label="发出条数" width="100" />
        <el-table-column prop="arrive_rate" label="到达率" width="100" />
        <el-table-column prop="show_rate" label="曝光率" width="100" />
        <el-table-column prop="click_rate_pv" label="点击率PV" width="100" />
        <el-table-column prop="click_rate_uv" label="拉活率UV" width="100" />
        <el-table-column prop="active_rate" label="拉活率" width="100" />

        <el-table-column prop="state" label="开关" width="200">
          <template #default="scope">
            <el-switch v-model="scope.row.state" :inactive-text="scope.row.language" :active-value="1"
              :inactive-value="60" @change="handleSwitch(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <div class="flex gap-2">
              <el-link type="success" v-if="scope.row.op_list.includes('preview')"
                @click="handlePreview(scope.row)">预览</el-link>
              <el-link type="primary" v-if="scope.row.op_list.includes('batch_data')"
                @click="handleBatch(scope.row)">分批次数据</el-link>
              <el-link type="primary" v-if="scope.row.op_list.includes('duplicate_and_create')"
                @click="handleDuplicate(scope.row)">复制并新增</el-link>
              <el-link type="primary" v-if="scope.row.op_list.includes('replace')"
                @click="onReplace(scope.row)">替换</el-link>
              <el-popconfirm confirm-button-text="是" cancel-button-text="否" :icon="InfoFilled" icon-color="#626AEF"
                title="确定删除吗?" @confirm="() => onDelete([scope.row.push_task_id])">
                <template #reference>
                  <el-link type="danger" v-if="scope.row.op_list.includes('delete')">删除</el-link>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container my-2">
        <el-pagination v-model:current-page="searchForm.page_info.page_index"
          v-model:page-size="searchForm.page_info.page_size" :total="total" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
      <el-dialog v-model="previewVisible" title="预览" width="500">
        <Preview />
      </el-dialog>

      <el-dialog v-model="addVisible" title="新建Push" width="500">
        <AddForm ref="addFormRef" />
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="addVisible = false">取消</el-button>
            <el-button type="primary" :loading="isUpdating" @click="handleSave">
              确定
            </el-button>
          </div>
        </template>
      </el-dialog>

      <el-dialog custom-class="replace-dialog" v-model="replaceVisible" title="一键替换" width="500">
        <ReplaceForm ref="replaceFormRef" :push-info="currentPush" />
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="replaceVisible = false">取消</el-button>
            <el-button type="primary" :loading="isReplaceFormUpdating" @click="onSeeReplaceResult">
              确定
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { Plus, Delete, Download, InfoFilled } from '@element-plus/icons-vue'
import ReplaceForm from './replace-form.vue'
import AddForm from './notification-add-form-v2.vue'
import Preview from './notification-preview.vue'
import { useNotification } from './use-notification';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';
import { nextTick } from 'vue';
import { openDialog } from '@skynet/ui';
import { PreviewReplaceResult } from './preview-replace-result';
import { apiSavePushText, apiUpdatePushState } from './notification-api';

const { getConfig, pushConfig, onSearchNotifications, list, loading, searchForm, total, form, onDelete, onDownload, initFromData, onReset, isUpdating } = useNotification('half-related')

onMounted(() => {
  console.log('mounted')
  getConfig();
  onReset(3);
})

const replaceFormRef = ref<InstanceType<typeof ReplaceForm>>()
const addFormRef = ref<InstanceType<typeof AddForm>>()
const multipleSelection = ref<M.PushNotificationNew[]>([])
const isReplaceFormUpdating = ref<boolean>(false)

const onSubmit = () => {
  console.log('submit!')
  console.log(searchForm)
  onSearchNotifications();
}

const addVisible = ref<boolean>(false)
const replaceVisible = ref<boolean>(false)
const previewVisible = ref<boolean>(false)


//列表类型，必传，1:人工push，2：剧集无关，3：剧集半相关，4：剧集相关
const listTypeMap: Record<number, string> = {
  3: '继续观看',
  16: '高付费剧',
  17: '限免剧',
  18: '折扣剧',
  23: '签到提醒',
  19: '订阅到期次日',
  20: '订阅到期5天',
  21: '免费部分完播',
  22: '充值用户',
  24: '预约新剧',
  26: '相关剧'
}

const handleSelectionChange = (val: M.PushNotificationNew[]) => {
  multipleSelection.value = val
}

const handleSwitch = async (row: M.PushNotificationNew) => {
  const res = await apiUpdatePushState({
    rec_type: 3,
    push_task_id: row.push_task_id,
    language: row.language,
    state: row.state
  })

  if (res.code !== 200) {
    return ElMessage.error(res.message)
  }

  onSearchNotifications()
}

// 处理操作方法
const handlePreview = (row: M.PushNotificationNew) => {
  console.log('预览', row)
  form.value = {
    ...row,
    style_config: {
      ...row.style_config,
      has_tips: row.style_config.tips ? true : false,
      // 需要手动管理按钮和进度条的显示
      media_progress_type: row.style_config.media_progress ? "2" : "1",
      buttonOrProgress: row.style_config.btn1_text ? '1' : row.style_config.media_progress ? '2' : '3',
      btn1_deep_link: extractUrl(row.style_config.btn1_deep_link),
      btn2_deep_link: extractUrl(row.style_config.btn2_deep_link),
      btn1_deep_link_url: getUrlFromDeepLink(row.style_config.btn1_deep_link),
      btn2_deep_link_url: getUrlFromDeepLink(row.style_config.btn2_deep_link),
    },
    target_app_names_source: row.target_app_names.split(','),
  }
  previewVisible.value = true
}

const handleBatch = (row: M.PushNotificationNew) => {
  window.open(`/notification-new/${row.list_type}/${row.id}/${row.push_task_id}`)
}
// dramawave://dramawave.com/series/123?url=https://dramawave.com/series/123
// 获取query中的url
const getUrlFromDeepLink = (deepLink: string): string => {
  return decodeURIComponent(deepLink.split('url=')[1] || '')
}
// dramawave://dramawave.com/series/123?url=https://dramawave.com/series/123
// 获取原始的url
const extractUrl = (url: string): string => {
  return url.split('?')[0]
}


const handleBatchReplace = () => {
  console.log('批量替换', multipleSelection.value)

}


const handleBatchDownload = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要下载的数据')
    return
  }
  onDownload(multipleSelection.value.map(item => item.id), 1, 3)
  console.log('批量下载', multipleSelection.value)
}

const handleDuplicate = (row: M.PushNotificationNew) => {
  console.log('复制并新增', row)
  form.value = {
    ...row,
    push_task_id: undefined,
    // style_config: {
    //   ...row.style_config,
    //   has_tips: row.style_config.tips ? true : false,
    //   // 需要手动管理按钮和进度条的显示
    //   media_progress_type: row.style_config.media_progress ? "2" : "1",
    //   buttonOrProgress: row.style_config.btn1_text ? '1' : row.style_config.media_progress ? '2' : '3',
    //   btn1_deep_link: extractUrl(row.style_config.btn1_deep_link),
    //   btn2_deep_link: extractUrl(row.style_config.btn2_deep_link),
    //   btn1_deep_link_url: getUrlFromDeepLink(row.style_config.btn1_deep_link),
    //   btn2_deep_link_url: getUrlFromDeepLink(row.style_config.btn2_deep_link),
    // },
    // target_app_names_source: row.target_app_names.split(','),
  }
  addVisible.value = true
}

const handleBatchData = (row: M.PushNotificationNew) => {
  console.log('分批次数据', row)
}

const handleAdd = (): void => {
  form.value = cloneDeep({ ...initFromData, list_type: 3 })
  addVisible.value = true
}

// 处理页码改变
const handleCurrentChange = (val: number): void => {
  searchForm.value.page_info.page_index = val
  onSearchNotifications()
}

// 处理每页条数改变
const handleSizeChange = (val: number): void => {
  searchForm.value.page_info.page_size = val
  onSearchNotifications()
}

const handleSave = async () => {
  const res = await addFormRef.value?.submit()
  // 保存成功再关闭页面
  if (res) {
    addVisible.value = false
  }
}


const currentPush = ref<M.PushNotificationNew>()

const onReplace = (row: M.PushNotificationNew) => {
  currentPush.value = row
  replaceVisible.value = true

  nextTick(() => {
    replaceFormRef.value?.getPrompts()
  })
}

const onSeeReplaceResult = async () => {
  isReplaceFormUpdating.value = true
  const rs: any = await replaceFormRef.value?.onSubmit()
  isReplaceFormUpdating.value = false
  console.log(rs);

  if (!rs) {
    return
  }

  const hide = openDialog({
    title: '替换结果',
    body: () => <PreviewReplaceResult
      result={rs?.gen_txt || {
        title: '',
        body: '',
        language: '',
      }}
      onHide={hide}
      onSubmit={async (gen_txt) => {
        const r = await apiSavePushText({
          ...rs,
          gen_txt
        })

        if (r.code !== 200) {
          ElMessage.error(r.msg || r.message || '保存失败')
          return
        }
        hide()
        replaceVisible.value = false
        onSearchNotifications()
      }}
    />,
    mainClass: 'w-[820px]',
    customClass: '!w-[820px]',
  })
}
</script>

<style scoped></style>