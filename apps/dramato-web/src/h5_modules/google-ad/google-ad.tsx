import { createComponent, fn } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { initGoogleAds } from 'src/init/init-third-party'
import { onMounted, ref } from 'vue'
import { useGoogleAd } from './use-google-ad'
import { CT } from '../i18n/common.i18n'
import { Fn } from '@vueuse/core'
import { h5Track } from 'src/lib/h5-track'
type GoogleAdOptions = {
  props: {
    dom?: HTMLElement | null
  }
  emits: {
    next: Fn
  }
}
export const GoogleAd = createComponent<GoogleAdOptions>({
  props: {
    dom: null,
  },
  emits: {
    next: fn,
  },
}, (props, { emit }) => {
  const loading = ref(true)
  const wrapper = ref<HTMLElement | null>(null)
  const adWrapper = ref<HTMLElement | null>(null)
  const ad = ref<HTMLElement | null>(null)
  onMounted(() => {
    h5Track('ads', '', 'show', {
      ads_type: 'display',
    })
    if (!ad.value) return
    const el = ad.value
    // 监听 ad.value 标签的属性变化，一旦发现 data-ad-status 变为 'filled'，就说明广告加载完成
    const observer = new MutationObserver(mutations => {
      for (const mutation of mutations) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-ad-status') {
          const status = el.getAttribute('data-ad-status')
          if (status === 'filled') {
            loading.value = false
            observer.disconnect()
          } else if (status === 'unfilled') {
            loading.value = false
            if (!adWrapper.value) return
            adWrapper.value.style.display = 'none'
          }
        }
      }
    },
    )
    observer.observe(el, {
      attributes: true,
      attributeFilter: ['data-ad-status'],
    })
    void initGoogleAds?.()?.then(() => {
      // @ts-expect-error 这是 Google 提供的代码
      (adsbygoogle = window.adsbygoogle || []).push({})
    })
  })
  onMounted(() => {
    if (!wrapper.value) return
    if (props.dom) {
      wrapper.value.$el.appendChild(props.dom)
    }
  })
  const { getAdWidth, getAdHeight } = useGoogleAd()

  return () => (
    <MergeClass tag="x-google-ad" baseClass="flex items-center justify-center relative" ref={wrapper}
      style={{ width: getAdWidth() + 'px', height: getAdHeight() + 'px' }}
    >
      <span class="absolute left-0 top-full w-full py-3 text-center capitalize"
        onClick={() => emit('next')}
      >{CT.playNextEpisode()}</span>
      {props.dom
        ? null
        : (
            <>
              {loading.value
                ? (
                    <x-loading-ad class="absolute inset-0 flex shrink-0 flex-col items-center justify-center gap-y-4">
                      <div class="border-t-solid size-[64px] shrink-0 animate-spin rounded-full border-t-4 border-blue-500 border-x-transparent
              border-b-transparent" />
                      <div class="shrink-0">
                        Loading Ad ...
                      </div>
                    </x-loading-ad>
                  ) : (
                    null
                  )}

              <x-ad class="relative flex h-[600px] w-[320px] items-center justify-center " ref={adWrapper}>
                {/* <!-- 播放页 --> */}
                <ins class="adsbygoogle grow-1"
                  ref={ad}
                  style="display:inline-block;width:330px;height:520px"
                  data-ad-client="ca-pub-4028685783554495"
                  data-ad-slot="8311153139" />
              </x-ad>
            </>
          ) }
    </MergeClass>
  )
})

export default GoogleAd
