/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, fn, mc } from '@skynet/shared'
import { Button, CreateTableOld } from '@skynet/ui'
import { ref } from 'vue'
import { Fn } from '@vueuse/core'
import { languageMap } from './spend_board_material-item'

type TaskFormOptions = {
  props: {
    lang: string
    downloadList: {
      title: string
      serial_number: number
      origin_path: string
    }[]
  }
  emits: {
    cancel: Fn
    ok: () => void
  }
}
export const TaskForm = createComponent<TaskFormOptions>({
  props: {
    downloadList: [],
    lang: '',
  },
  emits: {
    cancel: fn,
    ok: fn,
  },
}, (props, { emit }) => {
  const Table = CreateTableOld<{
    serial_number: number
    origin_path: string
    title: string
  }>()

  const checkedItems = ref(new Map<number, boolean>())
  const previewSrc = ref('')
  const downloadingSeries = ref<string[]>([])

  const makeExcel = (data: any, name: string) => {
    return new Promise(resolve => {
    // 将数据转换为 CSV 格式
      const csvContent = data.map((e: any) => e.join(',')).join('\n')
      // 添加 UTF-8 BOM
      const bom = '\uFEFF'
      const finalContent = bom + csvContent

      // 创建一个 Blob 对象
      const blob = new Blob([finalContent], { type: 'text/csv;charset=utf-8;' })

      // 创建下载链接
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', name)
      document.body.appendChild(link)
      link.click()
      resolve('')
    })
  }

  const downloadExcel = async () => {
    try {
      const langList = [props.lang]
      const tableHeader = [
        '集数',
        languageMap[props.lang],
      ]
      const notFinished = ['trans_failed', 'extract_failed', 'failed', 'processing', '']
      const tableData = []
      for (let i = 1; i <= props.downloadList.length; i++) {
        const row = props.downloadList.find(item => item.serial_number === i)
        if (!row) {
          tableData.push([i, ...langList.map(_ => '-')])
        } else {
          tableData.push([
            row.serial_number,
            row.origin_path,
          ])
        }
      }

      await makeExcel([
        tableHeader,
        ...tableData,
      ], props.downloadList.length + '')
    } catch (error) {

    }
  }

  const downloadSeries = async (params: { title: string, origin_path: string }[]) => {
    // 如果已经有在下载中的重复任务，先过滤掉
    const newParams = params.filter(p => !downloadingSeries.value.includes(p.origin_path))
    downloadingSeries.value = downloadingSeries.value.concat(newParams.map(p => p.origin_path))
    const downloadTasks = newParams.map(async p => {
      const response = await fetch(p.origin_path)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = p.title + p.origin_path.slice(p.origin_path.lastIndexOf('.'))
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      downloadingSeries.value = downloadingSeries.value.filter(item => item !== p.origin_path)
    })

    await Promise.all(downloadTasks)
  }

  return () => (
    <div class="flex">
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <div class="flex justify-between">
          <label class="font-bold  text-[18px]">您可下载全部的剧集（共{props.downloadList.length}集）</label>
          <div>
            <Button class="btn btn-sm btn-primary ml-2" onClick={() => {
              void downloadSeries(props.downloadList.map(item => ({ title: item.title, origin_path: item.origin_path })))
            }}
            >批量下载
            </Button>
            <Button class="btn btn-sm btn-primary ml-2" onClick={() => {
              void downloadExcel()
            }}
            >导出下载
            </Button>
          </div>
        </div>
        <Table
          class="tm-table-fix-first-column"
          list={props.downloadList || []}
          columns={[
            [row => (
              <input type="checkbox" checked={checkedItems.value.size === props.downloadList.length} onChange={e => {
                if ((e.target as HTMLInputElement).checked) {
                  props.downloadList.forEach(item => {
                    checkedItems.value.set(item.serial_number, true)
                  })
                } else {
                  checkedItems.value.clear()
                }
              }}
              />
            ), row => (
              <input type="checkbox" checked={checkedItems.value.has(row.serial_number)} onChange={e => {
                checkedItems.value.set(row.serial_number, (e.target as HTMLInputElement).checked)
              }}
              />
            ), { class: 'w-[40px]' }],
            ['剧集名称', 'title', { class: 'w-[120px]' }],
            ['操作', row => {
              return (
                <div class={mc('flex items-center')}>
                  <Button class="btn btn-sm btn-primary" onClick={() => {
                    previewSrc.value = row.origin_path
                  }}
                  >播放
                  </Button>
                  <Button class={mc('btn ml-2 btn-sm btn-primary')} disabled={downloadingSeries.value.includes(row.origin_path)} onClick={() => {
                    void downloadSeries([{ title: row.title, origin_path: row.origin_path }])
                  }}
                  >下载<span class={downloadingSeries.value.includes(row.origin_path) ? 'loading loading-spinner' : 'hidden'} />
                  </Button>
                </div>
              )
            }, { class: 'w-[80px]' }],
          ]}
        />
      </div>
      <div class="preview-area w-[300px] h-[533px]">
        {!previewSrc.value && <div class="flex items-center justify-center h-full text-gray-500">暂无预览</div>}
        {previewSrc.value && <video class="w-[300px] h-full" src={previewSrc.value} autoplay controls />}
      </div>
    </div>
  )
})

export default TaskForm
