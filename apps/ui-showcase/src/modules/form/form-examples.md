---

name: FormExamples
title: 表单示例
---

[[toc]]

# 示例

## 横向表单

<demo component="/src/modules/form/horizontal-form.tsx" />

## 纵向表单

纵向表单只需要在横向表单的基础上加一些 CSS 即可。

<demo component="/src/modules/form/vertical-form.tsx" />

## 嵌套表单项目

<demo component="/src/modules/form/nested-form-items.tsx" />

# 属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| data | 表单数据对象 | object | 必填 |
| dataReady | 数据是否准备就绪，reset 时会使用就绪的数据 | boolean | false |
| error | 表单错误信息 | any | null |
| items | 表单项配置数组 | FormItems | [] |
| actions | 自定义操作按钮 | Function \| VNode | null |
| class | 表单容器的自定义类名 | string | '' |
| actionClass | 操作按钮区域的自定义类名 | string | '' |
| labelClass | 标签的自定义类名 | string | '' |
| hasAction | 是否显示操作按钮区域 | boolean | true |
| submitText | 提交按钮文本 | string | '提交' |
| resetText | 重置按钮文本 | string | '重置' |

# 事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| change | 表单项值变更时触发 | (path: string, value: any) |
| submit | 表单提交时触发 | (event: Event) |
| reset | 表单重置时触发 | (event: Event, initialData: any) |