import { defineComponent, onBeforeMount, ref, watchEffect } from 'vue'
import { ElButton, ElImage, ElMessage, ElUpload } from 'element-plus'
import { Button, SvgIcon } from '@skynet/ui'
import { useRoute, useRouter } from 'vue-router'
import { useMyDrama } from './use-my-drama'
import { Uploader, UploadImage } from '../common/uploader/uploader'
import { uploadSuccess, uploadFail, uploadLoading } from './images/uploadImgs'
import { useI18n } from 'vue-i18n'
interface FileListItem {
  serial_number: number
  videoUploadStatus: UploadStatus
  subtitleUploadStatus: UploadStatus
  videoFile: File | null
  subtitleFile: File | null
}

enum UploadStatus {
  INIT,
  UPLOADING,
  UPLOADED,
  FAILED,
}

export default defineComponent({
  name: 'UploadDramaPage',
  setup() {
    const { t } = useI18n()
    const router = useRouter()
    const route = useRoute()
    const { getDramaDetail, uploadSubtitle, currentDrama, uploadVideo, dramaDetailLoading, deleteResource, getSerialNumber, submitAudit, isEditable } = useMyDrama()
    const fileList = ref<FileListItem[]>()

    const uploadMap = new Map()

    watchEffect(async () => {
      if (route?.params.id) {
        const data = await getDramaDetail(route.params.id as string)
        if (data?.list?.length) {
          fileList.value = data.list.map(item => {
            return {
              serial_number: item.serial_number,
              videoUploadStatus: !!item.episode?.path ? UploadStatus.UPLOADED : UploadStatus.INIT,
              subtitleUploadStatus: !!item.subtitle?.path ? UploadStatus.UPLOADED : UploadStatus.INIT,
              videoFile: null,
              subtitleFile: null,
            }
          })
          console.log(fileList.value)
        }
      }
    })

    const onVideoUpload = (className: string) => {
      // .video-uploader 点击这个元素
      const uploader = document.querySelector(`${className} input`) as HTMLDivElement
      if (uploader) {
        uploader.click()
      }
    }

    const getLoadingStatus = (status: UploadStatus) => {
      switch (status) {
        case UploadStatus.UPLOADING:
          return {
            icon: () => <ElImage src={uploadLoading} class="mr-1 size-5 animate-spin" />,
            text: t('uploading'),
          }
        case UploadStatus.UPLOADED:
          return {
            icon: () => <ElImage src={uploadSuccess} class="mr-1 size-5" />,
            text: t('uploadSuccess'),
          }
        case UploadStatus.FAILED:
          return {
            icon: () => <ElImage src={uploadFail} class="mr-1 size-5" />,
            text: t('uploadFailed'),
          }
        default:
          return {
            icon: () => '',
            text: '',
          }
      }
    }
    return () => (
      <div class="mx-auto p-6" v-loading={dramaDetailLoading.value}>
        <div class="mb-8 flex items-center justify-between">
          <div class="flex" onClick={() => history.back()}>
            <SvgIcon name="ic-chevron-down" class="mr-1 size-6" />
            <h1 class="text-xl font-bold">{t('releaseNewDramaUploadVideoSubtitles')}</h1>
          </div>
          {!!fileList.value?.length && (
            <div>
              <Button disabled={!isEditable} onClick={() => onVideoUpload('.video-uploader')} class="btn btn-sm btn-primary btn-ghost btn-sm mr-4 w-[120px] py-3">{t('uploadVideos')}</Button>
              <Button disabled={!isEditable} onClick={() => onVideoUpload('.subtitle-uploader')} class="btn btn-sm btn-primary btn-ghost btn-sm w-[120px] py-3">{t('uploadSubtitles')}</Button>
            </div>
          )}

        </div>
        {/* { JSON.stringify(fileList.value) } */}
        {!!fileList.value?.length && (
          <div class="border">
            <el-table data={fileList.value} class="min-h-[500px] w-full">
              <el-table-column prop="serial_number" label={t('numberOfEpisodes')} width="180" />
              <el-table-column label={t('video')} width="180">
                {{
                  default: ({ row }: { row: FileListItem }) => {
                    const { text, icon } = getLoadingStatus(row.videoUploadStatus)
                    return <span class="flex items-center">{icon()}{text}</span>
                  },
                }}
              </el-table-column>
              <el-table-column label={t('subtitleFile')} width="180">
                {{
                  default: ({ row }: { row: FileListItem }) => {
                    const { text, icon } = getLoadingStatus(row.subtitleUploadStatus)
                    return <span class="flex items-center">{icon()}{text}</span>
                  },
                }}
              </el-table-column>
              <el-table-column label={t('operation')} width="auto">
                {{
                  default: ({ row }: { row: FileListItem }) => {
                    return (
                      <div class="flex gap-2">
                        <Button class="btn btn-sm btn-info group hover:text-[var(--brand-6)]" onClick={async () => {
                          if (row.videoUploadStatus === UploadStatus.UPLOADED) {
                            await deleteResource({
                              serial_number: row.serial_number,
                              resource_key: currentDrama.value.resource_key,
                              type: 0,
                            })
                          }
                          fileList.value = fileList.value?.filter(item => item.serial_number !== row.serial_number)
                        }}>
                          <SvgIcon noColor name="ic-trash" class="mr-1 size-5 group-hover:text-[var(--brand-6)]" />{t('delete')}
                        </Button>
                      </div>
                    )
                  },
                }}
              </el-table-column>
            </el-table>
          </div>
        )}
        {currentDrama.value.oss_type && (
          <div class={'grid grid-cols-2 gap-4 min-h-[508px]' + (!!fileList.value?.length ? ' hidden' : '')}>
            <div class=" rounded-lg ">
              <Uploader
                accept="mp4, mkv, avi, mov"
                maxsize={1024 * 1024 * 1000}
                isImage={false}
                class="video-uploader cursor-pointer overflow-hidden rounded-md"
                showFileList={false}
                beforeUpload={(obj: { files: File[] }) => {
                  console.log(obj.files)
                  // TODO：这里做名称拦截
                  const isNameCorrect = obj.files.some(item => {
                    const serial_number = getSerialNumber(item.name)
                    if (!serial_number || serial_number < 0 || serial_number > (currentDrama.value.count || 0)) {
                      return false
                    }
                    return true
                  })
                  if (!isNameCorrect) {
                    ElMessage.warning('文件名格式错误,请检查文件名后再上传')
                    return Promise.resolve(false)
                  }
                  const tempFileList = obj.files.map((file, index) => {
                    uploadMap.set(file.name, false)
                    const targetFile = fileList.value?.find(item => item.serial_number === getSerialNumber(file.name))
                    // 如果有，只更新视频文件
                    if (targetFile) {
                      targetFile.videoFile = file
                      targetFile.videoUploadStatus = UploadStatus.UPLOADING
                      return targetFile
                    }
                    // 如果没有，新增一条数据
                    return {
                      serial_number: getSerialNumber(file.name),
                      videoUploadStatus: UploadStatus.UPLOADING,
                      subtitleUploadStatus: UploadStatus.INIT,
                      videoFile: file,
                      subtitleFile: null,
                    }
                  })
                  const existSerialNumber = tempFileList.map(item => item.serial_number)
                  // 合并数据, 并按集数排序
                  const restFileList = fileList.value?.filter(item => !existSerialNumber.includes(item.serial_number))
                  fileList.value = [...(tempFileList || []), ...(restFileList || [])].sort((a, b) => a.serial_number - b.serial_number)
                }}
                onUploadSuccess={async d => {
                  if (uploadMap.get(d.file?.name)) return
                  uploadMap.set(d.file?.name, true)
                  const data = await uploadVideo(d)

                  fileList.value?.forEach(item => {
                    if (item.videoFile?.name === d.file?.name && item.videoFile?.size === d.file?.size) {
                      if (data?.temp_path) {
                        item.videoUploadStatus = UploadStatus.UPLOADED
                      } else {
                        item.videoUploadStatus = UploadStatus.FAILED
                      }
                    }
                  })
                }}
              >
                {{
                  default: () => (
                    <div class="flex h-[508px] flex-col items-center justify-center border border-dashed border-[#ECE9EC] bg-[#FDFBFC] p-5">
                      <SvgIcon name="ic-video-camera" class="mb-2 size-8" />
                      <div class="mb-2 font-medium text-gray-900">{t('clickUploadOrDragVideo')}</div>
                      <div class="text-xs text-[#A1A0A3]">
                        {t('videoNamingRecommendation')}
                      </div>
                      <ElButton class="mt-4">{t('uploadVideo')}</ElButton>
                    </div>
                  ),
                }}
              </Uploader>
            </div>

            <div class=" rounded-lg">
              <Uploader
                accept="srt"
                maxsize={1024 * 1024 * 1000}
                isImage={false}
                class="subtitle-uploader cursor-pointer overflow-hidden rounded-md"
                showFileList={false}
                beforeUpload={(obj: { files: File[] }) => {
                  console.log(obj.files)

                  const tempFileList = obj.files.map((file, index) => {
                    uploadMap.set(file.name, false)
                    const targetFile = fileList.value?.find(item => item.serial_number === getSerialNumber(file.name))
                    // 如果有，只更新视频文件
                    if (targetFile) {
                      targetFile.subtitleFile = file
                      targetFile.subtitleUploadStatus = UploadStatus.UPLOADING
                      return targetFile
                    }
                    // 如果没有，新增一条数据
                    return {
                      serial_number: getSerialNumber(file.name),
                      videoUploadStatus: UploadStatus.INIT,
                      subtitleUploadStatus: UploadStatus.UPLOADING,
                      videoFile: null,
                      subtitleFile: file,
                    }
                  })
                  const existSerialNumber = tempFileList.map(item => item.serial_number)
                  // 合并数据, 并按集数排序
                  const restFileList = fileList.value?.filter(item => !existSerialNumber.includes(item.serial_number))
                  console.log(tempFileList, restFileList)
                  fileList.value = [...(tempFileList || []), ...(restFileList || [])].sort((a, b) => a.serial_number - b.serial_number)
                }}
                onUploadSuccess={async d => {
                  if (uploadMap.get(d.file?.name)) return
                  uploadMap.set(d.file?.name, true)
                  const targetFile = fileList.value?.find(item => item.subtitleFile?.name === d.file?.name && item.subtitleFile?.size === d.file?.size)
                  if (targetFile?.subtitleUploadStatus === UploadStatus.UPLOADING) {
                    const data = await uploadSubtitle(d)
                    if (data?.temp_path) {
                      targetFile.subtitleUploadStatus = UploadStatus.UPLOADED
                    } else {
                      targetFile.subtitleUploadStatus = UploadStatus.FAILED
                    }
                  }
                }}
              >
                {{
                  default: () => (
                    <div class="flex h-[508px] flex-col items-center justify-center border border-dashed border-[#ECE9EC] bg-[#FDFBFC] p-5">
                      <SvgIcon name="ic-subtite" class="mb-2 size-8" />
                      <div class="mb-2 font-medium text-gray-900">{t('clickUploadOrDragSubtitle')}</div>
                      <div class="text-xs text-[#A1A0A3]">
                        {t('subtitleNamingRecommendation')}
                      </div>
                      <ElButton class="mt-4">{t('uploadVideo')}</ElButton>
                    </div>
                  ),
                }}
              </Uploader>
            </div>
          </div>
        )}
        <div class="mt-8 flex justify-end gap-4">
          <Button class="btn btn-info btn-sm w-[120px] py-3" onClick={() => router.push('/my-drama')}> {t('cancel')}</Button>
          <Button class="btn btn-primary btn-ghost btn-sm w-[120px] py-3" onClick={() => router.push(`/my-drama/edit/${route.params.id as string}`)}>{t('previous')}</Button>
          <Button disabled={!isEditable} class="btn btn-primary btn-sm w-[120px] py-3" onClick={() => submitAudit({
            resource_key: currentDrama.value.resource_key,
            audit_operate: 1,
          })}>{t('dramaSubmitForReview')}
          </Button>
        </div>
      </div>
    )
  },
})
