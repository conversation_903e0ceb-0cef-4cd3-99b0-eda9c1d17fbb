/**
 * 获取 client ip
 */
export default function useIp() {
  if (import.meta.client) {
    return {}
  }
  // 获取请求头
  const headers = useRequestHeaders(['x-forwarded-for', 'x-real-ip', 'remote-addr'])

  // 尝试从不同的请求头中获取 IP
  let clientIp: string | null = null

  if (headers['x-forwarded-for']) {
    const forwardedFor = headers['x-forwarded-for']
    clientIp = forwardedFor ? forwardedFor.split(',')[0].trim() : null
  } else if (headers['x-real-ip']) {
    const realIp = headers['x-real-ip']
    clientIp = realIp ? realIp.split(',')[0].trim() : null
  } else if (headers['remote-addr']) {
    const remoteAddr = headers['remote-addr']
    clientIp = remoteAddr ? remoteAddr.split(',')[0].trim() : null
  }

  // 如果都获取不到，返回默认值
  return {
    ip: clientIp || 'unknown'
  }
}
