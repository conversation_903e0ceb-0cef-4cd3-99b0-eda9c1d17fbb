import type { SupportedLocale} from '~/modules/common/constants';
import { supportedLocaleList } from '~/modules/common/constants'
import { useLocale } from '~/modules/i18n/i18n'
import { useHomePageStore } from '~/modules/home/<USER>'
import { langApi } from '~/modules/lang-select/lang-api'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'

export const initDefaultLang = () => {
  const { currentLocale } = useLocale()
  const route = useRoute()
  const { getHomeTab, getHomeModuleList } = useHomePageStore()
  const { skipFetch } = storeToRefs(useHomePageStore())
  const onLangChange = async function (l: SupportedLocale) {
    const target = supportedLocaleList.find(item => item.language === l)
    currentLocale.value.shortCode = target?.shortCode ?? 'en'
    currentLocale.value.language = target?.language ?? 'en-US'
    await langApi.settingLang(l)
    const res = await getHomeTab()
    await getHomeModuleList({ tab_key: res[0].tab_key, position_index: res[0].position_index, first: '' })
  }

  if (route.query.language) {
    const lang = route.query.language as string
    const target = supportedLocaleList.find(item => item.shortCode === lang)
    if (target) {
      skipFetch.value = true
      void onLangChange(target.language)
    }
  }
}
