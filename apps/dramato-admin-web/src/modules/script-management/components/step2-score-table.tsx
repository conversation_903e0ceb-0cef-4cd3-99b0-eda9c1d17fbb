/* eslint-disable @typescript-eslint/no-explicit-any */
import { Tooltip } from '@skynet/ui'
import { ElTable, ElTableColumn, ElSelect, ElOption } from 'element-plus'
import { ref, computed } from 'vue'

interface ScoreTableProps {
  data: M.ScoreTableData[]
  onScoreChange?: (data: M.ScoreTableData[], total: number, rating: string) => void
  disabled?: boolean
}

interface TableColumn {
  property: string
  [key: string]: any
}

export const Step2ScoreTable = (props: ScoreTableProps) => {
  const tableData = ref<M.ScoreTableData[]>(props.data)
  const totalScore = computed(() => {
    return tableData.value.reduce((sum, item) => sum + (item.score || 0), 0)
  })

  const rating = computed(() => {
    const score = totalScore.value
    if (score >= 95) return 'S'
    if (score >= 80) return 'A'
    if (score >= 60) return 'B+'
    return 'B'
  })

  const handleScoreChange = (value: number, row: M.ScoreTableData) => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      const newData = [...tableData.value]
      newData[index] = { ...row, score: value }
      tableData.value = newData

      props.onScoreChange?.(newData, totalScore.value, rating.value)
    }
  }

  const spanMethod = ({ row, column, rowIndex }: {
    row: M.ScoreTableData
    column: TableColumn
    rowIndex: number
  }) => {
    if (column.property === 'category') {
      const prevRow = tableData.value[rowIndex - 1]
      if (prevRow && prevRow.category === row.category) {
        return { rowspan: 0, colspan: 0 }
      }

      let count = 1
      for (let i = rowIndex + 1; i < tableData.value.length; i++) {
        if (tableData.value[i].category === row.category) {
          count++
        } else {
          break
        }
      }
      return { rowspan: count, colspan: 1 }
    }
  }

  return (
    <div class="w-full">
      <ElTable
        data={[...tableData.value, { id: 'total', category: '总分', criteria: '', score: totalScore.value }]}
        border={true}
        spanMethod={spanMethod}
        class="w-full"
        height={505}
      >
        <ElTableColumn
          prop="category"
          label="评估项目"
          width="120"
          v-slots={{
            default: ({ row }: { row: M.ScoreTableData }) => row.id === 'total'
              ? (
                  <div class="font-bold">
                    {row.category}
                  </div>
                )
              : row.category,
          }}
        />
        <ElTableColumn
          prop="criteria"
          label="评估维度"
          minWidth="300"
          v-slots={{
            default: ({ row }: { row: M.ScoreTableData }) => (
              row.id === 'total'
                ? (
                    <div class="font-bold">
                      {rating.value}
                    </div>
                  )
                : row.desc
                  ? (
                      <Tooltip
                        popContent={() => row.desc}
                        popWrapperClass="!max-w-[400px]"
                      >
                        <span>{row.criteria}</span>
                      </Tooltip>
                    )
                  : (
                      <span>{row.criteria}</span>
                    )
            ),
          }}
        />

        <ElTableColumn
          prop="score"
          label="评分"
          width="120"
          v-slots={{
            default: ({ row }: { row: M.ScoreTableData }) => row.id === 'total'
              ? (
                  <div class="font-bold">{row.score}</div>
                )
              : (
                  <ElSelect
                    v-model={row.score}
                    disabled={props.disabled}
                    placeholder="请选择"
                    onChange={(value: number) => handleScoreChange(value, row)}
                  >
                    {row.isOption
                      ? [0, 1, 2, 3, 4, 5].map(score => (
                          <ElOption
                            key={score}
                            label={score}
                            value={score}
                          />
                        ))
                      : [1, 2, 3, 4, 5].map(score => (
                          <ElOption
                            key={score}
                            label={score}
                            value={score}
                          />
                        ))}
                  </ElSelect>
                ),
          }}
        />
      </ElTable>
    </div>
  )
}
