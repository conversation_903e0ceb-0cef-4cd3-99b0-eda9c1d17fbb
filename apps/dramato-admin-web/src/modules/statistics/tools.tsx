/* eslint-disable @typescript-eslint/no-explicit-any */
import { nextTick } from 'vue'
import * as echarts from 'echarts'
import { get } from 'lodash-es'

const color = ['#3B82F6', '#FACC15', '#D50B95', '#14B8A6', '#F59E0B', '#FF5555']

const formatXAxis = (arr: M.IncomeData[]) => {
  return arr.map((row: { time_str: string }) => row.time_str?.split(' ')[1])
}

function getGradientColor(color: string) {
  return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: hexToRgba(color, 0.37), // 半透明
    },
    {
      offset: 1,
      color: hexToRgba(color, 0), // 完全透明
    },
  ])
}

// 将 hex 颜色转换为 rgba 格式
function hexToRgba(hex: string, alpha: number) {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

export const getOptions = async (data: M.IChartData, type: string, chartMap: any) => {
  const legendDescMap = {
    today: '当日',
    yesterday: '前一天',
    week_ago: '七天前',
    month_ago: '上月',
  }
  const legendKeys = Object.values(legendDescMap)
  // 获取x轴时间
  const xAxis = formatXAxis(data?.yesterday || [])

  const yAxis = Object.keys(legendDescMap).map((k, index) => {
    const statisticData = get(data, k, [])
    return {
      name: legendKeys[index],
      type: 'line',
      smooth: true,
      showSymbol: false,
      data: statisticData.map((row: { k_value: string }) => row.k_value),
      areaStyle: {
        color: getGradientColor(color[index]), // 动态计算渐变色
      },
    }
  })

  if (xAxis.length === 0) {
    chartMap.value[type].hasData = false
  } else {
    chartMap.value[type].hasData = true
  }
  await nextTick()
  chartMap.value[type].options = {
    color: color,
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: legendKeys,
    },
    grid: {
      left: 30,
      right: 30,
      bottom: 10,
      top: 30,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxis,
      splitLine: {
        show: true,
        lineStyle: {
          color: '#E7EAEE',
        },
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: '#E7EAEE',
        },
      },
    },
    series: yAxis,
  }
}
