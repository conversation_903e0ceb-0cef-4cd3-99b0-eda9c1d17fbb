declare namespace Api {

  namespace RoleMark {
    interface ListReqParams {
      series_resource_id?: number // 剧名
      page_index?: number // 当前页索引
      page_size?: number // 每页大小
    }

    interface IAiVoiceResourceInfo {
      num: number
      srt_path: string
      audio_path: string
      annotation: number
      voice: number
      ora_video_path: string
      pure_video_path: string
      episode_path: string
      voice_err_msg: string
      sentences: number
    }

    interface ISrt {
      language: string
      infos: IAiVoiceResourceInfo[]
    }

    interface IAiVoiceResource {
      default_language_code: string
      srts: ISrt[]
      audios: IResourceAudio[]
      subtitle_shorten_tasks: IShortAiTask[]
    }

    interface IAnnotation {
      order: number
      name: string
      character_id?: number
      official_name?: string
      // character_type: 0 | 1 // 角色类型 0-旧角色 1-新角色
      emotion: string
      effect: string
      term?: string
    }

    interface ICharacter {
      name: string
      character_id: number
      official_name: string
      isFixed?: boolean
      // character_type: 0 | 1 // 0 # 0-旧角色 1-新角色
    }

    interface ISingleSubtitle {
      srt_path: string
      annotations: IAnnotation[]
      characters: ICharacter[]
      emotions: string[]
      effects: string[]
      emotions_cn: string[]
      effects_cn: string[]
      origin_syllable_list: ISyllable[]
      shorten_syllable_list: ISyllable[]
    }

    interface IResource {
      num: number
      srt_path: string
      audio_path: string
      annotation: number
      voice: number
      ora_video_path: string
      pure_video_path: string
    }

    type ISubtitleLine = {
      time: string
      oldTime?: string
      content: string
      character_id?: number
      name?: string
      official_name?: string
      order?: number
      contentEditing?: boolean
      timeEditing?: boolean
      oldContent?: string

      selectedCharacters?: Api.RoleMark.IAnnotation[]
      emotion?: string
      effect?: string
      highlighted?: string
      errs?: M.IResourceSubtitleDetailError[]
      syllable?: Api.VoiceoverResource.ISyllable
    }
  }
}
