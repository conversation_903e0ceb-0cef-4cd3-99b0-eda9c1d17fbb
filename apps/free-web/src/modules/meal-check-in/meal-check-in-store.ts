import { ref } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { WelfareEatDetails } from './meal-check-in.d'
import { apiGetWelfareEatDetails } from './meal-check-in-api'
import { bindLoading, when } from '@skynet/shared'
import { authInfo } from 'src/lib/get-auth'
export const useMealCheckInStore = () => {
  const { t } = useLocale()
  const eatDetails = ref<WelfareEatDetails>()
  const getEatDetails = () => {
    void when(() => authInfo).then(async () => {
      const res = await apiGetWelfareEatDetails()
      if (!res.data) return
      eatDetails.value = res.data
    })
    // const res = {
    //   data: {
    //     welfare_id: 4, // 任务id
    //     welfare_key: '', // 任务key
    //     task_total_gold: 2400, // 任务总金币
    //     status: 1, // 状态
    //     eta_item_list: [{ // 任务列表
    //       schedule: 1, // 任务编号
    //       name: 'Breakfasreakfast', // 名称
    //       status: 1, // 状态
    //       gold: 500, // 金币数
    //       coins_icon: '', // 金币icon
    //       background_icon: '', // 背景
    //       bottom_show_text: 'Claleaable', // 底部展示文案
    //       is_curr_node: true, // 是否当前节点
    //     }, { // 任务列表
    //       schedule: 1, // 任务编号
    //       name: 'Lunch', // 名称
    //       status: 1, // 状态
    //       gold: 500, // 金币数
    //       coins_icon: '', // 金币icon
    //       background_icon: '', // 背景
    //       bottom_show_text: '7:00-9:00', // 底部展示文案
    //       is_curr_node: false,
    //     }, {
    //       schedule: 1, // 任务编号
    //       name: 'Dinner', // 名称
    //       status: 3, // 状态
    //       gold: 500, // 金币数
    //       coins_icon: '', // 金币icon
    //       background_icon: '', // 背景
    //       bottom_show_text: 'Claimed', // 底部展示文案
    //       is_curr_node: false,
    //     }, {
    //       schedule: 1, // 任务编号
    //       name: 'Late-night snack', // 名称
    //       status: 4, // 状态
    //       gold: 500, // 金币数
    //       coins_icon: '', // 金币icon
    //       background_icon: '', // 背景
    //       bottom_show_text: 'Re-claim available', // 底部展示文案
    //       is_curr_node: false,
    //     }],
    //     button_show: {
    //       btn_txt: 'CoardConard Congrateeward',
    //     },
    //     top_show: {
    //       title: 'Congrateeward Congrateeward Congrateeward Congrateeward',
    //       sub_title: 'Congrates_oneward Congrateeward Congrateeward',
    //     },
    //     bottom_show: {
    //       title: 'Congrates_oConx_Rewardd',
    //       sub_title: 'Congrates_on_Treasure_Box_RewdCongrates_on_Treasure_Box_Rewardd',
    //     },
    //     jump_widget: { // 跳转挂件
    //       widget_icon: '',
    //       widget_link: '',
    //       widget_text: '',
    //     },
    //     activity_rules_widget: { // 规则挂件
    //       widget_icon: '',
    //       widget_link: '',
    //       widget_text: '',
    //     },
    //   },

    // }
    // eatDetails.value = res.data
  }

  return {
    eatDetails,
    getEatDetails,
  }
}
