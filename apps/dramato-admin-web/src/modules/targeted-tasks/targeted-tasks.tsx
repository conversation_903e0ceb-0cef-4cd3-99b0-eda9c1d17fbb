/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { onMounted } from 'vue'
import { Button, DateTime, showAlert } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useTasks } from './use-targeted-tasks'
import { apiCancelTask, apiResetTask } from './targeted-tasks-api'
type TargetedTasksOptions = {
  props: {}
}
export const TargetedTasks = createComponent<TargetedTasksOptions>({
  props: {},
}, props => {
  const {
    Table,
    list,
    loading,
    search,
    syncTask,
    showCreateTaskDialog,
  } = useTasks()

  onMounted(() => {
    void search()
  })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>定向任务管理</li>
          </ul>
        ),
        tableActions: () => (
          <div class="flex justify-between items-center">
            定向任务管理表
            <x-buttons class="flex gap-x-2">
              <Button class="btn btn-primary btn-sm" onClick={syncTask}>同步任务</Button>
              <Button class="btn-primary btn btn-sm" onClick={showCreateTaskDialog}>定向任务</Button>
            </x-buttons>

          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['创建时间', row => <DateTime value={row.created * 1000} />, { class: 'w-[100px]' }],
            ['搜索关键词', 'search_key', { class: 'w-[200px]' }],
            [
              '状态',
              row => (
                <span class="badge badge-outline whitespace-nowrap">{[
                  { label: '待执行', value: 1 },
                  { label: '执行中', value: 2 },
                  { label: '执行完毕', value: 3 },
                  { label: '异常停止', value: 4 },
                ].find(item => item.value === row.status)?.label ?? row.status}
                {row.status === 4 && <span class="badge badge-error badge-outline ml-2">{row.err_msg}</span>}
                </span>
              ),
              { class: 'w-[80px]' },
            ],
            ['爬取数量', 'count', { class: 'w-[100px]' }],
            [
              '资源类型',
              row => (
                <span class="badge badge-outline whitespace-nowrap">{[
                  { label: '国内', value: 2 },
                  { label: '海外', value: 1 },
                ].find(item => item.value === row.info_from)?.label ?? row.info_from}
                </span>
              ),
              { class: 'w-[80px]' },
            ],
            ['备注', 'comment', { class: 'w-[200px]' }],
            [
              <span class="px-3">操作</span>,
              row => (
                <div class="flex gap-x-2">
                  <Button
                    class="btn btn-outline btn-xs"
                    onClick={() => {
                      if (!row.task_id) return
                      void apiCancelTask({
                        task_id: row.task_id,
                        serial_number: row.serial_number,
                      })
                        .then(() => {
                          void search()
                          showAlert('取消成功', 'success')
                        })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                  >
                    取消
                  </Button>
                  <Button
                    class="btn btn-outline btn-xs"
                    onClick={() => {
                      if (!row.task_id) return
                      void apiResetTask({
                        task_id: row.task_id,
                        serial_number: row.serial_number,
                      })
                        .then(() => {
                          void search()
                          showAlert('重试成功', 'success')
                        })
                        .catch((error: any) => {
                          showAlert(error.response.data.message, 'error')
                        })
                    }}
                  >
                    重试
                  </Button>
                </div>
              ), {
                class: 'w-[200px]',
              },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
      }}
    </NavFormTablePager>
  )
})

export default TargetedTasks
