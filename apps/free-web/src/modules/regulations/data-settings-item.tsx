import { createComponent } from '@skynet/shared'
import { Switch } from '../common/switch/switch'
type DataSettingsItemOptions = {
  props: {
    name: string
    desc: string
    modelValue: boolean
  }
  emits: {
    'update:modelValue': (value: boolean) => void
  }
}
export const DataSettingsItem = createComponent<DataSettingsItemOptions>({
  props: {
    name: '',
    desc: '',
    modelValue: false,
  },
  emits: {
    'update:modelValue': (value: boolean) => value,
  },
}, (props, { emit }) => {
  return () => (
    <div class="border-b-[1px] border-b-solid text-sm font-normal border-b-[var(--line-1)]">
      <div class="h-14 px-3 py-4 justify-between items-center flex" onClick={() => emit('update:modelValue', !props.modelValue)}>
        <span>{props.name}</span>
        <Switch modelValue={props.modelValue} />
      </div>
      <div class="w-full px-3 pt-2 pb-4 text-[var(--text-1)]">
        {props.desc}
      </div>
    </div>
  )
})
