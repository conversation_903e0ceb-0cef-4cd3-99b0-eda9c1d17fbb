import { httpClient } from 'src/lib/http-client'

export const apiGetAreaList = () => {
  return httpClient.post<ApiResponse<{
    list: M.Area[]
  }>>('/reward/area/list')
}

export const apiGetRewardConfigList = (data: { app_id?: number }) => {
  return httpClient.post<ApiResponse<{
    is_need_release: boolean // 更新上线按钮是否可点击
    list: M.RewardsConfig[]
  }>>('/reward/config/list', data)
}

export const apiDeleteConfig = (data: { id: number }) => {
  return httpClient.post<ApiResponse>('/reward/config/delete', data)
}

export const apiSaveConfig = (data: M.RewardsConfig | M.ResetRewardsConfig) => {
  return httpClient.post<ApiResponse>('/reward/config/save', data)
}

export const apiReleaseConfig = (data: { app_id: number }) => {
  return httpClient.post<ApiResponse>('/reward/config/release', data)
}
