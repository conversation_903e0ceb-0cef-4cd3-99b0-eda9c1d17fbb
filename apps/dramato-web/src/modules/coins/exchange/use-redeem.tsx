import { getQuery } from '@skynet/shared'
import { useLocale } from 'src/lib/use-locale'
import { GiftType } from './exchange'
import { dialogBg, indiaDialog, malaysiaDialog, ovoDialog, paypalDialog, philippinesDialog, pixDialog, redeemDialog, successDialog, thailandDialog, vietnamDialog } from '../images/images'
import { useCoinsStore } from '../coins-store'
import { openDialog } from 'src/modules/common/dialog'
import { computed, watchEffect } from 'vue'
import { track } from 'src/lib/track'

// 修改hooks函数定义
export const useRedeem = (gift: GiftType) => {
  const { t } = useLocale(getQuery('language', 'en'))
  const { getExchangeDetails } = useCoinsStore()
  const reachLimit = computed(() => gift?.user_coins >= gift?.gift_coins)

  watchEffect(() => {
    if (!reachLimit.value) {
      track('exchangezone', 'earncoins_button', 'show', {
        gift_id: gift?.id,
      })
    }
    if (gift?.can_exchange) {
      if (gift?.button_txt && gift?.button_link) {
        if (gift?.task_id === 10) {
          track('watchreels', 'redeemlevel', 'show', {
            brand: gift?.type,
          })
        } else if (gift?.task_id === 11) {
          track('checkin', 'redeemlevel', 'show', {
            brand: gift?.type,
          })
        }
        return
      }
      if (reachLimit.value) {
        track('exchangezone', 'redeem_button', 'show', {
          brand: gift?.type,
        })
      }
    }
  })

  const dialogTitleBg = {
    malaysia: malaysiaDialog,
    india: indiaDialog,
    vietnam: vietnamDialog,
    philippines: philippinesDialog,
    thailand: thailandDialog,
    brazil: pixDialog,
    ovo: ovoDialog,
    mexico: redeemDialog,
    paypal: paypalDialog,
  }

  const commonTitle = () => (
    <x-title class="relative block h-16 w-full">
      <img src={dialogTitleBg[gift?.type as keyof typeof dialogTitleBg]} class="absolute left-1/2 top-1/2 h-full w-auto -translate-x-1/2 object-cover" />
    </x-title>
  )

  const openRedeemFailDialog = () => { // 提现失败
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="text-center text-lg font-bold leading-snug text-[#090609]">{t('coins.exchange.redeemFailed')}</x-title>
            <x-content class="mb-6 mt-2 w-full text-center text-sm font-normal text-[#ff2d3e]">
              {t('coins.exchange.redeemFailedContent')}
            </x-content>
            <x-btn class="w-full rounded-lg border-none bg-[#fc2763] px-3 py-2.5 text-base font-bold text-white" onClick={() => closeDialog()}>
              <p class="line-clamp-3 w-full break-all text-center">{t('coins.exchange.gotItBtn')}</p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openRedeemSuccessDialog = () => { // 提现成功
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: () => (
        <x-title class="relative block h-[94px] w-full">
          <img src={successDialog} width={94} height={94} class="absolute left-1/2 top-1/2 h-full w-auto -translate-x-1/2 object-cover" />
        </x-title>
      ),
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 mt-2 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="text-center text-lg font-bold text-[#090609]">{t('coins.exchange.transferSuccessTitle')}</x-title>
            <x-content class="mb-5 mt-3 w-full text-center text-sm font-medium text-[#4d4e4f]">
              {t('coins.exchange.transferSuccessContent')}
            </x-content>
            <x-btn class="w-full rounded-lg border-none bg-[#fc2763] px-3 py-2.5 text-base font-bold text-white" onClick={() => closeDialog()}>
              <p class="line-clamp-3 w-full break-all text-center" onClick={() => {
                if (gift?.cash_success_btn_link) {
                  window.location.href = gift?.cash_success_btn_link
                } else {
                  void getExchangeDetails()
                  closeDialog()
                }
              }}
              >{gift?.cash_success_btn_txt ? gift?.cash_success_btn_txt : t('coins.exchange.gotItBtn')}
              </p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  return {
    reachLimit,
    commonTitle,
    openRedeemSuccessDialog,
    openRedeemFailDialog,
  }
}
