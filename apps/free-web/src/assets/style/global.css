@import './reset.css';
@import './vars.css';
@import './color-scheme.css';
@import './components.css';
@import './animate.css';
@unocss;


:root {
  color-scheme: light dark;
}

html, body, #app {
  max-height: 100vh;
  scroll-behavior: smooth;
  -webkit-tap-highlight-color: transparent;
  /* iOS Chrome */
  -ms-tap-highlight-color: transparent;
  /* IE */
  -moz-tap-highlight-color: transparent;
  /* Firefox */
  -o-tap-highlight-color: transparent;
  /* Opera */
}

#app > div:nth-child(1) {
  height: 100vh;
}


body {
  @apply bg-[var(--fill-4)] text-[var(--text-1)];
  font-synthesis: none;
  text-rendering: optimizeLegibility;
}

* {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    SF Pro,
    Arial,
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    sans-serif;
}

html.dark {
  .markmap {
    --markmap-text-color: #ffffff;
  }
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.tm-shadow {
  box-shadow: 0 10px 15px -3px #0a66c220, 0 4px 6px -4px #0a66c220;
}

.no-dark {
  background-color: #fff !important;
  color: #141414 !important;

  * {
    background-color: #fff !important;
    color: #141414 !important;
  }
}

.tm-overflow-hidden {
  overflow: hidden;
}

/** 处理 messgae-content 组件自带最小高问题  */
.tiptap {
  min-height: auto !important;
}


.share-bottom-8 {
  @apply pb-8;
}

.share-bottom-5 {
  @apply pb-5;
}

@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
  
  .share-bottom-8 {
    padding-bottom: calc(constant(safe-area-inset-bottom) + 2rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
  }

  .share-bottom-5 {
    padding-bottom: calc(constant(safe-area-inset-bottom) + 1.25rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 1.25rem);
  }

  .share-bottom-35 {
    padding-bottom: calc(constant(safe-area-inset-bottom) + 8.75rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 8.75rem);
  }
}

