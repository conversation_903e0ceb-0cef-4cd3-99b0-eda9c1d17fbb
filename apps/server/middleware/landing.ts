export default defineNuxtRouteMiddleware(async (to, from) => {
  if (import.meta.client) return

  if (to.path === '/landingpage') {
    const af_dp = to.query.af_dp
    if (!af_dp) {
      return navigateTo('/')
    }

    const series_id = af_dp // 这里假设af_dp就是series_id,根据实际情况可能需要其他解析逻辑

    // 重定向到series页面
    const queryParams = new URLSearchParams({
      return_to: '/',
      ...to.query, // 保留当前路由参数
    }).toString()

    return navigateTo('/series/' + series_id + '?' + queryParams)
  }
})
