import { createComponent } from '@skynet/shared'
import { FunctionalComponent, onMounted, ref } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import s from './left-aside.module.css'
type LeftAsideOptions = {
  props: {}
}
export const LeftAside = createComponent<LeftAsideOptions>({
  props: {},
}, () => {
  onMounted(() => {
    // 确保当前激活的路由在可视区域内
    aside.value?.querySelector('.router-link-active')?.scrollIntoView({ block: 'center' })
  })
  const aside = ref<HTMLElement | null>(null)
  return () => (
    <x-left-aside ref={aside} class={['block bg-fill-4 h-full sticky left-0 top-0 overflow-auto min-w-[12em]', s.wrapper]}>
      <nav class="">
        <ol>
          <Item path="/examples">示例</Item>
          <Item path="/get-started">开始使用</Item>
          <Item path="/installation">安装</Item>
        </ol>
        <Title>集成</Title>
        <hr class="mx-4 border-line-1" />
        <ol>
          <Item path="/crud/form">
            CRUD Form  <br />
            <small>增删改查 - 表单</small>
          </Item>
        </ol>
        <Title>工具</Title>
        <hr class="mx-4 border-line-1" />
        <ol>
          <Item path="/merge-class">MergeClass <br /><small> 合并样式 </small></Item>
          <Item path="/client-track">Client Track <br /> <small>数据上报</small></Item>
        </ol>
        <Title>通用</Title>
        <hr class="mx-4 border-line-1" />
        <ol>
          <Item path="/button">Button 按钮</Item>
          <Item path="/color">Color 颜色</Item>
          <Item path="/icon">Icon 图标</Item>
          <Item path="/input">Input 组件</Item>
          <Item path="/form-item">Form 表单组件</Item>
          <Item path="/form-item">Image 图片</Item>
          <Item path="/form-item">Toast 提示</Item>
        </ol>
        <Title>布局</Title>
        <ol>
          <Item path="/1">1</Item>
        </ol>
        <Title>表单</Title>
        <ol>
          <Item path="/1">1</Item>
        </ol>
        <Title>反馈</Title>
        <ol>
          <Item path="/alert">Alert 提示</Item>
        </ol>
      </nav>
    </x-left-aside>
  )
})

const Item: FunctionalComponent<{ path: string }> = (props, { slots }) => {
  return (
    <li>
      <RouterLink
        class="block py-3 px-4 hover:bg-brand-3 cursor-pointer
          [&.router-link-active]:bg-brand-6 [&.router-link-active]:text-text-6
        "
        to={props.path}
      >
        {slots.default?.()}
      </RouterLink>
    </li>
  )
}
const Title: FunctionalComponent = (_props, { slots }) => {
  return <h2 class="px-4 py-3 text-text-3">{slots.default?.()}</h2>
}
