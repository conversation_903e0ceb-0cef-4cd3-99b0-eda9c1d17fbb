// @ts-expect-error ignore
import { skynetUnoRules } from '@skynet/preset/uno'
import { twJoin, twMerge } from 'tailwind-merge'
import { ClassName } from './typings'

const _mergeClass = (_left: ClassName, _right: ClassName) => {
  const left = twJoin(_left)
  const right = twJoin(_right)
  const leftParts = left.split(' ').filter(item => !item.match(/\s+/g))
  const rightParts = right.split(' ').filter(item => !item.match(/\s+/g))
  skynetUnoRules.forEach((rules: [RegExp]) => {
    leftParts.forEach((left, index) => {
      const rule = rules[0]
      const matched = left.match(rule)
      if (matched) {
        const found = rightParts.find(right => right.match(rule))
        if (found) leftParts[index] = ''
      }
    })
  })
  return twMerge(leftParts.filter(Boolean).join(' '), rightParts.join(' '))
}
/**
 * @alias mc
 */
export const mergeClass = (...classes: ClassName[]) => {
  // 从后往前，使用 mergeClass两两合并
  return classes.reduceRight((prev, current) => _mergeClass(current, prev), '')
}
export const mc = mergeClass
