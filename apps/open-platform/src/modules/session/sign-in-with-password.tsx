import { createComponent, getQuery } from '@skynet/shared'
import { CreateForm, Button, Tab } from '@skynet/ui'
import { set } from 'lodash-es'
import { ref } from 'vue'
import { sessionApi } from './session-api'
import { keepError } from 'src/lib/http-client'
import { useSession } from './use-session'
import router from 'src/router'
import { RouterLink } from 'vue-router'
import { useI18n } from 'vue-i18n'
type Options = {
  props: {}
}
export const SignInWithPassword = createComponent<Options>({
  props: {},
}, () => {
  type Data = {
    phone: string
    pwd: string
  }
  const { t } = useI18n()
  const serverError = ref('')
  const Form = CreateForm<Data>()
  const { formData, signInWithPassword } = useSession()
  const returnTo = getQuery('return_to', '/')
  const onSignInWithPassword = async () => {
    await signInWithPassword().catch(keepError(error => serverError.value = error.message || '登录失败'))
    void router.push(returnTo)
  }
  return () => (
    <x-sign-in-with-password class="block">
      <Form data={formData.value} onSubmit={onSignInWithPassword} onChange={(path, value) => set(formData.value, path, value)}
        class="flex flex-col gap-6 [&_x-error]:hidden [&_x-label]:hidden "
        items={[
          ['手机号', 'phone', { type: 'text',
            class: 'w-[318px] !bg-[#faf7fa] p-4 leading-4 rounded-lg',
            wrapperClass: 'block h-auto',
            placeholder: t('enterPhoneNumber'),
          }, { }],
          ['密码', 'pwd', { type: 'password',
            class: 'w-[318px] !bg-[#faf7fa] p-4 leading-4 rounded-lg pr-[8em]',
            wrapperClass: 'block h-auto',
            placeholder: t('enterPassword'),
          }, { class: 'relative' }],
          serverError.value ? () => <div class="text-center text-sm text-[var(--brand-6)]">{serverError.value}</div> : null,
        ]}
        actions={() => (
          <x-action class="block w-full">
            <Button class="btn w-full" type="submit">{t('login')}</Button>
            {/* <div class="mt-2 text-center">
              <RouterLink to="/sign_up" class="block py-2 text-sm text-[#434546]">{t('registerNow')}</RouterLink>
            </div> */}
          </x-action>
        )}
      >
        {/* Add form fields here */}
      </Form>
    </x-sign-in-with-password>
  )
})

export default SignInWithPassword
