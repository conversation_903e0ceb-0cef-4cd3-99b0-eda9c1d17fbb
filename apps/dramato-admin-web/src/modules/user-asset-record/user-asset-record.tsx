import { createComponent, mc } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { DateTime, Pager, transformTimestamp, Button, openDialog, showFailToast } from '@skynet/ui'
import { set } from 'lodash-es'
import { useUserAssetRecord } from './use-user-asset-record'
import { apiGetUserOpsCompensation, apiGetUserOpsTransfer } from './user-asset-record-api'
import { requiredLabel } from 'src/lib/required-label'
type UserOptOptions = {
  props: {}
}
export const UserAssetRecord = createComponent<UserOptOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    page,
    pageSize,
    total,
    search,
    opsCompensationDetail,
    opsTransferDetail,
  } = useUserAssetRecord()
  const detailBtn = async (row: M.UserAssetRecord.Item) => {
    if (row.op_type == 'compensation') { // 补偿
      const res = await apiGetUserOpsCompensation({ id: row.id })
      console.log(res)
      opsCompensationDetail.value = res.data
      const hideDeleteDialog = openDialog({
        title: '补偿/奖励发放',
        mainClass: 'pb-0 px-5',
        body: (
          <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
            <x-delete-episode-body>
              <div>
                <div>目标用户ID：{opsCompensationDetail.value?.target_user_id}</div>
                <div>发放类型：{opsCompensationDetail.value?.compensation_type}(有效期10天)</div>
                <div>发放数量：{opsCompensationDetail.value?.compensation_amount}</div>
                <div>发放原因：{opsCompensationDetail.value?.compensation_reason}</div>
                <div>备注：{opsCompensationDetail.value?.remark}</div>
              </div>
            </x-delete-episode-body>
            <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
              <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>关闭</button>
            </x-delete-episode-footer>
          </x-delete-episode-confirm-dialog>
        ),
      })
    } else { // 资产转移
      const res = await apiGetUserOpsTransfer({ id: row.id })
      console.log(res)
      opsTransferDetail.value = res.data
      const hideDeleteDialog = openDialog({
        title: '资产转移记录',
        mainClass: 'pb-0 px-5',
        body: (
          <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
            <x-delete-episode-body>
              <div>
                <div>转出账号ID：{opsTransferDetail.value?.out_user_id}</div>
                <div class="mb-10">转入账号ID：{opsTransferDetail.value?.in_user_id}</div>

                <div>转移资产</div>
                <div class={mc(opsTransferDetail.value?.transfered_asset.subscription !== '' && '!inline-block', 'hidden')}>订阅权益：{opsTransferDetail.value?.transfered_asset?.subscription || '--'} (到期时间: <DateTime value={opsTransferDetail.value?.transfered_asset?.subscription_expire_time ? opsTransferDetail.value.transfered_asset?.subscription_expire_time * 1000 : '--'} />)</div>
                <div class={mc(opsTransferDetail.value?.transfered_asset.coins !== 0 && '!inline-block', 'hidden')}>金币：{opsTransferDetail.value?.transfered_asset.coins} coins</div>
                <div class={mc((opsTransferDetail.value?.transfered_asset.bonus !== 0 || opsTransferDetail.value.transfered_asset.rewards !== 0) && '!inline-block', 'hidden')}>奖励币：{opsTransferDetail.value?.transfered_asset?.bonus} bonus + {opsTransferDetail.value?.transfered_asset?.rewards} rewards</div>
                <div class={mc(opsTransferDetail.value?.transfered_asset.coupon !== 0 && '!inline-block', 'hidden')}>兑换券：{opsTransferDetail.value?.transfered_asset.coupon}</div>
                <div class={mc(opsTransferDetail.value?.transfered_asset?.unlocked_episode !== 0 && '!inline-block', 'hidden')}>已解锁的剧集：{opsTransferDetail.value?.transfered_asset?.unlocked_episode}</div>
              </div>
            </x-delete-episode-body>
            <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
              <button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>关闭</button>
            </x-delete-episode-footer>
          </x-delete-episode-confirm-dialog>
        ),
      })
    }
  }
  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li>资产操作记录<span class="text-red-500 text-sm">(仅支持查询最近一年的记录)</span></li>
          </ul>
        ),
        form: () => (
          <Form
            submitText="查询"
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            onReset={() => {
              params.value = { op_type: 'all' }
              page.value = 1
              pageSize.value = 20
              list.value = []
            }}
            onSubmit={() => {
              if (params.value.start_time && params.value.end_time) {
                void search()
              } else {
                showFailToast('请先选择日期范围哦～')
              }
            }}
            data={params.value}
            items={[
              {
                label: requiredLabel('日期范围-开始'),
                path: 'start_time',
                input: { type: 'datetime', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
                transform: transformTimestamp,
              },
              {
                label: requiredLabel('日期范围-结束'),
                path: 'end_time',
                input: { type: 'datetime', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm:ss' },
                transform: transformTimestamp,
              },
              {
                label: requiredLabel('类型'),
                path: 'op_type',
                input: { type: 'select', class: 'w-[240px]', options: [
                  {
                    value: 'all',
                    label: '全部',
                  }, {
                    value: 'asset_transfer',
                    label: '资产转移',
                  }, {
                    value: 'compensation',
                    label: '补偿/奖励发放',
                  },
                ], autoInsertEmptyOption: false },
              },
            ]}
          />
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['记录时间', row => (<DateTime value={(row?.op_time || 0) * 1000} />), { class: 'w-[150px]' }],
            ['操作类型', row => [{
              value: 'asset_transfer',
              label: '资产转移',
            }, {
              value: 'compensation',
              label: '补偿/奖励发放',
            }].find(item => item.value === row.op_type)?.label, { class: 'w-[100px]' }],
            ['涉及用户ID', 'target_users', { class: 'w-[100px]' }],
            ['操作人', 'operator', { class: 'w-[100px]' }],
            ['操作详情', row => (
              <div class="flex flex-nowrap">
                <Button class="btn btn-link btn-sm" onClick={() => {
                  void detailBtn(row)
                }}
                >详情
                </Button>
              </div>
            ), { class: 'w-[200px]' }],

          ]}
          />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search()
                  }}
                  onUpdate:size={() => {
                    void search()
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})
