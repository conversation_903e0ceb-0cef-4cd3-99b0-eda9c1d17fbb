import { createComponent, getQuery } from '@skynet/shared'
import { BackButton } from 'src/modules/common/button/back-button'
import { useActivityStore } from '../use-activity-store'
import { useRoute } from 'vue-router'
import { computed, onMounted } from 'vue'
import { track } from 'src/lib/track'
import { foolsDayImages } from './images/images'
import { ActivityBlock } from '../activity-block'
import { isIos } from 'src/lib/ua'

const imageToButtonMap: Record<number, number | null> = {
  0: null,
  1: 0,
  2: 1,
  3: null,
}

export const FoolsDayPage = createComponent(null, () => {
  const { config, fetchConfig } = useActivityStore()
  const route = useRoute()
  const language = getQuery('language', 'en')
  const eventId = getQuery('event_id', 0)
  const images = computed(() => {
    return foolsDayImages[language][isIos() ? 'ios' : 'android']
  })

  onMounted(() => {
    if (!eventId) return
    void fetchConfig(eventId)
  })

  onMounted(() => {
    track('activity_H5', 'page', 'show', {
      activity_id: route.query.event_id,
      user_id: route.query.user_id,
      from: route.query.from,
    })
  })
  return () => (
    <x-activity-page class="relative block bg-[#0c0a0d]">
      <BackButton class="absolute left-0 top-[max(env(safe-area-inset-top),_44px)] z-up" />
      <x-block-list class="flex flex-col gap-0">
        {images.value?.map((i, index) => (
          <ActivityBlock key={index} type="image" src={i} button={config.value[imageToButtonMap[index] ?? -1]} />
        ))}
      </x-block-list>
    </x-activity-page>
  )
})

export default FoolsDayPage
