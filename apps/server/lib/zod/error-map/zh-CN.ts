import { type ZodErrorMap, ZodIssueCode, ZodParsedType, util } from 'zod'
// 代码来自 https://github.com/colinhacks/zod/blob/v3.24.1/src/locales/en.ts
// 由 Cursor 直接意译为中文，提示词是：将英文 message 翻译成中文 message，不要直译，尽量意译，使其读起来更流畅。

export const zhCN: ZodErrorMap = (issue, _ctx) => {
  let message: string
  switch (issue.code) {
    case ZodIssueCode.invalid_type:
      if (issue.received === ZodParsedType.undefined) {
        message = '此项必填'
      } else {
        message = `类型不匹配，需要 ${issue.expected}，但得到 ${issue.received}`
      }
      break
    case ZodIssueCode.invalid_literal:
      message = `值不符合要求，应为 ${JSON.stringify(
        issue.expected,
        util.jsonStringifyReplacer,
      )}`
      break
    case ZodIssueCode.unrecognized_keys:
      message = `对象包含未知的属性：${util.joinValues(
        issue.keys,
        '、',
      )}`
      break
    case ZodIssueCode.invalid_union:
      message = `数据格式不正确`
      break
    case ZodIssueCode.invalid_union_discriminator:
      message = `标识值不正确，可选值为：${util.joinValues(
        issue.options,
      )}`
      break
    case ZodIssueCode.invalid_enum_value:
      message = `选项不正确，可选值为：${util.joinValues(
        issue.options,
      )}，当前值为：'${issue.received}'`
      break
    case ZodIssueCode.invalid_arguments:
      message = `函数参数不正确`
      break
    case ZodIssueCode.invalid_return_type:
      message = `函数返回值类型不正确`
      break
    case ZodIssueCode.invalid_date:
      message = `日期格式不正确`
      break
    case ZodIssueCode.invalid_string:
      if (typeof issue.validation === 'object') {
        if ('includes' in issue.validation) {
          message = `必须包含 "${issue.validation.includes}"`

          if (typeof issue.validation.position === 'number') {
            message = `${message}，且出现位置不能早于第 ${issue.validation.position} 位`
          }
        } else if ('startsWith' in issue.validation) {
          message = `必须以 "${issue.validation.startsWith}" 开头`
        } else if ('endsWith' in issue.validation) {
          message = `必须以 "${issue.validation.endsWith}" 结尾`
        } else {
          util.assertNever(issue.validation)
        }
      } else if (issue.validation !== 'regex') {
        message = `${issue.validation} 格式不正确`
      } else {
        message = '格式不正确'
      }
      break
    case ZodIssueCode.too_small:
      if (issue.type === 'array')
        message = `数组长度需${
          issue.exact ? '为' : issue.inclusive ? `不少于` : `超过`
        } ${issue.minimum} 个`
      else if (issue.type === 'string')
        message = issue.minimum === 1
          ? '必填'
          : `字符个数需${
            issue.exact ? '为' : issue.inclusive ? `不少于` : `超过`
          } ${issue.minimum} 个`
      else if (issue.type === 'number')
        message = `数值需${
          issue.exact
            ? `等于`
            : issue.inclusive
              ? `不小于`
              : `大于`
        } ${issue.minimum}`
      else if (issue.type === 'date')
        message = `日期需${
          issue.exact
            ? `为`
            : issue.inclusive
              ? `不早于`
              : `晚于`
        } ${new Date(Number(issue.minimum)).toLocaleString()}`
      else message = '格式不正确'
      break
    case ZodIssueCode.too_big:
      if (issue.type === 'array')
        message = `数组长度需${
          issue.exact ? `为` : issue.inclusive ? `不超过` : `少于`
        } ${issue.maximum} 个`
      else if (issue.type === 'string')
        message = `字符个数需${
          issue.exact ? `为` : issue.inclusive ? `不超过` : `少于`
        } ${issue.maximum} 个`
      else if (issue.type === 'number')
        message = `数值需${
          issue.exact
            ? `等于`
            : issue.inclusive
              ? `不大于`
              : `小于`
        } ${issue.maximum}`
      else if (issue.type === 'bigint')
        message = `数值需${
          issue.exact
            ? `等于`
            : issue.inclusive
              ? `不大于`
              : `小于`
        } ${issue.maximum}`
      else if (issue.type === 'date')
        message = `日期需${
          issue.exact
            ? `为`
            : issue.inclusive
              ? `不晚于`
              : `早于`
        } ${new Date(Number(issue.maximum)).toLocaleString()}`
      else message = '格式不正确'
      break
    case ZodIssueCode.custom:
      message = `格式不正确`
      break
    case ZodIssueCode.invalid_intersection_types:
      message = `多个类型的约束条件无法同时满足`
      break
    case ZodIssueCode.not_multiple_of:
      message = `数值必须是 ${issue.multipleOf} 的整数倍`
      break
    case ZodIssueCode.not_finite:
      message = '数值必须是有限数'
      break
    default:
      message = _ctx.defaultError
      util.assertNever(issue)
  }
  return { message }
}
