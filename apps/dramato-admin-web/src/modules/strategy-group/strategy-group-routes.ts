import { r, redirect } from '@skynet/shared'

export const strategyGroupRoutes = [
  r('strategy-group', 'IAP策略组', null, [
    r('', 'IAP策略组', () => import('src/modules/strategy-group/strategy-group-page.tsx')),
    r('create', '新建IAP策略组', () => import('src/modules/strategy-group/strategy-group-create-layout.tsx'), [
      redirect('', 'target'),
      r('target', '新建IAP策略组 - 目标条件', () => import('src/modules/strategy-group/strategy-group-create-target-page.tsx')),
      r('product', '新建IAP策略组 - 商品配置', () => import('src/modules/strategy-group/strategy-group-create-product-page.tsx')),
      r('plan', '新建IAP策略组 - 任务计划', () => import('src/modules/strategy-group/strategy-group-create-plan-page.tsx')),
    ]),
  ]),
]
