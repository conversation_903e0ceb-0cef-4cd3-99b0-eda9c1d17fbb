import { createComponent } from '@skynet/shared'
import free from '../image/free.png'
type HorizontalLayoutOptions = {
  props: {
    title: string
    list: M.DramaItem[]
    show_title?: boolean
  }
}
export const HorizontalLayout = createComponent<HorizontalLayoutOptions>({
  props: {
    title: '',
    list: [],
    show_title: true,
  },
}, props => {
  const dramaItem = (drama: M.DramaItem) => {
    return (
      <div class="flex flex-col items-center size-full relative">
        {drama.free && <img src={free} class="absolute right-1 top-1 w-40px h-auto object-cover shadow-sm" />}
        <img class="object-cover rounded-md h-[192px] w-auto shrink-0 grow-0" src={drama.cover} />
        <div class="w-full my-1 text-left text-[#cccacb] text-sm font-normal font-['SF Pro'] leading-[16px] line-clamp-2">{drama.title}</div>
      </div>
    )
  }
  return () => (
    props.list && (
      <x-column-horizontal class="w-full flex flex-col gap-2 overflow-x-auto">
        {props.show_title && <x-column-horizontal-title class="w-full h-[50px] pt-4 pb-3 text-[#fdfbfc] text-lg font-bold font-['SF Pro'] leading-snug">{props.title}</x-column-horizontal-title>}
        <x-column-horizontal-content class="flex flex-row gap-2 w-full items-center justify-start">
          {
            props.list.map(item => {
              return (
                <x-column-horizontal-drama class="shrink-0 w-[36.5%] h-[230px]">
                  {dramaItem(item)}
                </x-column-horizontal-drama>
              )
            })
          }
        </x-column-horizontal-content>
      </x-column-horizontal>
    )
  )
})
