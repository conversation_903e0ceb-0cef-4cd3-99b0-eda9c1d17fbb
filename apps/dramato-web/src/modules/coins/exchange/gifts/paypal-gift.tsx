import { createComponent, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { coins, dialogBg, paypalCard } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { ref, watch } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { openDialog } from 'src/modules/common/dialog'
import { useRedeem } from '../use-redeem'
type GiftOptions = {
  props: {
    gift?: GiftType
  }
}
export const PaypalGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      type: 'paypal',
    },
  },
}, props => {
  const { t } = useLocale()
  const tipsRef = ref<HTMLDivElement>()
  const email = ref('')
  const confirmEmail = ref('')
  const emailError = ref('')
  const confirmEmailError = ref('')
  let closeConfirmEmailDialog: Fn
  let closeConfirmDialog: Fn

  const { commonTitle, reachLimit } = useRedeem(props.gift)

  const openConfirmDialog = () => { // 确认兑换弹窗
    closeConfirmEmailDialog?.()
    closeConfirmDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      mainClass: 'rounded-sm',
      title: commonTitle,
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex flex-col rounded-xl bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="line-clamp-2 w-full text-center text-lg font-bold text-[#090609]">{t('coins.exchange.transferXConfirm', { X: props.gift?.amount })}</x-title>
            <x-content class="mb-6 mt-2 w-full text-center text-sm text-[#090609]">
              {t('coins.exchange.transferToYourAccountX', { X: email.value })}
            </x-content>
            <Button class="no-tap-color mx-7 h-11 w-[calc(100%-2.5rem)] rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={redeemHandler}>{t('coins.exchange.confirmBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const handleClick = () => {
    if (props.gift?.can_exchange) {
      if (reachLimit.value) {
        openEmailConfirmDialog()
      } else {
        track('exchangezone', 'earncoins_button', 'click')
        window.location.href = 'dramawave://dramawave.app/rewards_task?enter_from=exchange_page'
      }
    }
  }

  const flag = ref(false)
  watch(() => [tipsRef.value, flag.value], () => {
    flag.value = false
    if (tipsRef.value) {
      const lineHeight = parseFloat(getComputedStyle(tipsRef.value).lineHeight)
      const fontSize = parseFloat(getComputedStyle(tipsRef.value).fontSize)
      if (fontSize <= 8) {
        return
      }
      const maxHeight = lineHeight * 2
      if (tipsRef.value.scrollHeight > maxHeight) {
        tipsRef.value.style.fontSize = `${fontSize - 2 <= 8 ? 8 : fontSize - 2}px`
        flag.value = true
      }
    }
  })

  const openEmailConfirmDialog = () => {
    const handleEmailBlur = () => {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      if (!email.value) {
        emailError.value = t('coins.exchange.enterEmailError')
      } else if (!emailRegex.test(email.value)) {
        emailError.value = t('coins.exchange.enterEmailValidError')
      } else {
        emailError.value = ''
      }
    }
    const handleConfirmEmailBlur = () => {
      if (confirmEmail.value !== email.value) {
        confirmEmailError.value = t('coins.exchange.emailNotMatchError')
      } else {
        confirmEmailError.value = ''
      }
    }

    closeConfirmEmailDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      closeVisible: true,
      body: () => (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex flex-col bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col gap-y-4">
            <x-title class="truncate text-center text-lg font-bold text-[#090609]">{t('coins.exchange.transferConfirm')}</x-title>
            <x-form>
              <x-form-item class="block text-sm">
                <x-form-label class="font-bold">Paypal Account email</x-form-label>
                <input
                  type="email"
                  value={email.value}
                  onBlur={handleEmailBlur}
                  onInput={(e: Event) => {
                    email.value = (e.target as HTMLInputElement).value
                    emailError.value = ''
                  }}
                  placeholder={t('coins.exchange.paypalAccountEmail')}
                  class="dramato-input mt-2 bg-transparent py-3 pl-0"
                />
                <x-line class="border-top-1 block h-px w-full border-0 border-t border-solid border-[#CCCACB]"> </x-line>
                {emailError.value && <x-error class="line-clamp-2 w-full pt-1 text-xs text-[#FF3B30]">{emailError.value}</x-error>}
              </x-form-item>
              <x-form-item class="mt-4 block text-sm">
                <x-form-label class="font-bold">{t('coins.exchange.confirmPaypalAccountEmail')}</x-form-label>
                <input
                  type="email"
                  value={confirmEmail.value}
                  onBlur={handleConfirmEmailBlur}
                  onInput={(e: Event) => {
                    confirmEmail.value = (e.target as HTMLInputElement).value
                    confirmEmailError.value = ''
                  }}
                  placeholder={t('coins.exchange.confirmPaypalAccountEmail')}
                  class="dramato-input mt-2 bg-transparent py-3 pl-0"
                />
                <x-line class="border-top-1 block h-px w-full border-0 border-t border-solid border-[#CCCACB]"> </x-line>
                {confirmEmailError.value && <x-error class="line-clamp-2 w-full pt-[4px] text-xs text-[#FF3B30]">{confirmEmailError.value}</x-error>}
              </x-form-item>
            </x-form>
            <Button disabled={
              !email.value
              || !confirmEmail.value
              || !!emailError.value
              || !!confirmEmailError.value
            } class="no-tap-color mx-7 mt-4 h-11 w-[calc(100%-2.5rem)] rounded-lg border-none bg-[#fc2763] text-base font-bold text-white disabled:!bg-[#ffa0b6] disabled:text-[#fdfbfc]/50" onClick={openConfirmDialog}
            >
              {t('coins.exchange.transferX', { X: props.gift?.amount })}
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const redeemHandler = () => {
    closeConfirmDialog?.()
  }

  return () => (
    <x-gift-item class="flex gap-x-3 p-3">
      <x-gift-item-left class={mc('flex max-w-[44px] flex-col gap-1', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-card class="box-border size-11 rounded-lg border border-solid border-[#ece9ec] px-1 py-3">
          <img src={props?.gift?.image_url ?? paypalCard} class="h-auto w-full object-cover" />
        </x-card>
        <x-price class="block w-full break-all text-center text-xs font-bold text-[#4d4e4f]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class={mc('flex flex-1 flex-col gap-1 truncate', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-gift-title class="w-full truncate text-sm text-[#4d4e4f]">{props.gift.name}</x-gift-title>
        {
          props.gift?.can_exchange && (
            <x-gift-coins class="flex items-center text-sm">
              <img src={coins} width={16} height={16} class="mr-[2px] size-4 object-contain" />
              <x-gift-amount class="text-[#fc2763]">{props.gift.user_coins}</x-gift-amount>
              <x-separator class="px-[2px] text-[#a1a2a3]">/</x-separator>
              <x-gift-remains class="text-[#090609]">{props.gift.gift_coins}</x-gift-remains>
            </x-gift-coins>
          )
        }
        {
          props.gift?.can_exchange && (
            <x-gift-progress>
              <Progress class="mt-1 bg-[#ECE9EC]" value={props.gift.ratio} max={1} />
            </x-gift-progress>
          )
        }
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21 justify-center', props.gift?.button_tips ? 'gap-y-1' : '')}>
        <Button class={mc('min-h-8 w-full rounded-lg border-none px-1 pt-[2px] pb-[3px]  text-sm', !props.gift?.can_exchange ? 'bg-[#FFA0B6] !text-[#FDFBFC80]' : 'bg-[#fc2763] !text-white')} onClick={handleClick}>
          <p class="line-clamp-2 h-auto w-full text-wrap break-words text-center leading-4">
            {reachLimit.value ? t('coins.earnings.exchange') : t('coins.exchange.earnCoins')}
          </p>
        </Button>
        {props.gift?.button_tips && <x-tips class={mc('line-clamp-2 h-auto w-full break-words text-center text-[10px] leading-3 text-[#4d4e4f]', props.gift?.can_exchange ? '' : 'opacity-30')} ref={tipsRef}>{props.gift?.button_tips}</x-tips>}
      </x-gift-item-right>
    </x-gift-item>
  )
})
