<template>
  <div>
    <img
      class="block aspect-video w-full object-cover"
      :src="props.image"
      alt="banner"
    >
    <div class="mask" />
    <div class="info">
      <div
        v-if="!props.noTag"
        class="mb-1"
      >
        <div
          v-for="tag in props.tags"
          :key="tag.name"
          :style="{ 'background': tag.color }"
          class="tag"
        >
          {{ tag.name }}
        </div>
      </div>
      <div class="name">
        {{ props.name }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  noTag?: boolean
  name: string
  image: string
  tags: {
    name: string
    color: string
  }[]
}>()
</script>

<style scoped>
.mask {
  position: absolute;
  left: 0;
  bottom: -4px;
  width: 100%;
  height: 90px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.0002) 6.67%,
    rgba(0, 0, 0, 0.0066) 13.33%,
    rgba(0, 0, 0, 0.0156) 20%,
    rgba(0, 0, 0, 0.0305) 26.67%,
    rgba(0, 0, 0, 0.0527) 33.33%,
    rgba(0, 0, 0, 0.0837) 40%,
    rgba(0, 0, 0, 0.125) 46.67%,
    rgba(0, 0, 0, 0.178) 53.33%,
    rgba(0, 0, 0, 0.2441) 60%,
    rgba(0, 0, 0, 0.325) 66.67%,
    rgba(0, 0, 0, 0.4219) 73.33%,
    rgba(0, 0, 0, 0.5364) 80%,
    rgba(0, 0, 0, 0.6699) 86.67%,
    rgba(0, 0, 0, 0.824) 93.33%,
    rgba(0, 0, 0, 1) 100%
  );
}
.info {
  position: absolute;
  z-index: 1;
  bottom: 10px;
  left: 12px;
  width: calc(100% - 24px);

  .tag {
    display: inline-block;
    height: 18px;
    padding: 3px 4px;
    border-radius: 4px;
    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    margin-right: 6px;
  }

  .name {
    font-size: 18px;
    font-weight: 510;
    color: #fff;
    width: calc(100% - 74px);
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>