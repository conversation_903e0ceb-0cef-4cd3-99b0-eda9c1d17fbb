import { createComponent, mc } from '@skynet/shared'
import { Tab } from '@skynet/ui'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import { usePrizeRecordList } from './use-prize-record-list'
type RecordPageOptions = {
  props: {}
}
export const DrawPrizeRecordPage = createComponent<RecordPageOptions>({
  props: {},
}, props => {
  const { renderTableSearch, renderTable, renderPager, fetchPrizeRecordList } = usePrizeRecordList()
  onMounted(() => {
    void fetchPrizeRecordList()
  })
  return () => (
    <x-record-page class="block">
      <NavFormTablePager canExpand={false} class="p-0 m-0" tableClass="p-0">{{
        nav: () => (
          <Tab class="gap-4" itemClass={active => mc('text-gray-400 py-2 text-lg', active ? 'text-primary' : '')}
            modelValue="prizeRecords" items={[
              ['prizeList', () => <RouterLink to="/draw-prize">奖品列表</RouterLink>],
              ['prizeRecords', () => <RouterLink to="/draw-prize/record">中奖记录</RouterLink>],
            ]} />
        ),
        form: () => renderTableSearch(),
        table: () => renderTable(),
        pager: () => renderPager(),
      }}</NavFormTablePager>
    </x-record-page>
  )
})

export default DrawPrizeRecordPage
