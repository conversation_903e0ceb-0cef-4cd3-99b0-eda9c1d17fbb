/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { <PERSON><PERSON>, DialogFooter, openDialog, showAlert } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { apiCreateNewUserStrategy, apiUpdateNewUserStrategy } from './user-engagement-api'
import { userEngagementStore } from './user-engagement-store'

type userEngagementCreateStrategyPageOptions = {
  props: {}
}

export const userEngagementCreateStrategyPage = createComponent<userEngagementCreateStrategyPageOptions>({
  props: {},
}, props => {
  const { formData, Form, resetStrategyFormData, stepOneError, validateStepOne } = userEngagementStore()

  const router = useRouter()
  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')
  const refreshLoading = ref(false)
  const onRefresh = () => {
    if (refreshLoading.value) return
    refreshLoading.value = true
  }

  const onSubmit = async () => {
    if (!validateStepOne()) {
      openDialog({
        title: '出错提示',
        body: (
          <div>请检查表单<br />
            {JSON.stringify(stepOneError.value)}
          </div>
        ),
      })
      return
    }
    if (formData.value.is_abtest == 1) { formData.value.strategy_layer_ids = [] }
    const apiUrl = formData.value.id ? apiUpdateNewUserStrategy : apiCreateNewUserStrategy
    const response = await apiUrl({
      ...formData.value,
    }).catch((err: any) => {
      console.log('err', err)
      showAlert(err.response.data.err_msg || err.response.data.message, 'error')
    })
    if (!response?.data) return
    setTimeout(() => {
      void router.push({ path: route.path, query: { ...route.query, has_edit: 'false' } }).then(() => {
        const closeDialog = openDialog({
          title: '提示',
          body: (
            <div>保存成功
              <DialogFooter okText="返回列表页" onOk={() => {
                closeDialog()
                void router.push('/new-user-engagement')
              }} cancelText="留在当前页" onCancel={() => closeDialog()}
              />
            </div>
          ),
        })
      })
    })
  }

  return () => (
    <div class="flex flex-1 flex-col overflow-hidden p-8">
      <Form
        class="flex flex-col gap-0"
        data={formData.value}
        // error={stepTwoError.value}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        onReset={() => {
          resetStrategyFormData()
        }}
        onSubmit={onSubmit}
        items={[
          [requiredLabel('开剧模式'), 'undertake_type', {
            type: 'radio',
            options: [
              { label: '推荐弹窗（5秒开剧）', value: 2, disabled: isViewMode.value },
              { label: '推荐弹窗（点击开剧）', value: 3, disabled: isViewMode.value },
            ],
            disabled: isViewMode.value,
          }],
          [requiredLabel('推荐剧来源'), 'drama_source', {
            type: 'radio',
            options: [
              { label: '算法1', value: 1, disabled: isViewMode.value },
              { label: '推荐1（新用户归因当天播放uv数据）', value: 2, disabled: isViewMode.value },
              { label: '推荐2（历史90天播放uv数据）', value: 3, disabled: isViewMode.value },
            ],
            disabled: isViewMode.value,
          }],
        ]}
        actions={() => (
          <div class="mt-10 flex items-center justify-between gap-x-4">
            <Button class="btn btn-sm" type="button" onClick={() => {
              void router.push({ path: `/new-user-engagement/create/target`, query: { ...route.query } })
            }}
            >上一步
            </Button>
            {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button>}
            {isViewMode.value ? null : <Button class="btn btn-primary btn-sm" type="submit">保存</Button>}
          </div>
        )}
      />

    </div>
  )
})
export default userEngagementCreateStrategyPage
