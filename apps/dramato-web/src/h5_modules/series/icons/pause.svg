<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_18021_21669)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M29 54C30.1046 54 31 53.1046 31 52L31 20C31 18.8954 30.1046 18 29 18H22C20.8954 18 20 18.8954 20 20L20 52C20 53.1046 20.8954 54 22 54H29ZM50 54C51.1046 54 52 53.1046 52 52L52 20C52 18.8954 51.1046 18 50 18H43C41.8954 18 41 18.8954 41 20L41 52C41 53.1046 41.8954 54 43 54H50Z" fill="#FDFBFC" fill-opacity="0.7" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_18021_21669" x="12" y="11" width="48" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.56 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_18021_21669"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_18021_21669" result="shape"/>
</filter>
</defs>
</svg>
