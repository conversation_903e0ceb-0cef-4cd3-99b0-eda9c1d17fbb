/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { apiGetNameByLanguage } from '../resource-publish-api'
import { langKey, langValue } from 'src/modules/resource/constant'
import { ElCascader } from 'element-plus'
import { ref } from 'vue'
import { Button, Icon, showFailToast, showSuccessToast } from '@skynet/ui'
import { apiDistributeTask } from '../resource-publish-api'

type DistributeTableOptions = {
  props: {
    resourceIds: number[]
    type: 1 | 2 | 3 | 4 | number
  }
  emits: {
    cancel: () => void
    select: (users: string[][]) => void
  }
}
export const DistributeTable = createComponent<DistributeTableOptions>({
  props: {
    resourceIds: [],
    type: -1,
  },
  emits: {
    cancel: () => {},
    select: (users: string[][]) => {},
  },
}, (props, { emit }) => {
  const loading = ref(false)
  const cascaderOptions = ref<any[]>([])
  const selected = ref<string[][]>([])
  const btnLoading = ref(false)

  // 初始化语言选项
  const initLanguageOptions = () => {
    cascaderOptions.value = langKey.map((key, index) => {
      return {
        value: key,
        label: langValue[index],
        children: [],
        leaf: false,
      }
    })
  }

  // 加载二级菜单数据
  const loadData = async (node: any, resolve: any) => {
    if (node.level === 0) {
      return resolve([])
    }

    if (node.level === 1) {
      loading.value = true
      try {
        const languageCode = node.value
        const response = await apiGetNameByLanguage({ lang_code: languageCode })
        if (response && response.data?.names) {
          const children = response.data?.names.map((name: string) => ({
            value: name,
            label: name,
            leaf: true,
          }))
          resolve(children)
        } else {
          resolve([])
        }
      } catch (error) {
        console.error('获取语言对应人员失败:', error)
        resolve([])
      } finally {
        loading.value = false
      }
    } else {
      return resolve([])
    }
  }

  // 初始化
  initLanguageOptions()

  return () => (
    <x-distribute-table class="block">
      <ElCascader
        class="w-full"
        modelValue={selected.value}
        onUpdate:modelValue={(e: unknown) => {
          // selected.value = e as [string, string][]
          // 使用数组方法处理数据，避免使用Map迭代
          const uniqueItems: Record<string, [string, string]> = {}
          const reversedItems = [...e as [string, string][]]

          reversedItems.forEach((item: [string, string]) => {
            uniqueItems[item[0]] = item
          })

          // 转换为数组
          const uniqueValues = Object.values(uniqueItems)
          selected.value = uniqueValues
        }}
        options={cascaderOptions.value}
        show-all-levels
        props={{
          lazy: true,
          multiple: true,
          lazyLoad: loadData,
          checkStrictly: true,
          emitPath: true,
          disabled(data, node) {
            // 只允许选择叶子节点
            return node.level === 1
          },

        }}
        placeholder="请选择语言和人员"
        clearable
      />
      <x-distribute-table-footer class="flex w-full justify-end mt-4 gap-x-[10px]">
        <Button class="btn btn-ghost btn-sm" onClick={() => emit('cancel')}>取消</Button>
        <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
          if (selected.value.length === 0) {
            showFailToast('请选择人员')
            return
          }
          try {
            btnLoading.value = true
            const distribution_infos = selected.value.map(item => {
              return {
                claim_name: item[1],
                lang_code: item[0],
                resource_ids: props.resourceIds,
              }
            })
            await apiDistributeTask({
              distribution_infos,
              type: props.type,
            })
            showSuccessToast('操作成功')
            emit('select', selected.value)
          } catch (error: any) {
            showFailToast(error.response.data.err_msg || '操作失败')
          } finally {
            btnLoading.value = false
          }
        }}
        >
          {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
          确定
        </Button>
      </x-distribute-table-footer>

    </x-distribute-table>
  )
})

export default DistributeTable
