/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { onMounted, ref } from 'vue'
import { useRoleMark } from './use-role-mark-store'
import { ElTable, ElTableColumn } from 'element-plus'
import { RouterLink, useRoute } from 'vue-router'
import { Button } from '@skynet/ui'
import { useTopStore } from './use-top-store.tsx'

type RoleMarkPageOptions = {
  props: {}
}
export const RoleMarkPage = createComponent<RoleMarkPageOptions>({
  props: {},
}, props => {
  const { checkClearExpiredKeys } = useTopStore()
  const { loading, list, onQuery, showVideoAndSrt } = useRoleMark()
  const tableRef = ref<InstanceType<typeof ElTable>>()
  const route = useRoute()

  onMounted(() => {
    void onQuery(+route.params.id)
    checkClearExpiredKeys()
  })

  const handleSelectionChange = (selection: Api.RoleMark.ISrt[]) => {
    console.log(selection)
  }

  return () => (
    <x-timbre-page class="block space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <section class="breadcrumbs text-sm">
            <ul>
              <li>
                <RouterLink to="/resource-publish/voice-over"
                >配音管理
                </RouterLink>
              </li>
              <li>角色标注</li>
            </ul>
          </section>
        ),
        tableActions: () => {
          return (
            <div class="flex justify-end">
              <RouterLink to={`/voiceover-role?id=${+route.params.id}`}>
                <Button class="btn btn-sm btn-primary">角色标记</Button>
              </RouterLink>

            </div>
          )
        },
        table: () => (
          <ElTable
            ref={tableRef}
            v-loading={loading.value}
            onSelection-change={handleSelectionChange}
            data={list.value || []}
          >
            <ElTableColumn prop="num" label="集数" />
            <ElTableColumn prop="srt_path" align="left" label="字幕" v-slots={{
              default: (scope: { row: Api.RoleMark.IAiVoiceResourceInfo }) => {
                return (
                  <div class="flex items-center">
                    <RouterLink to={`/resource-publish/inspection-role-detail/${+route.params.id}?number=${scope.row.num}`}>
                      <Button class="btn btn-link btn-xs">角色标注</Button>
                    </RouterLink>
                  </div>
                )
              },
            }} />
            <ElTableColumn prop="annotation" label="标注进度" v-slots={{
              default: (scope: { row: Api.RoleMark.IAiVoiceResourceInfo }) => {
                return (
                  <div class="flex items-center">
                    {scope.row.annotation}/{scope.row.sentences}
                  </div>
                )
              },
            }} />
          </ElTable>
        ),
      }}
      </NavFormTablePager>
    </x-timbre-page>
  )
})

export default RoleMarkPage
