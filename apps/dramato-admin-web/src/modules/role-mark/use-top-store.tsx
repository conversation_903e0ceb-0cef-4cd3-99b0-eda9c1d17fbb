/* eslint-disable @typescript-eslint/no-explicit-any */
export const useTopStore = () => {
  return {
    saveData,
    getData,
    removeData,
    clearExpiredData,
    checkClearExpiredKeys
  }
}

const EXPIRY_DAYS = 14
const TERMS_TOP_FIXED_IDS = 'TERMS_TOP_FIXED_IDS'

// 保存数据到localStorage，并设置过期时间
const saveData = (key: string, value: any) => {
  const now = new Date()
  const item = {
    value: JSON.stringify(value),
    expiry: now.getTime() + EXPIRY_DAYS * 24 * 60 * 60 * 1000,
  }
  localStorage.setItem(key, JSON.stringify(item))
  // 保存key 检查删除
  try {
    const ids = localStorage.getItem(TERMS_TOP_FIXED_IDS) || '[]'
    const idArr = JSON.parse(ids) as any[]
    if (!idArr.includes(key)) idArr.push(key)
    localStorage.setItem(TERMS_TOP_FIXED_IDS, JSON.stringify(idArr))
  } catch (e) {
    // 解析出错，清除该项
    localStorage.removeItem(TERMS_TOP_FIXED_IDS)
    return null
  }
}

const checkClearExpiredKeys = () => {
  try {
    const ids = localStorage.getItem(TERMS_TOP_FIXED_IDS) || '[]'
    const idArr = JSON.parse(ids) as any[]
    const now = new Date().getTime()
    const removeKeys: string[] = []

    for (let i = 0; i < idArr.length; i++) {
      const key = idArr[i]
      if (key) {
        const itemStr = localStorage.getItem(key)
        if (itemStr) {
          try {
            const item = JSON.parse(itemStr)
            if (item.expiry && now > item.expiry) {
              localStorage.removeItem(key)
              removeKeys.push(key)
            }
          } catch (e) {
            // 解析出错，清除该项
            localStorage.removeItem(key)
            removeKeys.push(key)
          }
        }
      }
    }
    localStorage.setItem(TERMS_TOP_FIXED_IDS, JSON.stringify(idArr.filter((item: string) => !removeKeys.includes(item))))
  } catch (e) {
    // 解析出错，清除该项
    localStorage.removeItem(TERMS_TOP_FIXED_IDS)
    return null
  }
}

// 从localStorage获取数据，如果已过期则删除并返回null
const getData = (key: string) => {
  const itemStr = localStorage.getItem(key)
  if (!itemStr) {
    return null
  }

  try {
    const item = JSON.parse(itemStr)
    const now = new Date()

    // 检查是否过期
    if (now.getTime() > item.expiry) {
      // 如果过期，删除该项
      localStorage.removeItem(key)
      return null
    }

    return JSON.parse(item.value) as any[]
  } catch (e) {
    // 解析出错，清除该项
    localStorage.removeItem(key)
    return null
  }
}

// 删除数据
const removeData = (key: string) => {
  localStorage.removeItem(key)
}

// 清理所有过期数据
const clearExpiredData = () => {
  const now = new Date().getTime()

  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key) {
      const itemStr = localStorage.getItem(key)
      if (itemStr) {
        try {
          const item = JSON.parse(itemStr)
          if (item.expiry && now > item.expiry) {
            localStorage.removeItem(key)
          }
        } catch (e) {
          // 解析出错，清除该项
          localStorage.removeItem(key)
        }
      }
    }
  }
}
