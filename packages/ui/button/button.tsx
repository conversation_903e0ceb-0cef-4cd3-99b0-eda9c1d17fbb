import { ClassName, createComponent, mergeClass, render, Renderable } from '@skynet/shared'
import { fn, SlotFn } from '@skynet/shared/create-component'
import { useThrottleFn } from '@vueuse/core'
import { ButtonHTMLAttributes } from 'vue'

type ButtonOptions = {
  props: {
    loading?: boolean
    class?: ClassName
    prefix?: Renderable | null
    suffix?: Renderable | null
    type?: 'button' | 'submit' | 'reset'
  }
  emits: {
    click: () => void
  }
  slots: {
    default: SlotFn
  }
}

export const Button = createComponent<ButtonOptions, ButtonHTMLAttributes>({
  props: {
    loading: false,
    prefix: null,
    suffix: null,
    class: '',
    type: 'button',
  },
  emits: {
    click: fn,
  },
}, (props, { emit, slots }) => {
  // 防止连点
  const throttleClick = useThrottleFn(() => {
    if (props.disabled) return
    emit('click')
  }, 500)
  return () => (
    <button
      class={mergeClass('no-tap-color dramato-button box-border text-nowrap cursor-pointer outline-none', props.class)}
      onClick={throttleClick}
      type={props.type}
    >
      {render(props.prefix)}
      {slots?.default?.()}
    </button>
  )
})

export default Button
