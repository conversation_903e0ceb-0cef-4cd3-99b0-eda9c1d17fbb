import { createComponent } from '@skynet/shared'
import { useColorSchemeStore } from 'src/modules/color-scheme/use-color-scheme-store'
import { RouterLink } from 'vue-router'
type TopBarOptions = {
  props: {}
}
export const TopBar = createComponent<TopBarOptions>({
  props: {},
}, props => {
  const { colorScheme } = useColorSchemeStore()
  return () => (
    <x-top-bar class="py-2 px-4 flex items-center justify-start h-full max-h-[var(--top-bar-height)]">
      <RouterLink to="/">
        <h2 class="text-2xl">前端组件库</h2>
      </RouterLink>
      <section class="flex flex-nowrap gap-2 items-center ml-auto">
        <span>主题</span>
        <button class="bg-white text-black px-2" onClick={() => { colorScheme.value = 'light' }}>亮</button>
        <button class="bg-black text-white px-2" onClick={() => { colorScheme.value = 'dark' }}>暗</button>
      </section>
    </x-top-bar>
  )
})
