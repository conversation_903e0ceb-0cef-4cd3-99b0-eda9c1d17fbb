/* eslint-disable @typescript-eslint/no-explicit-any */
import { openDialog, Icon, showFailToast, showSuccessToast } from '@skynet/ui'
import { computed, ref } from 'vue'
import { apiGetAuditFinalDetail, apiGetAuditFinalMajorDetail, apiUpdateAuditFinal, apiUpdateAuditFinalMajor } from './audit-script-api'
import { Step2ScoreTable } from './components/step2-score-table'
import { mc } from '@skynet/shared'
import { defaultTemplateStep2 } from './constant'
import { cloneDeep } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { openFile } from './util'
import { PreviewFile } from './components/preview-file'
import { useMenu } from 'src/modules/menu/use-menu'
import { labels, currentUser } from './audit-script-store'

export const useAuditFinalStore = () => {
  return {
    statusMap,
    onFinalResponsibleEditorEvaluate,
    onFinalChiefEditorEvaluate
  }
}
const { getUserRoles } = useMenu()
const detailData = ref<M.IAuditScriptSecondDetail>()
const chiefAuditDetailData = ref<M.IMajorAuditScriptSecondDetail>()
const scoreData = ref<M.IScore[]>([])
const totalScore = ref<number>(0)
const rating = ref<string>('')
const btnLoading = ref<boolean>(false)
const detailLoading = ref<boolean>(false)
const currentRoles = ref<number[]>(getUserRoles())
const playItem = ref<M.IScriptItemDetail>()

const statusMap: Record<number, { label: string, color: string }> = {
  1: {
    label: '待审核',
    color: 'default',
  },
  2: {
    label: '审核拒绝',
    color: 'error',
  },
  3: {
    label: '放弃',
    color: 'ghost',
  },
  4: {
    label: '审核通过',
    color: 'primary',
  },
}

const isAuditable = computed(() => {
  return playItem.value?.audit_status === 9
})

const auditCommitMajor = async (data: Api.AuditScriptStep2Major.Request.Params, cb?: () => void) => {
  btnLoading.value = true
  try {
    await apiUpdateAuditFinalMajor(data)
    cb && cb()
    showSuccessToast('操作成功')
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    btnLoading.value = false
  }
}

const getAuditSecondDetail = async (play_key: string) => {
  detailData.value = undefined
  try {
    detailLoading.value = true
    const res = await apiGetAuditFinalDetail({ play_key })
    detailData.value = res.data
    playItem.value = res.data?.play_item
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    detailLoading.value = false
  }
}

const getChiefAuditSecondDetail = async (play_key: string) => {
  chiefAuditDetailData.value = {
    main_detail: {
      status: 0,
      rating: '',
    },
    list: [],
    cover: '',
    play_path: '',
  }
  try {
    detailLoading.value = true
    const res = await apiGetAuditFinalMajorDetail({ play_key })
    chiefAuditDetailData.value = res.data
    playItem.value = res.data?.play_item
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    detailLoading.value = false
  }
}

const renderAuditTable = (list: M.IScore[]) => {
  totalScore.value = 0
  rating.value = ''
  const data = list && list.length > 0
    ? list.map((item, index) => ({
      id: String(index + 1),
      category: item.item || '',
      criteria: item.content || '',
      desc: item.desc || '',
      score: item.score !== null ? Number(item.score) : null,
    }))
    : cloneDeep(defaultTemplateStep2 as unknown as M.ScoreTableData[])

  scoreData.value = data.map(item => ({
    item: item.category,
    content: item.criteria,
    desc: item.desc || '',
    score: item.score !== null ? String(item.score) : undefined,
  }))

  const tableData = ref<M.ScoreTableData[]>(data as M.ScoreTableData[])

  return (
    <div>
      <Step2ScoreTable
        disabled={list.length !== 0}
        data={tableData.value}
        onScoreChange={(newData, total, rate) => {
          totalScore.value = total
          rating.value = rate
          scoreData.value = newData.map(item => ({
            item: item.category,
            content: item.criteria,
            desc: item.desc || '',
            score: item.score !== null ? String(item.score) : undefined,
            isOption: item.isOption,
          }))
        }}
      />
    </div>
  )
}

// 指定责编和主编评估
const onFinalResponsibleEditorEvaluate = (row: M.IScriptManageListItem, cb?: () => void) => {
  const dialog = openDialog({
    title: '评估',
    mainClass: 'pb-0 px-5',
    customClass: '!w-[1200px]',
    body: () => (
      <x-audit-confirm-dialog v-loading={detailLoading.value} class="flex flex-col gap-y-[25px] text-[var(--el-text-color-regular)]">
        <div class="flex flex-row space-x-4">
          <div class="w-[600px] space-y-4">
            <PreviewFile customClass="w-[600px] h-[600px] block text-center" url={playItem.value?.play_path || ''} />
          </div>
          <div class="flex w-full flex-col gap-y-2">
            <div>
              剧名: {row.title}
            </div>
            <div class="flex items-center flex-wrap gap-2">
              标签：{
                (playItem.value?.audit_label_ids as number[])?.map(item => (
                  <span class="badge badge-primary">{labels.value.find(label => label.label_id === item)?.content || ''}</span>
                ))
              }
            </div>
            <div>
              <span class="link link-primary" onClick={() => openFile(playItem.value?.play_path || '')}>查看剧本</span>
            </div>

            {isAuditable.value && playItem.value?.audit_failed_reason
              ? (
                  <div>
                    修改原因: {playItem.value?.audit_failed_reason}
                  </div>
                )
              : null}
            {
              renderAuditTable(detailData.value?.detail.list || [])
            }
          </div>
        </div>
        {isAuditable.value && !detailData.value?.detail?.score && (currentUser.value === row.principal || currentRoles.value.includes(26))
          ? (
              <x-audit-footer class="flex w-full justify-end gap-x-[10px]">
                <button class="btn btn-primary btn-sm" onClick={() => {
                  if (scoreData.value.some(item => !item.isOption && item.score === undefined)) {
                    showFailToast('请完成所有打分')
                    return
                  }
                  const description = ref('')
                  const confirmDialog = openDialog({
                    title: '确认打分',
                    body: () => (
                      <div class="flex flex-col gap-y-4">
                        <div class="flex flex-col gap-y-4">
                          <span>备注：</span>
                          <textarea
                            class={mc('textarea textarea-bordered block')}
                            value={description.value}
                            onInput={(e: Event) => {
                              description.value = (e.target as HTMLTextAreaElement).value
                            }}
                          />
                        </div>
                        <div class="flex w-full justify-end gap-x-[10px]">
                          <button class="btn btn-ghost btn-sm" onClick={() => {
                            confirmDialog()
                          }}
                          >取消
                          </button>
                          <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                            btnLoading.value = true
                            try {
                              await apiUpdateAuditFinal({
                                play_key: row.play_key,
                                data: {
                                  list: scoreData.value,
                                  score: totalScore.value,
                                  rating: rating.value,
                                  description: description.value,
                                },
                              })
                              showSuccessToast('操作成功')
                              await getAuditSecondDetail(row.play_key)
                              confirmDialog()
                              cb && cb()
                            } finally {
                              btnLoading.value = false
                            }
                          }}
                          >
                            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            确定
                          </button>
                        </div>
                      </div>
                    ),
                  })
                }}
                >确认打分
                </button>
              </x-audit-footer>
            )
          : null}
      </x-audit-confirm-dialog>
    ),
  })
  void getAuditSecondDetail(row.play_key)
}
// 总编评估
const onFinalChiefEditorEvaluate = (row: M.IScriptManageListItem, cb?: () => void) => {
  const dialog = openDialog({
    title: '总编评估',
    mainClass: 'pb-0 px-5',
    customClass: '!w-[870px]',
    body: () => (
      <x-audit-confirm-dialog v-loading={detailLoading.value} class="flex flex-col gap-y-[25px] text-[var(--el-text-color-regular)]">
        <div class="flex flex-row space-x-4">
          <div class="w-[600px] space-y-4">
            <PreviewFile customClass="w-[600px] h-[600px] block text-center" url={playItem.value?.play_path || ''} />
          </div>
          <div class="flex w-full flex-col gap-y-2">
            <div>
              剧名: {row.title}
            </div>
            <div class="flex items-center flex-wrap gap-x-2">
              标签：{
                (playItem.value?.audit_label_ids as number[])?.map(item => (
                  <span class="badge badge-primary">{labels.value.find(label => label.label_id === item)?.content || ''}</span>
                ))
              }
            </div>
            <div class="flex flex-row space-x-2">
              <span>状态:</span>
              <span class={`badge- badge${statusMap[chiefAuditDetailData.value?.main_detail?.status || 0]?.color}`}>
                {statusMap[chiefAuditDetailData.value?.main_detail?.status || 0]?.label}
              </span>
            </div>
            <div>
              <span class="link link-primary" onClick={() => openFile(chiefAuditDetailData.value?.play_path || '')}>查看剧本</span>
            </div>
            {
              chiefAuditDetailData.value?.main_detail?.status === 2 && chiefAuditDetailData.value?.main_detail?.rejected_reason
                ? (
                    <div>
                      驳回原因：{chiefAuditDetailData.value?.main_detail?.rejected_reason}
                    </div>
                  )
                : null
            }
            {isAuditable.value && playItem.value?.audit_failed_reason
              ? (
                  <div>
                    修改原因: {playItem.value?.audit_failed_reason}
                  </div>
                )
              : null}
            <div>
              整体评级: {chiefAuditDetailData.value?.main_detail?.rating}
            </div>
            <div class="mt-4">
              {chiefAuditDetailData.value?.list && chiefAuditDetailData.value?.list.length > 0 ? <div class="mb-2">评估详情:</div> : null}

              <div class="space-y-2">
                {
                  chiefAuditDetailData.value?.list?.map(item => (
                    <div class="mb-2 space-y-2 rounded-lg bg-gray-100 p-2">
                      <div>{item.audit_user}: {item.rating}</div>
                      {
                        item.description
                          ? (
                              <>
                                评分说明：{item.description}
                              </>
                            )
                          : null
                      }
                    </div>
                  ))
                }
              </div>
            </div>
          </div>
        </div>
        {isAuditable.value && currentRoles.value.includes(31) && row.principal_audited
          ? (
              <x-audit-footer class="flex w-full justify-end gap-x-[10px]">
                <button class="btn btn-ghost btn-sm" onClick={() => {
                  const dialog = openDialog({
                    title: '放弃',
                    customClass: '!w-[300px]',
                    body: () => (
                      <div class="flex flex-col gap-y-4">
                        <div>
                          确定放弃该剧
                        </div>
                        <div class="flex w-full justify-end gap-x-[10px]">
                          <button class="btn btn-ghost btn-sm" onClick={() => {
                            dialog()
                          }}
                          >取消
                          </button>
                          <button class="btn btn-primary btn-sm" onClick={async () => {
                            await auditCommitMajor({
                              play_key: row.play_key,
                              data: {
                                status: 3,
                                rating: chiefAuditDetailData.value?.main_detail?.rating || '',
                              },
                            }, () => {
                              void getChiefAuditSecondDetail(row.play_key)
                              cb && cb()
                              dialog()
                            })
                          }}
                          >
                            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            确定
                          </button>
                        </div>
                      </div>
                    ),
                  })
                }}
                >放弃
                </button>
                <button class="btn btn-error btn-sm" onClick={() => {
                  const rejected_reason = ref('')
                  const rejected_reason_en = ref('')
                  const tempRating = ref('')
                  if (!['B', 'B+'].includes(chiefAuditDetailData.value?.main_detail?.rating || '')) {
                    tempRating.value = ''
                  } else {
                    tempRating.value = chiefAuditDetailData.value?.main_detail?.rating || ''
                  }
                  const dialog = openDialog({
                    title: '打回修改',
                    customClass: '!w-[450px]',
                    body: () => (
                      <div class="flex flex-col gap-y-4">
                        <div class="flex flex-col gap-y-4">
                          {requiredLabel('评级：')}
                          <select class="select select-bordered select-sm w-full" v-model={tempRating.value}>
                            <option value="B+">B+</option>
                            <option value="B">B</option>
                          </select>
                        </div>
                        <div class="flex flex-col gap-y-4">
                          {requiredLabel('中文备注：')}
                          <textarea
                            class={mc('textarea textarea-bordered block')}
                            value={rejected_reason.value}
                            onInput={(e: Event) => {
                              rejected_reason.value = (e.target as HTMLTextAreaElement).value
                            }}
                          />
                        </div>
                        <div class="flex flex-col gap-y-4">
                          {requiredLabel('英文备注：')}
                          <textarea
                            class={mc('textarea textarea-bordered block')}
                            value={rejected_reason_en.value}
                            onInput={(e: Event) => {
                              rejected_reason_en.value = (e.target as HTMLTextAreaElement).value
                            }}
                          />
                        </div>
                        <div class="flex w-full justify-end gap-x-[10px]">
                          <button class="btn btn-ghost btn-sm" onClick={() => {
                            dialog()
                          }}
                          >取消
                          </button>
                          <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                            if (!tempRating.value) {
                              showFailToast('请选择评级')
                              return
                            }
                            if (!rejected_reason.value) {
                              showFailToast('请填写备注')
                              return
                            }
                            await auditCommitMajor({
                              play_key: row.play_key,
                              data: {
                                status: 2,
                                rating: tempRating.value,
                                rejected_reason: rejected_reason.value,
                                rejected_reason_en: rejected_reason_en.value,
                              },
                            }, () => {
                              void getChiefAuditSecondDetail(row.play_key)
                              dialog()
                              cb && cb()
                            })
                          }}
                          >
                            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            确定
                          </button>
                        </div>
                      </div>
                    ),
                  })
                }}
                >
                  打回修改
                </button>
                <button class="btn btn-primary btn-sm" onClick={() => {
                  const tempRating = ref('')
                  if (!['A', 'S'].includes(chiefAuditDetailData.value?.main_detail?.rating || '')) {
                    tempRating.value = ''
                  } else {
                    tempRating.value = chiefAuditDetailData.value?.main_detail?.rating || ''
                  }

                  const dialog = openDialog({
                    title: '评估通过',
                    customClass: '!w-[450px]',
                    body: () => (
                      <div class="flex flex-col gap-y-4">
                        <div class="flex flex-col gap-y-4">
                          {requiredLabel('评级：')}
                          <select class="select select-bordered select-sm w-full" v-model={tempRating.value}>
                            <option value="S">S</option>
                            <option value="A">A</option>
                          </select>
                        </div>
                        <div class="flex w-full justify-end gap-x-[10px]">
                          <button class="btn btn-ghost btn-sm" onClick={() => {
                            dialog()
                          }}
                          >取消
                          </button>
                          <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={() => {
                            if (!tempRating.value) {
                              showFailToast('请选择评级')
                              return
                            }
                            void auditCommitMajor({
                              play_key: row.play_key,
                              data: {
                                status: 4,
                                rating: tempRating.value,
                              },
                            }, () => {
                              void getChiefAuditSecondDetail(row.play_key)
                              dialog()
                              cb && cb()
                            })
                          }}
                          >
                            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            确定
                          </button>
                        </div>
                      </div>
                    ),
                  })
                }}
                >评估通过
                </button>
              </x-audit-footer>
            )
          : null}
      </x-audit-confirm-dialog>
    ),
  })
  void getChiefAuditSecondDetail(row.play_key)
}
