/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, exportAsCsv } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateForm, Pager, Icon, Button, transformNumber, SvgIcon, showFailToast, showSuccessToast, openDialog } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { ElTable, ElTableColumn } from 'element-plus'
import { apiGetResourceAuditProcess, apiUploadBatchResource } from './resource-audit-process-api'
import { langKey, langValue } from 'src/modules/resource/constant'
import { useRouter } from 'vue-router'
import { releaseRounds } from './constant'
import { requiredLabel } from 'src/lib/required-label'
import { formatSecondsToString } from './util'

type ResourceAuditProcessPageOptions = {
  props: {}
}
export const ResourceAuditProcessPage = createComponent<ResourceAuditProcessPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const loading = ref(false)
  const list = ref<M.IResourceAuditProcess.AuditList>([])
  const btnLoading = ref(false)
  const defaultParams: Api.ResourceAuditProcess.Param = {
    series_key_or_title_list: '',
    resource_id_or_title_list: '',
  }
  const processDesc = ['', '资源录入', '资源验收', '资源发布', '资源上架']
  const form = ref<Api.ResourceAuditProcess.Param>({ ...defaultParams })
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const QueryForm = CreateForm<Api.ResourceAuditProcess.Param>()

  const getList = async () => {
    const params = {
      ...form.value,
      ...pageInfo.value,
    }
    loading.value = true
    list.value = []
    try {
      const res = await apiGetResourceAuditProcess(params)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const getLangValue = (key: string) => {
    if (!key) return '-'
    const index = langKey.findIndex(k => k === key)
    return langValue[index]
  }

  const columns = [
    { prop: 'id', label: '资源ID', width: 120, fixed: true },
    { prop: 'title', label: '资源名称', width: 170, fixed: true, render: (scope: { row: M.IResourceAuditProcess.AuditItem }) => {
      return (
        <div class="relative p-4">
          {scope.row.release_round === 1 ? <SvgIcon class="size-8 absolute -top-[2px] -left-[2px]" name="ic_release" /> : null}
          <span>{scope.row.title}</span>
        </div>
      )
    } },
    {
      props: 'duration',
      label: '总时长',
      width: 140,
      render: (scope: { row: M.IResourceAuditProcess.AuditItem }) => {
        return <span>{scope.row.count}集 / { !scope.row.duration ? '-' : formatSecondsToString(scope.row.duration) }</span>
      },
    },
    {
      prop: 'process',
      label: '最新进度',
      render: (scope: { row: M.IResourceAuditProcess.AuditItem }) => processDesc[scope.row.process] ? <span class="badge badge-primary badge-outline">{processDesc[scope.row.process]}</span> : '-',
      width: 120,
    },
    { prop: 'partner_name', label: '合作方', width: 120 },
    { prop: 'entering_time', label: '录入完成时间', width: 140 },
    { prop: 'entering_user', label: '操作人', width: 120 },
    { prop: 'check_time', label: '验收完成时间', width: 140 },
    { prop: 'check_user', label: '操作人', width: 120 },

    {
      prop: 'subtitle_check_time',
      label: '字幕校对完成时间',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        const langItem = row.subtitle_check_time.find(item => item.language_code === 'en')
        return langItem
          ? (
              <div>
                <div>【{getLangValue(langItem.language_code)}】</div>
                <div>{langItem.time}</div>
              </div>
            )
          : '-'
      },
      width: 140,
    },
    {
      prop: 'subtitle_check_user',
      label: '操作人',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        const langItem = row.subtitle_check_user.find(item => item.language_code === 'en')
        return langItem
          ? langItem.user
          : '-'
      },
      width: 120,
    },

    { prop: 'info_check_time', label: '信息校验完成时间', width: 140 },
    { prop: 'info_check_user', label: '操作人', width: 120 },

    {
      prop: 'publish_source_check_time',
      label: '原剧集发布时间',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        const langItem = row.publish_source_check_time && row.publish_source_check_time[0] ? row.publish_source_check_time[0] : null
        return langItem
          ? langItem.time
          : '-'
      },
      width: 140,
    },
    {
      prop: 'publish_source_check_user',
      label: '操作人',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        const langItem = row.publish_source_check_user && row.publish_source_check_user[0] ? row.publish_source_check_user[0] : null
        return langItem
          ? langItem.user
          : '-'
      },
      width: 120,
    },

    {
      prop: 'publish_compound_check_time',
      label: '多语言发布时间',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        const langItem = row.publish_compound_check_time.find(item => item.language_code === 'en')
        return langItem
          ? (
              <div>
                <div>【{getLangValue(langItem.language_code)}】</div>
                <div>{langItem.time}</div>
              </div>
            )
          : '-'
      },
      width: 140,
    },
    {
      prop: 'publish_compound_check_user',
      label: '操作人',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        const langItem = row.publish_compound_check_user.find(item => item.language_code === 'en')
        return langItem
          ? langItem.user
          : '-'
      },
      width: 120,
    },

    {
      prop: 'final_check_time',
      label: '终审人审完成时间',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        return row.final_check_time
          ? row.final_check_time
          : '-'
      },
      width: 140,
    },

    {
      prop: 'publish_source_check_user',
      label: '操作人',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        return row.final_check_user
          ? row.final_check_user
          : '-'
      },
      width: 120,
    },

    {
      prop: 'shelve_time',
      label: '上架时间',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        const langItem = row.shelve_time.find(item => item.language_code === 'en')
        return langItem
          ? (
              <div>
                <div>{langItem.series_key}【{getLangValue(langItem.language_code)}】</div>
                <div>{langItem.time}</div>
              </div>
            )
          : '-'
      },
      width: 170,
    },
    {
      prop: 'shelve_user',
      label: '操作人',
      render: (scope: any) => {
        const row = scope.row as M.IResourceAuditProcess.AuditItem
        const langItem = row.shelve_user.find(item => item.language_code === 'en')
        return langItem && langItem.user
          ? langItem.user
          : '-'
      },
      width: 120,
    },
    {
      prop: 'operation',
      label: '操作',
      render: (scope: any) => {
        return (
          <div class="flex gap-x-2">
            <Button class="btn-link  btn btn-xs" onClick={() => {
              void router.push(`/resource-publish/update-detail/${scope.row.id}`)
            }}
            >编辑
            </Button>
          </div>
        )
      },
      width: 120,
    },
  ]

  const onUploadBusinessExcel = () => {
    const uploading = ref(false)
    const inputRef = ref<HTMLInputElement>()
    const fileChange = async (e: Event) => {
      const files = (e.target as HTMLInputElement)?.files || []
      const fileList = Array.from(files)
      if (fileList && fileList.length > 0) {
        const file = fileList[0]
        const ext = file.name.split('.').pop()?.toLowerCase() || ''
        if (ext !== 'csv' && ext !== 'xlsx') {
          showFailToast('仅支持上传.csv，.xlsx文件');
          (inputRef.value as HTMLInputElement).value = ''
          return
        }
        try {
          uploading.value = true
          await apiUploadBatchResource({ file })
          showSuccessToast('操作成功！')
          createDialog()
          void getList()
        } catch (error: any) {
          showFailToast(error.response.data.message || '操作失败！');
          (inputRef.value as HTMLInputElement).value = ''
        } finally {
          uploading.value = false
        }
      }
    }
    const createDialog = openDialog({
      title: () => (
        <div class="flex items-center space-x-2">
          <div>批量覆盖</div>
          <div class="text-red-500 text-sm">请谨慎操作</div>
        </div>
      ),
      mainClass: 'pb-0 px-5',
      body: () => (
        <div>
          <div>
            <input ref={inputRef} disabled={uploading.value} class="file-input file-input-xs file-input-primary w-full max-w-xs" type="file" multiple={false} accept="csv,xlsx" onChange={fileChange} />
            <div class="mt-2 text-xs">{requiredLabel('仅支持上传.csv，.xlsx文件')}</div>
          </div>
        </div>
      ),
    })
  }

  const onExport = async () => {
    btnLoading.value = true
    try {
      const params = {
        ...form.value,
        ...pageInfo.value,
        page_index: 1,
        page_size: 100000,
      }
      const res = await apiGetResourceAuditProcess(params)
      const list = res.data?.list || []
      const data = [
        [
          '资源id',
          '资源名称',
          '资源类型',
          '发行轮次',
          '已上架情况',
        ],
      ].concat(
        list?.map((item: M.IResourceAuditProcess.AuditItem) => [
          item.id,
          item.title,
          ['', '首发', '二轮'][item.resource_type] || '-',
          ['', '本土', '翻译'][item.release_round] || '-',
          `${item.shelve_time.length}/16`,
        ].map(i => JSON.stringify(i))) ?? [],
      )
      const detailData: {
        id: number
        title: string
        resource_type: 1 | 2
        release_round: 1 | 2
        langCode: string
        onlineTime: string
        series_key: string
      }[] = []
      list.map(item => {
        langKey.map((langCode: string) => {
          const curRowData = item.shelve_time.find(o => o.language_code === langCode)
          detailData.push({
            id: item.id,
            title: item.title,
            resource_type: item.resource_type,
            release_round: item.release_round,
            langCode,
            onlineTime: curRowData && curRowData.time ? curRowData.time as string : '-',
            series_key: curRowData && curRowData.series_key ? curRowData.series_key : '-',
          })
        })
      })

      const data1 = [
        [
          '资源id',
          '资源名称',
          '资源类型',
          '发行轮次',
          '语言',
          '上架时间',
          '剧集id',
        ],
      ].concat(
        detailData?.map(item => [
          item.id,
          item.title,
          ['', '首发', '二轮'][item.resource_type] || '-',
          ['', '本土', '翻译'][item.release_round] || '-',
          getLangValue(item.langCode),
          item.onlineTime,
          item.series_key,
        ].map(i => JSON.stringify(i))) ?? [],
      )
      exportAsCsv(data, `统计_${Date.now()}.csv`)
      exportAsCsv(data1, `详情_${Date.now()}.csv`)
    } finally {
      btnLoading.value = false
    }
  }

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = { ...defaultParams }
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }

  onQuery()

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>资源审核进度</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              ['剧集名称/ID：', 'series_key_or_title_list', { type: 'text', placeholder: '请输入剧集名称/ID' }],
              ['资源名称/ID：', 'resource_id_or_title_list', { type: 'text', placeholder: '请输入资源名称/ID' }],
              ['合作方：', 'partner_name', { type: 'text', placeholder: '请输入合作方' }],
              ['发行轮次：', 'release_round', { type: 'select', placeholder: '请选择发行轮次', options: releaseRounds }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex justify-between items-center relative">
            <span>资源审核进度列表</span>
            <div class="space-x-2 flex">
              <Button class="btn btn-primary btn-sm" onClick={onUploadBusinessExcel}>批量覆盖</Button>
              {/* <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={onExport}>
                {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
                导出
              </button> */}
            </div>
          </x-table-actions>
        ),
        table: () => (
          <ElTable
            stripe
            data={list.value || []}
            v-loading={loading.value}
            class="tm-table-fix-last-column"
          >
            <ElTableColumn type="expand" fixed v-slots={{
              default: ({ row }: { row: M.IResourceAuditProcess.AuditItem }) => {
                return (
                  <div class="flex left-12 sticky w-[calc(100vw-192px-70px)]">
                    <div class="w-1/3 p-4 space-y-2">
                      <div class="font-semibold">多语言字幕校对完成时间</div>
                      {
                        row.subtitle_check_time && row.subtitle_check_time.length > 0
                          ? row.subtitle_check_time.map((item, index) => (
                            <div>
                              <div>【{getLangValue(item.language_code)}】{item.time}</div>
                            </div>
                          ))
                          : <div>暂无进度</div>
                      }
                    </div>
                    <div class="w-1/3 p-4 space-y-2">
                      <div class="font-semibold">多语言发布时间</div>
                      {
                        row.publish_compound_check_time && row.publish_compound_check_time.length > 0
                          ? row.publish_compound_check_time?.map((item, index) => (
                            <div>
                              <div>【{getLangValue(item.language_code)}】{item.time}</div>
                            </div>
                          ))
                          : <div>暂无进度</div>
                      }
                    </div>
                    <div class="w-1/3 p-4 space-y-2">
                      <div class="font-semibold">多语言上架时间</div>
                      {
                        row.shelve_time && row.shelve_time.length > 0
                          ? row.shelve_time?.map((item, index) => (
                            <div>
                              <div>{item.series_key}【{getLangValue(item.language_code)}】: {item.time}</div>
                            </div>
                          ))
                          : <div>暂无进度</div>
                      }
                    </div>
                  </div>
                )
              },
            }}
            />
            {columns.map(col => {
              if (col.render) {
                return (
                  <ElTableColumn key={col.prop} prop={col.prop} label={col.label} width={col.width} v-slots={{
                    default: ({ row }: { row: any }) => col.render({ row }),
                  }}
                  />
                )
              } else {
                return (
                  <ElTableColumn key={col.prop} prop={col.prop} label={col.label} width={col.width} fixed={col.fixed} />
                )
              }
            })}
          </ElTable>
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ResourceAuditProcessPage
