import { createComponent, mc } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { ref, onBeforeUnmount, watch } from 'vue'
import { M3u8Player } from './m3u8-player'

type ClipOptions = {
  props: {
    path: string
    title?: string
    defaultValue?: M.Dimension
  }
  emits: {
    change(dimension: M.Dimension): void
  }
}
export const Clip = createComponent<ClipOptions>({
  props: {
    path: '',
    title: '',
    defaultValue: [0, 0, 0, 0]
  },
  emits: {
    change: () => {},
  },
}, (props, { emit }) => {
  const targetElement = ref<HTMLDivElement | null>(null)
  const transparentCanvas = ref<HTMLCanvasElement | null>(null)
  const dimension = ref<[number, number, number, number]>([0, 0, 0, 0])
  const showCanvas = ref(false)

  let isDrawing = false
  let startX = 0, startY = 0, currentX = 0, currentY = 0
  let ctx: CanvasRenderingContext2D | null = null

  const resizeCanvas = () => {
    if (transparentCanvas.value && targetElement.value) {
      const canvas = transparentCanvas.value
      canvas.width = targetElement.value.clientWidth
      canvas.height = targetElement.value.clientHeight
    }
  }

  const handleMouseDown = (e: MouseEvent) => {
    if (!transparentCanvas.value) return
    isDrawing = true
    const rect = transparentCanvas.value.getBoundingClientRect()
    startX = e.clientX - rect.left
    startY = e.clientY - rect.top
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDrawing || !transparentCanvas.value || !ctx) return
    const rect = transparentCanvas.value.getBoundingClientRect()

    currentX = Math.min(Math.max(e.clientX - rect.left, 0), rect.width)
    currentY = Math.min(Math.max(e.clientY - rect.top, 0), rect.height)

    const width = currentX - startX
    const height = currentY - startY

    // Smooth drawing with requestAnimationFrame
    requestAnimationFrame(() => {
      ctx!.clearRect(0, 0, transparentCanvas.value!.width, transparentCanvas.value!.height)
      ctx!.strokeStyle = 'blue'
      ctx!.lineWidth = 2
      ctx!.strokeRect(startX, startY, width, height)
    })
  }

  const fix2 = (str: number) => +(Math.abs(str).toFixed(2))

  const handleMouseUp = (e: MouseEvent) => {
    if (!transparentCanvas.value || !isDrawing) return
    isDrawing = false

    const rect = transparentCanvas.value.getBoundingClientRect()

    // 计算当前鼠标位置，并限制在 canvas 边界内
    currentX = Math.min(Math.max(e.clientX - rect.left, 0), rect.width)
    currentY = Math.min(Math.max(e.clientY - rect.top, 0), rect.height)

    // 确保框选左上角是 (topLeftX, topLeftY)
    const topLeftX = Math.min(startX, currentX)
    const topLeftY = Math.min(startY, currentY)

    // 宽度和高度取绝对值
    const width = Math.abs(currentX - startX)
    const height = Math.abs(currentY - startY)

    const canvasWidth = rect.width
    const canvasHeight = rect.height

    // 将左上角的坐标和宽高按比例转换为 0-1 的相对值
    dimension.value = [
      fix2(topLeftX / canvasWidth), // 左上角 X
      fix2(topLeftY / canvasHeight), // 左上角 Y
      fix2(width / canvasWidth), // 宽度
      fix2(height / canvasHeight), // 高度
    ]

    // 如果框选区域太小，取消操作
    const minSize = 5
    if (width < minSize || height < minSize) {
      ctx!.clearRect(0, 0, transparentCanvas.value.width, transparentCanvas.value.height)
      dimension.value = [0, 0, 0, 0]
      console.log('框选太小，操作取消')
      return
    }

    emit('change', dimension.value)
  }

  watch(() => transparentCanvas.value, () => {
    if (transparentCanvas.value) {
      ctx = transparentCanvas.value.getContext('2d')
      resizeCanvas()
      transparentCanvas.value.addEventListener('mousedown', handleMouseDown)
      window.addEventListener('mousemove', handleMouseMove)
      window.addEventListener('mouseup', handleMouseUp)
      window.addEventListener('resize', resizeCanvas)
    }
  })

  onBeforeUnmount(() => {
    if (transparentCanvas.value) {
      transparentCanvas.value.removeEventListener('mousedown', handleMouseDown)
    }
    window.removeEventListener('mousemove', handleMouseMove)
    window.removeEventListener('mouseup', handleMouseUp)
    window.removeEventListener('resize', resizeCanvas)
  })

  watch(() => showCanvas.value, newValue => {
    if (transparentCanvas.value) {
      const _width = transparentCanvas.value.width
      const _height = transparentCanvas.value.height
      ctx!.clearRect(0, 0, transparentCanvas.value.width, transparentCanvas.value.height)
      ctx!.strokeStyle = 'blue'
      ctx!.lineWidth = 2
      ctx!.strokeRect(_width * dimension.value[0], _height * dimension.value[1], _width * dimension.value[2], _height * dimension.value[3])
      emit('change', dimension.value)
    }
  })
  dimension.value = props.defaultValue
  return () => (
    <>
      <div ref={targetElement} class="relative inline-block">
        <M3u8Player id="clip-player" class="w-[350px]" url={props.path} onPlayerReady={_ => {
          resizeCanvas()
        }} />
        <Button class="btn btn-xs btn-primary absolute right-2 top-2 z-up" onClick={() => {
          showCanvas.value = !showCanvas.value
        }}
        >{props.title || '截取坐标'}
        </Button>
        <canvas ref={transparentCanvas} class={mc('absolute top-0 left-0 w-full h-full', showCanvas.value ? 'block' : 'hidden')} />
      </div>
      <div class="pt-2 text-[var(--text-2)]">[{dimension.value.join(',')}]</div>
    </>
  )
})
