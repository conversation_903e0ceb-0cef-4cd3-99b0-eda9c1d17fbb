import { r } from '@skynet/shared'
import ShareLayout from 'src/layouts/share-layout'

export const customerServiceCenterRoutes = [
  // Dynamic import, code splitting, lazy loading
  // r('recharge', 'Recharge', () => import('./recharge-page')),
  //
  // Static import
  r('customer-service-center', '', ShareLayout, [
    r('', 'Customer Service Center', () => import('./customer-service-center-page')),
    r('feedback', 'Feedback', () => import('./feedback/feedback-page')),
  ]),
]
