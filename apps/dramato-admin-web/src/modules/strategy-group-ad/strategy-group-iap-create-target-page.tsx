import { createComponent, mc } from '@skynet/shared'
import { Button, Checkbox, Empty, showAlert, transformInteger, transformNumber, transformStringArray } from '@skynet/ui'
import { FormOptions } from '@skynet/ui/form/form-types'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { apiGetStrategyGroupAdChannelList, apiGetStrategyGroupUserProfile } from 'src/modules/strategy-group/strategy-group-api'
import { useStrategyGroupAdStore } from 'src/modules/strategy-group-ad/strategy-group-ad-store'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { apiSaveStrategyGroupAds } from './strategy-group-ad-api'
import { ProfileCustomRenderers, profileDefaultValues } from '../strategy-group/profile-custom-renders'
import { useUserStrategyLayer } from '../user-strategy-layer/use-user-strategy-layer'
type StrategyGroupIapCreateTargetPageOptions = {
  props: {}
}
export const StrategyGroupIapCreateTargetPage = createComponent<StrategyGroupIapCreateTargetPageOptions>({
  props: {},
}, props => {
  const { formData, Form, stepOneError, validateStepOne, resetUserFormData } = useStrategyGroupAdStore()
  const route = useRoute()
  const { list, page, pageSize, search } = useUserStrategyLayer()
  const isViewMode = computed(() => route.query.mode === 'view')

  const adChannelList = ref<Array<{ name: string, code: string }>>([])
  onMounted(async () => {
    const response = await apiGetStrategyGroupAdChannelList()
    if (!response) return
    adChannelList.value = response.data?.list ?? []
  })
  const router = useRouter()
  const onSubmit = () => {
    if (!validateStepOne()) return
    void router.push({ path: './solution', query: { ...route.query, id: formData.value.id } })
  }

  onMounted(() => {
    page.value = 1
    pageSize.value = 9999
    void search(page.value)
  })

  const items = computed(() => [
    [
      [requiredLabel('方案名称'), 'name', { type: 'text', disabled: isViewMode.value }, { class: 'w-[20em]' }],
      formData.value.id && ['方案id', 'id', { type: 'text', disabled: true }, { class: 'shrink-0' }],
    ].filter(Boolean),
    [
      <span>用户设备 <small class="ml-2 text-gray-400">配置用户画像时，需保证用户设备与此选项一致</small></span>,
      'platform',
      {
        type: 'radio', options: [
          { label: '全部', value: 'all', disabled: isViewMode.value },
          { label: 'iOS', value: 'ios', disabled: isViewMode.value },
          { label: 'Android', value: 'android', disabled: isViewMode.value },
        ],
      }],
    // [
    //   '资源ID',
    //   'custom_series_ids',
    //   {
    //     type: 'text',
    //     disabled: isViewMode.value || formData.value.series_package_id,
    //   },
    //   { class: 'w-[20em]',
    //   },
    // ],
    // [
    //   '剧包筛选',
    //   'series_package_id',
    //   {
    //     type: 'select',
    //     disabled: formData.value.custom_series_ids?.length,
    //     options: [{
    //       value: 1,
    //       label: '首发保护期15天内',
    //     }, {
    //       value: 2,
    //       label: '首发保护期15天外，累计10w',
    //     }, {
    //       value: 3,
    //       label: ' 付费剧',
    //     },
    //     {
    //       value: -1,
    //       label: '其他',
    //     }],
    //     class: 'w-[23em]',
    //   },
    //   {
    //     transform: transformNumber,
    //   },
    // ],
    // ['', 'is_abtest', { type: 'checkbox', label: '是 AB Test', trueValue: 1, falseValue: 0 }, {}],
    // formData.value.is_abtest
    //   ? null
    //   : [
    //       '分层画像', 'strategy_layer_ids', {
    //         type: 'multi-select',
    //         search: true,
    //         popoverWrapperClass: 'z-popover-in-dialog',
    //         options: list.value.map((n, index) => {
    //           return { value: n.id, label: `${n.id}/${n.name}` }
    //         }),
    //         class: 'w-[600px]',
    //       },
    //     ],
    <h2 class="col-span-2 mb-4 text-lg font-bold"> {requiredLabel('广告全局控制参数')} </h2>,
    [
      requiredLabel('Showtime gap（广告间隔时间）（不含激励视频）'),
      'show_time_gap',
      { type: 'number', min: 0, suffix: 's' },
      { transform: transformNumber, hint: '输入整数' },
    ],
    [
      [
        requiredLabel('权重'), 'task_config.priority', {
          type: 'number',
          placeholder: '请输入 1 ~ 99，越大越优先',
          disabled: isViewMode.value,
        },
        { transform: transformInteger, class: 'w-300px' },
      ],
      [
        requiredLabel('方案持续时间'), 'task_config.duration', {
          type: 'number',
          placeholder: '填 -1 表示不限',
          suffix: '天',
          min: -1,
          disabled: isViewMode.value,
        },
        { transform: transformNumber, class: 'w-300px' },
      ],
    ],
  ] as FormOptions<FormData>['props']['items'])
  return () => (
    <div>
      <Form class="flex flex-col gap-4 p-8" data={formData.value} items={items.value}
        error={stepOneError.value}
        onSubmit={onSubmit}
        onReset={resetUserFormData}
        onChange={(path, value) => {
          set(formData.value, path, value)
        }}
        actions={() => (
          <div class="flex justify-between gap-x-2">
            {isViewMode.value ? <Empty /> : <Button class="btn btn-sm" type="reset">重置</Button>}
            <Button class="btn btn-primary btn-sm" type="submit">下一步</Button>
          </div>
        )}
        actionClass="col-span-2"
      />
    </div>
  )
})
export default StrategyGroupIapCreateTargetPage
