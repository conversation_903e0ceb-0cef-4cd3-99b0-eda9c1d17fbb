export const ru = {
  // 通用
  common: {
    cancel: 'Отмена',
  },
  // 分享页
  share: {
    watchNow: 'Смотреть Сейчас',
    episodeDialog: {
      title: 'Присоединяйся ко мне и получи дополнительные монеты!',
      description: 'Я подсел на этот сериал!',
      tips: 'Рекомендация',
      button: 'Смотреть весь сериал',
      limit24: 'Ограниченное время: 24h',
    },
    toast: {
      takeOffShelf: 'Контент Недоступен',
    },
  },
  'customer-service-center': {
    title: 'Центр обслуживания клиентов',
    description: 'Описание',
    'description-placeholder': 'Предоставьте сведения, которые помогут нам понять проблему',
    'email-placeholder': 'Введите свой адрес электронной почты, чтобы получать обновления отзывов',
    'submit-feedback': 'Отправить',
    'enter-feedback': 'Другие проблемы с отзывами',
    'upload-pictures': 'Загрузить изображения',
    feedback: 'Обратная связь',
    faq: {
      question1: 'Почему написано "Реклама Еще Не Готова"?',
      answer1: 'Иногда проблемы могут быть вызваны нестабильным подключением сервера или рекламодателя. Мы рекомендуем повторить попытку позже и не нажимать "Смотреть Рекламу" слишком часто. Ограничения на рекламу могут применяться из-за политик третьих лиц или ограничений вашей страны. Мы постоянно работаем с нашими поставщиками рекламы, чтобы улучшить ваш опыт просмотра рекламы.',
      question2: 'Почему написано "Сеть Недоступна" для рекламы?',
      answer2: 'Попробуйте перезапустить приложение, полностью закрыв его, а затем снова открыв. Если реклама по-прежнему не загружается, подождите некоторое время, прежде чем повторить попытку, и не нажимайте слишком часто. Если проблема сохраняется, вы можете связаться с нашей системой обратной связи. Мы также работаем с нашей командой по рекламе, чтобы улучшить опыт просмотра. Благодарим вас за терпение.',
    },
  },
  toast: {
    'upload-failed': 'Загрузка не удалась, пожалуйста, загрузите снова',
    'submit-success': 'Отправить успешно',
    'email-incorrect': 'Неправильный формат электронной почты',
  },
  not_found: {
    title: 'Страница, которую вы ищете, не существует',
  },
  regulations: {
    howUseData: 'Как мы используем ваши данные',
    content: `Если вы выберете "Разрешить" в запросе разрешения на прозрачность отслеживания приложений, мы будем использовать вашу информацию и данные для оптимизации наших услуг и предоставления вам наилучшего опыта, в противном случае мы будем только собирать данные и не будем проводить никаких действий по отслеживанию.

Если вы согласны разрешить DramaWave и нашим партнерам и поставщикам использовать данные с вашего устройства для анализа того, как вы используете приложение, персонализации рекомендаций по контенту и предоставления дополнительных услуг, связанных с информацией, пожалуйста, нажмите "Согласиться и продолжить", чтобы продолжить.`,
    acceptAll: 'Принять все',
    dataSettings: 'Настройки данных',
    next: 'Далее',
    confirm: 'Подтвердить',
    aboutYourPrivacy: 'О вашей конфиденциальности',
    personalDataName: 'Персонализированные данные',
    personalDataDescription: 'Мы используем эту информацию, чтобы запомнить ваши выборы и предоставлять персонализированную рекламу и рекомендации по контенту, уведомления и напоминания и т.д.',
    advertisementDataName: 'Рекламные данные',
    advertisementDataDescription: 'Файлы cookie, идентификаторы вашего устройства или другая информация могут храниться или быть доступны на вашем устройстве для целей онлайн-рекламы. Данные пользователей также могут быть переданы нашим рекламным партнерам.',
    behaviorsDataName: 'Поведенческие данные',
    behaviorsDataDescription: 'Данные, связанные с аналитикой, такие как файлы cookie или идентификаторы устройств, могут храниться для измерения и анализа использования, поведения и эффективности рекламы.',
    privacyAndTerms: 'Политика конфиденциальности и условия использования',
    referToPrivacy: 'Пожалуйста, ознакомьтесь с Политикой конфиденциальности',
    referToTerms: 'Пожалуйста, ознакомьтесь с Условиями использования',
    agreeAndSave: 'Согласиться и сохранить',
  },
  coins: {
    earnings: {
      title: 'Мои доходы',
      total: 'Всего монет',
      exchange: 'Обмен',
      earningsHistory: 'История доходов',
      exchangeHistory: 'История обмена',
      noRecords: 'Нет записей',
      last50earnings: 'Отображаются только последние 50 доходов',
      last50exchanges: 'Отображаются только последние 50 записей обмена',
      benefits: 'Преимущества',
      gotIt: 'Понял',
    },
    exchange: {
      title: 'Зона обмена',
      notOpen: 'Еще не открыто, пожалуйста, ожидайте',
      receiveBtn: 'С радостью принять',
      earnCoins: 'Заработать монеты',
      redeemRequiredXCoins: 'Для обмена требуется {X} золотых монет.',
      exchangeSuccess: 'Погашение успешно',
      cardId: 'Идентификатор карты',
      exchangeFailed: 'Погашение не удалось',
      transferFailedInsufficientStock: 'Нет в наличии, приходите завтра пораньше.',
      gotItBtn: 'знал',
      transferFailedRiskControl: 'Аккаунт является ненормальным и не может быть временно погашен.',
      tryAgainBtn: 'Пожалуйста, повторите попытку позже',
      transferConfirm: 'Подтвердить перевод',
      paypalAccountEmail: 'Электронная почта учетной записи PayPal',
      confirmPaypalAccountEmail: 'Подтвердите адрес электронной почты учетной записи PayPal',
      transferX: 'Перевести {X}',
      transferXConfirm: 'Подтвердить перевод {X}',
      transferToYourAccountX: 'Переведите {X} на свой счет',
      confirmBtn: 'подтверждать',
      transferSuccessTitle: 'Мы переводим средства на ваш счет PayPal!',
      transferSuccessContentX: 'Ваши средства поступят на счет {X} в течение 5 дней.',
      copySuccess: 'Копирование успешно',
      enterEmailError: 'Пожалуйста, введите действительный адрес электронной почты',
      enterEmailValidError: 'Ошибка ввода, пожалуйста, введите снова',
      emailNotMatchError: 'Адрес электронной почты не совпадает, пожалуйста, введите снова',
    },
  },
  my_wallet: {
    title: 'Мой кошелёк',
    help_dialog: {
      title: 'Правила использования билетов',
      content1: `1. 1 купленный билет = 1 купленный призовой билет = 1 рекламный призовой билет = 1 билет за обмен монет – все они могут использоваться для разблокировки эпизодов.`,
      content2: `2. Все билеты открывают доступ к эпизодам с постоянным правом на просмотр, но Purchased Reward Tickets истекают через определённый период.`,
      content3: `3. Купленные билеты можно приобрести напрямую.`,
      content4: `4. Купленные призовые билеты можно получить только во время специальных акций при покупке.`,
      content5: `5. Рекламные призовые билеты можно заработать, выполняя задачи по просмотру рекламы.`,
      content6: `6. Билеты за обмен монет можно получить путем обмена монет.`,
    },
    account_balance: 'Баланс счёта',
    purchased_tickets: 'Приобретённые билеты для просмотра',
    purchase_reward_tickets: 'Подарочные билеты при покупке',
    coin_exchange_tickets: 'Билеты за обмен монет',
    ad_reward_tickets: 'Билеты за просмотр рекламы',
    top_up: 'Пополнение',
    purchase_history: 'История покупок',
    rewards_history: 'История наград',
    exchange_history: 'История обменов',
    consumption_records: 'Записи о расходах',
    auto_unlock: 'Эпизоды с Авто-разблокировкой',
  },
  top_up: {
    exchange_title: 'Обмен монет',
    purchase_title: 'Покупка',
    tickets: 'Билеты',
    coins: 'Монеты',
    balance: 'Баланс',
    title: 'Магазин',
    tips: 'Советы',
    tips_1: 'Наша платформа предлагает как бесплатные короткие драмы, так и премиальные драмы с ранним доступом.',
    tips_2: 'Для просмотра контента с ранним доступом необходимо использовать билеты. Эти билеты можно получить путем покупки, обмена монет или просмотра рекламы.',
    tips_3: 'Все билеты открывают доступ к эпизодам с постоянным правом на просмотр, но Purchased Reward Tickets истекают через определённый период.',
    tips_4: 'При разблокировке эпизодов в первую очередь используются купленные призовые билеты. Если их недостаточно, автоматически будут использованы обычные билеты.',
    tips_5: 'Если оплата прошла успешно, но баланс не обновился, попробуйте обновить ваш кошелек.',
    tips_6: 'По любым другим вопросам обращайтесь к нам через раздел «Профиль» -> «Центр обслуживания клиентов» -> «Обратная связь».',
    exchange_dialog_title: 'Вы уверены, что хотите обменять?',
    redeem_required: 'Выкупить требуется',
    exchange_success: 'xxx билетов успешно обменяно',
    balance_not_enough: 'Недостаточно средств',
    exchange_failed: 'Обмен не удался',
  },
  my_wallet_in: {
    transaction_history: 'История Танзакций',
    EPnum: 'Эп.{num}',
    Expired: 'Истекает',
    AD: 'Реклама',
    Expires_on_num: 'Действительно до {num}',
    Thats_all_for_now: 'На этом пока всё.',
    consumption_records: 'Записи о расходах',
  },
  meal_check_in: {
    sleeping_link: 'Вы можете зарабатывать золотые монеты во время сна',
    rules_title: 'Правила активности',
    benefits_title: 'Benefits',
    rules_1: '1. Каждый день есть четыре времени для записи ваших приемов пищи: Завтрак: 07:00-10:00; Обед: 11:00-14:00; Ужин: 17:00-20:00; Полночный перекус: 21:00-24:00.',
    rules_2: '2. Если вы пропустили время записи, вы можете посмотреть видео, чтобы получить свою награду.',
    rules_3: '3. Награды сбрасываются каждый день в полночь, не забудьте забрать свою награду.',
  },
  sleep_check_in: {
    earn_link: 'Записывайте приемы пищи, чтобы зарабатывать',
    benefits_title: 'Benefits',
    rules_1: '1. Вы можете активировать режим «Спать, чтобы зарабатывать» каждый вечер с 19:00 до 24:00.',
    rules_2: '2. После активации на следующий день, после 8 часов сна, вы можете получить вознаграждение за сон с 8:00 до 12:00.',
    rules_3: '3. Если вы пропустили время для получения вознаграждения, вы можете посмотреть видео с 12:00 до 19:00 на следующий день, чтобы заново получить вознаграждение.',
  },
}
