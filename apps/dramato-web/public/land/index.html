<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="referrer" content="always">
  <meta name="viewport"
    content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
  <title>DramaWave</title>
  <script>
    window.t0_for_report = Date.now()
    window.land = {}
    const search = window.location.search.replace(/^\?/, '')
    const _query = new URLSearchParams(search)
    window.land.query = Object.fromEntries(_query.entries())
  </script>
  <!-- reset css -->
  <style>
    * {
      padding: 0;
      margin: 0;
      box-sizing: border-box;
    }

    *::before {
      box-sizing: border-box;
    }

    *::after {
      box-sizing: border-box;
    }

    ol,
    ul {
      list-style: none;
    }

    button,
    input {
      font-size: inherit;
      font-family: inherit;
      color: inherit;
    }

    img {
      max-width: 100%;
    }

    body {
      font-synthesis: none;
      text-rendering: optimizeLegibility;
    }
  </style>
  <!-- app css -->
  <style>
    body {
      background-color: #0b0b0b;
      color: #fff;
    }

    @media (min-width: 1000px) {
      #app {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }

      #app>article {
        flex: 1;
        display: flex;
      }
    }


    x-wrapper {
      flex: 1;
      display: block;
      background-size: cover;
      background-position: center;
      position: relative;
      padding-top: 28px;
      display: block;
      padding-bottom: 28px;
      overflow: hidden;
    }

    @media (min-width: 1000px) {
      x-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    main {
      display: block;
      margin-left: 12px;
      margin-right: 12px;
    }

    @media (min-width: 1000px) {
      main {
        display: flex;
        max-width: 1000px;
        column-gap: 50px;
      }
    }

    x-cover {
      display: flex;
      justify-content: center;
      align-items: center;
      /* 宽高比 3:4 */
      aspect-ratio: 3 / 4;
      margin: 0 28px;
      position: relative;
      overflow: hidden;
    }



    @media (min-width: 1000px) {
      x-cover {
        width: 400px;
        flex-shrink: 0;
        margin: 0;
      }
    }

    x-cover>.cover {
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.20);
      background-color: lightgray;
    }

    x-cover>.play {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    x-summary {
      display: block;
    }

    @media (min-width: 1000px) {
      x-summary {
        display: flex;
        flex-direction: column;
        row-gap: 12px;
        justify-content: center;
        align-items: center;
      }
    }

    x-summary x-desc {
      overflow: hidden;
      line-height: 1.4em;
      color: #AAA;
      font-size: 14px;
    }

    x-summary x-desc.folded {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      text-overflow: ellipsis;
      max-height: 7em;
    }

    x-summary x-desc.unfolded {
      max-height: none;
      display: block;
    }

    x-summary x-more,
    x-summary x-pick-up {
      display: block;
      text-align: right;
      font-size: 12px;
    }

    x-summary x-pick-up {
      display: none;
    }

    x-summary h1 {
      margin-top: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: var(--text-Icon-text-1, #FDFBFC);
      font-size: 20px;
      font-weight: 510;
    }

    @media (min-width: 1000px) {
      x-summary h1 {
        font-size: 24px;
        font-size: 38px;

        white-space: normal;
      }
    }

    x-summary ul {
      display: flex;
      align-items: center;
      gap: 6px;
      margin: 0 auto;
    }

    x-summary ul li {
      line-height: 1.25rem;
      border-radius: 0.675rem;
      background: #313131;
      padding: 0 1rem;
      font-size: 12px;
    }

    x-summary button {
      background-color: blue;
      color: white;
      min-height: 48px;
      min-width: 10em;
      border: none;
      border-radius: 24px;
      /* 渐变背景，从左往右，FE8C0E 到 F700D8 */
      background: linear-gradient(to right, #FE8C0E, #F700D8);
      margin-top: 32px;
      font-size: 20px;
      font-weight: 700;
      position: fixed;
      bottom: 80px;
      left: 12px;
      right: 12px;
    }

    @media (min-width: 1000px) {
      x-summary button {
        position: relative;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
      }
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.2);
      }

      100% {
        transform: scale(1);
      }
    }

    x-summary button img {
      position: absolute;
      top: 15px;
      right: 18px;
      color: white;
      animation: pulse 1s infinite;
    }



    x-mask {
      display: block;
      position: absolute;
      inset: 0;
      height: 100%;
      z-index: 2;
      background: linear-gradient(to bottom, #0b0b0b1a, #0b0b0bff);
    }

    @media (min-width: 1000px) {
      x-mask {
        background: linear-gradient(to bottom, #0b0b0b6a, #0b0b0bff);
      }
    }

    x-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 1;
      overflow: hidden;
    }

    x-bg img {
      /* 模糊效果 */
      filter: blur(10px);
      margin: -10px;
      display: block;
      width: calc(100% + 20px);
      max-width: none;
    }

    x-top-nav {
      position: sticky;
      top: 0;
      z-index: 10;
      color: #fff;
      background-color: #0b0b0b;
      display: grid;
      grid-template-areas:
        'logo title button'
        'logo subtitle button'
      ;
      grid-template-columns: max-content 1fr max-content;
      grid-template-rows: 1fr max-content;
      justify-content: left;
      align-items: center;
      justify-items: start;
      padding: 12px 18px;
      row-gap: 0px;
      column-gap: 8px;
    }

    @media (min-width: 1000px) {
      x-top-nav {
        position: fixed;
        top: 10px;
        left: 0;
        right: 0;
        z-index: 10;
        max-width: 1000px;
        margin: 0 auto;
        padding-left: 0;
        padding-right: 0;
        background-color: transparent;
      }
    }

    x-top-nav .logo {
      grid-area: logo;
    }

    x-top-nav .title {
      grid-area: title;
    }

    @media (min-width: 1000px) {
      x-top-nav .title {
        width: 164px;
        height: 21px;
      }
    }

    x-top-nav .subtitle {
      grid-area: subtitle;
      color: #666;
      font-size: 12px;
    }

    @media (min-width: 1000px) {
      x-top-nav .subtitle {
        display: none;
      }
    }

    x-top-nav button {
      grid-area: button;
      display: flex;
      height: 38px;
      padding: 0px var(--number-16, 18px);
      justify-content: center;
      align-items: center;
      gap: 10px;
      border: none;
      border-radius: 19px;
      background: var(--brand-brand-6, #FC2763);
    }

    @media (min-width: 1000px) {
      x-top-nav button {
        display: none;
      }
    }
  </style>
  <!-- Facebook Pixel Code -->
  <script>
    window.land.pixel_id = window.land.query.pixel_id || '1210605023775986'
    !function (f, b, e, v, n, t, s) {
      if (f.fbq) return; n = f.fbq = function () {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
      n.queue = []; t = b.createElement(e); t.async = !0;
      t.src = v; s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', window.land.pixel_id);
    fbq('track', 'PageView');
  </script>
  <noscript>
    <img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=1210605023775986&ev=PageView&noscript=1" />
  </noscript>
  <!-- End Facebook Pixel Code -->
</head>

<body>
  <!-- 自定义标签不能自闭合，自闭合会出 bug -->
  <div id="app" style="position: relative;">

    <x-top-nav>
      <img class="logo" src="https://static-v1.mydramawave.com/frontend_static/assets/logo-ZkLn7rqY.webp" width="37"
        height="37">
      <img class="title" src="https://static-v1.mydramawave.com/frontend_static/assets/dramawave-JYta-J2M.webp"
        width="92" height="12">
      <span class="subtitle ui-name" data-i18n="continueToNextEpisode"></span>
      <button data-i18n="open">&nbsp;</button>
    </x-top-nav>
    <article class="art">
      <x-wrapper>
        <x-bg>
          <img class="bg ui-cover" alt="background" />
          <x-mask> <!-- 不能自闭合 --> </x-mask>
        </x-bg>
        <main style="position: relative; z-index: 3;">
          <x-cover>
            <img class="ui-cover cover" src="" alt="cover" />
            <img class="play" width="90" height="90"
              src="https://static-v1.mydramawave.com/frontend_static/dramato-web/art/btn.svg" alt="play" />
          </x-cover>
          <x-summary style="display: flex; flex-direction: column; row-gap: 12px; text-align: center;">
            <h1 class="ui-name">&nbsp;</h1>
            <!-- tags -->
            <ul class="ui-content_tags">
            </ul>
            <div style="text-align: left;">
              <x-desc class="ui-desc unfolded"></x-desc>
            </div>
            <button>
              <img src="https://static-v1.mydramawave.com/frontend_static/dramato-web/art/click.webp" width="60"
                height="51" alt="click" />
              <span data-i18n="continueWatching"></span>
            </button>
          </x-summary>
        </main>
      </x-wrapper>
    </article>

  </div>
  <div style="width: 0; height: 0; margin-left: -10px;">
    <iframe id="iframe" src="" width="0" height="0" name="iframe"></iframe>
  </div>
  <!-- i18n starts-->
  <script>
    window.i18n = {
      'en-US': {
        'continueWatching': 'Continue Watching',
        'pickUp': 'Pick up',
        'more': 'More',
        'continueToNextEpisode': 'Next Episode',
        'open': 'Open'
      },
      'zh-CN': {
        'continueWatching': '继续观看',
        'pickUp': '收起',
        'more': '更多',
        'continueToNextEpisode': '继续下一集',
        'open': '打开'
      },
      'zh-TW': {
        'continueWatching': '繼續觀看',
        'pickUp': '收合',
        'more': '更多',
        'continueToNextEpisode': '繼續下一集',
        'open': '開啟'
      },
      'ja-JP': {
        'continueWatching': '続きを見る',
        'pickUp': '折りたたむ',
        'more': 'その他',
        'continueToNextEpisode': '次話を見る',
        'open': '開く'
      },
      'ko-KR': {
        'continueWatching': '이어 보기',
        'pickUp': '접기',
        'more': '더보기',
        'continueToNextEpisode': '다음 회차',
        'open': '열기'
      },
      'es-ES': {
        'continueWatching': 'Continuar viendo',
        'pickUp': 'Contraer',
        'more': 'Más',
        'continueToNextEpisode': 'Siguiente episodio',
        'open': 'Abrir'
      },
      'pt-PT': {
        'continueWatching': 'Continuar a ver',
        'pickUp': 'Recolher',
        'more': 'Mais',
        'continueToNextEpisode': 'Próximo Episódio',
        'open': 'Abrir'
      },
      'vi-VN': {
        'continueWatching': 'Tiếp tục xem',
        'pickUp': 'Thu gọn',
        'more': 'Thêm',
        'continueToNextEpisode': 'Tập tiếp theo',
        'open': 'Mở'
      },
      'th-TH': {
        'continueWatching': 'ดูต่อ',
        'pickUp': 'พับเก็บ',
        'more': 'เพิ่มเติม',
        'continueToNextEpisode': 'ตอนต่อไป',
        'open': 'เปิด'
      },
      'id-ID': {
        'continueWatching': 'Lanjutkan menonton',
        'pickUp': 'Sembunyikan',
        'more': 'Lainnya',
        'continueToNextEpisode': 'Episode Selanjutnya',
        'open': 'Buka'
      },
      'tl-PH': {
        'continueWatching': 'Magpatuloy sa Panonood',
        'pickUp': 'Tiklop',
        'more': 'Marami pa',
        'continueToNextEpisode': 'Susunod na Episode',
        'open': 'Buksan'
      },
      'fr-FR': {
        'continueWatching': 'Continuer à regarder',
        'pickUp': 'Réduire',
        'more': 'Plus',
        'continueToNextEpisode': 'Épisode suivant',
        'open': 'Ouvrir'
      },
      'de-DE': {
        'continueWatching': 'Weiter schauen',
        'pickUp': 'Einklappen',
        'more': 'Mehr',
        'continueToNextEpisode': 'Nächste Folge',
        'open': 'Öffnen'
      },
      'it-IT': {
        'continueWatching': 'Continua a guardare',
        'pickUp': 'Riduci',
        'more': 'Altro',
        'continueToNextEpisode': 'Prossimo Episodio',
        'open': 'Apri'
      },
      'ru-RU': {
        'continueWatching': 'Продолжить просмотр',
        'pickUp': 'Свернуть',
        'more': 'Ещё',
        'continueToNextEpisode': 'Следующий эпизод',
        'open': 'Открыть'
      },
      'tr-TR': {
        'continueWatching': 'İzlemeye Devam Et',
        'pickUp': 'Daralt',
        'more': 'Daha Fazla',
        'continueToNextEpisode': 'Sonraki Bölüm',
        'open': 'Aç'
      },
      'ms-MY': {
        'continueWatching': 'Teruskan menonton',
        'pickUp': 'Tutup',
        'more': 'Lagi',
        'continueToNextEpisode': 'Episod Seterusnya',
        'open': 'Buka'
      }
    }


  </script>
  <!-- i18n ends -->
  <script src=""></script>

  <!-- main starts -->
  <script>


  </script>
  <!-- main ends -->
  <script>
    window.t1_for_report = Date.now()
    const load_time = window.t1_for_report - window.t0_for_report
    addReport({
      event: 'ad_page_load_time',
      event_info: JSON.stringify(getReportData({ load_time: load_time })),
      load_time: load_time
    })
    reportNow()
  </script>

</body>



</html>
