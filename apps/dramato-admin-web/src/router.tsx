import { r, redirect } from '@skynet/shared'
import { DialogFooter, openDialog, showAlert } from '@skynet/ui'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory, RouterView } from 'vue-router'
import { AdminLayout } from './layouts/admin-layout.tsx'
import { track } from './lib/track.ts'
import { cardPointStrategyGroupRoutes } from './modules/card-point-strategy-group/card-point-strategy-group-routes.ts'
import { commercialSearchToolRoutes } from './modules/commercial-search-tool/commercial-search-tool-router.tsx'
import { commercialStrategyGroupRoutes } from './modules/commercial-strategy-new/commercial-strategy-routes.ts'
import { contentEvaluateRoutes } from './modules/content-evaluate/content-evaluate-routes.ts'
import { EpisodeDataClimbRoutes } from './modules/episode-data-climb/episode-data-climb-routes.ts'
import { apiSaveFreeStrategyGroupAds } from './modules/free-strategy-group-ad/strategy-group-ad-api.ts'
import { FreeStrategyGroupAdRoutes } from './modules/free-strategy-group-ad/strategy-group-ad-routes.ts'
import SpendBoard from './modules/spend_board/spend_board.tsx'
import { SpendBoardMaterial } from './modules/spend_board_material/spend_board_material.tsx'
import { SpendBoardSeriesList } from './modules/spend_board_material/spend_board_series_list.tsx'
import { SpendBoardTaskList } from './modules/spend_board_material/spend_board_task_list.tsx'
import { apiSaveStrategyGroupAds } from './modules/strategy-group-ad/strategy-group-ad-api.ts'
import { strategyGroupAdRoutes } from './modules/strategy-group-ad/strategy-group-ad-routes.ts'
import { apiSaveStrategyGroup } from './modules/strategy-group/strategy-group-api.ts'
import { strategyGroupRoutes } from './modules/strategy-group/strategy-group-routes.ts'
import { useStrategyGroupStore } from './modules/strategy-group/strategy-group-store.ts'
import { UserAssetManage } from './modules/user-asset-manage/user-asset-manage.tsx'
import { UserAssetRecord } from './modules/user-asset-record/user-asset-record.tsx'
import { UserComDis } from './modules/user-com-dis/user-com-dis.tsx'
import { userEngagementRoutes } from './modules/user-engagement/user-engagement-routes.ts'
import { UserOpt } from './modules/user-opt/user-opt.tsx'
import { UserPay } from './modules/user-pay/user-pay.tsx'
import { UserQuery } from './modules/user-query/user-query.tsx'
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
  }
}

/**
 * 公开路由，无需登录即可访问
 * 路由一经发布，不得修改，只能新增和重定向
 */
export const publicRoutes: RouteRecordRaw[] = [
  r('/login', '登录', () => import('src/modules/login/login-page.tsx')),
  r('/sso-redirect', 'sso登录', () => import('src/modules/login/sso-redirect-page.tsx')),
  r('/', '管理后台', AdminLayout, [
    redirect('/', '/guide'),
    r('draw-prize', '抽奖', null, [
      r('', '奖品列表', () => import('./modules/draw-prize/draw-prize-page')),
      r('record', '中奖记录', () => import('./modules/draw-prize/draw-prize-record-page')),
    ]),
    r('guide', '引导页', () => import('./modules/guide/guide-page.tsx')),
    r('application', '应用管理', () => import('src/modules/application/application-page.tsx')),
    r('application/:id', '应用管理-应用详情', () => import('src/modules/application/application-detail-page.tsx')),
    r('short-drama', '应用短剧管理', () => import('./modules/short-drama/short-drama-page.tsx')),
    r('short-drama/:drama_id', '应用短剧管理', () => import('./modules/short-drama-details/short-drama-details-page.tsx')),
    r('episode', '剧集管理', () => import('./modules/episode/episode-page.tsx')),
    r('episode/:seriesKey', '剧集管理-短剧详情', () => import('src/modules/episode/episode-details-page.tsx')),
    r('member', '会员档位管理', () => import('src/modules/member/member-page.tsx')),
    r('recharge-item-management', '充值道具管理', () => import('src/modules/recharge-item-management/recharge-item-page.tsx')),
    r('recharge-level', '配置应用收银台-充值档位', () => import('./modules/recharge-level/recharge-level-page.tsx')),
    r('free-recharge-level', '配置应用收银台-观影券购买档位管理配置', () => import('./modules/free-recharge-level/free-recharge-level-page.tsx')),
    r('free-buy-level', '配置应用收银台-观影券兑换档位管理配置', () => import('./modules/free-buy-level/free-buy-level-page.tsx')),
    r('exchange-area', '兑换专区', () => import('./modules/exchange-area/exchange-area-page.tsx')),
    r('top-up-level', '配置应用收银台-充值档位', () => import('./modules/recharge-level/recharge-level-page.tsx')),
    r('content-template/:id', '内容模板详情', () => import('./modules/content-template/template-detail-page.tsx')),
    r('content-template', '内容模板', () => import('./modules/content-template/content-template-page.tsx')),
    r('drama-rank', '短剧排行', () => import('./modules/drama-rank/drama-rank.tsx')),
    r('competition-content', '竞品内容数据', () => import('./modules/competition-content/competition-content-page.tsx')),
    r('competition-content/:id', '竞品内容数据-详情', () => import('./modules/competition-content/competition-content-detail-page.tsx')),
    r('material', 'Top素材投放表', () => import('src/modules/material/material-page.tsx')),
    r('targeted-material', '定向素材投放表', () => import('src/modules/material/targeted-material-page.tsx')),
    r('material-push', '素材推送', () => import('src/modules/material-push/material-push-page.tsx')),
    r('material-upload', '素材上传', () => import('src/modules/material-upload/material-upload-page.tsx')),
    r('banner', 'banner管理', () => import('./modules/banner/banner-page.tsx')),
    r('create-banner', '创建banner', () => import('./modules/banner/banner-create-page.tsx')),
    r('ad-create-banner', '创建广告位banner', () => import('./modules/banner/ad-banner-create-page.tsx')),
    r('banner-manage', 'banner图库管理', () => import('./modules/banner-manage/banner-page.tsx')),
    r('tool', '工具', RouterView, [
      r('recharge_bonus', '充值金币', () => import('./modules/tools/recharge-bonus-pag.tsx')),
    ]),
    r('notification', '推送通知', () => import('./modules/notification/notification-page.tsx')),
    r('notification-new', 'new-推送通知', () => import('./modules/notification-new/notification-page.vue')),
    r('activity-h5-admin', '活动管理', () => import('./modules/activity-h5-admin/activity-h5-admin-page.vue')),
    r('activity-h5-admin-detail/:id?', '活动管理', () => import('./modules/activity-h5-admin/activity-h5-admin-detail-page.vue')),
    r('company-admin', '企业认证审核', () => import('./modules/company-admin/company-admin-page.vue')),
    r('company-admin-detail/:id?', '企业认证审核', () => import('./modules/company-admin/company-admin-detail-page.vue')),
    r('risk-users', '风控用户查询', () => import('./modules/risk-users/risk-users-page.vue')),
    r('material-tools', '素材效率工具', () => import('./modules/material-tools/material-tools-page.vue')),
    r('material-tools-role-mark/:id?', '角色标注', () => import('./modules/material-tools/material-tools-role-mark-page.vue')),
    r('notification-new/:type/:id/:pushId', 'new-推送通知-分批次数据', () => import('./modules/notification-new/notification-page-drama-batch-list.vue')),
    r('episode-breakdownPage/:seriesKey/:episodeName/:episodeNum', 'AI视频拆解', () => import('./modules/episode-breakdown/episode-breakdown-page.tsx')),
    r('vocal-generate/:seriesKey', '声纹提取', () => import('./modules/episode-breakdown/vocal-generate-page.tsx')),
    r('clip-material/:seriesKey', 'AI剪素材', () => import('./modules/episode-clip-material/episode-clip-material.tsx')),
    r('episode-material/:seriesKey/:materialNumber/:title', '剧集素材', () => import('./modules/episode-clip-material/episode-material-details.tsx')),
    r('resource', '资源管理', () => import('src/modules/resource/resource-page.tsx')),
    r('resource/:id', '资源详情', () => import('src/modules/resource/resource-detail-page.tsx')),
    r('operation-position-item/:id?', '运营位元素管理', () => import('./modules/ac-operation-position/popup-scene.tsx')),
    r('activity-popup', '弹窗管理', () => import('./modules/activity-popup/activity-popup-page.tsx')),
    r('popup-scene', '弹窗场景', () => import('./modules/activity-popup/popup-scene.tsx')),
    r('operation-position-item/:id?', '元素管理', () => import('./modules/ac-operation-position/activity-popup-page.tsx')),
    r('operation-position', '运营位管理', () => import('./modules/ac-operation-position/activity-popup-page.tsx')),
    r('home-config', '首页模块配置', () => import('./modules/home-config/home-config-page.tsx')),
    r('home-config-new', '新首页模块配置', () => import('./modules/home-config-new/home-config-page.tsx')),
    r('statistic', '充值数据报表', () => import('./modules/statistic/statistic-page.tsx')),
    r('rewards', 'Rewards页管理', () => import('./modules/rewards/rewards-page.tsx')),
    r('statistics/income', '收入数据', () => import('./modules/statistics/statistics-page.tsx')),
    r('statistics/user', '用户数据', () => import('./modules/statistics/statistics-page.tsx')),
    r('statistics/push', 'push数据', () => import('./modules/statistics/statistics-page.tsx')),
    r('statistics/freereels', 'freereels用户数据', () => import('./modules/statistics/statistics-page.tsx')),
    r('statistics/freereels-push', 'FreeReels-PUSH数据', () => import('./modules/statistics/statistics-page.tsx')),
    r('statistics/finances', '商业化数据', () => import('./modules/statistics/statistics-page.tsx')),
    r('statistics/advertise', '广告链路数据', () => import('./modules/statistics/statistics-page.tsx')),
    r('statistics/w2a', 'w2a链路数据', () => import('./modules/statistics/statistics-page.tsx')),
    r('advertise', '广告管理', () => import('./modules/advertise/advertise-page.tsx')),
    r('free-advertise', '广告详情', () => import('./modules/free-advertise/advertise-page.tsx')),
    strategyGroupRoutes,
    strategyGroupAdRoutes,
    FreeStrategyGroupAdRoutes,
    cardPointStrategyGroupRoutes,
    commercialStrategyGroupRoutes,
    userEngagementRoutes,
    contentEvaluateRoutes,
    EpisodeDataClimbRoutes,
    commercialSearchToolRoutes,
    r('episode-pricing', '剧集定价', null, [
      r('', '剧集定价', () => import('./modules/episode-pricing/episode-pricing-page.tsx')),
      r('create', '新建剧集定价', () => import('src/modules/episode-pricing/update-episode-pricing-page.tsx')),
      r('edit', '剧集定价详情', () => import('src/modules/episode-pricing/update-episode-pricing-page.tsx')),
    ]),
    r('activity-icon', '运营活动页管理', () => import('src/modules/activity-icon/activity-icon-page.tsx')),
    r('activity-icon-detail/:id?', '运营活动详情页', () => import('src/modules/activity-icon/activity-icon-detail-page.tsx')),
    r('user-query', '用户查询', UserQuery),
    r('user-pay', '购买记录', UserPay),
    r('user-opt', '操作记录', UserOpt),
    r('user-asset-manage', '用户资产管理', UserAssetManage),
    r('asset-manage-detail', '资产转移', () => import('src/modules/user-asset-manage/asset-manage-detail-page.tsx')),
    r('asset-sure-form', '资产转移', () => import('src/modules/user-asset-manage/asset-sure-form.tsx')),
    r('asset-finish-form', '资产转移', () => import('src/modules/user-asset-manage/asset-finish-form.tsx')),
    r('user-compensation-distribution', '补偿奖励发放', UserComDis),
    r('user-asset-record', '资产操作记录', UserAssetRecord),
    r('episode-tags', '剧集标签', () => import('./modules/episode-tags/episode-tags.tsx')),
    r('coupons', '优惠券', () => import('./modules/coupons/coupons-page.tsx')),

    r('episode-tab', 'Tab管理', () => import('./modules/episode-tabs/episode-tab.tsx')),
    r('episode-theatre', '剧场管理', null, [
      redirect('', 'theatre'),
      r('theatre', '剧场管理', () => import('./modules/episode-theatres/episode-theatre.tsx')),
      r('channel', '频道', () => import('./modules/episode-theatres/episode-theatre-channel.tsx')),
      r('module', '模块', () => import('./modules/episode-theatres/episode-theatre-module.tsx')),
      r('operating-positon', '运营位', () => import('./modules/episode-theatres/operating-positon.tsx')),
      r('operational-item', '运营项', () => import('./modules/episode-theatres/operational-item.tsx')),
    ]),
    r('episode-content', '模块内容管理', () => import('./modules/episode-content/episode-content.tsx')),
    r('episode-module', '模块管理', () => import('./modules/episode-modules/episode-modules.tsx')),

    r('targeted-tasks', '定向任务', () => import('./modules/targeted-tasks/targeted-tasks.tsx')),
    r('user-feedback', '反馈收集', null, [
      r('', '反馈收集', () => import('src/modules/user-feedback/user-feedback-page.tsx')),
      r('detail/:id', '反馈收集-详情', () => import('src/modules/user-feedback/user-feedback-detail.tsx')),
    ]),
    r('feedback-faq', 'FAQ管理', null, [
      r('', 'FAQ管理', () => import('./modules/feedback-faq/feedback-faq-page.tsx')),
      r('detail/:id?', 'FAQ详情', () => import('./modules/feedback-faq/feedback-faq-detail-page.tsx')),
    ]),
    r('comment', '评论管理', () => import('./modules/comment/comment-page.tsx')),
    r('menu', '菜单管理', () => import('src/modules/menu/menu-page.tsx')),
    r('user', '用户管理', () => import('src/modules/user/user-page.tsx')),
    r('role', '角色管理', () => import('src/modules/role/role-page.tsx')),
    r('business-resource', '短剧信息录入', () => import('src/modules/business-resource/business-resource-page.tsx')),
    r('business-resource-detail/:id?', '短剧信息录入详情', () => import('src/modules/business-resource/business-resource-detail-page.tsx')),
    r('audit-business-resource', '商务短剧信息审核', () => import('src/modules/business-resource/business-resource-audit-page.tsx')),
    r('audit-business-resource-detail/:id?', '商务短剧信息审核详情', () => import('src/modules/business-resource/business-resource-audit-detail-page.tsx')),
    r('ad-unit', '广告单元管理', () => import('./modules/ad-unit/ad-unit.tsx')),
    r('ad-level', '广告价值区间', () => import('./modules/ad-level/ad-level.tsx')),
    r('resource-publish', '资源发布', null, [
      r('entry', '资源录入', () => import('src/modules/resource-publish/resource-entry-page.tsx')),
      r('entry-detail/:id?', '资源录入详情', () => import('src/modules/resource-publish/resource-entry-detail-page.tsx')),
      r('inspection', '资源验收', () => import('src/modules/resource-publish/resource-inspection-page.tsx')),
      r('inspection-detail/:id?', '资源录入详情', () => import('src/modules/resource-publish/resource-inspection-detail-page.tsx')),
      r('publish', '终审发布', () => import('src/modules/resource-publish/resource-publish-page.tsx')),
      r('info-verify', '多语言信息校验', null, [
        r('', '多语言信息校验', () => import('src/modules/resource-publish/resource-info-modify-page.tsx')),
        r('information/:id', '资源信息', () => import('src/modules/resource-publish/resource-info-page-for-info-modify.tsx')),
      ]),
      r('multi-subtitle', '多语言字幕校验', null, [
        r('', '多语言字幕校验', () => import('src/modules/resource-publish/resource-multi-subtitle-page.tsx')),
        r('detail/:id', '资源详情', () => import('src/modules/resource/resource-publish-detail-page.tsx')),
      ]),
      r('detail/:id', '资源详情', () => import('src/modules/resource/resource-publish-detail-page.tsx')),
      r('information/:id', '资源信息', () => import('src/modules/resource-publish/resource-info-page-for-info-modify.tsx')),
      r('audit-process', '资源审核进度', () => import('src/modules/resource-publish/resource-audit-process-page.tsx')),
      r('update-detail/:id?', '资源详情', () => import('src/modules/resource-publish/resource-update-detail-page.tsx')),

      r('user-statistic', '人员统计', () => import('src/modules/resource-publish/resource-user-statistic-page.tsx')),
      r('inject-subtitle', '字幕压制', () => import('src/modules/resource-publish/resource-subtitle-cover.tsx')),

      r('voice-over', '配音管理', null, [
        r('', '配音管理', () => import('src/modules/resource-publish/voice-over-page.tsx')),
        r('role-mark/:id?', '角色标注', () => import('src/modules/role-mark/role-mark-page.tsx')),
        r('detail/:id?', '配音详情', () => import('src/modules/voiceover-resource/voiceover-resource-page.tsx')),
      ]),
    ]),
    r('timbre', '音色管理', () => import('src/modules/timbre/timbre-page.tsx')),
    r('voiceover-role', '角色-音色管理', () => import('src/modules/voiceover-role/voiceover-role-page.tsx')),
    r('dataeye-drama', 'dramaeye数据', () => import('src/modules/data-eye-drama/data-eye-pages.tsx')),

    r('spend-board', '短剧投放监控', SpendBoard),
    r('spend-board-material', '短剧投放列表', SpendBoardMaterial),
    r('user-strategy-layer', '用户分层', null, [
      r('', '用户分层', () => import('./modules/user-strategy-layer/user-strategy-layer-page.tsx')),
      r('create', '用户分层', () => import('./modules/user-strategy-layer/creation-or-edition.tsx')),
      r('edit', '用户分层', () => import('./modules/user-strategy-layer/creation-or-edition.tsx')),
    ]),
    r('spend-board-task-list', '任务管理中心', SpendBoardTaskList),
    r('spend-board-series-list', '剧单库', SpendBoardSeriesList),
    r('ab-test', 'ABTest', () => import('src/modules/ab-test/ab-test-page.tsx')),
    r('ab-test-new', 'ABTestNew', () => import('src/modules/ab-test-new/ab-test-new-page.tsx')),
    r('rank', 'Rank', () => import('src/modules/rank/rank-page.tsx')),
    r('jd-rank', '剧单配置页', () => import('src/modules/jd-rank/rank-page.tsx')),
    r('jd-common-rank', '普通tab运营资源位配置', () => import('src/modules/jd-common-rank/rank-page.tsx')),
    r('label', '标签管理', () => import('src/modules/label/label-page.tsx')),
    r('actor', '演员管理', () => import('src/modules/actor/actor-page.tsx')),
    r('script', '剧本管理', null, [
      r('list', '剧本管理', () => import('./modules/script-management/script-manage-page.tsx')),
      r('detail/:id', '剧本详情', () => import('./modules/script-management/script-manage-detail-page.tsx')),
      r('audit', '剧本审核', () => import('./modules/script-management/audit-script-page.tsx')),
    ]),
    r('resource-income', '收入分账汇总', () => import('./modules/resource-income/resource-income-page.tsx')),
    r('report', '盗版监控', () => import('./modules/report/report-page.tsx')),
    r('demo/1', '测试专用1', () => import('./modules/demo/demo-1-page.tsx')),
    r('demo/2', '测试专用2', () => import('./modules/demo/demo-2-page.vue')),
    r('/series-package', '剧包管理列表', () => import('src/modules/series-package-new/series-package-page.tsx')),
    r('/blacklist', '异常用户管理', () => import('src/modules/blacklist/blacklist-page.tsx')),
    r('/voiceover-editor', '配音编辑器', () => import('./modules/voiceover-editor/voiceover-editor.vue')),
    r('/voiceover-editor2', '配音编辑器', () => import('./modules/voiceover-editor/timeLineEditor.vue')),
    r('/material-translation', '素材翻译', () => import('./modules/material-translation/material-translation.vue')),
    r('/language-translate', '语言翻译', () => import('./modules/language-translate/language-translate.vue')),
    r('/language-translate-task/:id', '任务列表', () => import('./modules/language-translate/language-translate-task-list.vue')),
    r('/language-translate-task-detail/:id', '任务详情', () => import('./modules/language-translate/language-translate-task-detail.vue')),
    r('/language-translate-main-key', '主键库', () => import('./modules/language-translate/main-key.vue')),
    r('/ad-switch', '广告开关', () => import('./modules/ad-switch/ad-switch.vue')),
    r('/ad-authorization', '投放授权', () => import('./modules/ad-authorization/ad-authorization-page.tsx')),
  ]),
  r('/resource-publish/voice-over/resource/:id?', '字幕校对', () => import('src/modules/voiceover-resource/resource-detail-page.tsx')),
  r('/resource-publish/inspection-role-detail/:id?', '资源验收详情页', () => import('src/modules/resource-publish/inspection-role-detail-page.tsx')),
  r('/resource-publish/multiple-language-subtitle/:id?', '多语言字幕校对', () => import('src/modules/resource-publish/multiple-language-subtitle-page.tsx')),
  r('/resource-publish/final-subtitle/:id?', '终身发布字幕校对', () => import('src/modules/resource-publish/multiple-language-subtitle-page.tsx')),
  r('/tool/image', '图片优化', () => import('src/modules/tool/image-page.tsx')),
  r('/:pathMatch(.*)*', '页面不存在', () => import('./modules/not-found/not-found-page.tsx')),
]

const router = createRouter({
  history: createWebHistory(),
  routes: publicRoutes,
})

const { formData, validateStepOne, validateStepTwo, validateStepOneForIAA, validateStepTwoForIAA, validateAllStepsForIAA, validateAllSteps, validateStepAssociate } = useStrategyGroupStore()

router.beforeEach((to, from, next) => {
  if (
    from.query.mode !== 'view'
    && from.query.has_edit === 'true'
    && to.query.has_edit !== 'true'
    && ['/strategy-group', '/ad-strategy-group/iaa', '/ad-strategy-group/iap', '/free-ad-strategy-group/iaa', '/free-ad-strategy-group/iap'].includes(to.path)) {
    const saveAdsRequest = from.fullPath.includes('free') ? apiSaveFreeStrategyGroupAds : apiSaveStrategyGroupAds
    const closeDialog = openDialog({
      title: '提示',
      body: (
        <div>是否存为草稿？
          <DialogFooter okText="确认" onOk={async () => {
            closeDialog()
            if (from.fullPath.includes('strategy')) {
              let isIAAMode = false
              if (from.fullPath.includes('/iaa') || from.fullPath.includes('/iap')) {
                isIAAMode = true
              }
              const validationConfig = {
                iaa: {
                  validationMap: {
                    target: validateStepOneForIAA,
                    product: validateStepTwoForIAA,
                    plan: validateAllStepsForIAA,
                    associate: validateStepAssociate,
                  },
                  saveApi: () => saveAdsRequest({
                    ...formData.value,
                    save_type: 2,
                    pay_mode: (router.currentRoute.value.params.tabName as string).toUpperCase(),
                  }),
                },
                default: {
                  validationMap: {
                    target: validateStepOne,
                    product: validateStepTwo,
                    plan: validateAllSteps,
                  },
                  saveApi: () => apiSaveStrategyGroup({ ...formData.value, save_type: 2 }),
                },
              }

              console.log('isIAAMode', isIAAMode)

              const config = isIAAMode ? validationConfig.iaa : validationConfig.default

              for (const [key, validator] of Object.entries(config.validationMap)) {
                if (from.fullPath.includes(key) && !validator()) {
                  return
                }
              }
              const response = await config.saveApi()
              if (response.code === 200) {
                showAlert('保存成功', 'success')
                next()
              } else {
                showAlert(response.msg, 'error')
              }
            }
          }} cancelText="取消" onCancel={() => {
            closeDialog()
            next()
          }}
          />
        </div>
      ),
    })
    return
  }
  next()
})

router.afterEach((to, from) => {
  track('admin', 'page', 'show', {
    path: to.path,
    from: from.path,
  })
})

export default router

let goingToErrorPage = false
export const goToErrorPage = async (fromPath?: string, message?: string) => {
  if (goingToErrorPage) return
  goingToErrorPage = true
  fromPath = fromPath || router.currentRoute.value.fullPath
  await router.push(`/error-page?fromPath=${encodeURIComponent(fromPath)}&message=${message ?? ''}`).finally(() => {
    goingToErrorPage = false
  })
}
