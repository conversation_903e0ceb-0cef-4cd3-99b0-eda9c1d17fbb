export default defineNitroPlugin(nitroApp => {
  // startTime
  nitroApp.hooks.hook('request', async event => {
    if (event.path === '/metrics') return
    event.context.startTime = Date.now()
  })

  // endTime
  nitroApp.hooks.hook('afterResponse', async event => {
    if (event.path === '/metrics') return
    if (!event.context.startTime) return

    const { httpRequestDuration } = await import('~/utils/metrics')

    const duration = (Date.now() - event.context.startTime) / 1000
    const method = event.node.req.method || 'GET'
    const route = getRoute(event)
    const statusCode = event.node.res.statusCode || 200

    // 记录指标
    httpRequestDuration
      .labels(method, route, statusCode.toString())
      .observe(duration)
  })
})

function getRoute(event: { path: string }) {
  const pathname = event.path.split('?')[0] || '/' // 去除查询参数
  const level1 = pathname.split('/')[1] || '' // 为了防止指标膨胀，只记录一级路由
  return '/' + level1
}
