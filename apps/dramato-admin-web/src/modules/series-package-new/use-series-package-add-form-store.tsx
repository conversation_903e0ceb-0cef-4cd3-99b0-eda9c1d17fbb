import { useValidator } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { ref } from 'vue'
import { z } from 'zod'
import { apiCreateSeriesPackage, apiEditSeriesPackage } from './series-package-api'
import { useSeriesPackageStore } from './use-series-package-store'

const Form = CreateForm<M.SeriesPackage.Details>()
const defaultDate: M.SeriesPackage.Details = {
  package_name: '',
  user_platform: 0,
  scene_type: 1,
  import_type: 1,
  package_config: {
    package_type_1_config: {
      ios_series_keys: [],
      android_series_keys: [],
    },
    package_type_2_config: {
      ios_url: '',
      android_url: '',
    },
    package_type_3_config: {
      ios_list: [],
      android_list: [],
    },
    package_type_4_config: {
      language_version_code: [],
      listing_status: [], // 上线状态：0 全部 -1 已下架 1 未上架 2 待上架 3 已上架 4 待下架
      // episode_count_type: 0, // 剧集数量类型 0 全部 1 0-50 2 50-100 3 100+
      resource_type: 0, // 资源类型 1 本土 2 翻译
      free_status: 0, // 0 全部 1 免费 2 付费
      serialize_status: 0, // 连载状态 1 未完结 2 已完结
      release_round: 0, // 0 全部 1 首发 2 二轮
      episode_count_begin: '',
      episode_count_end: '',
      protect_begin: '',
      protect_end: '',
      amount_begin: '',
      amount_end: '',
      spend_begin: '',
      spend_end: '',
      // protect_types: [], // 保护类型 1 0-7 2 0-15 3 7+ 4 15+
      // amount_types: [], // 剧集包剧集数量类型 1 0-10w 2 10-30w 3 30-50W 4 50w+
    },
    package_type_5_config: {
      collection_type: 1, // 集合方式 1 正选 2 反选
      collection_package_ids: [], // 普通剧包列表
    },
  },
}
const formData = ref<M.SeriesPackage.Details>(defaultDate)
const curStep = ref(1)
const curTab = ref([0, 1].includes(formData.value?.user_platform || 0) ? 'ios_config' : 'android_config')
const appId = ref(0)
const checkedEpisodeItem = ref<M.ApplicationDramaItem[]>([])

const oneFormRules = z.object({
  package_name: z.string().min(1, '请输入包名'),
  priority: z.number().min(1, '请输入优先级'),
  package_config: z.object({
    package_type_5_config: z.object({
      collection_package_ids: z.array(z.number()).min(1, { message: '请填写剧包ID' }),
    }),
  }),
})
const { error: oneStepError, validateAll: validateOneForm } = useValidator(formData, oneFormRules)

const twoFormRules = z.object({
  package_config: z.object({
    package_type_1_config: z.object({
      ios_series_keys: z.array(z.string()).min(1, { message: '必填' }),
      android_series_keys: z.array(z.string()).min(1, { message: '必填' }),
    }),
    package_type_2_config: z.object({
      ios_url: z.string().min(1, { message: '必填' }),
      android_url: z.string().min(1, { message: '必填' }),
    }),
    package_type_3_config: z.object({
      ios_list: z.array(z.object({
        drama_id: z.number(),
      })).min(1, { message: '必填' }).max(300, { message: '筛选导入剧上限条数：最多不超过300部剧' }),
      android_list: z.array(z.object({
        drama_id: z.number(),
      })).min(1, { message: '必填' }).max(300, { message: '筛选导入剧上限条数：最多不超过300部剧' }),
    }),
    package_type_4_config: z.object({
      episode_count_begin: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入整数',
      }).optional(),
      episode_count_end: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入整数',
      }).optional(),
      protect_begin: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入整数',
      }).optional(),
      protect_end: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入整数',
      }).optional(),
      amount_begin: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+(\.\d{1,2})?$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入两位小数点',
      }).optional(),
      amount_end: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+(\.\d{1,2})?$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入两位小数点',
      }).optional(),
      spend_begin: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+(\.\d{1,2})?$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入两位小数点',
      }).optional(),
      spend_end: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+(\.\d{1,2})?$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入两位小数点',
      }).optional(),
      first_list_begin: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入整数',
      }).optional(),
      first_list_end: z.string().refine((value: string) => {
        if (!value) {
          return true
        }
        const regex = /^\d+$/
        return regex.test('' + value)
      }, {
        message: '数值区间支持自定义输入整数',
      }).optional(),
    }),
  }),
})
const { error: twoStepError, validateAll: validateTwoForm } = useValidator(formData, twoFormRules)

const closeAddDialog = ref()

const onUpStepClick = () => curStep.value = curStep.value - 1

const onDownStepClick = () => {
  if (!validateOneForm({ exclude: ['package_config.package_type_5_config.collection_package_ids'] })) {
    return
  }
  curStep.value = curStep.value + 1
}

const resetOneForm = () => {
  formData.value = {
    ...formData.value,
    package_name: '',
    user_platform: 0,
    scene_type: 1,
    import_type: 1,
    priority: undefined,
    country_exclude: '',
    country_include: '',
    remark: '',
  }
}

const resetTwoForm = () => {
  formData.value = {
    ...formData.value,
    package_config: {
      package_type_1_config: {
        ios_series_keys: [],
        android_series_keys: [],
      },
      package_type_2_config: {
        ios_url: '',
        android_url: '',
      },
      package_type_3_config: {
        ios_list: [],
        android_list: [],
      },
      package_type_4_config: {
        language_version_code: ['all'],
        listing_status: [],
        // episode_count_type: 0, // 剧集数量类型 0 全部 1 0-50 2 50-100 3 100+
        resource_type: 0, // 资源类型 1 本土 2 翻译
        free_status: 0, // 0 全部 1 免费 2 付费
        serialize_status: 0, // 连载状态 1 未完结 2 已完结
        release_round: 0, // 0 全部 1 首发 2 二轮
        episode_count_begin: '',
        episode_count_end: '',
        protect_begin: '',
        protect_end: '',
        amount_begin: '',
        amount_end: '',
        spend_begin: '',
        spend_end: '',
        // protect_types: [],
        // amount_types: [],
      },
      package_type_5_config: {
        collection_type: 1, // 集合方式 1 正选 2 反选
        collection_package_ids: [], // 普通剧包列表
      },
    },
  }
}

const isSubmitting = ref(false)

const onSubmit = async () => {
  const { isCommon } = useSeriesPackageStore()
  const exclude = (isCommon.value ? [
    formData.value.import_type !== 1 && 'package_config.package_type_1_config',
    formData.value.import_type !== 2 && 'package_config.package_type_2_config',
    formData.value.import_type !== 3 && 'package_config.package_type_3_config',
    formData.value.import_type !== 4 && 'package_config.package_type_4_config',
    formData.value.import_type === 1 && formData.value.user_platform === 2 && 'package_config.package_type_1_config.ios_series_keys',
    formData.value.import_type === 1 && formData.value.user_platform === 1 && 'package_config.package_type_1_config.android_series_keys',
    formData.value.import_type === 2 && formData.value.user_platform === 2 && 'package_config.package_type_2_config.ios_url',
    formData.value.import_type === 2 && formData.value.user_platform === 1 && 'package_config.package_type_2_config.android_url',
    formData.value.import_type === 3 && formData.value.user_platform === 2 && 'package_config.package_type_3_config.ios_list',
    formData.value.import_type === 3 && formData.value.user_platform === 1 && 'package_config.package_type_3_config.android_list',
  ] : [
    'priority',
  ]) as string[]

  const valid = !isCommon.value ? validateOneForm : validateTwoForm

  if (!valid({ exclude })) {
    console.log('error', oneStepError, twoStepError)

    return
  }

  isSubmitting.value = true
  const requestApi = formData.value.id ? apiEditSeriesPackage : apiCreateSeriesPackage
  const response = await requestApi({ ...formData.value }).finally(() => {
    isSubmitting.value = false
  })
  if (response.code !== 200) return

  closeAddDialog.value && closeAddDialog.value()
  const { search, page } = useSeriesPackageStore()
  void search(formData.value.id ? page.value : 1)
}

export const useSeriesPackageAddFormStore = () => {
  return {
    formData,
    Form,
    curTab,
    curStep,
    onUpStepClick,
    onDownStepClick,
    onSubmit,
    resetOneForm,
    resetTwoForm,
    oneStepError,
    twoStepError,
    closeAddDialog,
    appId,
    checkedEpisodeItem,
    isSubmitting,
  }
}
