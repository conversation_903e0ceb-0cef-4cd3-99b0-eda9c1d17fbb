/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { computed, onMounted, ref, watch } from 'vue'
import { CreateForm, transformNumber, Button, transformTimestamp, Icon, showFailToast, showSuccessToast, openDialog } from '@skynet/ui'
import { z } from 'zod'
import { cloneDeep, get, set, trim } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { statusDesc } from './util'
import { langKey, langValue } from 'src/modules/resource/constant'
import { Wrapper } from 'src/layouts/wrapper'
import { resourceTypeOptions, authPlatforms, authLanguages, authAreas, sharingModels, contentRates, releaseRounds, maritalStatusOptions, fertilityStatusOptions, ageOptions, vipTypes, freeStatusOptions } from './constant'
import { useRouter, useRoute } from 'vue-router'
import { apiGetResourcePublishDetail, apiSaveDraft, apiGetCheckInfo, apiAnalysisNetUrl, apiUpdateActorAssociate } from './resource-publish-api'
import { useResourceStore } from './use-resource-publish-store'
import { CheckInfoForm } from './components/check-info-form'
import { EvaluateTable } from './components/evaluate-table'
import { defaultTemplate } from './constant'

type ResourceEntryDetailPageOptions = {
  props: {}
}

export const ResourceEntryDetailPage = createComponent<ResourceEntryDetailPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const route = useRoute()
  const { passFormData, validateInfo, getLabelList } = useResourceStore()

  const currentResource = ref<M.IResourcePublishDetail>({
    auth_platform: [],
    language_code: [],
    subtitle_language_code: [],
    upload_subtitle_language_code: [],
    list: [],
    free_status: 2,
    actors: [],
  })
  const urlSchema = z.string().url()
  const saveLoading = ref(false)
  const oprLoading = ref(false)
  const Form = CreateForm<M.IResourcePublishDetail>()
  const descriptionChanged = ref(false)
  const actors = ref<M.INewActor[]>([{
    name: '',
  }])
  const isAuditPage = computed(() => {
    return route.path.includes('inspection-detail')
  })

  watch(() => currentResource.value.free_status, newVal => {
    if (newVal !== 1) {
      currentResource.value.free_start_time = undefined
    }
  })

  watch(() => currentResource.value.preview_status, newVal => {
    if (newVal !== 2) {
      currentResource.value.preview_time = undefined
      currentResource.value.poster_preview_time = undefined
    }
  })

  const hasBanned = computed(() => {
    return [4, 5].includes(currentResource.value.recycle_status || 0)
  })

  const descriptionTip = computed(() => {
    return descriptionChanged.value ? '1000' : '300'
  })

  const formRules = z.object({
    title: z.string().min(1, {
      message: '请输入短剧名称',
    }).max(255, {
      message: '短剧名称太长',
    }),
    description: z.string().min(1, {
      message: '请输入短剧简介',
    }).refine(e => {
      // 如果存在id，则允许1000个字， 如果不存在 300个字
      if (!descriptionChanged.value) {
        if (e && e.length > 1000) return false
      } else {
        if (e && e.length > 300) return false
      }
      return true
    }, {
      message: `短剧简介太长，请控制在${descriptionTip.value}个字符`,
    }),
    language_code: z.array(z.string().min(1, {
      message: '请选择视频内嵌字幕语言',
    })),
    resource_type: z.number().min(1, {
      message: '请选择资源类型',
    }),
    auth_price: z.number().min(0, {
      message: '请输入授权价格',
    }),
    // resource_type_v2: z.number().min(1, {
    //   message: '请选择资源类型',
    // }),
    // partner_name: z.string().min(1, {
    //   message: '请填写合作方名称',
    // }),
    business_principal: z.string().min(1, {
      message: '请填写商务负责人',
    }),
    // deploy_advance: z.number().min(1, {
    //   message: '请选择是否可提前投放',
    // }),
    // content_rating: z.string().min(1, {
    //   message: '请选择内容评级',
    // }),
    resource_type_v2: z.number().min(1, {
      message: '请选择短剧类型',
    }),
    preview_status: z.number().min(1, {
      message: '请选择是否可预告',
    }),
    // .refine(e => {
    //   if (currentResource.value.preview_status === 2 && currentResource.value.release_round === 2) {
    //     return false
    //   }
    //   return true
    // }, {
    //   message: '二轮剧不可预告',
    // }),
    netdisk_url: z.string().min(1, {
      message: '请填写网盘名称',
    }),
    release_round: z.number().min(1, {
      message: '请选择发行轮次',
    }),
    contractor: z.any().refine(e => {
      if (currentResource.value.resource_type_v2 === 5 && !e) {
        return false
      }
      return true
    }, {
      message: '请输入承制方',
    }),
    count: z.number().min(1, {
      message: '请输入总集数',
    }),
    unlocked_episodes: z.number().min(1, {
      message: '请输入付费第一集',
    }).refine(e => {
      if (currentResource.value.count && e >= (currentResource.value.count || 0)) {
        return false
      }
      return true
    }, {
      message: '必须小于总集数',
    }),
    auth_start_time: z.number().min(1, {
      message: '请选择授权开始时间',
    }),
    auth_end_time: z.number().min(1, {
      message: '请选择授权结束时间',
    }).refine(e => {
      if (currentResource.value.auth_start_time) {
        if (e < +currentResource.value.auth_start_time) {
          return false
        }
      }
      return true
    }, {
      message: '授权结束时间必须大于授权开始时间',
    }),
    unlocked_other_days: z.any().refine(e => {
      if (currentResource.value.vip_type !== 0) {
        if (!e) return true
        if (e === null || e === undefined) return false
        if (!/^\+?[1-9][0-9]*$/.test(String(e))) return false
        return true
      }
      return true
    }, {
      message: '请输入正整数',
    }),
    free_status: z.number().min(1, {
      message: '请选择是否可免费',
    }),
    free_start_time: z.any().refine(e => {
      if (currentResource.value.online_time && currentResource.value.free_status === 1) {
        if (e < +currentResource.value.online_time) {
          return false
        }
      }
      return true
    }, {
      message: '最早可免费时间必须大于上线时间',
    }).refine(e => {
      if (currentResource.value.free_status === 1 && !e) {
        return false
      }
      return true
    }, {
      message: '请选择最早可免费时间',
    }),
    poster_preview_time: z.any().refine(e => {
      if (currentResource.value.preview_status === 2 && !e) {
        return false
      }
      return true
    }, {
      message: '请选择海报可预告时间',
    }).refine(e => {
      if (currentResource.value.preview_time && currentResource.value.preview_status === 2) {
        if (e > +currentResource.value.preview_time) {
          return false
        }
      }
      return true
    }, {
      message: '海报可预告时间必须不晚于预告片预告时间',
    }).refine(e => {
      if (currentResource.value.online_time && currentResource.value.preview_status === 2) {
        if (e > +currentResource.value.online_time - 24 * 60 * 60) {
          return false
        }
      }
      return true
    }, {
      message: '海报最早可预告时间必须早于上线时间24小时',
    }),
    preview_time: z.any().refine(e => {
      if (!e) return true
      if (currentResource.value.online_time && currentResource.value.preview_status === 2) {
        if (e > +currentResource.value.online_time - 12 * 60 * 60) {
          return false
        }
      }
      return true
    }, {
      message: '预告片最早可预告时间必须早于上线时间12小时',
    }),
    vip_type: z.number().min(0, {
      message: '请选择VIP类型',
    }),
    vip_everyday_update_sn: z.any().refine(e => {
      if (currentResource.value.vip_type === 2) {
        if (e === null || e === undefined) return false
        if (!/^\+?[1-9][0-9]*$/.test(String(e))) return false
        return true
      }
      return true
    }, {
      message: '请输入每天更新集数',
    }),
    deploy_time: z.any().refine(e => {
      if (currentResource.value.release_round === 1 && !e) {
        return false
      }
      return true
    }, {
      message: '请选择可投放时间',
    }),
  })
  const { error, validateAll } = useValidator(currentResource, formRules)

  const saveCraft = async (fn: () => void) => {
    if (!validateInfo.validateAll()) {
      return
    }
    if (!validateAll()) return
    try {
      saveLoading.value = true
      const res = await apiSaveDraft({
        entering_info_req: { ...currentResource.value },
        check_info_req: { ...passFormData.value, content_rating: currentResource.value.content_rating },
      })
      currentResource.value = res.data || {}
      saveLoading.value = false
      fn && fn()
    } catch (error: any) {
      saveLoading.value = false
      showFailToast(error.response.data.err_msg || '保存失败')
    }
  }

  const cancel = () => {
    void router.replace('/resource-publish/audit-process')
  }

  const showMulSelectedText = (text: string, options: { value: string | number, label: string }[]) => {
    const arr: any[] = get(currentResource.value, text, '') || []
    const result = options.filter(o => arr.includes(o.value as number))
    return <div class="text-base text-[var(--text-1)]">{result.map(o => o.label).join(',') || '- 空 -'}</div>
  }

  const editActor = () => {
    if (currentResource.value.actors && currentResource.value.actors.length > 0) {
      actors.value = [...currentResource.value.actors]
    } else {
      actors.value = [
        {
          name: '',
        },
      ]
    }
    const btnLoading = ref(false)

    const hideDialog = openDialog({
      title: '编辑演员信息',
      mainClass: 'pb-0 px-5',
      customClass: '!w-[700px]',
      body: () => (
        <x-actor-confirm-dialog class="block">
          <div class="mb-2 flex justify-end">
            <Button class="btn btn-link btn-sm" onClick={() => {
              if (actors.value.length > 4) {
                showFailToast('演员信息最多5条')
                return
              }
              actors.value.push({
                name: '',
              })
            }}
            >新增演员
            </Button>
          </div>
          <div class="space-y-4">
            {
              actors.value?.map((actor, index) => {
                return (
                  <div class="flex items-center space-x-2">
                    {requiredLabel('演员：')}
                    <input class="input input-sm input-bordered" value={actor.name} onInput={(e: Event) => {
                      actors.value[index] = {
                        ...actors.value[index],
                        name: (e.target as HTMLInputElement).value,
                      }
                    }}
                    />
                    {requiredLabel('性别：')}
                    <select class="select select-sm select-bordered" value={actor.gender} onChange={(e: Event) => {
                      actors.value[index] = {
                        ...actors.value[index],
                        gender: +(e.target as HTMLInputElement).value,
                      }
                    }}
                    >
                      <option value={0}>未知</option>
                      <option value={1}>男</option>
                      <option value={2}>女</option>
                    </select>
                    <span>链接：</span>
                    <input class="input input-sm input-bordered" value={actor.home_page} type="text" onInput={(e: Event) => {
                      actors.value[index] = {
                        ...actors.value[index],
                        home_page: (e.target as HTMLInputElement).value,
                      }
                    }} />
                    <button class="btn btn-square btn-xs" onClick={() => {
                      actors.value.splice(index, 1)
                    }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="size-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                )
              })
            }
          </div>
          <x-actor-footer class="mt-4 flex w-full justify-end gap-x-[10px]">
            <button class="btn btn-ghost btn-sm" onClick={() => hideDialog()}>取消</button>
            <button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
              if (!actors.value.every(item => trim(item.name))) {
                showFailToast('请输入演员名称')
                return
              }
              if (!actors.value.every(item => item.gender === 0 || item.gender === 1 || item.gender === 2)) {
                showFailToast('请选择性别')
                return
              }
              btnLoading.value = true
              const list = actors.value.filter(actor => actor.name)
              try {
                await apiUpdateActorAssociate({
                  actors: list,
                  series_resource_id: +currentResource.value.id!,
                })
                currentResource.value.actors = list
                hideDialog()
              } catch (error: any) {
                showFailToast(error.response.data.err_msg || '操作失败')
              } finally {
                btnLoading.value = false
              }
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </button>
          </x-actor-footer>
        </x-actor-confirm-dialog>
      ),
    })
  }

  // const isChEpisode = () => (currentResource.value?.language_code as string[])[0] === 'zh-CN'
  // const isChEpisode = () => (currentResource.value?.language_code?.[0] ?? '') === 'zh-CN'
  const getDetail = async () => {
    try {
      const checkRes = await apiGetCheckInfo({
        id: +route.params.id,
      })
      passFormData.value = checkRes.data || {}
      const res = await apiGetResourcePublishDetail({
        id: +route.params.id,
      })
      const data = {
        ...res.data,
        content_rating: passFormData.value.content_rating,
      }
      currentResource.value = data || {}
    } catch (error: any) {
      showFailToast(error.response.data.err_msg || '获取详情失败')
    }
  }

  onMounted(() => {
    currentResource.value = {}
    passFormData.value = {}
    if (route.params.id) {
      void getLabelList()
      void getDetail()
    }
  })

  return () => (
    <>
      <Wrapper>
        <section class="breadcrumbs text-sm">
          <ul>
            <li class="cursor-pointer" onClick={() => cancel()}>资源审核进度</li>
            <li class="cursor-pointer">编辑资源</li>
          </ul>
        </section>
        <Form
          class={mc('grid w-full m-auto grid-cols-3', isAuditPage.value ? 'pointer-events-none' : '')}
          hasAction={false}
          error={error.value}
          data={currentResource.value}
          onChange={(path, value: any) => {
            if (path === 'description') {
              descriptionChanged.value = true
            }
            if (path === 'resource_type_v2' && value !== 5) {
              currentResource.value.contractor = ''
            }
            if (path === 'vip_type' && value !== 2) {
              currentResource.value.vip_everyday_update_sn = undefined
            }
            if (path === 'vip_type' && value === 0) {
              currentResource.value.unlocked_other_days = 0
            }
            set(currentResource.value, path, value)
          }}
          items={[
            [
              requiredLabel('审核状态'),
              'audit_status',
              {
                type: 'custom',
                render: () => !hasBanned.value
                  ? statusDesc(currentResource.value.check_status || 0)
                  : (
                      <div class="line-clamp-1 flex items-center gap-x-4 overflow-hidden pb-4 text-sm">
                        <div class="badge  badge-outline text-red-500">{ currentResource.value.recycle_status === 4 ? '已暂缓' : '已退剧' }</div>
                      </div>
                    ),
              },
              {
                class: `col-span-3 ${currentResource.value.check_status! > 0 ? 'block' : 'hidden'}`,
                hint: () => currentResource.value.check_reason ? <div class="mt-4 !text-base !text-[var(--text-1)]">驳回原因：{currentResource.value.check_reason}</div> : '',
              },
            ],
            [
              requiredLabel('短剧名称：'),
              'title',
              {
                type: 'text',
                placeholder: '请输入短剧名称',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '短剧名称2：',
              'title2',
              {
                type: 'text',
                placeholder: '请输入短剧名称2',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '短剧名称3：',
              'title3',
              {
                type: 'text',
                placeholder: '请输入短剧名称3',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              '商务评级：',
              'external_content_rating',
              {
                type: 'select',
                placeholder: '请选择商务评级',
                options: contentRates,
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              requiredLabel('商务负责人：'),
              'business_principal',
              {
                type: 'text',
                placeholder: '请输入商务负责人',
              },
            ],
            [
              '视频质量评级：',
              'content_rating',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="text-lg">
                      {passFormData.value.content_rating}
                      {passFormData.value.rating_detail?.detail ? (
                        <Button class="btn btn-link btn-sm" onClick={() => {
                          const scoreData = ref<M.IRatingDetail[]>([])
                          const list = passFormData.value.rating_detail?.detail || []
                          const data = list && list.length > 0
                            ? list.map((item, index) => ({
                              id: String(index + 1),
                              category: item.category || '',
                              criteria: item.criteria || '',
                              desc: item.desc || '',
                              score: (item.score !== undefined && item.score !== null) ? item.score : undefined,
                              weight: item?.weight || 0,
                            }))
                            : cloneDeep(defaultTemplate as unknown as M.IRatingDetail[])

                          scoreData.value = data.map(item => ({
                            id: item.id,
                            category: item.category,
                            criteria: item.criteria,
                            score: item.score || 0,
                            desc: item.desc,
                            weight: item.weight,
                          }))
                          const tableData = ref<M.IRatingDetail[]>(data as M.IRatingDetail[])
                          const totalScore = ref(passFormData.value.content_rating)
                          const closeDialog = openDialog({
                            title: '评分详情',
                            body: () => (
                              <div class="space-x-y flex flex-col">
                                <EvaluateTable
                                  data={tableData.value}
                                  onScoreChange={(newData, total, rate) => {
                                    scoreData.value = newData.map(item => ({
                                      id: item.id,
                                      category: item.category,
                                      criteria: item.criteria,
                                      score: item.score || 0,
                                      desc: item.desc,
                                      weight: item.weight,
                                    }))
                                    totalScore.value = rate
                                  }} />
                                <div class="flex justify-end py-4">
                                  <Button class="btn btn-ghost btn-sm" onClick={() => {
                                    closeDialog()
                                  }}>取消</Button>
                                  <Button class="btn btn-primary btn-sm" onClick={() => {
                                    passFormData.value.rating_detail = {
                                      detail: scoreData.value,
                                    }
                                    passFormData.value.content_rating = totalScore.value
                                    closeDialog()
                                  }}>确定</Button>
                                </div>
                              </div>

                            ),
                          })
                        }}>
                          评分详情
                        </Button>
                      ) : null}
                    </div>
                  )
                },
              },
              {
                class: `col-span-1 ${(passFormData.value.content_rating) ? 'block' : 'hidden'}`,
              },
            ],
            [
              requiredLabel('短剧简介：'),
              'description',
              {
                type: 'textarea',
                placeholder: '请输入短剧简介',
              },
              {
                class: 'col-span-3',
              },
            ],
            [
              '合作方名称：',
              'partner_name',
              {
                type: 'text',
                placeholder: '请输入合作方名称',
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              [
                requiredLabel('网盘链接：'),
                'netdisk_url',
                {
                  type: 'text',
                  placeholder: '请输入网盘链接',
                },
                {
                  class: 'flex-1',
                },
              ], [
                () => <div class="h-[28px]" />,
                'xx',
                {
                  type: 'custom',
                  render: () => (
                    <Button class="btn btn-primary btn-sm" disabled={oprLoading.value} onClick={async () => {
                      try {
                        oprLoading.value = true
                        await apiAnalysisNetUrl({
                          series_resource_id: +route.params.id,
                          netdisk_url: currentResource.value.netdisk_url as string,
                        })
                        showSuccessToast('操作成功')
                      } catch (error: any) {
                        showFailToast(error.response.data.err_msg || '操作失败')
                      } finally {
                        oprLoading.value = false
                      }
                    }}
                    >
                      {oprLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                      重新解析网盘
                    </Button>
                  ),
                },
                {
                  class: currentResource.value.check_status === 2 ? 'block' : 'hidden',
                },
              ],
            ],
            [
              requiredLabel('视频内嵌人声语言：'),
              'language_code',
              {
                type: 'multi-select',
                search: true,
                maxlength: 1,
                placeholder: '请选择视频内嵌人声语言',
                options: langKey.map((langCode, index) => {
                  return {
                    label: langValue[index],
                    value: langCode,
                  }
                }),
              },
            ],
            [
              '视频内嵌字幕语言：',
              'subtitle_language_code',
              {
                type: 'multi-select',
                search: true,
                maxlength: 1,
                placeholder: '请选择视频内嵌字幕语言',
                options: langKey.map((langCode, index) => {
                  return {
                    label: langValue[index],
                    value: langCode,
                  }
                }),
              },
            ],
            [
              '上传字幕语言(多语言翻译输入)：',
              'upload_subtitle_language_code',
              {
                type: 'multi-select',
                search: true,
                maxlength: 1,
                placeholder: '请选择上传字幕语言',
                options: langKey.map((langCode, index) => {
                  return {
                    label: langValue[index],
                    value: langCode,
                  }
                }),
              },
            ],
            [
              requiredLabel('资源类型：'),
              'resource_type',
              {
                type: 'select',
                placeholder: '请选择短剧类型',
                options: [{
                  value: 1,
                  label: '本土剧',
                }, {
                  value: 2,
                  label: '翻译剧',
                }],
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('短剧类型：'),
              'resource_type_v2',
              {
                type: 'select',
                placeholder: '请选择短剧类型',
                options: resourceTypeOptions,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('承制方：'),
              'contractor',
              {
                type: 'text',
                placeholder: '请输入承制方',
                disabled: currentResource.value.resource_type_v2 !== 5,
              },
              {
                class: `col-span-1`,
              },
            ],
            [
              requiredLabel('发行轮次：'),
              'release_round',
              {
                type: 'select',
                placeholder: '请选择发行轮次',
                options: releaseRounds,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('VIP类型：'),
              'vip_type',
              {
                type: 'select',
                placeholder: '请选择VIP类型',
                options: vipTypes,
                autoInsertEmptyOption: false
              },
              {
                transform: transformNumber,
              },
            ],
            [
              () => 'N天后开放其他解锁方式：',
              'unlocked_other_days',
              {
                type: 'number',
                placeholder: '请输入每天更新集数',
                disabled: currentResource.value.vip_type === 0,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              () => currentResource.value.vip_type === 2 ? requiredLabel('每天更新集数：') : '每天更新集数：',
              'vip_everyday_update_sn',
              {
                type: 'number',
                placeholder: '请输入每天更新集数',
                disabled: currentResource.value.vip_type !== 2,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '授权平台：',
              'auth_platform',
              {
                type: 'multi-select',
                search: true,
                placeholder: '请选择授权平台',
                options: authPlatforms,
              },
            ],
            [
              '授权语言：',
              'auth_language',
              {
                type: 'select',
                placeholder: '请选择授权语言',
                options: authLanguages,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '授权区域：',
              'auth_area',
              {
                type: 'select',
                placeholder: '请选择授权区域',
                options: authAreas,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '分成模式：',
              'sharing_model',
              {
                type: 'select',
                placeholder: '请选择分成模式',
                options: sharingModels,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              <div class="flex items-center gap-2">
                {requiredLabel('授权价格')}
                <div class="font-bold">(请填写美元)：</div>
              </div>,
              'auth_price',
              {
                type: 'number',
                placeholder: '请输入授权价格（美元）',
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '投放金额：',
              'put_money',
              {
                type: 'number',
                placeholder: '请输入投放金额',
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '收入流水：',
              'revenue_flow',
              {
                type: 'number',
                placeholder: '请输入收入流水',
              },
              {
                transform: transformNumber,
              },
            ],
            [
              '预估分成：',
              'estimate_sharing',
              {
                type: 'number',
                placeholder: '请输入预估分成',
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('总集数：'),
              'count',
              {
                type: 'number',
                placeholder: '请输入总集数',
                suffix: () => <div>集</div>,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('付费第1集：'),
              'unlocked_episodes',
              {
                type: 'number',
                placeholder: '请输入付费第1集',
                suffix: () => <div>集</div>,
              },
              {
                transform: transformNumber,
              },
            ],
            [
              requiredLabel('是否可预告：'),
              'preview_status',
              {
                type: 'radio',
                options: [{
                  value: 1,
                  label: '不可预告',
                }, {
                  value: 2,
                  label: '可预告',
                }],
              },
              {
                class: 'col-span-1',
              },
            ],
            [
              () => currentResource.value.preview_status === 2 ? requiredLabel('海报可预告时间') : '海报可预告时间',
              'poster_preview_time',
              {
                type: 'datetime',
                disabled: currentResource.value.preview_status !== 2,
                rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00',
                displayFormat: 'YYYY-MM-DD HH:mm:ss',
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              () => '预告片可预告时间',
              'preview_time',
              {
                type: 'datetime',
                disabled: currentResource.value.preview_status !== 2,
                rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00',
                displayFormat: 'YYYY-MM-DD HH:mm:ss',
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              requiredLabel('是否可免费'),
              'free_status',
              {
                type: 'radio',
                options: freeStatusOptions,
              },
            ],
            [
              () => currentResource.value.free_status === 1 ? requiredLabel('最早可免费时间：') : '最早可免费时间：',
              'free_start_time',
              {
                type: 'datetime',
                disabled: currentResource.value.free_status !== 1,
                rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00',
                displayFormat: 'YYYY-MM-DD HH:mm:ss',
                // min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              () => currentResource.value.release_round === 1 ? requiredLabel('可投放时间：') : '可投放时间：',
              'deploy_time',
              {
                type: 'datetime',
                rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00',
                displayFormat: 'YYYY-MM-DD HH:mm:ss',
                // min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              '上线时间：',
              'online_time',
              {
                type: 'datetime',
                rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00',
                displayFormat: 'YYYY-MM-DD HH:mm:ss',
                // min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              requiredLabel('授权开始时间：'),
              'auth_start_time',
              {
                type: 'datetime',
                displayFormat: 'YYYY-MM-DD HH:mm:ss',
                // min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              requiredLabel('授权结束时间：'),
              'auth_end_time',
              {
                type: 'datetime',
                displayFormat: 'YYYY-MM-DD HH:mm:ss',
                // min: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                transform: transformTimestamp,
                hint: () => <div class="text-xs">提示：这里设定的是(UTC-08:00)北京时间</div>,
              },
            ],
            [
              '是否可提前投放：',
              'deploy_advance',
              {
                type: 'radio',
                options: [{
                  value: 1,
                  label: '是',
                }, {
                  value: 2,
                  label: '否',
                }],
              },
              {
                class: 'col-span-1'
              },
            ],
            [
              ' ',
              'x',
              {
                type: 'custom',
                render: () => {}
              },
              {
                class: 'col-span-2'
              },
            ],
            [
              '演员信息',
              'actors',
              {
                type: 'custom',
                render: () => {
                  return (
                    <div class="flex flex-col">
                      {
                        !currentResource.value?.actors || currentResource.value.actors?.length === 0
                          ? '- 暂无 -'
                          : currentResource.value.actors?.map(actor => {
                            return (
                              <div class="flex space-x-2">
                                <div title={actor.name} class="line-clamp-1 overflow-hidden max-w-[200px] space-x-1 break-all break-words">
                                  演员：{ actor.name }
                                </div>
                                {typeof actor.gender === 'number' ? <span>性别： { ['未知', '男', '女'][actor.gender || 0] }</span> : null }
                                <div title={actor.home_page} class="line-clamp-1 flex-1 break-words">链接： <a class={actor.home_page ? 'link' : ''} target="_blank" href={actor.home_page ? actor.home_page : 'javascript:void(0);'}>{actor.home_page}</a>
                                </div>
                              </div>
                            )
                          })
                      }
                      <Button class="btn btn-primary btn-xs mt-2 w-[75px]" onClick={editActor}>编辑</Button>
                    </div>
                  )
                },
              },
              {
                class: 'col-span-3',
              },
            ],
            [
              '婚姻状态：',
              'marital_status',
              {
                type: 'multi-select',
                placeholder: '请选择婚姻状态',
                options: maritalStatusOptions,
              },
            ],
            [
              '生育状态：',
              'fertility_status',
              {
                type: 'multi-select',
                placeholder: '请选择生育状态',
                options: fertilityStatusOptions,
              },
            ],
            [
              '年龄：',
              'age',
              {
                type: 'multi-select',
                placeholder: '请选择年龄',
                options: ageOptions,
              },
            ],
          ]}
        />
        <CheckInfoForm />
        <div class="flex justify-start gap-x-2">
          <Button class="btn-default btn btn-sm" onClick={() => {
            cancel()
          }}
          >返回
          </Button>
          <Button class="btn btn-primary btn-sm" disabled={saveLoading.value} onClick={() => {
            void saveCraft(() => {
              setTimeout(() => {
                showSuccessToast('操作成功')
                passFormData.value = {}
                void getLabelList()
                void getDetail()
              }, 500)
            })
          }}
          >
            {saveLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
            保存
          </Button>
        </div>
      </Wrapper>
    </>
  )
})

export default ResourceEntryDetailPage
