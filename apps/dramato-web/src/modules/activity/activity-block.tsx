import { createComponent } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { pick } from '@skynet/ui/utils'
import { jsBridge } from 'src/lib/jsbridge'
import { track } from 'src/lib/track'
import { useRoute } from 'vue-router'
type ActivityBlockOptions = {
  props: {
    type: 'image' | 'video'
    button?: M.ActivityButton | null
    src: string
  }
}
export const ActivityBlock = createComponent<ActivityBlockOptions>({
  props: {
    type: 'image',
    src: '',
    button: null,
  },
}, props => {
  const route = useRoute()
  const onClick = () => {
    console.log('click', JSON.stringify(props.button))
    if (!props.button) return
    if (props.button.target_type === 1) {
      void doPurchase()
    } else {
      void openView()
    }
    track('activity_H5', 'button', 'click', {
      activity_id: route.query.event_id,
      button_id: props.button.button_id,
      user_id: route.query.user_id,
    })
  }
  const doPurchase = async () => {
    console.log('try purchase')
    if (props.button?.pay_product_sku) {
      const response = await jsBridge('doPurchase', {
        pay_type: props.button.pay_product_sku.pay_channel,
        ...pick(props.button.pay_product_sku, ['delivery_details', 'product_id', 'discount_price', 'product_type', 'has_discount', 'currency', 'sku_id', 'r_info']),
      })
    }
  }
  const openView = async () => {
    if (!props.button) {
      window.alert('对应的链接不存在')
      return
    }
    if (props.button.target_content.startsWith('dramawave://')) {
      window.location.href = props.button.target_content
    } else if (props.button.target_content) {
      await jsBridge('view.open', { url: props.button.target_content })
    }
  }
  return () => (
    <MergeClass baseClass="flex">
      <img src={props.src} alt="picture" class="w-full h-auto" onClick={onClick} />
    </MergeClass>
  )
})
