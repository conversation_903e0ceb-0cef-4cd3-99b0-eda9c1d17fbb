import { createComponent } from '@skynet/shared'
import { AppName, appName } from 'src/lib/constants'
import { dramawave, commonBg, description, dramabuzz, googlePlay, appStore, series, credit, dramawaveLogo, dramabuzzLogo } from './image/image'
import 'src/assets/style/global.css'
import { initOldI18n } from 'src/init/init-old-i18n'
export const HomePage = createComponent(null, () => {
  initOldI18n()
  return () => (
    <div class="size-screen relative select-none overflow-hidden px-[6.5%] pt-[5.55%]">
      <img src={commonBg} class="absolute left-0 top-0 size-full object-cover" />
      <img src={series} class="absolute right-[7.8125%] top-0 h-screen w-[40.52%] object-cover" />
      <div class="relative z-up flex size-full flex-col">
        <div class="flex w-full items-center justify-between">
          <div class="flex w-full items-center">
            <img src={appName === 'dramawave' ? dramawaveLogo : dramabuzzLogo} class="h-auto w-[3.9%] object-cover" />
            {
              appName === 'dramawave'
                ? <img src={dramawave} class="ml-3 h-auto w-[14.58%] object-cover" />
                : <img src={dramabuzz} class="ml-8 h-auto w-[11.32%] object-cover" />
            }
          </div>
          <a href="/recharge-h5/" target="_blank">
            <div class="w-164px h-42px flex shrink-0 cursor-pointer items-center justify-center gap-x-2 rounded-lg border border-solid border-white text-base text-[var(--text-5)]">
              <img src={credit} class="size-6" />
              <div>RECHARGE</div>
            </div>
          </a>
        </div>
      </div>
      <div class="absolute left-[6.5%] top-[32.6%] z-up flex h-[38.7%] w-[39.43%] flex-col">
        <img src={description} width={757} height={258} class="h-auto w-full scale-90 object-cover" />
        <div class="mt-[16.13%] flex justify-between gap-[32px] px-[13.5%]">
          <img src={googlePlay} class="h-auto w-[calc(50%-1rem)] cursor-pointer object-fill" onClick={() => window.open('https://play.google.com/store/apps/details?id=com.dramawave.app', '_blank')} />
          <img src={appStore} class="h-auto w-[calc(50%-1rem)] cursor-pointer object-fill" onClick={() => window.open('https://apps.apple.com/us/app/dramawave/id6670430706', '_blank')} />
        </div>
      </div>
      {
        appName === 'dramabuzz'
          ? (
              <div class="z-99 absolute bottom-5 text-left text-base font-normal leading-[27px] text-white/80">
                <p class="flex gap-4">
                  <a class="underline" href="https://thedramabuzz.com/rules/terms.html">Terms of Use</a>
                  {' '}
                  |
                  {' '}
                  <a class="underline" href="https://thedramabuzz.com/rules/privacy.html">Privacy Policy</a>
                </p>
                <p>Contact Us：{appName}<EMAIL></p>
                <p>© {AppName}, All Rights Reserved Stardust Online Pte Ltd.</p>
              </div>
            )
          : (
              <div class="absolute bottom-5 z-footer text-left text-base font-normal leading-[27px] text-white/80">
                <p class="flex gap-4">
                  <a class="underline" href="https://mydramawave.com/rules/terms.html">Terms of Use</a>
                  {' '}
                  |
                  {' '}
                  <a class="underline" href="https://mydramawave.com/rules/privacy.html">Privacy Policy</a>
                </p>
                <p>Contact Us：{appName}<EMAIL></p>
              </div>
            )
      }
    </div>
  )
})

export default HomePage
