import { httpClient } from 'src/lib/http-client'

export type User = {
  open_id?: string
  status?: string
  token?: string
  auth_url?: string
  user?: {
    chat_id: string
    department: string
    display_name: string
    employee_no: string
    gender: string
    is_leader: 1
    leader_id: string
    mail: string
    open_id: string
    organization: string
    phone: string
    position: string
    source: string
    username: string
    user_id: string
  }
}

type PostSmsLoginParams = {
  username: string
  password: string
  mfa?: string
}

type CheckInternalLoginStatusParams = {
  open_id: string
}

export const apiPostSmsLogin = (data: PostSmsLoginParams) => {
  return httpClient.post<ApiResponse<User>>('/backend/login', data)
}

export const apiExternalLogin = (data: PostSmsLoginParams) => {
  return httpClient.post<ApiResponse<User>>('/backend/login_mfa', data)
}

export const apiSSOLogin = (data: PostSmsLoginParams) => {
  return httpClient.post<ApiResponse<User>>('/backend/sso/login', data)
}

// 飞书确认登录后， 轮训登录结果
export const apiInternalLoginStatus = (data: CheckInternalLoginStatusParams) => {
  return httpClient.post<ApiResponse<User>>('/backend/sso/check', data)
}
