import { createComponent, fn, mc } from '@skynet/shared'
import { Fn, onClickOutside } from '@vueuse/core'
import { onBeforeUnmount, onMounted, onUnmounted, ref, VNodeChild } from 'vue'
import { SvgIcon } from '../ui'

export type DialogOptions = {
  props: {
    title?: string | (() => VNodeChild)
    customClose?: null | (() => VNodeChild)
    body: unknown
    visible?: boolean
    closeVisible?: boolean
    customClass?: string
    beforeClose?: () => void | boolean
    hideParentWhenChildOpen?: boolean
    showParentWhenChildClose?: boolean
    canEscClose?: boolean
    mainClass?: string
    closeClass?: string
    preventScroll?: boolean
  }
  emits: {
    close: Fn
    clickOutside: (e: MouseEvent) => void
  }
}

export const Dialog = createComponent<DialogOptions>({
  props: {
    title: '',
    customClose: null,
    body: '',
    visible: false,
    closeVisible: true,
    customClass: '',
    beforeClose: fn,
    hideParentWhenChildOpen: false,
    showParentWhenChildClose: false,
    canEscClose: true,
    mainClass: 'px-5',
    closeClass: '',
    preventScroll: true,
  },
  emits: {
    close: fn,
    clickOutside: fn,
  },
}, (props, { emit }) => {
  const wrapper = ref<HTMLElement>()
  onMounted(() => {
    props.preventScroll && document.body.classList.add('overflow-hidden')
    props.canEscClose && window.addEventListener('keydown', keyDownFn)
    const el = document.activeElement
    if (!wrapper.value) return
    if (!wrapper.value.contains(el)) {
      const focusable = wrapper.value.querySelector('input , textarea , button')
      if (!focusable) {
        wrapper.value.focus()
      } else {
        // @ts-expect-error 不认识？
        focusable.focus()
      }
    }
  })
  const main = ref<HTMLElement>()
  const cleanupClickOutside = onClickOutside(main, e => {
    emit('clickOutside', e)
  })
  onBeforeUnmount(() => {
    cleanupClickOutside()
  })
  const close = () => {
    if (props.beforeClose?.() === false) return
    emit('close')
  }
  // esc 关闭弹窗
  const keyDownFn = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      close()
    }
  }
  onUnmounted(() => {
    props.preventScroll && document.body.classList.remove('overflow-hidden')
    window.removeEventListener('keydown', keyDownFn)
  })
  return () =>
    props.visible
      ? (
          <x-dialog class="block relative z-dialog" tabindex="0" ref="wrapper">
            <x-dialog-shadow class="fixed bottom-0 left-0 right-0 top-0 h-full w-full bg-black bg-opacity-40" />
            <x-dialog-main-wrapper ref={main} class={
              mc('fixed flex w-screen h-screen left-0 top-0 pc:left-1/2 pc:top-1/2 pc:-translate-x-1/2 pc:-translate-y-1/2 pad:left-1/2 pad:top-1/2',
                'pad:-translate-x-1/2 pad:-translate-y-1/2 transform flex-col gap-4 pc:rounded-xl pad:rounded-xl bg-[var(--fill-4)] py-5 shadow-md pad:h-auto',
                'pc:h-auto overflow-auto pad:w-560px pc:w-560px', props.customClass)
            }
            >
              {props.customClose instanceof Function
                ? props.customClose()
                : (
                    <SvgIcon
                      class={mc('fixed right-[20px] top-6 h-5 w-5 cursor-pointer text-gray-500', props.closeClass)}
                      name="ic_close"
                      onClick={close}
                    />
                  )}
              {props.title
              && (
                <header class="shrink-0 grow-0 px-5">
                  {props.title instanceof Function
                    ? props.title()
                    : <div class="text-lg font-medium leading-relaxed text-slate-950">{props.title}</div>}
                </header>
              )}
              <main class={mc('flex-1 max-h-[80vh] overflow-y-auto', props.mainClass)}>
                {props.body instanceof Function ? props.body() : <>{props.body}</>}
              </main>
            </x-dialog-main-wrapper>
          </x-dialog>
        )
      : null
})
