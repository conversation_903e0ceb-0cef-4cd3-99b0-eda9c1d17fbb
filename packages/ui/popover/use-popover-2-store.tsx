import { createCachedFn, createStringId, Key, OptionsToProps } from '@skynet/shared'
import { ref } from 'vue'
import { Popover2, Popover2Options } from './popover-2'
import { omit } from 'lodash-es'

type Props = OptionsToProps<Popover2Options>
type Item = Props & { id?: string }
export const usePopover2Store = createCachedFn((_key: Key) => {
  const popoverQueue = ref<Item[]>([])

  const addPopover = (popover: Props) => {
    const id = createStringId()
    const p: Item = { ...popover, id, visible: false }
    popoverQueue.value.push(p)
    return id
  }
  /**
   * 删除popover
   * @param id
   */
  const removePopover = (id: string) => {
    const index = popoverQueue.value.findIndex(item => item.id === id)
    popoverQueue.value.splice(index, 1)
  }

  const setPopoverVisible = (id: string, visible?: boolean) => {
    const popover = findPopover(id)
    if (!popover) return
    popover.visible = visible ?? !popover.visible
  }

  const findPopover = (id: string) => {
    return popoverQueue.value.find(item => item.id === id)
  }

  const updatePopover = (id: string, popover: Partial<Item>) => {
    const index = popoverQueue.value.findIndex(item => item.id === id)
    popoverQueue.value[index] = { ...popoverQueue.value[index], ...popover }
  }

  const renderPopovers = () => {
    return popoverQueue.value.map(item => {
      return (
        <Popover2 key={item.id} id={item.id} {...omit(item, ['id'])} />
      )
    })
  }

  return {
    popoverQueue,
    renderPopovers,
    addPopover,
    removePopover,
    setPopoverVisible,
    updatePopover,
    findPopover,
  }
})
