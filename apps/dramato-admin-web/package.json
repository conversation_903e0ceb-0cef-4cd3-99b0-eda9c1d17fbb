{"name": "dramato-admin-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"pnpm run dev:admin\" \"pnpm run dev:showcase\"  ", "dev:admin": "vite --host --mode development --port 5173 --no-clearScreen", "dev:showcase": "pnpm --filter='ui-showcase' run dev", "build": "vite build --mode production", "build:test": "NODE_OPTIONS='--max-old-space-size=8192' vite build --mode staging", "build:prod": "NODE_OPTIONS='--max-old-space-size=8192' vite build --mode production", "preview": "vite preview --host --base=./", "deploy:test": "bin/deploy.sh test", "deploy:prod": "bin/deploy.sh prod", "jenkins:test": "bin/jenkins.sh test", "jenkins:prod": "bin/jenkins.sh prod", "//webp": "pnpm run webp /Users/<USER>/Downloads/1 会将1目录下的图片转换为webp格式（非递归）", "webp": "bin/webp.mjs", "check": "tsc --noEmit -p tsconfig.app.json"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@shopify/draggable": "^1.1.3", "@skynet/client-track": "workspace:*", "@skynet/preset": "workspace:*", "@skynet/shared": "workspace:*", "@skynet/ui": "workspace:*", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "ali-oss": "6.22.0", "axios": "catalog:", "choices.js": "catalog:", "cropperjs": "^1.6.1", "dayjs": "catalog:", "echarts": "catalog:", "element-plus": "2.9.3", "emoji-mart-vue-fast": "^15.0.3", "github-markdown-css": "^5.6.1", "js-cookie": "3.0.5", "js-md5": "0.8.3", "konva": "^9.3.20", "lodash-es": "catalog:", "lottie-web": "^5.12.2", "md-editor-v3": "^5.0.1", "pinia": "^2.3.1", "qrcode": "^1", "qs": "^6.14.0", "swrv": "1.0.4", "vite-bundle-analyzer": "catalog:", "vue": "catalog:", "vue-konva": "^3.2.0", "vue-multiselect": "catalog:", "vue-router": "catalog:", "weixin-js-sdk": "^1.6.5", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@iconify/vue": "catalog:", "@skynet/vite-plugin-svg-icons": "workspace:*", "@types/ali-oss": "catalog:", "@types/js-cookie": "catalog:", "@types/lodash-es": "catalog:", "@types/sortablejs": "catalog:", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "autoprefixer": "catalog:", "daisyui": "catalog:", "monaco-editor": "catalog:", "monaco-types": "0.1.0", "postcss": "catalog:", "postcss-import": "catalog:", "postcss-nested": "catalog:", "tailwind-scrollbar": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:", "unplugin-element-plus": "0.9.1", "vconsole": "catalog:", "vite": "catalog:", "vite-plugin-markdown": "2.2.0", "zx": "catalog:"}, "browserslist": ["defaults", "chrome > 50", "edge > 14"]}