import { bindLoading, createComponent, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { coins, dialogBg, mercadoPagoCard, successDialog, redeemDialog } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { ref, watch, watchEffect } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { openDialog } from 'src/modules/common/dialog'
import { useCoinsStore } from '../../coins-store'
import { apiRedeemGift } from '../../coins-api'
import { useRedeem } from '../use-redeem'
type GiftOptions = {
  props: {
    gift?: GiftType
  }
}
interface TaskEventMap {
  [key: number]: string
}

let closeConfirmAccountDialog: Fn
let closeConfirmDialog: Fn

const customLanguage = {
  firstInputTitle: 'CLABE',
  secondInputTitle: 'Nombre Completo',
  firstInputPlaceholder: 'Obligatorio (18 numeros)',
  secondInputPlaceholder: 'Obligatorio (Máximo 100 caracteres)',
  notMatchError: 'Por favor vuelve a entrar',
  emptyError: 'Por favor ingresa',
  submit: 'Entregar',
  transferSuccessContent: 'Te enviamos el dinero a tu CLABE. Tu dinero llegará en 72 horas.',
  transferSuccessBtnTxt: 'Entiendo',
  redeemFailedTitle: 'Redención fallida',
  redeemFailedContent: 'Hay un error. Por favor inténtalo de nuevo más tarde.',
  redeemFailedBtnTxt: 'Entiendo',
}

export const MexicoGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      tags: [], // 礼品标签
      type: 'moxico',
    },
  },
}, props => {
  const { t } = useLocale()
  const tipsRef = ref<HTMLDivElement>()
  const accountError = ref('')
  const fullnameError = ref('')
  const redeemLoading = ref(false)

  const { account, getExchangeDetails } = useCoinsStore()
  const { commonTitle, reachLimit } = useRedeem(props.gift)

  const formData = ref({
    account: '',
    full_name: '',
  })

  watchEffect(() => {
    if (account.value) {
      formData.value = {
        account: account.value?.account || '',
        full_name: account.value?.full_name || '',
      }
    }
  })

  const openRedeemFailDialog = () => { // 提现失败
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: () => (
        <x-title class="relative block h-[68px] w-full">
          <img src={redeemDialog} width={100} height={68} class="absolute left-1/2 top-1/2 h-full w-auto -translate-x-1/2 object-cover" />
        </x-title>
      ),
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="text-center text-lg font-bold leading-snug text-[#090609]">{customLanguage.redeemFailedTitle}</x-title>
            <x-content class="mb-6 mt-2 w-full text-center text-sm font-normal text-[#ff2d3e]">
              {customLanguage.redeemFailedContent}
            </x-content>
            <x-btn class="w-full rounded-lg border-none bg-[#fc2763] px-3 py-2.5 text-base font-bold text-white" onClick={() => closeDialog()}>
              <p class="line-clamp-3 w-full break-all text-center">{customLanguage.redeemFailedBtnTxt}</p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openTransferSuccessDialog = () => { // 提现成功
    closeConfirmDialog?.()
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: () => (
        <x-title class="relative block h-[94px] w-full">
          <img src={successDialog} width={94} height={94} class="absolute left-1/2 top-1/2 h-full w-auto -translate-x-1/2 object-cover" />
        </x-title>
      ),
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 mt-2 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="text-center text-lg font-bold text-[#090609]">{t('coins.exchange.transferSuccessTitle')}</x-title>
            <x-content class="mb-5 mt-3 w-full text-center text-sm font-medium text-[#4d4e4f]">
              {customLanguage.transferSuccessContent}
            </x-content>
            <x-btn class="w-full rounded-lg border-none bg-[#fc2763] px-3 py-2.5 text-base font-bold text-white" onClick={() => closeDialog()}>
              <p class="line-clamp-3 w-full break-all text-center" onClick={() => {
                if (props.gift?.cash_success_btn_link) {
                  window.location.href = props.gift?.cash_success_btn_link
                } else {
                  void getExchangeDetails()
                  closeDialog()
                }
              }}
              >{props.gift?.cash_success_btn_txt ? props.gift?.cash_success_btn_txt : customLanguage.transferSuccessBtnTxt}
              </p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openConfirmDialog = () => { // 确认兑换弹窗
    closeConfirmAccountDialog?.()
    closeConfirmDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      mainClass: 'rounded-sm',
      title: commonTitle,
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col">
            <x-title class="line-clamp-2 w-full text-center text-lg font-bold text-[#090609]">{t('coins.exchange.transferXConfirm', { X: props.gift?.amount })}</x-title>
            <x-content class="mb-6 mt-2 w-full text-center text-sm text-[#4d4e4f]">
              {t('coins.exchange.transferToYourAccountX', { X: formData.value.account })}
            </x-content>
            <Button class="h-11 w-full rounded-lg border-none bg-[#fc2763] text-base font-bold text-white" onClick={redeemHandler}>{t('coins.exchange.confirmBtn')}</Button>
            <Button class="mt-3 w-full border-none bg-transparent text-base text-[#A1A0A3]" onClick={() => {
              closeConfirmDialog?.()
              openAccountConfirmDialog()
            }}
            >Volver</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const redeemHandler = () => {
    if (redeemLoading.value) return
    void bindLoading(apiRedeemGift({
      id: props.gift?.id,
      account: formData.value.account,
      type: props.gift?.type,
      full_name: formData.value.full_name,
    }), redeemLoading).then(res => {
      if (res.code === 200) {
        openTransferSuccessDialog()
      }
    }).catch(() => {
      openRedeemFailDialog()
    }).finally(() => {
      closeConfirmDialog?.()
    })
  }

  const handleClick = () => {
    if (props.gift?.can_exchange) {
      if (props.gift?.button_txt && props.gift?.button_link) {
        window.location.href = props.gift.button_link
        return
      }
      if (reachLimit.value) {
        if (formData.value.account && formData.value.account !== '') {
          openConfirmDialog()
        } else {
          openAccountConfirmDialog()
        }
      } else {
        track('exchangezone', 'earncoins_button', 'click')
        window.location.href = 'dramawave://dramawave.app/rewards_task?enter_from=exchange_page'
      }
    }
    trackHandle('click')
  }

  const flag = ref(false)
  watch(() => [tipsRef.value, flag.value], () => {
    flag.value = false
    if (tipsRef.value) {
      const lineHeight = parseFloat(getComputedStyle(tipsRef.value).lineHeight)
      const fontSize = parseFloat(getComputedStyle(tipsRef.value).fontSize)
      if (fontSize <= 8) {
        return
      }
      const maxHeight = lineHeight * 2
      if (tipsRef.value.scrollHeight > maxHeight) {
        tipsRef.value.style.fontSize = `${fontSize - 2 <= 8 ? 8 : fontSize - 2}px`
        flag.value = true
      }
    }
    trackHandle('show')
  })
  const trackHandle = (event: 'click' | 'load' | 'show' | 'successful') => {
    if (props?.gift?.task_id) {
      const taskEventMap: TaskEventMap = {
        10: 'watchreels',
        11: 'checkin',
      }
      const eventName = taskEventMap[props.gift.task_id]

      if (eventName) {
        track(eventName, 'redeemlevel', event, {
          gift_id: props.gift.id,
        })
      }
    }
  }

  const openAccountConfirmDialog = () => {
    const handleAccountBlur = () => {
      if (!formData.value.account) {
        accountError.value = customLanguage.emptyError
        return
      }
      // 18位数字
      if (!/^[0-9]{18}$/.test(formData.value.account)) {
        accountError.value = customLanguage.notMatchError
      } else {
        accountError.value = ''
      }
    }
    const handleConfirmAccountBlur = () => {
      if (!formData.value.full_name) {
        fullnameError.value = customLanguage.emptyError
        return
      }
      // 英文、空格、中划线（-）、点（.)、逗号（,)；小于100 字符
      if (!/^[a-zA-Z\s\-.,]{1,100}$/.test(formData.value.full_name)) {
        fullnameError.value = customLanguage.notMatchError
      } else {
        fullnameError.value = ''
      }
    }
    closeConfirmAccountDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      closeVisible: true,
      body: () => (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col rounded-xl bg-cover bg-no-repeat pb-6 pt-12">
          <x-body-main class="mx-4 flex flex-col gap-y-4">
            <x-title class="truncate text-center text-lg font-bold text-[#090609]">Canjear {props.gift?.amount}</x-title>
            <x-form>
              <x-form-item class="block text-sm">
                <x-form-label class="flex items-center justify-start gap-x-[2px] text-sm font-bold leading-[16.80px] text-[#0b080b]">{customLanguage.firstInputTitle} <div class="text-[#fc2763]">*</div></x-form-label>
                <input
                  type="text"
                  maxlength={18}
                  value={formData.value.account}
                  onBlur={handleAccountBlur}
                  onInput={(e: Event) => {
                    const value = (e.target as HTMLInputElement).value
                    formData.value.account = value
                  }}
                  placeholder={customLanguage.firstInputPlaceholder}
                  class="dramato-input mt-2 bg-transparent py-3 pl-0 text-black"
                />
                <x-line class="block h-px w-full border-0 border-t border-solid border-[#CCCACB]"> </x-line>
                {accountError.value && <x-error class="line-clamp-2 w-full pt-1 text-xs text-[#ff3b30]">{accountError.value}</x-error>}
              </x-form-item>
              <x-form-item class="mt-4 block text-sm">
                <x-form-label class="flex items-center justify-start gap-x-[2px] text-sm font-bold leading-[16.80px] text-[#0b080b]">{customLanguage.secondInputTitle} <div class="text-[#fc2763]">*</div></x-form-label>
                <input
                  type="text"
                  value={formData.value.full_name}
                  onBlur={handleConfirmAccountBlur}
                  onInput={(e: Event) => {
                    formData.value.full_name = (e.target as HTMLInputElement).value
                    fullnameError.value = ''
                  }}
                  placeholder={customLanguage.secondInputPlaceholder}
                  class="dramato-input mt-2 bg-transparent py-3 pl-0 text-black"
                />
                <x-line class="block h-px w-full border-0 border-t border-solid border-[#CCCACB]"> </x-line>
                {fullnameError.value && <x-error class="line-clamp-2 w-full pt-[4px] text-xs text-[#ff3b30]">{fullnameError.value}</x-error>}
              </x-form-item>
            </x-form>
            <Button disabled={
              !formData.value.account
              || !formData.value.full_name
              || !!accountError.value
              || !!fullnameError.value
            } class="mt-4  h-11 w-full  rounded-lg border-none bg-[#fc2763] text-base font-bold text-white disabled:!bg-[#ffa0b6] disabled:text-[#fdfbfc]/50" onClick={() => {
              handleAccountBlur()
              handleConfirmAccountBlur()
              if (accountError.value || fullnameError.value) return
              openConfirmDialog()
            }}
            >
              Entregar
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  return () => (
    <x-gift-item class="flex gap-x-3 p-3">
      <x-gift-item-left class={mc('flex max-w-[44px] flex-col items-center justify-center gap-1', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-card class="box-border size-11 rounded-lg border border-solid border-[#ece9ec] px-1 py-3">
          <img src={props?.gift?.image_url ?? mercadoPagoCard} class="h-auto w-full object-cover" />
        </x-card>
        <x-price class="block w-full break-all text-center text-xs font-bold text-[#4d4e4f]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class={mc('flex flex-1 flex-col gap-1 truncate', props.gift?.can_exchange ? '' : 'opacity-30')}>
        <x-gift-title class="w-full truncate text-sm text-[#4d4e4f]">{props.gift.name}</x-gift-title>
        <x-tags>
          {props.gift.tags?.map(tag => (
            <x-tag-item class="mr-1 inline-flex items-center justify-center gap-5 rounded border border-solid border-[#ffd9c4] bg-[#fff7ea] px-2 py-1 text-[10px] text-[#fe7123]">{tag}</x-tag-item>
          ))}
        </x-tags>
        {
          props.gift?.can_exchange
          && (
            <x-gift-coins class="flex items-center text-sm">
              <img src={coins} width={16} height={16} class="mr-[2px] size-4 object-contain" />
              <x-gift-amount class="text-[#fc2763]">{props.gift.user_coins}</x-gift-amount>
              <x-separator class="px-[2px] text-[#a1a2a3]">/</x-separator>
              <x-gift-remains class="text-[#090609]">{props.gift.gift_coins}</x-gift-remains>
            </x-gift-coins>
          )
        }
        {
          props.gift?.can_exchange
          && (
            <x-gift-progress>
              <Progress class="mt-1 bg-[#ECE9EC]" value={props.gift.ratio} max={1} />
            </x-gift-progress>
          )
        }
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21 justify-center', props.gift?.button_tips ? 'gap-y-1' : '')}>
        <Button class={mc('min-h-8 w-full rounded-lg border-none px-1 pt-[2px] pb-[3px]  text-sm', !props.gift?.can_exchange ? 'bg-[#FFA0B6] !text-[#FDFBFC80]' : 'bg-[#fc2763] !text-white')} onClick={handleClick}>
          <p class="line-clamp-2 h-auto w-full text-wrap break-words text-center leading-4">
            {props.gift?.button_txt && props.gift?.button_txt !== '' ? props.gift?.button_txt : (reachLimit.value ? t('coins.earnings.exchange') : t('coins.exchange.earnCoins'))}
          </p>
        </Button>
        {props.gift?.button_tips && <x-tips class={mc('line-clamp-2 h-auto w-full break-words text-center text-[10px] leading-3 text-[#4d4e4f]', props.gift?.can_exchange ? '' : 'opacity-30')} ref={tipsRef}>{props.gift?.button_tips}</x-tips>}
      </x-gift-item-right>
    </x-gift-item>
  )
})
