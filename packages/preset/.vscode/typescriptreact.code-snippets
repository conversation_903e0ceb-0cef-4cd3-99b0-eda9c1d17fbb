{
  "Component": {
    "prefix": [
      "co",
      "cre"
    ],
    "body": [
      "import { createComponent } from '@skynet/shared'",
      "type ${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}Options = {",
      "  props: {}",
      "}",
      "export const ${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/} = createComponent<${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}Options>({",
      "  props: {},",
      "}, props => {",
      "  return () => (",
      "    <x-${TM_FILENAME_BASE/(^[A-Z][a-z]*|[a-z])([A-Z])?/${1:/downcase}${2:+-}${2:/downcase}/g} class=\"block\">",
      "      ${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/} $0",
      "    </x-${TM_FILENAME_BASE/(^[A-Z][a-z]*|[a-z])([A-Z])?/${1:/downcase}${2:+-}${2:/downcase}/g}>",
      "  )",
      "})",
      "",
      "export default ${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}",
    ]
  },
  "SimpleComponent": {
    "prefix": [
      "sco",
      "scre"
    ],
    "body": [
      "import { createComponent } from '@skynet/shared'",
      "export const ${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/} = createComponent(null, () => {",
      "  return () => (",
      "    <x-${TM_FILENAME_BASE/(^[A-Z][a-z]*|[a-z])([A-Z])?/${1:/downcase}${2:+-}${2:/downcase}/g} class=\"block\">",
      "      ${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/} $0",
      "    </x-${TM_FILENAME_BASE/(^[A-Z][a-z]*|[a-z])([A-Z])?/${1:/downcase}${2:+-}${2:/downcase}/g}>",
      "  )",
      "})",
      "",
      "export default ${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}",
    ]
  },
  "Functional Component": {
    "prefix": [
      "f",
      "fco",
      "fc",
    ],
    "body": [
      "import { MergeClass } from '@skynet/ui'",
      "import { FunctionalComponent } from 'vue'",
      "",
      "export const ${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}: FunctionalComponent<{}> = (props, { slots }) => (",
      "  <MergeClass tag=\"${TM_FILENAME_BASE/(^[A-Z][a-z]*|[a-z])([A-Z])?/${1:/downcase}${2:+-}${2:/downcase}/g}\" baseClass=\"block\">",
      "    {slots.default?.()}$0",
      "  </MergeClass>",
      ")",
      "",
      "export default ${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}",
    ]
  },
  "i18n translations": {
    "prefix": [
      "tt",
      "i18n"
    ],
    "body": [
      "import { mergeI18n } from 'src/h5_modules/i18n/i18n'",
      "",
      "// 请优先使用 CT 中的翻译，尽量不要重复翻译相同的单词",
      "export const T = mergeI18n({",
      "  'en-US': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {},",
      "  },",
      "  'zh-CN': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'ja-JP': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {},",
      "  },",
      "  'ko-KR': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'es-ES': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'pt-PT': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'vi-VN': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'th-TH': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'id-ID': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'tl-PH': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'fr-FR': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'de-DE': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'it-IT': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'ru-RU': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'tr-TR': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'ms-MY': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "  'zh-TW': {",
      "    ${TM_FILENAME_BASE/(.*)\\.i18n/${1:/camelcase}/}: {}, ",
      "  },",
      "})",
      "",
    ]
  }
}