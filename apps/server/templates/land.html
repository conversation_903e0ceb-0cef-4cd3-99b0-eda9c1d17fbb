<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
  <title>Dramawave</title>
  <style>
  * {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

*::before {
  box-sizing: border-box;
}

*::after {
  box-sizing: border-box;
}

ol,
ul {
  list-style: none;
}

button,
input {
  font-size: inherit;
  font-family: inherit;
  color: inherit;
}

img {
  max-width: 100%;
}

body {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
}

  body {
    background-color: #0b0b0b;
    color: #fff;
  }

  @media (min-width: 1000px) {
    #app {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    #app>article {
      flex: 1;
      display: flex;
    }
  }


  x-wrapper {
    flex: 1;
    display: block;
    background-size: cover;
    background-position: center;
    position: relative;
    padding-top: 28px;
    display: block;
    padding-bottom: 28px;
    overflow: hidden;
  }

  @media (min-width: 1000px) {
    x-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  main {
    display: block;
    margin-left: 12px;
    margin-right: 12px;
  }

  @media (min-width: 1000px) {
    main {
      display: flex;
      max-width: 1000px;
      column-gap: 50px;
    }
  }

  x-cover {
    display: flex;
    justify-content: center;
    align-items: center;
    /* 宽高比 3:4 */
    aspect-ratio: 3 / 4;
    margin: 0 28px;
    position: relative;
    overflow: hidden;
  }



  @media (min-width: 1000px) {
    x-cover {
      width: 400px;
      flex-shrink: 0;
      margin: 0;
    }
  }

  x-cover>.cover {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.20);
    background-color: lightgray;
  }

  x-cover>.play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  x-summary {
    display: block;
  }

  @media (min-width: 1000px) {
    x-summary {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      justify-content: center;
      align-items: center;
    }
  }

  x-summary x-desc {
    overflow: hidden;
    line-height: 1.4em;
    color: #AAA;
    font-size: 14px;
  }

  x-summary x-desc.folded {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
    text-overflow: ellipsis;
    max-height: 7em;
  }

  x-summary x-desc.unfolded {
    max-height: none;
    display: block;
  }

  x-summary x-more,
  x-summary x-pick-up {
    display: block;
    text-align: right;
    font-size: 12px;
  }

  x-summary x-pick-up {
    display: none;
  }

  x-summary h1 {
    margin-top: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-Icon-text-1, #FDFBFC);
    font-size: 20px;
    font-weight: 510;
  }

  @media (min-width: 1000px) {
    x-summary h1 {
      font-size: 24px;
      font-size: 38px;

      white-space: normal;
    }
  }

  x-summary ul {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0 auto;
  }

  x-summary ul li {
    line-height: 1.25rem;
    border-radius: 0.675rem;
    background: #313131;
    padding: 0 1rem;
    font-size: 12px;
  }

  x-summary button {
    background-color: blue;
    color: white;
    min-height: 48px;
    min-width: 10em;
    border: none;
    border-radius: 24px;
    /* 渐变背景，从左往右，FE8C0E 到 F700D8 */
    background: linear-gradient(to right, #FE8C0E, #F700D8);
    margin-top: 32px;
    font-size: 20px;
    font-weight: 700;
    position: fixed;
    bottom: 80px;
    left: 12px;
    right: 12px;
  }

  @media (min-width: 1000px) {
    x-summary button {
      position: relative;
      bottom: 0;
      left: 0;
      right: 0;
      width: 100%;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.2);
    }

    100% {
      transform: scale(1);
    }
  }

  x-summary button img {
    position: absolute;
    top: 15px;
    right: 18px;
    color: white;
    animation: pulse 1s infinite;
  }



  x-mask {
    display: block;
    position: absolute;
    inset: 0;
    height: 100%;
    z-index: 2;
    background: linear-gradient(to bottom, #0b0b0b1a, #0b0b0bff);
  }

  @media (min-width: 1000px) {
    x-mask {
      background: linear-gradient(to bottom, #0b0b0b6a, #0b0b0bff);
    }
  }

  x-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1;
    overflow: hidden;
  }

  x-bg img {
    /* 模糊效果 */
    filter: blur(10px);
    margin: -10px;
    display: block;
    width: calc(100% + 20px);
    max-width: none;
  }

  x-top-nav {
    position: sticky;
    top: 0;
    z-index: 10;
    color: #fff;
    background-color: #0b0b0b;
    display: grid;
    grid-template-areas:
      'logo title button'
      'logo subtitle button'
    ;
    grid-template-columns: max-content 1fr max-content;
    grid-template-rows: 1fr max-content;
    justify-content: left;
    align-items: center;
    justify-items: start;
    padding: 12px 18px;
    row-gap: 0px;
    column-gap: 8px;
  }

  @media (min-width: 1000px) {
    x-top-nav {
      position: fixed;
      top: 10px;
      left: 0;
      right: 0;
      z-index: 10;
      max-width: 1000px;
      margin: 0 auto;
      padding-left: 0;
      padding-right: 0;
      background-color: transparent;
    }
  }

  x-top-nav .logo {
    grid-area: logo;
  }

  x-top-nav .title {
    grid-area: title;
  }

  @media (min-width: 1000px) {
    x-top-nav .title {
      width: 164px;
      height: 21px;
    }
  }

  x-top-nav .subtitle {
    grid-area: subtitle;
    color: #666;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  @media (min-width: 1000px) {
    x-top-nav .subtitle {
      display: none;
    }
  }

  x-top-nav button {
    grid-area: button;
    display: flex;
    height: 38px;
    padding: 0px var(--number-16, 18px);
    justify-content: center;
    align-items: center;
    gap: 10px;
    border: none;
    border-radius: 19px;
    background: var(--brand-brand-6, #FC2763);
  }

  @media (min-width: 1000px) {
    x-top-nav button {
      display: none;
    }
  }

  </style>
</head>
<body>
  <x-index class="block">
    <div id="_data_series_info" style="display: none;"><%= JSON.stringify(it.seriesItem) %></div>
    <div id="_data_ip" style="display: none;"><%= JSON.stringify(it.ip) %></div>
    <div id="app" style="position: relative;">
      <x-top-nav-wrapper style="display: block; min-height: 62px;">
        <x-top-nav id="minorImages" style="display: none;">
          <img class="logo" data-src="https://static-v1.mydramawave.com/frontend_static/assets/logo-ZkLn7rqY.webp"
            width="37" height="37" />
          <img class="title" data-src="https://static-v1.mydramawave.com/frontend_static/assets/dramawave-JYta-J2M.webp"
            width="92" height="12" />
          <span class="subtitle"><%= it.seriesItem.name %></span>
          <button><%= it.t.open %></button>
        </x-top-nav>
      </x-top-nav-wrapper>
      <article class="art">
        <x-wrapper>
          <x-bg>
            <img class="bg" src="<%= it.seriesItem.cover %>" alt="background" />
            <x-mask />
          </x-bg>
          <main style="position: relative; z-index: 3;">
            <x-cover>
              <img class="cover" width="300" height="auto" src="<%= it.seriesItem.cover %>" alt="cover" />
              <img class="play" width="90" height="90"
                src="https://static-v1.mydramawave.com/frontend_static/dramato-web/art/btn.svg" alt="play" />
            </x-cover>
            <x-summary style="display: flex; flex-direction: column; row-gap: 12px; text-align: center;">
              <h1><%= it.seriesItem.name %></h1>
              <ul class="">
              <% it.seriesItem.content_tags.forEach((tag, index) => { %>
                <li><%= tag %></li>
              <% }); %>
              </ul>
              <div style="text-align: left;">
                <x-desc class="unfolded"><%= it.seriesItem.desc %></x-desc>
              </div>
              <button>
                <img src="https://static-v1.mydramawave.com/frontend_static/dramato-web/art/click.webp" width="60"
                  height="51" alt="click" />
                <span><%= it.t.continueWatching %></span>
              </button>
            </x-summary>
          </main>
        </x-wrapper>
      </article>

    </div>
    <div style="width: 0; height: 0; margin-left: -10px;">
      <iframe id="iframe" src="" width="0" height="0" name="iframe"></iframe>
    </div>
  </x-index>



<script>
window.land = {}
const search = window.location.search.replace(/^\\?/, '')
const _query = new URLSearchParams(search)
window.land.query = Object.fromEntries(_query.entries())

</script>
<script>
window.land.pixel_id = window.land.query.pixel_id || '1210605023775986'

!(function (f, b, e, v, n, t, s) {
  if (f.fbq) return; n = f.fbq = function () {
    n.callMethod
      ? n.callMethod.apply(n, arguments) : n.queue.push(arguments)
  };
  if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
  n.queue = []; t = b.createElement(e); t.async = !0;
  t.src = v; s = b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t, s)
}(window, document, 'script',
  'https://connect.facebook.net/en_US/fbevents.js'));
fbq('init', window.land.pixel_id);
fbq('track', 'PageView');

</script>
<script>
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
!(function (t, e) { 'object' == typeof exports && 'object' == typeof module ? module.exports = e() : 'function' == typeof define && define.amd ? define([], e) : 'object' == typeof exports ? exports.ClipboardJS = e() : t.ClipboardJS = e() }(this, function () { return n = { 686: function (t, e, n) { 'use strict'; n.d(e, { default: function () { return b } }); var e = n(279), i = n.n(e), e = n(370), u = n.n(e), e = n(817), r = n.n(e); function c(t) { try { return document.execCommand(t) } catch (t) { return } } var a = function (t) { t = r()(t); return c('cut'), t }; function o(t, e) { var n, o, t = (n = t, o = 'rtl' === document.documentElement.getAttribute('dir'), (t = document.createElement('textarea')).style.fontSize = '12pt', t.style.border = '0', t.style.padding = '0', t.style.margin = '0', t.style.position = 'absolute', t.style[o ? 'right' : 'left'] = '-9999px', o = window.pageYOffset || document.documentElement.scrollTop, t.style.top = ''.concat(o, 'px'), t.setAttribute('readonly', ''), t.value = n, t); return e.container.appendChild(t), e = r()(t), c('copy'), t.remove(), e } var f = function (t) { var e = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : { container: document.body }, n = ''; return 'string' == typeof t ? n = o(t, e) : t instanceof HTMLInputElement && !['text', 'search', 'url', 'tel', 'password'].includes(null == t ? void 0 : t.type) ? n = o(t.value, e) : (n = r()(t), c('copy')), n }; function l(t) { return (l = 'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator ? function (t) { return typeof t } : function (t) { return t && 'function' == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? 'symbol' : typeof t })(t) } var s = function () { var t = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {}, e = t.action, n = void 0 === e ? 'copy' : e, o = t.container, e = t.target, t = t.text; if ('copy' !== n && 'cut' !== n) throw new Error('Invalid "action" value, use either "copy" or "cut"'); if (void 0 !== e) { if (!e || 'object' !== l(e) || 1 !== e.nodeType) throw new Error('Invalid "target" value, use a valid Element'); if ('copy' === n && e.hasAttribute('disabled')) throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute'); if ('cut' === n && (e.hasAttribute('readonly') || e.hasAttribute('disabled'))) throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes') } return t ? f(t, { container: o }) : e ? 'cut' === n ? a(e) : f(e, { container: o }) : void 0 }; function p(t) { return (p = 'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator ? function (t) { return typeof t } : function (t) { return t && 'function' == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? 'symbol' : typeof t })(t) } function d(t, e) { for (var n = 0; n < e.length; n++) { var o = e[n]; o.enumerable = o.enumerable || !1, o.configurable = !0, 'value' in o && (o.writable = !0), Object.defineProperty(t, o.key, o) } } function y(t, e) { return (y = Object.setPrototypeOf || function (t, e) { return t.__proto__ = e, t })(t, e) } function h(n) { var o = (function () { if ('undefined' == typeof Reflect || !Reflect.construct) return !1; if (Reflect.construct.sham) return !1; if ('function' == typeof Proxy) return !0; try { return Date.prototype.toString.call(Reflect.construct(Date, [], function () {})), !0 } catch (t) { return !1 } }()); return function () { var t, e = v(n); return t = o ? (t = v(this).constructor, Reflect.construct(e, arguments, t)) : e.apply(this, arguments), e = this, !(t = t) || 'object' !== p(t) && 'function' != typeof t ? (function (t) { if (void 0 !== t) return t; throw new ReferenceError('this hasn\'t been initialised - super() hasn\'t been called') }(e)) : t } } function v(t) { return (v = Object.setPrototypeOf ? Object.getPrototypeOf : function (t) { return t.__proto__ || Object.getPrototypeOf(t) })(t) } function m(t, e) { t = 'data-clipboard-'.concat(t); if (e.hasAttribute(t)) return e.getAttribute(t) } var b = (function () { !(function (t, e) { if ('function' != typeof e && null !== e) throw new TypeError('Super expression must either be null or a function'); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), e && y(t, e) }(r, i())); var t, e, n, o = h(r); function r(t, e) { var n; return (function (t) { if (!(t instanceof r)) throw new TypeError('Cannot call a class as a function') }(this)), (n = o.call(this)).resolveOptions(e), n.listenClick(t), n } return t = r, n = [{ key: 'copy', value: function (t) { var e = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : { container: document.body }; return f(t, e) } }, { key: 'cut', value: function (t) { return a(t) } }, { key: 'isSupported', value: function () { var t = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : ['copy', 'cut'], t = 'string' == typeof t ? [t] : t, e = !!document.queryCommandSupported; return t.forEach(function (t) { e = e && !!document.queryCommandSupported(t) }), e } }], (e = [{ key: 'resolveOptions', value: function () { var t = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {}; this.action = 'function' == typeof t.action ? t.action : this.defaultAction, this.target = 'function' == typeof t.target ? t.target : this.defaultTarget, this.text = 'function' == typeof t.text ? t.text : this.defaultText, this.container = 'object' === p(t.container) ? t.container : document.body } }, { key: 'listenClick', value: function (t) { var e = this; this.listener = u()(t, 'click', function (t) { return e.onClick(t) }) } }, { key: 'onClick', value: function (t) { var e = t.delegateTarget || t.currentTarget, n = this.action(e) || 'copy', t = s({ action: n, container: this.container, target: this.target(e), text: this.text(e) }); this.emit(t ? 'success' : 'error', { action: n, text: t, trigger: e, clearSelection: function () { e && e.focus(), window.getSelection().removeAllRanges() } }) } }, { key: 'defaultAction', value: function (t) { return m('action', t) } }, { key: 'defaultTarget', value: function (t) { t = m('target', t); if (t) return document.querySelector(t) } }, { key: 'defaultText', value: function (t) { return m('text', t) } }, { key: 'destroy', value: function () { this.listener.destroy() } }]) && d(t.prototype, e), n && d(t, n), r }()) }, 828: function (t) { var e; 'undefined' == typeof Element || Element.prototype.matches || ((e = Element.prototype).matches = e.matchesSelector || e.mozMatchesSelector || e.msMatchesSelector || e.oMatchesSelector || e.webkitMatchesSelector), t.exports = function (t, e) { for (;t && 9 !== t.nodeType;) { if ('function' == typeof t.matches && t.matches(e)) return t; t = t.parentNode } } }, 438: function (t, e, n) { var u = n(828); function i(t, e, n, o, r) { var i = (function (e, n, t, o) { return function (t) { t.delegateTarget = u(t.target, n), t.delegateTarget && o.call(e, t) } }.apply(this, arguments)); return t.addEventListener(n, i, r), { destroy: function () { t.removeEventListener(n, i, r) } } }t.exports = function (t, e, n, o, r) { return 'function' == typeof t.addEventListener ? i.apply(null, arguments) : 'function' == typeof n ? i.bind(null, document).apply(null, arguments) : ('string' == typeof t && (t = document.querySelectorAll(t)), Array.prototype.map.call(t, function (t) { return i(t, e, n, o, r) })) } }, 879: function (t, n) { n.node = function (t) { return void 0 !== t && t instanceof HTMLElement && 1 === t.nodeType }, n.nodeList = function (t) { var e = Object.prototype.toString.call(t); return void 0 !== t && ('[object NodeList]' === e || '[object HTMLCollection]' === e) && 'length' in t && (0 === t.length || n.node(t[0])) }, n.string = function (t) { return 'string' == typeof t || t instanceof String }, n.fn = function (t) { return '[object Function]' === Object.prototype.toString.call(t) } }, 370: function (t, e, n) { var f = n(879), l = n(438); t.exports = function (t, e, n) { if (!t && !e && !n) throw new Error('Missing required arguments'); if (!f.string(e)) throw new TypeError('Second argument must be a String'); if (!f.fn(n)) throw new TypeError('Third argument must be a Function'); if (f.node(t)) return c = e, a = n, (u = t).addEventListener(c, a), { destroy: function () { u.removeEventListener(c, a) } }; if (f.nodeList(t)) return o = t, r = e, i = n, Array.prototype.forEach.call(o, function (t) { t.addEventListener(r, i) }), { destroy: function () { Array.prototype.forEach.call(o, function (t) { t.removeEventListener(r, i) }) } }; if (f.string(t)) return t = t, e = e, n = n, l(document.body, t, e, n); throw new TypeError('First argument must be a String, HTMLElement, HTMLCollection, or NodeList'); var o, r, i, u, c, a } }, 817: function (t) { t.exports = function (t) { var e, n = 'SELECT' === t.nodeName ? (t.focus(), t.value) : 'INPUT' === t.nodeName || 'TEXTAREA' === t.nodeName ? ((e = t.hasAttribute('readonly')) || t.setAttribute('readonly', ''), t.select(), t.setSelectionRange(0, t.value.length), e || t.removeAttribute('readonly'), t.value) : (t.hasAttribute('contenteditable') && t.focus(), n = window.getSelection(), (e = document.createRange()).selectNodeContents(t), n.removeAllRanges(), n.addRange(e), n.toString()); return n } }, 279: function (t) { function e() {}e.prototype = { on: function (t, e, n) { var o = this.e || (this.e = {}); return (o[t] || (o[t] = [])).push({ fn: e, ctx: n }), this }, once: function (t, e, n) { var o = this; function r() { o.off(t, r), e.apply(n, arguments) } return r._ = e, this.on(t, r, n) }, emit: function (t) { for (var e = [].slice.call(arguments, 1), n = ((this.e || (this.e = {}))[t] || []).slice(), o = 0, r = n.length; o < r; o++)n[o].fn.apply(n[o].ctx, e); return this }, off: function (t, e) { var n = this.e || (this.e = {}), o = n[t], r = []; if (o && e) for (var i = 0, u = o.length; i < u; i++)o[i].fn !== e && o[i].fn._ !== e && r.push(o[i]); return r.length ? n[t] = r : delete n[t], this } }, t.exports = e, t.exports.TinyEmitter = e } }, r = {}, o.n = function (t) { var e = t && t.__esModule ? function () { return t.default } : function () { return t }; return o.d(e, { a: e }), e }, o.d = function (t, e) { for (var n in e)o.o(e, n) && !o.o(t, n) && Object.defineProperty(t, n, { enumerable: !0, get: e[n] }) }, o.o = function (t, e) { return Object.prototype.hasOwnProperty.call(t, e) }, o(686).default; function o(t) { if (r[t]) return r[t].exports; var e = r[t] = { exports: {} }; return n[t](e, e.exports, o), e.exports } var n, r }));

</script>
<script>
const eventReported = {
  pageView: false,
  viewContent: false,
  startTrial: false,
}
let buffer = []
let reportNowTimer = null
const environment = window.location.hostname.indexOf('test') >= 0 ? 'development' : 'prod'
const backendUrl = 'https://trace.mydramawave.com/client_track'
const backendUrlTest = 'https://trace-test.mydramawave.com/client_track'
const query = window.land.query
addReport({
  event: 'ad_page_show',
  event_info: JSON.stringify(getReportData()),
})
window.addEventListener('load', function () {
  addReport({
    event: 'ad_page_view',
    event_info: JSON.stringify(getReportData()),
  })
  reportNow()
})
document.addEventListener('DOMContentLoaded', () => {
  fetchSeries(query.content_id).then(null, () => {
    addReport({
      event: 'ad_page_load_series_failed',
      event_info: JSON.stringify(getReportData())
    })
    reportNow()
  })
  fetchClientInfo().then(ip => {
    window.land.ip = ip
  })
  const button = document.getElementById('app');
  button.addEventListener('click', function () {
    try {
      if (query.clipboard !== 'no') {
        writeToClipboard()
      }
      addReport({
        event: 'ad_page_click',
        event_info: JSON.stringify(getReportData({ button: 'continue' })),
      })
      reportNow(true)
    } catch (err) { }
    goToApp()
  });
  const minorImages = document.querySelector('#minorImages');
  setTimeout(() => {
    minorImages.style.display = 'grid';
    const images = minorImages.querySelectorAll('img');
    images.forEach(img => {
      img.src = img.dataset.src;
    });
  }, 1000)
  window.onbeforeunload = function () {
    reportNow()
  }
});

/************************************************************
   * Helpers
   ************************************************************/

function writeToClipboard() {
  const qs = createQueryString({
    content_id: query.content_id,
    event_time: Date.now(),
    fbclid: query.fbclid,
    external_id: undefined,
    ip: window.land.ip,
    ua: window.navigator.userAgent,
    media_source: query.media_source,
    pid: 'wave_ads',
    af_sub1: getCookie('_fbp'),
    af_sub2: getCookie('_fbc'),
    af_sub3: window.land.ip,
    af_sub4: window.navigator.userAgent,
    af_sub5: window.land.pixel_id,
    c: query.c,
    af_c_id: query.af_c_id,
    af_ad: query.af_ad,
    af_ad_id: query.af_ad_id,
    af_adset_id: query.af_adset_id,
    af_channel: query.af_channel,
    af_siteid: '1916',
    af_sub_siteid: '0_0_0',
  })
  try {
    ClipboardJS.copy([
      3,
      'https://mydramawave.com?redirect=/detail?id=' + query.content_id,
      qs,
    ].join(' '))
  } catch (e) {
    console.error('Error copying to clipboard:', e)
  }
}

function goToApp() {
  const links = generateLinks()
  window.location.href = links.oneLink
}
// https://api-test.mydramawave.com/dm-api/h5/series/info?series_id=QJHm97fn1X
function fetchSeries(seriesId) {
  const el = document.getElementById('_data_series_info')
  const string = el ? el.innerText : ''
  const data = string ? JSON.parse(string) : null
  if (data) {
    return Promise.resolve(data)
  }
  const baseUrl = environment === 'development' ? 'https://api-test.mydramawave.com' : 'https://api.mydramawave.com'
  const temp_t = Date.now()
  return fetch(baseUrl + '/dm-api/drama/share/series_info?series_id=' + seriesId)
    .then(res => {
      addReport({
        event: 'ad_page_load_series_time',
        event_info: JSON.stringify(getReportData({ load_time: Date.now() - temp_t })),
        load_time: Date.now() - temp_t
      })
      reportNow()
      return res.json()
    })
    .then(res => res.data.series_info)
}
function fetchClientInfo() {
  const el = document.getElementById('_data_ip')
  const string = el ? el.innerText : ''
  const data = string ? JSON.parse(string) : null
  if (data) {
    return Promise.resolve(data)
  }
  return fetch('https://trace.mydramawave.com/yl/c-info', { method: 'POST' }).then(r => r.json())
    .then(j => j.code === 200 ? j.data : Promise.reject())
}

function addReport(data) {
  if (eventReported[data.event]) return
  eventReported[data.event] = true
  buffer.push({
    channel: environment === 'development' ? 'devtest' : 'prod',
    environment,
    href: window.location.href,
    page_title: document.title,
    referrer: document.referrer,
    screen_height: window.screen.height.toString(),
    screen_width: window.screen.width.toString(),
    user_agent: window.navigator.userAgent,
    time: Date.now().toString(),
    client_height: window.innerHeight.toString(),
    client_width: window.innerWidth.toString(),
    os_name: getOSInfo().name,
    os_version: getOSInfo().version,
    user_source: 'web',
    ...data,
  })
}

function reportNow(force) {
  if (reportNowTimer) {
    window.clearTimeout(reportNowTimer);
  }
  const report = () => {
    reportNowTimer = null
    const isDev = environment === 'development'
    if (buffer.length === 0) return
    const dataToSend = [...buffer]
    buffer = []
    const blob = new Blob([JSON.stringify(dataToSend)], { type: 'application/json' })
    navigator.sendBeacon(isDev ? backendUrlTest : backendUrl, blob)
  }
  if (force) {
    report()
  } else {
    reportNowTimer = window.setTimeout(report, 300)
  }
}

function getReportData(more) {
  more = more || {}
  return {
    pixel: window.land.pixel_id,
    content_id: query.content_id,
    fbc: getCookie('_fbc'),
    fbp: getCookie('_fbp'),
    campaign_id: query.af_c_id,
    adset_id: query.af_adset_id,
    ad_id: query.af_ad_id,
    ip: window.land.ip,
    ua: window.navigator.userAgent,
    os_version: getOSInfo().version,
    model: getDeviceType(),
    timestamp: Date.now(),
    event_time: Date.now(),
    af_channel: query.af_channel,
    ...more,
  }
}
/**
   * 通过对象生成查询参数字符串，所有 value 自动转义
   * @param queries 要设置的查询参数对象
   */
function createQueryString(queries) {
  const p = new URLSearchParams()
  Object.entries(queries).forEach(([key, value]) => {
    value && p.set(key, value.toString())
  })
  return p.toString()
}

function generateLinks() {
  // dramawave://dramawave.app?redirect=/datail?id=MhC8rlRsV&timestamp=1678901234567&campaign id=120216175334570464&
  // adset id=120218590781410617&ad id=120217087272680464&fbp=fb.1.1596403881668.1116446470&fbc=fb.1.1554763741205.AbCdEfGhIiKlMnOpOrStUvWxYz&channel=facebook
  const os = getOSInfo()
  // "redirect=/detail?id=7zlsnH39p2&pixel_id=2246766269032711&dpsource=W2A&timestamp=1746605757791&campaign_id={{campaign.id}}&adset_id={{adset.id}}&ad_id={{ad.id}}&fbp=fb.1.1742562023858.660926066320256771&fbc=fb.1.1745307988714.IwY2xjawJ0KKNleHRuA2FlbQIxMAABHlW2iefxUSOoS8B7ejBnfQM4ymWSMltKUxo19OYooyrLp2KsoeOJeUeDmG3t_aem_6m6oesRAjYiL27vggektZQ&channel=Facebook&af_site_id=1916&af_sub_site_id=0_0_0&af_sub5=0"
  const appQs = createQueryString({
    id: query.content_id,
    pixel_id: window.land.pixel_id,
    timestamp: Date.now(),
    campaign_id: query.af_c_id,
    adset_id: query.adset_id,
    ad_id: query.ad_id,
    fbp: getCookie('_fbp'),
    fbc: getCookie('_fbc'),
    af_site_id: '1916',
    af_sub_site_id: '0_0_0',
    dpsource: 'W2A',
    af_sub5: window.land.pixel_id,
  })
  const deepLink = 'dramawave://dramawave.app?redirect=' + encodeURIComponent('/detail?' + appQs)
  const qs = createQueryString({
    is_retargeting: query.is_retargeting || true,
    af_reengagement_window: '23h',
    af_inactivity_window: '7d',
    af_click_lookback: '7d',
    af_force_deeplink: true,
    pid: 'wave_ads',
    c: query.c,
    af_c_id: query.af_c_id,
    af_adset: query.af_adset,
    af_adset_id: query.af_adset_id,
    af_ad: query.af_ad,
    af_ad_id: query.af_ad_id,
    af_channel: query.af_channel,
    af_siteid: '1916',
    af_sub_siteid: '0_0_0',
    af_ip: window.land.ip,
    af_ua: window.navigator.userAgent,
    af_os_version: os.version,
    af_model: getDeviceType(),
    af_sub1: getCookie('_fbp'),
    af_sub2: getCookie('_fbc'),
    af_sub3: window.land.ip,
    af_sub4: window.navigator.userAgent,
    af_sub5: window.land.pixel_id,
    af_dp: deepLink,
    deep_link_value: deepLink,
  })
  const oneLink = 'https://dramawave.onelink.me/gbfM?' + qs
  const universalLink = 'https://dramawave.onelink.me/gbfM?' + qs
  const googlePlayLink = 'market://details?id=com.dramawave.app'
  return { deepLink, universalLink, googlePlayLink, oneLink }
}

function getOSInfo() {
  const ua = navigator.userAgent;
  let osName = 'unknown';
  let osVersion = 'unknown';

  // Windows
  if (ua.indexOf('Windows NT 10.0') !== -1) {
    osName = 'Windows';
    osVersion = '10';
  } else if (ua.indexOf('Windows NT 6.3') !== -1) {
    osName = 'Windows';
    osVersion = '8.1';
  } else if (ua.indexOf('Windows NT 6.2') !== -1) {
    osName = 'Windows';
    osVersion = '8';
  } else if (ua.indexOf('Windows NT 6.1') !== -1) {
    osName = 'Windows';
    osVersion = '7';
  } else if (ua.indexOf('Windows NT 6.0') !== -1) {
    osName = 'Windows';
    osVersion = 'Vista';
  } else if (ua.indexOf('Windows NT 5.1') !== -1 || ua.indexOf('Windows XP') !== -1) {
    osName = 'Windows';
    osVersion = 'XP';
  }
  // Android
  else if (ua.indexOf('Android') !== -1) {
    osName = 'Android';
    const match = ua.match(/Android (\\d+(\\.\\d+)*)/);
    if (match && match[1]) {
      osVersion = match[1];
    }
  }
  // iOS
  else if (ua.indexOf('iPhone OS') !== -1 || ua.indexOf('iPad; CPU OS') !== -1) {
    osName = 'iOS';
    const match = ua.match(/(iPhone OS|CPU OS) (\\d+(_\\d+)*)/);
    if (match && match[2]) {
      osVersion = match[2].replace(/_/g, '.');
    }
  }
  // Linux
  else if (ua.indexOf('Linux') !== -1) {
    osName = 'Linux';
    // Linux 版本号较难统一获取
  }
  // Mac OS
  else if (ua.indexOf('Mac OS X') !== -1) {
    osName = 'Mac OS X';
    // 尝试提取版本号，例如 "Mac OS X 10_15_7"
    const match = ua.match(/Mac OS X (\\d+(_\\d+)*)/);
    if (match && match[1]) {
      osVersion = match[1].replace(/_/g, '.');
    }
  }

  return { name: osName, version: osVersion };
}
function getDeviceType() {
  const ua = navigator.userAgent;
  if (/iphone/i.test(ua)) {
    return 'iphone';
  } else if (/ipad/i.test(ua)) {
    return 'ipad';
  } else if (/ipod/i.test(ua)) {
    return 'ipod';
  } else if (/android/i.test(ua)) {
    // 对于安卓，可以尝试提取一些信息，但通常不是具体的市面型号
    // 例如，ua 可能包含 "SM-A525F Build/RP1A.200720.012"
    // 你可以尝试从中解析，但很复杂且不可靠
    return 'android';
  } else if (/windows phone/i.test(ua)) {
    return 'windowsphone';
  } else if (/macintosh|mac os x/i.test(ua) && navigator.maxTouchPoints > 0) {
    // 区分 macOS 和 iPadOS (iPad Pro 新版 ua 可能更像 macOS)
    // 如果是触屏 Mac，可能需要更复杂的逻辑。一般桌面 Mac maxTouchPoints 是 0。
    // 如果 iPadOS 的 userAgent 设置为 "Desktop site" 模式，它会伪装成 macOS。
    // 因此，检测 iPad 时需要更小心。
    return 'mac'; // 或保持为 Mac，取决于你的需求
  }
  return 'unknown';
}
function getCookie(name) {
  const value = '; ' + document.cookie;
  const parts = value.split('; ' + name + '=');
  if (parts.length === 2) return parts.pop().split(';').shift();
}

</script>


</body>
</html>
