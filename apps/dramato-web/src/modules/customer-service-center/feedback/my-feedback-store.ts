import { ref } from 'vue'
import { defineStore } from 'pinia'
import { MyFeedbackApi } from './feedback-api'

export const useMyFeedbackStore = defineStore('MyFeedbackPageStore', () => {
  /** 首页- tab-Feed */
  const feedback = ref<M.MyFeedback.List.Feedback[]>([])
  const feedbackNext = ref<string>('offset=0&page_size=20')
  const hasMoreFeedback = ref<boolean>(true)
  const loadingFeedback = ref<boolean>(false)

  /** 获取首页- tab-Feed */
  const getFeedback = async (_next?: string) => {
    if (!hasMoreFeedback.value) return
    if (loadingFeedback.value) return
    loadingFeedback.value = true
    console.log('_next', _next)

    const res = await MyFeedbackApi.getFeedbackList(_next || feedbackNext.value || 'offset=0&page_size=20')
    const list = (res.data?.list || [])
    feedback.value.push(...list)
    if (list.length === 0) {
      hasMoreFeedback.value = false
      loadingFeedback.value = false
      return
    }
    feedbackNext.value = res.data?.page_info?.next || ''
    hasMoreFeedback.value = res.data?.page_info.has_more || false
    loadingFeedback.value = false
  }

  return {
    feedback,
    getFeedback,
    loadingFeedback,
    hasMoreFeedback,
    feedbackNext,
  }
})
