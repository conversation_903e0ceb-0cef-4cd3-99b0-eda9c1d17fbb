/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import { trim } from 'lodash-es'
import { strToArr, idStrToList } from './util'
import { apiGetResource } from './resource-api'
import { langValue } from './constant'
const loading = ref(false)
const list = ref<M.IResourceColumn[]>([])
const form = ref({
  title: '',
  series_resource_id: '',
  series_key_list: '',
})
const pageInfo = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
})
const allMeta = ref<string[]>()
const visibleCol = ref<number[]>([3, 4])
const colsOptions = ref<{
  value: number
  label: string
  children: string[]
}[]>([
  {
    value: 1,
    label: '商务信息区',
    children: ['合作方名称', '短剧类型', '授权开始时间', '授权时长', '免费开始时间', '授权平台', '授权语言', '授权区域', '分成模式', '授权价格', '投放金额', '收入流水', '预估分成', '商务负责人'],
  },
  {
    value: 2,
    label: '资源信息区',
    children: ['完整集数', '解锁集数', '描述', '网盘链接', '验收状态', '内容评级', '内容分类', '验收时间', '验收负责人', '标签', '连载状态', '发行轮次', '资源类型', '免费上线时间']
  },
  {
    value: 3,
    label: '资源操作区',
    // '纯净音频', '原始音频', '背景音频', '特效音效'
    children: ['原始剧集列表', '纯净剧集列表'],
  },
  {
    value: 4,
    label: '多语言操作区',
    children: langValue.reduce(
      (accumulator: string[], currentValue) => {
        const obj = [`${currentValue}剧集列表`, `${currentValue}字幕列表`]
        return accumulator.concat(obj)
      },
      [],
    )
  },
])

const onQuery = async () => {
  pageInfo.page = 1
  await getList()
}

const onReset = async () => {
  form.value.series_resource_id = ''
  form.value.title = ''
  await onQuery()
}

const onPageChange = async () => {
  await getList()
}
const onPageSizeChange = async (n: number) => {
  pageInfo.pageSize = n
  await onQuery()
}

const getFilterMeta = () => {
  const dynamicCols = colsOptions.value.reduce(
    (accumulator: string[], currentValue) => {
      if (visibleCol.value.includes(currentValue.value)) {
        currentValue.children.forEach(row => allMeta.value?.includes(row) && accumulator.push(row))
        return accumulator
      } else {
        return accumulator
      }
    },
    [],
  )
  return ['资源名称', '资源ID'].concat(dynamicCols).concat(['状态', '创建时间', '更新时间'])
}

const getList = async (hasLoading = true) => {
  const params = {
    title: trim(form.value.title),
    series_resource_id_list: idStrToList(form.value.series_resource_id),
    series_key_list: strToArr(form.value.series_key_list),
    page_index: pageInfo.page,
    page_size: pageInfo.pageSize,
  }
  if (hasLoading) {
    loading.value = true
    list.value = []
  }
  try {
    const { data } = await apiGetResource(params)
    const _list: M.IResourceColumn[] = []
    allMeta.value = data?.meta || []
    data?.list.map(value => {
      const row: Record<string, string | number> = {}
      let a = -1
      value.map((item, index) => {
        a++
        const key = `column_${a}`
        row[key] = value[index]
      })
      _list.push(row)
    })

    list.value = _list
    pageInfo.total = data?.total || 0
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

export const useResourceListStore = () => {
  return {
    getList,
    loading,
    list,
    form,
    pageInfo,
    onReset,
    onQuery,
    onPageChange,
    onPageSizeChange,
    visibleCol,
    colsOptions,
    getFilterMeta,
    allMeta
  }
}
