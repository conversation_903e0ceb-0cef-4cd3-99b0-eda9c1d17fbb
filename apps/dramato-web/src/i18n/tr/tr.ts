export const tr = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: '<PERSON><PERSON><PERSON> Seçimler',
    },
  },
  myList: {
    watchHistory: `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>`,
    myList: 'Listem',
    seeAll: '<PERSON>ü<PERSON><PERSON><PERSON><PERSON>',
    noData: '<PERSON>l<PERSON>li sonuç yok',
  },
  // 分享页
  share: {
    watchNow: 'Şimdi İzle',
    episodeDialog: {
      title: 'Bana katıl ve ekstra jeton kazan!',
      description: '<PERSON>u diziye bayıldım!',
      tips: 'Tavsiye',
      button: '<PERSON>\'ı İzle',
      limit24: 'Sınırlı süre: 24h',
    },
    toast: {
      takeOffShelf: 'İçerik Mevcut Değil',
    },
  },
  'customer-service-center': {
    title: 'Müşteri Hizmetleri Merkezi',
    description: 'Açıklama',
    'description-placeholder': 'Sorunu anlamamıza yardımcı olacak ayrıntıları sağlayın',
    'email-placeholder': '<PERSON><PERSON> bildirim güncellemeleri almak için e-postanızı girin',
    'submit-feedback': 'Gönder',
    'enter-feedback': '<PERSON><PERSON><PERSON> sorunlar geri bildirimi',
    'upload-pictures': 'Resimleri yükle',
    feedback: 'Geri bildirim',
    'problem-type': 'Problem Türü',
    'problem-type-placeholder': 'Lütfen Seçin',
    'payment-issue-placeholder': `Lütfen kimliğinizi ve sipariş belgenizi ibraz edin: Google: https://pay.google.com/ (sipariş numarası GPA… ile başlar), iOS: App Store – Apple Kimliği – Satın Alma Geçmişi (sipariş kimliği)`,
    'content-issue-placeholder': 'Lütfen dizi adını, bölüm numaralarını ve eksik altyazı dillerini belirtin',
    'ads-issue-placeholder': 'Lütfen cihaz modelinizi, ülkenizi ve dil ayarlarınızı belirtin.',
    'technical-issue-placeholder': 'Lütfen cihaz modelinizi, ülkenizi ve dil ayarlarınızı belirtin.',
    'other-issue-placeholder': 'Lütfen daha fazla bilgi verin, cihaz modeliniz ve ülkeniz de dahil olmak üzere',
    'i-want-to-give-feedback': 'Geri bildirim vermek istiyorum',
    'are-you-satisfied-with-this-reply': 'Bu yanıtla memnun kaldınız mı?',
    unsatisfied: 'memnun değilim',
    satisfied: 'memnunum',
    myFeedback: 'Geri bildirim',
    'reply-to-feedback': 'Geri bildirime cevap ver',
    'description-min-length': 'Lütfen sorunu en az 6 karakterle açıklayın.',
    'problem-type-not-choose': 'Lütfen bir geri bildirim türü seçin.',
    'description-empty': 'Bu alan boş bırakılamaz.',
  },
  terminate: {
    question: 'Aboneliğimi nasıl yönetirim?',
    desc: 'Anlaşma Yönetimi sayfasına gitmek için alttaki butona tıklayarak aboneliğinizi yönetebilirsiniz:',
    title: 'Anlaşma Yönetimi',
    expiration: 'Son Geçerlilik Tarihi',
    manageDesc: 'Sözleşme İlişkisi',
  },
  toast: {
    'upload-failed': 'Yükleme başarısız, lütfen tekrar yükleyin',
    'submit-success': 'Başarıyla gönderildi',
    'email-incorrect': 'E-posta biçimi yanlış',
  },
  not_found: {
    title: 'Aradığınız sayfa bulunamadı',
  },
  regulations: {
    howUseData: 'Verilerinizi Nasıl Kullanıyoruz',
    content: `Uygulama İzleme Şeffaflığı izin isteğinde "İzin Ver" seçeneğini belirlerseniz, bilgilerinizi ve verilerinizi hizmetlerimizi optimize etmek ve size en iyi deneyimi sunmak için kullanacağız, aksi takdirde sadece verileri toplayacağız ve herhangi bir izleme faaliyeti yürütmeyeceğiz.

DramaWave ve ortaklarımızın ve tedarikçilerimizin cihazınızdaki verileri kullanarak uygulamayı nasıl kullandığınızı analiz etmesine, içerik önerilerini kişiselleştirmesine ve daha fazla bilgiyle ilgili hizmetler sunmasına izin vermeyi kabul ediyorsanız, devam etmek için lütfen "Kabul Et ve Devam Et"e tıklayın.`,
    acceptAll: 'Hepsini Kabul Et',
    dataSettings: 'Veri Ayarları',
    next: 'Sonraki',
    cancel: 'İptal',
    aboutYourPrivacy: 'Gizliliğiniz Hakkında',
    personalDataName: 'Kişiselleştirilmiş Veriler',
    personalDataDescription: 'Bu bilgileri, seçimlerinizi hatırlamak ve kişiselleştirilmiş reklamlar ve içerik önerileri, bildirimler ve hatırlatmalar sağlamak için kullanıyoruz.',
    advertisementDataName: 'Reklam Verileri',
    advertisementDataDescription: 'Çevrimiçi reklam amaçları için cihazınızda çerezler, cihazınızın tanımlayıcıları veya diğer bilgiler saklanabilir veya erişilebilir. Kullanıcı verileri ayrıca reklam ortaklarımızla paylaşılabilir.',
    behaviorsDataName: 'Davranış Verileri',
    behaviorsDataDescription: 'Kullanım, davranış ve reklam performansını ölçmek ve analiz etmek için çerezler veya cihaz tanımlayıcıları gibi analizle ilgili veriler saklanabilir.',
    privacyAndTerms: 'Gizlilik Politikası ve Hizmet Şartları',
    referToPrivacy: 'Lütfen Gizlilik Politikasına bakın',
    referToTerms: 'Lütfen Hizmet Şartlarına bakın',
    agreeAndSave: 'Kabul Et ve Kaydet',
  },
  coins: {
    earnings: {
      title: 'Kazançlarım',
      total: 'Toplam Elmas',
      exchange: 'Değiştir',
      earningsHistory: 'Kazanç Geçmişi',
      exchangeHistory: 'Değişim Geçmişi',
      noRecords: 'Kayıt yok',
      last50earnings: 'Sadece son 50 kazanç gösterilmektedir',
      last50exchanges: 'Sadece son 50 değişim kaydı gösterilmektedir',
      benefits: 'Faydalar',
      gotIt: 'Tamam',
    },
    exchange: {
      title: 'Değişim Alanı',
      notOpen: 'Henüz açılmadı, lütfen beklemede kalın',
      receiveBtn: 'Mutlu Bir Şekilde Alın',
      earnCoins: 'Elmas Kazan',
      redeemRequiredXCoins: 'Geri ödeme için {X} elmas gerekli',
      exchangeSuccess: 'Başarıyla Değiştirildi',
      cardId: 'KART ID',
      exchangeFailed: 'Değişim Başarısız',
      transferFailedInsufficientStock: 'Stokta yok. Lütfen yarın erken gelin.',
      gotItBtn: 'Tamam',
      transferFailedRiskControl: 'Hesapta anormallik var, geçici olarak değişim yapılamıyor.',
      tryAgainBtn: 'Sonra Tekrar Deneyin',
      transferConfirm: 'Transfer Onayı',
      paypalAccountEmail: 'Paypal Hesap e-posta adresi',
      confirmPaypalAccountEmail: 'Paypal Hesap e-posta adresini onaylayın',
      transferX: '{X} Transferi',
      transferXConfirm: '{X} Transferi Onayı',
      transferToYourAccountX: 'Hesabınıza {X} kadar transfer',
      confirmBtn: 'Onayla',
      redeemFailed: 'Kullanım başarısız oldu',
      redeemFailedContent: 'Bir hata oluştu. Lütfen daha sonra tekrar deneyin.',
      transferSuccessTitle: 'Parayı gönderiyoruz!',
      transferSuccessContent: 'Paran 5 dakika ila 72 saat içinde ulaşacaktır.',
      copySuccess: 'Başarı Kopyalandı',
      enterEmailError: 'Lütfen geçerli bir e-posta adresi girin',
      enterEmailValidError: 'Giriş hatası, lütfen yeniden girin',
      emailNotMatchError: 'E-posta adresi eşleşmiyor, lütfen yeniden girin',
    },
  },
  meal_check_in: {
    sleeping_link: 'Elmas Kazanırken Uyuyun',
    rules_title: 'Aktivite Kuralları',
    benefits_title: 'Benefits',
    rules_1: '1. Her gün, yemeklerinizi kaydetmek için dört zaman dilimi vardır: Kahvaltı: 07:00-10:00; Öğle Yemeği: 11:00-14:00; Akşam Yemeği: 17:00-20:00; Gece Atıştırması: 21:00-24:00. ',
    rules_2: '2. İlgili kaydetme zamanını kaçırırsanız, ödülü almak için bir video izleyebilirsiniz. ',
    rules_3: '3. Ödüller her gün gece yarısı sıfırlanır, bu yüzden ödüllerinizi unutmadan alın.',
  },
  sleep_check_in: {
    earn_link: 'Yemekleri Kaydederek Kazanın',
    benefits_title: 'Benefits',
    rules_1: '1. Her akşam 19:00 ile 24:00 arasında \'Uyuyarak Kazan\' modunu etkinleştirebilirsiniz.',
    rules_2: '2. Modu etkinleştirdikten sonra, ertesi gün, 8 saat uyuduktan sonra, ödülünüzü sabah 8:00 ile 12:00 arasında alabilirsiniz.',
    rules_3: '3. Ödül alma zamanını kaçırırsanız, ertesi gün öğle 12:00 ile akşam 7:00 arasında bir video izleyerek ödülünüzü yeniden alabilirsiniz.',
  },
  bind_email: {
    title: 'E-posta adresinizi girin',
    list_1: 'E-posta adresini bağla, özel ödül paraları kazan.',
    list_2: 'Yaklaşan etkinlikler ve fırsatlar hakkında bildirim al.',
    list_3: 'Yeni dizi bölümlerini öğrenen ilk kişi ol.',
    email_placeholder: 'Lütfen e-posta adresini girin.',
    verify: 'Doğrula',
    email_error: 'Lütfen geçerli bir e-posta adresi girin',
    verify_progress: 'E-posta doğrulanıyor',
    verify_success: 'Başarıyla alındı!',
    re_enter: 'Tekrar girin',
    email_repeat: 'Bu e-posta adresi zaten etkinlikte!',
  },
  infinityScroll: {
    end: 'Son',
    loading: 'Yükleniyor...',
  },
}
