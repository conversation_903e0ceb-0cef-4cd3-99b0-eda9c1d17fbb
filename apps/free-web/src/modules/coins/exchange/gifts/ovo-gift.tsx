import { bindLoading, createComponent, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { coins, dialogBg, ovoDialog, ovoCard } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { computed, ref, watch, watchEffect } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { openDialog } from 'src/modules/common/dialog'
import { useCoinsStore } from '../../coins-store'
import { apiRedeemGift } from '../../coins-api'
type GiftOptions = {
  props: {
    gift?: GiftType
  }
}
interface TaskEventMap {
  [key: number]: string
}
export const OvoGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      tags: ['Newcomers'], // 礼品标签
      type: 'ovo',
    },
  },
}, props => {
  const { t } = useLocale()
  const tipsRef = ref<HTMLDivElement>()
  const confirmPhone = ref('')
  const phoneError = ref('')
  const confirmPhoneError = ref('')
  const redeemLoading = ref(false)
  let closeConfirmAccountDialog: Fn
  let closeConfirmDialog: Fn

  const { getExchangeDetails, phone } = useCoinsStore()

  const reachLimit = computed(() => props.gift?.user_coins >= props.gift?.gift_coins)

  watchEffect(() => {
    if (!reachLimit.value) {
      track('exchangezone', 'earncoins_button', 'show', {
        gift_id: props.gift?.id,
      })
    }
  })

  const commonTitle = () => (
    <x-title class="block w-full h-16 relative">
      <img src={ovoDialog} class="absolute w-auto h-full object-cover left-1/2 -translate-x-1/2 top-1/2" />
    </x-title>
  )

  const openTransferFailByInsufficientStock = () => { // 库存不足导致失败
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6 bg-cover rounded-xl">
          <x-body-main class="flex mx-4 flex-col">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center truncate">{t('coins.exchange.exchangeFailed')}</x-title>
            <x-content class="w-full text-[var(--text-2)] text-center mt-3 mb-5">
              {t('coins.exchange.transferFailedInsufficientStock')}
            </x-content>
            <Button class="w-full  h-11  text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={() => closeDialog()}>{t('coins.exchange.gotItBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }
  const openTransferFailByRiskControl = () => { // 触发风控导致兑换失败
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6 bg-cover rounded-xl">
          <x-body-main class="flex mx-4 flex-col">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center truncate">{t('coins.exchange.exchangeFailed')}</x-title>
            <x-content class="w-full text-[var(--text-2)] text-center mt-3 mb-5">
              {t('coins.exchange.transferFailedRiskControl')}
            </x-content>
            <Button class="w-full h-11 text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={() => closeDialog()}>{t('coins.exchange.tryAgainBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }
  const openTransferSuccessDialog = () => { // 提现成功
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6 bg-cover rounded-xl">
          <x-body-main class="flex mx-4 flex-col">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center">{t('coins.exchange.transferSuccessTitle')}</x-title>
            <x-content class="w-full text-[var(--text-2)] font-medium text-sm text-center mt-3 mb-5">
              {t('coins.exchange.transferSuccessContentX', { X: phone.value })}
            </x-content>
            <x-btn class="w-full px-3 py-2.5 text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={() => closeDialog()}>
              <p class="w-full line-clamp-3 break-all text-center" onClick={() => {
                if (props.gift?.cash_success_btn_link) {
                  window.location.href = props.gift?.cash_success_btn_link
                } else {
                  getExchangeDetails()
                  closeDialog()
                }
              }}
              >{props.gift?.cash_success_btn_txt ? props.gift?.cash_success_btn_txt : t('coins.exchange.gotItBtn')}
              </p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openConfirmDialog = () => { // 确认兑换弹窗
    closeConfirmAccountDialog?.()
    closeConfirmDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      mainClass: 'rounded-sm',
      title: commonTitle,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6 rounded-xl bg-cover">
          <x-body-main class="flex mx-4 flex-col">
            <x-title class="w-full text-lg font-bold text-[var(--text-1)] text-center line-clamp-2">{t('coins.exchange.transferXConfirm', { X: props.gift?.amount })}</x-title>
            <x-content class="w-full text-[var(--text-2)] text-sm text-center mt-2 mb-6">
              {t('coins.exchange.transferToYourAccountX', { X: phone.value })}
            </x-content>
            <Button class="w-full  h-11  text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={redeemHandler}>{t('coins.exchange.confirmBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const redeemHandler = () => {
    if (redeemLoading.value) return
    void bindLoading(apiRedeemGift({
      id: props.gift?.id,
      account: phone.value,
      type: props.gift?.type,
    }), redeemLoading).then(res => {
      if (res.code === 200) {
        closeConfirmDialog?.()
        openTransferSuccessDialog()
      }
    }).catch((e: { response?: { data?: { code?: number } } }) => {
      if (e?.response?.data?.code === 60011) {
        closeConfirmDialog?.()
        openTransferFailByInsufficientStock()
      } else if (e?.response?.data?.code === 60012) {
        closeConfirmDialog?.()
        openTransferFailByRiskControl()
      }
    })
  }

  const handleClick = () => {
    if (props.gift?.can_exchange) {
      if (props.gift?.button_txt && props.gift?.button_link) {
        window.location.href = props.gift.button_link
        return
      }
      if (reachLimit.value) {
        if (phone.value && phone.value !== '') {
          openConfirmDialog()
        } else {
          openAccountConfirmDialog()
        }
      } else {
        track('exchangezone', 'earncoins_button', 'click')
        window.location.href = 'freereels://freereels.app/rewards_task?enter_from=exchange_page'
      }
    }
    trackHandle('click')
  }

  const flag = ref(false)
  watch(() => [tipsRef.value, flag.value], () => {
    flag.value = false
    if (tipsRef.value) {
      const lineHeight = parseFloat(getComputedStyle(tipsRef.value).lineHeight)
      const fontSize = parseFloat(getComputedStyle(tipsRef.value).fontSize)
      if (fontSize <= 8) {
        return
      }
      const maxHeight = lineHeight * 2
      if (tipsRef.value.scrollHeight > maxHeight) {
        tipsRef.value.style.fontSize = `${fontSize - 2 <= 8 ? 8 : fontSize - 2}px`
        flag.value = true
      }
    }
    trackHandle('show')
  })
  const trackHandle = (event: 'click' | 'load' | 'show' | 'successful') => {
    if (props?.gift?.task_id) {
      const taskEventMap: TaskEventMap = {
        10: 'watchreels',
        11: 'checkin',
      }
      const eventName = taskEventMap[props.gift.task_id]

      if (eventName) {
        track(eventName, 'redeemlevel', event, {
          gift_id: props.gift.id,
        })
      }
    }
  }

  const openAccountConfirmDialog = () => {
    const handleAccountBlur = () => {
      if (!phone.value) {
        phoneError.value = 'Nomor telepon tidak boleh kosong'
      } else {
        if (phone.value.length < 9 || phone.value.length > 14 || !/^\d+$/.test(phone.value)) {
          phoneError.value = 'Nomor ponsel harus terdiri dari 9 hingga 14 digit angka'
        } else {
          phoneError.value = ''
        }
      }
    }
    const handleConfirmAccountBlur = () => {
      if (confirmPhone.value !== phone.value) {
        confirmPhoneError.value = 'Nomor telepon yang dimasukkan tidak sama, silakan coba lagi.'
      } else {
        confirmPhoneError.value = ''
      }
    }

    closeConfirmAccountDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      body: () => (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})` }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6 bg-cover rounded-xl">
          <x-body-main class="flex mx-4 flex-col gap-y-4">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center truncate">{t('coins.exchange.transferConfirm')}</x-title>
            <x-form>
              <x-form-item class="block text-sm">
                <x-form-label class="font-bold">Nomor Telepon OVO</x-form-label>
                <input
                  type="number"
                  value={phone.value}
                  onBlur={handleAccountBlur}
                  onInput={(e: Event) => {
                    phone.value = (e.target as HTMLInputElement).value
                    confirmPhoneError.value = ''
                    handleAccountBlur()
                  }}
                  placeholder="'08' di awal nomor telepon OVO"
                  class="dramato-input py-3 pl-0 mt-2 bg-transparent"
                />
                <x-line class="block border-0 w-full h-[1px] border-[var(--line-2)] border-solid border-t border-top-1"> </x-line>
                {phoneError.value && <x-error class="w-full line-clamp-2 text-xs pt-1 text-[var(--red-6)]">{phoneError.value}</x-error>}
              </x-form-item>
              <x-form-item class="block mt-4 text-sm">
                <x-form-label class="font-bold">Konfirmasi Nomor Telepon OVO </x-form-label>
                <input
                  type="number"
                  value={confirmPhone.value}
                  onBlur={handleConfirmAccountBlur}
                  onInput={(e: Event) => {
                    confirmPhone.value = (e.target as HTMLInputElement).value
                    confirmPhoneError.value = ''
                  }}
                  placeholder="Konfirmasi Nomor Telepon OVO"
                  class="dramato-input py-3 pl-0 mt-2 bg-transparent"
                />
                <x-line class="block border-0 w-full h-[1px] border-[var(--line-2)] border-solid border-t border-top-1"> </x-line>
                {confirmPhoneError.value && <x-error class="w-full line-clamp-2 text-xs pt-[4px] text-[var(--red-6)]">{confirmPhoneError.value}</x-error>}
              </x-form-item>
            </x-form>
            <Button disabled={
              !phone.value
              || !confirmPhone.value
              || !!phoneError.value
              || !!confirmPhoneError.value
            } class="w-full  h-11 mt-4  text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={() => {
              handleAccountBlur()
              handleConfirmAccountBlur()
              if (phoneError.value || confirmPhoneError.value) return
              openConfirmDialog()
            }}
            >
              {t('coins.exchange.transferX', { X: props.gift?.amount })}
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  return () => (
    <x-gift-item class={mc('flex gap-x-3 p-3', props.gift?.can_exchange ? '' : 'opacity-20')}>
      <x-gift-item-left class="flex flex-col gap-1 items-center justify-center">
        <x-card class="px-1 py-3 size-11 box-border border-solid border-[1px] border-[var(--line-1)] rounded-lg">
          <img src={props.gift?.image_url ?? ovoCard} class="w-full h-auto object-cover" />
        </x-card>
        <x-price class="block text-center text-xs font-bold text-[var(--text-2)]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class="flex-1 flex flex-col gap-1 truncate">
        <x-gift-title class="w-full text-sm text-[var(--text-2)] truncate">{props.gift.name}</x-gift-title>
        <x-tags>
          {props.gift.tags?.map(tag => (
            <x-tag-item class="px-2 mr-1 py-1 bg-[#fff7ea] text-[10px] text-[#fe7123] rounded border border-solid border-[#ffd9c4] border-fill-1 justify-center items-center gap-5 inline-flex">{tag}</x-tag-item>
          ))}
        </x-tags>
        {
          props.gift?.can_exchange
          && (
            <x-gift-coins class="text-sm flex items-center">
              <img src={coins} class="w-4 h-4 object-cover mr-[2px]" />
              <x-gift-amount class="text-[var(--brand-6)]">{props.gift.user_coins}</x-gift-amount>
              <x-separator class="px-[2px] text-[var(--text-4)]">/</x-separator>
              <x-gift-remains class="text-[var(--text-1)]">{props.gift.gift_coins}</x-gift-remains>
            </x-gift-coins>
          )
        }
        {
          props.gift?.can_exchange
          && (
            <x-gift-progress>
              <Progress class="mt-1" value={props.gift.ratio} max={1} />
            </x-gift-progress>
          )
        }
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21', props.gift?.button_tips ? 'justify-start gap-y-1' : 'justify-center')}>
        <Button class="min-h-8 border-none px-1 py-[2px] bg-[var(--brand-6)] text-[var(--text-5)] text-sm w-full rounded-lg" onClick={handleClick}>
          <p class="w-full h-auto line-clamp-2 break-words text-wrap text-center leading-4">
            {props.gift?.button_txt && props.gift?.button_txt !== '' ? props.gift?.button_txt : (reachLimit.value ? t('coins.earnings.exchange') : t('coins.exchange.earnCoins'))}
          </p>
        </Button>
        {props.gift?.button_tips && <x-tips class="w-full h-6 text-[var(--text-2)] text-center text-[10px] leading-3 line-clamp-2 break-words" ref={tipsRef}>{props.gift?.button_tips}</x-tips>}
      </x-gift-item-right>
    </x-gift-item>
  )
})
