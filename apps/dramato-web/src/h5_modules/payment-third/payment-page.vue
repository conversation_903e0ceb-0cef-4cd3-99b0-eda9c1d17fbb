<template>
  <div class="bg-[url('src/h5_modules/payment-third/images/third-bg.webp')] bg-no-repeat overflow-x-hidden"
    style="background-size: 93% auto;background-position: 0 0;">
    <div class="min-h-screen bg-[rgba(11, 8, 11, 1)] relative pb-24">
      <NavBar :title="'Dramawave'" placeholder fixed class="text-text-1 font-medium"
        :class="scrollIsTop ? 'page-is-top' : 'page-not-top'" :border="false" @click-left="goApp()">
        <template #left>
          <SvgIcon name="ic-chevron-left" class="size-6 text-[#fff] " />
        </template>
      </NavBar>
      <div class="mt-6 px-3 relative">
        <div class="flex items-center">
          <div
            class="flex items-center w-12 h-12 border-[0.5px] border-solid border-[#FFFFFF4D] rounded-full overflow-hidden mr-2">
            <Image :src="user.icon || PaymentLogo" class="w-full h-full" />
          </div>
          <div class="flex items-start flex-col">
            <span class="text-base text-[#CCCACB]">ID {{ user.user_id }}</span>
            <span
              class="rounded-[200px] bg-[#434546] py-0.5 pl-1.5 pr-2 text-xs font-normal text-[#FDFBFC] flex items-center"><span
                class="bg-[url('src/h5_modules/payment-third/images/safety.webp')] bg-no-repeat bg-center inline-block bg-contain w-4 h-4 mr-1"></span>{{
                  T.paymentPage.Safety.ref }}</span>
          </div>
        </div>
        <img src="src/h5_modules/payment-third/images/third-helmet.webp"
          class="w-[10.125rem] h-[10.125rem] absolute -top-[3.08rem] right-0" />
      </div>
      <div
        class="mt-6 pt-5 relative rounded-t-xl bg-[#0B080B33] border-[0.5px] border-b-0 border-solid border-[#FFFFFF4D] flex justify-between"
        style="backdrop-filter: blur(7.5px);width: 100%;box-sizing: content-box;transform: translateX(-0.5px);">
        <div class="px-3 pb-4">{{ T.paymentPage.checkout.ref }}</div>
        <div class="text-xs text-[#CCCACB] px-3 flex items-top" @click="goApp(1)">
          <span>{{ T.paymentPage.ChangePlan.ref }}</span>
          <SvgIcon name="ic-chevron-left" class="size-4 rotate-180 " />
        </div>
      </div>
      <div class="px-3">
        <div style="background: linear-gradient(90deg, #352616 0%, #080808 100%);"
          v-if="currentProductData && currentProductData.find_product_type === 'membership'"
          class="rounded-lg px-3 py-3 flex items-center justify-between custom-border">
          <div>
            <div class="flex items-center">
              <img :src="Crown" class="w-5 h-5 mr-1" />
              <div class="text-base font-bold text-[#FFC76E]">{{ currentProductData.title }}</div>
            </div>
            <div class="text-xs text-[#CCCACB] mb-1">
              {{ currentProductData.description || ' ' }}
            </div>
            <div class="text-[0.625rem] mb-1 text-[#CCCACB] opacity-50">
              {{ currentProductData.discount_desc || ' ' }}
            </div>
            <div class="text-[0.625rem] mb-1 text-[#fff] opacity-50">
              {{ currentProductData.tips || ' ' }}
            </div>
          </div>
          <div>
            <div class="flex items-end flex-col">
              <div class="text-lg font-bold text-[#FFC76E]" v-if="currentProductData">
                {{ currentProductData.currency_symbol }}{{
                  (currentProductData.discount_price / 100).toFixed(2) }}</div>
              <div class="text-xs text-[#CCCACB] text-decoration-line: line-through" v-if="currentProductData">
                {{ currentProductData.currency_symbol }}{{ (currentProductData.price / 100).toFixed(2) }}
              </div>
            </div>
          </div>


        </div>

        <div v-else-if="currentProductData && currentProductData.find_product_type === 'recharge'"
          class="rounded-lg px-3 py-3 flex items-center justify-between custom-border bg-[#1D1D1E]">
          <div>
            <div class="flex items-center">
              <SvgIcon name="p-coins-fill" class="mr-[2px] size-5 shrink-0" />
              <div class="text-lg font-bold ">{{ currentProductData?.delivery_details?.quanity }}</div>
            </div>
            <div class="text-sm text-[#CCCACB] mt-1">
              +{{ currentProductData?.delivery_details?.bonus }} Bonus
            </div>
          </div>
          <div>
            <div class="flex items-end flex-col">
              <div class="text-lg font-bold mb-1 text-[#fff]" v-if="currentProductData">
                {{ currentProductData.currency_symbol }}{{ (currentProductData.discount_price / 100).toFixed(2) }}</div>
              <div class="text-xs text-[#797B7D] text-decoration-line: line-through" v-if="currentProductData">
                {{ currentProductData.currency_symbol }}{{ (currentProductData.price / 100).toFixed(2) }}
              </div>
            </div>
          </div>


        </div>
      </div>
      <div class="px-3 mt-7 text-base font-medium text-[#FDFBFC]">
        {{ T.paymentPage.payMethod.ref }}
      </div>
      <!-- 充值选项 -->
      <div class="px-3 mt-4">
        <template v-for="item in payChannelList" :key="item.pay_channel">
          <div v-if="item.pay_channel === currentPayChannel"
            class="flex items-center bg-[#1D1D1E] px-3 py-6 rounded-xl border border-solid border-[#434546]">
            <img :src="item.icon" class="w-[4.375rem] h-[1.25rem] rounded mr-4" />
            <div class="text-sm text-[#FDFBFC]">{{ item.pay_channel === 'AirWallex' ?
              T.paymentPage.CreditOrDebitCard.ref :
              item.pay_channel }}</div>
          </div>
        </template>

      </div>
      <div class="px-3 mt-5 text-sm text-[#797B7D]">
        <div class="mb-2">Tips:</div>
        <div class="mb-2 flex gap-1">
          <div>1.</div>
          <div>Content Selection: You can choose to unlock free or paid content on the DramaWave.</div>
        </div>
        <div class="mb-2 flex gap-1">
          <div>2.</div>
          <div>Reward Coins: Earn Reward Coins through tasks and top-up bonuses. These can be used like regular Coins to
            unlock episodes.</div>
        </div>
        <div class="mb-2 flex gap-1">
          <div>3.</div>
          <div>Coins will be used first when unlocking the episode. If the amount is insufficient, reward coins will
            automatically be used.</div>
        </div>
        <div class="mb-2 flex gap-1">
          <div>4.</div>
          <div>Privilege: Enjoy unlimited access to all series on DramaWave during your subscription period.</div>
        </div>
        <div class="mb-2 flex gap-1">
          <div>5.</div>
          <div>Activation: Subscriptions will be activated within 24 hours of purchase.</div>
        </div>
        <div class="mb-2 flex gap-1">
          <div>6.</div>
          <div>Auto-Renewal: Subscriptions auto-renew at the original price 24 hours before each period, unless
            canceled.
          </div>
        </div>
        <div class="mb-2 flex gap-1">
          <div>7.</div>
          <div>Cancellation: Cancel subscriptions anytime, 24 hours before renewal.</div>
        </div>
        <div class="mb-2 flex gap-1">
          <div>8.</div>
          <div>Pricing: Various subscription plans are available, with pricing based on your country/region.</div>
        </div>
        <div class="mb-2 flex gap-1">
          <div>9.</div>
          <div>If you have any other questions, please contact our online customer service through "Profile" > "Customer
            Service Center".</div>
        </div>
      </div>



      <SvgIcon v-if="payLoading" name="payment/loading"
        class="size-8 animate-spin fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
      <Overlay :show="showTip" @click="showTip = false">
        <div class="flex justify-center items-center flex-col h-full" @click.stop>
          <Image class="w-[78%]" :src="Dialog" />
          <SvgIcon name="ic-close-circle" class="size-8" @click="showTip = false" />
        </div>
      </Overlay>
      <Popup v-model:show="showPop" :destroy-on-close="true" :close-on-click-overlay="true" position="bottom"
        :style="{ height: '220px' }" class="rounded-t-lg">
        <PayThirdButton :payChannel="payChannel" :paySubChannel="paySubChannel" :type="thirdType"
          :product-id="productId" :from="route.query.return_url as string || '/'" @clickRule="gotoRules" />
      </Popup>
    </div>

    <div class="fixed bottom-0 left-0 right-0 px-3 pt-2 pb-6 bg-[#0B080B] z-10">
      <div class="flex items-center w-full justify-center h-11 rounded-lg bg-[#FC2763]"
        @click="() => handleProductClick(currentPayChannel, currentProductId)">
        <div class="text-base font-medium text-[#FDFBFC]">{{ T.paymentPage.completePurchase.ref }}</div>
      </div>
      <div
        class="mt-4 w-fit mx-auto rounded-[200px] bg-[#242526] py-0.5 pl-1.5 pr-2 text-xs font-normal text-[#FDFBFC] flex items-center">
        <span
          class="bg-[url('src/h5_modules/payment-third/images/safety.webp')] bg-no-repeat bg-center inline-block bg-contain w-4 h-4 mr-1"></span>
        {{ T.paymentPage.PaymentInformationProtected.ref }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SvgIcon } from '@skynet/ui'
import { ref, onBeforeMount, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NavBar, Divider, Image, Collapse, CollapseItem, Overlay, Popup } from 'vant'
import { PaymentLogo, Dialog, Crown, paypal, apple, google, visa } from './images/images';
import { usePayment } from './use-payment';
import { getUser, setUser } from 'src/h5_modules/common/user'
import PayThirdButton from './payment-third-button.vue'
import { T } from './payment-page.i18n'
import { gotoRules } from 'src/h5_modules/common/common'
import { setIOSTheme } from 'src/lib/set-ios-theme'
import { isIos } from 'src/lib/ua'
import { h5Track } from 'src/lib/h5-track';
import { useI18n } from 'vue-i18n'
import { useLocale } from '../i18n/i18n'
import { supportedLocaleList } from '../common/constants'
import { createComponent, getQueries } from '@skynet/shared'

const { locale, messages } = useI18n()


const activeNames = ref(['1'])
const showTip = ref(false)
const router = useRouter()
const route = useRoute()
const { getProductList, productData, uid, handlePay, thirdType, showPop, productId, selectedPayChannel, payChannel, paySubChannel } = usePayment()
const selectedOption = ref(null)
const user = ref()
const payLoading = ref(false)
const currentPayChannel = ref('')
const currentProductId = ref(0)
const currentProductType = ref('')
const scrollIsTop = ref(true)


const unitPriceAmount = computed(() => {
  return productData.value?.unit_price_amount
})

const unitPriceCoins = computed(() => {
  return productData.value?.unit_price_coins
})

const currentProductData = computed(() => {
  console.log('productData', productData.value)
  const memList = productData.value?.membership || []
  const rechargeList = productData.value?.recharge_list || []
  if (memList.find(item => item.product_id === currentProductId.value)) {
    let currentProduct = memList.find(item => item.product_id === currentProductId.value)
    currentProduct = { ...currentProduct, find_product_type: 'membership' }
    return currentProduct
  } else if (rechargeList.find(item => item.product_id === currentProductId.value)) {
    let currentProduct = rechargeList.find(item => item.product_id === currentProductId.value)
    currentProduct = { ...currentProduct, find_product_type: 'recharge' }
    return currentProduct
  }
  return { discount_price: 0, price: 0, currency_symbol: '' }
})

const goApp = (type: number) => {
  if (type) {
    h5Track('h6pay', 'ios', 'click', {
      event: 'h6pay_ios_click',
      user_id: user.value.user_id,
      click_type: 'Changeplay',
    })
  }
  window.location.href = route.query.return_url
}

const payChannelList = computed(() => {
  return productData.value?.channel_list?.map(item => ({
    ...item,
    // icon: item.pay_channel === 'Paypal' ? paypal : item.pay_channel === 'Apple Store' ? apple : item.pay_channel === 'Google Play' ? google : visa,
    product_list: productData.value?.recharge_list
  }))
})

// 滚动处理函数
const handleScroll = () => {
  scrollIsTop.value = window.scrollY <= 10
}

onBeforeMount(async () => {
  const { auth_key, auth_secret, icon, user_id, user_type, name, product_id } = route.query
  const payment_channel = decodeURIComponent(route.query.payment_channel as string)
  if (user_id) {
    setUser({
      auth_key: auth_key as string,
      auth_secret: auth_secret as string,
      icon: icon as string,
      name: name as string,
      user_id: Number(user_id),
      user_type: Number(user_type)
    })
    console.log('pay_channel', payment_channel)
    currentPayChannel.value = payment_channel as string
    currentProductId.value = Number(product_id)
    productId.value = Number(product_id)
  }

  user.value = getUser()
  uid.value = user.value.user_id
  await getProductList()
})


const { currentLocale } = useLocale()

onMounted(() => {
  h5Track('h5pay', 'ios', 'show', {
    event: 'h5pay_ios_show',
    user_id: user.value.user_id,
  })
  // 设置状态栏颜色
  setIOSTheme('#fff')

  // 添加滚动事件监听
  window.addEventListener('scroll', handleScroll)

  const { language_code } = getQueries({
    language_code: '',
  })

  const locale = supportedLocaleList.find(item => item.shortCode === (language_code ?? 'en'))
  if (locale) {
    currentLocale.value = locale
  }
})

onUnmounted(() => {
  // 移除滚动事件监听
  window.removeEventListener('scroll', handleScroll)
  setIOSTheme('#0b080b')
})


const handleProductClick = async (channel: string, id: number) => {
  h5Track('h5', 'payment', 'source', {
    iap_pop: true,
  })

  h5Track('h5pay', 'ios', 'click', {
    event: 'h5pay_ios_click',
    user_id: user.value.user_id,
    click_type: 'purchase',
  })

  const { payment_channel, sub_payment_channel } = route.query
  productId.value = id;
  selectedPayChannel.value = channel
  payChannel.value = encodeURIComponent(payment_channel as string)
  paySubChannel.value = sub_payment_channel as string
  handlePay({
    from: route.query.return_url as string || '/',
    series_id: route.query.series_id as string,
    video_id: route.query.video_id as string,
    video_type: route.query.video_type as string,
  })
}


const paymentCards = [
  'payment/visa',
  'payment/mastercard',
  'payment/jcb',
  'payment/amex',
  'payment/unionpay',
  'payment/apple',
  'payment/google',
  'payment/paypal'
]

const notices = computed(() => {
  return T.paymentPage.noticeDetail.ref.value.split('\n').map(item => item.replace(/^\d./g, ''))
})

</script>

<style scoped>
:deep(.van-nav-bar__title) {
  color: rgba(255, 255, 255, 1);
  font-weight: 500;
}

:deep(.van-nav-bar) {
  --van-nav-bar-background: rgba(11, 8, 11, 1);
}

:deep(.van-cell) {
  --van-cell-vertical-padding: 12px;
  --van-cell-horizontal-padding: 12px;
  align-items: center;
}

:deep(.van-collapse-item__wrapper) {
  --van-collapse-item-content-padding: 0px;
}

:deep(.van-collapse-item--border:after) {
  border: none;
}

:deep(.van-collapse-item__title--expanded:after) {
  left: 0;
  right: 0;
}

:deep(.van-collapse) {
  --van-border-width: 0px;
}

:deep(.van-popup) {
  --van-popup-background: #fff
}

.custom-border {
  position: relative;
}

.custom-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

:deep(.page-is-top) {
  .van-nav-bar {
    background: transparent;
  }
}
</style>