declare namespace M {
  namespace UserQuery {
    interface Params {
      user_ids?: number[] // 用户id
      uids?: number[] // uid
      user_id?: string
      uid?: string
      email?: string
      emails?: string[]
      page_index?: number // 页码
      page_size?: number // 每页条数
    }

    interface UserInfo {
      user_id: number // 用户id
      uid: number // uid
      user_type: number // 0 匿名 1 facebook,2 google 3.appstore
      third_party_account: string
      user_status: number // 状态： 1:正常 2:注销
      country: string // 国家，0331新增
      name: string // 用户名，0331新增
      avatar: string // 头像，0331新增
    }

    interface UserWallet {
      cash_balance: number
      bonus_balance: number
      vip_level: number // 0-非Vip，1-vip
      vip_expire: number // vip到期时间
      vip_period: string // weekly,monthly,yearly
      last_subscribe_period: string // 最近订阅周期，0331新增
      subscribe_renewal_status: number // 订阅类型 1.购买订阅 2.续订，0331新增
      total_recharge_amount: number // 累计充值金额，0331新增
      total_gold_balance: number // 金币剩余，0331新增
    }

    interface Response {
      user_info: UserInfo
      user_wallet: UserWallet
      device_info: {
        items: Array<{
          device_id: string // 设备ID，0331新增
          ip: string // IP，0331新增
          last_active_time: number
        }>
        last_info: {
          device_manufacturer: string // 最后一次，手机厂商，0331新增
          device_model: string // 最后一次，手机型号，0331新增
          last_active_time: number // 最后一次活跃日期，0331新增
          last_active_app_version: string // 最后一次APP版本，0331新增
        }
      }
      showAll?: boolean // 展开
    }
  }
}
