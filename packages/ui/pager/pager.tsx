import { createComponent, fn, tryCall } from '@skynet/shared'
import { uniq } from 'lodash-es'
import { computed, VNodeChild } from 'vue'
import { Icon } from '../icon/icon'
import { MergeClass } from '../ui'
type PagerOptions = {
  props: {
    page?: number
    size?: number
    total?: number
    pageClass?: string
    currentPageClass?: string
    ellipsis?: VNodeChild | (() => VNodeChild)
    selectClass?: string
    pageSizeOptions?: number[]
  }
  emits: {
    'update:page'?: (page: number) => void
    'update:size'?: (size: number) => void
  }

}
export const Pager = createComponent<PagerOptions>({
  props: {
    page: 1,
    size: 10,
    total: 0,
    pageClass: 'join-item btn btn-sm',
    currentPageClass: 'btn-active',
    selectClass: 'select select-bordered select-sm',
    ellipsis: (
      <x-ellipsis class="join-item px-4 h-8 flex items-center">
        <Icon name="ant-design:ellipsis-outlined" />
      </x-ellipsis>
    ),
    pageSizeOptions: [10, 20, 30, 50, 100, 200, 1000],
  },
  emits: {
    'update:page': fn,
    'update:size': fn,
  },
}, (props, { emit }) => {
  const pageCount = computed(() => Math.ceil(props.total / props.size))
  const pages = computed(() => {
    const buttons = uniq([
      1,
      props.page - 3, props.page - 2, props.page - 1,
      props.page, props.page + 1,
      props.page + 2, props.page + 3,
      pageCount.value,
    ].sort((a, b) => a - b)
      .filter(x => x >= 1 && x <= pageCount.value)).reduce((acc, cur, index, array) => {
      if (index > 0 && cur - array[index - 1] > 1) acc.push('...')
      return acc.concat(cur)
    }, [] as Array<number | '...'>)
    return buttons
  })
  return () => (
    <MergeClass tag="x-pager" baseClass="flex flex-nowrap gap-x-2 items-center flex-row">
      <x-total>共{props.total}条</x-total>
      {
        pages.value.length <= 1
          ? null
          : (
              <x-pages class="join">
                { pages.value.map(n => (
                  n === '...'
                    ? (tryCall(props.ellipsis))
                    : (
                        <button
                          class={[props.pageClass, { [props.currentPageClass]: n === props.page }]}
                          onClick={() => emit('update:page', n)}> {n} </button>
                      )
                ))}
              </x-pages>
            )
      }
      <x-select-wrapper class="block">
        <select
          class={props.selectClass}
          value={props.size}
          onInput={(e: Event) => emit('update:size', Number((e.target as HTMLSelectElement).value))}
        >
          { props.pageSizeOptions.map(n => <option value={n}> {n} 条/页 </option>) }
        </select>
      </x-select-wrapper>
    </MergeClass>
  )
})
