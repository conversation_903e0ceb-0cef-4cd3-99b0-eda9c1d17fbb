/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Button, Pager, Icon, RadioGroup } from '@skynet/ui'
import { AuthButton } from './components/auth-button'
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { set } from 'lodash-es'
import { useRoute } from 'vue-router'
import { openDialog, showFailToast, showSuccessToast } from '@skynet/ui'
import { ResourceForm } from './components/resource-form'
import { apiSeriesResourceList, apiGetEpisodeBatchList } from './resource-api'
import { CreateFormMultiSelect } from '@skynet/ui/form/form-multi-select'
import { langKeyForCol, langValue } from './constant'
import { useMenu } from 'src/modules/menu/use-menu'
import { useResourceStore } from './use-resource-store'
import { idStrToList } from './util'
import { useResourceListStore } from './use-resource-list'

type ResourcePageOptions = {
  props: {}
}

export const ResourcePage = createComponent<ResourcePageOptions>({
  props: {},
}, props => {
  const { getTags, onUploadResourceExcel, onUploadBusinessExcel } = useResourceStore()
  const { getList,
    loading,
    list,
    form,
    pageInfo,
    colsOptions,
    visibleCol,
    allMeta,
    getFilterMeta,
    onReset,
    onPageChange,
    onQuery,
    onPageSizeChange } = useResourceListStore()
  const FormMultiSelect = CreateFormMultiSelect<string | number | boolean>()
  const TableMultiSelect = CreateFormMultiSelect<string | number | boolean>()
  const route = useRoute()
  const { getRole, getUserRoles } = useMenu()
  const role = getRole() as string
  const Table = CreateTableOld<M.IResourceColumn>()
  const roles = getUserRoles()

  const QueryForm = CreateForm<{
    title: string
    series_resource_id: string
    series_key_list: string
  }>()

  let columns: TableColumnOld<M.IResourceColumn | any>[] = []

  const makeExcel = (data: any, name: string) => {
    return new Promise(resolve => {
      // 将数据转换为 CSV 格式
      const csvContent = data.map((e: any) => e.join(',')).join('\n')
      // 添加 UTF-8 BOM
      const bom = '\uFEFF'
      const finalContent = bom + csvContent

      // 创建一个 Blob 对象
      const blob = new Blob([finalContent], { type: 'text/csv;charset=utf-8;' })

      // 创建下载链接
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', name)
      document.body.appendChild(link)
      link.click()
      resolve('')
    })
  }

  const onDownload = async (id: number, langList: string[], name: string, count: number) => {
    try {
      const res = await apiSeriesResourceList({
        page_index: 1,
        page_size: 300,
        series_resource_id: id,
      })
      const _list = res?.data?.list_v2 || []
      const tableHeader = [
        '集数',
        ...langList.map(lang => {
          if (lang === '0') return '含字幕视频'
          if (lang === '-1') return '无字幕视频'
          return langValue[langKeyForCol.indexOf(lang)]
        }),
      ]
      const notFinished = ['trans_failed', 'extract_failed', 'failed', 'processing', '']
      const tableData = []
      for (let i = 1; i <= count; i++) {
        const row = _list.find(item => item.serial_number === i)
        if (!row) {
          tableData.push([i, ...langList.map(_ => '-')])
        } else {
          tableData.push([
            row.serial_number,
            ...langList.map(lang => {
              if (lang === '0') return row.origin_path
              if (lang === '-1') return row.pure_path
              return notFinished.includes(row[`${lang}_episode_path`]) ? '-' : row[`${lang}_episode_path`]
            }),
          ])
        }
      }

      await makeExcel([
        tableHeader,
        ...tableData,
      ], name)
    } catch (error) {
      loading.value = false
    }
  }

  const onDownloadBatchRes = async (episodeList: M.IEpisodeBatchItem[], langArr: string[]) => {
    try {
      const tableHeader = [
        '资源ID',
        '集数',
        '成片',
        ...langArr.map(lang => {
          return langValue[langKeyForCol.indexOf(lang)]
        }),
      ]
      const notFinished = ['trans_failed', 'extract_failed', 'failed', 'processing', '']
      const tableData: any = []

      episodeList.map(item => {
        tableData.push([
          item.series_resource_id,
          item.serial_number,
          item.origin_path,
          ...langArr.map(lang => {
            if (lang === '0') return item.origin_path
            return notFinished.includes(item[`${lang}_episode_path`]) ? '-' : item[`${lang}_episode_path`]
          }),
        ])
      })

      await makeExcel([
        tableHeader,
        ...tableData,
      ], '' + Date.now())
    } catch (error) {
      loading.value = false
    }
  }

  watch(() => [list.value, visibleCol.value], () => {
    const meta = getFilterMeta()
    if (meta && meta?.length > 0) {
      columns = meta.map(header => {
        const index = allMeta.value?.indexOf(header)
        let w = 'w-[100px]'
        if (['创建时间', '更新时间', '资源名称'].includes(header)) {
          w = 'w-[170px]'
        } else if (header.length > 7) {
          w = 'w-[150px]'
        } else if (header.length > 5) {
          w = 'w-[120px]'
        } else if (header.length > 4) {
          w = 'w-[100px]'
        }
        return [
          header,
          row => {
            if (typeof row[`column_${index}`] === 'object') {
              const value = row[`column_${index}`] as { failed_count: number, finish_count: number, is_sync: number, tasking_count: number, total_count: number }
              let taskNum = 0
              let taskName = ''
              let roundColor = ''
              // 优先级 如果有 处理失败的 展示 处理失败的状态、之后如果有未处理的 就展示
              if (value.failed_count > 0) {
                taskNum = value.failed_count
                taskName = '任务失败'
                roundColor = 'bg-[red]'
              } else if (value.tasking_count > 0) {
                taskNum = value.tasking_count
                roundColor = 'bg-primary'
                taskName = '任务中'
              } else {
                taskNum = 0
              }
              return (
                <div>
                  <div class={!taskName && value.finish_count === 0 ? 'text-[var(--text-1)]' : value.finish_count !== value.total_count ? 'text-[red]' : 'text-[green]'}>
                    <span>{value.finish_count}</span>
                    /
                    <span>{value.total_count}</span>
                  </div>
                  {value.is_sync === 1
                    ? (
                        <div class="flex items-center justify-center  gap-x-1 text-center">
                          <div class={mc('rounded-full size-2', 'bg-green-500')} />
                          <div>已同步</div>
                        </div>
                      )
                    : null }
                  {taskNum > 0
                    ? (
                        <div class="flex items-center justify-center gap-x-1 text-center">
                          <div class={mc('rounded-full size-2', roundColor)} />
                          <div>{taskName}: {taskNum}</div>
                        </div>
                      )
                    : null }
                </div>
              )
            } else {
              // const v = String(row[`column_${index}`])
              // const nums = v?.split('/')
              // if (v.indexOf('/') !== -1 && nums.length === 2 && typeof +nums[0] === 'number' && typeof +nums[1] === 'number') {
              //   return (
              //     <div class={nums[0] !== nums[1] ? 'text-[red]' : 'text-[green]'}>
              //       <span>{nums[0]}</span>
              //       /
              //       <span>{nums[1]}</span>
              //     </div>
              //   )
              // }
              return row[`column_${index}`]
            }
          },
          {
            class: `${w} text-center`,
          },
        ]
      })
      columns.push([
        '操作',
        row => {
          const oprLoading = ref(false)
          return (
            <div class="space-x-2">
              <button class="btn btn-link btn-xs" onClick={() => {
                // void router.push(`${route.fullPath}/${row['column_0']}`)
                window.open(`${route.fullPath}/${row['column_0']}`)
              }}
              >详情
              </button>
              <AuthButton blackRoleList={['3']} class="btn btn-link btn-xs" onClick={() => {
                const langList = ref<string[]>([])
                const hideDeleteDialog = openDialog({
                  title: '下载视频地址',
                  mainClass: 'pb-0 px-5',
                  body: () => (
                    <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                      <div class="flex items-center space-x-1">
                        <span>语言:</span>
                        <FormMultiSelect
                          class="w-[200px]"
                          popoverWrapperClass="z-popover-in-dialog"
                          options={
                            [
                              {
                                value: '-1',
                                label: '无字幕视频',
                              },
                              {
                                value: '0',
                                label: '含字幕视频',
                              },
                              ...langKeyForCol.map((n, index) => {
                                return { value: langKeyForCol[index], label: langValue[index] }
                              })]
                          }
                          modelValue={langList.value}
                          onUpdate:modelValue={e => {
                            langList.value = e as string[]
                          }}
                        />
                      </div>
                      <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                        <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                        <Button class="btn btn-primary btn-sm" disabled={oprLoading.value} onClick={async () => {
                          if (langList.value.length === 0) {
                            showFailToast('请选择视频语言')
                            return
                          }
                          try {
                            oprLoading.value = true
                            await onDownload(row['column_0'], langList.value, row['column_1'], row['column_2'])
                            showSuccessToast('操作成功')
                            hideDeleteDialog()
                          } catch (error: any) {
                            showFailToast(error.response.data.message || '操作失败')
                          } finally {
                            oprLoading.value = false
                          }
                        }}
                        >
                          {oprLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                          确定
                        </Button>
                      </x-delete-episode-footer>
                    </x-delete-episode-confirm-dialog>
                  ),
                })
              }}
              >下载
              </AuthButton>
            </div>
          )
        },
        { class: `${role === '3' ? 'w-[100px]' : 'w-[130px]'} text-center` },
      ])
    }
  })

  onMounted(async () => {
    void getTags('zh-CN')
    await onReset()
  })
  let interval = null
  interval = setInterval(() => {
    void getList(false)
  }, 60 * 1000)

  onUnmounted(() => {
    clearInterval(interval)
  })

  return () => (
    <div class="space-y-4 overflow-hidden">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>资源列表</li>
          </ul>
        ),
        form: () => (
          <>
            <QueryForm
              class="w-full"
              onSubmit={onQuery}
              onReset={onReset}
              data={form.value}
              onChange={(path, value) => {
                set(form.value, path, value)
              }}
              items={[
                { label: '资源名', path: 'title', input: { type: 'text' } },
                { label: '资源ID', path: 'series_resource_id', input: { type: 'text' } },
                { label: '关联剧集ID', path: 'series_key_list', input: { type: 'text' } },
              ]}
            />
          </>
        ),
        tableActions: () => (
          <x-table-actions class="flex w-full items-center justify-between">
            <div class="flex items-center space-x-1">
              <span>选择可见区域:</span>
              <TableMultiSelect
                class="flex-1"
                popoverWrapperClass="z-popover-in-dialog"
                options={colsOptions.value}
                modelValue={visibleCol.value}
                onUpdate:modelValue={e => {
                  visibleCol.value = e as number[]
                }}
              />
            </div>
            <x-table-actions-right class="flex gap-x-2">
              <div class="dropdown">
                <div class="space-x-2">
                  {
                    role === '3' ? null : <div tabindex={0} role="button" class="btn btn-primary btn-sm">批量新增</div>
                  }
                </div>
                <ul tabindex={0} class="menu dropdown-content z-up w-52 rounded-box bg-base-100 p-2 shadow">
                  <li>
                    <a href="javascript:void(0);" onClick={() => { onUploadBusinessExcel(onQuery) }}>商务信息导入</a>
                  </li>
                  <li>
                    <a href="javascript:void(0);" onClick={() => { onUploadResourceExcel(onQuery) }}>资源信息导入</a>
                  </li>
                </ul>
              </div>
              {/* {roles.includes(30) ? (
                <Button class="btn btn-primary btn-sm" onClick={() => {
                  const oprLoading = ref(false)
                  const langList = ref<string[]>([])
                  const oprType = ref<1 | 2>(1)
                  const hideDeleteDialog = openDialog({
                    title: '下载视频地址',
                    mainClass: 'pb-0 px-5',
                    body: () => (
                      <x-delete-episode-confirm-dialog class="flex flex-col gap-y-[25px]">
                        <div class="flex items-center space-x-1">
                          <span>语言:</span>
                          <FormMultiSelect
                            class="w-[200px]"
                            popoverWrapperClass="z-popover-in-dialog"
                            options={
                              [
                                ...langKeyForCol.map((n, index) => {
                                  return { value: langKeyForCol[index], label: langValue[index] }
                                })]
                            }
                            modelValue={langList.value}
                            onUpdate:modelValue={e => {
                              langList.value = e as string[]
                            }}
                          />
                        </div>
                        <div class="flex items-center space-x-1">
                          <span>视频类型:</span>
                          <RadioGroup
                            options={[
                              { label: '免费', value: 1 },
                              { label: '全部', value: 2 },
                            ]}
                            modelValue={oprType.value}
                            onUpdate:modelValue={e => {
                              oprType.value = e as 1 | 2
                            }}
                          />
                        </div>
                        <x-delete-episode-footer class="flex w-full justify-end gap-x-[10px]">
                          <Button class="btn btn-ghost btn-sm" onClick={() => hideDeleteDialog()}>取消</Button>
                          <Button class="btn btn-primary btn-sm" disabled={oprLoading.value} onClick={async () => {
                            if (langList.value.length === 0) {
                              showFailToast('请选择视频语言')
                              return
                            }
                            try {
                              oprLoading.value = true
                              const res = await apiGetEpisodeBatchList({
                                series_resource_id_list: idStrToList(form.value.series_resource_id).map(id => +id),
                                language_code_list: langList.value,
                                type: oprType.value,
                              })
                              const exportList = res.data?.list || []
                              await onDownloadBatchRes(exportList, langList.value)
                              showSuccessToast('操作成功')
                              hideDeleteDialog()
                            } catch (error: any) {
                              showFailToast(error.response.data.message || '操作失败')
                            } finally {
                              oprLoading.value = false
                            }
                          }}
                          >
                            {oprLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            确定
                          </Button>
                        </x-delete-episode-footer>
                      </x-delete-episode-confirm-dialog>
                    ),
                  })
                }}>批量导出视频地址</Button>
              ) : null} */}
              <AuthButton blackRoleList={['3']} class="btn btn-primary btn-sm" onClick={() => {
                const createDialog = openDialog({
                  title: '新增资源',
                  mainClass: 'pb-0 px-5',
                  body: (
                    <ResourceForm
                      count={undefined}
                      clazz="w-full flex flex-col"
                      onClose={() => createDialog()}
                      onAfterSave={() => {
                        createDialog()
                        void onQuery()
                      }}
                    />
                  ),
                })
              }}
              >新建资源
              </AuthButton>
            </x-table-actions-right>

          </x-table-actions>
        ),
        table: () => (
          <Table
            class="tm-table-fix-header tm-table-fix-first-column tm-table-fix-last-column !max-h-[calc(100vh-220px)]"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.page} v-model:size={pageInfo.pageSize} total={pageInfo.total} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ResourcePage
