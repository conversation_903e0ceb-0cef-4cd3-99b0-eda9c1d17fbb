import { httpClient } from 'src/lib/http-client'
import dayjs from 'dayjs'
import { get_k_sso_token } from 'src/lib/device-id.ts'

export const trimSplitIt = (separator: string[] = [',', ' ']) => (str: string) => str.split(new RegExp(`[${separator.join('')}]`)).map(str => str.replace(new RegExp(`^[${[' '].join('')}]+|[${[' '].join('')}]+$`, 'g'), ''))

const dateFormat = (time: number) => {
  if (!time) return '-'
  return dayjs(time * 1000).format('YYYY-MM-DD HH:mm')
}

const formatDateTime = (time: string) => {
  if (!time) return 0
  return new Date(time).getTime() / 1000
}

const formatEndDateTime = (time: string) => {
  if (!time) return 0
  return dayjs(time).endOf('day').unix()
}

export const apiGetUserStatistic = (data: Api.ResourceUserStatistic.Param) =>
  httpClient.post<ApiResponse<{
    list: Api.ResourceUserStatistic.List
    total: number
  }>>('/resource_stat/resource_user_list', data, {
    transformResponseData: {
      'data.list': [(list: Api.ResourceUserStatistic.List) => {
        return list.map(item => {
          item.cal_time =  item.cal_time ? dateFormat(item.cal_time as number) : '-'
          return item
        })
      }],
    },
    transformRequestData: {
      end_time: [formatEndDateTime],
      start_time: [formatDateTime],
    }
  })

export const apiGetGroupStatistic = (data: Api.ResourceUserStatistic.Param) =>
  httpClient.post<ApiResponse<{
    list: Api.ResourceUserStatistic.List
    total: number
  }>>('/resource_stat/resource_user_group_list', data, {
    transformResponseData: {
      'data.list': [(list: Api.ResourceUserStatistic.List) => {
        return list.map(item => {
          item.cal_time =  item.cal_time ? dateFormat(item.cal_time as number) : '-'
          return item
        })
      }],
    },
    transformRequestData: {
      end_time: [formatEndDateTime],
      start_time: [formatDateTime],
    }
  })

export const apiExportGroupStatistic = (data: Api.ResourceUserStatistic.Param) => {
  const params = {
    ...data
  }
  return fetch(`${import.meta.env.VITE_DRAMA_API_URL}/resource_stat/resource_user_list/export`, {
    method: 'post',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      Device: 'Web',
      Token: get_k_sso_token() || '',
    },
    body: JSON.stringify(params),
  })
}
