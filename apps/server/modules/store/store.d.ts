export interface MyWalletInfo {
  cash_balance: number
  bonus_balance: number
}
// 支付模式类型
type PayModel = 'IAP' | 'IAA'

// 发货详情类型（充值）
interface RechargeDeliveryDetails {
  period: string
  bonus?: number
  bonus_str?: string
  quanity: number
  quanity_str: string
}

// 发货详情类型（会员）
interface MembershipDeliveryDetails {
  period: 'weekly' | 'monthly' | 'yearly'
}

// 商品属性类型
type ProductProp = 'highlight' | 'default'

// 支付渠道类型
type PayChannel = 'appleiap' | 'googleplay'

export interface PaymentMethod {
  pay_channel: string // 展示名称
  payment_channel: string // 渠道名称
  sub_payment_channel: string // 子渠道名称(支付方式)
  unit_price_coins: string // 金币数
  unit_price_amount: string // 金额（分）
  icon: string // 支付方式图标
}

// 基础商品接口
interface BaseProduct {
  id: number
  type: 'recharge' | 'membership'
  title: string
  currency: string
  has_discount: 0 | 1
  expire_time: number
  slogan?: string // 可选属性
  discount_desc?: string
  description: string
  tips: string
  price: number // 单位：分
  discount_price: number // 单位：分
  props: ProductProp[]
  pay_channel: PayChannel
  r_info: string
}

// 充值商品接口
interface RechargeProduct extends BaseProduct {
  type: 'recharge'
  product_id: number
  currency_symbol: string
  delivery_details: RechargeDeliveryDetails
  sku_id: string // 充值独有字段
}

// 会员商品接口
export interface MembershipProduct extends BaseProduct {
  type: 'membership'
  product_id: number
  currency_symbol: string
  delivery_details: MembershipDeliveryDetails
}

// 完整数据结构
export interface StoreDataInfo {
  strategy_group_id: string
  product_id: number
  pay_model: PayModel
  recharge_list: RechargeProduct[]
  membership: MembershipProduct[]
  channel_list: PaymentMethod[]
  unit_price_amount: number
  unit_price_coins: number
}
