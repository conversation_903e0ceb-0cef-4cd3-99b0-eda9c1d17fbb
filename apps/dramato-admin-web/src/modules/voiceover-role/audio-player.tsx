import { createComponent } from '@skynet/shared'
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { SvgIcon } from '@skynet/ui'

type AudioPlayerOptions = {
  props: {
    src: string
    preload?: 'auto'| 'metadata' | 'none'
  }
  emits: {
    play: () => void
  }
}

export const AudioPlayer = createComponent<AudioPlayerOptions>({
  props: {
    src: '',
    preload: 'none'
  },
  emits: {
    play: () => {},
  },
}, (props, { emit }) => {
  const duration = ref(0)

  watch(() => props.src, () => {
    if (props.preload === 'none') return
    const audio = new Audio(props.src)
    audio.onloadedmetadata = () => {
      duration.value = audio.duration * 1000
    }
  }, { immediate: true })

  const isPlaying = ref(false)
  const progress = ref(0)
  const audioRef = ref<HTMLAudioElement>()

  const durationString = computed(() => msToTime(duration.value))

  const togglePlay = () => {
    const audio = audioRef.value
    if (isPlaying.value && audio) {
      audio.pause()
    } else {
      void audio?.play()
      emit('play')
    }
    const audioEl = new Audio(props.src)
    audioEl.onloadedmetadata = () => {
      duration.value = audioEl.duration * 1000
    }
    isPlaying.value = !isPlaying.value
  }

  const pause = () => {
    const audio = audioRef.value
    audio?.pause()
    isPlaying.value = false
  }

  const reset = () => {
    pause()
    progress.value = 0
    const audio = audioRef.value
    if (audio) {
      audio.currentTime = 0
    }
  }

  const msToTime = (duration: number) => {
    const seconds = Math.floor((duration % 60000) / 1000)
    const minutes = Math.floor(duration / 60000)
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }

  const updateProgress = () => {
    const audio = audioRef.value
    if (audio) {
      progress.value = audio.currentTime / audio.duration
    }
  }

  onBeforeUnmount(() => {
    reset()
    audioRef.value?.removeEventListener('timeupdate', updateProgress)
  })

  onMounted(() => {
    audioRef.value?.addEventListener('timeupdate', updateProgress)
  })

  return () => (
    <div class="flex flex-col truncate">
      <audio
        class="invisible absolute h-0"
        onEnded={reset}
        ref={audioRef}
        src={props.src}
        controls
        preload={props.preload}
      />
      <div class="flex h-[26px] items-center justify-start gap-[12px]">
        <div class="rounded-full" onClick={togglePlay}>
          {
            isPlaying.value ? (
              <SvgIcon
                key={1}
                class="relative size-[26px]"
                name="ic_pause_phone"
              />
            ) : (
              <SvgIcon
                key={2}
                class="relative size-[26px]"
                name="ic_play_phone"
              />
            )
          }
        </div>
        <div class="flex h-[20px] shrink grow basis-0 items-center justify-start gap-[8px]">
          <div class="relative flex flex-1 shrink grow basis-0 flex-col items-start justify-start gap-2.5 rounded bg-[#EDEFF1]">
            <div
              class="absolute -top-[2px] -ml-[2px] size-[8px] rounded-full bg-[#2785FF]"
              style={{
                left: `calc((100%*${progress.value}))`,
              }}
            />
            <div
              class="h-[4px] rounded-[4px] bg-[#BCDEFF]"
              style={{
                width: `calc(100%*${progress.value})`,
              }}
            />
          </div>
          <div class="text-[12px] font-normal leading-[20px] text-[#485568]">
            {msToTime(duration.value * progress.value)}/{durationString.value}
          </div>
        </div>
      </div>
    </div>
  )
})
