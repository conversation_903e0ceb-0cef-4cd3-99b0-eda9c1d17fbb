diff --git a/dist/shaka-player.compiled.d.ts b/dist/shaka-player.compiled.d.ts
index 19c093064f65798e767341b0186707d1db7c2dff..cc0a3fd3e66a2c964989d42270ac682a6a7ae04d 100644
--- a/dist/shaka-player.compiled.d.ts
+++ b/dist/shaka-player.compiled.d.ts
@@ -5117,3 +5117,5 @@ declare namespace shaka.extern {
 declare namespace shaka.extern {
   type TransmuxerPlugin = ( ) => shaka.extern.Transmuxer ;
 }
+
+export default shaka;
diff --git a/dist/shaka-player.ui.d.ts b/dist/shaka-player.ui.d.ts
index 1618ca0d62575cdc4e56271ce483d72d6e97e04a..a6076c6b04c49ea19401f362a24ab395f066891f 100644
--- a/dist/shaka-player.ui.d.ts
+++ b/dist/shaka-player.ui.d.ts
@@ -5830,3 +5830,5 @@ declare namespace shaka.extern {
 declare namespace shaka.extern {
   type UIVolumeBarColors = { base : string , level : string } ;
 }
+
+export default shaka;
diff --git a/index.d.ts b/index.d.ts
new file mode 100644
index 0000000000000000000000000000000000000000..86130b219f20157404545f817f41485ab13ced25
--- /dev/null
+++ b/index.d.ts
@@ -0,0 +1,2 @@
+/// <reference path="./dist/shaka-player.compiled.d.ts" />
+/// <reference path="./dist/shaka-player.ui.d.ts" />
diff --git a/ui.d.ts b/ui.d.ts
new file mode 100644
index 0000000000000000000000000000000000000000..747137162abfb952a82f8356dadf8fc5cd914ebc
--- /dev/null
+++ b/ui.d.ts
@@ -0,0 +1,3 @@
+import shaka from 'shaka-player/dist/shaka-player.ui'
+export * from 'shaka-player/dist/shaka-player.ui'
+export default shaka;
