import { httpClient } from 'src/lib/http-client'

export const apiGetUserPayList = (data: M.UserPay.Params) =>
  httpClient.post<ApiResponse<M.UserPay.Response>>('/wallet/payment/order/query', data)

export const apiWalletAssetReport = (data: M.UserPay.orderFormParams) =>
  httpClient.post<ApiResponse<boolean>>('/wallet/asset/report', data)

export const apiFetchAppleOrder = (data: M.UserPay.AppleOrderParams) =>
  httpClient.post<ApiResponse<boolean>>('/wallet/asset/apple/query', data)
