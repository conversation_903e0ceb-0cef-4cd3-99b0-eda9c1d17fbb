import type { ImagePreviewOptions } from './image-preview'
import { createVNode, ref, render } from 'vue'
import { ImagePreview } from './image-preview'

export const useImagePreviewStore = () => {
  return {
    showImagePreviewDialog,
    hideImagePreviewDialog,
    imagePreviewStack,
  }
}

const imagePreviewStack = ref<ImagePreviewOptions[]>([])
/**
 * 压入 dialog
 * @returns dialog 的 index
 */
const showImagePreviewDialog = (props: ImagePreviewOptions) => {
  const rootDiv = document.querySelector('#mountRoot')
  const wrapper = document.createElement('div')
  wrapper.id = 'image-preview-wrapper'
  console.log('props', props)

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const vnode = createVNode(ImagePreview, props as any)
  render(vnode, wrapper)
  rootDiv?.appendChild(wrapper)
}

/**
 * 删除 dialog
 */
const hideImagePreviewDialog = () => {
  const rootDiv = document.querySelector('#mountRoot')
  const wrapper = document.querySelector('#image-preview-wrapper')
  rootDiv?.removeChild(wrapper as HTMLElement)
}
