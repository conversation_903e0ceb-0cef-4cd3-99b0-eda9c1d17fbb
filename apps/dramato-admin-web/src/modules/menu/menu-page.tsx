/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Wrapper } from 'src/layouts/wrapper'
import { SvgIcon, openDialog, showFailToast, showSuccessToast, DialogFooter } from '@skynet/ui'
import { useMenu } from './use-menu'
import { MenuForm } from './components/menu-form'
import { ref } from 'vue'
import { apiCreateMenu, apiDelMenu, apiUpdateMenu } from './menu-api'

type MenuPageOptions = {
  props: {}
}
export const MenuPage = createComponent<MenuPageOptions>({
  props: {},
}, props => {
  const { getMenuInfo, menus, getRoles } = useMenu()
  void getMenuInfo()
  void getRoles()

  const curMenuItem = ref<M.IMenuItem>({
    parent_id: 0,
    path: '',
    title: '',
  })

  // 添加目录
  const addSubtitle = (menuSubItem: M.IMenuItem, parent_name: string) => {
    curMenuItem.value = {
      parent_id: menuSubItem.id,
      parent_name: menuSubItem.title,
    }
    const hideDialog = openDialog({
      title: '新增菜单',
      mainClass: 'pb-0 px-5',
      body: (
        <>
          <MenuForm
            menuItem={{
              parent_name: curMenuItem.value.parent_name,
              parent_id: curMenuItem.value.parent_id,
              serial_number: 0
            }}
            onMenuItemChange={async e => {
              curMenuItem.value = {
                ...e,
              }
              try {
                await apiCreateMenu({
                  parent_id: curMenuItem.value.parent_id,
                  title: curMenuItem.value.title,
                  path: curMenuItem.value.path,
                  roles: curMenuItem.value.roles,
                  serial_number: curMenuItem.value.serial_number
                })
                void getMenuInfo()
                showSuccessToast('操作成功')
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              }
              hideDialog()
            }}
          />
        </>
      ),
    })
  }

  const updateMenuItem = (menuSubItem: M.IMenuItem, parent_name: string) => {
    curMenuItem.value = {
      ...menuSubItem,
    }
    const hideDialog = openDialog({
      title: '更新菜单',
      mainClass: 'pb-0 px-5',
      body: (
        <>
          <MenuForm
            menuItem={{
              parent_name: parent_name,
              parent_id: curMenuItem.value.parent_id,
              title: curMenuItem.value.title,
              id: curMenuItem.value.id,
              path: curMenuItem.value.path,
              roles: curMenuItem.value.roles?.map(role => +role),
              serial_number: curMenuItem.value.serial_number
            }}
            onMenuItemChange={async e => {
              curMenuItem.value = {
                ...e,
              }
              try {
                await apiUpdateMenu({
                  parent_id: curMenuItem.value.parent_id,
                  title: curMenuItem.value.title,
                  path: curMenuItem.value.path,
                  id: curMenuItem.value.id,
                  roles: curMenuItem.value.roles,
                  serial_number: curMenuItem.value.serial_number
                })
                void getMenuInfo()
                showSuccessToast('操作成功')
              } catch (error: any) {
                showFailToast(error.response.data.message || '操作失败')
              }
              hideDialog()
            }}
          />
        </>
      ),
    })
  }

  const delMenuItem = (item: M.IMenuItem) => {
    const closeDialog = openDialog({
      title: '提示',
      body: (
        <div>
          确认删除【{item.title}】
          <DialogFooter okText="确认" onOk={async () => {
            closeDialog()
            const response = await apiDelMenu({
              id: item.id
            })
            if (response.code === 200) {
              void getMenuInfo()
              showSuccessToast('操作成功！')
            } else {
              showFailToast('操作失败')
            }
          }} cancelText="取消" onCancel={() => closeDialog()}
          />
        </div>
      ),
    })
  }

  const renderMenuItem = (menuSubItem: M.IMenuItem, parent_name: string) => {
    return (
      <li class="flex flex-row hover group items-center gap-x-4">
        <a href="javascript:void(0);">{menuSubItem.title} - {menuSubItem.path}</a>
        <button class="btn btn-xs btn-circle hidden group-hover:block" onClick={() => {
          updateMenuItem(menuSubItem, parent_name)
        }}
        >
          <SvgIcon name="ic_edit" />
        </button>
        <button class="btn btn-xs btn-circle hidden group-hover:block" onClick={() => delMenuItem(menuSubItem)}>
          <SvgIcon name="ic_del" />
        </button>
      </li>
    )
  }

  // 目录渲染
  const renderSubTitle = (menuSubItem: M.IMenuItem, parent_name: string) => {
    const id = menuSubItem.id
    return (
      <li>
        <details open>
          <summary class="inline-flex hover group">
            <span>{menuSubItem.title}</span>
            {
              menuSubItem.id === 0 || menuSubItem.parent_id === 0
                ? (
                    <button class="btn btn-xs btn-circle hidden group-hover:block" onClick={() => {
                      addSubtitle(menuSubItem, parent_name)
                    }}
                    >
                      <SvgIcon name="ic_black_add" />
                    </button>
                  )
                : null
            }
            {
              id !== 0
                ? (
                    <>
                      <button class="btn btn-xs btn-circle hidden group-hover:block" onClick={() => {
                        updateMenuItem(menuSubItem, parent_name)
                      }}
                      >
                        <SvgIcon name="ic_edit" />
                      </button>
                      <button class="btn btn-xs btn-circle hidden group-hover:block" onClick={() => delMenuItem(menuSubItem)}>
                        <SvgIcon name="ic_del" />
                      </button>
                    </>
                  )
                : null
            }
          </summary>
          <ul>
            {menuSubItem.children && renderMenu(menuSubItem.children, menuSubItem.title)}
          </ul>
        </details>
      </li>
    )
  }

  const renderMenu = (menus: M.IMenuItem[], parent_name = '根目录') => {
    return menus.map(menuSubItem => {
      if (menuSubItem.path) {
        return renderMenuItem(menuSubItem, parent_name)
      } else {
        return renderSubTitle(menuSubItem, parent_name)
      }
    })
  }

  return () => (
    <Wrapper>
      <div>
        <span>菜单管理：</span>
      </div>
      <ul class="menu lg:menu-horizontal bg-base-200 rounded-box lg:mb-64">
        {renderMenu(menus.value)}
      </ul>
    </Wrapper>
  )
})

export default MenuPage
