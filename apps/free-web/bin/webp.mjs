import imagemin from 'imagemin'
import imageminWebp from 'imagemin-webp'
import {join} from 'node:path'

const firstArg = process.argv[2];
const target = `${firstArg}/*.{png,jpg,jpeg}`
console.log('target: ', target)

const run = async () => {
  // 检测imagemin和imagemin-webp是否安装
  if (!imagemin || !imageminWebp) {
    console.log('Please install imagemin and imagemin-webp first!')
    return
  }
  const files = await imagemin([target], {
    destination: `${firstArg}`,
    plugins: [
      imageminWebp({quality: 100})
    ]
  });

  console.log('Images converted successfully!');
  console.log(files.map(({destinationPath}) => destinationPath).join('\n'));
}

run()
