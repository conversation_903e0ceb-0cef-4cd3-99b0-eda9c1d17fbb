import { createComponent } from '@skynet/shared'
import { Input } from '@skynet/ui'
import { ref, watch } from 'vue'

export const InputExamplesSearchable = createComponent(null, () => {
  const school = ref<string>('')
  const results = ref<School[]>([])

  type School = {
    name: string
  }

  watch(() => school.value, () => {
    if (!school.value) {
      results.value = []
      return
    }
    results.value = [
      { name: '清华大学' },
      { name: '北京大学' },
      { name: '复旦大学' },
      { name: '上海交通大学' },
      { name: '南京大学' },
    ].filter(item => item.name.includes(school.value))
  })

  const getResultDom = (results: School[]) => {
    if (!results.length) return null
    return () => (
      <ul class="bg-[var(--fill-4)] w-80">
        {
          results.map(item => (
            <li
              class="p-1 cursor-pointer flex h-10 hover:bg-[var(--fill-1)]"
              onClick={() => school.value = item.name}
            >
              <div>{item.name}</div>
            </li>
          ))
        }
      </ul>
    )
  }

  return () => (
    <div class="space-y-2 p-2 markdown-body">
      <Input
        searchable
        placeholder="请输入姓名"
        class="w-80 h-10 inline-block"
        v-model={school.value}
        results={getResultDom(results.value)}
      />
    </div>
  )
})

export default InputExamplesSearchable
