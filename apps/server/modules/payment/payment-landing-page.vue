<template>
  <div class="h-screen bg-white">
    <SvgIcon v-if="payLoading" name="payment/loading"
      class="size-8 animate-spin fixed top-[calc(50%-1rem)] left-[calc(50%-1rem)] z-up -translate-x-1/2 -translate-y-1/2" />
  </div>
</template>

<script setup lang="ts">
import { SvgIcon } from '@skynet/ui'
import { ref, onBeforeMount, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant';
import { usePayment } from './use-payment'
import { T } from './payment-page.i18n'

const payLoading = ref(true);
const { getAliwallexOrderStatus, paypalPayConfirm } = usePayment();
const route = useRoute()
const router = useRouter()

const decodeHelper = (str: string) => {
  const decodeStr = decodeURIComponent(str)
  const url = new URL(decodeStr, window.location.origin)
  url.searchParams.append('success', 'true')
  return url.pathname + url.search
}

const checkOrderStatus = async (id: string, url: string) => {
  const res = await getAliwallexOrderStatus(id);
  if (res && res.success) return location.href = url;
  // else if (res && res.success === false) {
  //   showToast('支付失败')
  //   router.push('/payment' + location.search)
  // }
  setTimeout(() => {
    checkOrderStatus(id, url);
  }, 1000);
}

const check = async () => {
  const { token, PayerID, order_id, from } = router.currentRoute.value.query
  const nextUrl = location.origin + '/payment-success' + location.search;
  // paypal回传
  try {
    if (token && PayerID) {
      const res = await paypalPayConfirm({
        order_id: token?.toString() ?? '',
        payer_id: PayerID?.toString() ?? ''
      })
      if (res) {
        location.href = nextUrl
      }
    } else if (order_id) {
      checkOrderStatus(route.query.order_id as string, nextUrl)
    } else {
      router.push(decodeHelper(from?.toString() || '/'))
    }
  } catch (error) {
    showToast(T.paymentPage.purchaseFailed.string)
    router.push(decodeHelper(from?.toString() || '/'))
  }
}

onBeforeMount(async () => {
  check()
})

onActivated(() => {
  check()
})

</script>

<style scoped></style>