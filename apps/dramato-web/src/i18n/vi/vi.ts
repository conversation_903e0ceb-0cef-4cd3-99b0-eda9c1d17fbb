export const vi = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: '<PERSON><PERSON><PERSON> chọn phổ biến',
    },
  },
  myList: {
    watchHistory: `<PERSON><PERSON><PERSON> sử xem`,
    myList: '<PERSON><PERSON><PERSON> sử',
    seeAll: 'Xem tất cả',
    noData: '<PERSON>hông có kết quả liên quan',
  },
  // 分享页
  share: {
    watchNow: 'Xem ngay',
    episodeDialog: {
      title: 'Tham gia cùng tôi và nhận thêm xu!',
      description: 'Tôi bị cuốn hút bởi bộ phim này!',
      tips: 'Đề xuất',
      button: 'Xem tất cả các bộ phim truyền hình',
      limit24: 'Giới hạn thời gian: 24h',
    },
    toast: {
      takeOffShelf: 'Không có nội dung nào khả dụng',
    },
  },
  'customer-service-center': {
    title: 'Trung tâm dịch vụ khách hàng',
    description: '<PERSON><PERSON> tả',
    'description-placeholder': '<PERSON><PERSON> cấp thông tin chi tiết để giúp chúng tôi hiểu vấn đề',
    'email-placeholder': 'Nhập email của bạn để nhận thông tin cập nhật phản hồi',
    'submit-feedback': 'Gửi',
    'enter-feedback': 'Phản hồi về các vấn đề khác',
    'upload-pictures': 'Tải ảnh lên',
    feedback: 'Phản hồi',
    'problem-type': 'Loại vấn đề',
    'problem-type-placeholder': 'Vui lòng chọn',
    'payment-issue-placeholder': 'Vui lòng cung cấp ID của bạn và biên lai đơn hàng: Google: https://pay.google.com/ (số đơn hàng bắt đầu bằng GPA...) iOS: App Store - Apple ID - Lịch sử mua hàng (Mã đơn hàng)',
    'content-issue-placeholder': 'Vui lòng cung cấp tên phim, số tập và các ngôn ngữ phụ đề bị thiếu.',
    'ads-issue-placeholder': 'Vui lòng cung cấp mẫu thiết bị, quốc gia và cài đặt ngôn ngữ của bạn.',
    'technical-issue-placeholder': 'Vui lòng cung cấp mẫu thiết bị, quốc gia và cài đặt ngôn ngữ của bạn.',
    'other-issue-placeholder': 'Vui lòng cung cấp thêm chi tiết, bao gồm mẫu thiết bị và quốc gia của bạn.',
    'i-want-to-give-feedback': 'Tôi muốn phản hồi ý kiến',
    'are-you-satisfied-with-this-reply': 'Bạn có hài lòng với phản hồi này không?',
    unsatisfied: 'Không hài lòng',
    satisfied: 'Hài lòng',
    myFeedback: 'Phản Hồi Của Tôi',
    'reply-to-feedback': 'Trả lời phản hồi',
    'description-min-length': 'Nhập mô tả vấn đề (ít nhất 6 ký tự).',
    'problem-type-not-choose': 'Vui lòng chọn loại phản hồi.',
    'description-empty': 'Không được để trống.',
  },
  terminate: {
    question: 'Quản lý gói đăng ký của tôi như thế nào?',
    desc: 'Nhấn vào nút bên dưới để vào trang quản lý hợp đồng. Bạn có thể quản lý gói đăng ký của mình bằng cách bật/tắt công tắc hợp đồng:',
    title: 'Quản Lý Hợp Đồng',
    expiration: 'Thời gian hết hạn',
    manageDesc: 'Mối quan hệ hợp đồng',
  },
  toast: {
    'upload-failed': 'Tải lên không thành công, vui lòng tải lại',
    'submit-success': 'Gửi thành công',
    'email-incorrect': 'Định dạng email không đúng',
  },
  not_found: {
    title: 'Trang bạn đang tìm kiếm không tồn tại',
  },
  regulations: {
    howUseData: 'Cách chúng tôi sử dụng dữ liệu của bạn',
    content: `Nếu bạn chọn "Cho phép" trong yêu cầu quyền minh bạch theo dõi ứng dụng, chúng tôi sẽ sử dụng thông tin và dữ liệu của bạn để tối ưu hóa dịch vụ của chúng tôi và cung cấp cho bạn trải nghiệm tốt nhất, nếu không chúng tôi sẽ chỉ thu thập dữ liệu và sẽ không thực hiện bất kỳ hoạt động theo dõi nào.

Nếu bạn đồng ý cho phép DramaWave và các đối tác và nhà cung cấp của chúng tôi sử dụng dữ liệu từ thiết bị của bạn để phân tích cách bạn sử dụng ứng dụng, cá nhân hóa các đề xuất nội dung và cung cấp thêm các dịch vụ liên quan đến thông tin, vui lòng nhấp vào "Đồng ý và Tiếp tục" để tiếp tục.`,
    acceptAll: 'Chấp nhận tất cả',
    dataSettings: 'Cài đặt dữ liệu',
    next: 'Tiếp theo',
    cancel: 'Hủy',
    aboutYourPrivacy: 'Về quyền riêng tư của bạn',
    personalDataName: 'Dữ liệu cá nhân hóa',
    personalDataDescription: 'Chúng tôi sử dụng thông tin này để ghi nhớ các lựa chọn của bạn và cung cấp các đề xuất quảng cáo và nội dung cá nhân hóa, thông báo và nhắc nhở, v.v.',
    advertisementDataName: 'Dữ liệu quảng cáo',
    advertisementDataDescription: 'Cookie, các định danh của thiết bị của bạn hoặc thông tin khác có thể được lưu trữ hoặc truy cập trên thiết bị của bạn cho mục đích quảng cáo trực tuyến. Dữ liệu người dùng cũng có thể được chia sẻ với các đối tác quảng cáo của chúng tôi.',
    behaviorsDataName: 'Dữ liệu hành vi',
    behaviorsDataDescription: 'Dữ liệu liên quan đến phân tích, chẳng hạn như cookie hoặc định danh thiết bị, có thể được lưu trữ để đo lường và phân tích việc sử dụng, hành vi và hiệu suất quảng cáo.',
    privacyAndTerms: 'Chính sách bảo mật và Điều khoản dịch vụ',
    referToPrivacy: 'Vui lòng tham khảo Chính sách bảo mật',
    referToTerms: 'Vui lòng tham khảo Điều khoản dịch vụ',
    agreeAndSave: 'Đồng ý và Lưu',
  },
  coins: {
    earnings: {
      title: 'Thu nhập của tôi',
      total: 'Tổng số kim cương',
      exchange: 'Trao đổi',
      earningsHistory: 'Lịch sử thu nhập',
      exchangeHistory: 'Lịch sử trao đổi',
      noRecords: 'Không có hồ sơ',
      last50earnings: 'Chỉ hiển thị 50 khoản thu nhập gần nhất',
      last50exchanges: 'Chỉ hiển thị 50 hồ sơ trao đổi gần nhất',
      benefits: 'Lợi ích',
      gotIt: 'Hiểu rồi',
    },
    exchange: {
      title: 'Khu vực trao đổi',
      notOpen: 'Chưa được mở, vui lòng đợi thêm',
      receiveBtn: 'Nhận một cách hân hoan',
      earnCoins: 'Kiếm kim cương',
      redeemRequiredXCoins: 'Đổi yêu cầu {X} kim cương',
      exchangeSuccess: 'Đổi thành công',
      cardId: 'Mã thẻ',
      exchangeFailed: 'Đổi không thành công',
      transferFailedInsufficientStock: 'Hết hàng. Vui lòng đến sớm vào ngày mai.',
      gotItBtn: 'Đã hiểu',
      transferFailedRiskControl: 'Tài khoản không bình thường, tạm thời không thể đổi thưởng.',
      tryAgainBtn: 'Thử lại sau',
      transferConfirm: 'Chuyển khoản',
      paypalAccountEmail: 'Email tài khoản PayPal',
      confirmPaypalAccountEmail: 'Xác nhận email tài khoản PayPal',
      transferX: 'Chuyển {X}',
      transferXConfirm: 'Xác nhận chuyển {X}',
      transferToYourAccountX: 'Chuyển vào tài khoản của bạn {X}',
      confirmBtn: 'Xác nhận',
      redeemFailed: 'Rút tiền thất bại',
      redeemFailedContent: 'Đã xảy ra lỗi. Vui lòng thử lại sau.',
      transferSuccessTitle: 'Chúng tôi đang chuyển tiền!',
      transferSuccessContent: ' Tiền của bạn sẽ đến trong vòng 5 phút đến 72 giờ.',
      copySuccess: 'Sao chép thành công',
      enterEmailError: 'Vui lòng nhập địa chỉ email hợp lệ',
      enterEmailValidError: 'Lỗi nhập liệu, vui lòng nhập lại',
      emailNotMatchError: 'Địa chỉ email không khớp, vui lòng nhập lại',
    },
  },
  meal_check_in: {
    sleeping_link: 'Kiếm kim cương khi ngủ',
    rules_title: 'Quy tắc hoạt động',
    benefits_title: 'Benefits',
    rules_1: '1. Mỗi ngày có bốn khung giờ để bạn ghi lại bữa ăn của mình: Bữa sáng: 07:00-10:00; Bữa trưa: 11:00-14:00; Bữa tối: 17:00-20:00; Bữa khuya: 21:00-24:00.',
    rules_2: '2. Nếu bạn bỏ lỡ thời gian ghi điểm, bạn có thể xem một video để nhận lại phần thưởng.',
    rules_3: '3. Phần thưởng được đặt lại mỗi ngày vào lúc nửa đêm, đừng quên nhận phần thưởng nhé.',
  },
  sleep_check_in: {
    earn_link: 'Ghi lại bữa ăn để kiếm',
    benefits_title: 'Benefits',
    rules_1: '1. Mỗi tối từ 7 giờ tối đến 12 giờ khuya, bạn có thể kích hoạt chế độ \'Ngủ để kiếm tiền\'.',
    rules_2: '2. Sau khi kích hoạt, vào ngày hôm sau, sau khi ngủ đủ 8 tiếng, bạn có thể nhận phần thưởng ngủ từ 8 giờ sáng đến 12 giờ trưa.',
    rules_3: '3. Nếu bạn bỏ lỡ thời gian nhận thưởng, bạn có thể xem một video từ 12 giờ trưa đến 7 giờ tối ngày hôm sau để nhận lại phần thưởng.',
  },
  bind_email: {
    title: 'Nhập địa chỉ email của bạn',
    list_1: 'Liên kết địa chỉ email của bạn để nhận phần thưởng là vàng đặc biệt.',
    list_2: 'Nhận thông báo về các sự kiện sắp diễn ra và nhữngnhững ưu đãi đặc biệt.',
    list_3: 'Cập nhật nhanh  nhất về các tập phim mới.',
    email_placeholder: 'Vui lòng nhập địa chỉ email của bạn.',
    verify: 'Xác minh',
    email_error: 'Vui lòng nhập địa chỉ email hợp lệ',
    verify_progress: 'Đang xác minh email',
    verify_success: 'Nhận thành công!',
    re_enter: 'Nhập lại',
    email_repeat: 'Email này từng tham gia sự kiện rồi!',
  },
  infinityScroll: {
    end: 'Kết Thúc',
    loading: 'Đang tải...',
  },
}
