<template>
  <x-profile-page
    class="profile relative flex size-full flex-col gap-y-3 overflow-y-auto bg-black px-3 py-6 text-[#cccacb]"
  >
    <div class="flex items-center justify-between">
      <div class="flex items-center justify-between">
        <div class="size-14 shrink-0 overflow-hidden rounded-[50%]">
          <img
            class="size-full"
            :src="auth_params.user_type !== 0 ? auth_params?.icon || loginInDefaultPicture : defaultProfilePicture"
          >
        </div>
        <div class="ml-2">
          <p class="line-clamp-1 text-lg text-[#fdfbfc]">
            {{ auth_params.name }}
          </p>
          <div class="flex items-center text-xs text-[#797b7d]">
            <p class="line-clamp-1">
              ID {{ auth_params.user_id }}
            </p>
            <SvgIcon
              name="ic-copy"
              class="ml-1 size-3.5 shrink-0"
              @click="handleCopy"
            />
          </div>
        </div>
      </div>
      <div
        v-if="auth_params.user_type === 0"
        class="right-arrow ml-2 shrink-0 pr-4 text-sm"
        @click="() => {
          router.push({ path: '/login', query: { from: 'profile' } })
          h5Track('profile', 'sign_in', 'click')
        }"
      >
        {{ T.profilePage.signIn.ref.value }}
      </div>
    </div>
    <div
      v-if="myWallet.vip_level"
      class="mt-6.5 vipCardAf relative flex w-full flex-col p-3"
      @click="gotoVipCenter"
    >
      <p class="line-clamp-1 text-base font-bold text-[#5a280a]">
        {{ myWallet.membership_product?.title }}
      </p>
      <p class="mb-1 line-clamp-1 text-sm text-[#5a280a]">
        {{ myWallet.membership_product?.description }}
      </p>
      <p class="line-clamp-1 text-[10px] text-[#8f5c3d]">
        {{ menT.memberCenterPage.validUntil.ref.value }}
        {{ myWallet.vip_expire ? dayjs.unix(myWallet.vip_expire).format('YYYY-MM-DD') : 0 }}
      </p>
      <Image
        class="size-22 absolute right--1 top--6 shrink-0"
        :src="vipWebp"
      />
    </div>
    <div
      v-else
      class="vipCardBf flex w-full items-center justify-between p-3"
    >
      <div class="mr-4 flex items-center justify-between">
        <Image
          class="size-14 shrink-0"
          :src="vipWebp"
        />
        <p class="ml-2 line-clamp-3 text-base font-bold leading-tight text-[#5a280a]">
          {{ menT.memberCenterPage.enjoyAllVip.ref.value }}
        </p>
      </div>
      <div
        class="w-21.5 shrink-0 rounded-lg bg-gradient-to-l from-[#d16800] to-[#fd9104] px-3 py-1 text-center text-sm font-bold text-white"
        @click="gotoVipCenter"
      >
        <p>{{ vipParts.part1 }}</p>
        <p>{{ vipParts.part2 }}</p>
      </div>
    </div>
    <div class="flex-col gap-4 rounded-xl bg-[#1d1d1e] px-4 py-3">
      <div
        class="right-arrow mb-4 text-sm"
        @click="() => {
          router.push({ path: '/wallet' })
          h5Track('my_wallet', 'open', 'click', {
            user_id: auth_params.user_id,
            balance
          })
        }"
      >
        {{ T.profilePage.myWalletStr.ref.value }}
      </div>
      <div class="flex items-center justify-between">
        <div class="flex items-center text-lg font-bold text-[#fdfbfc]">
          <SvgIcon
            name="ic-coins-fill"
            class="mr-2 size-8"
          />{{ balance }}
        </div>
        <div
          class="flex h-8 items-center justify-center rounded-lg  bg-[#fc2763] px-3 text-sm text-white"
          @click="() => {
            router.push({ path: '/store' })
          }"
        >
          {{ T.profilePage.topUp.ref.value }}
        </div>
      </div>
    </div>
    <div class="flex-col gap-4 rounded-xl bg-[#1d1d1e]">
      <div
        class="right-arrow p-4"
        style="background-position: right 1rem center"
        @click="() => {
          router.push({ path: '/watch-history', query: { from: 'profile' } })
        }"
      >
        <h3 class="side-title flex flex-col bg-[url('src/h5_modules/images/ic-his.webp')]">
          {{ T.profilePage.watchHistorySubTitle.ref.value }}
        </h3>
        <p
          v-if="!loading && !historyList.length"
          class="mt-1 pl-7 text-sm text-[#797b7d]"
        >
          {{ T.profilePage.noVideos.ref.value }}
        </p>
      </div>
      <div
        v-if="loading"
        class="flex items-center justify-center"
      >
        <Loading
          color="#ff007a"
          size="38px"
        />
      </div>
      <div
        v-if="historyList.length"
        class="relative"
      >
        <!-- 左侧遮罩 -->
        <div
          class="pointer-events-none absolute inset-y-0 left-0 w-5 bg-gradient-to-r from-[#1d1d1e] transition-opacity duration-300"
          :class="showLeftMask ? 'opacity-100' : 'opacity-0'"
        />

        <!-- 右侧遮罩 -->
        <!-- <div
          class="absolute inset-y-0 right-0 w-4 transition-opacity duration-300 pointer-events-none bg-gradient-to-l from-black"
          :class="showRightMask ? 'opacity-100' : 'opacity-0'"></div> -->
        <div
          class="pointer-events-none absolute inset-y-0 right-0 w-5 bg-gradient-to-l from-[#1d1d1e] via-black/40 to-transparent/0 transition-opacity duration-300"
          :class="showRightMask ? 'opacity-100' : 'opacity-0'"
        />

        <!-- 滚动容器 -->
        <div
          ref="scrollContainer"
          class="scrollbar-hide flex overflow-x-auto transition-[padding] duration-300"
          :class="{ 'pr-4': isScrolledToEnd }"
          @scroll="handleScroll"
        >
          <div
            v-for="item in historyList"
            :key="item.id"
            class="w-17.5 mx-1 shrink-0 first:ml-4 last:mr-0"
            @click="() => {
              // if (item.release_round === 1) {
              //   openLimitDialog(item, 'profile')
              // } else {
              router.push('/series/' +item.id +'/' + item.episode?.id + '?from_page=' + '/profile')
              // }
            }"
          >
            <div class="h-23.5 mb-1 w-full rounded-lg">
              <img
                class="size-full rounded-lg object-cover"
                :src="item.cover"
              >
            </div>
            <p class="line-clamp-1 text-xs text-[#797b7d]">
              EP.{{ item?.view_episode }} / EP.{{ item?.episode_count }}
            </p>
          </div>
        </div>
      </div>
      <div
        class="right-arrow p-4"
        style="background-position: right 1rem center"
        @click="() => {
          router.push({ path: '/settings' })
        }"
      >
        <h3 class="side-title bg-[url('src/h5_modules/images/setting.webp')]">
          {{ T.profilePage.settingText.ref.value }}
        </h3>
      </div>
      <div
        class="right-arrow p-4"
        style="background-position: right 1rem center"
        @click="() => {
          router.push({
            path: '/customer-service-center', query: {
              language: currentLocale?.language?.split('-')[0],
              country_code: currentLocale?.language?.split('-')[1]
            }
          })
        }"
      >
        <h3 class="side-title relative bg-[url('src/h5_modules/images/information-circle.webp')]">
          {{ T.profilePage.customerServiceCenter.ref.value }}
          <div
            v-if="hasRedDot"
            class="absolute right-5 top-1/2 size-1.5 translate-y--1/2 rounded-full bg-[#FC2763]"
          />
        </h3>
      </div>
    </div>
  </x-profile-page>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { apiHistoryList, apiWalletMy } from './profile-api'
import { SeriesItemInfo } from './profile.d'
import defaultProfilePicture from 'src/h5_modules/images/def-profile-picture.webp';
import loginInDefaultPicture from 'src/h5_modules/images/payment-logo.webp'
import { useRouter } from 'vue-router'
import { WalletDataInfo } from './profile'
import { T } from './profile-page.i18n'
import { T as menT } from 'src/h5_modules/member-center/member-center.i18n'
import { getUser } from 'src/h5_modules/common/user'
import { Loading, showToast, Image } from 'vant';
import { h5Track } from 'src/lib/h5-track';
import { SvgIcon } from '@skynet/ui'
import 'vant/es/toast/style';
import { openLimitDialog } from '../watch-history/component/drama-card';
import { useLocale } from 'src/h5_modules/i18n/i18n'
import coverDefBg from 'src/h5_modules/images/cover-def.webp'
import vipWebp from 'src/h5_modules/images/vip.webp'
import dayjs from 'dayjs'
import { MyFeedbackApi } from 'src/modules/customer-service-center/feedback/feedback-api';


const { currentLocale } = useLocale()
const scrollContainer = ref(null)
const historyList = ref<SeriesItemInfo[]>([])
const myWallet = ref<WalletDataInfo>({} as WalletDataInfo);
const balance = ref(0)

const router = useRouter();

const scrollState = ref({
  left: 0,
  right: false
})
const vipParts = computed(() => {
  const vipStr = menT.memberCenterPage.becomeVIP.ref.value;
  const lastSpaceIndex = vipStr.lastIndexOf(' ')
  return lastSpaceIndex === -1
    ? { part1: '', part2: '' } // 无空格时的处理
    : {
      part1: vipStr.slice(0, lastSpaceIndex),
      part2: vipStr.slice(lastSpaceIndex + 1)
    }
})
// const data66 = {
//         "cash_balance": 0,
//         "bonus_balance": 0,
//         "vip_level": 0,
//         "vip_expire": 22233333,
//         "vip_cooling_time": 0,
//         "auto_unlock": 1,
//         "membership_product": {
//             "title": "Weekly VIP offer",
//             "description": "first vip offer",
//             "vip_saved": "",
//             "membership_type": "",
//             "watch_ad_times": 0,
//             "ad_units": null,
//             "grace_period_sku_id": "",
//             "is_grace_period": false,
//             "coins_pass_check_in": false,
//             "coins_pass_daily_coins": 0
//         },
//         "vip_used": false,
//         "show_subscript": true,
//         "coupons_count": "",
//         "show_coins": false,
//         "diamond_auto_unlock": 0,
//         "diamond_balance": 0
//     }

const auth_params = getUser();

let rafId = null
const loading = ref(true)
// 计算属性
const showLeftMask = computed(() => scrollState.value.left > 1)
const showRightMask = computed(() => {
  const el = scrollContainer.value
  return el ? (el.scrollWidth - el.clientWidth - scrollState.value.left > 1) : false
})
const isScrolledToEnd = computed(() => scrollState.value.right)


// 滚动处理
const handleScroll = () => {
  if (!rafId) {
    rafId = requestAnimationFrame(() => {
      updateScrollState()
      rafId = null
    })
  }
}

const gotoVipCenter = () => {
  router.push({ path: '/member-center' })
  h5Track('profile_vip', 'center', 'click', {
    vip_status: myWallet.value.vip_level ? 1 : 0
  })
}

// 更新滚动状态
const updateScrollState = () => {
  const el = scrollContainer.value
  if (!el) return
  const newLeft = el.scrollLeft
  const newRight = newLeft + el.clientWidth >= el.scrollWidth - 1

  if (newLeft !== scrollState.value.left || newRight !== scrollState.value.right) {
    scrollState.value = {
      left: newLeft,
      right: newRight
    }
  }
}
const getHistoryList = async () => {
  loading.value = true;
  const res = await apiHistoryList()
  loading.value = false;
  if (res.data && res.data.items) {
    historyList.value = res.data.items
  }
}
const handleCopy = async () => {
  try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(auth_params.user_id)
        // showSuccessToast(T.profilePage.copyMsg.ref.value)
        showToast(
          {
            message: T.profilePage.copyMsg.ref.value,
            style: {
              background: '#434546',
              color: '#fff',
              padding: '0.75rem 1rem',
              minWidth: 0
            },
          })
        return
      }
    } catch (err) {
  }
}
const getWalletMy = async () => {
  const res = await apiWalletMy()
  if (res.data && res.data) {
    myWallet.value = res.data;
    // console.log(res.data.vip_level === 0, 9999)
    balance.value = res.data?.cash_balance + res.data?.bonus_balance
  }
}

const hasRedDot = ref(false)

onMounted(() => {
  const el = scrollContainer.value
  getWalletMy();
  getHistoryList();
  h5Track('profile', 'page', 'show')

  if (el) {
    updateScrollState()
    // 初始化后强制更新一次布局
    setTimeout(updateScrollState, 100)
  }

  void MyFeedbackApi.hasRedDot().then(res => {
    hasRedDot.value = res.data?.fd_has_red_dot ?? false
  })
})
const handleImageError = (event) => {
  event.target.src = coverDefBg
}
</script>

<style scoped>
.profile {
  .side-title {
    font-weight: normal;
    font-size: 0.875rem;
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 1.25rem;
    padding-left: 1.75rem;
  }
}

.vipCardBf {
  height: 5rem;
  background: url(/src/h5_modules/images/vip-card-bf.webp) no-repeat center center/ 100% 5rem;
}

.vipCardAf {
  height: 120px;
  background: url(/src/h5_modules/images/vip-card-af.webp) no-repeat center center/ 100% 120px;
}
</style>
