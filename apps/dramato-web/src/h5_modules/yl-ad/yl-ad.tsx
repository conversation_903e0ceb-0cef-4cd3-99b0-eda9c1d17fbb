import { getUser, setNewUserToFalse } from 'src/h5_modules/common/user'
import { h5AdTrack } from 'src/lib/h5-track'
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStatistics } from 'src/h5_modules/statistics/statistics'
import { reportNow } from '@skynet/client-track'
import { isFacebook } from 'src/lib/ua'
import Cookies from 'js-cookie'
import { boolean } from 'zod'

type AdParams = {
  pid?: string | number
  uid?: string
  u1?: string | number
  u2?: string
  u3?: string
  u4?: string
  u5?: string
  u6?: string
  u7?: string
  u8?: string
  u9?: string
  fbc?: string
  fbp?: string
  ua?: string
}
const adParams = ref<AdParams>({}) // 存储广告参数
const local = parseInt(localStorage.getItem('registerTime') || '0') || 0
const registerTime = ref(local) // 注册时间戳
const { sessionId } = useStatistics()

let series_id: string = ''
let observer

watch(() => registerTime.value, () => {
  if (registerTime.value) {
    localStorage.setItem('registerTime', registerTime.value.toString())
  } else {
    localStorage.removeItem('registerTime')
  }
})
const registerUid = ref() // 注册用户uid
let hasRegisterReported = false
let hasReRegisterReported = false

// 盈量sdk回调，执行回调，代表可以从页面获取广告参数
// const initAdParams = cb => {
//   try {
//     const formId = 'YlBox'
//     const page = series_id ? 'video' : 'home'
//     const params = getUser()
//     window.waitForImage && window.waitForImage(() => {
//       window.ADINPUT && window.ADINPUT(formId)
//       const ylInput = document.querySelector(`#${formId} input[name="ylurl"]`)

//       h5AdTrack(page, '', 'yl_ad_params', { // 自己看yl返回的数据
//         params: ylInput?.value,
//         ylInputName: ylInput?.name,
//         uid: params.user_id,
//       })
//       reportNow()

//       if (ylInput?.value) {
//         const params = new URLSearchParams(ylInput.value)
//         adParams.value = {
//           pid: params.get('pid') || undefined,
//           u1: params.get('u1') || undefined,
//           u2: params.get('u2') || undefined,
//           u3: params.get('u3') || undefined,
//           u4: params.get('u4') || undefined,
//           u5: params.get('u5') || undefined,
//           u6: params.get('u6') || undefined,
//           u7: params.get('u7') || undefined,
//           u8: params.get('u8') || undefined,
//           u9: params.get('u9') || undefined,
//           fbc: params.get('fbc') || undefined,
//           fbp: params.get('fbp') || undefined,
//           ua: params.get('ua') || undefined,
//         }
//         cb()
//       }
//     })
//   } catch (error) {
//     console.error('广告参数初始化失败:', error)
//     const page = series_id ? 'video' : 'home'
//     const params = getUser()
//     h5AdTrack(page, '', 'yl_ad_params_error', { // 自己看yl是否报错
//       error_message: error?.message, // 提取错误信息
//       error_stack: error?.stack,
//       uid: params.user_id,
//     })
//     reportNow()
//     if (localStorage.getItem('is_new_user')) {
//       trackRegisterTrace()
//     }
//   }
// }
// 新用户才上报 + 任何渠道都上报 + 只上报一次
const trackRegisterTrace = () => {
  if (hasRegisterReported) return
  console.log('--上报前-register_trace--')
  const params = getUser()
  const page = series_id ? 'video' : 'home'
  const session_id = page === 'video' ? sessionId.value || '' : ''
  const rt = Date.now() // 注册时间
  h5AdTrack(page, '', 'register_trace', {
    session_id: session_id,
    series_id: series_id,
    is_new_user: 1,
    uid: params.user_id,
    regTime: rt,
    id: '',
    ip: '',
    ...adParams.value,
  })
  reportNow()
  console.log('--上报成功-register_trace-', params.user_id)
  console.log('----adParams----', adParams.value)
  hasRegisterReported = true
  // 删除cookies auth_params is_new_user字段
  setNewUserToFalse()

  console.log('--注册时间--', rt)
  registerUid.value = params.user_id
  registerTime.value = rt
}
const hasParameter = (url: string, paramName: string) => {
  const query = url.split('?')[1] || ''
  return new URLSearchParams(query).has(paramName)
}
// 广告进来的 && 新老用户都上报 && 刷新还可以上报
const trackReRegisterTrace = (wasNewUser: boolean) => {
  if (hasReRegisterReported) return
  console.log('--上报前-re_register_trace--')
  const params = getUser()
  const page = series_id ? 'video' : 'home'
  const session_id = page === 'video' ? sessionId.value || '' : ''
  const rt = Date.now() // 注册时间
  h5AdTrack(page, '', 're_register_trace', {
    session_id: session_id,
    series_id: series_id,
    is_new_user: wasNewUser ? 1 : 0,
    uid: params.user_id,
    regTime: rt,
    id: '',
    ip: '',
    ...adParams.value,
  })
  reportNow()
  hasReRegisterReported = true
  console.log('--上报成功-re_register_trace--', params.user_id)
}
const initAdParams = (wasNewUser: boolean) => {
  if (document.getElementById('yllpid')) {
    const url = new URL(document.getElementById('yllpid')?.src)
    const params = new URLSearchParams(url.search)
    const page = series_id ? 'video' : 'home'
    const fbc = Cookies.get('_fbc')
    const fbp = Cookies.get('_fbp')
    const pid = params.get('pid') ? Number(params.get('pid')) : undefined
    const u1 = params.get('u1') ? Number(params.get('u1')) : undefined
    console.log('----fbc--fbp------', fbc, fbp)
    adParams.value = {
      pid: pid,
      u1: u1,
      u2: params.get('u2') || undefined,
      u3: params.get('u3') || undefined,
      u4: params.get('u4') || undefined,
      u5: params.get('u5') || undefined,
      u6: params.get('u6') || undefined,
      u7: params.get('u7') || undefined,
      u8: params.get('u8') || undefined,
      u9: params.get('u9') || undefined,
      fbc: fbc || undefined,
      fbp: fbp || undefined,
      ua: params.get('ua') || undefined,
    }
    console.log('--------adParams.value-------', adParams.value)
    if (wasNewUser) trackRegisterTrace()
    localStorage.removeItem('is_wait_ad')
    trackReRegisterTrace(wasNewUser)
    h5AdTrack(page, '', 'yl_params_init', {
      params: adParams?.value,
    })
    reportNow()
    observer && observer.disconnect() // 找到后立即停止监听
  }
}
export const useYlAd = () => {
  if (window.location.pathname === '/landingpage') {
    return
  }
  const route = useRoute()
  series_id = route.params?.series_id || route.query?.af_dp || ''
  const page = series_id ? 'video' : 'home'

  onMounted(() => {
    const isYlQ = hasParameter(location.href, 'af_c_id')
    const wasNewUser = getUser()?.is_new_user
    console.log('--wasNewUser--isYlQ-', wasNewUser, isYlQ, wasNewUser)
    console.log('--_fbc--', Cookies.get('_fbc'))

    if (window.ADINPUT || isYlQ) { // 盈量渠道，增加链接参数判断
      // localStorage.setItem('is_wait_ad', 'true') // 等广告信息时/series 不要执行else操作
      // initAdParams(() => {
      //   if (wasNewUser) trackRegisterTrace()
      //   trackReRegisterTrace(wasNewUser)
      //   localStorage.removeItem('is_wait_ad')
      // })
      localStorage.setItem('is_wait_ad', 'true')
      if (Cookies.get('_fbc')) {
        initAdParams(wasNewUser)
      } else {
        observer = new MutationObserver(() => {
          initAdParams(wasNewUser)
        })
        observer.observe(document.body, {
          childList: true, // 只监听直接子节点变化
          subtree: false, // 关键优化！不监听深层DOM
        })
      }
    } else if (wasNewUser) {
      const params = getUser()
      h5AdTrack(page, '', 'is_new_user', { // 非yl渠道
        uid: params.user_id,
      })
      reportNow()
      if (!localStorage.getItem('is_wait_ad')) {
        trackRegisterTrace()
      }
    }
  })

  onUnmounted(() => {
    observer && observer.disconnect()
  })

  return {
    adParams,
    registerTime,
    registerUid,
  }
}
