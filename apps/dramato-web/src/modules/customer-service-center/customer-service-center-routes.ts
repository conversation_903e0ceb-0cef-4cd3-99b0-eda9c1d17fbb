import { r } from '@skynet/shared'
import ShareLayout from 'src/layouts/share-layout'

export const customerServiceCenterRoutes = [
  r('customer-service-center', '', () => import('src/layouts/share-layout'), [
    r('', 'Customer Service Center', () => import('./customer-service-center-page')),
    r('feedback', 'Feedback', () => import('./feedback/feedback-page')),
    r('my-feedback', 'My feedback', () => import('./feedback/my-feedback-page')),
    r('feedback-detail', 'My Feedback', () => import('./feedback/feedback-details-page')),
    r('terminate', 'Contract Management', () => import('./terminate')),
  ]),
]
