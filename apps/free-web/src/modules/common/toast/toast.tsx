import { createComponent, fn, required } from '@skynet/shared'
import { useToastStore } from './use-toast-store'
import { computed, onMounted, ref, VNodeChild, watchEffect } from 'vue'
import { ToastType } from './toast-types'
import { Fn, TransitionPresets, useTransition } from '@vueuse/core'
import { MergeClass } from '@skynet/ui'
import router from 'src/router'

export interface ToastProps {
  toastId: string
  content?: VNodeChild | null
  type?: ToastType
  autoHide?: boolean | number
  /**
   * 由于 toast 的出现和消失伴随动画，所以 visible 为 false 时，不代表 toast 一定不被用户看见
   */
  visible?: boolean
  class?: string
}

interface ToastOptions {
  props: ToastProps
  emits: {
    destroy: Fn
  }
}
export const Toast = createComponent<ToastOptions>({
  props: {
    toastId: required,
    content: null,
    type: 'success',
    autoHide: 2000,
    visible: true,
    class: '',
  },
  emits: {
    destroy: fn,
  },
}, (props, { emit }) => {
  const { addToHideList, removeFromHideList } = useToastStore()
  const duration = computed(() => typeof props.autoHide === 'number' ? props.autoHide : 5000)
  onMounted(() => {
    watchEffect(() => {
      const time = new Date().getTime() + duration.value
      props.autoHide
        ? addToHideList(props.toastId, time)
        : removeFromHideList(props.toastId)
    })
  })
  const toast = ref<HTMLElement | null>(null)
  const y = ref(300)
  const targetY = useTransition(y, {
    duration: 300,
    transition: TransitionPresets.easeOutCubic,
    onFinished: () => {
      if (props.visible === false) {
        emit('destroy')
        setTimeout(() => {
          // 兼容oppo不消失出现残影，但是点击屏幕可以消失
          // 所以模拟触发一次点击事件
          document.body?.dispatchEvent(new Event('click'))
          document.body?.click()
        }, 1000)
      }
    },
  })
  watchEffect(() => {
    y.value = props.visible ? 0 : -300
  })

  const showNavigation = computed(() => router.currentRoute.value.query.showNavigation !== 'false')

  return () => (
    <MergeClass
      ref={toast}
      baseClass={`flex shrink-0 transform px-4 py-3 bg-[var(--surface-5)] rounded-[200px] shadow max-w-90vw ${props.class}`}
      style={{
        transform: `translateY(${targetY.value}%)`,
        opacity: (100 - Math.abs(targetY.value) / 3) / 100,
        'margin-top': showNavigation.value ? '0' : '0',
      }}
    >
      <div class="text-[var(--text-5)] text-center text-sm leading-[17px]">{props.content}</div>
    </MergeClass>
  )
})

export const showToast = (content: ToastProps['content'], type?: ToastProps['type'], toastProps?: Omit<ToastProps, 'content' | 'type' | 'toastId'>) => {
  const { addToast, updateToast } = useToastStore()

  const id = addToast({ content, type, ...toastProps })
  const hideToast = () => updateToast(id, { visible: false })
  return hideToast
}
