import { createComponent, mc } from '@skynet/shared'
import { useHomePageStore } from '../use-home-page-store'
import ThreeColumnDramaCard from './three-column-drama-card'
import { storeToRefs } from 'pinia'

export const ThreeColumnRecommend = createComponent(null, props => {
  const { threeColumnRecommend } = storeToRefs(useHomePageStore())
  return () => threeColumnRecommend.value && threeColumnRecommend.value.items && threeColumnRecommend.value.items?.length > 0
    ? (
        <x-three-column-recommend class="mt-4 block">
          {/* { threeColumnRecommend.value.show_title && <x-title class="block text-4.5 text-white h-12.5 leading-12.5 font-bold">{ threeColumnRecommend.value.module_name}</x-title>} */}
          <x-three-column-recommend-content class={mc('grid grid-cols-3  gap-2', threeColumnRecommend.value.items.length <= 6 ? 'grid-rows-2' : 'grid-rows-3')}>
            { threeColumnRecommend.value.items?.map((drama, index) => <ThreeColumnDramaCard key={`RecommendationDramaCard-${drama.key}-${index}`} drama={drama} />)}
          </x-three-column-recommend-content>
        </x-three-column-recommend>
      )
    : null
})

export default ThreeColumnRecommend
