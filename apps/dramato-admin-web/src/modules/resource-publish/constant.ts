// 可删除状态
export const deleteAbleStatus = [-1, 0, 1, 2]
// 可编辑状态
export const editableStatus = [-1, 0, 2]
// 可撤销状态
export const cancelAbleStatus = [1]
// 可审核状态
export const auditableStatus = [1]
// 审核成功状态
export const auditSuccess = [3]

// 状态下拉框
export const statusOptions = [{
  value: -1,
  label: '全部',
}, {
  value: 0,
  label: '草稿',
}, {
  value: 1,
  label: '待审核',
}, {
  value: 2,
  label: '审核不通过',
}, {
  value: 3,
  label: '审核通过',
}]

// 状态下拉框
export const netdiskStatusOptions = [{
  value: 0,
  label: '全部',
}, {
  value: 1,
  label: '检测未通过',
}, {
  value: 2,
  label: '检测完成',
}, {
  value: 3,
  label: '已转存',
}, {
  value: 4,
  label: '已开启同步',
}, {
  value: 5,
  label: '同步完成',
}, {
  value: 6,
  label: '同步失败',
}]

export const resourceTypeOptions = [
  {
    value: 1,
    label: '翻译剧非首发',
  },
  {
    value: 6,
    label: '翻译剧首发',
  },
  {
    value: 2,
    label: '本土首发',
  },
  {
    value: 3,
    label: '本土对投',
  },
  {
    value: 4,
    label: '本土二轮采买',
  },
  {
    value: 5,
    label: '本土自制',
  },
]

export const authPlatforms = [
  {
    value: 1,
    label: 'DramaWave',
  },
  {
    value: 2,
    label: 'Moboshort',
  },
  {
    value: 3,
    label: 'Freereel',
  },
  {
    value: 4,
    label: 'Dramareel',
  },
  {
    value: 5,
    label: 'Linky',
  },
]

export const authLanguages = [
  {
    value: 1,
    label: '简体中文以外的所有',
  },
  {
    value: 2,
    label: '简体中文、繁体中文以外的所有',
  },
  {
    value: 4,
    label: '全球',
  },
  {
    value: 3,
    label: '其他',
  },
]

export const authAreas = [
  {
    value: 1,
    label: '中国大陆以外所有',
  },
  {
    value: 2,
    label: '中国大陆及港澳台以外的所有',
  },
  {
    value: 3,
    label: '单个区域',
  },
  {
    value: 4,
    label: '全球',
  },
]

export const sharingModels = [
  {
    value: 1,
    label: '无',
  },
  {
    value: 2,
    label: '流水奖励',
  },
  {
    value: 3,
    label: '利润分成-50%',
  },
  {
    value: 3,
    label: '利润分层-其他',
  },
]

export const contentRates = [
  {
    value: 'S',
    label: 'S',
  },
  {
    value: 'A',
    label: 'A',
  },
  {
    value: 'B',
    label: 'B',
  },
  {
    value: 'C',
    label: 'C',
  },
]

export const releaseRounds = [
  {
    value: 1,
    label: '首发',
  },
  {
    value: 2,
    label: '二轮',
  },
]

export const vipTypes = [
  {
    value: 0,
    label: '非VIP剧',
  },
  {
    value: 1,
    label: 'VIP非连载剧',
  },
  {
    value: 2,
    label: 'VIP连载剧',
  },
]

export const publishStatusOptions = [
  {
    value: 0,
    label: '待发布',
  },
  {
    value: 1,
    label: '发布中',
  },
  {
    value: 2,
    label: '发布失败',
  },
  {
    value: 3,
    label: '发布完成',
  },
]

export const disabledPublishStatus = [1]

export const taskStatusOptions = [
  {
    value: 0,
    label: '全部',
  },
  {
    value: 1,
    label: '待认领',
  },
  {
    value: 2,
    label: '已认领',
  },
]
// 新驳回
export const auditStatusShow = [
  {
    value: 0,
    label: '待审核',
  },
  {
    value: 1,
    label: '待确认',
  },
  {
    value: 2,
    label: '审核通过',
  },
  {
    value: 99,
    label: '审核驳回',
  },
]

// 新驳回
export const auditStatusForQuery = [
  {
    value: 0,
    label: '待审核',
  },
  {
    value: 1,
    label: '待确认',
  },
  {
    value: 2,
    label: '审核通过',
  },
]

export const maritalStatusOptions = [
  {
    value: 1,
    label: '未婚',
  },
  {
    value: 2,
    label: '已婚',
  },
  {
    value: 3,
    label: '离异',
  },
  {
    value: 4,
    label: '单亲',
  },
]

export const fertilityStatusOptions = [
  {
    value: 1,
    label: '丁克',
  },
  {
    value: 2,
    label: '未育',
  },
  {
    value: 3,
    label: '已育',
  },
]

export const ageOptions = [
  {
    value: 1,
    label: '12-17岁',
  },
  {
    value: 2,
    label: '18-24岁',
  },
  {
    value: 3,
    label: '25-34岁',
  },
  {
    value: 4,
    label: '35-44岁',
  },
  {
    value: 5,
    label: '45岁+',
  },
]

export const freeStatusOptions = [
  {
    value: 1,
    label: '可免费',
  },
  {
    value: 2,
    label: '不可免费',
  },
  {
    value: 3,
    label: '不可收费',
  },
]

export const deployAdvanceOptions = [
  {
    value: 1,
    label: '是',
  },
  {
    value: 2,
    label: '否',
  },
]

export const defaultTemplate = [
  {
    id: 1,
    category: '画面',
    criteria: '画面-稳定流畅',
    score: 0,
    desc: '整体画面无抖动、黑影、模糊为佳',
    weight: 20,
  },
  {
    id: 2,
    category: '画面',
    criteria: '画面-色彩准确清晰',
    score: 0,
    desc: '整体画面色调一致，无偏色',
    weight: 10,
  },
  {
    id: 3,
    category: '画面',
    criteria: '画面-字幕无残留',
    score: 0,
    desc: '优到差排序：无视觉残留>黑影>字幕残留>人物扭曲',
    weight: 20,
  },
  {
    id: 4,
    category: '声音',
    criteria: '声音-完整度',
    score: 0,
    desc: '对白、bgm、特效音完备一致',
    weight: 15,
  },
  {
    id: 5,
    category: '声音',
    criteria: '声音-声画同步',
    score: 0,
    desc: '演员口型和声音进度一致',
    weight: 10,
  },
  {
    id: 6,
    category: '声音',
    criteria: '声音-双声道清晰',
    score: 0,
    desc: '左右声道均有声音，无缺音、电音、模糊等',
    weight: 15,
  },
  {
    id: 7,
    category: '字幕',
    criteria: '字幕-信息完整',
    score: 0,
    desc: '细节字幕（短信、弹幕等）是否加全',
    weight: 10,
  },
]
export const LABEL_OPTIONS = [{
  value: 1,
  label: '受众',
  level: 1,
}, {
  value: 2,
  label: '类型',
  level: 2,
}, {
  value: 3,
  label: '情节',
  level: 3,
}, {
  value: 4,
  label: '角色',
  level: 3,
}, {
  value: 5,
  label: '背景',
  level: 3,
}]
