declare namespace M {
  namespace SpendBoardMaterial {
    interface params {
      series_resource_id?: string // 资源id或者资源名（模糊查询）
      series_key?: string // 短剧id或者短剧名
      language?: string // 短剧默认语言
      listing_status?: number // -1 已下架 1 未上架 2 待上架 3 已上架 4 待下架' 0 为默认
      task_time_start?: number// 任务开始时间
      task_time_end?: number// 任务结束时间
      content_rating?: string // 商务评级
      editor_rating?: string // 剪辑师评级
      resource_round?: number// 发行轮次 1 首发 2二轮
      release_status?: number // 发行状态 0 关闭 1 开启
      recommend?: number // 是否推荐 1 推荐 2 不推荐 0 全部
      only_today?: boolean // 是否是今天
      page_index?: number
      page_size?: number
    }

    interface Special {
      name: string// 优化师名字
      spend: number// 消耗
      show_cnt: number// 广告曝光
      click_cnt: number// 广告点击
      cpm: number // 广告CPM
      ctr: number // 广告CTR 转化成百分比
      cpc: number // 广告cpc
      cvr: number // 广告CVR 转化成百分比
      install_cnt: number // 安装数量
      add_to_cart_uv: number // 加购人数
      pay_uv: number// 付费人数
      cpi: number // CPI
      arpu: number// ARPU
      pay_rate: number // 付费率 转化成百分比
      cpa: number // CPA
      arppu: number // arppu
      day0_revenue: number // day0回收
      day0_roas: number // day0 roas  转化成百分比
    }

    interface VideoCutter {
      user_name: string // 剪辑师名字
      spend: number // 消耗
      day0_revenue: number // 回收
      roi: number // roi 转化成百分比
      source_cnt: number // 跑量素材数

      show_cnt: number// 广告曝光
      click_cnt: number// 广告点击
      cpm: number // 广告CPM
      ctr: number // 广告CTR 转化成百分比
      cpc: number // 广告cpc
      cvr: number // 广告CVR 转化成百分比
      install_cnt: number // 安装数量
      add_to_cart_uv: number // 加购人数
      pay_uv: number// 付费人数
      cpi: number // CPI
      arpu: number// ARPU
      pay_rate: number // 付费率 转化成百分比
      cpa: number // CPA
      arppu: number // arppu
      day0_roas: number // day0 roas  转化成百分比
    }

    interface Series {
      default_language_code: string
      series_key: string
      title: string
      cover: string
      recommend: number // 是否推荐 1 推荐 2 不推荐 0 默认
      is_today: boolean // 是否是今天
      resource_round: number// 发行轮次 1 首发 2二轮
      // content_rating: string // 商务评级
      // editor_rating: string // 剪辑师评级
      // pay_rate: number // 站内付费率
      // view_rate: number // 站内观看率
      // dataeye_rank: number // dataeye短剧排行
      labels: string[]
      deploy_status: number // 1 待上架 2 已上架 3 素材中 4 投放中
      listing_status: number // -1 已下架 1 未上架 2 待上架 3 已上架 4 待下架
      listing_time: number // 上架时间/定时上架时间
      count: number // 剧集总数
      upload_count: number // 上传集数
      unlocked_episodes: number // 解锁集数
      deploy_task_time: number // 剧单日期
      real_money: number// 累计花费
      roi: number // roi
      recycle_money: number // 累计回收
      mat_url: string // 定向素材表，如果有就显示下载没有就不显示
      resource_type: number// 本土 翻译 1 本土 2 翻译
      // play_video_num_rate: number // 站内播放率
      tips: string[]
      reasons: string[]
      inner_tips: string[] // 由后端拼装数据，原有相关数据删除
      editor_rating: string // 剪辑师评级
      deploy_task_doc: string // 剧单文档
      specialist: Special[]
      videocutter: VideoCutter[]
      showDataeye?: boolean
      source_cnt: number // 已完成素材数
      delivery_cnt: number// 投放次数
      task_get_status: number // 1 已领取 2 未领取
      audio_type: number // 0 无配音 1 配音剧 2 ai配音剧
      // play_uv: number // 站内播放UV
    }

    interface ListItem {
      series_resource_id: number
      langs: Array<{
        lang: string
        audio_type: number
      }> //
      recommends: {
        lang: string
        audio_type: number
        deploy_task_time: number
        team: 1 | 2 // 1 昆仑 2 盈亮
        comment: string // 备注
      }[]
      title: string
      resource_round: number // 发行轮次 1 首发 2二轮
      resource_type: number// 本土 翻译 1 本土 2 翻译
      tips: string[]
      recommend: number // 是否推荐 1 推荐 2 不推荐 0 默认
      is_today: boolean // 是否是今天
      deploy_task_time: number // 剧单日期
      series: Series[]
      can_task: number // 0 不可点击 1 可点击
    }

    interface ListResponse {
      list: ListItem[]
      total_count: number
    }

    interface TaskListResponse {
      totle: number
      tasklist: TaskItem[]
    }

    interface TaskRequest {
      start_time: number // 默认为0
      end_time: number// 默认为0
      series_resource_id: string // 资源id或者title
      team: 0 | 1 | 2 // 1 昆仑 2 盈亮
      page_index: number
      page_size: number
    }

    interface TaskItem {
      series_resource_id: number
      deploy_task_time: number
      title: string
      lang: string
      team_id: 1 | 2// 1 昆仑 2 盈亮
      comment: string
      task_get_status: 1 | 2 // 任务领取状态 1 已领取 2
      task_owner: string// 任务领取人
      audio_type: number // 0 无配音 1 配音剧 2 ai配音剧
    }

    interface SeriesRequest {
      series_resource_title: string
      source_type: 0 | 1 | 2 // 1首发剧， 2 爬坡合格二轮
      page_info?: {
        page_index: number
        page_size: number
      }
    }

    interface SeriesResponse {
      total: number
      list: SeriesItem[]
    }

    interface SeriesItem {
      series_key: string
      updated: number
      series_resource_id: number // 资源ID
      series_resource_title: string // 资源名
      language_version: string // 语种
      source_type: 1 | 2 // 1首发剧， 2 爬坡合格二轮
      listing_time: number // 上架时间
    }
  }
}
