import { ref } from 'vue'

export const usePrefersColorScheme = () => {
  const isDarkMode = ref(window.matchMedia('(prefers-color-scheme: dark)').matches)
  function setHtmlClass(value: boolean) {
    document.documentElement.classList.toggle('dark', value)
  }
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
    isDarkMode.value = e.matches
    setHtmlClass(e.matches)
  })
  setHtmlClass(isDarkMode.value)

  return isDarkMode
}
