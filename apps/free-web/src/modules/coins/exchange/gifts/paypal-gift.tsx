import { createComponent, mc } from '@skynet/shared'
import type { GiftType } from '../exchange'
import { coins, dialogBg, paypalDialog, paypalCard } from '../../images/images'
import { Progress } from 'src/modules/common/progress/progress'
import { Button } from '@skynet/ui'
import { Fn } from '@vueuse/core'
import { computed, ref, watch, watchEffect } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { track } from 'src/lib/track'
import { openDialog } from 'src/modules/common/dialog'
import { useCoinsStore } from '../../coins-store'
type GiftOptions = {
  props: {
    gift?: GiftType
  }
}
export const PaypalGift = createComponent<GiftOptions>({
  props: {
    gift: {
      id: '', // gift_id
      name: '', // gift_name
      can_exchange: true, // 是否能兑换
      image_url: '', // 图片地址
      amount: '', // 礼品金额
      user_coins: 0, // 用户金币
      gift_coins: 0, // 礼品金币
      ratio: 0, // 兑换比例
      button_tips: '',
      type: 'paypal',
    },
  },
}, props => {
  const { t } = useLocale()
  const tipsRef = ref<HTMLDivElement>()
  const email = ref('')
  const confirmEmail = ref('')
  const emailError = ref('')
  const confirmEmailError = ref('')
  let closeConfirmEmailDialog: Fn
  let closeConfirmDialog: Fn

  const { getExchangeDetails } = useCoinsStore()

  const reachLimit = computed(() => props.gift?.user_coins >= props.gift?.gift_coins)

  watchEffect(() => {
    if (!reachLimit.value) {
      track('exchangezone', 'earncoins_button', 'show', {
        gift_id: props.gift?.id,
      })
    }
  })

  const commonTitle = () => (
    <x-title class="block w-full h-21 relative">
      <img src={paypalDialog} class="absolute w-[calc(33.33%+10px)] h-auto object-cover left-1/2 -translate-x-1/2 top-1/2" />
    </x-title>
  )

  const openTransferFailByInsufficientStock = () => { // 库存不足导致失败
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6">
          <x-body-main class="flex mx-4 flex-col">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center truncate">{t('coins.exchange.exchangeFailed')}</x-title>
            <x-content class="w-full text-[var(--text-1)] text-center mt-3 mb-5">
              {t('coins.exchange.transferFailedInsufficientStock')}
            </x-content>
            <Button class="w-[calc(100%-2.5rem)] mx-7 h-11 no-tap-color text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={() => closeDialog()}>{t('coins.exchange.gotItBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }
  const openTransferFailByRiskControl = () => { // 触发风控导致兑换失败
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6">
          <x-body-main class="flex mx-4 flex-col">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center truncate">{t('coins.exchange.exchangeFailed')}</x-title>
            <x-content class="w-full text-[var(--text-1)] text-center mt-3 mb-5">
              {t('coins.exchange.transferFailedRiskControl')}
            </x-content>
            <Button class="w-[calc(100%-2.5rem)] mx-7 h-11 no-tap-color text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={() => closeDialog()}>{t('coins.exchange.tryAgainBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }
  const openTransferSuccessDialog = () => { // 提现成功
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6">
          <x-body-main class="flex mx-4 flex-col">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center">{t('coins.exchange.transferSuccessTitle')}</x-title>
            <x-content class="w-full text-[var(--text-1)] font-medium text-sm text-center mt-3 mb-8">
              {t('coins.exchange.transferSuccessContentX', { X: email.value })}
            </x-content>
            <Button class="w-[calc(100%-2.5rem)] mx-7 h-11 no-tap-color text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={() => {
              getExchangeDetails()
              closeDialog()
            }}
            >{t('coins.exchange.gotItBtn')}
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openConfirmDialog = () => { // 确认兑换弹窗
    closeConfirmEmailDialog?.()
    closeConfirmDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      mainClass: 'rounded-sm',
      title: commonTitle,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6 rounded-xl">
          <x-body-main class="flex mx-4 flex-col">
            <x-title class="w-full text-lg font-bold text-[var(--text-1)] text-center line-clamp-2">{t('coins.exchange.transferXConfirm', { X: props.gift?.amount })}</x-title>
            <x-content class="w-full text-[var(--text-1)] text-sm text-center mt-2 mb-6">
              {t('coins.exchange.transferToYourAccountX', { X: email.value })}
            </x-content>
            <Button class="w-[calc(100%-2.5rem)] mx-7 h-11 no-tap-color text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={redeemHandler}>{t('coins.exchange.confirmBtn')}</Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const redeemHandler = () => {
    closeConfirmDialog?.()
    openTransferSuccessDialog()
  }

  const handleClick = () => {
    if (props.gift?.can_exchange) {
      if (reachLimit.value) {
        openEmailConfirmDialog()
        // openConfirmDialog()
      } else {
        track('exchangezone', 'earncoins_button', 'click')
        window.location.href = 'freereels://freereels.app/rewards_task?enter_from=exchange_page'
      }
    }
  }

  const flag = ref(false)
  watch(() => [tipsRef.value, flag.value], () => {
    flag.value = false
    if (tipsRef.value) {
      const lineHeight = parseFloat(getComputedStyle(tipsRef.value).lineHeight)
      const fontSize = parseFloat(getComputedStyle(tipsRef.value).fontSize)
      if (fontSize <= 8) {
        return
      }
      const maxHeight = lineHeight * 2
      if (tipsRef.value.scrollHeight > maxHeight) {
        tipsRef.value.style.fontSize = `${fontSize - 2 <= 8 ? 8 : fontSize - 2}px`
        flag.value = true
      }
    }
  })

  const openEmailConfirmDialog = () => {
    const handleEmailBlur = () => {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      if (!email.value) {
        emailError.value = t('coins.exchange.enterEmailError')
      } else if (!emailRegex.test(email.value)) {
        emailError.value = t('coins.exchange.enterEmailValidError')
      } else {
        emailError.value = ''
      }
    }
    const handleConfirmEmailBlur = () => {
      if (confirmEmail.value !== email.value) {
        confirmEmailError.value = t('coins.exchange.emailNotMatchError')
      } else {
        confirmEmailError.value = ''
      }
    }

    closeConfirmEmailDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      title: commonTitle,
      body: () => (
        <x-exchange-body style={{ backgroundImage: `url(${dialogBg})`, backgroundSize: '100% 100%' }} class="mb-4 flex flex-col bg-no-repeat pt-12 pb-6">
          <x-body-main class="flex mx-4 flex-col gap-y-4">
            <x-title class="text-lg font-bold text-[var(--text-1)] text-center truncate">{t('coins.exchange.transferConfirm')}</x-title>
            <x-form>
              <x-form-item class="block text-sm">
                <x-form-label class="font-bold">{t('coins.exchange.paypalAccountEmail')}</x-form-label>
                <input
                  type="email"
                  value={email.value}
                  onBlur={handleEmailBlur}
                  onInput={(e: Event) => {
                    email.value = (e.target as HTMLInputElement).value
                    emailError.value = ''
                  }}
                  placeholder={t('coins.exchange.paypalAccountEmail')}
                  class="dramato-input py-3 pl-0 mt-2 bg-transparent"
                />
                <x-line class="block border-0 w-full h-[1px] border-[var(--line-2)] border-solid border-t border-top-1"> </x-line>
                {emailError.value && <x-error class="w-full line-clamp-2 text-xs pt-1 text-[var(--red-6)]">{emailError.value}</x-error>}
              </x-form-item>
              <x-form-item class="block mt-4 text-sm">
                <x-form-label class="font-bold">{t('coins.exchange.confirmPaypalAccountEmail')}</x-form-label>
                <input
                  type="email"
                  value={confirmEmail.value}
                  onBlur={handleConfirmEmailBlur}
                  onInput={(e: Event) => {
                    confirmEmail.value = (e.target as HTMLInputElement).value
                    confirmEmailError.value = ''
                  }}
                  placeholder={t('coins.exchange.confirmPaypalAccountEmail')}
                  class="dramato-input py-3 pl-0 mt-2 bg-transparent"
                />
                <x-line class="block border-0 w-full h-[1px] border-[var(--line-2)] border-solid border-t border-top-1"> </x-line>
                {confirmEmailError.value && <x-error class="w-full line-clamp-2 text-xs pt-[4px] text-[var(--red-6)]">{confirmEmailError.value}</x-error>}
              </x-form-item>
            </x-form>
            <Button disabled={
              !email.value
              || !confirmEmail.value
              || !!emailError.value
              || !!confirmEmailError.value
            } class="w-[calc(100%-2.5rem)] mx-7 h-11 mt-4 no-tap-color text-base font-bold text-[var(--text-5)] bg-[var(--brand-6)] rounded-lg border-none" onClick={openConfirmDialog}
            >
              {t('coins.exchange.transferX', { X: props.gift?.amount })}
            </Button>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  return () => (
    <x-gift-item class={mc('flex gap-x-3 p-3', props.gift?.can_exchange ? '' : 'opacity-20')}>
      <x-gift-item-left class="flex flex-col gap-1">
        <x-card class="px-1 py-3 size-11 box-border border-solid border-[1px] border-[var(--line-1)] rounded-lg">
          <img src={props.gift?.image_url ?? paypalCard} class="w-full h-auto object-cover" />
        </x-card>
        <x-price class="block text-center text-xs font-bold text-[var(--text-2)]">{props.gift?.amount}</x-price>
      </x-gift-item-left>
      <x-gift-item-center class="flex-1 flex flex-col gap-1 truncate">
        <x-gift-title class="w-full text-sm text-[var(--text-2)] truncate">{props.gift.name}</x-gift-title>
        {
          props.gift?.can_exchange && (
            <x-gift-coins class="text-sm flex items-center">
              <img src={coins} class="w-4 h-4 object-cover mr-[2px]" />
              <x-gift-amount class="text-[var(--brand-6)]">{props.gift.user_coins}</x-gift-amount>
              <x-separator class="px-[2px] text-[var(--text-4)]">/</x-separator>
              <x-gift-remains class="text-[var(--text-1)]">{props.gift.gift_coins}</x-gift-remains>
            </x-gift-coins>
          )
        }
        {
          props.gift?.can_exchange && (
            <x-gift-progress>
              <Progress class="mt-1" value={props.gift.ratio} max={1} />
            </x-gift-progress>
          )
        }
      </x-gift-item-center>
      <x-gift-item-right class={mc('flex flex-col w-auto shrink-0 grow-0 items-center w-21', props.gift?.button_tips ? 'justify-start gap-y-1' : 'justify-center')}>
        <Button class="min-h-8 border-none px-1 py-[2px] bg-[var(--brand-6)] text-[var(--text-5)] text-sm w-full rounded-lg" onClick={handleClick}>
          <p class="w-full h-auto line-clamp-2 break-words text-wrap text-center leading-4">
            {reachLimit.value ? t('coins.earnings.exchange') : t('coins.exchange.earnCoins')}
          </p>
        </Button>
        {props.gift?.button_tips && <x-tips class="w-full h-6 text-[var(--text-2)] text-center text-[10px] leading-3 line-clamp-2 break-words" ref={tipsRef}>{props.gift?.button_tips}</x-tips>}
      </x-gift-item-right>
    </x-gift-item>
  )
})
