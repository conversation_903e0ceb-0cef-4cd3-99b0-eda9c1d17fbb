/**
 * Dynamically load a JS or CSS file and ensure it is not loaded multiple times.
 * @param url - The URL of the JS or CSS file.
 * @param type - The type of the file, either "js" or "css".
 * @returns A promise that resolves when the file is loaded or already exists.
 *
 *Usage examples:
  loadResource('https://cdn.example.com/library.js', 'js')
    .then(console.log)
    .catch(console.error)

  loadResource('https://cdn.example.com/styles.css', 'css')
    .then(console.log)
    .catch(console.error)
 */
export function loadResource(url: string, type: 'js' | 'css'): Promise<string> {
  return new Promise((resolve, reject) => {
    // Check if the resource is already loaded
    const isLoaded = Array.from(document.querySelectorAll(type === 'js' ? 'script' : 'link'))
      .some(el => (type === 'js' ? (el as HTMLScriptElement).src : (el as HTMLLinkElement).href) === url)

    if (isLoaded) {
      resolve(`Resource already loaded: ${url}`)
      return
    }

    let element: HTMLScriptElement | HTMLLinkElement

    if (type === 'js') {
      element = document.createElement('script')
      element.src = url
      element.type = 'text/javascript'
      element.async = true
    } else if (type === 'css') {
      element = document.createElement('link')
      element.href = url
      element.rel = 'stylesheet'
      element.type = 'text/css'
    } else {
      reject(new Error('Invalid resource type. Use \'js\' or \'css\'.'))
      return
    }

    element.onload = () => resolve(`Resource loaded: ${url}`)
    element.onerror = () => reject(new Error(`Failed to load resource: ${url}`))

    // Append to <head> to start loading the resource
    document.head.appendChild(element)
  })
}
