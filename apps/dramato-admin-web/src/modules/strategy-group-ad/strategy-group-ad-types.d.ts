declare namespace Api {
  namespace AdStrategyGroup {
    namespace Request {
      interface List {
        strategy_id?: string
        status?: 0 | 1 | 2 | number // 1 启用 3 未启用
        pay_mode: 'IAA' | 'IAP' | string// IAA,IAP,
        platform: 'android' | 'ios' | string // android,ios
        is_abtest?: 0 | 1 | number // 是否AB实验 0否 1是
        // ASC: 从小到大, DESC: 从大到小
        sort?: {
          created?: 'ASC' | 'DESC' | string
          id?: 'ASC' | 'DESC' | string
        }
        sortType?: string
        // ad_type: 'free' | 'common' | string
        page_info: {
          offset: number
          size: number
        }
      }
    }
    namespace Response {
      type List = ApiResponse<{ list: M.AdStrategyGroup[], page_info: PageInfo2 }>
    }
  }
  namespace AdStrategyGroupUserProfile {
    type Item = {
      item_code: string
      item_value: string
      item_name: string
      item_description: string
      item_platform: string
      item_component_attr: number
      item_child: Item[]
    }
    namespace Response {
      type List = ApiResponse<{ list: M.AdStrategyGroupUserProfileItem[] }>
    }
  }
  namespace AdStrategyGroupAdChannel {
    namespace Response {
      type List = ApiResponse<{ list: Array<{ code: string, name: string }> }>
    }
  }

  namespace OriginAD {
    type Item = {
      id?: number
      name?: string
      ad_type?: string
      platform?: string// 上架平台：ios,android
      ad_unit?: string// 广告组单元
      mediation?: string// 竞价
      ad_platform?: string // 广告平台admob/meta
      period?: string // 周期类型:daily
      preload?: number // 是否预加载 1.是 0.否
      coins?: number // Coin价格
      version?: string // 最低版本号
      ad_expire_time?: number // 广告过期时间,单位毫秒,1小时
      ad_style?: string // 广告样式,这个字段这一期不用,两个样式随机
      created?: number// 创建时间
      create_user?: string
      updated?: number// 更新时间
      update_user?: string
      ad_group?: string
    }
  }
}
