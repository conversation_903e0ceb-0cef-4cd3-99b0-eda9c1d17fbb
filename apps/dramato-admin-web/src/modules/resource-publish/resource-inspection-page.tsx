/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Pager, transformNumber, Button, openDialog, Icon, showSuccessToast, showFailToast, SvgIcon } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { apiGetResourceCheckList, apiUpdateResourceRecycleStatus } from './resource-publish-api'
import { netdiskStatusOptions } from './constant'
import { useRouter } from 'vue-router'
import { useResourceStore } from './use-resource-publish-store'
import { renderStatusDesc } from './util'
import { taskStatusOptions, auditStatusForQuery, releaseRounds } from './constant'
import { requiredLabel } from 'src/lib/required-label'
import { useImagePreviewStore } from '@skynet/ui/image/use-preview-store'

type ResourceInspectionPageOptions = {
  props: {}
}
export const ResourceInspectionPage = createComponent<ResourceInspectionPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const { showImagePreviewDialog } = useImagePreviewStore()
  const { onUploadVideo,
    onDiskUpload,
    onUploadCaption,
    refreshSyncList,
    showUploadProgress,
    uploadSyncList,
    uploadSyncLoading,
    getTaskRole,
    resourceRole,
    all_members,
    onGetTask,
    onDistributeTaskNoTeam
  } = useResourceStore()
  const loading = ref(false)
  const Table = CreateTableOld<M.IResourceCheckItem>()
  const list = ref<M.IResourceCheckItem[]>([])
  const QueryForm = CreateForm<M.IResourcePublishQueryParams>()
  const defaultParams: M.IResourcePublishQueryParams = {
    title: '',
    id: undefined,
    partner_name: '',
    create_start_time: undefined, // 录入开始时间
    create_end_time: undefined, // 录入结束时间
    check_status: -1,
    netdisk_status: 0,
    series_key_or_title_list: '',
    resource_id_or_title_list: '',
    claim_names: [],
    audit_status_list: [],
    task_status: 0,
    entry_user: '',
    recycle_status_list: [],
  }
  const form = ref<M.IResourcePublishQueryParams>({ ...defaultParams })
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const resourceType = 1

  const recycleStatusOptions = [
    { value: 4, label: '暂缓' },
    { value: 5, label: '退剧' },
  ]
  const showProgressDesc = (value: {
    finish_count: number
    total_count: number
    failed_count: number
    tasking_count: number
  }, id: number, title: string) => {
    let taskNum = 0
    let taskName = ''
    let roundColor = ''
    // 优先级 如果有 处理失败的 展示 处理失败的状态、之后如果有未处理的 就展示
    if (value.failed_count > 0) {
      taskNum = value.failed_count
      taskName = '任务失败'
      roundColor = 'bg-[red]'
    } else if (value.tasking_count > 0) {
      taskNum = value.tasking_count
      roundColor = 'bg-primary'
      taskName = '同步中'
    } else {
      taskNum = 0
    }
    return (
      <div class="link" onClick={() => {
        void refreshSyncList(id)
        openDialog({
          title: '同步详情',
          body: () => uploadSyncLoading.value ? <div class="h-[88px] text-center leading-[88px]">加载中……</div> : showUploadProgress(uploadSyncList.value, id, onQuery),
          customClass: '!w-[500px] !max-h-[70vh]',
        })
      }}
      >
        <div>
          <div class="flex justify-center">
            <div>{title}:</div>
            <div class="flex flex-col">
              <div class={!taskName && value.finish_count === 0 ? 'text-[var(--text-1)]' : value.finish_count !== value.total_count ? 'text-[red]' : 'text-[green]'}>
                <span>{value.finish_count}</span>
                /
                <span>{value.total_count}</span>
              </div>
            </div>
          </div>

          {taskNum > 0
            ? (
                <div class="flex items-center justify-center gap-x-1 text-center">
                  <div class={mc('rounded-full size-2', roundColor)} />
                  <div>{taskName}: {taskNum}</div>
                </div>
              )
            : null }

        </div>
      </div>
    )
  }

  const showProgress = (value: M.IResourceStatus, id: number) => {
    return (
      <div>
        <div class="flex justify-center text-[var(--text-2)]">{showProgressDesc(value.full, id, '正片')}</div>
        <div class="flex justify-center text-[var(--text-2)]">{showProgressDesc(value.preview, id, '预告')}</div>
      </div>
    )
  }

  const columns: TableColumnOld<M.IResourceCheckItem>[] = [
    ['资源ID', 'id', { class: 'w-[100px]' }],
    ['资源名称', row => {
      return (
        <div class="relative">
          {row.release_round === 1 ? <SvgIcon class="absolute -left-4 -top-4 size-8" name="ic_release" /> : null}
          {row.recycle_status === 4 ? <span class="text-red-500">【已暂缓】</span> : row.recycle_status === 5 ? <span class="text-red-500">【已退剧】</span> : null}
          <span>{row.title}</span>
          {![4, 5].includes(row.recycle_status) && row.preview_status === 2 ? <span class="text-red-500">【可预告】</span> : null}
        </div>
      )
    }, { class: 'w-[180px]' }],
    ['合作方', 'partner_name', { class: 'w-[140px]' }],
    ['3:4封面', row => {
      return row.vertical_cover ? <img src={row.vertical_cover} class="size-[100px] object-contain" /> : '-'
    }, { class: 'w-[140px] flex justify-center' }],
    ['状态', row => {
      if (row.audit_status === 1) {
        return (
          <div class="tooltip flex items-center gap-x-1" data-tip={row.audit_reason}>
            <div class="tooltip line-clamp-1 flex-1 overflow-hidden text-red-500">
              <span class="text-[var(--text-3] text-xs">审核驳回：{row.audit_reason}</span>
            </div>
            {row.reject_image_list && row.reject_image_list.length > 0 ? (
              <div class="size-[60px]">
                <img class="size-full cursor-pointer object-contain" src={row.reject_image_list?.[0]} alt="" onClick={() => {
                  showImagePreviewDialog({
                    imageList: row.reject_image_list?.map(imageUrl => ({
                      src: imageUrl,
                      width: 2000,
                    })) || [],
                    canEscClose: true,
                  })
                }} />
              </div>
            ) : null}
          </div>
        )
      } else {
        return renderStatusDesc(row)
      }
    }, { class: 'text-center w-[240px]' }],
    ['网盘解析状态', row => {
      return (
        row.netdisk_status
          ? row.netdisk_err_msg && row.netdisk_status === 6
            ? (
                <div class="tooltip line-clamp-1 flex items-center justify-center overflow-hidden" data-tip={row.netdisk_err_msg}>
                  <div class="badge badge-outline">{row.netdisk_status ? ['', '检测未通过', '检测完成', '已转存', '已开启同步', '同步完成', '同步失败'][row.netdisk_status] : '-'}</div>
                  <div>{ row.netdisk_err_msg && row.netdisk_status === 6 ? row.netdisk_err_msg : '-' }</div>
                </div>
              )
            : (
                <div class="flex items-center justify-center">
                  <div class="badge badge-outline">{row.netdisk_status ? ['', '检测未通过', '检测完成', '已转存', '已开启同步', '同步完成', '同步失败'][row.netdisk_status] : '-'}</div>
                </div>
              )
          : '-'
      )
    }, { class: 'w-[140px] text-center' }],
    ['含字幕视频', row => showProgress(row.origin_episode_progress, row.id), { class: 'w-[140px] text-center' }],
    ['不含字幕视频', row => showProgress(row.pure_episode_progress, row.id), { class: 'w-[140px] text-center' }],
    ['字幕', row => showProgress(row.subtitle_language_progress, row.id), { class: 'w-[140px] text-center' }],
    ['认领人', 'claim_name', { class: 'w-[120px]' }],
    ['商务负责人', 'business_principal', { class: 'w-[120px]' }],
    ['录入人', 'entry_user', { class: 'w-[120px]' }],
    ['操作', row => (
      <div class="flex justify-center gap-x-2">
        <Button class="btn btn-link btn-xs" onClick={() => {
          openDialog({
            title: '上传资源',
            mainClass: 'pb-0 px-5',
            hideParentWhenChildOpen: true,
            body: () => (
              <x-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                <ul tabindex={0} class="menu z-[10] w-full rounded-box bg-base-100 p-2 ">
                  <li><a onClick={() => onDiskUpload(row.id, getList, row.subtitle_up_language_code)}>网盘资源上传</a></li>
                  <li><a onClick={() => onUploadVideo(row.id, row.partner_name || '', getList)}>上传视频</a></li>
                  <li><a onClick={() => onUploadCaption(row.id, getList, row.subtitle_up_language_code)}>上传字幕</a></li>
                  {/* <li><a onClick={() => onUploadAudio(row.id, getList)}>上传音频</a></li> */}
                  {/* <li><a onClick={() => onUploadImage(row.id, getList)}>上传封面</a></li> */}
                </ul>
              </x-status-confirm-dialog>
            ),
          })
        }}
        >
          资源上传
        </Button>
        {
          row.audit_status === 0
            ? (
                <Button
                  class="btn btn-link btn-xs"
                  onClick={() => {
                    void router.push(`/resource-publish/inspection-detail/${row.id}`)
                  }}
                >
                  验收
                </Button>
              )
            : (
                <Button
                  class="btn btn-link btn-xs"
                  onClick={() => {
                    void router.push(`/resource-publish/inspection-detail/${row.id}`)
                  }}
                >
                  查看
                </Button>
              )
        }
        {
          resourceRole.value === 1
            ? (
                <Button
                  class="btn btn-link btn-xs"
                  onClick={() => {
                    void onDistributeTaskNoTeam({
                      type: resourceType,
                      resource_ids: [row.id],
                    }, getList)
                  }}
                >
                  分配任务
                </Button>
              )
            : null
        }
        {
          ![4, 5].includes(row.recycle_status)
            ? (
                <Button class="btn btn-link btn-xs" onClick={() => {
                  const btnLoading = ref(false)
                  const recycleStatus = ref(row.recycle_status)
                  const dialog = openDialog({
                    title: '回收资源',
                    mainClass: 'pb-0 px-5',
                    body: () => (
                      <x-recycle-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                        <div class="flex items-center gap-x-2 pb-4">
                          {requiredLabel('状态:')}
                          <select class="select select-bordered select-sm" value={recycleStatus.value} onChange={(e: any) => {
                            recycleStatus.value = +e.target.value as 1 | 4 | 5
                          }}
                          >
                            {
                              recycleStatusOptions.map(option => (
                                <option value={option.value}>{option.label}</option>
                              ))
                            }
                          </select>
                        </div>
                        <div class="flex justify-end gap-x-2">
                          <Button class="s btn btn-sm" onClick={() => dialog()}>取消</Button>
                          <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                            btnLoading.value = true
                            try {
                              await apiUpdateResourceRecycleStatus({
                                series_resource_id_list: [row.id],
                                recycle_status: recycleStatus.value,
                              })
                              showSuccessToast('操作成功')
                              void getList()
                              dialog()
                            } catch (error: any) {
                              showFailToast(error.response.data.err_msg || '操作失败')
                            } finally {
                              btnLoading.value = false
                            }
                          }}
                          >
                            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                            确定
                          </Button>
                        </div>
                      </x-recycle-status-confirm-dialog>
                    ),
                  })
                }}
                >
                  回收
                </Button>
              )
            : row.recycle_status === 4
              ? (
                  <Button class="btn btn-link btn-xs" onClick={() => {
                    const btnLoading = ref(false)
                    const recycleStatus = ref(row.recycle_status)
                    const dialog = openDialog({
                      title: '回收资源',
                      mainClass: 'pb-0 px-5',
                      body: () => (
                        <x-recycle-status-confirm-dialog class="flex flex-col gap-y-[25px]">
                          <div class="flex items-center gap-x-2 pb-4">
                            确认取消暂缓？
                          </div>
                          <div class="flex justify-end gap-x-2">
                            <Button class="s btn btn-sm" onClick={() => dialog()}>取消</Button>
                            <Button class="btn btn-primary btn-sm" disabled={btnLoading.value} onClick={async () => {
                              btnLoading.value = true
                              try {
                                await apiUpdateResourceRecycleStatus({
                                  series_resource_id_list: [row.id],
                                  recycle_status: 1,
                                })
                                showSuccessToast('操作成功')
                                void getList()
                                dialog()
                              } catch (error: any) {
                                showFailToast(error.response.data.err_msg || '操作失败')
                              } finally {
                                btnLoading.value = false
                              }
                            }}
                            >
                              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
                              确定
                            </Button>
                          </div>
                        </x-recycle-status-confirm-dialog>
                      ),
                    })
                  }}
                  >
                    取消暂缓
                  </Button>
                )
              : null
        }

      </div>
    ),
    { class: 'text-center w-[260px]' }],
  ]

  const getList = async () => {
    const params = {
      ...form.value,
      ...pageInfo.value,
    }
    loading.value = true
    list.value = []
    try {
      const res = await apiGetResourceCheckList(params)
      list.value = res.data?.list || []
      total.value = res.data?.total || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = { ...defaultParams, audit_status_list: [] }
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }

  onQuery()
  void getTaskRole({
    type: resourceType,
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>资源验收</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              ['资源名称/ID：', 'resource_id_or_title_list', { type: 'text', placeholder: '请输入资源名称或者id' }],
              ['剧集名称/ID：', 'series_key_or_title_list', { type: 'text', placeholder: '请输入资源名称' }],
              ['审核状态：', 'audit_status_list', { type: 'multi-select', maxlength: 1, options: auditStatusForQuery }],
              ['合作方：', 'partner_name', { type: 'text', placeholder: '请输入合作方' }],
              ['认领人', 'claim_names', { type: 'multi-select', search: true, options: all_members.value.map(m => {
                return {
                  value: m,
                  label: m,
                }
              }) }],
              ['录入人：', 'entry_user', { type: 'text', placeholder: '请输入录入人' }],
              ['认领状态：', 'task_status', { type: 'select', options: taskStatusOptions, autoInsertEmptyOption: false }, { transform: transformNumber }],
              ['网盘解析状态：', 'netdisk_status', { type: 'select', options: netdiskStatusOptions, autoInsertEmptyOption: false }, { transform: transformNumber }],
              ['回收状态：', 'recycle_status_list', { type: 'multi-select', maxlength: 1, options: recycleStatusOptions }],
              ['发行轮次：', 'release_round', { type: 'select', placeholder: '请选择发行轮次', options: releaseRounds }, { transform: transformNumber }],
            ]}
          />
        ),
        tableActions: () => (
          <x-table-actions class="flex items-center justify-between">
            <span>验收列表</span>
            { resourceRole.value === 2
              ? (
                  <button class="btn btn-primary btn-sm" onClick={() => {
                    void onGetTask(resourceType, onQuery)
                  }}
                  >领取任务
                  </button>
                )
              : null}
          </x-table-actions>
        ),
        table: () => (
          <Table
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default ResourceInspectionPage
