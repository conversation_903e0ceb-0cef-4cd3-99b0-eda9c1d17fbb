import { httpClient } from 'src/lib/http-client'

export const apiGetVoiceOverList = (data: Api.VoiceOver.ListReqParams) =>
  httpClient.post<ApiResponse<Api.VoiceOver.ListResp>>('/audio/list', data)

export const apiPublishVoiceOver = (data: Api.VoiceOver.PublishReqParams) =>
  httpClient.post<ApiResponse<null>>('/audio/publish', data)

export const apiUploadVoiceOver = (data: Api.AudioUpload.UploadReqParams) =>
  httpClient.post<ApiResponse<null>>('/series_resource_v2/baidu_personal_to_company', data)

export const apiGenerateVoiceOver = (data: Api.VoiceOver.GenerateReqParams) =>
  httpClient.post<ApiResponse<null>>('/audio/dubbing_generation_auto', data)
