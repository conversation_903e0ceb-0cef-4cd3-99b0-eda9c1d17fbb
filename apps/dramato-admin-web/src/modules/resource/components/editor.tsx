/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, ref, onMounted, watch, onBeforeUnmount, PropType, shallowRef, toRaw } from 'vue'
import { useLoadMonaco } from 'src/lib/load-monaco-editor'
import * as Monaco from 'monaco-types'
import { useExpose } from '@skynet/ui/use/use-expose'
import { throttle } from 'lodash-es'

export default defineComponent({
  props: {
    customClass: {
      type: String as PropType<string>,
      required: false,
    },
    code: {
      type: String as PropType<string>,
      required: false,
      default: '',
    },
    options: {
      type: Object as PropType<object>,
      required: false,
    },
    onChange: {
      type: Function as PropType<(value: string, event: Monaco.editor.IModelContentChangedEvent) => void>,
      required: false,
    },
    onScroll: {
      type: Function as PropType<(value: number) => void>,
      required: false,
    },
    scrollTop: {
      type: Number as PropType<number>,
      required: false,
    },
    onVideoProgress: {
      type: Function,
      required: false,
    },
    currentTime: {
      type: Number as PropType<number>,
      required: false,
    },
    isAsync: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
    initFormat: {
      type: Boolean as PropType<boolean>,
      required: false,
      default: false,
    },
    errs: {
      type: Array as PropType<Array<M.IResourceSubtitleDetailError>>,
      required: false,
      default: () => [],
    },
    warns: {
      type: Array as PropType<Array<M.IResourceSubtitleFix>>,
      required: false,
      default: () => [],
    },
    highlightTerms: {
      type: Array as PropType<Array<string>>,
      required: false,
      default: () => [],
    },
    suggestions: {
      type: Array as PropType<Array<{
        label: string
        insertText: string
        detail?: string
        documentation?: string
      }>>,
      required: false,
      default: () => [],
    },
  },
  setup(props) {
    const { loadMonacoEditor, monacoLoading } = useLoadMonaco()

    // must be shallowRef, if not, editor.getValue() won't work
    const editorRef = shallowRef()

    const containerRef = ref()

    let _subscription: Monaco.IDisposable | undefined
    let __prevent_trigger_change_event = false
    let currentDecorationId: string[] = []
    let completionProvider: Monaco.IDisposable | undefined

    const getSeconds = (timeString: string) => {
      if (timeString.split(' --> ').length < 2) return
      // 提取第一个时间部分
      const firstTime = timeString.split(' --> ')[0] // "00:01:29,560"
      // 提取时、分、秒部分，忽略毫秒
      const [hours, minutes, seconds] = firstTime.split(':')
      const pureSeconds = parseInt(seconds.split(',')[0], 10) // 去掉毫秒部分

      // 计算总秒数
      return parseInt(hours, 10) * 3600 + parseInt(minutes, 10) * 60 + pureSeconds
    }

    // 解析字幕的时间范围
    const getSubtitleTimeRange = (lineContent: string): [number | undefined, number | undefined] => {
      const timeMatch = lineContent.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})/)

      if (timeMatch) {
        const startTimeInSeconds = getSecondsFromMatch(timeMatch, 1) // 获取开始时间
        const endTimeInSeconds = getSecondsFromMatch(timeMatch, 5) // 获取结束时间
        return [startTimeInSeconds, endTimeInSeconds]
      }

      return [undefined, undefined]
    }

    // 从时间匹配结果中提取秒数
    const getSecondsFromMatch = (match: RegExpMatchArray, startIndex: number): number => {
      const hours = parseInt(match[startIndex], 10)
      const minutes = parseInt(match[startIndex + 1], 10)
      const seconds = parseInt(match[startIndex + 2], 10)
      const milliseconds = parseInt(match[startIndex + 3], 10)

      return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
    }

    const findSubtitleLineByTime = (time: number): number | undefined => {
      const editor = editorRef.value
      const model = editor.getModel()

      if (!model) return

      const lines = model.getLinesContent()

      for (let i = 0; i < lines.length; i++) {
        const lineContent = lines[i]
        // 提取字幕的开始时间和结束时间
        const [startTime, endTime] = getSubtitleTimeRange(lineContent)

        // 判断当前播放时间是否在该时间范围内
        if (startTime !== undefined && endTime !== undefined) {
          if (time >= startTime && time <= endTime) {
            return i + 1 // Monaco 行号从 1 开始
          }
        }
      }
    }

    const highlightSubtitleLine = (lineNumber?: number) => {
      const editor = editorRef.value
      const model = editor.getModel()

      if (!editor || !model) return

      // 先清除之前的高亮
      currentDecorationId.forEach(id => {
        editor.deltaDecorations([id], [])
      })
      currentDecorationId = [] // 清空当前装饰 ID 数组

      if (lineNumber) {
        // 高亮当前字幕行
        const range = new window.monaco.Range(lineNumber, 1, lineNumber, 1)
        const decorations = [
          {
            range: range,
            options: {
              isWholeLine: true,
              inlineClassName: 'highlight-subtitle', // 高亮样式类
              stickiness: window.monaco.editor.TrackedRangeStickiness.AlwaysGrowsWhenTypingAtEdges,
            },
          },
        ]
        // 应用当前字幕行的高亮
        currentDecorationId.push(editor.deltaDecorations([], decorations)[0])
      }
      // 遍历高亮项并创建装饰
      props.highlightTerms.forEach(item => {
        const matches = editor.getModel().findMatches(item)
        matches.forEach((match: any) => { // 添加类型声明以消除 linter 错误
          currentDecorationId.push(editor.deltaDecorations([], [
            {
              range: match.range,
              options: {
                isWholeLine: false,
                inlineClassName: 'bg-red-200',
              },
            },
          ])[0]) // 保存新装饰的 ID
        })
      })
    }

    // 注册建议提供者
    const registerSuggestionProvider = () => {
      if (!window.monaco || !editorRef.value) return

      const provider = {
        triggerCharacters: ['/'],
        provideCompletionItems: (model: any, position: any) => {
          const textUntilPosition = model.getValueInRange({
            startLineNumber: position.lineNumber, // 获取当前光标所在行号
            startColumn: 1, // 从该行的第一列开始
            endLineNumber: position.lineNumber, // 结束行号也是当前行
            endColumn: position.column, // 结束列是当前光标所在列
          })

          // 检查是否输入了触发字符
          if (textUntilPosition.endsWith('/')) {
            return {
              suggestions: props.suggestions.map(item => ({
                ...item, // 展开传入的建议项属性
                kind: window.monaco.languages.CompletionItemKind.Text, // 设置建议类型为文本
                range: {
                  startLineNumber: position.lineNumber, // 开始行号为当前行
                  endLineNumber: position.lineNumber, // 结束行号为当前行
                  startColumn: position.column, // 开始列为当前光标位置
                  endColumn: position.column, // 结束列为当前光标位置
                },
              })),
            }
          }

          return { suggestions: [] }
        },
      }

      // 注册提示提供者
      completionProvider = window.monaco.languages.registerCompletionItemProvider('plaintext', provider)
    }

    // 删除建议提供者
    const removeSuggestions = () => {
      if (completionProvider) {
        completionProvider.dispose()
        completionProvider = undefined
      }
    }

    onMounted(async () => {
      await loadMonacoEditor()
      const editor = editorRef.value = window.monaco.editor.create(containerRef.value, {
        value: props.code,
        ...props.options,
      })

      // 注册自定义提示
      registerSuggestionProvider()

      if (props.initFormat) {
        setTimeout(() => {
          editorRef.value.getAction('editor.action.formatDocument').run()
        }, 300)
      }

      const model = editor.getModel()

      window.monaco.editor.defineTheme('customTheme', {
        base: 'vs',
        inherit: true,
        rules: [
          { token: 'error', foreground: 'FF0000' }, // 红色
        ],
        colors: {},
      })

      // 截流滚动
      const fn = throttle(() => {
        const scrollTop = editor.getScrollTop()
        props.onScroll && props.onScroll(scrollTop)
      }, 200)

      editor.onDidScrollChange(() => {
        fn()
      })

      editor.onMouseDown((e: any) => {
        // 检查是否点击了标记（警告）
        if (e.target.detail === 'editor.contrib.resizableContentHoverWidget') {
          const text = e.target?.element.innerText
          if (text && text.indexOf('[点击替换]') > -1) {
            const model = editor.getModel()
            // 获取所有的标记
            const markers = window.monaco.editor.getModelMarkers({ owner: 'owner' })
            // 找到匹配当前文本的警告标记
            const marker = markers.find(m =>
              m.severity === window.monaco.MarkerSeverity.Warning
              && m.message === text,
            )

            if (marker) {
              const lineNumber = marker.startLineNumber
              const lineContent = model?.getLineContent(lineNumber)
              // 从警告消息中提取替换内容
              const replacement = text.replace(' [点击替换]', '')

              // 执行替换
              editor.executeEdits('', [{
                range: {
                  startLineNumber: lineNumber,
                  endLineNumber: lineNumber,
                  startColumn: 1,
                  endColumn: lineContent ? lineContent.length + 1 : 0,
                },
                text: replacement,
              }])
            }
          }
        }

        // 保留原有的视频进度处理逻辑
        if (!props.onVideoProgress || typeof props.onVideoProgress !== 'function') return
        const position = editor.getPosition()
        const model = editor.getModel()

        if (position && model) {
          try {
            const lineNumber = position.lineNumber // 获取点击的行号
            const lineContent = model.getLineContent(lineNumber) // 获取该行的内容
            const r = getSeconds(lineContent)
            if (r !== undefined) {
              props.onVideoProgress(r)
            }
          } catch (err) {
            console.error('时间格式错误')
          }
        }
      })

      _subscription = editor.onDidChangeModelContent((event: Monaco.editor.IModelContentChangedEvent) => {
        if (!__prevent_trigger_change_event) {
          props.onChange && props.onChange(toRaw(editor.getValue()), event)
        }
      })
    })

    onBeforeUnmount(() => {
      if (_subscription)
        _subscription.dispose()

      removeSuggestions()
    })

    watch(() => props.currentTime, newTime => {
      if (editorRef.value && newTime !== undefined) {
        const lineNumber = findSubtitleLineByTime(newTime)
        if (lineNumber !== undefined) {
          const editor = editorRef.value
          if (props.isAsync) editor.revealLineInCenter(lineNumber) // 滚动到该行
        }
        highlightSubtitleLine(lineNumber) // 调用合并后的高亮函数
      }
    }, {
      immediate: true,
    })

    watch(() => props.scrollTop, v => {
      if (editorRef.value) editorRef.value.setScrollTop(v)
    }, {
      immediate: true,
    })

    watch(() => (props.options as any).fontSize, v => {
      if (editorRef.value) editorRef.value.updateOptions({ fontSize: v })
    })

    watch(() => [
      editorRef.value,
      props.code,
      props.errs,
      props.warns,
    ], () => {
      if (!editorRef.value) return
      const editor = editorRef.value
      const model = editor.getModel()
      window.monaco.editor.setModelMarkers(model, 'owner', [
        ...props.errs.map(err => ({
          startLineNumber: err.line,
          endLineNumber: err.line,
          startColumn: 1,
          endColumn: model.getLineMaxColumn(err.line),
          severity: window.monaco.MarkerSeverity.Error,
          message: err.err_content,
        })),
        ...props.warns.map(err => ({
          startLineNumber: err.line,
          endLineNumber: err.line,
          startColumn: 1,
          endColumn: model.getLineMaxColumn(err.line),
          severity: window.monaco.MarkerSeverity.Warning,
          message: err.err_content + ' [点击替换]',
        })),
      ])
    }, {
      immediate: true,
    })

    watch(() => props.code, v => {
      if (!editorRef.value) return
      const editor = editorRef.value
      const model = editor.getModel()
      if (v !== toRaw(model.getValue())) {
        editor.pushUndoStop()
        __prevent_trigger_change_event = true
        // pushEditOperations says it expects a cursorComputer, but doesn't seem to need one.
        model.pushEditOperations(
          [],
          [
            {
              range: model.getFullModelRange(),
              text: v,
            },
          ],
        )
        editor.pushUndoStop()
        __prevent_trigger_change_event = false
      }
      // if (v !== editorRef.value.getValue()) {
      //   editorRef.value.setValue(v)
      // }
    })

    const getEditor = () => {
      return editorRef.value
    }

    const insertText = (text: string) => {
      const position = editorRef.value.getPosition()
      if (position.column === 1 && position.lineNumber === 1) return
      editorRef.value.executeEdits('', [
        {
          range: new window.monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
          text: text
        }
      ])
      editorRef.value.setPosition(position.lineNumber, position.column + text.length)
      editorRef.value.focus()
    }

    useExpose({
      getEditor,
      removeSuggestions,
      insertText
    })

    return () => {
      return (
        monacoLoading.value
          ? (
              <div class="flex h-[55vh] w-full items-center justify-center">
                <span>加载中……</span>
              </div>
            )
          : <div ref={containerRef} class={props.customClass} />
      )
    }
  },
})
