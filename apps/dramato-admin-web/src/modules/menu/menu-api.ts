import { httpClient } from 'src/lib/http-client'

export const apiGetMenu = () =>
  httpClient.post<ApiResponse<{
    menu: M.IMenuItem[]
    open_roles: number[]
    role: number
  }>>('/backend/menu_v2')

export const apiCreateMenu = (data: M.IMenuItem) =>
  httpClient.post<ApiResponse<null>>('/backend/menu_add', data)

export const apiUpdateMenu = (data: M.IMenuItem) =>
  httpClient.post<ApiResponse<null>>('/backend/menu_update', data)

export const apiDelMenu = (data: M.IMenuItem) =>
  httpClient.post<ApiResponse<null>>('/backend/menu_delete', data)

export const apiGetRoles = () =>
  httpClient.post<ApiResponse<{
    list: M.ISimpleRole[]
  }>>('/backend/user_role_list')
