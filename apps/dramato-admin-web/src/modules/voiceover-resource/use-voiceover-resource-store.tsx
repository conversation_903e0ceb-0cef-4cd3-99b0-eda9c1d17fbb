/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Checkbox, Icon, openDialog, showFailToast, showSuccessToast, Tooltip } from '@skynet/ui'
import { computed, ref, watch } from 'vue'
import { apiGetAiVoiceResource } from './voiceover-resource-api'
import { apiGetSingleSubtitle, apiGetSeriesCharacters, apiSaveSubtitleContent } from 'src/modules/role-mark/role-mark-api'
import { langKey, langValue } from 'src/modules/resource/constant'
import { M3u8Player } from '../resource/components/m3u8-player'
// import { Subtitle } from 'src/modules/common/subtitle/subtitle'
import { RoleTimbre } from 'src/modules/voiceover-resource/role-timbre'
import { RouterLink } from 'vue-router'
import { SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { ElIcon, ElTooltip } from 'element-plus'

export const useVoiceoverResource = () => {
  return {
    list,
    loading,
    onQuery,
    getList,
    columns,
    defaultLanguageCode,
    cdnUrl,
    audios,
    filterCols,
    selectCols,
  }
}
const loading = ref<boolean>(false)
const list = ref<Api.VoiceoverResource.IMultipleResource[]>([])
const defaultLanguageCode = ref<string>('')
const audios = ref<Api.VoiceoverResource.IResourceAudio[]>([])
const cdnUrl = 'https://img.tianmai.cn/'
const series_resource_id = ref()
const selectCols = ref<string[]>([])
const characters = ref<Api.RoleMark.ICharacter[]>([])
const annotations = ref<Api.RoleMark.IAnnotation[]>([])
const previewLoading = ref(false)

const PROCESSING = 'processing'
const PURE_ERROR = 'failed'
const ResourceTaskStatusVoiceGenerateFailed = 'voice_generate_failed' // 音频拆分片段失败
const ResourceTaskStatusVoiceCombineFailed = 'voice_combine_failed' // 音频片段合成失败
const TRANS_ERROR = 'trans_failed'
const EXTRACT_ERROR = 'extract_failed'
const HISTORY_EXTRACT_ERROR = 'failed'
const shortAiTasks = ref<Api.VoiceoverResource.IShortAiTask[]>([])

const getList = async (id: number, showLoading = true) => {
  if (showLoading) {
    loading.value = true
  }
  const res = await apiGetAiVoiceResource({
    series_resource_id: id,
  })

  // 将数据按照剧集号(num)重新组织
  const srts = res.data?.srts || []
  shortAiTasks.value = res.data?.subtitle_shorten_tasks || []
  const episodeMap = new Map()

  srts.forEach(srt => {
    const language = srt.language
    srt.infos?.forEach(info => {
      const { num } = info
      if (!episodeMap.has(num)) {
        episodeMap.set(num, { num })
      }
      const episode = episodeMap.get(num)
      // 为每种语言添加相关路径信息
      episode[`${language}_ora_video_path`] = info.ora_video_path
      episode[`${language}_pure_video_path`] = info.pure_video_path
      episode[`${language}_srt_path`] = info.srt_path
      episode[`${language}_audio_path`] = info.audio_path
      episode[`${language}_annotation`] = info.annotation
      episode[`${language}_episode_path`] = info.episode_path
      episode[`${language}_voice`] = info.voice
      episode[`${language}_voice_err_msg`] = info.voice_err_msg
      episode[`${language}`] = srt.language
    })
  })
  audios.value = res.data?.audios || []
  // 转换为数组形式
  list.value = Array.from(episodeMap.values()).sort((a, b) => a.num - b.num)
  defaultLanguageCode.value = res.data?.default_language_code || ''
  loading.value = false
  console.log(list.value)
}

const getCharacters = async (id: number) => {
  try {
    const res = await apiGetSeriesCharacters({
      series_resource_id: id,
    })
    characters.value = res?.data?.characters || []
  } catch (error: any) {
    showFailToast(error.response.data.message || '操作失败')
  } finally {
    loading.value = false
  }
}

function getRealSubtitle(path: string) {
  const url = path.indexOf('http') === 0 ? path : `${cdnUrl}${path}`
  console.log(url, 'url----')
  return url
}

const onQuery = (id: number, showLoading = true) => {
  series_resource_id.value = id
  if (characters.value.length === 0) {
    void getCharacters(id)
  }
  void getList(id, showLoading)
}

const showAudioButton = (str: string) => {
  if (!str) {
    return <div>-</div>
  } if (str === PROCESSING) {
    return <div class="badge badge-info badge-outline">处理中</div>
  } else if (str === PURE_ERROR) {
    return <div class="badge badge-error badge-outline ">处理失败</div>
  } else if (str === ResourceTaskStatusVoiceGenerateFailed) {
    return <div class="badge badge-error badge-outline ">音频分段报错</div>
  } else if (str === ResourceTaskStatusVoiceCombineFailed) {
    return <div class="badge badge-error badge-outline ">合并失败</div>
  } else {
    return <Button class="btn btn-link btn-xs" onClick={() => previewAudio(`${getRealSubtitle(str)}`)}>音频预览</Button>
  }
}

const showVideoButton = (str: string) => {
  if (!str) {
    return <div>-</div>
  } if (str === PROCESSING) {
    return <div class="badge badge-info badge-outline ">处理中</div>
  } else if (str === PURE_ERROR) {
    return <div class="badge badge-error badge-outline ">处理失败</div>
  } else {
    return <Button class="btn btn-link btn-xs" onClick={() => onPreviewVideo(getRealSubtitle(str))}>视频预览</Button>
  }
}

const showCombineVideo = (str: string) => {
  if (!str) {
    return <div>-</div>
  } if (str === PROCESSING) {
    return <div class="badge badge-info badge-outline ">处理中</div>
  } else if (str === 'failed') {
    return <div class="badge badge-error badge-outline ">合成失败</div>
  } else {
    return <Button class="btn btn-link btn-xs" onClick={() => onPreviewVideo(getRealSubtitle(str))}>视频预览</Button>
  }
}

const onPreviewVideo = (videoUrl: string) => {
  openDialog({
    title: () => (
      <div class="flex items-center space-x-2">
        <span>预览</span>
      </div>
    ),
    body: <M3u8Player class="max-h-[550px] w-[350px]" url={videoUrl} />,
    customClass: '!w-[350px]',
  })
}

const previewAudio = (path: string) => {
  openDialog({
    title: '音频查看',
    mainClass: 'px-4 !py-0',
    customClass: '!w-[400px]',
    body: () => <audio src={path} controls />,
  })
}

const getAnnotations = async (num: number) => {
  const res = await apiGetSingleSubtitle({
    series_resource_id: series_resource_id.value,
    serial_number: num,
  })
  // 已经填入的
  annotations.value = res.data?.annotations || []
}

// const previewSubtitle = async (row: Api.VoiceoverResource.IMultipleResource, langIndex: number) => {
//   if (previewLoading.value) return
//   previewLoading.value = true
//   const btnLoading = ref(false)
//   const resourceLoading = ref(false)
//   // 当前选中行
//   const currentRow = ref<Api.VoiceoverResource.IMultipleResource>(row)
//   // 剧集原语言
//   const referenceLangIndex = ref(langKey.findIndex(k => defaultLanguageCode.value && defaultLanguageCode.value && k === defaultLanguageCode.value))
//   const videoRef = ref()
//   const currentTime = ref(0)
//   // 右侧展示语言
//   const originalLangIndex = ref(langIndex)
//   // 左侧
//   const originalRef = ref()
//   const referenceRef = ref()

//   const isAsync = ref(true)

//   const lineNotMatch = ref(false)

//   const videoType = ref<'pure_video_path' | 'ora_video_path'>('pure_video_path')
//   const videoUrl = ref(currentRow.value[`${defaultLanguageCode.value}_${videoType.value}`])
//   await getAnnotations(currentRow.value.num)
//   // 左侧对照永远选择 中文
//   const subtitleLang = langKey[referenceLangIndex.value]
//   const referenceUrl = currentRow.value[`${langKey[referenceLangIndex.value]}_srt_path`]
//   const reference = ref({
//     url: referenceUrl,
//     language: subtitleLang,
//   })
//   const original = ref({
//     language: langKey[originalLangIndex.value],
//     url: row[`${langKey[langIndex]}_srt_path`],
//   })

//   const subtitles = ref([
//     {
//       language: original.value.language,
//       type: 'normal',
//       subtitle: getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_srt_path`]),
//     },
//   ])

//   watch(() => currentRow.value.num, async () => {
//     resourceLoading.value = true
//     currentTime.value = 0
//     lineNotMatch.value = false
//     await getAnnotations(currentRow.value.num)
//     const subtitleLang = langKey[referenceLangIndex.value]
//     // 左侧对照永远选择 中文
//     reference.value = {
//       url: currentRow.value[`${subtitleLang}_srt_path`],
//       language: langKey[referenceLangIndex.value],
//     }
//     original.value = {
//       language: langKey[originalLangIndex.value],
//       url: currentRow.value[`${langKey[originalLangIndex.value]}_srt_path`],
//     }
//     videoUrl.value = currentRow.value[`${defaultLanguageCode.value}_${videoType.value}`]

//     subtitles.value = [
//       {
//         language: original.value.language,
//         type: 'normal',
//         subtitle: getRealSubtitle(currentRow.value[`${langKey[originalLangIndex.value]}_srt_path`]),
//       },
//     ]
//     // 可选的角色 suggestion
//     resourceLoading.value = false
//   }, {
//     immediate: true,
//   })

//   watch(() => videoRef.value, newVal => {
//     if (newVal) {
//       videoRef.value.on('timeupdate', () => {
//         currentTime.value = videoRef.value.getCurrentTime() || 0
//       })
//     }
//   })

//   watch(() => videoType.value, () => {
//     if (videoType.value === 'ora_video_path') {
//       videoUrl.value = currentRow.value[`${defaultLanguageCode.value}_ora_video_path`]
//     } else if (videoType.value === 'pure_video_path') {
//       videoUrl.value = currentRow.value[`${defaultLanguageCode.value}_pure_video_path`]
//     }
//   })

//   watch(() => referenceLangIndex.value, () => {
//     resourceLoading.value = true
//     lineNotMatch.value = false
//     reference.value = {
//       url: currentRow.value[`${langKey[referenceLangIndex.value]}_srt_path`],
//       language: langKey[referenceLangIndex.value],
//     }

//     resourceLoading.value = false
//   })

//   watch(() => originalLangIndex.value, async () => {
//     resourceLoading.value = true
//     lineNotMatch.value = false
//     await getAnnotations(currentRow.value.num)
//     original.value = {
//       url: currentRow.value[`${langKey[originalLangIndex.value]}_srt_path`],
//       language: langKey[originalLangIndex.value],
//     }

//     resourceLoading.value = false
//   })

//   const getFormatSubtitle = (list: Api.RoleMark.ISubtitleLine[]) => {
//     return list.map(item => {
//       return `${item.order}\n${item.time}\n${item.content}\n`
//     }).join('\n')
//   }

//   openDialog({
//     title: () => (
//       <div class="flex items-center space-x-4">
//         <span>字幕校对</span>
//         <span class="text-red-500 text-sm">{lineNotMatch.value ? '字幕行数对不上' : ''}</span>
//       </div>
//     ),
//     canEscClose: false,
//     mainClass: 'px-4 !py-0 !pb-0',
//     hideParentWhenChildOpen: true,
//     customClass: '!w-[1200px] min-h-[520px]',
//     body: () => (
//       <div>
//         <div class="flex flex-row">
//           <x-video-area>
//             { videoUrl.value
//               ? (
//                   <M3u8Player
//                     subtitles={subtitles.value}
//                     currentLanguage={langKey[originalLangIndex.value]}
//                     url={getRealSubtitle(videoUrl.value)}
//                     onPlayerReady={(e: any) => {
//                       videoRef.value = e
//                     }}
//                   />
//                 )
//               : <div class="flex h-[550px] w-[309px] items-center justify-center text-[var(--text-2)]">暂无视频</div> }
//           </x-video-area>
//           <x-subtitle-area class="flex h-[550px] w-[820px] flex-1 overflow-auto">
//             <div>
//               <select value={reference.value.language} class="select select-bordered select-xs w-[100px]" onChange={(e: any) => {
//                 const value = e.target.value as string
//                 referenceLangIndex.value = langKey.findIndex(key => key === value)
//               }}
//               >
//                 {
//                   langKey.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
//                 }
//               </select>
//               <Subtitle
//                 ref={referenceRef}
//                 class="h-full w-[400px]"
//                 url={getRealSubtitle(reference.value.url)}
//                 onVideoProgress={(time: number) => {
//                   if (videoRef.value) videoRef.value?.seek(time)
//                 }}
//                 rolePick={false}
//                 showCharacters={true}
//                 characters={characters.value}
//                 annotations={annotations.value}
//                 isEditable={false}
//               />
//             </div>
//             <div>
//               <select value={original.value.language} class="select select-bordered select-xs w-[100px]" onChange={(e: any) => {
//                 const value = e.target.value as string
//                 originalLangIndex.value = langKey.findIndex(key => key === value)
//               }}
//               >
//                 {
//                   langKey.map((lang, index) => <option value={lang}>{langValue[index]}</option>)
//                 }
//               </select>
//               <Subtitle
//                 ref={originalRef}
//                 class="h-full w-[280px]"
//                 url={getRealSubtitle(original.value.url)}
//                 currentTime={currentTime.value}
//                 onVideoProgress={(time: number) => {
//                   if (videoRef.value) videoRef.value?.seek(time)
//                 }}
//                 showCharacters={false}
//                 characters={[]}
//                 annotations={[]}
//                 isEditable={true}
//                 rolePick={false}
//                 isAsyncScroll={isAsync.value}
//               />
//             </div>
//             <RoleTimbre lang={langKey[originalLangIndex.value]} onChangeEpisodeNum={num => {
//               const index = list.value.findIndex(item => item.num === num)
//               currentRow.value = list.value[index]
//             }} />
//           </x-subtitle-area>
//         </div>
//         <x-subtitle-operation>
//           <div class="bp-2 flex items-center justify-between pt-4">
//             <div class="flex">
//               <Button class="btn btn-outline btn-xs" disabled={+currentRow.value.num === +list.value[0].num || resourceLoading.value || btnLoading.value} onClick={() => {
//                 const index = list.value.findIndex(item => item.num === currentRow.value.num)
//                 currentRow.value = list.value[index - 1]
//               }}
//               >上一集
//               </Button>
//               <div class="px-4">{ currentRow.value.num }</div>
//               <Button class="btn btn-outline btn-xs" disabled={list.value[list.value.length - 1].num === currentRow.value.num || resourceLoading.value || btnLoading.value} onClick={() => {
//                 const index = list.value.findIndex(item => item.num === currentRow.value.num)
//                 currentRow.value = list.value[index + 1]
//               }}
//               >下一集
//               </Button>
//               <select value={videoType.value} class="select select-bordered select-xs ml-4 w-[100px]" onChange={(e: any) => {
//                 const value = e.target.value
//                 videoType.value = value
//               }}
//               >
//                 <option value="pure_video_path">无字幕视频</option>
//                 <option value="ora_video_path">原视频</option>
//               </select>
//               <label class="flex items-center ml-4 cursor-pointer">
//                 <Checkbox
//                   label=""
//                   modelValue={isAsync.value}
//                   onUpdate:modelValue={(value: boolean) => {
//                     isAsync.value = value
//                   }}
//                 />
//                 <span>同步滚动</span>
//               </label>
//             </div>
//             <div>
//               <Button class="btn btn-primary btn-sm" disabled={btnLoading.value || resourceLoading.value} onClick={async () => {
//                 const _list = originalRef.value.getContent() as Api.RoleMark.ISubtitleLine[]
//                 const referenceList = referenceRef.value.getContent() as Api.RoleMark.ISubtitleLine[]

//                 const list = _list.map((item, index) => {
//                   item.order = index + 1
//                   return item
//                 })

//                 const arr = list.filter(item => !item.content || !item.time)
//                 if (arr.length > 0) {
//                   showFailToast(`字幕第${arr.map(item => item.order).join(',')}请完成字幕内容及时间的填写`)
//                   return
//                 }

//                 if (_list.length !== referenceList.length) {
//                   lineNotMatch.value = true
//                 } else {
//                   lineNotMatch.value = false
//                 }

//                 const formatCode = getFormatSubtitle(list)
//                 btnLoading.value = true

//                 try {
//                   const res = await apiSaveSubtitleContent({
//                     serial_number: currentRow.value.num,
//                     series_resource_id: series_resource_id.value,
//                     language_code: original.value.language,
//                     alter_type: 1,
//                     subtitle_text: formatCode,
//                     audio_type: 1,
//                   })
//                   currentRow.value[`${original.value.language}_srt_path`] = res.data?.file_path || ''
//                   showSuccessToast('保存成功')
//                 } catch (error: any) {
//                   showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
//                 } finally {
//                   btnLoading.value = false
//                 }
//               }}>
//                 {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
//                 保存字幕
//               </Button>
//             </div>
//           </div>
//         </x-subtitle-operation>
//       </div>
//     ),
//   })
//   previewLoading.value = false
// }

const filterCols = ref<{ value: string, label: string }[]>([])

langKey.map((key, index) => {
  selectCols.value.push(langValue[index])

  filterCols.value.push({
    value: langValue[index],
    label: langValue[index],
  })
})

const filterShortAiTasks = (num: number, key: string) => {
  return shortAiTasks.value.find(item => item.serial_number === num && item.language === key)
}

const columns = computed(() => [
  {
    prop: 'ora_video_path',
    minWidth: 100,
    label: '含字幕视频',
    align: 'center',
    render: (scope: { row: Api.VoiceoverResource.IMultipleResource }) => {
      const path = scope.row[`${defaultLanguageCode.value}_ora_video_path`]
      return (
        <Button class="btn btn-link btn-xs" onClick={() => {
          onPreviewVideo(getRealSubtitle(path))
        }}>
          预览
        </Button>
      )
    },
  },
  {
    prop: 'pure_video_path',
    minWidth: 100,
    label: '无字幕视频',
    align: 'center',
    render: (scope: { row: Api.VoiceoverResource.IMultipleResource }) => {
      const path = scope.row[`${defaultLanguageCode.value}_pure_video_path`]
      return (
        <Button class="btn btn-link btn-xs" onClick={() => {
          onPreviewVideo(getRealSubtitle(path))
        }}>
          预览
        </Button>
      )
    },
  },
  {
    prop: 'pure_resource',
    minWidth: 100,
    label: '纯净资源',
    align: 'center',
    render: (scope: { row: Api.VoiceoverResource.IMultipleResource }) => {
      const audioPath = audios.value.find(item => item.num === scope.row.num)?.ora_path || ''
      const videoPath = audios.value.find(item => item.num === scope.row.num)?.noaudio_path || ''

      return (
        <div>
          <div>
            {showVideoButton(videoPath)}
          </div>
          <div>
            {showAudioButton(audioPath)}
          </div>
        </div>
      )
    },
  },
  {
    prop: 'rawPersonAudio',
    minWidth: 110,
    label: '原始人声音轨',
    align: 'center',
    render: (scope: { row: Api.VoiceoverResource.IMultipleResource }) => {
      const audioPath = audios.value.find(item => item.num === scope.row.num)?.voice_path || ''
      return (
        <div>
          {showAudioButton(audioPath)}
        </div>
      )
    },
  },
  {
    prop: 'rawBackgroundAudio',
    minWidth: 110,
    label: '原始背景音轨',
    align: 'center',
    render: (scope: { row: Api.VoiceoverResource.IMultipleResource }) => {
      const audioPath = audios.value.find(item => item.num === scope.row.num)?.background_path || ''
      return (
        <div>
          {showAudioButton(audioPath)}
        </div>
      )
    },
  },
  ...selectCols.value.map(item => ({
    prop: langKey[langValue.findIndex(r => r === item)],
    label: item,
    minWidth: 130,
    align: 'center',
    render: (scope: { row: Api.VoiceoverResource.IMultipleResource }) => {
      // const videoUrl = scope.row[`${item}_ora_video_path`]
      const index = langValue.findIndex(r => r === item)
      const key = langKey[index]
      const subtitlePath = scope.row[`${key}_srt_path`]
      const audioPath = scope.row[`${key}_audio_path`]
      const episodePath = scope.row[`${key}_episode_path`]
      const voiceNum = +scope.row[`${key}_voice`]
      return (
        <div class="flex flex-col items-center gap-1">
          <div>
            {
              !subtitlePath ? '-' : (subtitlePath === TRANS_ERROR || subtitlePath === HISTORY_EXTRACT_ERROR) ? '翻译错误' : subtitlePath === EXTRACT_ERROR ? '提取错误'
                : (
                    // <Button class="btn btn-link btn-active btn-xs" onClick={() => {
                    //   if (previewLoading.value) return
                    //   void previewSubtitle(scope.row, index)
                    // }}>字幕-配</Button>
                    <RouterLink class="btn btn-link btn-active btn-xs" to={`/resource-publish/voice-over/resource/${series_resource_id.value}?ref=${defaultLanguageCode.value}&language=${langKey[langValue.findIndex(r => r === item)]}&number=${scope.row.num}`}>字幕-配
                      {key == filterShortAiTasks(scope.row.num, key)?.language && (filterShortAiTasks(scope.row.num, key)?.status || 0) >= 0 ? (
                        <span class="text-xs text-red-500 flex items-center gap-0">
                          (缩
                          {
                            filterShortAiTasks(scope.row.num, key)?.status == 2 ? <el-icon class="!text-green-500"><SuccessFilled /></el-icon> : ''
                          }

                          {
                            filterShortAiTasks(scope.row.num, key)?.status == 3 ? (
                              <ElTooltip content={filterShortAiTasks(scope.row.num, key)?.err_msg} placement="top">
                                <el-icon class="text-red-500"><CircleCloseFilled /></el-icon>
                              </ElTooltip>
                            ) : ''
                          } 
                          )
                        </span>
                      ) : null}
                    </RouterLink>
                  )
            }
          </div>
          <div>
            {
              voiceNum > 0 ? (
                <Button class="btn btn-link btn-xs" onClick={() => {
                  const url = window.location.href
                  const urlParamArr = url.split('/')
                  const id = urlParamArr[urlParamArr.length - 1]
                  window.open(`/voiceover-editor?series_resource_id=${id}&serial_number=${scope.row.num}&language=${key}`)
                }}>配音审核</Button>
              ) : voiceNum === -1 ? <div class="badge badge-info badge-outline ">处理中</div> : voiceNum === -2 ? (
                <Tooltip popContent={() => <div>{scope.row[`${key}_voice_err_msg`] || '-'}</div>}>
                  <div class="badge badge-error badge-outline">生成失败</div>
                </Tooltip>
              ) : '-'
            }
          </div>
          <div>
            { showAudioButton(audioPath) }
          </div>
          <div>
            { showCombineVideo(episodePath) }
          </div>
        </div>
      )
    },
  })),
])
