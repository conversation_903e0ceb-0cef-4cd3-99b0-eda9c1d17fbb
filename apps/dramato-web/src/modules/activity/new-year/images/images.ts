import { default as enImage1 } from './en/1.webp'
import { default as enImage2 } from './en/2.webp'
import { default as enImage3 } from './en/3.webp'

import { default as esImage1 } from './es/1.webp'
import { default as esImage2 } from './es/2.webp'
import { default as esImage3 } from './es/3.webp'

import { default as frImage1 } from './fr/1.webp'
import { default as frImage2 } from './fr/2.webp'
import { default as frImage3 } from './fr/3.webp'

import { default as idImage1 } from './id/1.webp'
import { default as idImage2 } from './id/2.webp'
import { default as idImage3 } from './id/3.webp'

import { default as itImage1 } from './it/1.webp'
import { default as itImage2 } from './it/2.webp'
import { default as itImage3 } from './it/3.webp'

import { default as ptImage1 } from './pt/1.webp'
import { default as ptImage2 } from './pt/2.webp'
import { default as ptImage3 } from './pt/3.webp'

import { default as thImage1 } from './th/1.webp'
import { default as thImage2 } from './th/2.webp'
import { default as thImage3 } from './th/3.webp'

import { default as tlImage1 } from './tl/1.webp'
import { default as tlImage2 } from './tl/2.webp'
import { default as tlImage3 } from './tl/3.webp'

import { default as viImage1 } from './vi/1.webp'
import { default as viImage2 } from './vi/2.webp'
import { default as viImage3 } from './vi/3.webp'

export const newYearImages: Record<string, string[]> = {
  en: [enImage1, enImage2, enImage3],
  es: [esImage1, esImage2, esImage3],
  fr: [frImage1, frImage2, frImage3],
  id: [idImage1, idImage2, idImage3],
  it: [itImage1, itImage2, itImage3],
  pt: [ptImage1, ptImage2, ptImage3],
  th: [thImage1, thImage2, thImage3],
  tl: [tlImage1, tlImage2, tlImage3],
  vi: [viImage1, viImage2, viImage3],
}
