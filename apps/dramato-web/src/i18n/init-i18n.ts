// src/i18n.js
import { createI18n } from 'vue-i18n'
import { en } from './en/en'
import { es } from './es/es'
import { ja } from './ja/ja'
import { ko } from './ko/ko'
import { pt } from './pt/pt'
import { th } from './th/th'
import { id } from './id/id'
import { vi } from './vi/vi'
import { tl } from './tl/tl'
import { fr } from './fr/fr'
import { de } from './de/de'
import { it } from './it/it'
import { ru } from './ru/ru'
import { tr } from './tr/tr'
import { ms } from './ms/ms'
import { hi } from './hi/hi'
import { zh_tw } from './zh-TW/zh-TW'
import { zh_cn } from './zh-CN/zh-CN'

const messages = {
  en,
  ja,
  es,
  ko,
  pt,
  th,
  id,
  vi,
  tl,
  fr,
  de,
  it,
  tr,
  ru,
  ms,
  hi,
  'zh-TW': zh_tw,
  'zh-CN': zh_cn,
}

const i18n = createI18n({
  locale: 'en', // 默认语言
  messages,
})

export default i18n
