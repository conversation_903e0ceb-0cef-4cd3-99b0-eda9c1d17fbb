<template>
  <div class="h-screen flex justify-center items-center flex-col">
    <div ref="paymentAnimationRef" class="w-[3.375rem] h-[3.375rem]" />
    <div class="text-[#FDFBFC] text-base font-medium mt-4 text-center">Your payment information has been <br />
      protected.</div>
    <div class="text-text-2 text-sm mt-1">Placing order,please wait...</div>
    <Popup v-model:show="showPop" :destroy-on-close="true" :close-on-click-overlay="true" position="bottom"
      :style="{ height: '220px' }" class="rounded-t-lg">
      <PayThirdButton :payChannel="payChannel" :paySubChannel="paySubChannel" :type="thirdType" :product-id="productId"
        :from="route.query.return_url as string || '/'" @clickRule="gotoRules" />
    </Popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount, computed, watch } from 'vue'
import { loadPaymentAnimation } from 'src/modules/common/animation/payment-animation'
import paymentAnimation from 'src/modules/common/animation/payment-loading.json'
import { gotoRules } from 'src/h5_modules/common/common'
import { h5Track } from 'src/lib/h5-track';
import { reportNow } from '@skynet/client-track'
import { usePayment } from './use-payment'
import { useRoute } from 'vue-router'
import { Popup } from 'vant'
import PayThirdButton from './payment-third-button.vue'
import { getUser, setUser } from 'src/h5_modules/common/user'

const route = useRoute()
const paymentAnimationRef = ref<HTMLDivElement>()

const { getProductList, productData, uid, handlePay, thirdType, showPop, productId, selectedPayChannel, payChannel, paySubChannel } = usePayment()
const user = ref()
const currentPayChannel = ref('')
const currentProductId = ref(0)
const currentProductType = ref('')
onMounted(() => {
  void loadPaymentAnimation({
    container: paymentAnimationRef.value,
    renderer: 'svg',
    loop: true,
    autoplay: true,
    animationData: paymentAnimation,
  })
})
// 客户端参数
// ?user_id=**********
// &auth_key=pnqSRKDKgTELiXDAv7iD17Vt0TaGOxR6
// &auth_secret=HokPQQkTVNBSgms9XN7Lf38wSVrzBWQG
// &icon=https://static-v1.mydramawave.com/avatar/default_guest1.png
// &name=Guest
// &user_type=0
// &language_code=en
// &return_url=dramawave://payment-return&product_id=523
// &sub_payment_channel=bank
// &payment_channel=AirWallex
onBeforeMount(async () => {
  const { auth_key, auth_secret, icon, user_id, user_type, name, product_id } = route.query
  const payment_channel = decodeURIComponent(route.query.payment_channel as string)
  if (user_id) {
    setUser({
      auth_key: auth_key as string,
      auth_secret: auth_secret as string,
      icon: icon as string,
      name: name as string,
      user_id: Number(user_id),
      user_type: Number(user_type)
    })

    console.log('pay_channel', payment_channel)
    currentPayChannel.value = payment_channel as string
    currentProductId.value = Number(product_id)
    productId.value = Number(product_id)
  }
  user.value = getUser()
  uid.value = user.value.user_id
  h5Track('h5pay', 'ios', 'show', {
    event: 'h5pay_ios_show',
    user_id: user.value.user_id,
  })
  reportNow()
  await getProductList()
})


const currentProductData = computed(() => {
  console.log('productData', productData.value)
  const memList = productData.value?.membership || []
  const rechargeList = productData.value?.recharge_list || []
  if (memList.find(item => item.product_id === currentProductId.value)) {
    let currentProduct = memList.find(item => item.product_id === currentProductId.value)
    currentProduct = { ...currentProduct, find_product_type: 'membership' }
    return currentProduct
  } else if (rechargeList.find(item => item.product_id === currentProductId.value)) {
    let currentProduct = rechargeList.find(item => item.product_id === currentProductId.value)
    currentProduct = { ...currentProduct, find_product_type: 'recharge' }
    return currentProduct
  }
  return { discount_price: 0, price: 0, currency_symbol: '' }
})

watch(currentProductData, (newVal) => {
  const payment_channel = decodeURIComponent(route.query.payment_channel as string)
  const { product_id, sub_payment_channel } = route.query
  console.log('newVal', newVal)
  if (newVal.find_product_type) {
    productId.value = Number(product_id);
    selectedPayChannel.value = payment_channel
    payChannel.value = encodeURIComponent(route.query.payment_channel as string)
    paySubChannel.value = sub_payment_channel as string
    handlePay({
      from: route.query.return_url as string || '/',
      series_id: route.query.series_id as string,
      video_id: route.query.video_id as string,
      video_type: route.query.video_type as string,
    })
  }
})
</script>

<style scoped>
:deep(.van-nav-bar__title) {
  color: rgba(255, 255, 255, 1);
  font-weight: 500;
}

:deep(.van-nav-bar) {
  --van-nav-bar-background: rgba(11, 8, 11, 1);
}

:deep(.van-cell) {
  --van-cell-vertical-padding: 12px;
  --van-cell-horizontal-padding: 12px;
  align-items: center;
}

:deep(.van-collapse-item__wrapper) {
  --van-collapse-item-content-padding: 0px;
}

:deep(.van-collapse-item--border:after) {
  border: none;
}

:deep(.van-collapse-item__title--expanded:after) {
  left: 0;
  right: 0;
}

:deep(.van-collapse) {
  --van-border-width: 0px;
}

:deep(.van-popup) {
  --van-popup-background: #fff
}

.custom-border {
  position: relative;
}

.custom-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

:deep(.page-is-top) {
  .van-nav-bar {
    background: transparent;
  }
}
</style>