{"name": "dramato-admin-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --mode development --port 5176 --no-clearScreen", "build": "vite build --mode production", "build:test": "NODE_OPTIONS='--max-old-space-size=8192' vite build --mode staging", "build:prod": "NODE_OPTIONS='--max-old-space-size=8192' vite build --mode production", "preview": "vite preview --host --base=./", "deploy:test": "bin/deploy_from_local.sh test", "deploy:prod": "bin/deploy_from_local.sh prod", "jenkins:test": "bin/jenkins.sh test", "jenkins:prod": "bin/jenkins.sh prod", "//webp": "pnpm run webp /Users/<USER>/Downloads/1 会将1目录下的图片转换为webp格式（非递归）", "webp": "bin/webp.mjs", "check": "tsc --noEmit -p tsconfig.app.json"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@skynet/client-track": "workspace:*", "@skynet/preset": "workspace:*", "@skynet/shared": "workspace:*", "@skynet/ui": "workspace:*", "@vueuse/core": "catalog:", "@vueuse/integrations": "^11.0.0", "ali-oss": "6.22.0", "axios": "catalog:", "choices.js": "catalog:", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "crypto.js": "^3.3.4", "dayjs": "catalog:", "echarts": "catalog:", "element-plus": "2.9.3", "github-markdown-css": "^5.6.1", "js-cookie": "3.0.5", "js-md5": "0.8.3", "lodash-es": "catalog:", "lottie-web": "^5.12.2", "pinia": "^2.3.1", "qs": "^6.14.0", "swrv": "1.0.4", "vite-bundle-analyzer": "catalog:", "vue": "catalog:", "vue-i18n": "^10.0.1", "vue-multiselect": "catalog:", "vue-router": "catalog:", "weixin-js-sdk": "^1.6.5", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@iconify/vue": "catalog:", "@skynet/vite-plugin-svg-icons": "workspace:*", "@types/ali-oss": "catalog:", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "catalog:", "@types/lodash-es": "catalog:", "@types/sortablejs": "catalog:", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "autoprefixer": "catalog:", "monaco-editor": "catalog:", "monaco-types": "0.1.0", "postcss": "catalog:", "postcss-import": "catalog:", "postcss-nested": "catalog:", "tailwind-scrollbar": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:", "unplugin-element-plus": "0.9.1", "vconsole": "catalog:", "vite": "catalog:", "vite-plugin-markdown": "2.2.0", "zx": "catalog:"}, "browserslist": ["defaults", "chrome > 50", "edge > 14"]}