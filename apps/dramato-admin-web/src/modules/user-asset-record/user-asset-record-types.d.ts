declare namespace M {
  namespace UserAssetRecord {
    interface Params {
      start_time?: number
      end_time?: number
      op_type?: string
      page_index?: number
      page_size?: number
    }

    interface Item {
      id: number // 操作记录id 请求详情接口需要带上
      op_time: number // 记录时间戳
      op_type: string // "compensation":补偿 "asset_transfer":资产转移
      target_users: string
      operator: string
    }
    interface Response {
      items: Item[]
      total: number
    }
    interface OpsCompensationDetail {
      target_user_id: number
      compensation_type: string
      compensation_amount: number
      compensation_reason: string
      remark: string // 备注
    }
    interface OpsTransferDetail {
      in_user_id: number
      out_user_id: number
      transfered_asset: {
        subscription: string // "": 无，"weekly":周卡会员
        subscription_expire_time: number
        coins: number
        bonus: number
        rewards: number
        coupon: number
        unlocked_episode: number
      }
    }
  }
}
