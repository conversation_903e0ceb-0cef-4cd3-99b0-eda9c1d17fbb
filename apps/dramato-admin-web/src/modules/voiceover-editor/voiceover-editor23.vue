<template>
  <div class="voiceover-editor">
    <div class="toolbar">
      <el-button @click="importVideo">导入视频</el-button>
      <el-button @click="importAudio">导入音频</el-button>
      <el-button @click="addSubtitleTrack">添加字幕轨道</el-button>
      <el-button @click="addAudioTrack">添加音频轨道</el-button>
      <el-button @click="exportData">导出数据</el-button>
      <el-button @click="playPreview" :disabled="!tracks.length">
        {{ isPlaying ? '暂停' : '播放' }}
      </el-button>
      <el-button 
        @click="splitClipAtCurrentTime" 
        :disabled="!canSplitCurrentClip"
        title="在当前播放线位置分割选中的轨道块">
        分割轨道
      </el-button>
      <div class="zoom-control">
        <el-slider v-model="zoomLevel" :min="0.1" :max="10" :step="0.1" @input="handleZoom" />
      </div>
    </div>
    
    <div class="timeline-container" ref="timelineContainer">
      <div 
        v-if="showTimeIndicator"
        class="time-indicator" 
        :class="{ 'playing-indicator': isPlaying }"
        :style="{ left: `${currentTime * canvasState.pixelsPerSecond + canvasState.headerWidth}px` }"
      ></div>
      <canvas ref="timelineCanvas" @mousedown="handleMouseDown" @mouseup="handleMouseUp" @dblclick="handleDoubleClick" @contextmenu="handleContextMenu"></canvas>
    </div>

    <div class="preview-panel">
      <div class="video-container">
        <video ref="videoPlayer" controls>
          <source :src="videoUrl" type="video/mp4">
        </video>
      </div>
    </div>

    <el-dialog v-model="subtitleDialog.visible" title="编辑字幕" width="500px">
      <el-form>
        <el-form-item label="字幕内容">
          <el-input
            v-model="subtitleDialog.content"
            type="textarea"
            rows="3"
            @input="handleSubtitleInput"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="subtitleDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveSubtitle">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 基础状态
const timelineCanvas = ref<HTMLCanvasElement | null>(null)
const timelineContainer = ref<HTMLElement | null>(null)
const videoPlayer = ref<HTMLVideoElement | null>(null)
const zoomLevel = ref(1)
const videoUrl = ref('')
const isDragging = ref(false)
const currentTime = ref(0)
const videoDuration = ref(0)

// 轨道数据类型定义
type TrackType = 'video' | 'audio' | 'subtitle'

// 重新设计数据结构，支持一个轨道有多个片段
type Clip = {
  id: string
  startTime: number
  endTime: number
  content?: string
  file?: File
}

type Track = {
  id: string
  type: TrackType
  clips: Clip[]
  upButton?: { x: number, y: number, width: number, height: number }
  downButton?: { x: number, y: number, width: number, height: number }
}

// 修改 tracks 的初始化，按照字幕、音频、视频的顺序添加默认轨道
const tracks = ref<Track[]>([
  // 字幕轨道
  {
    id: 'subtitle-track-1',
    type: 'subtitle',
    clips: []
  },
  // 音频轨道
  {
    id: 'audio-track-1',
    type: 'audio',
    clips: []
  },
  // 主视频轨道
  {
    id: 'video-track-1',
    type: 'video',
    clips: []
  }
])

// 或者，如果您希望在 onMounted 中创建轨道，可以这样修改：
// const tracks = ref<Track[]>([])

// onMounted(() => {
//   // 初始化编辑器
//   if (timelineCanvas.value && timelineContainer.value) {
//     // 设置画布尺寸
//     updateCanvasSize()
//     
//     // 添加默认轨道
//     addDefaultTracks()
//     
//     // 绘制初始内容
//     updateCanvas()
//     
//     // 其他初始化代码...
//   }
// })

// 添加默认轨道的函数
const addDefaultTracks = () => {
  // 清空现有轨道
  tracks.value = []
  
  // 添加字幕轨道
  tracks.value.push({
    id: 'subtitle-track-1',
    type: 'subtitle',
    clips: []
  })
  
  // 添加音频轨道
  tracks.value.push({
    id: 'audio-track-1',
    type: 'audio',
    clips: []
  })
  
  // 添加主视频轨道
  tracks.value.push({
    id: 'video-track-1',
    type: 'video',
    clips: []
  })
  
  // 更新画布
  updateCanvas()
}

// 如果您已经有添加各种轨道的函数，也可以直接调用它们：
// onMounted(() => {
//   // 初始化编辑器
//   if (timelineCanvas.value && timelineContainer.value) {
//     // 设置画布尺寸
//     updateCanvasSize()
//     
//     // 添加默认轨道
//     addVideoTrack()
//     addAudioTrack()
//     addSubtitleTrack()
//     
//     // 绘制初始内容
//     updateCanvas()
//     
//     // 其他初始化代码...
//   }
// })

// 画布相关参数
const canvasState = reactive({
  width: 0,
  height: 0,
  pixelsPerSecond: 100,
  trackHeight: 60,
  headerWidth: 100, // 轨道标题宽度
  timeScaleHeight: 30, // 时间刻度高度 - 这个值必须保持不变
})

// 颜色配置
const colors = {
  video: '#4a9eff',
  audio: '#50c14e',
  subtitle: '#f5a623',
  background: '#f5f5f5',
  timeline: '#666',
  text: '#333'
}

// 添加新的状态
const isPlaying = ref(false)
const selectedTrack = ref<Track | null>(null)
const audioContext = ref<AudioContext | null>(null)
const audioBuffers = new Map<string, AudioBuffer>()
const audioSources = new Map<string, AudioBufferSourceNode>()

// 字幕编辑对话框状态
const subtitleDialog = reactive({
  visible: false,
  content: '',
  trackId: '',
  clipId: ''
})

// 添加拖拽操作类型
const DragType = {
  NONE: 'none',
  MOVE: 'move',
  RESIZE_LEFT: 'resize-left',
  RESIZE_RIGHT: 'resize-right'
}

// 修改拖拽状态，增加clipId
const dragState = reactive({
  type: DragType.NONE,
  trackId: '',
  clipId: '',
  initialMouseX: 0,
  initialStartTime: 0,
  initialEndTime: 0
})

// 添加轨道拖拽相关状态
const trackDragState = reactive({
  isDragging: false,
  trackIndex: -1,
  startY: 0,
  offsetY: 0,
  ghostTrack: null as Track | null,
  targetIndex: -1
})

// 存储最后一次的鼠标事件
let lastMouseEvent: MouseEvent | null = null

// 添加选中片段状态
const selectedClip = reactive({
  trackId: '',
  clipId: ''
});

// 判断当前是否可以分割选中的片段
const canSplitCurrentClip = computed(() => {
  if (!selectedClip.trackId || !selectedClip.clipId) return false;
  
  const track = tracks.value.find(t => t.id === selectedClip.trackId);
  if (!track) return false;
  
  const clip = track.clips.find(c => c.id === selectedClip.clipId);
  if (!clip) return false;
  
  // 播放线必须在片段内部才能分割
  return currentTime.value > clip.startTime && currentTime.value < clip.endTime;
});

// 更新鼠标样式函数
const updateCursor = (e: MouseEvent) => {
  if (!timelineCanvas.value) return
  
  const rect = timelineCanvas.value.getBoundingClientRect()
  const mouseX = e.clientX - rect.left
  const mouseY = e.clientY - rect.top - canvasState.timeScaleHeight
  
  if (mouseY < 0) {
    timelineCanvas.value.style.cursor = 'default'
    return
  }
  
  const trackIndex = Math.floor(mouseY / canvasState.trackHeight)
  if (trackIndex < 0 || trackIndex >= tracks.value.length) {
    timelineCanvas.value.style.cursor = 'default'
    return
  }
  
  const track = tracks.value[trackIndex]
  const resizeHandleWidth = 5 // 调整手柄宽度
  
  // 检查所有片段
  for (const clip of track.clips) {
    const startX = clip.startTime * canvasState.pixelsPerSecond + canvasState.headerWidth
    const endX = clip.endTime * canvasState.pixelsPerSecond + canvasState.headerWidth
    
    if (Math.abs(mouseX - startX) <= resizeHandleWidth) {
      // 左侧调整手柄
      timelineCanvas.value.style.cursor = 'w-resize'
      return
    } else if (Math.abs(mouseX - endX) <= resizeHandleWidth) {
      // 右侧调整手柄
      timelineCanvas.value.style.cursor = 'e-resize'
      return
    } else if (mouseX >= startX && mouseX <= endX) {
      // 片段内部
      timelineCanvas.value.style.cursor = 'move'
      return
    }
  }
  
  // 如果没有命中任何片段
  timelineCanvas.value.style.cursor = 'default'
}

// 添加鼠标按下事件处理函数
const handleMouseDown = (e: MouseEvent) => {
  if (!timelineCanvas.value) return
  
  const rect = timelineCanvas.value.getBoundingClientRect()
  const mouseX = e.clientX - rect.left
  const mouseY = e.clientY - rect.top
  
  // 检查是否点击在时间轴刻度区域
  if (mouseY < canvasState.timeScaleHeight) {
    // 计算对应的时间点
    if (mouseX >= canvasState.headerWidth) {
      const clickTime = (mouseX - canvasState.headerWidth) / canvasState.pixelsPerSecond
      
      // 限制在视频时长范围内
      if (clickTime >= 0 && (!videoDuration.value || clickTime <= videoDuration.value)) {
        // 设置当前时间
        currentTime.value = clickTime
        
        // 如果视频已加载，则更新视频位置
        if (videoPlayer.value) {
          videoPlayer.value.currentTime = clickTime
        }
        
        // 更新画布
        updateCanvas()
      }
    }
    return
  }
  
  // 判断是否点击在轨道标题区域
  if (mouseX < canvasState.headerWidth && mouseY >= canvasState.timeScaleHeight) {
    const trackIndex = Math.floor((mouseY - canvasState.timeScaleHeight) / canvasState.trackHeight)
    if (trackIndex >= 0 && trackIndex < tracks.value.length) {
      // 启动轨道拖拽
      e.preventDefault()
      
      trackDragState.isDragging = true
      trackDragState.trackIndex = trackIndex
      trackDragState.startY = mouseY
      trackDragState.offsetY = 0
      trackDragState.ghostTrack = { ...tracks.value[trackIndex] }
      trackDragState.targetIndex = trackIndex
      
      // 更改鼠标样式
      if (timelineCanvas.value) {
        timelineCanvas.value.style.cursor = 'grabbing'
      }
      
      return
    }
  }
  
  // 如果不是拖拽轨道标题，则处理片段拖拽逻辑
  const trackY = mouseY - canvasState.timeScaleHeight
  if (trackY < 0) return
  
  const trackIndex = Math.floor(trackY / canvasState.trackHeight)
  if (trackIndex < 0 || trackIndex >= tracks.value.length) return
  
  const track = tracks.value[trackIndex]
  const resizeHandleWidth = 5 // 调整手柄宽度
  
  // 检查是否点击在片段上
  let clickedOnClip = false
  for (const clip of track.clips) {
    const startX = clip.startTime * canvasState.pixelsPerSecond + canvasState.headerWidth
    const endX = clip.endTime * canvasState.pixelsPerSecond + canvasState.headerWidth
    
    if (Math.abs(mouseX - startX) <= resizeHandleWidth) {
      // 左侧调整手柄
      e.preventDefault()
      isDragging.value = true
      
      dragState.type = DragType.RESIZE_LEFT
      dragState.trackId = track.id
      dragState.clipId = clip.id
      dragState.initialMouseX = mouseX
      dragState.initialStartTime = clip.startTime
      dragState.initialEndTime = clip.endTime
      
      // 设置当前选中的片段
      selectedClip.trackId = track.id
      selectedClip.clipId = clip.id
      
      clickedOnClip = true
      break
    } else if (Math.abs(mouseX - endX) <= resizeHandleWidth) {
      // 右侧调整手柄
      e.preventDefault()
      isDragging.value = true
      
      dragState.type = DragType.RESIZE_RIGHT
      dragState.trackId = track.id
      dragState.clipId = clip.id
      dragState.initialMouseX = mouseX
      dragState.initialStartTime = clip.startTime
      dragState.initialEndTime = clip.endTime
      
      // 设置当前选中的片段
      selectedClip.trackId = track.id
      selectedClip.clipId = clip.id
      
      clickedOnClip = true
      break
    } else if (mouseX >= startX && mouseX <= endX) {
      // 移动整个片段
      e.preventDefault()
      isDragging.value = true
      
      dragState.type = DragType.MOVE
      dragState.trackId = track.id
      dragState.clipId = clip.id
      dragState.initialMouseX = mouseX
      dragState.initialStartTime = clip.startTime
      dragState.initialEndTime = clip.endTime
      
      // 设置当前选中的片段
      selectedClip.trackId = track.id
      selectedClip.clipId = clip.id
      
      clickedOnClip = true
      break
    }
  }
  
  // 如果点击在轨道区域但不是在片段上，清除选中状态并设置当前时间
  if (!clickedOnClip && mouseX >= canvasState.headerWidth) {
    selectedClip.trackId = ''
    selectedClip.clipId = ''
    
    const clickTime = (mouseX - canvasState.headerWidth) / canvasState.pixelsPerSecond
    
    // 限制在视频时长范围内
    if (clickTime >= 0 && (!videoDuration.value || clickTime <= videoDuration.value)) {
      currentTime.value = clickTime
      
      if (videoPlayer.value) {
        videoPlayer.value.currentTime = clickTime
      }
      
      updateCanvas()
    }
  }
  
  updateCanvas()
}

// 添加或修改鼠标移动处理函数以支持所有类型轨道的拖动，并防止重叠
const handleMouseMove = (e: MouseEvent) => {
  if (!timelineCanvas.value) return
  
  const rect = timelineCanvas.value.getBoundingClientRect()
  const mouseX = e.clientX - rect.left
  
  // 只有在拖动状态时才处理片段移动
  if (isDragging.value && dragState.type !== DragType.NONE) {
    e.preventDefault()
    
    // 计算时间差值
    const deltaX = mouseX - dragState.initialMouseX
    const deltaTime = deltaX / canvasState.pixelsPerSecond
    
    // 找到当前正在拖动的轨道和片段
    const track = tracks.value.find(t => t.id === dragState.trackId)
    if (!track) return
    
    const clipIndex = track.clips.findIndex(c => c.id === dragState.clipId)
    if (clipIndex === -1) return
    
    const clip = track.clips[clipIndex]
    
    // 处理不同类型的拖动
    if (dragState.type === DragType.MOVE) {
      // 移动整个片段
      let newStartTime = Math.max(0, dragState.initialStartTime + deltaTime)
      const clipDuration = dragState.initialEndTime - dragState.initialStartTime
      let newEndTime = newStartTime + clipDuration
      
      // 检查是否会与同轨道的其他片段重叠
      const otherClips = track.clips.filter((c, i) => i !== clipIndex)
      
      // 调整位置避免重叠
      for (const otherClip of otherClips) {
        // 如果新片段的右侧与其他片段的左侧重叠
        if (newEndTime > otherClip.startTime && newStartTime < otherClip.startTime) {
          // 贴合到其他片段的左侧
          newEndTime = otherClip.startTime
          newStartTime = newEndTime - clipDuration
          break
        }
        
        // 如果新片段的左侧与其他片段的右侧重叠
        if (newStartTime < otherClip.endTime && newEndTime > otherClip.endTime) {
          // 贴合到其他片段的右侧
          newStartTime = otherClip.endTime
          newEndTime = newStartTime + clipDuration
          break
        }
        
        // 如果新片段完全包含或被包含于其他片段
        if ((newStartTime >= otherClip.startTime && newEndTime <= otherClip.endTime) ||
            (newStartTime <= otherClip.startTime && newEndTime >= otherClip.endTime)) {
          // 恢复到原始位置
          return
        }
      }
      
      // 确保不超出视频总长度
      if (videoDuration.value && newEndTime > videoDuration.value) {
        newEndTime = videoDuration.value
        newStartTime = newEndTime - clipDuration
      }
      
      // 更新片段位置
      clip.startTime = newStartTime
      clip.endTime = newEndTime
    } else if (dragState.type === DragType.RESIZE_LEFT) {
      // 调整左侧边界
      let newStartTime = Math.max(0, dragState.initialStartTime + deltaTime)
      
      // 检查是否会与同轨道的前一个片段重叠
      const otherClips = track.clips.filter((c, i) => i !== clipIndex)
      
      // 寻找左侧最近的片段
      const leftClips = otherClips.filter(c => c.endTime <= clip.endTime)
      if (leftClips.length > 0) {
        const nearestLeftClip = leftClips.reduce((nearest, current) => 
          current.endTime > nearest.endTime ? current : nearest, leftClips[0])
        
        // 确保不会与左侧片段重叠
        newStartTime = Math.max(newStartTime, nearestLeftClip.endTime)
      }
      
      // 确保不超过右侧边界且保持最小长度
      if (newStartTime < clip.endTime - 0.1) {
        clip.startTime = newStartTime
      }
    } else if (dragState.type === DragType.RESIZE_RIGHT) {
      // 调整右侧边界
      let newEndTime = dragState.initialEndTime + deltaTime
      
      // 检查是否会与同轨道的后一个片段重叠
      const otherClips = track.clips.filter((c, i) => i !== clipIndex)
      
      // 寻找右侧最近的片段
      const rightClips = otherClips.filter(c => c.startTime >= clip.startTime)
      if (rightClips.length > 0) {
        const nearestRightClip = rightClips.reduce((nearest, current) => 
          current.startTime < nearest.startTime ? current : nearest, rightClips[0])
        
        // 确保不会与右侧片段重叠
        newEndTime = Math.min(newEndTime, nearestRightClip.startTime)
      }
      
      // 确保不低于左侧边界且保持最小长度
      if (newEndTime > clip.startTime + 0.1) {
        // 还要确保不超出视频总长度
        if (videoDuration.value) {
          clip.endTime = Math.min(newEndTime, videoDuration.value)
        } else {
          clip.endTime = newEndTime
        }
      }
    }
    
    // 重新绘制画布
    updateCanvas()
  }
  
  // 处理轨道拖拽预览
  if (trackDragState.isDragging) {
    const mouseY = e.clientY - rect.top
    trackDragState.offsetY = mouseY - trackDragState.startY
    
    // 计算目标位置
    const targetY = mouseY - canvasState.timeScaleHeight
    const targetIndex = Math.floor(targetY / canvasState.trackHeight)
    
    // 确保在有效范围内
    if (targetIndex >= 0 && targetIndex < tracks.value.length) {
      trackDragState.targetIndex = targetIndex
    }
    
    updateCanvas()
  }
}

// 确保 handleMouseUp 函数正确重置拖动状态
const handleMouseUp = (e: MouseEvent) => {
  // 重置拖拽状态，无论轨道类型
  if (isDragging.value) {
    isDragging.value = false
    dragState.type = DragType.NONE
    dragState.trackId = ''
    dragState.clipId = ''
    
    // 更新画布
    updateCanvas()
  }
  
  // 其余代码...
}

// 完全分离画布更新和尺寸计算
const updateCanvas = () => {
  if (!timelineCanvas.value) return
  
  // 重要：移除对 updateCanvasSize 的调用
  // 画布绘制不应该改变画布尺寸
  
  // 直接调用绘制函数
  drawTimeline()
  
  // 如果在 drawTimeline 中有固定的时间指示器绘制，可以在这里清除它
  if (currentTime.value !== null && currentTime.value < 0.05) {
    // 清除0秒位置的指示器
    const ctx = timelineCanvas.value?.getContext('2d')
    if (ctx) {
      const x = canvasState.headerWidth // 0秒位置
      ctx.clearRect(x - 2, 0, 4, canvasState.height) // 清除一个小区域
      
      // 重新绘制垂直分隔线
      ctx.strokeStyle = '#ccc'
      ctx.lineWidth = 1
      ctx.setLineDash([])
      ctx.beginPath()
      ctx.moveTo(canvasState.headerWidth, 0)
      ctx.lineTo(canvasState.headerWidth, canvasState.height)
      ctx.stroke()
    }
  }
}

// 修改 updateCanvasSize 函数，动态调整画布宽度
const updateCanvasSize = () => {
  if (!timelineCanvas.value || !timelineContainer.value) return

  // 获取容器宽度
  const containerWidth = timelineContainer.value.clientWidth

  // 计算内容所需的宽度
  const totalDuration = videoDuration.value || 30 // 使用视频时长或默认30秒
  const contentWidth = totalDuration * canvasState.pixelsPerSecond + canvasState.headerWidth

  // 使用较大的值作为画布宽度
  const canvasWidth = Math.max(containerWidth, contentWidth)

  // 计算轨道部分所需的高度
  const tracksHeight = tracks.value.length * canvasState.trackHeight
  const totalHeight = canvasState.timeScaleHeight + (tracksHeight > 0 ? tracksHeight : 70)

  // 更新画布尺寸
  timelineCanvas.value.width = canvasWidth
  timelineCanvas.value.height = totalHeight
  canvasState.width = canvasWidth
  canvasState.height = totalHeight

  // 设置画布样式
  timelineCanvas.value.style.width = `${canvasWidth}px`
  timelineCanvas.value.style.height = `${totalHeight}px`
}

// 修改初始化函数和事件处理
const initializeEditor = () => {
  console.log('初始化编辑器')
  
  // 初始化音频上下文
  try {
    audioContext.value = new (window.AudioContext || (window as any).webkitAudioContext)()
  } catch (e) {
    console.error('音频上下文初始化失败:', e)
  }

  if (timelineCanvas.value && timelineContainer.value) {
    // 首先设置画布尺寸
    updateCanvasSize()
    // 然后绘制内容
    drawTimeline()
  }
  
  // 设置事件监听
  window.addEventListener('resize', () => {
    // 窗口大小变化时，先更新尺寸，再绘制
    updateCanvasSize()
    updateCanvas()
  })
  
  if (timelineContainer.value) {
    timelineContainer.value.addEventListener('wheel', handleWheelZoom, { passive: false })
    timelineContainer.value.addEventListener('mousemove', (e: MouseEvent) => {
      // 只保存鼠标事件，不直接触发尺寸更新
      lastMouseEvent = e
      // 只更新绘制，不更新尺寸
      updateCanvas()
    })
  }
}

// 在修改轨道后，需要同时更新尺寸和画布
const addSubtitleTrack = () => {
  const trackId = `subtitle-${Date.now()}`
  
  const subtitleTrack: Track = {
    id: trackId,
    type: 'subtitle',
    clips: []
  }
  
  tracks.value.push(subtitleTrack)
  
  // 先更新尺寸，再更新画布
  updateCanvasSize()
  updateCanvas()
}

// 添加音频轨道
const addAudioTrack = () => {
  const trackId = `audio-${Date.now()}`
  
  const audioTrack: Track = {
    id: trackId,
    type: 'audio',
    clips: []
  }
  
  tracks.value.push(audioTrack)
  
  // 更新画布大小和重绘
  updateCanvasSize()
  updateCanvas()
}

// 导出数据
const exportData = () => {
  const exportData = {
    tracks: tracks.value.map(track => ({
      id: track.id,
      type: track.type,
      clips: track.clips.map(clip => ({
        id: clip.id,
        startTime: clip.startTime,
        endTime: clip.endTime,
        content: clip.content
      }))
    }))
  }
  
  const dataStr = JSON.stringify(exportData, null, 2)
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
  
  const exportFileName = `voiceover-project-${new Date().toISOString().slice(0, 10)}.json`
  
  const linkElement = document.createElement('a')
  linkElement.setAttribute('href', dataUri)
  linkElement.setAttribute('download', exportFileName)
  linkElement.click()
}

// 处理字幕输入
const handleSubtitleInput = (value: string) => {
  subtitleDialog.content = value
}

// 绘制视频片段
const drawVideoClip = (ctx: CanvasRenderingContext2D, clip: Clip, startX: number, y: number, width: number) => {
  // 确定是否正在拖拽这个片段
  const isCurrentDrag = isDragging.value && 
                      dragState.clipId === clip.id
  
  // 绘制片段背景
  ctx.fillStyle = isCurrentDrag ? '#ffd700' : '#4a9eff44'
  ctx.fillRect(
    startX,
    y + 5,
    width,
    canvasState.trackHeight - 10
  )
  
  // 为拖拽中的片段添加边框
  ctx.strokeStyle = colors.video
  ctx.lineWidth = 2
  ctx.strokeRect(
    startX,
    y + 5,
    width,
    canvasState.trackHeight - 10
  )
  
  // 显示文本内容
  if (clip.content) {
    ctx.fillStyle = '#333'
    ctx.font = '12px Arial'
    ctx.textAlign = 'left'
    
    // 显示最多前20个字符
    const displayText = clip.content.length > 20 
      ? clip.content.substring(0, 20) + '...' 
      : clip.content
      
    ctx.fillText(
      displayText,
      startX + 5,
      y + canvasState.trackHeight / 2 + 5
    )
  }
}

// 绘制音频片段
const drawAudioClip = (ctx: CanvasRenderingContext2D, clip: Clip, startX: number, y: number, width: number) => {
  // 确定是否正在拖拽这个片段
  const isCurrentDrag = isDragging.value && 
                      dragState.clipId === clip.id
  
  // 绘制片段背景
  ctx.fillStyle = isCurrentDrag ? '#ffd700' : '#50c14e44'
  ctx.fillRect(
    startX,
    y + 5,
    width,
    canvasState.trackHeight - 10
  )
  
  // 绘制边框
  ctx.strokeStyle = colors.audio
  ctx.lineWidth = 2
  ctx.strokeRect(
    startX,
    y + 5,
    width,
    canvasState.trackHeight - 10
  )
  
  // 绘制音频波形
  ctx.strokeStyle = colors.audio
  ctx.lineWidth = 1
  
  const waveHeight = canvasState.trackHeight - 20
  const centerY = y + canvasState.trackHeight / 2
  
  // 简单的波形图案
  ctx.beginPath()
  for (let i = 0; i < width; i += 5) {
    const x = startX + i
    const amplitude = Math.sin(i * 0.1) * waveHeight / 4
    
    ctx.moveTo(x, centerY)
    ctx.lineTo(x, centerY + amplitude)
  }
  ctx.stroke()
  
  // 显示音频片段信息
  const duration = clip.endTime - clip.startTime
  ctx.fillStyle = '#333'
  ctx.font = 'bold 12px Arial'
  ctx.textAlign = 'left'
  ctx.fillText(
    `音频: ${formatTime(duration)}`,
    startX + 5,
    y + 20
  )
}

// 绘制字幕片段
const drawSubtitleClip = (ctx: CanvasRenderingContext2D, clip: Clip, startX: number, y: number, width: number) => {
  // 确定是否正在拖拽这个片段
  const isCurrentDrag = isDragging.value && 
                      dragState.clipId === clip.id
  
  // 绘制片段背景
  ctx.fillStyle = isCurrentDrag ? '#ffd700' : '#f5a62344'
  ctx.fillRect(
    startX,
    y + 5,
    width,
    canvasState.trackHeight - 10
  )
  
  // 为拖拽中的片段添加边框
  ctx.strokeStyle = colors.subtitle
  ctx.lineWidth = 2
  ctx.strokeRect(
    startX,
    y + 5,
    width,
    canvasState.trackHeight - 10
  )
  
  // 显示文本内容
  if (clip.content) {
    ctx.fillStyle = '#333'
    ctx.font = '12px Arial'
    ctx.textAlign = 'left'
    
    // 显示最多前20个字符
    const displayText = clip.content.length > 20 
      ? clip.content.substring(0, 20) + '...' 
      : clip.content
      
    ctx.fillText(
      displayText,
      startX + 5,
      y + canvasState.trackHeight / 2 + 5
    )
  }
}

// 修改 drawTrack 函数，实现选中片段高亮和未选中片段暗化
const drawTrack2 = (ctx: CanvasRenderingContext2D, track: Track, index: number) => {
  // 保存上下文状态
  ctx.save()
  
  // 计算轨道Y位置
  const y = canvasState.timeScaleHeight + (index * canvasState.trackHeight)
  
  // 绘制轨道背景
  ctx.fillStyle = colors.background
  ctx.fillRect(canvasState.headerWidth, y, canvasState.width - canvasState.headerWidth, canvasState.trackHeight)
  
  // 绘制轨道标题背景
  ctx.fillStyle = '#e8e8e8'
  ctx.fillRect(0, y, canvasState.headerWidth, canvasState.trackHeight)
  
  // 绘制轨道边框
  ctx.strokeStyle = '#ddd'
  ctx.lineWidth = 1
  ctx.strokeRect(canvasState.headerWidth, y, canvasState.width - canvasState.headerWidth, canvasState.trackHeight)
  
  // 绘制轨道标题
  ctx.fillStyle = colors.text
  ctx.font = '12px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText(
    getTrackName(track, index),
    canvasState.headerWidth / 2,
    y + canvasState.trackHeight / 2
  )
  
  // 绘制轨道内的所有片段
  track.clips.forEach(clip => {
    const startX = clip.startTime * canvasState.pixelsPerSecond + canvasState.headerWidth
    const endX = clip.endTime * canvasState.pixelsPerSecond + canvasState.headerWidth
    const clipWidth = endX - startX
    
    // 检查是否是当前选中的片段
    const isSelected = (track.id === selectedClip.trackId && clip.id === selectedClip.clipId)
    
    // 根据轨道类型选择基础颜色
    let baseColor = colors[track.type]
    
    // 根据是否选中调整颜色
    if (isSelected) {
      // 选中的片段使用更亮的颜色
      ctx.fillStyle = track.type === 'video' ? '#5c9cff' : 
                     track.type === 'audio' ? '#ff9c5c' : '#9cff5c'
    } else {
      // 未选中的片段使用暗化的颜色
      ctx.fillStyle = track.type === 'video' ? 'rgba(0, 100, 200, 0.5)' : 
                     track.type === 'audio' ? 'rgba(200, 100, 0, 0.5)' : 'rgba(100, 200, 0, 0.5)'
    }
    
    // 绘制片段背景
    ctx.fillRect(
      startX,
      y + 5,
      clipWidth,
      canvasState.trackHeight - 10
    )
    
    // 为选中的片段添加高亮边框和阴影效果
    if (isSelected) {
      // 添加阴影效果
      ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
      ctx.shadowBlur = 5
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 0
      
      // 绘制高亮边框
      ctx.strokeStyle = '#ffffff'
      ctx.lineWidth = 2
      ctx.strokeRect(
        startX,
        y + 5,
        clipWidth,
        canvasState.trackHeight - 10
      )
      
      // 重置阴影
      ctx.shadowColor = 'transparent'
      ctx.shadowBlur = 0
      
      // 如果当前播放线在选中片段内，绘制可能的分割线提示
      if (currentTime.value > clip.startTime && currentTime.value < clip.endTime) {
        const indicatorX = currentTime.value * canvasState.pixelsPerSecond + canvasState.headerWidth
        
        // 绘制分割提示线
        ctx.strokeStyle = '#ffffff'
        ctx.setLineDash([4, 2])
        ctx.beginPath()
        ctx.moveTo(indicatorX, y)
        ctx.lineTo(indicatorX, y + canvasState.trackHeight)
        ctx.stroke()
        ctx.setLineDash([])
      }
    }
    
    // 绘制片段内容（根据轨道类型）
    ctx.fillStyle = isSelected ? '#ffffff' : '#f0f0f0' // 文本颜色
    ctx.font = '12px Arial'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'middle'
    
    const padding = 5
    
    if (track.type === 'subtitle' && clip.content) {
      // 显示字幕内容
      let displayText = clip.content
      if (ctx.measureText(displayText).width > clipWidth - 10) {
        displayText = displayText.substring(0, 15) + '...'
      }
      ctx.fillText(displayText, startX + padding, y + canvasState.trackHeight / 2)
    } else if (track.type === 'audio' && clip.file) {
      // 显示音频文件名
      const fileName = clip.file.name
      const displayName = fileName.length > 15 ? fileName.substring(0, 15) + '...' : fileName
      ctx.fillText(displayName, startX + padding, y + canvasState.trackHeight / 2)
    } else if (track.type === 'video') {
      // 显示视频时间范围
      const timeText = `${formatTime(clip.startTime)} - ${formatTime(clip.endTime)}`
      ctx.fillText(timeText, startX + padding, y + canvasState.trackHeight / 2)
    }
    
    // 绘制调整手柄
    drawResizeHandles(ctx, startX, endX, y)
  })
  
  // 恢复上下文状态
  ctx.restore()
}

// 获取轨道显示名称
const getTrackDisplayName = (track: Track, index: number): string => {
  // 对字幕轨道特殊处理
  if (track.type === 'subtitle') {
    return '字幕'
  }
  
  // 其他轨道显示类型加序号
  const trackTypeName = track.type === 'video' ? '视频' : '音频'
  return `${trackTypeName} ${index + 1}`
}

// 修改 drawTimeline 函数，完全移除对 drawTimeIndicator 的调用
const drawTimeline = () => {
  if (!timelineCanvas.value) return
  
  const ctx = timelineCanvas.value.getContext('2d')
  if (!ctx) return
  
  // 清除画布
  ctx.clearRect(0, 0, canvasState.width, canvasState.height)
  
  // 1. 绘制时间刻度
  drawTimeScale(ctx)
  
  // 2. 绘制轨道背景和标题
  // ...
  
  // 3. 绘制所有轨道
  tracks.value.forEach((track, i) => {
    drawTrack(ctx, track, i)
  })
  
  // 4. 绘制鼠标悬停指示器
  drawHoverIndicator(ctx)
  
  // 5. 手动绘制时间指示器，但只在非0秒位置
  if (currentTime.value !== null && currentTime.value > 0.05) {
    // 计算当前时间位置
    const x = currentTime.value * canvasState.pixelsPerSecond + canvasState.headerWidth
    
    // 绘制时间指示线
    ctx.save()
    ctx.strokeStyle = isPlaying.value ? '#ff3333' : '#ff0000'
    ctx.lineWidth = 2
    
    // 设置虚线样式
    if (!isPlaying.value) {
      ctx.setLineDash([5, 3]) // 非播放时使用虚线
    }
    
    // 绘制从顶部到底部的直线
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, canvasState.height)
    ctx.stroke()
    
    // 重置虚线设置
    ctx.setLineDash([])
    
    // 显示当前时间
    ctx.fillStyle = isPlaying.value ? '#ff3333' : '#ff0000'
    ctx.font = '10px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(formatTime(currentTime.value), x, 25)
    
    ctx.restore()
  }
}

// 添加缺失的辅助函数 drawResizeHandles
const drawResizeHandles = (ctx: CanvasRenderingContext2D, startX: number, endX: number, y: number) => {
  // 绘制左侧调整手柄
  ctx.fillStyle = '#333'
  ctx.fillRect(
    startX - 2,
    y + 5,
    4,
    canvasState.trackHeight - 10
  )
  
  // 绘制右侧调整手柄
  ctx.fillRect(
    endX - 2,
    y + 5,
    4,
    canvasState.trackHeight - 10
  )
}

// 添加滚轮缩放功能
const handleWheelZoom = (e: WheelEvent) => {
  // 阻止默认滚动行为
  e.preventDefault()
  
  // 获取当前鼠标位置下的时间点
  if (!timelineCanvas.value) return
  
  const rect = timelineCanvas.value.getBoundingClientRect()
  const mouseX = e.clientX - rect.left
  
  // 只有在时间轴区域内才进行缩放
  if (mouseX < canvasState.headerWidth) return
  
  // 计算鼠标位置对应的时间
  const timeAtMouse = (mouseX - canvasState.headerWidth) / canvasState.pixelsPerSecond
  
  // 确定缩放方向和强度
  // deltaY 为负表示向上滚动（放大），为正表示向下滚动（缩小）
  const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1
  
  // 更新缩放级别，限制在合理范围内
  zoomLevel.value = Math.max(0.1, Math.min(10, zoomLevel.value * zoomFactor))
  
  // 更新每秒像素数
  const oldPixelsPerSecond = canvasState.pixelsPerSecond
  canvasState.pixelsPerSecond = 100 * zoomLevel.value
  
  // 计算新的位置，确保鼠标下的时间点保持不变
  if (timelineContainer.value) {
    const newMouseX = timeAtMouse * canvasState.pixelsPerSecond + canvasState.headerWidth
    const scrollAdjustment = newMouseX - mouseX
    timelineContainer.value.scrollLeft += scrollAdjustment
  }
  
  // 重新绘制时间轴
  updateCanvas()
}

// 添加处理缩放的函数
const handleZoom = (value: number) => {
  // 使用滑块调整缩放级别
  zoomLevel.value = value
  
  // 更新画布像素比例
  canvasState.pixelsPerSecond = 100 * value
  
  // 更新画布
  updateCanvas()
}

// 修复导入视频功能（确保只声明一次）
const importVideo = () => {
  console.log('导入视频按钮点击')
  
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'video/mp4,video/webm,video/ogg'
  
  input.onchange = (e: Event) => {
    const target = e.target as HTMLInputElement
    if (!target.files || target.files.length === 0) {
      console.log('没有选择文件')
      return
    }
    
    console.log('已选择文件:', target.files[0].name)
    
    const file = target.files[0]
    const videoURL = URL.createObjectURL(file)
    videoUrl.value = videoURL
    
    // 创建视频元素以获取视频时长
    const tempVideo = document.createElement('video')
    tempVideo.src = videoURL
    
    tempVideo.onloadedmetadata = () => {
      console.log('视频元数据加载完成, 时长:', tempVideo.duration)
      // 获取视频时长
      const duration = tempVideo.duration
      videoDuration.value = duration
      
      // 创建视频轨道
      const videoTrackId = `video-${Date.now()}`
      
      // 检查是否已有视频轨道，如果有则替换
      const existingVideoTrackIndex = tracks.value.findIndex(t => t.type === 'video')
      
      const videoTrack: Track = {
        id: videoTrackId,
        type: 'video',
        clips: [
          {
            id: `video-clip-${Date.now()}`,
            startTime: 0,
            endTime: duration,
            file
          }
        ]
      }
      
      if (existingVideoTrackIndex >= 0) {
        // 替换现有视频轨道
        tracks.value[existingVideoTrackIndex] = videoTrack
      } else {
        // 添加新视频轨道
        tracks.value.unshift(videoTrack) // 将视频轨道添加到顶部
      }
      
      // 如果视频播放器已加载，则加载视频
      if (videoPlayer.value) {
        videoPlayer.value.src = videoURL
        videoPlayer.value.load()
      }
      
      // 调整画布大小并重绘
      updateCanvasSize()
      updateCanvas()
    }
  }
  
  input.click()
}

// 添加播放/暂停功能
const playPreview = () => {
  console.log('播放/暂停按钮点击')
  
  if (!videoPlayer.value) {
    console.error('视频播放器元素未找到')
    return
  }
  
  try {
    if (isPlaying.value) {
      // 暂停
      videoPlayer.value.pause()
      
      // 停止音频
      stopAllAudio()
      
      isPlaying.value = false
      console.log('停止播放')
    } else {
      // 播放
      // 确保音频上下文已初始化
      if (!audioContext.value) {
        audioContext.value = new (window.AudioContext || (window as any).webkitAudioContext)()
      }
      
      // 将当前时间设置为时间指示器位置
      if (currentTime.value !== null && currentTime.value !== undefined) {
        videoPlayer.value.currentTime = currentTime.value
      }
      
      // 启动视频
      const playPromise = videoPlayer.value.play()
      
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('视频播放开始')
            isPlaying.value = true
            playAudioTracks()
            
            // 开始更新时间指示器
            requestAnimationFrame(updateTimeIndicator)
          })
          .catch(error => {
            console.error('播放视频失败:', error)
            // 可能是由于用户交互限制，如自动播放策略
            alert('播放失败，请再次点击播放按钮')
          })
      }
    }
  } catch (error) {
    console.error('播放/暂停处理时出错:', error)
  }
}

// 停止所有音频
const stopAllAudio = () => {
  console.log('停止所有音频')
  audioSources.forEach(source => {
    try {
      source.stop()
    } catch (e) {
      // 忽略已经停止的音频源
      console.log('停止音频源时出现非关键错误', e)
    }
  })
  audioSources.clear()
}

// 简化悬浮指示器绘制，移除顶部三角形
const drawHoverIndicator = (ctx: CanvasRenderingContext2D) => {
  if (!lastMouseEvent || !timelineCanvas.value) return
  
  const rect = timelineCanvas.value.getBoundingClientRect()
  const mouseX = lastMouseEvent.clientX - rect.left
  
  // 只在时间轴区域内显示
  if (mouseX < canvasState.headerWidth) return
  
  // 保存当前上下文状态
  ctx.save()
  
  // 绘制悬浮线 - 简化样式，只保留虚线
  ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)' // 半透明红色
  ctx.lineWidth = 2
  
  // 设置虚线样式
  ctx.setLineDash([5, 3]) // 5像素实线，3像素空白
  
  // 绘制从顶部到底部的虚线
  ctx.beginPath()
  ctx.moveTo(mouseX, 0)
  ctx.lineTo(mouseX, canvasState.height)
  ctx.stroke()
  
  // 重置虚线设置
  ctx.setLineDash([])
  
  // 显示当前悬浮位置的时间
  const hoverTime = (mouseX - canvasState.headerWidth) / canvasState.pixelsPerSecond
  if (hoverTime >= 0) {
    ctx.fillStyle = 'rgba(255, 0, 0, 0.5)'
    ctx.font = '10px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(formatTime(hoverTime), mouseX, 25)
  }
  
  // 恢复上下文状态
  ctx.restore()
}

// 添加缺失的 getTrackName 函数
const getTrackName = (track: Track, index: number): string => {
  switch (track.type) {
    case 'video':
      return `视频轨道 ${index + 1}`;
    case 'audio':
      return `音频轨道 ${index + 1}`;
    case 'subtitle':
      return `字幕轨道 ${index + 1}`;
    default:
      return `轨道 ${index + 1}`;
  }
}

// 音频导入功能
const importAudioFile = async () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'audio/*'
  
  input.onchange = async (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (!file) return
    
    try {
      // 初始化 AudioContext
      if (!audioContext.value) {
        audioContext.value = new AudioContext()
      }
      
      const arrayBuffer = await file.arrayBuffer()
      const audioBuffer = await audioContext.value.decodeAudioData(arrayBuffer)
      
      const trackId = `audio-${Date.now()}`
      audioBuffers.set(trackId, audioBuffer)
      
      // 添加音频轨道
      tracks.value.push({
        id: trackId,
        type: 'audio',
        clips: [{
          id: `clip-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
          startTime: 0,
          endTime: audioBuffer.duration,
        }]
      })
      
      updateCanvas()
    } catch (error) {
      console.error('音频导入失败:', error)
      // TODO: 显示错误提示
    }
  }
  
  input.click()
}

const playAudioTracks = () => {
  console.log('尝试播放音频轨道')
  if (!audioContext.value) {
    console.warn('音频上下文不存在')
    return
  }
  
  // 首先停止所有正在播放的音频
  stopAllAudio()
  
  // 查找所有音频轨道
  const audioTracks = tracks.value.filter(track => track.type === 'audio')
  
  for (const track of audioTracks) {
    for (const clip of track.clips) {
      // 如果当前播放位置已经超过了片段，跳过它
      if (currentTime.value >= clip.endTime) {
        continue
      }
      
      // 如果当前播放位置还没到达片段，设置延迟播放
      if (currentTime.value < clip.startTime) {
        const delay = (clip.startTime - currentTime.value) * 1000
        
        console.log(`计划延迟 ${delay}ms 后播放音频片段:`, clip.id)
        
        // 使用延迟播放
        setTimeout(() => {
          if (!isPlaying.value) return // 如果已经暂停，则不播放
          playAudioClip(track, clip)
        }, delay)
      } else {
        // 如果当前播放位置在片段中，立即开始播放
        console.log('立即播放音频片段:', clip.id)
        playAudioClip(track, clip, currentTime.value - clip.startTime)
      }
    }
  }
}

const playAudioClip = (track: Track, clip: Clip, startOffset = 0) => {
  if (!audioContext.value) {
    console.warn('音频上下文不存在')
    return
  }
  
  // 获取音频缓冲区
  const audioBuffer = audioBuffers.get(clip.id)
  if (!audioBuffer) {
    console.warn('找不到音频片段的缓冲区:', clip.id)
    return
  }
  
  try {
    const source = audioContext.value.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.value.destination)
    
    // 计算偏移和持续时间
    const offset = Math.max(0, startOffset)
    const duration = clip.endTime - clip.startTime - offset
    
    if (duration <= 0) {
      console.log('音频片段已结束，跳过')
      return
    }
    
    // 保存音频源，以便稍后停止
    audioSources.set(clip.id, source)
    
    // 开始播放，指定偏移和持续时间
    source.start(0, offset, duration)
    console.log(`开始播放音频 ${clip.id}，偏移量: ${offset}s，持续时间: ${duration}s`)
    
    // 设置音频结束时的回调
    source.onended = () => {
      audioSources.delete(clip.id)
      console.log('音频片段播放完成:', clip.id)
    }
  } catch (error) {
    console.error('播放音频片段时出错:', error)
  }
}

const updateTimeIndicator = () => {
  if (!isPlaying.value) return
  
  // 使用视频播放器的当前时间更新时间指示器
  if (videoPlayer.value) {
    currentTime.value = videoPlayer.value.currentTime
  }
  
  // 更新画布
  updateCanvas()
  
  // 继续更新
  requestAnimationFrame(updateTimeIndicator)
}

// 双击处理函数，支持添加或编辑字幕
const handleDoubleClick = (e: MouseEvent) => {
  if (!timelineCanvas.value) return
  
  const rect = timelineCanvas.value.getBoundingClientRect()
  const mouseX = e.clientX - rect.left
  const mouseY = e.clientY - rect.top - canvasState.timeScaleHeight
  
  if (mouseY < 0) return
  
  const trackIndex = Math.floor(mouseY / canvasState.trackHeight)
  if (trackIndex < 0 || trackIndex >= tracks.value.length) return
  
  const track = tracks.value[trackIndex]
  const timePosition = (mouseX - canvasState.headerWidth) / canvasState.pixelsPerSecond
  
  if (timePosition < 0) return
  
  if (track.type === 'subtitle') {
    // 检查是否点击在已有字幕上
    const clip = track.clips.find(clip => 
      timePosition >= clip.startTime && timePosition <= clip.endTime
    )
    
    if (clip) {
      // 编辑已有字幕
      subtitleDialog.trackId = track.id
      subtitleDialog.clipId = clip.id
      subtitleDialog.content = clip.content || ''
      subtitleDialog.visible = true
    } else {
      // 在当前位置添加新字幕
      addSubtitleClip(track.id, timePosition)
    }
  }
}

// 修改saveSubtitle函数
const saveSubtitle = () => {
  const track = tracks.value.find(t => t.id === subtitleDialog.trackId)
  if (!track || track.type !== 'subtitle') return
  
  const clip = track.clips.find(c => c.id === subtitleDialog.clipId)
  if (clip) {
    clip.content = subtitleDialog.content
    updateCanvas()
  }
  
  subtitleDialog.visible = false
}

// 处理按钮点击事件
const handleButtonClick = (e: MouseEvent) => {
  if (!timelineCanvas.value) return
  
  const rect = timelineCanvas.value.getBoundingClientRect()
  const mouseX = e.clientX - rect.left
  const mouseY = e.clientY - rect.top
  
  // 检查是否点击在轨道标题区域按钮上
  for (let i = 0; i < tracks.value.length; i++) {
    const track = tracks.value[i]
    
    // 检查上移按钮
    if (track.upButton && isPointInRect(mouseX, mouseY, track.upButton)) {
      moveTrackUp(i)
      return
    }
    
    // 检查下移按钮
    if (track.downButton && isPointInRect(mouseX, mouseY, track.downButton)) {
      moveTrackDown(i)
      return
    }
  }
}

// 辅助函数：判断点是否在矩形内
const isPointInRect = (x: number, y: number, rect: { x: number, y: number, width: number, height: number }) => {
  return x >= rect.x && x <= rect.x + rect.width && y >= rect.y && y <= rect.y + rect.height
}

// 上移轨道
const moveTrackUp = (index: number) => {
  if (index <= 0 || index >= tracks.value.length) return
  
  // 交换轨道位置
  const temp = tracks.value[index]
  tracks.value[index] = tracks.value[index - 1]
  tracks.value[index - 1] = temp
  
  updateCanvas()
}

// 下移轨道
const moveTrackDown = (index: number) => {
  if (index < 0 || index >= tracks.value.length - 1) return
  
  // 交换轨道位置
  const temp = tracks.value[index]
  tracks.value[index] = tracks.value[index + 1]
  tracks.value[index + 1] = temp
  
  updateCanvas()
}

// 简化播放时间指示器绘制，移除顶部三角形
const drawTimeIndicator = (ctx: CanvasRenderingContext2D) => {
  // 保存上下文状态
  ctx.save()
  
  // 计算当前时间位置
  const x = currentTime.value * canvasState.pixelsPerSecond + canvasState.headerWidth
  
  // 绘制时间指示线
  ctx.strokeStyle = isPlaying.value ? '#ff3333' : '#ff0000'
  ctx.lineWidth = 2
  
  // 设置虚线样式
  if (!isPlaying.value) {
    ctx.setLineDash([5, 3]) // 非播放时使用虚线
  }
  
  // 绘制从顶部到底部的直线
  ctx.beginPath()
  ctx.moveTo(x, 0)
  ctx.lineTo(x, canvasState.height)
  ctx.stroke()
  
  // 重置虚线设置
  ctx.setLineDash([])
  
  // 显示当前时间
  ctx.fillStyle = isPlaying.value ? '#ff3333' : '#ff0000'
  ctx.font = '10px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(formatTime(currentTime.value), x, 25)
  
  // 恢复上下文状态
  ctx.restore()
}

// 添加字幕片段
const addSubtitleClip = (trackId: string, startTime: number) => {
  const track = tracks.value.find(t => t.id === trackId)
  if (!track || track.type !== 'subtitle') return
  
  const endTime = startTime + 2 // 默认2秒
  
  // 检查是否与现有字幕重叠
  const isOverlapping = track.clips.some(clip => 
    (startTime >= clip.startTime && startTime <= clip.endTime) ||
    (endTime >= clip.startTime && endTime <= clip.endTime) ||
    (startTime <= clip.startTime && endTime >= clip.endTime)
  )
  
  if (isOverlapping) {
    alert('新字幕与现有字幕重叠，请选择其他位置。')
    return
  }
  
  const newClip: Clip = {
    id: `subtitle-clip-${Date.now()}`,
    startTime,
    endTime,
    content: '新字幕'
  }
  
  track.clips.push(newClip)
  
  // 打开编辑对话框
  subtitleDialog.trackId = trackId
  subtitleDialog.clipId = newClip.id
  subtitleDialog.content = newClip.content
  subtitleDialog.visible = true
  
  updateCanvas()
}

// 完全固定刻度线高度的最终解决方案
const drawTimeScale = (ctx: CanvasRenderingContext2D) => {
  // 固定硬编码的高度值
  const SCALE_HEIGHT = 30;
  
  // 保存当前绘图状态
  ctx.save();
  
  // 清除时间刻度区域
  ctx.clearRect(0, 0, canvasState.width, SCALE_HEIGHT);
  
  // 绘制时间刻度背景
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(0, 0, canvasState.width, SCALE_HEIGHT);
  
  // 绘制水平分隔线
  ctx.strokeStyle = '#ccc';
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.moveTo(0, SCALE_HEIGHT);
  ctx.lineTo(canvasState.width, SCALE_HEIGHT);
  ctx.stroke();
  
  // 绘制垂直分隔线
  ctx.beginPath();
  ctx.moveTo(canvasState.headerWidth, 0);
  ctx.lineTo(canvasState.headerWidth, SCALE_HEIGHT);
  ctx.stroke();
  
  // 绘制时间刻度标签
  ctx.fillStyle = '#333';
  ctx.font = '12px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  
  // 计算刻度间隔
  const pixelsPerSecond = canvasState.pixelsPerSecond;
  let interval = 1;
  
  if (pixelsPerSecond < 30) interval = 5;
  else if (pixelsPerSecond < 60) interval = 2;
  
  // 确保显示足够的时间轴
  const maxTime = Math.max(videoDuration.value || 10, 30);
  
  // 绘制刻度和标签 - 使用固定的绝对高度值
  for (let i = 0; i <= maxTime; i += interval) {
    const x = i * pixelsPerSecond + canvasState.headerWidth;
    
    if (x < canvasState.headerWidth) continue;
    if (x > canvasState.width) break;
    
    // 绘制刻度线
    ctx.beginPath();
    ctx.moveTo(x, SCALE_HEIGHT - 5);
    ctx.lineTo(x, SCALE_HEIGHT);
    ctx.stroke();
    
    // 绘制时间标签
    ctx.fillText(formatTime(i), x, SCALE_HEIGHT / 2);
  }
  
  // 恢复绘图状态
  ctx.restore();
}

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 实现分割轨道块的功能
const splitClipAtCurrentTime = () => {
  if (!selectedClip.trackId || !selectedClip.clipId) {
    ElMessage.warning('请先选择一个轨道块');
    return;
  }
  
  // 获取当前选中的轨道和片段
  const track = tracks.value.find(t => t.id === selectedClip.trackId);
  if (!track) return;
  
  const clipIndex = track.clips.findIndex(c => c.id === selectedClip.clipId);
  if (clipIndex === -1) return;
  
  const clip = track.clips[clipIndex];
  
  // 检查当前播放线是否在片段内
  if (currentTime.value <= clip.startTime || currentTime.value >= clip.endTime) {
    ElMessage.warning('播放线必须在选中的轨道块内才能分割');
    return;
  }
  
  // 分割前的片段时长（可能需要用于内容的比例分配）
  const originalDuration = clip.endTime - clip.startTime;
  
  // 创建两个新片段
  const leftClip: Clip = {
    id: `${clip.id}-left-${Date.now()}`,
    startTime: clip.startTime,
    endTime: currentTime.value,
    content: clip.content,
    file: clip.file
  };
  
  const rightClip: Clip = {
    id: `${clip.id}-right-${Date.now()}`,
    startTime: currentTime.value,
    endTime: clip.endTime,
    content: clip.content,
    file: clip.file
  };
  
  // 如果是字幕，可能需要根据时间比例分割内容
  if (track.type === 'subtitle' && clip.content) {
    // 简单按时间比例分割文本
    const ratio = (currentTime.value - clip.startTime) / originalDuration;
    const contentLength = clip.content.length;
    const splitPoint = Math.floor(contentLength * ratio);
    
    leftClip.content = clip.content.substring(0, splitPoint);
    rightClip.content = clip.content.substring(splitPoint);
  }
  
  // 用两个新片段替换原始片段
  track.clips.splice(clipIndex, 1, leftClip, rightClip);
  
  // 更新选中状态为左侧片段
  selectedClip.clipId = leftClip.id;
  
  // 更新画布
  updateCanvas();
  
  ElMessage.success('轨道块已分割');
};

// 添加鼠标右键点击处理函数，用于在任何位置设置播放位置
const handleContextMenu = (e: MouseEvent) => {
  // 阻止默认的右键菜单
  e.preventDefault()
  
  if (!timelineCanvas.value) return
  
  const rect = timelineCanvas.value.getBoundingClientRect()
  const mouseX = e.clientX - rect.left
  
  // 只在时间轴内容区域生效
  if (mouseX < canvasState.headerWidth) return
  
  // 计算点击位置对应的时间
  const clickTime = (mouseX - canvasState.headerWidth) / canvasState.pixelsPerSecond
  
  // 限制在视频时长范围内
  if (clickTime >= 0 && (!videoDuration.value || clickTime <= videoDuration.value)) {
    // 设置当前时间
    currentTime.value = clickTime
    
    // 如果视频已加载，则更新视频位置
    if (videoPlayer.value) {
      videoPlayer.value.currentTime = clickTime
    }
    
    // 更新画布
    updateCanvas()
  }
}

// 添加检查时间点是否在视频片段内的函数
const isTimeInVideoClip = (time: number): { isIn: boolean, videoClip?: Clip } => {
  // 查找所有视频轨道
  const videoTracks = tracks.value.filter(track => track.type === 'video');
  
  // 检查所有视频轨道的所有片段
  for (const track of videoTracks) {
    for (const clip of track.clips) {
      if (time >= clip.startTime && time < clip.endTime) {
        return { isIn: true, videoClip: clip };
      }
    }
  }
  
  return { isIn: false };
};

// 添加用于控制视频显示/隐藏的函数
const updateVideoVisibility = (time: number) => {
  if (!videoPlayer.value) return;
  
  const { isIn, videoClip } = isTimeInVideoClip(time);
  
  if (isIn && videoClip) {
    // 当前时间在视频片段内，显示视频并设置正确的时间点
    videoPlayer.value.style.visibility = 'visible';
    
    // 如果视频正在播放，确保视频位置正确
    const videoOffset = time - videoClip.startTime;
    if (Math.abs(videoPlayer.value.currentTime - videoOffset) > 0.1) {
      videoPlayer.value.currentTime = videoOffset;
    }
  } else {
    // 当前时间不在任何视频片段内，隐藏视频
    videoPlayer.value.style.visibility = 'hidden';
    
    // 如果在播放中，暂停视频但不暂停时间轴
    if (!videoPlayer.value.paused) {
      videoPlayer.value.pause();
    }
  }
};

// 修改更新播放进度的函数
const updatePlaybackProgress = () => {
  if (!isPlaying.value) return;
  
  // 增加当前时间
  currentTime.value += 0.05; // 每帧增加的时间量
  
  // 检查是否需要更新视频状态
  updateVideoVisibility(currentTime.value);
  
  // 更新画布
  updateCanvas();
  
  // 如果到达总时长则停止播放
  if (videoDuration.value && currentTime.value >= videoDuration.value) {
    stopPlayback();
    return;
  }
  
  // 继续播放
  requestAnimationFrame(updatePlaybackProgress);
};

// 开始播放
const startPlayback = () => {
  if (isPlaying.value) return;
  
  isPlaying.value = true;
  
  // 初始检查当前播放位置是否在视频片段内
  updateVideoVisibility(currentTime.value);
  
  // 使用requestAnimationFrame更新播放进度
  requestAnimationFrame(updatePlaybackProgress);
};

// 停止播放
const stopPlayback = () => {
  isPlaying.value = false;
  
  // 暂停视频
  if (videoPlayer.value && !videoPlayer.value.paused) {
    videoPlayer.value.pause();
  }
};

// 修改视频容器样式，增加黑屏背景
// 在样式部分添加：
// .video-container {
//   background-color: #000;
//   aspect-ratio: 16/9;
//   display: flex;
//   justify-content: center;
//   align-items: center;
// }

// 修改 onMounted 函数，在整个时间轴容器上监听鼠标移动
onMounted(() => {
  // 初始化编辑器
  if (timelineCanvas.value && timelineContainer.value) {
    // 先设置画布尺寸
    updateCanvasSize();
    // 然后绘制内容
    updateCanvas();
    
    // 添加滚轮缩放事件
    timelineContainer.value.addEventListener('wheel', handleWheelZoom, { passive: false });
    
    // 在时间轴容器上监听鼠标移动
    timelineContainer.value.addEventListener('mousemove', (e: MouseEvent) => {
      // 存储最后一次鼠标事件
      lastMouseEvent = e;
      
      // 更新鼠标样式
      updateCursor(e);
      
      // 更新画布，显示悬浮指示器
      updateCanvas();
      
      // 处理鼠标移动逻辑
      handleMouseMove(e);
    });
    
    // 确保鼠标离开时也能正确处理
    timelineContainer.value.addEventListener('mouseleave', () => {
      // 清除最后一次鼠标事件
      lastMouseEvent = null;
      // 更新画布，移除悬浮指示器
      updateCanvas();
    });
    
    // 添加右键点击事件
    timelineCanvas.value.addEventListener('contextmenu', handleContextMenu);
  }
  
  // 设置窗口大小变化监听
  window.addEventListener('resize', () => {
    updateCanvasSize();
    updateCanvas();
  });

  // 为视频播放器添加事件监听
  if (videoPlayer.value) {
    // 视频加载完成后
    videoPlayer.value.addEventListener('loadedmetadata', () => {
      // 设置初始视频可见性
      updateVideoVisibility(currentTime.value);
    });
    
    // 视频播放过程中
    videoPlayer.value.addEventListener('timeupdate', () => {
      if (isPlaying.value) {
        // 同步视频状态
        updateVideoVisibility(currentTime.value);
      }
    });
  }
  
  // 初始化时不显示时间指示器
  // currentTime.value = null
  
  // 更新画布
  updateCanvas()
});

// 确保在组件销毁时清理滚轮事件
onUnmounted(() => {
  // 清理窗口大小变化监听
  window.removeEventListener('resize', () => {
    updateCanvasSize();
    updateCanvas();
  });
  
  // 清理时间轴容器的事件监听
  if (timelineContainer.value) {
    // 移除滚轮事件
    timelineContainer.value.removeEventListener('wheel', handleWheelZoom);
    
    // 移除鼠标移动事件
    timelineContainer.value.removeEventListener('mousemove', (e: MouseEvent) => {
      lastMouseEvent = e;
      updateCursor(e);
      updateCanvas();
      handleMouseMove(e);
    });
    
    // 移除鼠标离开事件
    timelineContainer.value.removeEventListener('mouseleave', () => {
      lastMouseEvent = null;
      updateCanvas();
    });
  }
  
  // 清理右键点击事件
  if (timelineCanvas.value) {
    timelineCanvas.value.removeEventListener('contextmenu', handleContextMenu);
  }
});

// 添加加载状态和初始化数据接口
interface InitialData {
  video?: {
    url: string;
    duration?: number;
  };
  audio?: {
    id: string;
    url: string;
    startTime: number;
    endTime: number;
    name?: string;
  }[];
  subtitles?: {
    id: string;
    content: string;
    startTime: number;
    endTime: number;
  }[];
}

// 添加加载状态管理
const isLoading = ref(true);
const loadingProgress = ref(0);
const loadingMessage = ref('准备加载资源...');
const resourcesLoaded = ref(false);
const totalResources = ref(0);
const loadedResources = ref(0);

// 添加禁用播放和编辑的条件检查
const canPlayOrEdit = computed(() => {
  return resourcesLoaded.value && !isLoading.value;
});

// 初始化编辑器数据
const initializeFromJson = async (jsonData: InitialData) => {
  isLoading.value = true;
  loadingMessage.value = '正在解析数据...';
  
  // 清空现有轨道
  tracks.value = [];
  
  // 计算需要加载的资源总数
  totalResources.value = 0;
  if (jsonData.video?.url) totalResources.value++;
  if (jsonData.audio) totalResources.value += jsonData.audio.length;
  
  loadedResources.value = 0;
  
  try {
    // 1. 创建字幕轨道
    const subtitleTrack: Track = {
      id: 'subtitle-track-1',
      type: 'subtitle',
      clips: []
    };
    
    // 添加字幕片段
    if (jsonData.subtitles && jsonData.subtitles.length > 0) {
      loadingMessage.value = '正在处理字幕数据...';
      
      jsonData.subtitles.forEach(subtitle => {
        subtitleTrack.clips.push({
          id: subtitle.id || `subtitle-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          startTime: subtitle.startTime,
          endTime: subtitle.endTime,
          content: subtitle.content
        });
      });
    }
    
    // 添加字幕轨道
    tracks.value.push(subtitleTrack);
    
    // 2. 创建音频轨道
    const audioTrack: Track = {
      id: 'audio-track-1',
      type: 'audio',
      clips: []
    };
    
    // 预加载音频文件并添加到轨道
    if (jsonData.audio && jsonData.audio.length > 0) {
      loadingMessage.value = '正在加载音频文件...';
      
      // 创建音频加载队列
      const audioLoadPromises = jsonData.audio.map(audio => {
        return new Promise<void>((resolve, reject) => {
          const audioElement = new Audio();
          
          audioElement.oncanplaythrough = () => {
            // 音频加载完成
            loadedResources.value++;
            loadingProgress.value = (loadedResources.value / totalResources.value) * 100;
            
            // 创建音频片段
            const audioFile = new File(
              [new Blob()], // 这里只是占位，实际不需要文件内容
              audio.name || `audio-${audio.id}.mp3`,
              { type: 'audio/mpeg' }
            );
            
            // 添加到轨道
            audioTrack.clips.push({
              id: audio.id || `audio-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              startTime: audio.startTime,
              endTime: audio.endTime,
              file: audioFile,
              url: audio.url,
              audioElement: audioElement
            });
            
            resolve();
          };
          
          audioElement.onerror = () => {
            console.error(`Failed to load audio: ${audio.url}`);
            loadedResources.value++;
            loadingProgress.value = (loadedResources.value / totalResources.value) * 100;
            resolve(); // 继续加载其他资源，不中断整个过程
          };
          
          // 开始加载
          audioElement.src = audio.url;
          audioElement.load();
        });
      });
      
      // 等待所有音频加载完成
      await Promise.all(audioLoadPromises);
    }
    
    // 添加音频轨道
    tracks.value.push(audioTrack);
    
    // 3. 创建视频轨道
    const videoTrack: Track = {
      id: 'video-track-1',
      type: 'video',
      clips: []
    };
    
    // 加载主视频
    if (jsonData.video?.url) {
      loadingMessage.value = '正在加载视频文件...';
      
      // 设置视频URL
      videoUrl.value = jsonData.video.url;
      
      // 等待视频加载完成
      await new Promise<void>((resolve, reject) => {
        if (!videoPlayer.value) {
          console.error('Video player not initialized');
          resolve(); // 继续执行
          return;
        }
        
        videoPlayer.value.oncanplaythrough = () => {
          // 视频加载完成
          loadedResources.value++;
          loadingProgress.value = (loadedResources.value / totalResources.value) * 100;
          
          // 获取视频时长
          const duration = videoPlayer.value?.duration || jsonData.video?.duration || 0;
          videoDuration.value = duration;
          
          // 添加视频片段
          videoTrack.clips.push({
            id: 'main-video',
            startTime: 0,
            endTime: duration,
            content: '主视频'
          });
          
          resolve();
        };
        
        videoPlayer.value.onerror = () => {
          console.error(`Failed to load video: ${jsonData.video?.url}`);
          loadedResources.value++;
          loadingProgress.value = (loadedResources.value / totalResources.value) * 100;
          resolve(); // 继续执行
        };
        
        // 开始加载视频
        videoPlayer.value.src = jsonData.video.url;
        videoPlayer.value.load();
      });
    }
    
    // 添加视频轨道
    tracks.value.push(videoTrack);
    
    // 所有资源加载完成
    resourcesLoaded.value = true;
    loadingMessage.value = '所有资源加载完成';
    
    // 更新画布
    updateCanvas();
    
  } catch (error) {
    console.error('初始化编辑器失败:', error);
    loadingMessage.value = '加载失败，请刷新页面重试';
  } finally {
    isLoading.value = false;
  }
};

// 在组件挂载时添加初始化方法
onMounted(() => {
  // 初始化编辑器
  if (timelineCanvas.value && timelineContainer.value) {
    // 设置画布尺寸
    updateCanvasSize();
    
    // 绘制初始内容
    updateCanvas();
    
    // 其他初始化代码...
  }
  
  // 示例：从props或其他来源获取初始数据
  // 如果有初始数据，则调用初始化方法
  if (props.initialData) {
    initializeFromJson(props.initialData);
  } else {
    // 没有初始数据，直接设置为非加载状态
    isLoading.value = false;
    resourcesLoaded.value = true;
  }
});

// 添加加载状态UI组件
// 在模板中添加加载状态显示
// <div v-if="isLoading" class="loading-overlay">
//   <div class="loading-content">
//     <div class="loading-spinner"></div>
//     <div class="loading-message">{{ loadingMessage }}</div>
//     <div class="loading-progress">
//       <div class="progress-bar" :style="{width: `${loadingProgress}%`}"></div>
//     </div>
//   </div>
// </div>

// 注意：不要重新声明 playPreview 函数，而是在现有的 playPreview 函数中添加资源加载检查
// 找到现有的 playPreview 函数，并修改为：
/*
const playPreview = () => {
  // 添加资源加载检查
  if (!canPlayOrEdit.value) {
    // 显示提示：资源正在加载中
    alert('资源正在加载中，请稍候...');
    return;
  }
  
  // 原有的播放逻辑
  if (isPlaying.value) {
    stopPlayback();
  } else {
    startPlayback();
  }
};
*/

// 同样修改其他编辑功能，确保资源加载完成后才能操作

// 添加一个计算属性，决定是否显示时间指示器
const showTimeIndicator = computed(() => {
  // 只有当 currentTime 不为0或null时才显示
  return currentTime.value !== null && currentTime.value > 0.05;
});

// 添加导入音频功能
const importAudio = () => {
  // 创建文件输入元素
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = 'audio/*' // 只接受音频文件
  fileInput.style.display = 'none'
  document.body.appendChild(fileInput)
  
  // 监听文件选择事件
  fileInput.onchange = async (event) => {
    const files = (event.target as HTMLInputElement).files
    if (!files || files.length === 0) return
    
    const audioFile = files[0]
    
    // 查找音频轨道，如果没有则创建一个
    let audioTrack = tracks.value.find(track => track.type === 'audio')
    if (!audioTrack) {
      // 创建新的音频轨道
      const newTrackId = `audio-track-${Date.now()}`
      audioTrack = {
        id: newTrackId,
        type: 'audio',
        clips: []
      }
      tracks.value.push(audioTrack)
    }
    
    try {
      // 创建音频元素以获取时长
      const audioElement = new Audio()
      const audioUrl = URL.createObjectURL(audioFile)
      
      // 等待音频加载以获取时长
      await new Promise<void>((resolve, reject) => {
        audioElement.onloadedmetadata = () => resolve()
        audioElement.onerror = () => reject(new Error('Failed to load audio'))
        audioElement.src = audioUrl
      })
      
      // 获取音频时长
      const duration = audioElement.duration
      
      // 创建新的音频片段
      const newClip: Clip = {
        id: `audio-clip-${Date.now()}`,
        startTime: 0, // 默认从0秒开始
        endTime: duration,
        file: audioFile,
        audioElement: audioElement
      }
      
      // 添加到轨道
      audioTrack.clips.push(newClip)
      
      // 更新画布
      updateCanvas()
      
      // 显示成功消息
      ElMessage.success(`已导入音频: ${audioFile.name}`)
      
    } catch (error) {
      console.error('导入音频失败:', error)
      ElMessage.error('导入音频失败，请检查文件格式')
    } finally {
      // 清理文件输入元素
      document.body.removeChild(fileInput)
    }
  }
  
  // 触发文件选择对话框
  fileInput.click()
}

// 添加音频波形分析和缓存
const audioWaveformCache = new Map<string, number[]>();

// 简化版：生成随机波形数据
const generateRandomWaveform = (length: number, seed?: number): number[] => {
  const waveform = [];
  // 使用种子确保相同的clipId生成相同的波形
  const seedValue = seed || Math.random() * 1000;
  
  // 简单的伪随机数生成器
  const pseudoRandom = (seed: number) => {
    let value = seed;
    return () => {
      value = (value * 9301 + 49297) % 233280;
      return value / 233280;
    };
  };
  
  const random = pseudoRandom(seedValue);
  
  // 生成平滑的随机波形
  let lastValue = 0.5;
  for (let i = 0; i < length; i++) {
    // 生成0.2到0.8之间的随机值，并与前一个值平滑过渡
    const randomValue = 0.2 + random() * 0.6;
    const smoothValue = lastValue * 0.7 + randomValue * 0.3;
    waveform.push(smoothValue);
    lastValue = smoothValue;
  }
  
  return waveform;
};

// 分析音频并生成波形数据 - 简化版，不使用AudioContext
const analyzeAudio = async (audioElement: HTMLAudioElement, clipId: string): Promise<number[]> => {
  // 检查缓存中是否已有波形数据
  if (audioWaveformCache.has(clipId)) {
    return audioWaveformCache.get(clipId)!;
  }
  
  // 使用clipId作为种子生成一致的随机波形
  const seed = parseInt(clipId.replace(/\D/g, '')) || Date.now();
  const waveformLength = Math.max(50, Math.ceil(audioElement.duration / 0.05));
  const waveformData = generateRandomWaveform(waveformLength, seed);
  
  // 缓存波形数据
  audioWaveformCache.set(clipId, waveformData);
  
  return waveformData;
};

// 在 drawTrack 函数中添加波形绘制
const drawTrack = (ctx: CanvasRenderingContext2D, track: Track, index: number) => {
  // 保存上下文状态
  ctx.save()
  
  // 计算轨道Y位置
  const y = canvasState.timeScaleHeight + (index * canvasState.trackHeight)
  
  // 绘制轨道背景
  ctx.fillStyle = colors.background
  ctx.fillRect(canvasState.headerWidth, y, canvasState.width - canvasState.headerWidth, canvasState.trackHeight)
  
  // 绘制轨道标题背景
  ctx.fillStyle = '#e8e8e8'
  ctx.fillRect(0, y, canvasState.headerWidth, canvasState.trackHeight)
  
  // 绘制轨道边框
  ctx.strokeStyle = '#ddd'
  ctx.lineWidth = 1
  ctx.strokeRect(canvasState.headerWidth, y, canvasState.width - canvasState.headerWidth, canvasState.trackHeight)
  
  // 绘制轨道标题
  ctx.fillStyle = colors.text
  ctx.font = '12px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText(
    getTrackName(track, index),
    canvasState.headerWidth / 2,
    y + canvasState.trackHeight / 2
  )
  
  // 绘制轨道内的所有片段
  track.clips.forEach(clip => {
    const startX = clip.startTime * canvasState.pixelsPerSecond + canvasState.headerWidth
    const endX = clip.endTime * canvasState.pixelsPerSecond + canvasState.headerWidth
    const clipWidth = endX - startX
    
    // 检查是否是当前选中的片段
    const isSelected = (track.id === selectedClip.trackId && clip.id === selectedClip.clipId)
    
    // 根据是否选中调整颜色
    if (isSelected) {
      // 选中的片段使用更亮的颜色
      ctx.fillStyle = track.type === 'video' ? '#5c9cff' : 
                     track.type === 'audio' ? '#ff9c5c' : '#9cff5c'
    } else {
      // 未选中的片段使用暗化的颜色
      ctx.fillStyle = track.type === 'video' ? 'rgba(0, 100, 200, 0.5)' : 
                     track.type === 'audio' ? 'rgba(200, 100, 0, 0.5)' : 'rgba(100, 200, 0, 0.5)'
    }
    
    // 绘制片段背景
    ctx.fillRect(
      startX,
      y + 5,
      clipWidth,
      canvasState.trackHeight - 10
    )
    
    // 为音频片段绘制波形
    if (track.type === 'audio') {
      // 获取或生成波形数据
      let waveformData: number[] = [];
      
      // 尝试从缓存获取波形数据
      if (audioWaveformCache.has(clip.id)) {
        waveformData = audioWaveformCache.get(clip.id)!;
      } else {
        // 如果没有缓存，生成随机波形
        const seed = parseInt(clip.id.replace(/\D/g, '')) || Date.now();
        waveformData = generateRandomWaveform(Math.ceil(clipWidth / 3), seed);
        
        // 缓存波形数据
        audioWaveformCache.set(clip.id, waveformData);
      }
      
      // 绘制波形
      const waveHeight = canvasState.trackHeight - 20; // 波形高度
      const waveY = y + 10; // 波形Y坐标起点
      
      // 设置波形颜色
      ctx.strokeStyle = isSelected ? '#ffffff' : 'rgba(255, 255, 255, 0.7)';
      ctx.lineWidth = 1.5;
      
      // 开始绘制波形路径
      ctx.beginPath();
      
      // 计算每个波形点的X坐标间隔
      const pointSpacing = clipWidth / waveformData.length;
      
      // 绘制波形 - 只绘制一行
      for (let i = 0; i < waveformData.length; i++) {
        const x = startX + (i * pointSpacing);
        const amplitude = waveformData[i] * waveHeight;
        
        if (i === 0) {
          // 移动到起始点
          ctx.moveTo(x, waveY + waveHeight - amplitude);
        } else {
          // 连接到下一个点
          ctx.lineTo(x, waveY + waveHeight - amplitude);
        }
      }
      
      // 描边波形
      ctx.stroke();
      
      // 可选：添加底线
      ctx.beginPath();
      ctx.moveTo(startX, waveY + waveHeight);
      ctx.lineTo(startX + clipWidth, waveY + waveHeight);
      ctx.stroke();
    }
    
    // 为选中的片段添加高亮边框和阴影效果
    if (isSelected) {
      // 添加阴影效果
      ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
      ctx.shadowBlur = 5;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
      
      // 绘制高亮边框
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.strokeRect(
        startX,
        y + 5,
        clipWidth,
        canvasState.trackHeight - 10
      );
      
      // 重置阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      
      // 如果当前播放线在选中片段内，绘制可能的分割线提示
      if (currentTime.value > clip.startTime && currentTime.value < clip.endTime) {
        const indicatorX = currentTime.value * canvasState.pixelsPerSecond + canvasState.headerWidth;
        
        // 绘制分割提示线
        ctx.strokeStyle = '#ffffff';
        ctx.setLineDash([4, 2]);
        ctx.beginPath();
        ctx.moveTo(indicatorX, y);
        ctx.lineTo(indicatorX, y + canvasState.trackHeight);
        ctx.stroke();
        ctx.setLineDash([]);
      }
    }
    
    // 绘制片段内容（根据轨道类型）
    ctx.fillStyle = isSelected ? '#ffffff' : '#f0f0f0'; // 文本颜色
    ctx.font = '12px Arial';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    
    const padding = 5
    
    if (track.type === 'subtitle' && clip.content) {
      // 显示字幕内容
      let displayText = clip.content
      if (ctx.measureText(displayText).width > clipWidth - 10) {
        displayText = displayText.substring(0, 15) + '...'
      }
      ctx.fillText(displayText, startX + padding, y + canvasState.trackHeight / 2)
    } else if (track.type === 'audio' && clip.file) {
      // 显示音频文件名
      const fileName = clip.file.name
      const displayName = fileName.length > 15 ? fileName.substring(0, 15) + '...' : fileName
      ctx.fillText(displayName, startX + padding, y + canvasState.trackHeight / 2)
    } else if (track.type === 'video') {
      // 显示视频时间范围
      const timeText = `${formatTime(clip.startTime)} - ${formatTime(clip.endTime)}`
      ctx.fillText(timeText, startX + padding, y + canvasState.trackHeight / 2)
    }
    
    // 绘制调整手柄
    drawResizeHandles(ctx, startX, endX, y)
  })
  
  // 恢复上下文状态
  ctx.restore()
}

// 添加视频帧缓存
const videoFrameCache = new Map<string, string>();

// 从视频中提取帧并转换为数据URL
const extractVideoFrame = async (videoElement: HTMLVideoElement, time: number): Promise<string> => {
  return new Promise((resolve) => {
    // 保存当前时间
    const currentTime = videoElement.currentTime;
    
    // 创建一个临时canvas来捕获视频帧
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // 设置canvas大小与视频相同
    canvas.width = videoElement.videoWidth;
    canvas.height = videoElement.videoHeight;
    
    // 当视频跳转到指定时间后的处理函数
    const handleSeeked = () => {
      // 绘制当前视频帧到canvas
      ctx?.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
      
      // 将canvas内容转换为数据URL
      const dataURL = canvas.toDataURL('image/jpeg', 0.7); // 使用JPEG格式，质量0.7
      
      // 恢复视频原来的时间
      videoElement.currentTime = currentTime;
      
      // 移除事件监听
      videoElement.removeEventListener('seeked', handleSeeked);
      
      // 返回数据URL
      resolve(dataURL);
    };
    
    // 监听seeked事件，当视频跳转到指定时间后触发
    videoElement.addEventListener('seeked', handleSeeked);
    
    // 跳转到指定时间
    videoElement.currentTime = time;
  });
};

// 为视频片段生成缩略图
const generateVideoThumbnail = async (videoElement: HTMLVideoElement, clip: Clip): Promise<void> => {
  // 生成缓存键
  const cacheKey = `${clip.id}-${clip.startTime.toFixed(2)}`;
  
  // 检查缓存
  if (videoFrameCache.has(cacheKey)) {
    return;
  }
  
  try {
    // 提取视频帧
    const frameURL = await extractVideoFrame(videoElement, clip.startTime);
    
    // 缓存帧
    videoFrameCache.set(cacheKey, frameURL);
    
    // 更新画布
    updateCanvas();
  } catch (error) {
    console.error('Failed to generate video thumbnail:', error);
  }
};

// 在视频加载完成后，为所有视频片段生成缩略图
const generateAllVideoThumbnails = async (): Promise<void> => {
  // 确保视频元素已加载
  if (!videoPlayer.value || !videoPlayer.value.readyState >= 2) {
    return;
  }
  
  // 查找所有视频轨道
  const videoTracks = tracks.value.filter(track => track.type === 'video');
  
  // 为每个视频片段生成缩略图
  for (const track of videoTracks) {
    for (const clip of track.clips) {
      await generateVideoThumbnail(videoPlayer.value, clip);
    }
  }
};

// 在视频加载完成后生成所有缩略图
onMounted(() => {
  // 其他初始化代码...
  
  // 监听视频加载完成事件
  if (videoPlayer.value) {
    videoPlayer.value.addEventListener('loadeddata', () => {
      // 生成所有视频片段的缩略图
      generateAllVideoThumbnails();
    });
  }
});

// 在添加或修改视频片段时生成缩略图
// 在相关函数中添加调用 generateVideoThumbnail
</script>

<style scoped>
.voiceover-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.zoom-control {
  width: 200px;
  margin-left: 20px;
}

.timeline-container {
  position: relative;
  border: 1px solid #ddd;
  overflow-x: auto; /* 允许横向滚动 */
  overflow-y: hidden; /* 禁止纵向滚动 */
  height: 300px;
  cursor: default;
  scroll-behavior: smooth; /* 平滑滚动 */
}

/* 轨道标题区域的光标 */
.track-title-area {
  cursor: grab;
}

/* 拖动时的光标 */
.track-dragging {
  cursor: grabbing;
}

/* 动画效果 */
@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 0.9; }
  100% { opacity: 0.7; }
}

.dragging-track {
  animation: pulse 1s infinite;
}

.preview-panel {
  margin-top: 20px;
}

video {
  max-width: 100%;
  height: auto;
}

/* 修改时间指示器样式，移除所有可能的三角形 */
.time-indicator {
  position: absolute;
  top: 0;
  width: 0;
  height: 100%;
  border-left: 2px dashed #ff0000; /* 使用虚线边框 */
  pointer-events: none;
  z-index: 1;
}

/* 播放时的动画效果 */
@keyframes playingPulse {
  0% { border-color: rgba(255, 0, 0, 0.7); }
  50% { border-color: rgba(255, 51, 51, 1); }
  100% { border-color: rgba(255, 0, 0, 0.7); }
}

.playing-indicator {
  border-left: 2px solid #ff3333; /* 播放时使用实线 */
  animation: playingPulse 1s infinite;
}

/* 确保没有使用伪元素添加三角形 */
.time-indicator::before,
.time-indicator::after {
  display: none !important;
}
</style>

