<template>
  <div>
    <div class="pt-4 pb-2 flex justify-between items-center border-b border-gray-200">
      <h1><span @click="router.back()" class="cursor-pointer">{{currentProjectData.name}}</span> > 任务列表</h1>
      <ElButton type="primary" @click="createTask">新增任务</ElButton>
    </div>
    <div class="">
      <ElTable :data="tableData" style="width: 100%">
        <ElTableColumn prop="name" label="名称" width="200" />
        <ElTableColumn prop="create_time" label="新增日期" width="200" />
        <ElTableColumn prop="deadline_time" label="截止日期" width="200" />
        <ElTableColumn prop="language" label="语言" width="200" />
        <ElTableColumn prop="operator_name" label="负责人" width="200" />
        <ElTableColumn prop="status" label="翻译进度">
          <template #default="scope">
            {{ statusList.find(item => item.value === scope.row.status)?.label }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="created_at" label="操作" fixed="right" width="350">
          <template #default="scope">
            <ElButton type="primary" link @click="editTask(scope.row)">编辑任务</ElButton>
            <ElButton type="danger" link @click="deleteTask(scope.row)">删除任务</ElButton>
            <ElButton type="primary" link @click="translateDetail(scope.row)">去翻译</ElButton>
            <ElButton type="primary" link @click="exportExcel(scope.row)">下载</ElButton>
            <ElButton type="primary" link @click="publishTask(scope.row)">发布</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
      <div class="mt-4 flex justify-end">
        <ElPagination v-model="pageInfo.page_index" layout="total, sizes, prev, pager, next" :page-size="pageInfo.page_size" :total="pageInfo.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>
    <ElDialog :close-on-click-modal="false" v-model="taskPop" title="编辑任务" width="1000px">
      <ElForm :model="editForm" :rules="rules" label-width="100px" ref="editFormRef">
        <ElFormItem label="项目">
          <ElInput v-model="currentProjectData.name" :disabled="true" />
        </ElFormItem>
        <ElFormItem label="名称" prop="name">
          <ElInput v-model="editForm.name" />
          <span>任务名称：xxx功能翻译</span>
        </ElFormItem>
        <ElFormItem label="DDL" prop="deadline_time">
          <ElDatePicker v-model="editForm.deadline_time" value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" type="datetime" />
          <span>日期具体到时间，不添加默认每周三提醒</span>
        </ElFormItem>
        <ElFormItem label="工单链接" prop="work_link">
          <ElInput v-model="editForm.work_link" />
        </ElFormItem>
        <ElFormItem label="负责人" prop="operator_id">
          <ElSelect v-model="editForm.operator_id" filterable multiple>
            <ElOption v-for="item in adminUserList" :key="item.id" :label="item.name" :value="item.id" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="语言" prop="language">
          <div class="flex gap-2 w-full">
            <ElSelect v-model="editForm.language" clearable multiple>
              <ElOption v-for="item in languageList" :key="item.code" :label="item.name" :value="item.code" />
            </ElSelect>
            <ElButton type="primary" @click="selectAllLanguage">全选</ElButton>
          </div>
        </ElFormItem>
        <ElFormItem label="主键" prop="keys">
          <div class="w-full">
            <div>
              <ElButton type="primary" @click="addExistKey">添加已有key</ElButton>
              <ElButton type="primary" @click="addKey">新增key</ElButton>
            </div>
            <div>
              <ElTable :data="editForm.keys" style="width: 100%">
                <ElTableColumn prop="key_name" label="key_name">
                  <template #default="scope">
                    <ElInput v-model="scope.row.key_name" maxlength="500" />
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="content" label="源语言（en）">
                  <template #default="scope">
                    <ElInput v-model="scope.row.content" />
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="content" label="操作">
                  <template #default="scope">
                    <ElButton type="danger" link @click="deleteKey(scope)">删除</ElButton>
                  </template>
                </ElTableColumn>
              </ElTable>
            </div>
          </div>
          
        </ElFormItem>
      </ElForm>
      <template #footer>
        <div class="flex justify-end">
          <ElButton  @click="cancelEditTask">取消</ElButton>
          <ElButton type="primary" @click="saveEditTask">保存</ElButton>
        </div>
      </template>
    </ElDialog>
    <ElDialog :close-on-click-modal="false" v-model="keyPop" title="添加已有Key" width="700px">
      <div class="flex justify-between items-center">
        <div class="flex gap-2">
          <ElInput v-model="keySearch" placeholder="查询已有主键" />
          <ElButton type="primary" @click="getExistKey">搜索</ElButton>
        </div>
        <div class="flex gap-2">
          <ElButton type="primary" @click="selectAllKey">全选</ElButton>
          <ElButton type="primary" @click="batchAddKey" :disabled="selectedKeys.length === 0">批量添加</ElButton>
        </div>
      </div>
      <div class="mt-4">
        <ElTable 
          :data="keyList" 
          ref="keyTableRef"
          v-loading="existKeyLoading"
          style="width: 100%"
          :row-key="row => row.key_name"
          @selection-change="handleSelectionChange"
        >
          <ElTableColumn type="selection" width="55" />
          <ElTableColumn prop="key_name" label="key_name" />
          <ElTableColumn prop="content" label="操作">
            <template #default="scope">
              <ElButton type="primary" link @click="addExistKeySave(scope.row)">添加</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
    </ElDialog>

  </div>
</template>
<script setup lang="ts">
  import {
    ElTable,
    ElTableColumn,
    ElPagination,
    ElButton,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElDatePicker,
    ElMessage,
    ElLoading,
    ElMessageBox
  } from 'element-plus'
  import { ref, onMounted } from 'vue'
  import { apiGetProjectTaskList, apiGetProjectDetail, apiGetAdminUserList, apiGetLanguageList, apiCheckKeyname, apiUpdateTask, apiGetTaskDetail, apiGetTaskOption, apiExportTaskExcel, apiSearchKeyname } from './language-translate-api'
  import { useRoute, useRouter } from 'vue-router'
  import dayjs from 'dayjs'
  import { get_k_sso_token } from 'src/lib/device-id.ts'

  const tableData = ref([])
  const taskPop = ref(false)
  const route = useRoute()
  const router = useRouter()
  const keyTableRef = ref()
  const formInit = {
    id: 0, // ID为0表示创建，非0表示修改
    project_id: 0, // 关联的翻译项目ID
    name: '',
    deadline_time: '',
    work_link: '', // 工单链接
    language: [],
    operator_id: [], // 负责人ID
    keys: [],
  }
  const statusList = ref([
    {
      label: '待翻译',
      value: 1,
    },
    {
      label: '翻译中',
      value: 2,
    },
    {
      label: '翻译完成',
      value: 3,
    },
    {
      label: '已发布',
      value: 4,
    },
  ])
  const submitLoading = ref(false)
  const editFormRef = ref()
  const adminUserList = ref([])
  const rules = {
    name: [
      { required: true, message: '请输入任务名称', trigger: 'blur' },
    ],
    language: [
      { required: true, message: '请选择语言', trigger: 'blur' },
    ],
  }
  const keyList = ref([])
  const keyPop = ref(false)
  const keySearch = ref('')
  const selectedKeys = ref([])
  const languageList = ref([])
  const currentProjectData = ref({})
  const editForm = ref({
    id: 0, // ID为0表示创建，非0表示修改
    project_id: 3, // 关联的翻译项目ID
    name: '服务端VIP相关文案',
    deadline_time: '2025-05-18 16:01:00',
    work_link: 'https://...', // 工单链接
    language: ['en', 'de', 'fr', 'hi'],
    operator_id: [158, 160], // 负责人ID
    keys: [
      {
        key_name: 'vip_download_success_tips',
        content: 'Download Success!', // 初始英文翻译
        }, {
        key_name: 'vip_download_failed_tips',
        content: 'Download Failed!',
      },
    ],
  })
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
    total: 0,
  })
  const transDetailData = ref({})
  const translatePop = ref(false)

  const addKey = () => {
    if(!editForm.value.keys){
      editForm.value.keys = []
    }
    editForm.value.keys.push({
      key_name: '',
      content: '',
    })
  }

  const addExistKey = () => {
    keySearch.value = ''
    keyPop.value = true
    selectedKeys.value = []
    getExistKey()
  }
  const existKeyLoading = ref(false)
  const getExistKey = () => {
    existKeyLoading.value = true
    apiSearchKeyname({
      key_name: keySearch.value,
      platform: currentProjectData.value.platform,
    }).then((res) => {
      keyList.value = res.data.keys
      console.log(res)
    }).catch((err) => {
      ElMessage.error(err?.response?.data?.err_msg || '请求失败')
    }).finally(() => {
      existKeyLoading.value = false
    })
  }

  const selectAllLanguage = () => {
    editForm.value.language = languageList.value.map((item: any) => item.code)
  }

  const addExistKeySave = (row: any) => {
    editForm.value.keys.push({
      key_name: row.key_name,
      content: row.content,
    })
    keyPop.value = false
    keySearch.value = ''
    keyList.value = []
    selectedKeys.value = []
    ElMessage.success('添加成功')
  }

  const deleteKey = (row: any) => {
    console.log(row)
    const index = row.$index
    editForm.value.keys.splice(index, 1)
    // ElMessage.success('删除成功')
  }

  const selectAllKey = () => {
    keyTableRef.value.toggleAllSelection()
  }

  const batchAddKey = () => {
    console.log(selectedKeys.value)
    if(selectedKeys.value.length === 0) {
      ElMessage.error('请选择要添加的key')
      return
    }
    let existKeys = []
    let newKeys = []
    for(let i in keyList.value) {
      if(selectedKeys.value.includes(keyList.value[i].key_name)) {
        if(editForm.value.keys.find((item: any) => item.key_name === keyList.value[i].key_name)) {
          existKeys.push(keyList.value[i].key_name)
        }else{
          editForm.value.keys.push(keyList.value[i])
          newKeys.push(keyList.value[i].key_name)
        }
      }
    }
    if(existKeys.length > 0) {
      ElMessageBox.alert('已存在：' + existKeys.join(', '), '提示', {
        confirmButtonText: '确定',
        // cancelButtonText: '取消',
      })
    }
    keyPop.value = false
    if(newKeys.length > 0) {
      ElMessage.success('批量添加成功')
    }
  }

  const publishTask = (row: any) => {
    console.log(row)
    ElMessageBox.confirm('确定发布任务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }).then(() => {
      const loading = ElLoading.service({
        lock: true,
        text: '发布中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      apiGetTaskOption({
        id: row.id,
        option: 2,
      }).then((res) => {
        if(res.code === 200) {
          ElMessage.success('发布成功')
          getProjectTaskList()
        } else {
          ElMessage.error(res.err_msg)
        }
      }).catch((err) => {
        ElMessage.error(err?.response?.data?.err_msg || '请求失败')
      }).finally(() => {
        loading.close()
      })
    })
  }

  const deleteTask = (row: any) => {
    ElMessageBox.confirm('确定删除任务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }).then(() => {
      const loading = ElLoading.service({
        lock: true,
        text: '删除中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      apiGetTaskOption({
        id: row.id,
        option: 1,
      }).then((res) => {
        if(res.code === 200) {
          ElMessage.success('删除成功')
          getProjectTaskList()
        } else {
          ElMessage.error(res.err_msg)
        }
      }).catch((err) => {
        ElMessage.error(err?.response?.data?.err_msg || '请求失败')
      }).finally(() => {
        loading.close()
      })
    })
  }

  const handleSelectionChange = (selection: any) => {
    console.log(selection)
    selectedKeys.value = selection.map((item: any) => item.key_name)
    console.log(selectedKeys.value)
  }

  const handleBatchAdd = () => {
    console.log(selectedKeys.value)
  }

  const getExistKeyList = () => {
  }

  const cancelEditTask = () => {
    taskPop.value = false
  }

  const exportExcel = (row: any) => {
    const loading = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    
    fetch(import.meta.env.VITE_DRAMA_API_URL+'/translation/task/excel/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'token': get_k_sso_token(),
        'Accept': '*/*'
      } as HeadersInit,
      body: JSON.stringify({
        task_id: row.id
      })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response.blob()
    })
    .then(blob => {
      if (blob.size === 0) {
        throw new Error('下载的文件为空')
      }
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      const fileName = `${row.name}-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    })
    .catch(err => {
      ElMessage.error(err.message || '下载失败')
      console.error('下载失败:', err)
    })
    .finally(() => {
      loading.close()
    })
  }

  const editTask = (row: any) => {
    const loading = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    apiGetTaskDetail({
      task_id: row.id,
    }).then((res) => {
      if(res.code === 200) {
        editForm.value = JSON.parse(JSON.stringify(res.data))
        editForm.value.keys = editForm.value.items
        taskPop.value = true
      } else {
        ElMessage.error(res.err_msg)
      }
    }).catch((err) => {
      ElMessage.error(err?.response?.data?.err_msg || '请求失败')
    }).finally(() => {
      loading.close()
    })
  }

  const saveEditTask = () => {
    let hasNullKey = false
    let allKey = {}
    for(let i in editForm.value.keys) {
      if(editForm.value.keys[i].key_name.length > 500) {
        ElMessage.error('key_name不能超过500个字符')
        return false
      }
      if(editForm.value.keys[i].key_name === '') {
        ElMessage.error('key_name不能为空')
        hasNullKey = true
        return false
      }

      if(editForm.value.keys[i].content === '') {
        ElMessage.error('源语言不能为空')
        hasNullKey = true
        return false
      }
      if(allKey[editForm.value.keys[i].key_name]) {
        ElMessage.error('存在相同的key_name：' + editForm.value.keys[i].key_name + '，保存失败')
        return false
      }else{
        allKey[editForm.value.keys[i].key_name] = 1
      }
    }
    editFormRef.value.validate().then((valid) => {
      if (valid) {
        submitLoading.value =  ElLoading.service({
          lock: true,
          text: '提交中...',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        console.log(editForm.value)
        if(editForm.value.id === 0) {
          checkKeyname()
        } else {
          updateTask()
        }
      } else {
        console.log('error submit!!')
      }
    })
  }

  const updateTask = () => {
    
    apiUpdateTask({
      ...editForm.value,
      project_id: currentProjectData.value.id,
    }).then((res) => {
      if(res.code === 200) {
        ElMessage.success(editForm.value.id === 0 ? '任务创建成功' : '任务更新成功')
        getProjectTaskList()
        taskPop.value = false
      } else {
        ElMessage.error(res.err_msg)
      }
    }).catch((err) => {
      ElMessage.error(err?.response?.data?.err_msg || '请求失败')
    }).finally(() => {
      submitLoading.value.close()
    })
  }

  const checkKeyname = () => {
    apiCheckKeyname({
      key_name: editForm.value.name,
      platform: currentProjectData.value.platform,
    }).then((res) => {
      if(!res.data.already_exists) {
        submitLoading.value =  ElLoading.service({
          lock: true,
          text: '提交中...',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        updateTask()
      } else {
        ElMessage.error('任务名称已存在')
      }
    }).finally(() => {
      submitLoading.value.close()
    })
  }

  const cancelAddExistKey = () => {
    keyPop.value = false
  }

  const saveAddExistKey = () => {
    console.log(selectedKeys.value)
  }

  const cancelTranslate = () => {
    translatePop.value = false
  }

  const saveTranslate = () => {
    console.log(translateForm.value)
  }

  const createTask = () => {
    editForm.value = JSON.parse(JSON.stringify(formInit))
    taskPop.value = true
  }

  const translateDetail = (row: any) => {
    router.push({
      path: '/language-translate-task-detail/' + row.id,
    })
  }

  const handleSizeChange = (size: number) => {
    pageInfo.value.page_size = size
    pageInfo.value.page_index = 1
    getProjectTaskList()
  }

  const handleCurrentChange = (index: number) => {
    pageInfo.value.page_index = index

    getProjectTaskList()
  }
  const getProjectTaskList = () => {
    console.log(route.params.id)
    const loading = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const projectId = Number(route.params.id)
    apiGetProjectTaskList({
      project_id: projectId,
      page_index: pageInfo.value.page_index,
      page_size: pageInfo.value.page_size,

    }).then((res) => {
      tableData.value = res.data.items
      pageInfo.value.total = res.data.total
    }).catch((err) => {
      ElMessage.error(err?.response?.data?.err_msg || '请求失败')
    }).finally(() => {
      loading.close()
    })
  }

  const getProjectDetail = () => {
    const projectId = Number(route.params.id)
    apiGetProjectDetail({
      id: projectId,
    }).then((res) => {
      console.log(res)
      currentProjectData.value = res.data
      getLanguageList()
    })
  }

  const getAdminUserList = () => {
    apiGetAdminUserList().then((res) => {
      console.log(res)
      adminUserList.value = res.data.items
    })
  }

  const getLanguageList = () => {
    apiGetLanguageList({
      platform: currentProjectData.value.platform,
    }).then((res) => {
      console.log(res)
      languageList.value = res.data.items
    })
  }
  onMounted(() => {
    getProjectTaskList()
    getProjectDetail()
    getAdminUserList()
  })
</script>

