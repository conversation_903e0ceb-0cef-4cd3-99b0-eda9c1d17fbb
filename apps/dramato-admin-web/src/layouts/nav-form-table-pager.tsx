import { createComponent, mc, SlotFn, useBool, type ClassName } from '@skynet/shared'
import { Button, Icon, MergeClass } from '@skynet/ui'
import { isNil } from 'lodash-es'

type NavFormTablePagerOptions = {
  props: {
    stickyHeader?: boolean
    tableClass?: ClassName
    headerClass?: ClassName
    canExpand?: boolean
  }
  slots: {
    nav: SlotFn
    form: SlotFn
    pager: SlotFn
    tableActions: SlotFn
    table: SlotFn
  }
}
export const NavFormTablePager = createComponent<NavFormTablePagerOptions>({
  props: {
    stickyHeader: false,
    tableClass: '',
    headerClass: '',
    canExpand: true,
  },
}, (props, { slots }) => {
  const [active, toggleActive] = useBool(true)
  return () => (
    <MergeClass tag="x-nav-form-table-pager" baseClass="block space-y-4 py-4 relative ">
      {(!isNil(slots.nav) || !isNil(slots.form) || !isNil(slots.pager))
      && (
        <header class={mc('overflow-hidden relative space-y-2', active.value ? 'h-auto' : 'h-12',
          props.stickyHeader ? 'sticky bg-white z-table-header  top-16 shadow-lg ' : '',
          props.headerClass)}>
          {/* 收起按钮 */}
          {props.canExpand
          && (
            <div class="flex justify-end h-9 items-center  absolute right-0 top-0 py-1">
              <Button class="btn btn-sm btn-ghost" onClick={() => toggleActive()}>
                <Icon name="heroicons:chevron-up" /> {active.value ? '收起' : '展开'}
              </Button>
            </div>
          )}
          {/* 面包屑导航 */}
          {!isNil(slots.nav) && (
            <section class="breadcrumbs text-sm px-4">
              {slots.nav()}
            </section>
          )}
          {active.value
            ? (
                <>
                  {/* 过滤表单 */}
                  {!isNil(slots.form) && (
                    <section class="bg-base-100 rounded-lg px-4 pt-4 pb-1">
                      {slots.form()}
                    </section>
                  )}
                  {/* pager */}
                  {!isNil(slots.pager) && (
                    <section class="">
                      {slots.pager()}
                    </section>
                  )}
                </>
              )
            : null}
        </header>
      )}
      {/* table */}
      {(!isNil(slots.tableActions) || !isNil(slots.table)) && (
        <section class={mc(' rounded-lg pt-4 pb-4 bg-base-100', props.tableClass)}>
          {slots.tableActions && (
            <>
              <div class="px-4">
                {slots.tableActions()}
              </div>
              <hr class="my-4" />
            </>
          )}
          {slots.table && (
            slots.table()
          )}
        </section>
      )}
      {!isNil(slots.pager) && (
        <section>
          {slots.pager()}
        </section>
      )}
      {/* pager */}
    </MergeClass>
  )
})
