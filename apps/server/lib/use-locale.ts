import { getQuery } from '@skynet/shared'
import { useI18n } from 'vue-i18n'
import { Ref } from 'vue'

// @eslint-disable-next-line
export const useLocale = (language?: string): {
  t: (key: string, replace?: Record<string, unknown>) => string
  locale: Ref<string>
} => {
  language = language ?? getQuery('language', 'en')
  const { t, locale } = useI18n()
  const supportedLocales = ['ja', 'en', 'ko', 'es', 'th', 'id', 'vi', 'pt', 'tl', 'it', 'fr', 'de', 'tr', 'ru', 'ms', 'hi']
  const normalizedLanguage = language.toLowerCase()
  if (normalizedLanguage === 'zh') {
    const country = getQuery('country_code', 'CN')
    if (country) {
      locale.value = `zh-${country}`
    }
  } else {
    locale.value = supportedLocales.includes(normalizedLanguage) ? normalizedLanguage : 'en'
  }
  return { t, locale }
}
