import { createComponent, mc } from '@skynet/shared'
import DefaultCover from 'src/h5_modules/images/cover-def.webp'
import { icHistory } from 'src/h5_modules/images/images'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useRoute } from 'vue-router'
import { openDialog } from 'src/h5_modules/common/dialog'
import { Fn } from '@vueuse/core'
import { borderBg, newEpisodesLaunched } from '../images/images'
import { Button } from '@skynet/ui'
import { goToApp } from 'src/lib/go-to-app'
import { isIOS } from '@skynet/ui/utils'
import { T } from './drama-card-i18n'
import { h5Track } from 'src/lib/h5-track'
import { reportNow } from '@skynet/client-track'

let closeLimitDialog: Fn

export const closeCurrentLimitDialog = () => {
  if (closeLimitDialog) {
    closeLimitDialog() // 执行关闭
    closeLimitDialog = null
  }
}
export const openLimitDialog = (drama: M.Drama, from: string, sessionId: string, closeFn?: () => void) => {
  // 关闭已存在的弹窗
  closeCurrentLimitDialog()
  h5Track('video', 'first_block', 'show', {
    series_id: drama.id,
    video_id: drama?.episode?.id,
    from: from,
    pop_type: 3,
    pop_scene: 'video',
    video_type: 'charge',
    session_id: sessionId,
  })
  closeLimitDialog = openDialog({
    body: () => (
      <x-dialog-content class="relative block overflow-hidden rounded-xl bg-[#434546] shadow-[0px_2px_16px_0px_rgba(0,0,0,0.16)]">
        <img src={drama.cover} class="h-60vh relative inset-0 block w-full rounded-xl bg-[#434546] object-cover" width="288" height="384" />
        <img src={borderBg} class="absolute inset-0 z-up-up block size-full" />
        <x-mask-1 class="h-100px absolute bottom-0 left-0 w-full rounded-b-lg bg-zinc-900" />
        <x-mask-2 class="bottom-99px absolute left-0 h-28 w-full bg-gradient-to-b from-zinc-900/0 to-zinc-900" />
        <div class="bottom-10px absolute left-0 z-up-up flex h-auto w-full flex-col items-center gap-y-0 text-center text-2xl text-white">
          <x-title class="mt-5px w-full truncate px-3 text-center text-base font-bold text-white">
            {drama.name}
          </x-title>
          <x-description class="mt-2 line-clamp-2 text-center font-['SF_Pro'] text-xs font-normal leading-none text-[#CCCACB]">{T.dramaCard.intro()}</x-description>
          <Button class="mx-3 mt-4 flex h-10 w-[calc(100%-24px)] items-center justify-center gap-2.5 rounded-lg !border-none bg-gradient-to-r from-[#fc2763] to-[#ff4634] text-lg font-medium leading-snug text-white !outline-none" onClick={() => {
            h5Track('video', 'first_block', 'click', {
              series_id: drama.id,
              video_id: drama?.episode?.id,
              from: from,
              pop_type: 3,
              pop_scene: 'video',
              video_type: 'charge',
              session_id: sessionId,
            })
            reportNow()
            const deepLink = `dramawave://dramawave.app?redirect=${encodeURIComponent('/detail?id=' + drama.id + '&from=watch-history')}`
            const universalLink = `https://mydramawave.com?redirect=${encodeURIComponent('/detail?id=' + drama.id + '&from=watch-history')}`
            goToApp(isIOS() ? universalLink : deepLink)
          }}>{T.dramaCard.play()}</Button>
        </div>
      </x-dialog-content>
    ),
    closeVisible: true,
    beforeClose: () => {
      closeFn?.()
      closeLimitDialog = null
      return true // 确保关闭操作继续执行
    },
  })
}

type DramaCardOptions = {
  props: {
    drama: M.Drama
    is_last_history?: boolean
  }
}
export const DramaCard = createComponent<DramaCardOptions>({
  props: {
    drama: {},
    is_last_history: false,
  },
}, props => {
  const cover = ref<string>(props.drama.cover || '')
  const loadingCover = ref<boolean>(false)
  const router = useRouter()

  const onSeeMoreHistory = () => {
    void router.push('/watch-history')
  }

  const gotoPaly = () => {
    // if (props.drama.release_round === 1) {
    //   openLimitDialog(props.drama, 'my_list')
    // } else {
    window.location.href = window.location.origin + '/series/' + props.drama.id + '/' + props.drama?.episode?.id + '?return_to=/watch-history&from_page=library/history'
    // void router.push('/series/' + props.drama.id + '/' + props.drama?.episode?.id)
    // }
  }

  const playDrama = () => {
    if (props.is_last_history) {
      return
    }

    // go to play page
    gotoPaly()
  }

  return () => (
    <x-drama-card class="flex h-auto w-full items-center gap-x-3 overflow-hidden rounded-lg" onClick={playDrama}>
      <div class="w-22.5 min-w-22.5 min-h-30 h-30 relative block overflow-hidden rounded-lg">
        <img
          class={mc('size-full object-cover rounded-lg', loadingCover.value ? 'opacity-0' : '')}
          src={props.drama.cover || ''}
          onLoad={() => loadingCover.value = false}
          onError={() => cover.value = DefaultCover}
        />
        {
          props.is_last_history && (
            <x-view-more onClick={onSeeMoreHistory} class="absolute left-0 top-0 flex size-full flex-col items-center justify-center gap-y-2 bg-[rgba(11,8,11,0.6)]">
              <img src={icHistory} class="size-11" />
              <div class="text-3.5 text-center text-[#FDFBFC]">See All</div>
            </x-view-more>
          )
        }
      </div>
      {
        !props.is_last_history && (
          <x-drama-desc-wrap class="flex flex-1 flex-col items-start justify-start gap-y-1 overflow-hidden p-2 pl-0">
            <x-drama-card-title class="text-4 block max-h-5 w-full truncate break-words leading-5 text-[#fdfbfc]">{props.drama.name}</x-drama-card-title>
            <x-drama-card-title class="text-3 leading-3.5 block max-h-5 w-full truncate break-words text-[#797b7d]">
              EP.{ props.drama?.view_episode } / EP.{ props.drama?.episode_count }
            </x-drama-card-title>
          </x-drama-desc-wrap>
        )
      }
    </x-drama-card>
  )
})

export default DramaCard
