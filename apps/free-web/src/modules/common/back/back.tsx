import { ClassName, createComponent, mc } from '@skynet/shared'
import backLight from './images/back-light.svg'
import back from './images/back.svg'
import { jsBridge } from 'src/lib/jsbridge'
import { onMounted, ref } from 'vue'
import { getDeviceInfo } from 'src/lib/get-device-info'
import router from 'src/router'
import { isIos } from 'src/lib/ua'
type BackOptions = {
  props: {
    hasBack?: boolean
    isCloseWebview?: boolean
    title?: string
    isWhite?: boolean
    class?: ClassName
  }
  slots: {
    default?: () => unknown
  }
}
export const Back = createComponent<BackOptions>({
  props: {
    hasBack: true,
    isCloseWebview: false,
    title: '',
    isWhite: false,
    class: '',
  },
}, (props, { slots }) => {
  const topDistance = ref<number>()
  onMounted(async () => {
    if (isIos()) return
    const deviceInfo = await getDeviceInfo()
    topDistance.value = ((deviceInfo?.safe_area_height ?? 0) / window.devicePixelRatio) ?? 0
  })
  return () => (
    <x-back class={mc('bg-[var(--surface-1)] z-100 px-3 flex items-center static top-0', props.title ? 'justify-center' : 'justify-start', isIos() ? 'h-[calc(env(safe-area-inset-top)+2.75rem)] pt-[env(safe-area-inset-top)]' : 'h-11', props.class)} style={isIos() ? '' : `padding-top:${topDistance.value}px; height:${(topDistance.value ?? 0) + 44}px`}>
      {
        props.hasBack && (
          <img src={props.isWhite ? back : backLight} class="w-6 h-6 relative left-0 top-0 shrink-0 object-cover z-up-up" onClick={() => {
            if (props.isCloseWebview) {
              void jsBridge('close')
            } else {
              router.back()
            }
          }}
          />
        )
      }
      {
        props.title
        && (
          <x-title class="flex-1 flex items-center justify-center z-up ml--5">
            {props.title}
          </x-title>
        )
      }
      {slots.default?.()}
    </x-back>
  )
})
