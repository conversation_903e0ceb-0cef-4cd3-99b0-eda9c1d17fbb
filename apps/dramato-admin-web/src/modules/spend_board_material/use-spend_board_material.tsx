/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetSpendBoardMaterialList, apiQueryByKeywords, apiResourceQueryByKeywords } from './spend_board_material-api'
import dayjs from 'dayjs'
import { DramaItem } from 'src/modules/content-template/series-store.tsx'
import { throttle } from 'lodash-es'

export const useSpendBoardMaterial = () => {
  return {
    Form,
    params,
    list,
    loading,
    total,
    search,
    isUpdating,
    dramaList,
    onUpdateDrama,
    dramaLoading,
    selectDramas,
    onSearchDrama,
    limitText,
    clearAllDrama,
    resourceList,
    onSearchResource,
    selectResources,
    resourceLoading,
    onUpdateResource,
    clearAllResource,
  }
}

const Form = CreateForm<M.SpendBoardMaterial.params>()
const params = ref<M.SpendBoardMaterial.params>({
  page_index: 1,
  page_size: 20,
  // task_time_start: dayjs(dayjs().format('YYYY-MM-DD 00:00:00')).unix(),
  // task_time_end: dayjs(dayjs().format('YYYY-MM-DD 23:59:59')).unix(),
})
const list = ref<M.SpendBoardMaterial.ListItem[]>([])
const loading = ref<boolean>(false)
const total = ref<number>(1)
const isUpdating = ref(false)
const dramaList = ref<DramaItem[]>([])

const search = async (_page?: number) => {
  _page = _page || (params.value?.page_index || 0) + 1
  loading.value = true
  const res = await apiGetSpendBoardMaterialList({
    ...params.value,
    task_time_start: params.value.task_time_start || 0,
    task_time_end: params.value.task_time_end || 0,
  })
    .finally(() => {
      loading.value = false
    })
  list.value = res.data?.list || []
  total.value = res.data?.total_count || 0
  params.value.page_index = _page
}

const onSearchDrama = throttle((val: string) => {
  dramaLoading.value = true
  void apiQueryByKeywords({
    drama_name: val,
  }).then(res => {
    if (res.data) {
      dramaList.value = res.data.list
    }
  }).finally(() => {
    dramaLoading.value = false
  })
}, 100)

const selectDramas = ref<any[]>([])
const dramaLoading = ref<boolean>(false)

const onUpdateDrama = (raw: any) => {
  console.log('onUpdateDrama', raw)
  selectDramas.value = raw
  params.value.series_key = raw.map((i: any) => i.series_key).join(',')
}

const limitText = (count: number) => {
  return `和其他 ${count} 个选项`
}

const clearAllDrama = () => {
  selectDramas.value = []
}

// 搜索资源相关
const resourceList = ref<{
  resource_id: number
  resource_name: string
}[]>([])

const onSearchResource = throttle((val: string) => {
  resourceLoading.value = true
  void apiResourceQueryByKeywords({
    resource_name: val,
  }).then(res => {
    if (res.data) {
      resourceList.value = res.data.list
    }
  }).finally(() => {
    resourceLoading.value = false
  })
}, 100)

const selectResources = ref<any[]>([])
const resourceLoading = ref<boolean>(false)

const onUpdateResource = (raw: any) => {
  console.log('onUpdateResource', raw)
  selectResources.value = raw
  params.value.series_resource_id = raw.map((i: any) => i.resource_id).join(',')
}

const clearAllResource = () => {
  selectResources.value = []
}
