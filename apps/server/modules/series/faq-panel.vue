<template>
  <van-action-sheet v-model:show="show">
    <div class="pb-4 pt-2">
      <div class="flex flex-col justify-center text-[#fdfbfc]">
        <div
class="right-arrow px-3 py-4" style="background-position: right 1rem center" @click="gotoServiceCenter">
          <p class="flex items-center text-base">
            <SvgIcon name="ic-information-circle" class="mr-2 size-6 shrink-0" />
            {{ T.productPanel.FAQ.ref.value }}
          </p>
        </div>
      </div>
      <div class="flex flex-col justify-center text-[#fdfbfc]">
        <div
class="right-arrow px-3 py-4" style="background-position: right 1rem center" @click="goToFeedback">
          <p class="flex items-center text-base">
            <SvgIcon name="ic-envelope" class="mr-2 size-6 shrink-0" />
            {{ T.productPanel.feedback.ref.value }}
          </p>
        </div>
      </div>
    </div>
  </van-action-sheet>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useLocale } from '~/modules/i18n/i18n'
import { ActionSheet as vanActionSheet } from 'vant';
import { SvgIcon } from '@skynet/ui'
import { T } from './product-panel.i18n'
// import { useRouter } from 'vue-router'
const { currentLocale } = useLocale()

// const router = useRouter();

const props = defineProps({
  faqPanelVisible: {
    type: Boolean,
    required: false
  }
});

const emit = defineEmits(['update:faqPanelVisible']);
const show = ref(props.faqPanelVisible);

const gotoServiceCenter = () => {
  const language = currentLocale.value.language;
  const url = new URL('/customer-service-center', window.location.origin);
  url.search = new URLSearchParams({
    language: language.split('-')[0] || '',
    country_code: language.split('-')[1] || '',
    from: 'series'
  }).toString();
  show.value = false;

  window.location.href = url.toString();
}
const goToFeedback = () => {
  const language = currentLocale.value.language;
  const url = new URL('/customer-service-center/feedback', window.location.origin);
  url.search = new URLSearchParams({
    language: language.split('-')[0] || '',
    country_code: language.split('-')[1] || '',
    from: 'series'
  }).toString();
  show.value = false;

  window.location.href = url.toString();
  // void router.push({
  //           path: '/customer-service-center/feedback',
  //           query: { language: currentLocale?.language?.split('-')[0], country_code: currentLocale?.language?.split('-')[1], from: 'series' }
  //         })
}

watch(() => props.faqPanelVisible, (newVal) => {
  show.value = newVal;
}, { immediate: true });

watch(show, (newVal) => {
  if (newVal !== props.faqPanelVisible) {
    emit('update:faqPanelVisible', newVal);
  }
});
</script>