import { createComponent } from '@skynet/shared'
import { onMounted, ref } from 'vue'
import { SvgIcon } from '../ui'
import { useImagePreviewStore } from './use-preview-store'

export interface ImagePreviewOptions {
  imageList: {
    src: string
    width: number
    height?: number
  }[]
  canEscClose?: boolean
  hideOnClickModal?: boolean
}
export type ImagePreviewType = {
  props: ImagePreviewOptions
}
export const ImagePreview = createComponent<ImagePreviewType>({
  props: {
    imageList: [],
    canEscClose: true,
    hideOnClickModal: true
  },
}, props => {
  const currentIndex = ref(0)
  const { hideImagePreviewDialog } = useImagePreviewStore()

  const onLeftClick = () => {
    currentIndex.value = currentIndex.value - 1 || 0
  }

  const onRightClick = () => {
    currentIndex.value = currentIndex.value + 1 > props.imageList.length - 1 ? props.imageList.length - 1 : currentIndex.value + 1
  }

  const keyDownFn = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      hideImagePreviewDialog()
    }
  }

  onMounted(() => {
    console.log('>>> ImagePreview mounted <<<', props.imageList)
    props.canEscClose && window.addEventListener('keydown', keyDownFn)
  })

  return () => (
    <div id="tm-image-preview-dialog" class="fixed top-0 left-0 right-0 bottom-0 flex justify-between items-center z-dialog bg-black bg-opacity-50 px-6">
      <div class="absolute top-0 left-0 right-0 bottom-0 z-[-1]" onClick={e => {
        e.preventDefault()
        e.stopPropagation()
        props.hideOnClickModal && hideImagePreviewDialog()
      }}
      />
      <SvgIcon name="ic_close_fill" class="size-5 cursor-pointer absolute right-5 top-5" onClick={hideImagePreviewDialog} />
      <div
        class="size-8 rounded-full bg-white bg-opacity-10 flex justify-center items-center cursor-pointer"
        onClick={onLeftClick}
        style={{
          visibility: currentIndex.value === 0 ? 'hidden' : 'visible',
        }}
      >
        <SvgIcon name="ic_arrow_back_light" class="size-5" />
      </div>
      <img
        class="object-cover object-c"
        style={{
          width: props.imageList[currentIndex.value]?.width / 4 + 'px',
          height: props.imageList[currentIndex.value]?.height ? (props.imageList[currentIndex.value]?.height || 0) / 4 + 'px' : 'auto',
        }}
        src={props.imageList[currentIndex.value]?.src}
      />
      <div
        class="size-8 rounded-full bg-white bg-opacity-10 flex justify-center items-center cursor-pointer"
        onClick={onRightClick}
        style={{
          visibility: currentIndex.value === props.imageList.length - 1 ? 'hidden' : 'visible',
        }}
      >
        <SvgIcon name="ic_arrow_back_light" class="size-5 rotate-180" />
      </div>
    </div>
  )
})
