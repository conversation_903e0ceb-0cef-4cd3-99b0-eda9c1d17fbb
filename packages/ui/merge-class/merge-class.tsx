import { ClassName, createComponent, mc, SlotFn } from '@skynet/shared'
import { h, HTMLAttributes } from 'vue'
type MergeClassOptions = {
  props: {
    tag?: string
    baseClass?: ClassName
    class?: ClassName
  }
  slots: {
    default: SlotFn
  }
}
export const MergeClass = createComponent<MergeClassOptions, HTMLAttributes>({
  inheritAttrs: false,
  props: {
    tag: 'div',
    baseClass: '',
    class: '',
  },
}, (props, { slots, attrs }) => {
  return () => {
    const localClass = mc('block', props.baseClass)
    return h(props.tag, { ...attrs, className: mc(localClass, props.class) }, slots.default?.() ?? undefined)
  }
})
