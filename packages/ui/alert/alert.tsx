import { createComponent, fn, required } from '@skynet/shared'
import { useAlertStore } from './use-alert-store'
import { MergeClass } from '../ui'
import { computed, onMounted, ref, VNodeChild, watchEffect } from 'vue'
import { AlertType } from './alert-types'
import { Fn, TransitionPresets, useTransition } from '@vueuse/core'

export interface AlertProps {
  alertId: string
  content?: VNodeChild | null
  type?: AlertType
  autoHide?: boolean | number
  /**
   * 由于 alert 的出现和消失伴随动画，所以 visible 为 false 时，不代表 alert 一定不被用户看见
   */
  visible?: boolean
}

interface AlertOptions {
  props: AlertProps
  emits: {
    destroy: Fn
  }
}
export const Alert = createComponent<AlertOptions>({
  props: {
    alertId: required,
    content: null,
    type: 'success',
    autoHide: 3000,
    visible: true,
  },
  emits: {
    destroy: fn,
  },
}, (props, { emit }) => {
  const { icons, addToHideList, removeFromHideList } = useAlertStore()
  const duration = computed(() => typeof props.autoHide === 'number' ? props.autoHide : 5000)
  onMounted(() => {
    watchEffect(() => {
      const time = new Date().getTime() + duration.value
      console.log('3: ', props.autoHide, time)
      props.autoHide
        ? addToHideList(props.alertId, time)
        : removeFromHideList(props.alertId)
    })
  })
  const localVisible = ref(true)
  const alert = ref<HTMLElement | null>(null)
  const y = ref(300)
  const targetY = useTransition(y, {
    duration: 300,
    transition: TransitionPresets.easeOutCubic,
    onFinished: () => {
      if (props.visible === false || localVisible.value === false) {
        emit('destroy')
      }
    },
  })
  watchEffect(() => {
    if (props.visible) {
      y.value = 0
    } else {
      y.value = -300
    }
  })
  watchEffect(() => {
    if (localVisible.value === false) {
      y.value = -300
    }
  })

  return () => (
    <MergeClass
      ref={alert}
      baseClass={['inline-flex transform translate-y-full alert shadow-lg !w-auto',
        props.type === 'info' && 'alert-info',
        props.type === 'error' && 'alert-error',
        props.type === 'warning' && 'alert-warning',
        props.type === 'success' && 'alert-success',
      ]}
      style={{
        transform: `translateY(${targetY.value}%)`,
        opacity: (100 - Math.abs(targetY.value) / 3 * 0.5) / 100,
      }}
    >
      <span>{icons.value[props.type] }</span>
      <div>{props.content}</div>
    </MergeClass>
  )
})

export const showAlert = (content: AlertProps['content'], type?: AlertProps['type'], alertProps?: Omit<AlertProps, 'content' | 'type' | 'alertId'>) => {
  const { addAlert, updateAlert } = useAlertStore()
  const id = addAlert({ content, type, ...alertProps })
  const hideAlert = () => updateAlert(id, { visible: false })
  return hideAlert
}
