import { onMounted, ref } from 'vue'
import { createComponent } from '@skynet/shared'
import { useMealCheckInStore } from './meal-check-in-store'
import Loading from 'src/modules/common/loading/loading'
import { useLocale } from 'src/lib/use-locale'
import { jsBridge } from 'src/lib/jsbridge'
import { Back } from 'src/modules/common/back/back'
import { Button } from '@skynet/ui'
import { openDialog } from 'src/modules/common/dialog'
import { showToast } from 'src/modules/common/toast/toast'
import { DialogInfo } from './meal-check-in.d'
import { ApiPostWelfareReceive, ApiPostWelfareADReceive, apiPostWelfareReclaimReceive } from './meal-check-in-api'
import { useBetterScroll } from 'src/lib/use-better-scroll'
import { coin_bg, background, star, coin, logo, qa_bg, toastIcon, diamonds } from './images/images'
import { preloadImages } from 'src/lib/preload-images'
import { useRouter } from 'vue-router'

export const MealCheckInPage = createComponent(null, () => {
  const router = useRouter()
  const { t } = useLocale()
  const { eatDetails, getEatDetails, getPrivilege, privilege } = useMealCheckInStore()
  const wrapper = ref<HTMLDivElement>()
  const { bsInstance: explainBsInstance, init: initExplainBs } = useBetterScroll(wrapper)

  preloadImages([toastIcon, coin_bg])

  onMounted(() => {
    void getEatDetails()
    void getPrivilege()
  })

  const openActiveRulesDialog = () => {
    const closeDialog = openDialog({
      title: t('meal_check_in.rules_title'),
      closeVisible: false,
      customClass: 'bg-[#FDFBFC] rounded-xl shadow',
      body: () => (
        <x-explain-dialog>
          <x-explain ref={wrapper} class="mt-1 block h-[200px] overflow-hidden break-words px-[26px] text-sm font-normal text-[#090609]">
            <x-wrapper id="explain-wrapper" class="block pb-7" style="-webkit-overflow-scrolling: touch;">
              <p class="mb-2 block">{t('meal_check_in.rules_1')}</p>
              <p class="mb-2 block">{t('meal_check_in.rules_2')}</p>
              <p class="block">{t('meal_check_in.rules_3')}</p>
            </x-wrapper>
          </x-explain>
          <x-footer class="relative flex flex-col bg-white">
            <x-mask class="absolute -top-[38px] h-10 w-full bg-gradient-to-b from-[rgba(255,_255,_255,_0)] to-[rgba(255,_255,_255,_1)]" />
            <x-got-it>
              <Button class="no-tap-color mx-4 mb-6 mt-4 h-11 w-[calc(100%-2rem)] rounded-lg border-none bg-[#fc2763] text-base text-white outline-none"
                onClick={() => {
                  closeDialog()
                  explainBsInstance.value?.destroy()
                }}
              >{t('coins.exchange.gotItBtn')}
              </Button>
            </x-got-it>
          </x-footer>
        </x-explain-dialog>
      ),
    })
    initExplainBs()
  }

  const openExchangeSuccessDialog = (dialogMessage: DialogInfo, extra: string, isLast?: true) => {
    const closeDialog = openDialog({
      customClass: 'bg-transparent shadow-none gap-y-0',
      closeVisible: true,
      body: (
        <x-exchange-body style={{ backgroundImage: `url(${coin_bg})`, backgroundSize: '100% auto' }} class="pt-42.5 mb-4 flex min-h-[266px] w-full flex-col bg-no-repeat shadow">
          <x-body-main class="flex w-full flex-col rounded-lg rounded-t-none bg-white px-4">
            <x-title class="line-clamp-2 w-full break-words pt-5 text-center text-lg font-bold text-[#0b080b] ">{dialogMessage.title || dialogMessage.sub_title}</x-title>
            <x-explain class="flex flex-col items-center pt-2 text-center">
              <x-price class="mx-[6px] block max-w-[calc(100%-12px)] truncate text-2xl font-bold text-[#FC2763]">{dialogMessage.reward_text}</x-price>
              {
                (!!privilege.value && privilege.value > 0) && (
                  <x-bonus class="mt-0.5 flex w-auto max-w-full items-center justify-center gap-0.5 rounded-[200px] bg-amber-100 px-2 py-0.5 text-sm font-bold text-[#5A280A]">
                    <span class="flex-1 shrink-0 truncate">VIP Bonus</span>
                    <img src={diamonds} class="block size-5 object-cover" />
                    <span class="max-w-60px shrink-0 truncate">+{dialogMessage.reward_num * (privilege.value)}</span>
                  </x-bonus>
                )
              }
            </x-explain>
            <x-btn class="no-tap-color mb-6 mt-4 w-full rounded-lg bg-[#fc2763] px-3 py-2.5 text-base font-bold text-white" onClick={() => closeDialog()}>
              <p class="line-clamp-2 w-full break-words text-center" onClick={() => {
                void openAdHandle(extra, isLast)
              }}
              >{dialogMessage.major_btn_txt}
              </p>
            </x-btn>
          </x-body-main>
        </x-exchange-body>
      ),
    })
  }

  const openAdHandle = (ad_extra: string, isLast?: boolean) => {
    void jsBridge('playAd', {}).then(() => {
      void ApiPostWelfareADReceive({ ad_extra }).then(res => {
        if (res && res.data) {
          if (isLast) {
            showToast(
              <div class="min-w-30 flex flex-col items-center justify-center gap-y-3 px-5 py-4">
                <img class="size-11" width={44} height={44} src={toastIcon} />
                <p class="text-5 font-bold">{res.data.toast.text}</p>
              </div>,
              'success', // type
              { class: 'rounded-4 !p-0' }, // 可选的其他属性
            )
          } else if (res.data.dialog) {
            openExchangeSuccessDialog(res.data.dialog, res.data.ad_extra.extra, true)
          }
        }
      })
    })
  }

  const cardClickHandle = async () => {
    if (eatDetails.value) {
      const { status } = eatDetails.value
      const { welfare_id } = eatDetails.value
      if (status === 2 || status === 3) {
        return
      }
      if (status === 1) {
        // 可领取
        const res = await ApiPostWelfareReceive({ welfare_id })
        void getEatDetails()
        if (res && res.data) {
          openExchangeSuccessDialog(res.data.dialog, res.data.ad_extra.extra)
        }
      } else if (status === 4) { // 可补领
        void jsBridge('playAd', {}).then(() => {
          void apiPostWelfareReclaimReceive({ welfare_id }).then(res => {
            if (res && res.data) {
              showToast(
                <div class="min-w-30 flex flex-col items-center justify-center gap-y-3 px-5 py-4">
                  <img class="size-11" width={44} height={44} src={toastIcon} />
                  <p class="text-5 font-bold">{res.data.toast.text}</p>
                </div>,
                'success', // type
                { class: 'rounded-4 !p-0' }, // 可选的其他属性
              )
            }
            void getEatDetails()
          })
        })
      }
    }
  }

  return () => (
    !eatDetails.value
      ? (
          <Loading />
        )
      : (
          <x-meal-check-in-page class="relative flex size-full flex-col gap-y-2 overflow-y-auto bg-[#F2E1D9] text-white">
            <img src={background} class="absolute left-0 top-0 z-0 h-auto w-full" />
            <Back isWhite={false} isCloseWebview={true} class="z-100 sticky top-0 flex h-11 shrink-0 items-center bg-[#f0d4c7] px-3" />
            <div class="z-up flex h-[35%] flex-col">
              <x-meal-check-in-title class="z-up mx-7 flex items-center justify-center font-bold text-[#f94f06]">
                <p class="text-6 line-clamp-2 w-full text-center">{eatDetails.value.top_show.title}</p>
              </x-meal-check-in-title>

              <x-tips class="mx-17 z-up flex items-center justify-start text-sm text-[#f94f06] ">
                <p class="line-clamp-3 w-full text-center">{eatDetails.value.top_show.sub_title}</p>
              </x-tips>
              <x-star onClick={() => {
                void router.push({ path: '/sleep-check-in' })
              }} class="z-up mr-7 mt-8 flex justify-end"><img class="size-14" src={star} /></x-star>
              <x-sleeping-link-wrap class="z-up flex justify-end">
                <x-sleeping-link onClick={() => {
                  void router.push({ path: '/sleep-check-in' })
                }} class="text-2.5 w-22 pt-0.6 rounded-50 mr-3 mt--3 line-clamp-3 bg-[#6532f7] p-1 text-center font-bold"
                >
                  {t('meal_check_in.sleeping_link')}
                </x-sleeping-link>
              </x-sleeping-link-wrap>
              <x-rules-img class="mr-4.5 z-up mt-[12%] flex justify-end"><img class="size-9" src={qa_bg} /></x-rules-img>
              <x-rules-link-wrap class="z-up flex justify-end">
                <x-rules-link onClick={openActiveRulesDialog} class="text-2.5 mr-3 mt--2 line-clamp-4 w-12 rounded-lg bg-[#fe5c00] px-1 py-0.5 text-center font-['SF_Pro'] font-bold">
                  {t('meal_check_in.rules_title')}
                </x-rules-link>
              </x-rules-link-wrap>
            </div>
            <x-card className="z-up mb-2 mt-14 flex justify-center gap-x-[9px] px-3">
              {eatDetails.value.eta_item_list.map((item, index) => {
                // 公共样式
                const cardItemClass = `w-1/4 grow-0 py-[6px] justify-between flex flex-col shadow-[0px_2px_6px_0px_rgba(247,82,11,0.30)] rounded-xl border items-center ${
                  item.status === 3
                    ? 'opacity-50 bg-[#ff7f48]'
                    : item.is_curr_node
                    ? 'bg-[#ff7f48]'
                    : 'bg-[#fff]'
                }`

                const cardTitleClass = `flex w-full px-1 h-7 shrink-0  items-center justify-center text-center text-xs ${
      item.is_curr_node ? 'font-bold text-[#fff]' : item.status === 3 ? 'text-[#fff]' : 'text-[#090609]'
    }`

                const cardBtnClass = `flex mt-2 text-center h-7.5 max-h-7.5 w-full line-clamp-2 rounded-lg px-1 py-1.5 text-2.5 items-center justify-center ${
      item.status === 3
        ? 'text-[#090609]'
        : item.is_curr_node
        ? 'bg-[#f7df00] text-[#090609] font-bold'
        : 'bg-[#fff2c7] text-[#ff4c00] font-bold'
    }`

                return (
                  <x-card-item key={index} class={cardItemClass}>
                    <x-card-title class={cardTitleClass}>
                      <p class="line-clamp-2 w-full break-words leading-[14px]">{item.name}</p>
                    </x-card-title>
                    <x-card-bottom className="flex w-full flex-col items-center px-1">
                      <img src={coin} class="h-[30px] w-8 object-cover text-xs" />
                      <x-coin class={`mt-[6px] text-xs font-bold ${item.status === 3 || item.is_curr_node ? 'text-white ' : 'text-[#ff3b30]'}`}>{item.gold}</x-coin>
                      <x-card-btn class={cardBtnClass}>
                        <p class="line-clamp-2 w-full break-words">
                          {item.bottom_show_text}
                        </p>
                      </x-card-btn>
                    </x-card-bottom>
                  </x-card-item>
                )
              })}
            </x-card>
            <x-main-tips
              onClick={cardClickHandle}
              class={`${eatDetails.value.status === 3 ? 'bg-[#ffab7b]' : 'bg-[#f6510b]'} rounded-50 mx-4 flex w-[(100%_-_2rem)] shrink-0 items-center justify-center px-3 py-2 text-base font-bold text-white`}
            >
              <p class="line-clamp-2 w-full break-words text-center">{eatDetails.value.button_show.btn_txt}</p>
            </x-main-tips>
            <x-care-notice class="mt-2 flex flex-col items-center justify-center gap-y-2 px-3">
              <p class="line-clamp-3 w-full break-words text-center text-base text-[#863e00]">{eatDetails.value.bottom_show.title}</p>
              <p class="line-clamp-3 w-full break-words text-center text-xs text-[#ae6b32]">{eatDetails.value.bottom_show.sub_title}</p>
            </x-care-notice>
            <x-footer-logo class="h-17 flex w-full items-center justify-center pb-[10px] pt-6 "><img width="158" height="34" class="h-[34px] w-auto" src={logo} /></x-footer-logo>
          </x-meal-check-in-page>
        )
  )
})

export default MealCheckInPage
