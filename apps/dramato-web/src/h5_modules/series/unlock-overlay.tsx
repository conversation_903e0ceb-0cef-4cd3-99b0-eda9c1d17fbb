import { createComponent, fn, required } from '@skynet/shared'
import { MergeClass, SvgIcon } from '@skynet/ui'
import { T } from './product-panel.i18n'
import { Fn } from '@vueuse/core'

type UnlockOverlayOptions = {
  props: {
    bgUrl: string
  }
  emits: {
    clickUnlock: Fn
  }
}
export const UnlockOverlay = createComponent<UnlockOverlayOptions>({
  props: {
    bgUrl: required,
  },
  emits: {
    clickUnlock: fn,
  },
}, (props, { emit }) => {
  return () => (
    <MergeClass tag="x-unlock-overlay" baseClass="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
      <div class="h-79 rounded-2 w-60 overflow-hidden">
        <img class="size-full" src={props.bgUrl} />
      </div>
      <div class="mt-3 flex items-center justify-center rounded-lg bg-[#fc2763] py-2" onClick={() => emit('clickUnlock')}>
        <SvgIcon name="ic-lock-fill" class="ml-0.5 size-6 text-white" />
        <div class="text-base text-white/90">
          { T.productPanel.unlockNow() }
        </div>
      </div>
    </MergeClass>
  )
})

export default UnlockOverlay
