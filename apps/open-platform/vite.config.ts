// @ts-expect-error ignore js
import { createSvgIconsPlugin } from '@skynet/vite-plugin-svg-icons'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { dirname, join } from 'node:path'
import { fileURLToPath } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import { analyzer } from 'vite-bundle-analyzer'
import { plugin as mdPlugin, Mode } from 'vite-plugin-markdown'
import ElementPlus from 'unplugin-element-plus/vite'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const projectDir = __dirname
const project = (path: string) => join(projectDir, path)
const envDir = project('env')

const isCustomElement = (tag: string) => tag.startsWith('x-')
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, envDir)
  console.log('env: ', env)
  return {
    base: env.VITE_CONFIG_BASE,
    envDir,
    plugins: [
      ElementPlus({}),
      vue(),
      vueJsx({
        isCustomElement,
        defineComponentName: ['defineComponent', 'createComponent'],
      }),
      createSvgIconsPlugin({
        iconDirs: [project('src/icons/svg')],
        symbolId: '[name]',
      }),
      createSvgIconsPlugin({
        registerName: 'virtual:svg-no-colored-icons-register',
        iconDirs: [project('src/icons/no-colored-svg')],
        symbolId: '[name]',
        svgoOptions: {
          plugins: [
            {
              name: 'removeAttrs',
              params: {
                attrs: ['fill'],
                preserveCurrentColor: true,
              },
            },
          ],
        },
        customDomId: '__svg__no__colored__icons__dom__',
      }),
      mdPlugin({ mode: [Mode.HTML, Mode.VUE] }),
      // legacy({
      //   targets: ['defaults', 'chrome > 50', 'edge > 14'],
      //   additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      //   renderLegacyChunks: true,
      // }),
      mode === 'production'
        ? null
        : analyzer({
          analyzerMode: 'static',
          fileName: 'bundle-analyzer',
          summary: true,
        }),
    ],
    resolve: {
      alias: {
        src: project('src'),
      },
    },
  }
})
