import { createNumberId } from '@skynet/shared/id-helper'

interface JsBridge {
  <T>(method: string, data?: JsonValue): Promise<T>
  inDramawaveApp: boolean
}

const jsBridgeForFlutter = async <T>(method: string, data: JsonValue = null) => {
  if (jsBridge.inDramawaveApp) {
    console.log('jsBridgeForFlutter', method, data)
    return window.flutter_inappwebview?.callHandler?.(method, data) as Promise<T>
  }
  return Promise.reject(`jsBridgeForFlutter: You can not call jsBridge, method: ${method}, data: ` + JSON.stringify(data))
}

const jsBridgeForNative = async <T>(method: string, data: JsonValue = null) => {
  if (!jsBridge.inDramawaveApp) return Promise.reject('不在APP内')
  console.log('jsBridgeForNative', method, data)
  return new Promise<T>((resolve, reject) => {
    const handlerName = 'func_' + createNumberId()
    window.callbacks[handlerName] = (isSuccess: boolean, res: unknown) => {
      // ios在失败时 第二个回调参数为true, 目前h5修改兼容
      if (res === 'failed') {
        console.log('jsBridgeForNative failed', method, 'error res', res)
        reject(res as T)
      }
      if (isSuccess) {
        let obj = null
        try {
          obj = JSON.parse(res as string)
        } catch (e) {
          obj = res
        }
        console.log('jsBridgeForNative success', method, ' res', obj)
        resolve(obj as T)
      } else {
        console.log('jsBridgeForNative failed', method, 'error res', res)
        reject(res as T)
      }
    }
    window.refreshPage = () => {
      console.log('refreshPage')
      window.location.reload()
    }
    if (typeof window.dwJsHandler === 'undefined') {
      return reject('jsBridgeForNative is not ready')
    }
    window.dwJsHandler.execute(method, data === null ? null : JSON.stringify(data), handlerName)
  })
}

export const jsBridge: JsBridge = <T>(method: string, data: JsonValue = null) => {
  if (typeof window.dwJsHandler !== 'undefined') {
    window.callbacks = {}
    window.dwBridge = {
      callback: function (handlerName: string, isSuccess: boolean, result: unknown) {
        const callback = window.callbacks[handlerName]
        callback && callback(isSuccess, result)
      },
    }
    return jsBridgeForNative<T>(method, data)
  }
  return jsBridgeForFlutter<T>(method, data)
}

jsBridge.inDramawaveApp = true

Object.defineProperty(jsBridge, 'inDramawaveApp', {
  get() {
    if (typeof window.dwJsHandler !== 'undefined') {
      return true
    }
    return window.flutter_inappwebview?.callHandler !== undefined
  },
})

declare global {
  interface Window {
    flutter_inappwebview?: {
      callHandler?: (handlerName: string, ...args: unknown[]) => Promise<unknown>
    }
    callbacks: Record<string, (isSuccess: boolean, res: unknown) => void>
    dwJsHandler: {
      execute: (method: string, data: string | null, handlerName: string) => void
    }
    dwBridge: {
      callback: (name: string, isSuccess: boolean, res: unknown) => void
    }
    refreshPage: () => void
  }
}
