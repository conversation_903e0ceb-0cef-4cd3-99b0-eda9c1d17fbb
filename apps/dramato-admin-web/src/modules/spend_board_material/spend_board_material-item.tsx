/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Button, TableColumnOld, CreateTableOld, DateTime, Icon, openDialog, showAlert, Switch } from '@skynet/ui'
import { computed, ref, watch } from 'vue'
import { apiGetSpendBoardMaterialDesc, apiGetSpendBoardTask } from './spend_board_material-api'
import { apiSeriesResourceListV2 } from '../resource/resource-api'
import dayjs from 'dayjs'
import RecommendForm from './recommend-form'
import DownloadForm from './download-form'
import ScoreForm from './score-form'
import { useSpendBoardMaterial } from './use-spend_board_material'
import { useClipboard } from '@vueuse/core'
import { useRouter } from 'vue-router'

export const languageMap: Record<string, string> = {
  ja: '日语',
  en: '英语',
  ko: '韩语',
  es: '西班牙语',
  th: '泰语',
  id: '印尼语',
  vi: '越南语',
  pt: '葡萄牙语',
  tl: '菲律宾语',
  it: '意大利语',
  fr: '法语',
  de: '德语',
  tr: '土耳其语',
  ru: '俄语',
  ms: '马来西亚语',
  'zh-TW': '繁体中文',
  hi: '印地语',
}

type ListItemProps = {
  props: {
    i?: M.SpendBoardMaterial.ListItem
    seeAllData?: boolean
    seeAllResource?: boolean
  }
}

// const makeExcel = (data: any, name: string) => {
//   return new Promise(resolve => {
//     // 将数据转换为 CSV 格式
//     const csvContent = data.map((e: any) => e.join(',')).join('\n')
//     // 添加 UTF-8 BOM
//     const bom = '\uFEFF'
//     const finalContent = bom + csvContent

//     // 创建一个 Blob 对象
//     const blob = new Blob([finalContent], { type: 'text/csv;charset=utf-8;' })

//     // 创建下载链接
//     const link = document.createElement('a')
//     const url = URL.createObjectURL(blob)
//     link.setAttribute('href', url)
//     link.setAttribute('download', name)
//     document.body.appendChild(link)
//     link.click()
//     resolve('')
//   })
// }

const onDownload = async (id: number, langList: string[], name: string, lang: string, audio_type: number) => {
  try {
    const res = await apiSeriesResourceListV2({
      page_index: 1,
      page_size: 300,
      series_resource_id: id,
      src_lang: lang,
      audio_type,
    })
    const _list = res?.data?.list_v2 || []

    openDialog({
      title: '素材下载',
      body: (
        <DownloadForm
          lang={lang}
          downloadList={
            _list.map((item: { serial_number: number, origin_path: any }) => ({
              serial_number: item.serial_number,
              title: name + '.' + item.serial_number,
              origin_path: item.origin_path,
            }))
          }
        />
      ),
      customClass: '!w-1000px',
    })
    // const tableHeader = [
    //   '集数',
    //   ...langList.map(lang => {
    //     if (lang === '0') return '含字幕视频'
    //     return languageMap[lang]
    //   }),
    // ]
    // const notFinished = ['trans_failed', 'extract_failed', 'failed', 'processing', '']
    // const tableData = []
    // for (let i = 1; i <= count; i++) {
    //   const row = _list.find(item => item.serial_number === i)
    //   if (!row) {
    //     tableData.push([i, ...langList.map(_ => '-')])
    //   } else {
    //     tableData.push([
    //       row.serial_number,
    //       ...langList.map(lang => {
    //         if (lang === '0') return row.origin_path
    //         return notFinished.includes(row[`${lang}_episode_path`]) ? '-' : row[`${lang}_episode_path`]
    //       }),
    //     ])
    //   }
    // }

    // await makeExcel([
    //   tableHeader,
    //   ...tableData,
    // ], name)
  } catch (error) {
    // loading.value = false
  }
}

const SpendBoardMaterialItem = createComponent<ListItemProps>({
  props: {
    i: undefined,
    seeAllData: false,
    seeAllResource: false,
  },
}, props => {
  const currentLanguage = ref(!props.i ? '' : props.i?.series[0].default_language_code)
  const currentAudioType = ref(!props.i ? '' : props.i?.series[0].audio_type)
  // const currentLanguage = ref(!i ? '' : '')
  const currentSeries = computed(() => props.i?.series.find(j => j.default_language_code === currentLanguage.value && j.audio_type === currentAudioType.value))
  const router = useRouter()
  // const ratingArr = computed(() => {
  //   const r = []

  //   if (!!currentSeries.value?.content_rating) {
  //     r.push({
  //       desc: '商务评分',
  //       value: currentSeries.value?.content_rating,
  //     })
  //   }

  //   if (!!currentSeries.value?.editor_rating) {
  //     r.push({
  //       desc: '素材评分',
  //       value: currentSeries.value?.editor_rating,
  //     })
  //   }

  //   if (!!currentSeries.value?.play_uv) {
  //     r.push({
  //       desc: '播放uv',
  //       value: currentSeries.value?.play_uv,
  //     })
  //   }

  //   if (currentSeries.value?.pay_rate && currentSeries.value?.pay_rate > 0) {
  //     r.push({
  //       desc: '站内付费率',
  //       value: ((currentSeries.value?.pay_rate || 0) * 100).toFixed(2) + '%',
  //     })
  //   }

  //   if (currentSeries.value?.view_rate && currentSeries.value?.view_rate > 0) {
  //     r.push({
  //       desc: '站内观看率',
  //       value: ((currentSeries.value?.view_rate || 0) * 100).toFixed(2) + '%',
  //     })
  //   }

  //   if (currentSeries.value?.play_video_num_rate && currentSeries.value?.play_video_num_rate > 0) {
  //     r.push({
  //       desc: '人均观看集数占比',
  //       value: ((currentSeries.value?.play_video_num_rate || 0) * 100).toFixed(2) + '%',
  //     })
  //   }

  //   if (currentSeries.value?.dataeye_rank && currentSeries.value?.dataeye_rank > 0) {
  //     r.push({
  //       desc: 'Date X 榜单',
  //       value: 'Top' + currentSeries.value?.dataeye_rank,
  //     })
  //   }
  //   return r
  // })

  const SpecialTable = CreateTableOld<M.SpendBoardMaterial.Special>()

  const SpecialTableColumns: TableColumnOld<M.SpendBoardMaterial.Special>[] = [
    ['优化师名字', 'name', { class: 'w-[160px]' }],
    ['消耗', 'spend', { class: 'w-[100px]' }],
    ['广告曝光', 'show_cnt', { class: 'w-[100px]' }],
    ['广告点击', 'click_cnt', { class: 'w-[100px]' }],
    ['广告CPM', 'cpm', { class: 'w-[100px]' }],
    ['广告CTR', row => (row.ctr * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
    ['广告CPC', 'cpc', { class: 'w-[100px]' }],
    ['广告CVR', row => (row.cvr * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
    ['安装', 'install_cnt', { class: 'w-[100px]' }],
    ['加购', 'add_to_cart_uv', { class: 'w-[100px]' }],
    ['付费', 'pay_uv', { class: 'w-[100px]' }],
    ['Day0 回收', 'day0_revenue', { class: 'w-[100px]' }],
    ['CPI', 'cpi', { class: 'w-[100px]' }],
    ['ARPU', 'arpu', { class: 'w-[100px]' }],
    ['付费率', row => (row.pay_rate * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
    ['CPA', 'cpa', { class: 'w-[100px]' }],
    ['Arppu', 'arppu', { class: 'w-[100px]' }],
    ['Day0 ROAS', row => (row.day0_roas * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
  ]

  const VideoCutterTable = CreateTableOld<M.SpendBoardMaterial.VideoCutter>()

  const VideoCutterTableColumns: TableColumnOld<M.SpendBoardMaterial.VideoCutter>[] = [
    ['剪辑师名字', 'user_name', { class: 'w-[160px]' }],
    ['跑量素材数', 'source_cnt', { class: 'w-[100px]' }],
    ['消耗', 'spend', { class: 'w-[100px]' }],
    ['广告曝光', 'show_cnt', { class: 'w-[100px]' }],
    ['广告点击', 'click_cnt', { class: 'w-[100px]' }],
    ['广告CPM', 'cpm', { class: 'w-[100px]' }],
    ['广告CTR', row => (row.ctr * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
    ['广告CPC', 'cpc', { class: 'w-[100px]' }],
    ['广告CVR', row => (row.cvr * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
    ['安装', 'install_cnt', { class: 'w-[100px]' }],
    ['加购', 'add_to_cart_uv', { class: 'w-[100px]' }],
    ['付费', 'pay_uv', { class: 'w-[100px]' }],
    ['Day0 回收', 'day0_revenue', { class: 'w-[100px]' }],
    ['CPI', 'cpi', { class: 'w-[100px]' }],
    ['ARPU', 'arpu', { class: 'w-[100px]' }],
    ['付费率', row => (row.pay_rate * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
    ['CPA', 'cpa', { class: 'w-[100px]' }],
    ['Arppu', 'arppu', { class: 'w-[100px]' }],
    ['Day0 ROAS', row => (row.day0_roas * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
    // ['ROI', row => (row.roi * 100).toFixed(2) + '%', { class: 'w-[100px]' }],
  ]

  const { copy, copied } = useClipboard()

  watch(() => copied.value, () => {
    if (copied.value) {
      showAlert('复制成功')
    }
  })

  const onCopy = (s: string) => {
    void copy(s)
  }

  const renderSeries = () => (
    <x-series>
      <x-list-item-body class={mc('w-full flex gap-x-2 pt-2', props.seeAllData ? 'hidden' : '')}>
        <x-image class="mr-[12px] min-w-[160px]">
          <img src={'https://static-v1.mydramawave.com/' + currentSeries.value?.cover} alt="" class={`h-52 w-[160px] min-w-[160px] rounded-lg object-cover ${currentSeries.value?.cover ? '' : 'bg-gray-300'}`} />
        </x-image>
        <x-warp-main class="flex flex-1 flex-col justify-between gap-y-3">
          <x-warp-main-content class="flex flex-1 flex-col gap-y-3">
            <x-warp-title class="flex items-center gap-x-2">
              <x-title class="mr-5 flex items-center gap-x-2 text-[16px] font-semibold" title={currentSeries.value?.title}>
                {currentSeries.value?.title}
                <Icon onClick={() => onCopy(currentSeries.value?.title || '')} name="ant-design:copy-filled" class="cursor-pointer" />
              </x-title>
              <x-subtitle class="flex items-center gap-x-2 text-[12px] text-gray-500">
                {currentSeries.value?.series_key}
                <Icon onClick={() => onCopy(currentSeries.value?.series_key || '')} name="ant-design:copy-filled" class="cursor-pointer" />
              </x-subtitle>
            </x-warp-title>
            <x-warp-desc class="flex items-center gap-x-2">
              <x-label class="w-[80px] min-w-[80px]">剧集信息：</x-label>
              <x-desc>{languageMap[currentSeries.value?.default_language_code || '']}-{currentSeries.value?.default_language_code}</x-desc>
              <x-desc>{['', '首发', '二轮'][currentSeries.value?.resource_round || 0]}</x-desc>
              <x-desc>{['', '本土', '翻译'][currentSeries.value?.resource_type || 0]}</x-desc>
              <x-desc class="btn btn-primary btn-xs" onClick={() => {
                void apiGetSpendBoardMaterialDesc({ series_key: currentSeries.value?.series_key || '' }).then(rs => {
                  const desc = rs.data?.desc || ''
                  openDialog({
                    title: '剧集介绍',
                    body: desc,
                  })
                }).catch((error: any) => {
                  showAlert(error.response.data.err_msg || '', 'error')
                })
              }}
              >
                短剧介绍
              </x-desc>
            </x-warp-desc>
            {
              currentSeries.value?.tips && currentSeries.value?.tips.length > 0
                ? (
                    <x-warp-desc class="flex gap-x-2">
                      <x-label class="w-[80px]">推荐原因：</x-label>
                      {
                        currentSeries.value.tips.map((r, l) => (
                          <div class="tooltip" data-tip={currentSeries.value?.reasons?.[l]}>
                            <x-tip class="btn btn-outline btn-xs bg-yellow-200" key={r}>{r}</x-tip>
                          </div>
                        ))
                      }
                    </x-warp-desc>
                  )
                : null
            }
            <x-warp-desc class="flex gap-x-2">
              <x-label class=": w-[84px] min-w-20">站内数据：</x-label>
              <x-tips class=" flex max-w-[620px] flex-row flex-wrap gap-2">
                {
                  currentSeries.value?.inner_tips && currentSeries.value?.inner_tips.length > 0
                    ? currentSeries.value?.inner_tips.map((r, l) => (
                      <>
                        <x-desc>{r}</x-desc>
                        {currentSeries.value?.inner_tips && l !== currentSeries.value?.inner_tips.length - 1 && <x-desc class="h-[24px] w-px min-w-px  bg-gray-200" />}
                      </>
                    ))
                    : '暂无评分'
                }
              </x-tips>

            </x-warp-desc>
            <x-warp-desc class="flex gap-x-2">
              <x-label class="w-[80px] min-w-[80px]">剧集标签：</x-label>
              <x-tips class=" flex max-w-[620px] flex-row flex-wrap gap-2">
                {
                  currentSeries.value?.labels && currentSeries.value?.labels.length > 0
                    ? currentSeries.value?.labels.map((r, l) => (
                      <x-tip class="" key={r}>{r} |</x-tip>
                    ))
                    : '暂无标签'
                }
              </x-tips>
            </x-warp-desc>
            <x-warp-desc class="flex gap-x-2">
              <x-label class="w-[80px] min-w-[80px]">营收情况：</x-label>
              <x-desc>累计花费：{currentSeries.value?.real_money}</x-desc>
              <x-desc class="h-[24px] w-px min-w-px bg-gray-200" />
              <x-desc>累计回收：{currentSeries.value?.recycle_money}</x-desc>
              <x-desc class="h-[24px] w-px min-w-px bg-gray-200" />
              <x-desc>Roi：{((currentSeries.value?.roi || 0) * 100).toFixed(2) + '%'}</x-desc>
            </x-warp-desc>
          </x-warp-main-content>
          <x-warp-footer class="flex items-center">
            <ul class="flex items-center">
              <li class={mc('btn  btn-xs', currentSeries.value?.deploy_status && currentSeries.value?.deploy_status >= 1 ? 'btn-primary' : 'btn-outline')}>待上架</li>
              <x-desc class="mx-2 h-px w-[24px]  min-w-[24px] bg-black" />
              <li class={mc('btn  btn-xs', currentSeries.value?.deploy_status && currentSeries.value?.deploy_status >= 2 ? 'btn-primary' : 'btn-outline')}>已上架</li>
              <x-desc class="mx-2 h-px w-[24px]  min-w-[24px] bg-black" />
              <li class={mc('btn  btn-xs', currentSeries.value?.deploy_status && currentSeries.value?.deploy_status >= 3 ? 'btn-primary' : 'btn-outline')}>已推送</li>
              <x-desc class="mx-2 h-px w-[24px]  min-w-[24px] bg-black" />
              <li class={mc('btn  btn-xs', currentSeries.value?.deploy_status && currentSeries.value?.deploy_status >= 4 ? 'btn-primary' : 'btn-outline')}>素材中（{currentSeries.value?.source_cnt || 0}）</li>
              <x-desc class="mx-2 h-px w-[24px]  min-w-[24px] bg-black" />
              <li class={mc('btn btn-xs', currentSeries.value?.deploy_status && currentSeries.value?.deploy_status >= 5 ? 'btn-primary' : 'btn-outline')}>投放中 ({currentSeries.value?.delivery_cnt || 0})</li>
            </ul>
          </x-warp-footer>
        </x-warp-main>
        <x-warp-aside class="flex w-[420px] flex-col gap-y-2">
          <x-warp-desc class="flex items-center gap-x-2">
            <x-label class="w-[80px] min-w-[80px]">剧集状态：</x-label>
            <x-desc>{[
              { value: 1, label: '未上架' },
              { value: 2, label: '待上架' },
              { value: 3, label: '已上架' },
              { value: 4, label: '待下架' },
              { value: -1, label: '已下架' },
            ].find(item => item.value === currentSeries.value?.listing_status)?.label ?? '未知'}
            </x-desc>
            <x-desc class="btn btn-link btn-sm"><DateTime value={(currentSeries.value?.listing_time || 0) * 1000} /></x-desc>
          </x-warp-desc>
          <x-warp-desc class="flex gap-x-2">
            <x-label class="w-[80px] min-w-[80px]">剧集集数：</x-label>
            <x-desc>共{currentSeries.value?.count}集</x-desc>
            <x-desc class="h-[24px] w-px min-w-px bg-gray-200" />
            <x-desc>已上传{currentSeries.value?.upload_count}集</x-desc>
            <x-desc class="h-[24px] w-px min-w-px bg-gray-200" />
            <x-desc>{currentSeries.value?.unlocked_episodes}集开始收费</x-desc>
          </x-warp-desc>
          <x-warp-desc class="flex gap-x-2">
            <x-label class="w-[80px] min-w-[80px]">剧单日期：</x-label>
            <x-desc><DateTime value={(currentSeries.value?.deploy_task_time || 0) * 1000} /></x-desc>
          </x-warp-desc>
          <x-warp-desc class="flex w-full gap-x-2">
            <x-label class="w-[80px] min-w-[80px]">剧拆文档：</x-label>
            <x-desc class="flex-1 break-all">
              {currentSeries.value?.deploy_task_doc}
              <Icon onClick={() => onCopy(currentSeries.value?.deploy_task_doc || '')} name="ant-design:copy-filled" class="mb-[2px] ml-2 inline-block cursor-pointer" />
            </x-desc>
          </x-warp-desc>
        </x-warp-aside>
        <x-actions class="flex flex-col gap-y-2">
          <Button class="btn btn-primary btn-sm" onClick={() => {
            void onDownload(props.i.series_resource_id, [currentSeries.value?.default_language_code || ''], `${props.i?.title}`, currentLanguage.value, currentSeries.value?.audio_type || 0)
          }}
          >剧集资源下载
          </Button>
          <Button class="btn btn-primary btn-sm" onClick={() => {
            void router.push('material')
          }}
          >查看参考素材
          </Button>
          <x-warp-desc class="flex items-center gap-x-2">
            <x-label>认领状态：</x-label>
            <Switch
              modelValue={currentSeries.value?.task_get_status === 1}
              onUpdate:modelValue={v => {
                if (!currentSeries.value) {
                  return
                }
                void apiGetSpendBoardTask({
                  series_key: currentSeries.value?.series_key || '',
                  status: !!v ? 1 : 2,
                }).then(() => {
                  const { search, params } = useSpendBoardMaterial()
                  void search(params.value.page_index)
                }).catch((error: any) => {
                  showAlert(error.response.data.err_msg || error.response.data.message || '更新失败', 'error')
                })
              }}
              labels={{ on: '开', off: '关' }}
            />
          </x-warp-desc>
        </x-actions>
      </x-list-item-body>
      <x-series-data class="flex flex-col gap-y-4">
        <h1 class={mc('flex items-center gap-x-2 cursor-pointer font-semibold', props.seeAllData ? 'hidden' : '')} onClick={() => currentSeries.value.showDataeye = !currentSeries.value?.showDataeye}>
          展开查看投放数据
          <Icon name="ant-design:down-square-filled" class="size-4" />
        </h1>
        {
          currentSeries.value?.showDataeye || props.seeAllData
            ? (
                <>
                  <x-table>
                    <h1 class="mb-2 font-medium">优化师投放数据</h1>
                    <SpecialTable list={currentSeries.value?.specialist || []} columns={SpecialTableColumns} class="tm-table-fix-last-column" />
                  </x-table>
                  <x-table>
                    <h1 class="mb-2 font-medium">剪辑师消耗数据</h1>
                    <VideoCutterTable list={currentSeries.value?.videocutter || []} columns={VideoCutterTableColumns} class="tm-table-fix-last-column" />
                  </x-table>
                </>
              )
            : null
        }

      </x-series-data>
    </x-series>

  )

  const renderResource = () => (
    <x-list-item-body class="flex w-full gap-x-2 pt-2">
      <x-warp-main class="flex  flex-1 flex-col gap-y-3">
        <x-warp-title class="flex items-center gap-x-2">
          <x-title class="mr-5 max-w-[420px] truncate text-[16px] font-semibold" title={props.i.title}>{props.i.title || '暂无'}</x-title>
        </x-warp-title>
        <x-warp-desc class="flex items-center gap-x-2">
          <x-label>资源信息：</x-label>
          <x-desc>{['-暂无-', '首发', '二轮'][props.i.resource_round || 0]}</x-desc>
          <x-desc class="h-[24px] w-px min-w-px bg-gray-200" />
          <x-desc>{['-暂无-', '本土', '翻译'][props.i.resource_type || 0]}</x-desc>
          <x-desc class="h-[24px] w-px min-w-px bg-gray-200" />
          <x-desc>{Number(props.i.recommend) !== 1 ? '不推荐' : '推荐'}</x-desc>
        </x-warp-desc>
        <x-warp-desc class="flex gap-x-2">
          <x-label>资源投放监控提醒：</x-label>
          <x-tips class=" flex max-w-[620px] flex-row flex-wrap gap-2">
            {
              props.i.tips && props.i.tips.length > 0
                ? props.i.tips.map((r, l) => (
                  <x-tip class="btn btn-outline btn-xs" key={r}>{r}</x-tip>
                ))
                : '暂无'
            }
          </x-tips>
        </x-warp-desc>
        {
          props.i.deploy_task_time === undefined || !props.i.deploy_task_time
            ? null
            : (
                <x-warp-desc class="flex items-center gap-x-2">
                  <x-label>剧单日期：</x-label>
                  <x-desc>{dayjs(props.i.deploy_task_time * 1000).format('YYYY-MM-DD')}</x-desc>
                </x-warp-desc>
              )
        }
        {
          Number(props.i.recommend) !== 1
            ? null
            : (
                <x-warp-desc class="flex gap-x-2">
                  <x-label>推荐语言：</x-label>
                  <x-tips class=" flex max-w-[620px] flex-row flex-wrap gap-2">
                    {
                      props.i.langs && props.i.langs.length > 0
                        ? props.i.langs.map((s, l) => (
                          <x-tip class="btn btn-outline btn-xs" key={s}>{languageMap[s.lang] ? languageMap[s.lang] + '-' + s.lang + ['', '-配音剧', '-ai配音剧'][s.audio_type] : s.lang + ['', '-配音剧', '-ai配音剧'][s.audio_type]}</x-tip>
                        ))
                        : '暂无'
                    }
                  </x-tips>
                </x-warp-desc>
              )
        }
      </x-warp-main>
      <x-actions class="flex flex-col gap-y-2">
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const model = ref()
          model.value = openDialog({
            title: '资源剧单（推荐）',
            body: (
              <RecommendForm
                series_resource_id={props.i.series_resource_id}
                languages={props.i.series.map(s => {
                  return {
                    label: languageMap[s.default_language_code] ? languageMap[s.default_language_code] + '-' + s.default_language_code + ['', '-配音剧', '-ai配音剧'][s.audio_type] : s.default_language_code + ['', '-配音剧', '-ai配音剧'][s.audio_type],
                    value: s.default_language_code + '??' + s.audio_type,
                  }
                })}
                recommends={props.i.recommends}
                onCancel={() => model.value && model.value()}
                onOk={() => {
                  const { search, params } = useSpendBoardMaterial()
                  model.value && model.value()
                  void search(params.value.page_index)
                }}
              />
            ),
            customClass: '!pb-0 !w-[1000px]',
          })
        }}
        >资源剧单（推荐）
        </Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const model = ref()
          model.value = openDialog({
            title: '添加信息',
            body: (
              <ScoreForm
                series_resource_id={props.i.series_resource_id}
                editor_rating={currentSeries.value?.editor_rating}
                deploy_task_doc={currentSeries.value?.deploy_task_doc}
                onCancel={() => model.value && model.value()}
                onOk={() => {
                  model.value && model.value()
                }}
              />
            ),
            customClass: '!pb-0',
          })
        }}
        >添加剪辑评分
        </Button>
      </x-actions>
    </x-list-item-body>
  )

  if (!props.i) {
    return () => null
  }

  return () => (
    <x-list-item class="flex w-full flex-col gap-y-2 rounded-lg bg-white p-4 text-[14px]" key={props.i.series_resource_id}>
      <x-list-item-header class="border-1 flex w-full items-start gap-x-2 border-b border-gray-200 pb-2">
        <div
          class={mc('h-[24px] leading-[24px] mt-[4px] w-[46px] text-left font-semibold text-[16px] cursor-pointer', !currentLanguage.value || props.seeAllResource ? 'text-primary' : '')}
          onClick={() => props.i.can_task && (currentLanguage.value = '')}
        >
          {props.i.series_resource_id}
        </div>
        <div role="tablist" class="tabs-boxed tabs tabs-sm flex flex-1 flex-wrap bg-white">
          {props.i.series.map(s => (
            <x-tab-header class="relative flex">
              <a
                onClick={() => {
                  currentLanguage.value = s.default_language_code
                  currentAudioType.value = s.audio_type
                }}
                role="tab"
                class={`tab ${s.default_language_code === currentLanguage.value && s.audio_type === currentAudioType.value && !props.seeAllResource ? 'tab-active' : ''}`}
                key={s.default_language_code}
              >
                {languageMap[s.default_language_code] ? languageMap[s.default_language_code] + '-' + s.default_language_code + ['', '-配音剧', '-ai配音剧'][s.audio_type] : s.default_language_code + ['', '-配音剧', '-ai配音剧'][s.audio_type]}
              </a>
              {
                s.recommend === 1 && <Icon name="ant-design:like-filled" class="absolute right-1 top-1 z-up size-3 -translate-y-1/2 text-[red]" />
              }
              { s.is_today && <span class="absolute -right-1 top-1 z-up size-4 -translate-y-1/2 rounded-full bg-yellow-500 text-center text-xs text-white">今</span>}
            </x-tab-header>
          ))}
        </div>
      </x-list-item-header>
      {currentLanguage.value && !props.seeAllResource ? renderSeries() : renderResource()}
    </x-list-item>
  )
})

export default SpendBoardMaterialItem
