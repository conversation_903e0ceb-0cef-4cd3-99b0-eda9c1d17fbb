import { httpClient, h5HttpClient } from 'src/lib/http-client'
import { StsSign } from './upload-types'
import { user_key } from 'src/lib/mock'
import Cookies from 'js-cookie'
import { jsBridge } from 'src/lib/jsbridge'
import { get_authorization } from 'src/lib/authorization'

export const apiGetOssSign = () => {
  const client = jsBridge.inDramawaveApp ? httpClient : h5HttpClient
  return client.post<ApiResponse<StsSign>>('drama-api/customer_service_center/upload/sign', null, {
    headers: {
      ...JSON.parse(Cookies.get('headers') ?? '{}'),
      authorization: get_authorization(),
    },
  })
}
