export const pt = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: 'Escolhas Populares',
    },
  },
  myList: {
    watchHistory: `<PERSON>er Histórico`,
    myList: 'Favoritos',
    seeAll: 'Ver tudo',
    noData: 'Nenhum resultado relacionado',
  },
  // 分享页
  share: {
    watchNow: 'Ver agora',
    episodeDialog: {
      title: 'Junte-se a mim e ganhe moedas extras!',
      description: 'Estou viciado nesta série!',
      tips: 'Recomendação',
      button: 'Assista ao drama completo',
      limit24: 'Tempo limitado: 24h',
    },
    toast: {
      takeOffShelf: 'O conteúdo não está disponível',
    },
  },
  'customer-service-center': {
    title: 'Centro de atendimento ao cliente',
    description: 'Descrição',
    'description-placeholder': 'Forneça detalhes para nos ajudar a entender o problema',
    'email-placeholder': 'Insira seu e-mail para receber atualizações de opinião.',
    'submit-feedback': 'Enviar',
    'enter-feedback': 'Comentários soubre outros problemas',
    'upload-pictures': 'Carregar fotos',
    feedback: 'opinião',
    'problem-type': 'Tipo de problema',
    'problem-type-placeholder': 'Por favor, selecione',
    'payment-issue-placeholder': 'Por favor, forneça seu ID e o recibo do pedido: Google: https://pay.google.com/ (o número do pedido começa com GPA...) iOS: App Store - ID Apple - Histórico de compras (ID do pedido)',
    'content-issue-placeholder': 'Por favor, forneça o título do drama, o número do episódio e o idioma da legenda ausente.',
    'ads-issue-placeholder': 'Por favor, forneça a configuração de modelo, país e idioma do seu dispositivo.',
    'technical-issue-placeholder': 'Por favor, forneça a configuração de modelo, país e idioma do seu dispositivo.',
    'other-issue-placeholder': 'Por favor, forneça mais detalhes, incluindo o modelo do seu dispositivo e o país.',
    'i-want-to-give-feedback': 'Quero deixar um feedback',
    'are-you-satisfied-with-this-reply': 'Você está satisfeito(a) com esta resposta?',
    unsatisfied: 'Não satisfeito(a)',
    satisfied: 'Satisfeito(a)',
    myFeedback: 'Meu feedback',
    'reply-to-feedback': 'Resposta ao feedback',
    'description-min-length': 'Descreva o problema (mínimo 6 caracteres).',
    'problem-type-not-choose': 'Selecione o tipo de feedback',
    'description-empty': 'O campo não pode ficar em branco',
  },
  terminate: {
    question: 'Como gerenciar minha assinatura?',
    desc: 'Clique no botão abaixo para entrar na página de gerenciamento de contrato. Gerencie sua assinatura pelo interruptor do contrato:',
    title: 'Gerenciamento de Contrato',
    expiration: 'Data de Expiração',
    manageDesc: 'Relação Contratual',
  },
  toast: {
    'upload-failed': 'Falha na carrega, por favor tente novamente',
    'submit-success': 'Enviado com sucesso',
    'email-incorrect': 'O formato do e-mail está incorreto',
  },
  not_found: {
    title: 'A página que você está procurando não existe',
  },
  regulations: {
    howUseData: 'Como usamos seus dados',
    content: `Se você selecionar "Permitir" na solicitação de permissão de Transparência de Rastreamento de Aplicativos, usaremos suas informações e dados para otimizar nossos serviços e fornecer a melhor experiência para você, caso contrário, apenas coletaremos os dados e não realizaremos nenhuma atividade de rastreamento.

Se você concordar em permitir que a DramaWave e nossos parceiros e fornecedores usem dados do seu dispositivo para analisar como você usa o aplicativo, personalizar recomendações de conteúdo e fornecer mais serviços relacionados a informações, clique em "Concordar e Continuar" para continuar.`,
    acceptAll: 'Aceitar tudo',
    dataSettings: 'Configurações de dados',
    next: 'Próximo',
    cancel: 'Cancelar',
    aboutYourPrivacy: 'Sobre sua privacidade',
    personalDataName: 'Dados personalizados',
    personalDataDescription: 'Usamos essas informações para lembrar suas escolhas e fornecer recomendações de publicidade e conteúdo personalizadas, notificações e lembretes, etc.',
    advertisementDataName: 'Dados de publicidade',
    advertisementDataDescription: 'Cookies, identificadores do seu dispositivo ou outras informações podem ser armazenados ou acessados no seu dispositivo para fins de publicidade online. Os dados do usuário também podem ser compartilhados com nossos parceiros de publicidade.',
    behaviorsDataName: 'Dados comportamentais',
    behaviorsDataDescription: 'Dados relacionados à análise, como cookies ou identificadores de dispositivos, podem ser armazenados para medir e analisar o uso, comportamento e desempenho da publicidade.',
    privacyAndTerms: 'Política de Privacidade e Termos de Serviço',
    referToPrivacy: 'Consulte a Política de Privacidade',
    referToTerms: 'Consulte os Termos de Serviço',
    agreeAndSave: 'Concordar e Salvar',
  },
  coins: {
    earnings: {
      title: 'Meus Ganhos',
      total: 'Diamantes Totais',
      exchange: 'Troca',
      earningsHistory: 'Histórico de Ganhos',
      exchangeHistory: 'Histórico de Trocas',
      noRecords: 'Sem registro',
      last50earnings: 'Apenas os últimos 50 ganhos são exibidos',
      last50exchanges: 'Apenas os últimos 50 registros de troca são exibidos',
      benefits: 'Benefícios',
      gotIt: 'Entendi',
    },
    exchange: {
      title: 'Zona de Troca',
      notOpen: 'Ainda não aberto, por favor, fique atento.',
      receiveBtn: 'Receber felizmente',
      earnCoins: 'Ganhar Diamantes',
      redeemRequiredXCoins: 'Resgatar requer {X} diamantes',
      exchangeSuccess: 'Troca realizada com sucesso',
      cardId: 'ID do CARTÃO',
      exchangeFailed: 'Troca Falhou',
      transferFailedInsufficientStock: 'Fora de estoque. Por favor, venha cedo amanhã.',
      gotItBtn: 'Entendi',
      transferFailedRiskControl: 'Conta incomum, temporariamente incapaz de trocar.',
      tryAgainBtn: 'Tente Novamente Mais Tarde',
      transferConfirm: 'Confirmar Transferência',
      paypalAccountEmail: 'Email da Conta Paypal',
      confirmPaypalAccountEmail: 'Confirmar Email da Conta Paypal',
      transferX: 'Transferir {X}',
      transferXConfirm: 'Confirmar Transferência de {X}',
      transferToYourAccountX: 'Transferir para sua conta {X}',
      confirmBtn: 'Confirmar',
      redeemFailed: 'Resgate falhou',
      redeemFailedContent: 'Ocorreu um erro. Por favor, tente novamente mais tarde.',
      transferSuccessTitle: 'Estamos enviando o dinheiro!',
      transferSuccessContent: ' Seu dinheiro chegará em até 5 minutos a 72 horas.',
      copySuccess: 'Cópia bem-sucedida',
      enterEmailError: 'Por favor, insira um endereço de e-mail válido.',
      enterEmailValidError: 'Erro de entrada, por favor insira novamente.',
      emailNotMatchError: 'O endereço de e-mail não coincide, por favor insira novamente.',
    },
  },
  meal_check_in: {
    sleeping_link: 'Ganhe diamantes enquanto dorme',
    rules_title: 'Regras da atividade',
    benefits_title: 'Benefits',
    rules_1: '1. Todos os dias há quatro horários para você registrar suas refeições: Café da manhã: 07:00-10:00; Almoço: 11:00-14:00; Jantar: 17:00-20:00; Lanche da noite: 21:00-24:00.',
    rules_2: '2. Se você perder o horário de registro correspondente, pode assistir a um vídeo para reivindicar a recompensa.',
    rules_3: '3. As recompensas são redefinidas todos os dias à meia-noite, então não se esqueça de reclamá-las.',
  },
  sleep_check_in: {
    earn_link: 'Registre refeições para ganhar',
    benefits_title: 'Benefits',
    rules_1: '1. Todas as noites, das 19h às 24h, você pode ativar o modo \'Dormir para Ganhar\'.',
    rules_2: '2. Após ativá-lo, no dia seguinte, após dormir por 8 horas, você pode reclamar a recompensa de sono entre 8h e 12h.',
    rules_3: '3. Se você perder o horário para reivindicar a recompensa, pode assistir a um vídeo entre 12h e 19h no dia seguinte para reivindicar novamente a recompensa.',
  },
  bind_email: {
    title: 'Inserir seu endereço de e-mail',
    list_1: 'Vincule seu endereço de e-mail para ganhar moedas de recompensa exclusivas',
    list_2: 'Receber notificações sobre eventos futuros e ofertas especiais',
    list_3: 'Ficar por dentro dos novos episódios em primeira mão',
    email_placeholder: 'Por favor, insira seu endereço de e-mail.',
    verify: 'Verificar',
    email_error: 'Por favor, digite um endereço de e-mail válido.',
    verify_progress: 'Verificando e-mail',
    verify_success: 'Resgate realizado com sucesso!',
    re_enter: 'Digitar novamente',
    email_repeat: 'Este e-mail já participou da atividade!',
  },
  infinityScroll: {
    end: 'Fim',
    loading: 'Carregando...',
  },
}
