import { get_authorization_for_browser } from "~/lib/authorization"
import { get_k_device_hash_from_cookie } from "~/lib/device-id"

export function useApiFetch<T>(url: string, options = {}) {
  const config = useRuntimeConfig()
  return useFetch<T>(config.public.apiUrl + url, {
    ...options,
    onRequest({ options }) {
      options.headers = options.headers || {} as Headers
      // 例如添加授权头
      options.headers = {
        ...options.headers,
        'content-type': 'application/json',
        'app-name': 'com.dramawave.h5', // 'com.dramawave.app',
        'app-version': '1.2.20',
        'device-hash': get_k_device_hash_from_cookie(),
        'device-id': get_k_device_hash_from_cookie(),
        authorization: get_authorization_for_browser(),
        device: 'h5',
      }
     
    },
    onRequestError({ error }) {
      console.error('请求失败:', error)
    },
    onResponse({ response }) {
      // 可以在这里处理响应数据
      return response
    },
  })
}