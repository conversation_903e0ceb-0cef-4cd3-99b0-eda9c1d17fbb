/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { onMounted } from 'vue'
import { useSeriesPackageStore } from './use-series-package-store'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { Button, openDialog, Pager, TableColumnOld, transformInteger } from '@skynet/ui'
import { set } from 'lodash-es'
import SeriesPackageAddForm from './series-package-add-form'
import { useSeriesPackageAddFormStore } from './use-series-package-add-form-store'

export const dialogMainClass = 'flex flex-col flex-auto pb-0 w-[1000px] overflow-hidden'

type SeriesPackagePageOptions = {
  props: {}
}

export const SeriesPackagePage = createComponent<SeriesPackagePageOptions>({
  props: {},
}, props => {
  const {
    SearchForm,
    listParams,
    Table,
    list,
    page,
    pageSize,
    total,
    loading,
    search,
    columns,
    exportExcel,
    checkedItems,
    isCommon,
  } = useSeriesPackageStore()

  const {
    resetOneForm,
    resetTwoForm,
    formData,
    curStep,
    closeAddDialog,
  } = useSeriesPackageAddFormStore()

  onMounted(() => {
    void search(1)
  })

  const onListTypeChange = (list_type: number) => {
    isCommon.value = list_type === 0
    void search(1)
  }

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <x-nav class={mc('space-y-2')}>
            <ul>
              <li>剧包列表</li>
            </ul>
            <x-nav-tab role="tablist" class="tabs-boxed tabs w-1/3 bg-slate-200">
              <x-tab class={mc('tab', isCommon.value ? 'tab-active' : '')} onClick={() => onListTypeChange(0)}>普通剧包</x-tab>
              <x-tab class={mc('tab', !isCommon.value ? 'tab-active' : '')} onClick={() => onListTypeChange(1)}>集合剧包</x-tab>
            </x-nav-tab>
          </x-nav>
        ),
        form: () => (
          <SearchForm
            onChange={(path, value: any) => {
              set(listParams.value, path, value)
            }}
            onReset={() => {
              listParams.value = { }
              page.value = 1
              pageSize.value = 20
              void search(1)
            }}
            onSubmit={() => search(1)} data={listParams.value} items={[
              [isCommon.value ? '剧包ID' : '集合ID', 'id', { type: 'text' }, { transform: transformInteger }],
              [isCommon.value ? '剧包名称' : '集合名称', 'package_name', { type: 'text' }],
              isCommon.value && ['应用场景', 'scene_type', {
                type: 'select',
                options: [
                  { label: '商业化策略', value: 1 },
                  { label: '首页内容', value: 3 },
                  { label: '活动', value: 4 },
                  { label: '运营位弹窗', value: 5 },
                ],
              }, { transform: transformInteger }],
              isCommon.value && ['排序', 'sort_desc', {
                type: 'select',
                options: [
                  { label: '创建时间：从新到旧', value: 'created-desc' },
                  { label: '创建时间：从旧到新', value: 'created-asc' },
                  { label: 'ID：从大到小', value: 'id-desc' },
                  { label: 'ID：从小到大', value: 'id-asc' },
                  { label: '优先级：从大到小', value: 'priority-desc' },
                  { label: '优先级：从小到大', value: 'priority-asc' },
                ],
              }],
            ]}
          />
        ),
        tableActions: () => (
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              剧包管理列表
            </div>
            <x-hide-when-in-dialog class="flex items-center gap-2">
              <Button
                class="btn btn-primary btn-sm"
                onClick={exportExcel}
                disabled={checkedItems.value.length === 0}
              >
                导出Excel
              </Button>
              <Button class="btn btn-primary btn-sm" onClick={
                () => {
                  resetOneForm()
                  resetTwoForm()
                  delete formData.value.id
                  curStep.value = 1
                  formData.value.import_type = isCommon.value ? 1 : 5
                  closeAddDialog.value = openDialog({
                    title: '新建剧包',
                    body: () => <SeriesPackageAddForm />,
                    mainClass: dialogMainClass,
                    customClass: '!w-[1000px] overflow-hidden',
                  })
                }
              }
              >新建
              </Button>
            </x-hide-when-in-dialog>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={columns.value} class="tm-table-fix-last-column" />
        ),
        pager: () => (
          total.value
            ? (
                <Pager class="justify-end"
                  v-model:page={page.value}
                  v-model:size={pageSize.value}
                  total={total.value}
                  onUpdate:page={() => {
                    void search(page.value)
                  }}
                  onUpdate:size={() => {
                    void search(page.value)
                  }}
                />
              )
            : null
        ),
      }}
    </NavFormTablePager>
  )
})

export default SeriesPackagePage
