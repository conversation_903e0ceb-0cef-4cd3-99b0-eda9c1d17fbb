import { httpClient } from 'src/lib/http-client'

export type IncomeData = {
  time_str: string
  k_value: number
}

export const apiGetStatistic = (params: {
  dt: string
  type: 'income_all' | 'new_user_income_all' | string
}) => {
  return httpClient.get<ApiResponse<{
    today: IncomeData[]
    yesterday: IncomeData[]
    week_ago: IncomeData[]
    month_ago: IncomeData[]
  }>>(`/statistic/realtime/${params.type}/list`, params)
}
