<template>
  <x-member-center-page
    class="bg-top-center flex size-full flex-col  bg-[url('src/h5_modules/images/mem-bg.webp')] bg-[length:100%_auto] bg-no-repeat">
    <Back :is-white="true" :title="T.memberCenterPage.vipCenter.ref.value" style="background-position: 0 20px;" :class="[
      'z-100 sticky top-0 flex h-11 w-full shrink-0 items-center bg-transparent',
      { 'bg-[#0B080B]': isScrolled }
    ]" />
    <div class="mt-2 flex flex-col gap-3 overflow-y-auto px-3">
      <div class="flex items-center justify-between">
        <p
          class="line-clamp-3 bg-gradient-to-r from-[#ffd571] via-[#fff4cf] to-[#ffc76e] bg-clip-text text-lg font-bold leading-snug text-transparent">
          {{ T.memberCenterPage.enjoyVip2.ref.value }}
        </p>
        <!-- <SvgIcon name="vip-2" class="ml-3 size-24 shrink-0" /> -->
        <Image class="ml-3 size-24 shrink-0" :src="vip2Webp" />
      </div>
      <template v-if="productData && productData.membership">
        <div v-for="(item, i) in productData.membership" :key="i"
          :class="productId === item.product_id && 'border-[#fff8e6] border border-solid'"
          class="relative flex items-center justify-between overflow-hidden rounded-lg border  border-solid border-[#2a2932] bg-gradient-to-l from-[#1f1e27] to-[#141419] px-3 py-4"
          @click="() => {
            productId = item?.product_id;
            if (selectedPayChannel === 'Paypal') {
              setDefaultPayChannel();
            }
            h5Track('vip_center', 'subscription', 'click', {
              price: item.discount_price / 100, //订阅VIP金额（数值）
              currency: item.currency,// 币种缩写（例如美元是USD）
              product_id: item?.product_id, //商品ID
            })
          }">
          <div class="flex w-[63%] flex-col gap-1">
            <!-- <SvgIcon name="crown" class="size-10 shrink-0" /> -->
            <Image class="size-10 shrink-0" :src="crownSvg" />
            <div class="mt-1 flex items-center">
              <p
                class="mr-1 line-clamp-1 bg-gradient-to-r from-[#ffc76d] via-[#ffc76d] to-[#ffc76d] bg-clip-text text-base font-bold leading-tight text-transparent">
                {{ item.title }}
              </p>
              <SvgIcon name="tag-vip" class="size-6 shrink-0" />
            </div>
            <p v-if="item.description" class="line-clamp-1 text-sm text-[#cccacb]">
              {{ item.description }}
            </p>
            <p v-if="item.discount_desc" class="line-clamp-1 text-[10px] text-[#cccacb]">
              {{ item.discount_desc }}
            </p>
            <p class="line-clamp-1 text-[10px] text-[#797b7d]">
              {{ item.tips }}
            </p>
          </div>
          <div class="ml-2 shrink-0">
            <div
              class="border-t-solid mb-1 w-24 rounded-lg border-t-[0.50px] border-t-white bg-gradient-to-l from-[#fdd779]  to-[#e9a131] p-2 text-center text-sm font-medium leading-4 text-[#5a280a]">
              {{ item?.currency_symbol }}{{ item.discount_price / 100 }}/ {{ item?.delivery_details?.period }}
            </div>
            <p class="text-center text-xs font-medium text-[#797b7d]">
              {{ item?.currency_symbol }}{{ item?.discount_price / 100 }}/ {{ item?.delivery_details?.period }}
            </p>
          </div>
          <div v-if="productId === item?.product_id" class="activeBg" />
        </div>
      </template>
      <div
        className="mb-5 pb-2 self-stretch bg-gradient-to-l from-[#131317]/75 to-[#131216] rounded-lg border border-solid border-[0.50px] border-[#816e6b] inline-flex flex-col">
        <div
          className="self-stretch mb-2 px-3 py-2.5 bg-gradient-to-l from-[#1f1e27] to-[#141419] rounded-tl-lg rounded-tr-lg border border-solid border-[0.50px] border-[#d9a858] inline-flex justify-center items-center gap-2">
          <div className="justify-center text-[#ffc76d] text-base leading-tight">
            {{ T.memberCenterPage.becomeVIP.ref.value }}
          </div>
        </div>
        <div v-if="vipBenefitsLoading" class="flex h-20 items-center justify-center">
          <Loading color="#ff007a" size="38px" />
        </div>
        <template v-if="vipCenterData.vip_benefits">
          <div v-for="(item, i) in vipCenterData.vip_benefits" :key="i" class="px-3">
            <div class="flex items-center py-2.5 text-sm text-[#cccacb]">
              <img class="mr-3 size-8 shrink-0" :src="item.icon">
              <p class="line-clamp-2">
                {{ item.title }}
              </p>
            </div>
          </div>
        </template>
      </div>
    </div>
    <!-- 支付方式 -->
    <div v-if="productData?.channel_list" class="fixed bottom-0 left-0 z-up mt-1 w-full bg-black px-3 py-2 text-text-2">
      <div class="mb-3">
        {{ PT.paymentPage.paymentMethod.ref }}
      </div>
      <div class="flex w-[calc(100%+0.75rem)] overflow-x-auto overflow-y-hidden pb-0.5 pr-3">
        <div class="flex gap-3">
          <div v-for="item in orderedProductList" :key="item.pay_channel" :class="getPayMethodClass(item)"
            class="relative flex h-[3.1875rem] w-[6.5rem] shrink-0 items-center justify-center overflow-hidden rounded-lg border border-solid border-line-2 bg-[var(--grey-13)] px-3"
            :style="selectedPayChannel === item.pay_channel && { background: 'var(--grey-13)', 'borderColor': '#fff8e6' }"
            @click="() => {
              selectedPayChannel = item.pay_channel
              payChannel = item.payment_channel
              paySubChannel = item.sub_payment_channel
              h5Track('pay_unlock', 'channel', 'click', {
                series_id: route.params.series_id as string,
                video_id: route.params.episode_id as string,
                pay_channel: item.pay_channel,
                pay_sub_channel: item.sub_payment_channel,
              })
            }">
            <Image class="w-20" :src="item.icon" />
            <div v-if="selectedPayChannel === item.pay_channel" class="channelActiveBg" />
          </div>
        </div>
      </div>
      <div class="mt-5 flex h-11 w-full items-center justify-center rounded-lg bg-brand-6 font-medium text-white"
        @click="() => {
          handlePay({
            from: curPath,
            series_id: route.params.series_id as string,
            video_id: route.params.episode_id as string,
            video_type: '',
          })
        }">
        {{ PT.paymentPage.payNow.ref }} {{ selectedProduct?.currency_symbol }} {{ (selectedProduct?.discount_price || 0)
          /
          100 }}
      </div>
    </div>
    <div class="mb-44  mt-2 flex flex-col gap-2 px-3 text-xs leading-4 text-[#434546]">
      <p>{{ T.memberCenterPage.tips.ref.value }}</p>
      <p>1.{{ T.memberCenterPage.tips1.ref.value }}</p>
      <p>2.{{ T.memberCenterPage.tips2.ref.value }}</p>
      <p>3.{{ T.memberCenterPage.tips3.ref.value }}</p>
      <p>4.{{ T.memberCenterPage.tips4.ref.value }}</p>
      <p>5.{{ T.memberCenterPage.tips5.ref.value }}</p>
      <p>6.{{ T.memberCenterPage.tips6.ref.value }}</p>
      <p>7.{{ T.memberCenterPage.tips7.ref.value }}</p>
      <p>8.{{ T.memberCenterPage.tips8.ref.value }}</p>
      <p>9.{{ T.memberCenterPage.tips9.ref.value }}</p>
    </div>
    <SvgIcon v-if="payLoading" name="payment/loading" class="fixed inset-0 m-auto size-8 animate-spin" />
    <Popup v-model:show="showPop" :destroy-on-close="true" :close-on-click-overlay="true" position="bottom"
      :style="{ height: '220px' }" class="rounded-t-l">
      <PayThirdButton :pay-channel="payChannel" :pay-sub-channel="paySubChannel" :type="thirdType"
        :product-id="productId" :from="curPath" :currency="selectedProduct?.currency" @click-rule="gotoRules" />
    </Popup>
  </x-member-center-page>
</template>
<script setup lang="ts">
import { h5Track } from 'src/lib/h5-track';
import { Back } from 'src/h5_modules/common/back/back'
import { useRoute, useRouter } from 'vue-router'
import { isIos, isTikTok } from 'src/lib/ua'
import { onMounted, onUnmounted, ref, computed } from 'vue';
import PayThirdButton from '../payment/payment-third-button.vue'
import { apiVipCenterData, apiProductList } from './member-center-api'
import { SvgIcon } from '@skynet/ui'
import { T } from './member-center.i18n'
import { StoreDataInfo } from 'src/h5_modules/store/store'
import { apiWalletMy } from 'src/h5_modules/profile/profile-api'
import { VipDataInfo } from './member-center'
import { Loading, Image, Popup } from 'vant';
import { usePayment } from '../payment/use-payment';
import { T as PT } from '../payment/payment-page.i18n'
import { gotoRules } from 'src/h5_modules/common/common'
import { crownSvg, vip2Webp } from './image'

const route = useRoute()
const curPath = encodeURIComponent((window.location.pathname + window.location.search).slice(1))

const vipCenterData = ref<VipDataInfo>({} as VipDataInfo)
const vipBenefitsLoading = ref(true);

const { productId, handlePay, thirdType, showPop, payChannel, paySubChannel,
  selectedPayChannel, payLoading, productData, getProductList, getPayMethodClass,
  orderedProductList, selectedProduct, isRecurringSelected, setDefaultPayChannel } = usePayment()

// const data = {
//         "vip_benefits": [
//             {
//                 "icon": "https://static-v1.mydramawave.com/icon/vip_center/ic-ublock-fill-color.png",
//                 "title": "Unlock All Episodes",
//                 "type": ""
//             },
//             {
//                 "icon": "https://static-v1.mydramawave.com/icon/vip_center/ic-free-fill-color.png",
//                 "title": "Ad-Free Experience",
//                 "type": ""
//             },
//             {
//                 "icon": "https://static-v1.mydramawave.com/icon/vip_center/ic-1080-fill-color.png",
//                 "title": "1080p HD Viewing",
//                 "type": ""
//             },
//             {
//                 "icon": "https://static-v1.mydramawave.com/icon/vip_center/ic-devices-fill-color.png",
//                 "title": "Up to five devices can be logged in",
//                 "type": ""
//             },
//             {
//                 "icon": "https://static-v1.mydramawave.com/icon/vip_center/ic-early-fill-color.png",
//                 "title": "Early Access for Members",
//                 "type": ""
//             },
//             {
//                 "icon": "https://static-v1.mydramawave.com/icon/vip_center/ic-vip-fill-color.png",
//                 "title": "Exclusive VIP Services",
//                 "type": ""
//             }
//         ],
//         "coming_soon": []
// }


// 滚动状态
const isScrolled = ref(false)

// 滚动处理函数
const handleScroll = () => {
  isScrolled.value = window.scrollY > 0
}

const getCenterData = async () => {
  // vipCenterData.value = data
  const res = await apiVipCenterData();
  vipBenefitsLoading.value = false
  if (res.data) {
    vipCenterData.value = res.data
  }
}
const getWalletMy = async () => {
  const res = await apiWalletMy()
  if (res.data && res.data) {
    if (res.data?.vip_level === 0) { // 非vip
      await getProductList()
      productId.value = productData.value.membership?.[0]?.product_id
    }
  }
}
onMounted(() => {
  getWalletMy()
  getCenterData()
  window.addEventListener('scroll', handleScroll)
  h5Track('vip_center', 'page', 'show')
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

</script>
<style scoped>
.activeBg {
  width: 6rem;
  height: 16rem;
  left: 6rem;
  top: -3rem;
  transition: all 0.3s ease;
  position: absolute;
  transform-Origin: top left;
  transform: rotate(30deg);
  opacity: 0.10;
  background: linear-gradient(270deg, rgba(253, 215, 121, 0) 0%, #FDD779 50%, rgba(253, 215, 121, 0) 100%);
  animation: shine 2s ease-in-out infinite;
}

.channelActiveBg {
  width: 5rem;
  height: 14rem;
  left: 2.5rem;
  top: -3rem;
  transition: all 0.3s ease;
  position: absolute;
  transform-Origin: top left;
  transform: rotate(30deg);
  background: linear-gradient(270deg, rgba(253, 215, 121, 0) 0%, rgba(253, 215, 121, 0.20) 50%, rgba(253, 215, 121, 0) 100%);
  animation: shine 2s ease-in-out infinite;
}

@keyframes shine {
  0% {
    left: -25%;
  }

  100% {
    left: 125%;
  }
}
</style>
