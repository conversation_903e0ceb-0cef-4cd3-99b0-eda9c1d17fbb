import { ref } from 'vue'
import { PurchaseHistoryItem } from './purchase-history/purchase-history'
import { ConsumptionItem } from './consumption-records/consumption-records'
import { apiGetMyWallet, apiGetPurchaseHistoryList, apiGetConsumptionList } from './my-wallet-api'
import { bindLoading, when } from '@skynet/shared'
import { authInfo } from 'src/lib/get-auth'

const myWallet = ref<MyWallet.MyWallet>()

export const useMyWalletStore = () => {
  const gettingMyWallet = ref(false)
  const gettingPurchaseHistory = ref(false)
  const gettingConsumption = ref(false)

  const defaultPurchaseHistory = {
    items: [],
    page_info: {
      next: '0',
      has_more: false,
    },
  }
  const purchaseHistory = ref<{
    items: PurchaseHistoryItem[]
    page_info: {
      next: string
      has_more: boolean
    }
  }>(defaultPurchaseHistory)

  const consumptionList = ref<{
    items: ConsumptionItem[]
    page_info: {
      next: string
      has_more: boolean
    }
  }>(defaultPurchaseHistory)

  const getMyWallet = () => {
    void when(() => authInfo).then(async () => {
      const res = await bindLoading(apiGetMyWallet(), gettingMyWallet)
      if (!res.data) return
      myWallet.value = res.data
    })
  }

  const getPurchaseHistoryList = (data: any) => {
    void when(() => authInfo).then(async () => {
      const res = await bindLoading(apiGetPurchaseHistoryList(data), gettingPurchaseHistory)
      if (!res.data) return
      purchaseHistory.value = {
        items: [...purchaseHistory.value.items, ...res?.data?.items],
        page_info: res?.data?.page_info,
      }
    })
  }

  const getConsumptionList = (data: any) => {
    void when(() => authInfo).then(async () => {
      const res = await bindLoading(apiGetConsumptionList(data), gettingConsumption)
      if (!res.data) return
      consumptionList.value = {
        items: [...consumptionList.value.items, ...res?.data?.items],
        page_info: res?.data?.page_info,
      }
    })
  }

  return {
    myWallet,
    getMyWallet,
    gettingMyWallet,
    purchaseHistory,
    getPurchaseHistoryList,
    gettingPurchaseHistory,
    consumptionList,
    getConsumptionList,
    gettingConsumption,
  }
}
