declare namespace M {

  interface HomeConfigSearchProps {
    app_id?: number
    language?: M.Language
    page_info: {
      page_index: number
      page_size: number
    }
  }

  interface HomeItemConfig {
    id?: number
    key?: string // 数据源key
    name: string // 模块名称
    type?: number// 类目类型，1：横排专栏，2：竖排专栏，3：榜单
    app_id?: number // 生效应用id
    language?: string // 生效语言
    state?: number // 生效状态，1：已发布，10：已下线
    updated?: number | string // 更新时间
    u_user_name?: string // 更新人
    created?: number | string // 创建时间
    c_user_name?: string // 创建人
    timing_type?: number // 定时类型，1：立即发布，2：定时发布
    timing_time?: string | number // 发布时间
    category_id?: number // 内容id
    category_name?: string // 内容名称
    sort_no?: number // 排序
    category_ids?: number[] // 内容id 用于multi-select
    show_title?: number // 是否显示标题，1：显示，2：不显示
    crowd_info?: number // 是否新老用户
  }

  interface HomeConfigRelease {
    module_ids: number[]
    release_type: number // 发布类型，1：发布，10：下线
  }

  interface HomeConfigSort {
    app_id: number
    language: string
    curr_row_id: number// 当前操作记录id,获取首页模块列表 接口返回的记录唯一id
    to_sort_no: number
  }

  interface HomeConfigCategorySearchProps {
    app_id?: number
    language?: M.Language
    type?: number // 类目类型，1：横排专栏，2：竖排专栏，3：榜单
    search_key?: string // 关键词
  }

  interface HomeConfigPreviewProps {
    app_id: number
    language: M.Language
    category_id?: number
    type?: number // 类目类型，1：横排专栏，2：竖排专栏，3：榜单
  }

  interface DramaItem {
    cover: string
    title: string
    desc: string
    tag: string[]
    free: boolean
    series_tag: string[]
    episode_info: {
      id: string
      name: string
      cover: string
      video_url: string
      mp4_urls: {
        trans_720p: string
        trans_540p: string
        trans_360p: string
      }
      caption_urls: [{
        lang: string
        name: string
        url: string
      },
      {
        lang: string
        name: string
        url: string
      },
      ]
      index: number
      unlock: boolean
      duration: number
      episode_price: number
      video_type: number
      new: boolean
      update_time: number
    }
    link_type: number
    link: string
  }

  interface HomepageUnit {
    type: 'banner' | 'column_horizontal' | 'column_vertical' | 'billboard' | 'recommend'
    module_name: string
    items: DramaItem[]
    show_title?: number // 是否显示标题，1：显示，2：不显示
  }
}
