import { html as terms } from '../../../bin/public/dramareels/rules/en/terms.md'
import { html as privacy } from '../../../bin/public/dramareels/rules/en/privacy.md'
import { html as termsEs } from '../../../bin/public/dramareels/rules/es/terms.md'
import { html as privacyEs } from '../../../bin/public/dramareels/rules/es/privacy.md'
import { html as termsDe } from '../../../bin/public/dramareels/rules/de/terms.md'
import { html as privacyDe } from '../../../bin/public/dramareels/rules/de/privacy.md'
import { html as termsFr } from '../../../bin/public/dramareels/rules/fr/terms.md'
import { html as privacyFr } from '../../../bin/public/dramareels/rules/fr/privacy.md'
import { html as termsId } from '../../../bin/public/dramareels/rules/id/terms.md'
import { html as privacyId } from '../../../bin/public/dramareels/rules/id/privacy.md'
import { html as termsIt } from '../../../bin/public/dramareels/rules/it/terms.md'
import { html as privacyIt } from '../../../bin/public/dramareels/rules/it/privacy.md'
import { html as termsJa } from '../../../bin/public/dramareels/rules/ja/terms.md'
import { html as privacyJa } from '../../../bin/public/dramareels/rules/ja/privacy.md'
import { html as termsKo } from '../../../bin/public/dramareels/rules/ko/terms.md'
import { html as privacyKo } from '../../../bin/public/dramareels/rules/ko/privacy.md'
import { html as termsPt } from '../../../bin/public/dramareels/rules/pt/terms.md'
import { html as privacyPt } from '../../../bin/public/dramareels/rules/pt/privacy.md'
import { html as termsRu } from '../../../bin/public/dramareels/rules/ru/terms.md'
import { html as privacyRu } from '../../../bin/public/dramareels/rules/ru/privacy.md'
import { html as termsVi } from '../../../bin/public/dramareels/rules/vi/terms.md'
import { html as privacyVi } from '../../../bin/public/dramareels/rules/vi/privacy.md'
import { html as termsTr } from '../../../bin/public/dramareels/rules/tr/terms.md'
import { html as privacyTr } from '../../../bin/public/dramareels/rules/tr/privacy.md'
import { html as termsTl } from '../../../bin/public/dramareels/rules/tl/terms.md'
import { html as privacyTl } from '../../../bin/public/dramareels/rules/tl/privacy.md'
import { html as termsMs } from '../../../bin/public/dramareels/rules/ms/terms.md'
import { html as privacyMs } from '../../../bin/public/dramareels/rules/ms/privacy.md'
import { html as termsZh } from '../../../bin/public/dramareels/rules/zh/terms.md'
import { html as privacyZh } from '../../../bin/public/dramareels/rules/zh/privacy.md'

export const rulesMap: Record<string, Record<string, string>> = {
  en: {
    terms: terms,
    privacy: privacy,
  },
  pt: {
    terms: termsPt,
    privacy: privacyPt,
  },
  ru: {
    terms: termsRu,
    privacy: privacyRu,
  },
  vi: {
    terms: termsVi,
    privacy: privacyVi,
  },
  tr: {
    terms: termsTr,
    privacy: privacyTr,
  },
  tl: {
    terms: termsTl,
    privacy: privacyTl,
  },
  es: {
    terms: termsEs,
    privacy: privacyEs,
  },
  de: {
    terms: termsDe,
    privacy: privacyDe,
  },
  fr: {
    terms: termsFr,
    privacy: privacyFr,
  },
  it: {
    terms: termsIt,
    privacy: privacyIt,
  },
  ja: {
    terms: termsJa,
    privacy: privacyJa,
  },
  ko: {
    terms: termsKo,
    privacy: privacyKo,
  },
  id: {
    terms: termsId,
    privacy: privacyId,
  },
  ms: {
    terms: termsMs,
    privacy: privacyMs,
  },
  zh: {
    terms: termsZh,
    privacy: privacyZh,
  },
}
