import { createComponent, getQueries, mc } from '@skynet/shared'
import { Button, Input, SvgIcon } from '@skynet/ui'
import { useFeedbackStore } from './feedback-store'
import { Upload } from './upload/upload'
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { track, trackNative } from 'src/lib/track'
import { apiSubmitFeedback, apiGetProblemTypes } from './feedback-api'
import { showToast } from '../../common/toast/toast'
import { ActionDialog } from './action-dialog/action-dialog'
import { jsBridge } from 'src/lib/jsbridge'
import { useLocale } from 'src/lib/use-locale'
import { Back } from 'src/h5_modules/common/back/back'
import { h5Track } from 'src/lib/h5-track'
import { chevronRight } from '../images/images'
import { useRouter } from 'vue-router'
import { isIos } from 'src/lib/ua'
type FeedbackPageOptions = {
  props: {}
}
export const FeedbackPage = createComponent<FeedbackPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const { showNavigation } = getQueries({ showNavigation: true })
  const language = router.currentRoute.value.query.language as string
  const from = router.currentRoute.value.query.from as string
  const series_id = router.currentRoute.value.query.series_id as string
  const episode_id = router.currentRoute.value.query.episode_id as string
  const safe_bottom = router.currentRoute.value.query.safe_bottom as string

  const { id, problem_type } = getQueries({ id: 0, problem_type: 0 })
  const showBack = ref(false)

  const wrapper = ref<HTMLDivElement>()
  const isSubmitting = ref(false)
  const visible = ref(false)

  const { t } = useLocale(language)
  const problemTypes = ref<{
    value: number | string
    label: string
  }[]>([])

  const { formData, resetFormData } = useFeedbackStore()

  watch(() => problem_type, () => {
    if (!problem_type) {
      return
    }
    formData.value.problem_type = problem_type
  }, { immediate: true })

  const canSubmit = computed(() => {
    return formData.value.problem_type && formData.value.description !== '' && isSubmitting.value === false
  })

  const selectedProblem = computed(() => {
    return problemTypes.value.find(row => row.value === formData.value.problem_type)?.label || ''
  })

  const validateAll = () => {
    // 问题类型必填
    if (!formData.value.problem_type) {
      return false
    }
    // description 必填
    if (formData.value.description === '') {
      return false
    }
    // email 邮箱格式校验
    if (formData.value.email && !validateEmail(formData.value.email.trim())) {
      showToast(t('toast.email-incorrect'))
      return false
    }
    return true
  }

  const validateEmail = (email: string) => {
    const reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
    return reg.test(email)
  }

  const submit = () => {
    if (!formData.value.problem_type) {
      showToast(t('customer-service-center.problem-type-not-choose'))
      return
    }

    if (formData.value.description.length == 0) {
      showToast(t('customer-service-center.description-empty'))
      return
    }
    if (formData.value.description.length < 6) {
      showToast(t('customer-service-center.description-min-length'))
      return
    }

    if (isSubmitting.value) {
      return
    }
    if (!canSubmit.value) return
    
    const track = jsBridge.inDramawaveApp ? trackNative : h5Track
    track('feedback', 'submit', 'click', {
      from,
      series_id,
      episode_id,
      button: id ? 1 : 0,
    })
    if (!validateAll()) return
    isSubmitting.value = true
    void apiSubmitFeedback({ ...formData.value, email: formData.value.email.trim(), series_id, episode_id, work_order_id: id }).then(() => {
      isSubmitting.value = true
      showToast(t('toast.submit-success'))
      setTimeout(() => {
        if (jsBridge && jsBridge.inDramawaveApp) {
          void jsBridge('back')
        } else {
          router.back()
        }
      }, 2000)
    }).finally(() => {
      isSubmitting.value = false
    })
  }
  const getProblemTypeList = async () => {
    const res = await apiGetProblemTypes()
    problemTypes.value = res.data?.list.map(row => {
      return {
        value: row.id,
        label: row.problem_type,
      }
    }).sort((a, b) => a.value - b.value) || []
  }

  const placeholder = computed(() => {
    return [
      t('customer-service-center.description-placeholder'),
      t('customer-service-center.payment-issue-placeholder'),
      t('customer-service-center.content-issue-placeholder'),
      t('customer-service-center.ads-issue-placeholder'),
      t('customer-service-center.technical-issue-placeholder'),
      t('customer-service-center.other-issue-placeholder'),
    ][(formData.value.problem_type ?? 0)]
  })

  onBeforeUnmount(() => {
    resetFormData()
  })

  onMounted(() => {
    track('feedback', 'page', 'show', {
      from,
      series_id,
      episode_id,
    })
    document.title = t('customer-service-center.feedback')
    void getProblemTypeList()
  })

  onMounted(() => {
    if (jsBridge.inDramawaveApp) {
      showBack.value = !showNavigation
    } else {
      showBack.value = true
    }
  })

  return () => (
    <div ref={wrapper} class="size-full bg-[var(--black)]">
      {showBack.value && (
        <Back
          // backTo={jsBridge.inDramawaveApp && from !== 'css' ? () => void jsBridge('close') : undefined}
          title={t('customer-service-center.feedback')}
          isWhite={true}
          class="shrink-0 bg-transparent" />
      ) }
      <form class="pb-23 flex w-full flex-col gap-y-5 px-3 pt-5">
        {/* problem_type */}
        <x-form-item class="flex justify-between gap-x-2">
          <x-label class="flex items-center justify-start gap-x-1 text-sm font-normal text-[#fdfbfc]">
            {t('customer-service-center.problem-type')}
            <div class="text-[var(--brand-6)]">*</div>
          </x-label>
          <x-value class="flex flex-1 items-center gap-x-1 overflow-hidden break-words text-right" onClick={() => {
            if (problem_type && id) {
              return
            }
            visible.value = true
          }}>
            {
              selectedProblem.value
                ? <div class={mc('w-full truncate text-sm', id && problem_type ? 'text-[#434546]' : 'text-[#fdfbfc]')}>{selectedProblem.value}</div>
                : <div class="w-full truncate text-sm text-[#434546]">{t('customer-service-center.problem-type-placeholder')}</div>
            }
          </x-value>
          <img src={chevronRight} class={mc('size-5 text-[#797b7d] transition-transform', visible.value && 'rotate-180')} onClick={() => {
            if (problem_type && id) {
              return
            }
            visible.value = true
          }} />
        </x-form-item>
        <x-form-item>
          <x-label class="flex items-center justify-start gap-x-1 text-sm font-normal text-[var(--white)]">
            {t('customer-service-center.description')}
            <div class="text-[var(--brand-6)]">*</div>
          </x-label>
          <Input
            v-model={formData.value.description}
            placeholder={placeholder.value}
            type="textarea"
            class="mt-3 rounded-lg bg-[var(--grey-13)] pb-7"
            inputClass="h-[calc(150px-2rem)] text-sm pb-0 rounded-b-none"
            maxlength={300}
            showWordLimit
          />
        </x-form-item>
        <x-form-item>
          <x-label class="flex items-center justify-start gap-x-1 text-sm font-normal text-[var(--white)]">
            {t('customer-service-center.upload-pictures')} ({formData.value.image_list.length}/3)
          </x-label>
          <Upload list={formData.value.image_list} class="mt-3" onRemove={(index: number) => formData.value.image_list.splice(index, 1)} onAdd={(item: string) => formData.value.image_list.push(item)} />
          {
            // formData.value.image_list && formData.value.image_list[0] && <img src={'https://static.mydramawave.com/' + formData.value.image_list[0]} class="w-full" />
          }
        </x-form-item>
        <x-form-item>
          <x-label class="flex items-center justify-start gap-x-1 text-sm font-normal text-[var(--white)]">
            Email
          </x-label>
          <Input
            v-model={formData.value.email}
            placeholder={t('customer-service-center.email-placeholder')}
            type="text"
            inputClass="text-sm"
            class="mt-3"
            onFocus={() => {
              setTimeout(() => {
                window?.scroll({ top: document.body.scrollHeight, behavior: 'smooth' })
              }, 1000)
            }}
          />
        </x-form-item>
      </form>
      <div class={`pc:w-[var(--phone-page-max-width)] pad:w-[var(--phone-page-max-width)] fixed bottom-0 left-1/2 z-shareButton w-full -translate-x-1/2 bg-[var(--black)] ${jsBridge.inDramawaveApp ? 'h-23' : 'share-bottom-2 flex h-16 items-center pb-2 pt-3'}`}>
        <Button
          class={`pc:w-[calc(var(--phone-page-max-width)-1.5rem)] pad:w-[calc(var(--phone-page-max-width)-1.5rem)] fixed left-1/2 z-shareButton h-11 w-[calc(100%-1.5rem)] -translate-x-1/2 rounded-lg border-none bg-[var(--brand-6)] text-base font-medium leading-tight text-[var(--text-5)] ${jsBridge.inDramawaveApp ? 'bottom-11' : '!relative'}`}
          onClick={submit}
        >
          {t('customer-service-center.submit-feedback')}
        </Button>
      </div>
      <ActionDialog
        visible={visible.value}
        title={t('customer-service-center.problem-type')}
        actions={problemTypes.value}
        selected={formData.value.problem_type}
        onClose={() => visible.value = false}
        safeBottom={safe_bottom === 'false'}
        onSelect={(id: number | string) => {
          formData.value.problem_type = id as number
          setTimeout(() => {
            visible.value = false
          }, 200)
        }}
      />
    </div>
  )
})

export default FeedbackPage
