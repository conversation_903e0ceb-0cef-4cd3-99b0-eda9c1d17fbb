#!/bin/bash
set -e
export LANG=en_US.UTF-8

# define vars
TEST_SERVERS=(na-test-dramato-server-1)
PROD_SERVERS=(na-prod-dramato-server-16 na-prod-dramato-server-17 na-prod-freereels-server-3)
TEST_DIR="/home/<USER>/www_free"
PROD_DIR="/home/<USER>/www_free"

# check params
if [[ $# != 1 ]]; then
  echo "Usage: $0 [env:test|prod]"
  exit 0
fi

# get base path
CURR_DIR=$(dirname "$0")
OUTPUT_DIR="$CURR_DIR/dist"

# parse parameters
ENV_NAME=$(echo "$1" | tr '[:upper:]' '[:lower:]')

if [[ "$ENV_NAME" == "prod" ]]; then
  SERVERS=("${PROD_SERVERS[@]}")
  DIR=$PROD_DIR
else
  SERVERS=("${TEST_SERVERS[@]}")
  DIR=$TEST_DIR
fi

pwd
rm -rf dist || echo 'dist not exits'
mkdir dist || echo 'dist exits'
tar -xf dist.tar -C dist

for SERVER in "${SERVERS[@]}"; do
  if [[ "$SERVER" == "${SERVERS[0]}" && "$ENV_NAME" == "prod" ]]; then
    ssh worker@$SERVER "rm -rf $DIR/dist; mkdir -p $DIR/dist"
    scp -r $OUTPUT_DIR/* worker@$SERVER:$DIR/dist
    ssh worker@$SERVER "ossutil sync -f $DIR/dist oss://us-dramato-prod/frontend_static_free/"
  fi
  scp -r $OUTPUT_DIR/* worker@$SERVER:$DIR
done

set +e
echo 'success'
