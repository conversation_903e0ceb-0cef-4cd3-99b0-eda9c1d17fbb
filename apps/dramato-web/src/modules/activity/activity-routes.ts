import { r } from '@skynet/shared'
import ShareLayout from 'src/layouts/share-layout'

export const activityRoutes = [
  r('embed-activity', '', null, [
    r('easter', 'DramaWave', () => import('./easter/easter-page')),
    r('workers-day', 'DramaWave', () => import('./workers-day/workers-day-page')),
    r('mothers-day', 'DramaWave', () => import('./mothers-day/mothers-day-page')),
    r('weekend', 'DramaWave', () => import('./weekend/weekend-20250516-page')),
    r('tip', 'DramaWave', () => import('./activity-tips')),
    r('mexico', 'DramaWave', () => import('./mexico/mexico-page')),
    r('dragon-boat-festival', 'DramaWave', () => (import('./dragon-boat-festival/dragon-boat-festival-page'))),
    r('20250603', 'DramaWave', () => (import('./day-20250603/day-20250603-page.tsx'))),
  ]),
  // 从 2025 年 4 月 19 日起，不在使用 activity 路由，请用 embed-activity 路由
  r('activity', '', ShareLayout, [
    r('', '', () => import('./activity-page')),
    r('promote', '', () => import('./promote-page')),
    r('christmas/:price', '', () => import('./christmas/christmas-page')),
    r('womens', '', () => import('./womens-day/womens-day-page')),
    r('new-year', '', () => import('./new-year/new-year-page')),
    r('spring', '', () => import('./spring/spring-page')),
    r('white-day', '', () => import('./white-day/white-day-page')),
    r('season', '', () => import('./season/season-page')),
    r('fools-day', '', () => import('./fools-day/fools-day-page')),
    r('eid', '', () => import('./eid/eid-page')),
    r('thai-water', 'DramaWave', () => import('./thai-water/thai-water-page')),
    r('weekend', 'DramaWave', () => import('./weekend/weekend-page')),
    // 上面是通过后台配置创建的活动
    r('activity-h5', '', () => import('./activity-h5/activity-h5-page.vue')),
  ]),
]
