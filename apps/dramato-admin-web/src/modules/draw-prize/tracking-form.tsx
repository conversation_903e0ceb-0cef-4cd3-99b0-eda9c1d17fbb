import { createComponent, fn, required, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert } from '@skynet/ui'
import type { Fn } from '@vueuse/core'
import { set } from 'lodash-es'
import { httpClient, keepError } from 'src/lib/http-client'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, ref } from 'vue'
import { z } from 'zod'
import { trackingStatusMap } from './const'
import { usePrizeRecordList } from './use-prize-record-list'

type TrackingFormOptions = {
  props: {
    recordId: M.PrizeRecord['id']
  }
  emits: {
    beforeSave: Fn
    afterSave: Fn
  }
}
export const TrackingForm = createComponent<TrackingFormOptions>({
  props: {
    recordId: required,
  },
  emits: {
    beforeSave: fn,
    afterSave: fn,
  },
}, (props, { emit }) => {
  const { findRecord } = usePrizeRecordList()
  const formData = ref<Partial<M.TrackingInfo>>({
    tracking_status: '',
    tracking_no: '',
    tracking_company: '',
    tracking_url: '',
  })

  const rules = z.object({
    tracking_status: z.string().min(1, '物流状态不能为空'),
    tracking_no: z.string().min(1, '物流单号不能为空'),
    tracking_company: z.string().min(1, '物流公司不能为空'),
    tracking_url: z.string().url('请输入有效的物流链接').optional().or(z.literal('')),
  })

  const { validateAll, error } = useValidator(formData, rules)
  const Form = CreateForm<Partial<M.TrackingInfo>>()

  onMounted(() => {
    if (!props.recordId) return
    const record = findRecord(props.recordId)
    if (record?.tracking_info) {
      formData.value = { ...record.tracking_info }
    }
  })

  return () => (
    <x-tracking-form class="block">
      <Form
        data={formData.value}
        error={error.value}
        onChange={(path, value) => set(formData.value, path, value)}
        class="flex flex-col"
        onSubmit={() => onSubmit().then(() => emit('afterSave'))}
        actions={() => (
          <Button class="btn btn-primary btn-sm" type="submit">
            提交
          </Button>
        )}
        items={[
          [
            [requiredLabel('物流状态'), 'tracking_status', {
              type: 'select',
              autoInsertEmptyOption: false,
              options: Object.entries(trackingStatusMap).map(([value, label]) => ({ label, value })),
            }],
            [requiredLabel('物流公司'), 'tracking_company', { type: 'text', placeholder: '请输入物流公司名称' }],
          ],
          [requiredLabel('物流单号'), 'tracking_no', { type: 'text', placeholder: '请输入物流单号' }],
          ['物流链接', 'tracking_url', { type: 'text', placeholder: '请输入物流查询链接（选填）' }],
        ]}
      />
    </x-tracking-form>
  )

  async function onSubmit() {
    if (!validateAll()) {
      return Promise.reject()
    }
    console.log(JSON.stringify(formData.value, null, 2))
    emit('beforeSave')
    await httpClient.post<ApiResponse>('/draw/prize/record/tracking/update', {
      id: props.recordId,
      ...formData.value,
    })
      .catch(keepError(() => showAlert('更新物流信息失败，请稍后再试', 'error')))
    showAlert('物流信息更新成功', 'success')
  }
})

export default TrackingForm
