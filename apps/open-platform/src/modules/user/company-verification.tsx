import { createComponent, fn, useValidator } from '@skynet/shared'
import { Button, CreateForm, SvgIcon, transformInteger } from '@skynet/ui'
import { computed, ref } from 'vue'
import { useMe } from './use-me'
import { set } from 'lodash-es'
import { Uploader } from '../common/uploader/uploader'
import { apiGetTempPath } from '../drama/my-drama-api'
import { z } from 'zod'
import { ElMessage } from 'element-plus'
import { FormItem } from '@skynet/ui/form/form-types'
import { useI18n } from 'vue-i18n'
type CompanyVerificationOptions = {
  props: {}
  emits: {
    success: () => void
    error: () => void
  }
}
export const CompanyVerification = createComponent<CompanyVerificationOptions>({
  props: {},
  emits: {
    success: fn,
    error: fn,
  },
}, (props, { emit }) => {
  type Data = {
    company: string
    name: string
    usci: string
    licence_pic: string
    user_type: number
    apply_reason: string
  }
  const formData = ref<Data>({
    company: '',
    name: '',
    usci: '',
    licence_pic: '',
    user_type: 1,
    apply_reason: '',
  })
  const { t } = useI18n()
  const Form = CreateForm<Data>()
  const rules = z.object({
    company: z.string().min(1, t('pleaseEnterCompanyName')),
    name: z.string().min(1, t('pleaseEnterYourName')),
    usci: z.string().min(1, t('pleaseEnterEinCode')),
    licence_pic: z.string().min(1, '请上传营业执照照片'),
    user_type: z.number().min(1, '请选择认证类型'),
    apply_reason: z.string().min(1, '请输入申请理由'),
  })
  const { validateAll, error } = useValidator(formData, rules)
  const { updateMe } = useMe()
  const onSave = () => {
    const exclude = formData.value.user_type === 1 ? ['apply_reason', 'name'] : ['company', 'usci', 'licence_pic']
    if (validateAll({ exclude })) {
      if (formData.value.user_type === 2) {
        // 个人认证的姓名使用 company 字段
        formData.value.company = formData.value.name
      }
      updateMe(formData.value).then(() => emit('success'), () => emit('error'))
    } else {
      ElMessage.info('请检查输入内容')
    }
  }
  const uploadRef = ref<InstanceType<typeof Uploader>>()
  return () => (
    <x-company-verification class="block">
      <Form
        class="cp-form flex-col [&_x-label]:w-[120px] [&_x-label]:text-sm [&_x-label]:leading-[38px]"
        hideEmptyError onChange={(path, value) => set(formData.value, path, value)} onSubmit={onSave} error={error.value} data={formData.value}
        items={[
          [t('type'), 'user_type', {
            type: 'radio',
            options: [{ label: t('enterprise'), value: 1 }, { label: t('personal'), value: 2 }],
            class: 'h-[38px] flex items-center',
          }, { transform: transformInteger, class: 'cp-form-item' }],
          ...(formData.value.user_type === 1 ? [
            [t('company'), 'company', {
              type: 'text', class: 'leading-[22px] py-2 px-4 bg-[#faf7fa] rounded-lg',
              placeholder: t('pleaseEnterCompanyName'),
              maxlength: 50,
            }, { class: 'cp-form-item' }],
            [t('ein'), 'usci', {
              type: 'text', class: 'leading-[22px] py-2 px-4 bg-[#faf7fa] rounded-lg',
              placeholder: t('pleaseEnterEinCode'),
              maxlength: 18,
            }, { class: 'cp-form-item' }],
            [t('businessLicensePhoto'), 'licence_pic', {
              type: 'custom', render: ({ onInput, item, value }) => {
                const v = value as string
                return (
                  <x-input-wrapper class="!h-auto">
                    <Uploader ref={uploadRef} accept="png,jpg,jpeg,webp" maxsize={1024 * 1024 * 100} ossKeyType="resource"
                      showFileList={false}
                      class="size-[100px] cursor-pointer overflow-hidden rounded-md border border-dashed border-[#CCCACB]"
                      onUploadSuccess={d => {
                        console.log('d:', d)
                        onInput(d.oss_static_prefix + d.key!)
                      }}
                    >
                      {
                        v ? (
                          <img src={v} class="size-full object-cover" />
                        ) : (
                          <div class="flex size-full shrink-0 items-center justify-center bg-[#FAF7FA] text-sm text-[#A1A0A3]">
                            <SvgIcon name="ic-add" class="size-8 text-[var(--text-4)]" noColor />
                          </div>
                        )
                      }
                    </Uploader>
                  </x-input-wrapper>
                )
              },
            }, { class: 'cp-form-item [&_x-label]:self-center' }],
          ] : [
            [t('name'), 'name', {
              type: 'text', class: 'leading-[22px] py-2 px-4 bg-[#faf7fa] rounded-lg',
              maxlength: 15,
              placeholder: t('pleaseEnterYourName'),
            }, { class: 'cp-form-item' }],
            [t('reasonForApplication'), 'apply_reason', {
              type: 'textarea',
              class: 'leading-[22px] py-2 px-4 bg-[#faf7fa] rounded-lg',
              rows: 3,
              placeholder: '请输入代表作品或推荐人',
            }, { class: 'cp-form-item' }],
          ]) as FormItem[],
        ]}
        actionClass="[&_x-label]:hidden"
        actions={() => <Button class="btn btn-md px-10" type="submit">{t('submit')}</Button>}
      />
    </x-company-verification>
  )
})

export default CompanyVerification
