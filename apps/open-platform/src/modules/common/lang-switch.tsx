import { createComponent } from '@skynet/shared'
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'
import { setLocale } from 'src/locales'

type LangSwitchOptions = {
  props: {}
}

export const LangSwitch = createComponent<LangSwitchOptions>({
  props: {},
}, props => {
  const currentLocale = ref(localStorage.getItem('locale') || 'zh-CN')
  const isAnimating = ref(false)
  const { t } = useI18n()

  const toggleLang = () => {
    if (isAnimating.value) return
    isAnimating.value = true

    const newLocale = currentLocale.value === 'zh-CN' ? 'en-US' : 'zh-CN'
    setLocale(newLocale)
    currentLocale.value = newLocale

    setTimeout(() => {
      isAnimating.value = false
    }, 300)
  }

  return () => (
    <div
      class="bg-fill-2 relative ml-6 inline-flex h-[30px] cursor-pointer select-none items-center overflow-hidden rounded-lg text-center"
      onClick={toggleLang}
    >
      <div
        class={[
          'bg-brand-6 absolute inset-y-0.5 top-0 h-full w-8 rounded-lg transition-transform duration-300 ease-in-out',
          currentLocale.value === 'zh-CN' ? 'left-0' : 'translate-x-8',
        ]}
      />
      <span class={[
        'relative z-10 w-8 rounded-full py-1 text-sm font-normal transition-colors duration-300',
        currentLocale.value === 'zh-CN' ? 'text-white' : 'text-[var(--btn-info-text)]',
      ]}>中</span>
      <span class={[
        'relative z-10 w-8 rounded-full py-1 text-sm font-normal transition-colors duration-300',
        currentLocale.value === 'en-US' ? 'text-white' : 'text-[var(--btn-info-text)]',
      ]}>EN</span>
    </div>
  )
})

export default LangSwitch
