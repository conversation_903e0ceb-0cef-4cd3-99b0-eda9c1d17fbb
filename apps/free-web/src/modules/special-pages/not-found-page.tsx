import { createComponent } from '@skynet/shared'
import { useLocale } from 'src/lib/use-locale'
import router from 'src/router'
import { Back } from '../common/back/back'
type NotFoundPageOptions = {
  props: {}
}
export const NotFoundPage = createComponent<NotFoundPageOptions>({
  props: {},
}, props => {
  const { t } = useLocale(router.currentRoute.value.query.language as string)
  return () => (
    <div class="bg-[var(--surface-1)] text-center h-screen flex flex-col justify-center items-center text-[var(--text-1)] relative
      max-w-[var(--phone-page-max-width)] pad:max-w-[var(--phone-page-max-width)] pc:max-w-[var(--phone-page-max-width)]
      mx-auto
    "
    >
      <Back class="absolute left-0 z-up top-[max(env(safe-area-inset-top),_44px)]" />
      <h1 class="text-2xl font-bold -mt-20">{t('not_found.title')}</h1>
    </div>
  )
})
