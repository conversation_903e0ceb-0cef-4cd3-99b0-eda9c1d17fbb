declare namespace M {
  namespace UserEngagement {
    namespace List {
      interface Request {
        id?: number
        name?: string
        status?: number // 状态 0 不限制 1 草稿 2 上架 3 下架
        next?: string // "page=1&page_size=10"
        page_info?: {
          page_index?: number
          page_size?: number
        }
      }
      interface Item {
        id?: number // ID
        name: string
        is_abtest: number // 是否AB测试
        operator_name: string
        platform: number // 0 all 1 IOS 2 Android
        status: number // 状态 1 草稿 2 上架 3 下架
        list_time: string // 上架时间
        create_time: string // 创建时间
      }

      interface Response {
        page_info: {
          total: number
          next: string
          has_more: boolean
        }
        items: Item[]
      }
    }

    namespace Save {
      interface Request {
        id?: number
        name: string
        is_abtest: number // 是否AB测试
        priority: number // 优先级
        platform: number // 0 all 1 IOS 2 Android
        strategy_layer_ids?: number[] // 用户分层ID
        duration: number // 持续时间 -1 表示不限
        undertake_type: number // 承接类型 1 直接开剧;2 推荐弹窗,5秒开剧;3 推荐弹窗,点击开剧; 4 红包弹窗
        drama_source: number // 短剧来源，1，算法，2，推荐1，3，推荐2
      }
    }

    namespace option {
      interface Request {
        id: number
        option: number // 操作类型，1，上架，2，下架，3，删除
      }
    }
  }
}
