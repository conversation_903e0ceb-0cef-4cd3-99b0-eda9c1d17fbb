import { createComponent, useValidator } from '@skynet/shared'
import { Button, CreateForm, transformInteger } from '@skynet/ui'
import { z } from 'zod'
import { requiredLabel } from 'src/lib/required-label'
import { set } from 'lodash-es'
import { useTasks } from './use-targeted-tasks.tsx'

export const TargetedTasksForm = createComponent(null, () => {
  const {
    task,
    onCreate,
    hideCreateTaskDialog,
  } = useTasks()
  const Form = CreateForm<M.TargetedTasks.SaveRequest.Task>()

  const formRules = z.object({
    search_key: z.string().min(1, '请输入搜索关键词'),
    info_from: z.number().min(1, '请选择'),
    search_type: z.number().min(1, '请选择'),
    period: z.number().min(1, '请选择'),
    start_page: z.number().min(1, '请填写'),
  })

  const { error, validateAll } = useValidator(task, formRules)

  return () => (
    <>
      <div class="flex-1 flex flex-col overflow-y-auto px-[20px] gap-y-[25px]">
        <Form
          class="grid gap-y-3 grid-cols-1 flex-1"
          hasAction={false}
          error={error.value}
          onChange={(path, value) => {
            set(task.value || {}, path, value)
          }}
          items={[
            {
              label: requiredLabel('搜索关键词'),
              path: 'search_key',
              input: {
                type: 'textarea',
                placeholder: '请输入搜索关键词,可以通过分号分隔多任务',
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('查找类型'),
              path: 'search_type',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  {
                    label: '全部',
                    value: 1,
                  },
                  {
                    label: '产品名称',
                    value: 2,
                  },
                  {
                    label: '短剧名字',
                    value: 7,
                  },
                ],
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('时间周期'),
              path: 'period',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  {
                    label: '全部',
                    value: 1,
                  },
                  {
                    label: '7天',
                    value: 2,
                  },
                  {
                    label: '30天',
                    value: 3,
                  },
                ],
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('资源类型'),
              path: 'info_from',
              transform: transformInteger,
              input: {
                type: 'radio',
                options: [
                  {
                    label: '海外',
                    value: 1,
                  },
                  {
                    label: '国内',
                    value: 2,
                  },
                ],
              },
              class: 'col-span-3',
            },
            {
              label: requiredLabel('起始页码'),
              path: 'start_page',
              transform: transformInteger,
              input: {
                type: 'number',
              },
              class: 'col-span-3',
            },
            {
              label: '备注',
              path: 'comment',
              input: {
                type: 'textarea',
              },
              class: 'col-span-3',
            },
          ]}
          data={task.value}
        />
      </div>
      <div class="flex justify-end gap-x-2 px-[20px]">
        <Button class="btn  btn-sm" onClick={hideCreateTaskDialog.value}>取消</Button>
        <Button class="btn btn-primary btn-sm" onClick={() => {
          const exclude: string[] = []
          if (!validateAll({ exclude })) {
            console.log('error', error.value)

            return
          }
          void onCreate()
        }}
        >确定
        </Button>
      </div>
    </>
  )
})
