import { computed, onBeforeUnmount, onMounted, shallowRef, watch } from 'vue'
import { useSeriesItem } from './use-series-item'

type Callbacks = {
  onPlayOngoing5Seconds?: () => void
  onWaitOngoing2Seconds?: () => void
}
export function useOngoingTrack(seriesId: string, episodeId: string, callbacks: Callbacks) {
  const { onPlayOngoing5Seconds, onWaitOngoing2Seconds } = callbacks
  const timer = shallowRef<number | null>(null)
  const pausedAt = shallowRef<number | null>(Date.now())
  const playTimeSum = shallowRef<number>(0)
  const lastPlayTime = shallowRef<number>(0)
  const waitedAt = shallowRef<number | null>(null)
  const waitTimeSum = shallowRef<number>(0)
  const { currentEpisodeId } = useSeriesItem(seriesId)
  onMounted(() => {
    lastPlayTime.value = Date.now()
    timer.value = window.setInterval(() => {
      if (pausedAt.value === null && currentEpisodeId.value === episodeId) {
        playTimeSum.value += Date.now() - lastPlayTime.value
        lastPlayTime.value = Date.now()
      } else {
        lastPlayTime.value = Date.now()
      }
      if (waitedAt.value !== null && currentEpisodeId.value === episodeId) {
        waitTimeSum.value += Date.now() - waitedAt.value
        waitedAt.value = Date.now()
      }
    }, 100)
  })
  onBeforeUnmount(() => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
  })
  watch(() => playTimeSum.value, t => {
    if (t > 5000) {
      playTimeSum.value = t - 5000
      onPlayOngoing5Seconds?.()
    }
  })
  watch(() => waitTimeSum.value, t => {
    if (t > 2000) {
      waitTimeSum.value = t - 2000
      onWaitOngoing2Seconds?.()
    }
  })

  return {
    timer,
    pausedAt,
    waitedAt,
    playTimeSum,
    waitTimeSum,
  }
}
