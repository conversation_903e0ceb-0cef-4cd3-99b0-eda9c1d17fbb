/* eslint-disable @typescript-eslint/no-explicit-any */
import { get_k_device_hash_from_cookie, get_k_sso_token } from './device-id.ts'
import { computed } from 'vue'
import type {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  CreateAxiosDefaults,
  InternalAxiosRequestConfig,
} from 'axios'
import axios from 'axios'
import { openedInDev, tryParseJson, when } from '@skynet/shared'
import { appName } from './constants.ts'
import { get_authorization, get_authorization_for_browser } from './authorization.ts'
import { isIOS } from '@vueuse/core'
import Cookies from 'js-cookie'
import { jsBridge } from './jsbridge.ts'
import { useLocale } from 'src/h5_modules/i18n/i18n.tsx'
import { toRaw } from 'vue'
import { omit } from 'lodash-es'
import { encrypt } from './crypt.ts'

export interface ConstructParams extends CreateAxiosDefaults {
  transformRequest?: (data: any) => any
  /**
   * 用来判断 response 是不是错误
   * @example
   * ```ts
   * isError: (response: AxiosResponse) => response.data.code !== 200
   * ```
   */
  isError?: (response: AxiosResponse) => boolean | Record<string, JsonValue> | undefined
  /**
   * 用来获取业务数据
   * 建议尽量获取全面的数据，不要只获取 data 而忽略 code
   * @example
   * ```ts
   * getData: (response: AxiosResponse) => response.data // 不推荐 response.data.data
   * ```
   */
  getData?: <T>(response: AxiosResponse<T>) => T
  /**
   * 请求拦截器
   */
  requestInterceptor?: [
    ((config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig) | null,
    ((error: unknown) => Promise<unknown>) | null,
  ]
  /**
   * 响应拦截器
   */
  responseInterceptor?: [
    ((response: AxiosResponse) => AxiosResponse) | null,
    ((error: unknown) => Promise<unknown>) | null,
  ]
}
export class HttpClient {
  instance: AxiosInstance
  isError: ConstructParams['isError']
  getData: Exclude<ConstructParams['getData'], undefined> = response => response.data
  requestInterceptor: ConstructParams['requestInterceptor']
  responseInterceptor: ConstructParams['responseInterceptor']
  transformRequest: ConstructParams['transformRequest']
  constructor(params: ConstructParams) {
    const { isError, getData, requestInterceptor, responseInterceptor, transformRequest, ...rest } = params
    this.instance = axios.create(rest)
    this.isError = isError
    this.getData = getData || this.getData
    this.transformRequest = transformRequest
    this.requestInterceptor = requestInterceptor
    this.responseInterceptor = responseInterceptor
    this.intercept()
  }

  private intercept() {
    this.instance.interceptors.response.use((response: AxiosResponse) => {
      const error = this.isError ? this.isError(response) : false
      if (error === true) {
        return Promise.reject({ response })
      }
      if (error instanceof Object) {
        return Promise.reject(error)
      }
      return response
    })
    if (this.requestInterceptor) {
      this.instance.interceptors.request.use(...this.requestInterceptor)
    }
    if (this.responseInterceptor) {
      this.instance.interceptors.response.use(...this.responseInterceptor)
    }
  }

  /**
   * 发送 GET 请求
   * @param url 请求地址
   * @param params 查询参数
   * @param config 请求配置
   * @returns 请求结果
   * @example
   * ```ts
   * httpClient.get<ServerResponse<User>>("/api/user", { id: 1 })
   * ```
   */
  get<T = unknown>(
    url: string,
    params?: AxiosRequestConfig['params'],
    config = {} as Omit<AxiosRequestConfig, 'params'>,
  ) {
    return this._request<T>({ ...config, method: 'GET', url, params })
  }

  /**
   * 发送 POST 请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns 请求结果
   * @example
   * ```ts
   * httpClient.post<ServerResponse<User>>("/api/user", { id: 1 })
   * ```
   */
  post<T = unknown>(url: string, data?: any, config: AxiosRequestConfig = {}) {
    return this._request<T>({ ...config, method: 'POST', url, data })
  }

  /**
   * 发送 PUT 请求
   */
  put<T = unknown>(url: string, data?: JsonValue, config: AxiosRequestConfig = {}) {
    return this._request<T>({ ...config, method: 'PUT', url, data })
  }

  /**
   * 发送 DELETE 请求
   */
  delete<T = unknown>(
    url: string,
    params?: AxiosRequestConfig['params'],
    config = {} as Omit<AxiosRequestConfig, 'params'>,
  ) {
    return this._request<T>({ ...config, method: 'DELETE', url, params })
  }

  _request<T = unknown>(_config: AxiosRequestConfig) {
    const config = {
      transformRequest: this.transformRequest,
      ..._config,
    }
    if (jsBridge.inDramawaveApp) {
      return when(() => Cookies.get('headers')).then(() => {
        return this.instance.request<T>({ ...config,
          headers: {
            ...tryParseJson(Cookies.get('headers'), {}),
            authorization: get_authorization(),
          },
        }).then(this.getData)
      })
    } else {
      return this.instance.request<T>(config).then(this.getData)
    }
  }
}

const commonRequestInterceptor: HttpClient['requestInterceptor'] = [
  config => {
    Object.assign(config.headers, {
      'device-hash': get_k_device_hash_from_cookie(),
      device: isIOS ? 'ios' : 'android',
      'k-sso-token': get_k_sso_token() || (openedInDev ? import.meta.env.VITE_DEBUG_TOKEN : ''),
      'k-client-id': import.meta.env.VITE_CLIENT_ID,
      authorization: get_authorization(),
    })

    if (openedInDev) {
      // 这个是在浏览器端模拟客户端打开H5页面的请求头
      Object.assign(config.headers, {
        'app-name': 'com.dramawave.app', // 'com.dramawave.app',
        'app-version': '1.2.20',
        country: 'id',
        device: 'android',
        'device-hash': 'ef83295220c2a4f0145a6342850e2d54',
        'device-id': '5d354bb5-b0a5-4975-b83a-c5f5ca887675',
        language: 'in-ID',
        authorization: 'oauth_signature=67a990102e965de5e5068c40bd35ecf4,oauth_token=OkbDyrsm2vD0j1msLlUuL8FUmnFusmvR,ts=1741169812612',
      })
    }

    if (config.data && config.data.page_info) {
      config.data = {
        ...config.data,
        page_info: {
          ...config.data.page_info,
          next: config.data.page_info.offset.toString(),
        },
      }
    }
    return config
  },
  null,
]

const { currentLocale } = useLocale()

export const commonParams: ConstructParams = {
  baseURL: appName === 'dramawave' ? import.meta.env.VITE_DRAMAWAVE_API_URL : import.meta.env.VITE_DRAMABUZZ_API_URL,
  timeout: 20000,
  withCredentials: true,
  isError: response => ![200, 404, 0].includes(response.data.code),
  getData: response => response.data,
  requestInterceptor: commonRequestInterceptor,
}

// 这里不 omit name 会导致 axios 报错
const localeObject = computed(() => omit(currentLocale.value, 'name'))
// web H5配置写这里
const h5CommonParams: ConstructParams = {
  ...commonParams,
  requestInterceptor: [
    config => {
      Object.assign(config.headers, {
        'app-name': 'com.dramawave.h5', // 'com.dramawave.H5', //  'com.dramawave.H5'
        'app-version': '1.2.20',
        'device-hash': get_k_device_hash_from_cookie(),
        'device-id': get_k_device_hash_from_cookie(),
        authorization: get_authorization_for_browser(),
        device: 'h5', // 'h5',
        'Skip-Encrypt': 1,
        ...localeObject.value,
      })
      return config
    },
    null,
  ],
}

const k = '2r36789f45q01ae5'

// web H5配置写这里
const encryptedCommonParams: ConstructParams = {
  ...commonParams,
  requestInterceptor: [
    config => {
      if (config.data) {
        config.data = encrypt(JSON.stringify(config.data), k)
        config.headers['Content-Type'] = 'application/json'
      }
      Object.assign(config.headers, {
        'app-name': 'com.dramawave.h5', // 'com.dramawave.H5', //  'com.dramawave.H5'
        'app-version': '1.2.20',
        'device-hash': get_k_device_hash_from_cookie(),
        'device-id': get_k_device_hash_from_cookie(),
        authorization: get_authorization_for_browser(),
        device: 'h5', // 'h5',
        'Skip-Encrypt': undefined,
        ...localeObject.value,
      })
      return config
    },
    null,
  ],
}

/**
 * 如果以下配置不满足你的需求，你可以修改，或者创建其他 httpClient
 */
export const httpClient = new HttpClient(commonParams)
export const h5HttpClient = new HttpClient(h5CommonParams)
export const encryptedHttpClientV2 = new HttpClient(encryptedCommonParams)

export const keepError = (fn?: (err: unknown) => void) => (err: unknown) => {
  fn?.(err)
  throw err
}
