/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc, useValidator } from '@skynet/shared'
import { Button, Checkbox, CreateForm, CreateTableOld, openDialog, Pager, showFailToast, showSuccessToast, TableColumnOld, transformNumber, transformNumber2 } from '@skynet/ui'
import { Icon } from '@skynet/ui/icon/icon'
import { AxiosError } from 'axios'
import { cloneDeep, get, omit, set } from 'lodash-es'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { memberAttr, periodList, platformList } from 'src/lib/constant.ts'
import { requiredLabel } from 'src/lib/required-label'
import { apiGetAppOptions } from 'src/modules/application/application.api'
import { nextTick, onMounted, ref } from 'vue'
import { z } from 'zod'
import RechargeLevelPage from '../recharge-level/recharge-level-page'
import { apiCreateMember, apiGetMemberList } from './member.api'

type MemberPageOptions = {
  props: {
    hasActions?: boolean
    hasCheckItem?: boolean
    checkedItem?: M.MemberLevel[]
    hasNav?: boolean
    hasPriority?: boolean
    platform?: number
    appIdSelectDisabled?: boolean
  }
  emits: {
    // hide: () => void
    add: (id: M.MemberLevel) => void
    remove: (id: M.MemberLevel) => void
  }
}

export const rechargeItemPage = createComponent<MemberPageOptions>({
  props: {
    hasActions: true,
    hasCheckItem: false,
    checkedItem: [],
    hasNav: true,
    hasPriority: true,
    platform: 1,
    appIdSelectDisabled: false,
  },
  emits: {
    add: (item: M.MemberLevel) => {},
    remove: (item: M.MemberLevel) => {},
  },
}, (props, { emit }) => {
  const dialogMainClass = 'max-h-[80vh] overflow-y-auto flex flex-col flex-auto pb-0'
  const numbers = Array.from({ length: 12 }, (_, index) => ({
    value: index + 1,
    label: String(index + 1),
  }))

  const rules = z.object({
    app_id: z.number().min(1, '请选择生效应用'),
    package_type: z.number().min(1, '请选择道具类型'),
    description: z.string().min(1, '请输入道具说明'),
    unlock_num: z.number().min(1, '请输入解锁集数'),
    discount_rate: z.number().min(1, '请输入道具基础折扣'),
    package_description: z.string().min(1, '请输入商品描述'),
    show_floating_box_episode: z.number().min(1, '第几集开始展示多集解锁礼包'),
    // episode_products: z.array(z.object({
    //   price_gt: z.number().min(0, '请输入价格'),
    //   price_eq_or_lt: z.number().min(0, '请输入价格'),
    //   product_id: z.number().min(0, '请选择充值档位'),
    // })).min(1, '请填写定价'),
  })
  const memberAttrRef = ref(memberAttr)
  const QueryForm = CreateForm<M.QueryMemberLevel>()
  const Form = CreateForm<M.MemberLevel>()
  const Table = CreateTableOld<M.MemberLevel>()
  const btnLoading = ref(false)
  const list = ref<M.MemberLevel[]>([])
  const tableLoading = ref(false)
  const appList = ref<{ value: number, label: string, platform: string }[]>([])
  const total = ref(0)
  const defaultData = {
    id: 0,
    description: '',
    package_type: 1,
    unlock_num: 0,
    discount_rate: 0,
    product_ids: [],
    default_product_id: 0,
    app_name: '',
    status: 0,
    operator_name: '',
    episode_products: [{ price_gt: undefined, price_eq_or_lt: undefined, default_products: [] }],
    show_in_payment_panel: 1,
    show_in_unlock: 0,
  }
  const queryForm = ref<M.QueryMemberLevel>({
    page_size: 100,
    next: '',
    has_more: true,
    app_id: undefined,
    title: '',
    store: '',
    status: 0,
    membership_type: '',
  })
  const form = ref<M.MemberLevel>(cloneDeep(defaultData))
  const dialogRef = ref(() => {})

  const pageTypeList = [
    { label: '解锁礼包', value: 1 },
    { label: '整剧打包解锁', value: 5 },
  ]

  const { error, validateAll } = useValidator(form, rules)
  const onPageSizeChange = (page_size: number) => {
    queryForm.value.next = ''
    queryForm.value.page_size = page_size
    void onQuery()
  }
  const onPageChange = (page: number) => {
    queryForm.value.page = page
    queryForm.value.next = page - 1 > 0 ? `${(page - 1) * queryForm.value.page_size}` : ''

    void getList()
  }

  const getPeriodName = (row: M.MemberLevel) => {
    return periodList.find(item => item.value === row?.delivery_details?.period)?.label
  }

  const onReset = () => {
    queryForm.value = {
      app_id: queryForm.value.app_id || appList.value[0].value,
      page_size: 100,
      next: '',
      has_more: true,
      title: '',
      store: '',
      status: 0,
    }
    void getList()
  }

  const onClose = () => {
    form.value = cloneDeep(defaultData)
    dialogRef.value()
  }

  const onQuery = async () => {
    queryForm.value.page = 1
    queryForm.value.next = ''
    queryForm.value.has_more = true
    await getList()
  }
  // 价格数据对比
  const processArray = (arr: any) => {
    return arr.every((obj: any) => obj.price_gt < obj.price_eq_or_lt)
  }
  // 数据完整校验
  const judgeArray = (arr: any) => {
    return arr.every((obj: any) => {
      return obj.price_gt !== undefined && obj.price_gt !== ''
        && obj.price_eq_or_lt !== undefined && obj.price_eq_or_lt !== ''
        && obj.product_id !== undefined && obj.product_id !== ''
    })
  }
  // 数据商品sku值大小校验
  const judgeSkuArray = (arr: any) => {
    return arr.every((obj: any) => obj.price_eq_or_lt <= obj.default_products[0]?.delivery_details.quanity + (obj.default_products[0]?.delivery_details.bonus ? obj.default_products[0]?.delivery_details.bonus : 0))
  }
  const onSave = async () => {
    const exclude: string[] = []
    if (form.value.package_type == 1) {
      exclude.push('package_description')
      exclude.push('show_floating_box_episode')
    }
    if (form.value.package_type == 5) {
      exclude.push('unlock_num')
    }
    if (validateAll({ exclude: exclude })) {
      if (form.value.package_type == 5) { // 判断商品信息是否完整；及最小值最大值
        if (!judgeArray(form.value.episode_products)) {
          showFailToast('请将商品信息配置完整')
          return
        }
        if (!processArray(form.value.episode_products)) {
          showFailToast('商品最小值禁止大于最大值，请修改后再提交～')
          return
        }
        if (!judgeSkuArray(form.value.episode_products)) {
          showFailToast('导入的商品sku金币值应大于等于本行的最大值，请修改后再提交～')
          return
        }
      }
      if (checkedRechargeItem.value.length === 0) {
        showFailToast('请选择默认商品')
        return
      }
      try {
        let params: M.MemberLevel = {}
        if (form.value.package_type == 1) {
          form.value.default_product_id = checkedRechargeItem.value[0]?.id
          form.value.product_ids = checkedMoreRechargeItem.value.map(item => item.id)
          params = omit(form.value, ['package_description', 'show_floating_box_episode', 'episode_products', 'show_in_payment_panel', 'show_in_unlock'])
        }
        if (form.value.package_type == 5) {
          params = omit(form.value, ['unlock_num', 'default_product_id', 'product_ids'])
        }
        btnLoading.value = true
        const res = await apiCreateMember(params)
        if (res.code === 200) {
          showSuccessToast('操作成功！')
        } else {
          showFailToast(res?.message || '服务忙碌，稍后再试')
        }
        await onQuery()
      } catch (e) {
        const error = e as AxiosError
        showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
      } finally {
        btnLoading.value = false
      }
    } else {
      btnLoading.value = false
      console.log(error, 'error')
      return
    }
    onClose()
  }

  const showCreateDialog = () => {
    dialogRef.value = openDialog({
      mainClass: dialogMainClass,
      // beforeClose: () => { onClose() },
      title: () => <div>{ form.value.id ? '编辑道具档位' : '新建道具档位' }</div>,
      body: () => (
        <>
          <div class="flex flex-1 flex-col gap-y-[25px] overflow-y-auto px-[20px]">
            <Form
              class="flex w-full flex-col flex-nowrap"
              data={form.value}
              onChange={(path, value) => {
                if (path === 'app_id') {
                  form.value.app_name = appList.value.find(row => row.value === value)?.label
                }
                if (path === 'package_type') {
                  set(form.value, path, value)
                  if (form.value.package_type == 5) {
                    checkedRechargeItem.value = []
                    checkedMoreRechargeItem.value = []
                    if (form.value.episode_products == undefined) {
                      form.value.episode_products = [{ price_gt: undefined, price_eq_or_lt: undefined, default_products: [] }]
                      set(form.value, 'show_in_payment_panel', 1)
                    }
                  }
                }
                set(form.value, path, value)
              }}
              hasAction={false}
              error={error.value}
              items={[
                <h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">道具配置</h1>,
                { label: requiredLabel('生效应用'),
                  path: 'app_id',
                  input: {
                    type: 'select',
                    autoInsertEmptyOption: false,
                    options: appList.value,
                  },
                  transform: transformNumber,
                },
                { label: requiredLabel('道具类型'),
                  path: 'package_type',
                  input: {
                    type: 'select',
                    autoInsertEmptyOption: false,
                    options: pageTypeList,
                  },
                  transform: transformNumber,
                },
                { label: requiredLabel('道具说明'), path: 'description', input: { type: 'text' } },

                form.value.package_type === 1 && { label: requiredLabel('解锁集数'), path: 'unlock_num', input: { type: 'text', suffix: <>集</> }, transform: transformNumber, hint: '输入999表示解锁整部剧' },
                form.value.package_type === 1 && { label: requiredLabel('道具基础折扣'), path: 'discount_rate', input: { type: 'text', suffix: <>%</> }, transform: transformNumber, hint: '每集解锁的优惠额度。例如：20%的折扣，显示为80% OFF' },

                form.value.package_type === 1 && <h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">关联商品信息</h1>,
                form.value.package_type === 1 && <x-bot-tip class="mt-2 text-[12px] text-gray-500">请运营人员注意估算单集金币价格，配置的充值金币不低于（单集价格*解锁集数*礼包折扣）；更多充值档位作为用户备选，建议配置比默认兑换金币数等多的档位</x-bot-tip>,

                form.value.package_type === 1 && <h1 class={mc('font-medium border-l-[2px] border-l-solid border-l-primary pl-[10px]', form.value.first_recharge === 1 ? '' : 'hidden')}>折扣信息</h1>,
                form.value.package_type === 1 && {
                  label: requiredLabel('折扣价'),
                  path: 'discount_price',
                  input: {
                    type: 'number',
                    suffix: <>元</>,
                    min: '0',
                    step: '0.01',
                    placeholder: '支持小数点后两位',
                    // disabled: !!form.value.id,
                  },
                  transform: transformNumber2,
                  class: form.value.first_recharge === 1 ? '' : 'hidden',
                },
                form.value.package_type === 1 && { label: requiredLabel('折扣说明'), path: 'discount_desc', input: { type: 'text' }, class: form.value.first_recharge === 1 ? '' : 'hidden' },
                form.value.package_type === 1 && <h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">档位配置</h1>,

                form.value.package_type === 1 && <x-title class="flex items-center justify-between"><x-box><span class="text-red-500">*</span>默认充值档位 <span class="text-[12px] text-gray-500">仅1个</span></x-box> <span class="cursor-pointer text-sm text-blue-400" onClick={() => { showImportRechargeLevelDialog(1, -1) }}>导入</span></x-title>,

                <x-level-box class={mc('border-1 border-primary mt-2 border-solid pl-[10px]', form.value.package_type === 1 ? '' : 'hidden')}>
                  <x-span-box class="flex items-center justify-between">
                    <x-span>
                      {checkedRechargeItem.value.length > 0 && `${checkedRechargeItem.value[0]?.id}/${checkedRechargeItem.value[0]?.price / 100}/${checkedRechargeItem.value[0]?.delivery_details.quanity}/${checkedRechargeItem.value[0]?.delivery_details.bonus}`}</x-span>

                    {checkedRechargeItem.value.length > 0 && (
                      <span class="cursor-pointer text-sm text-blue-400" onClick={() => {
                        checkedRechargeItem.value = checkedRechargeItem.value?.filter(i => i.id !== checkedRechargeItem.value[0]?.id)
                      }}>删除</span>
                    )}
                  </x-span-box>
                </x-level-box>,
                form.value.package_type === 1 && <x-title class="flex items-center justify-between"><x-box>更多充值档位 <span class="text-[12px] text-gray-500">最多3个</span></x-box> <span class="cursor-pointer text-sm text-blue-400" onClick={() => { showImportRechargeLevelDialog(2, -1) }}>{checkedMoreRechargeItem.value.length > 0 ? '继续导入' : '导入'}</span></x-title>,

                <x-level-box class={mc('border-1 border-primary mt-2 border-solid pl-[10px]', form.value.package_type === 1 ? '' : 'hidden')}>
                  {checkedMoreRechargeItem.value.map(item => (
                    <x-span-box class="flex items-center justify-between">
                      <x-span>{item.id}/{item.price! / 100}/{item.delivery_details?.quanity}/{item.delivery_details?.bonus}</x-span>
                      <span class="cursor-pointer text-sm text-blue-400" onClick={() => {
                        checkedMoreRechargeItem.value = checkedMoreRechargeItem.value?.filter(i => i.id !== item.id)
                      }}>删除</span>
                    </x-span-box>
                  ))}
                </x-level-box>,
                // 整集解锁
                form.value.package_type === 5 && { label: requiredLabel('剧集折扣'), path: 'discount_rate', input: { type: 'text', placeholder: '正整数', suffix: <>%</> }, transform: transformNumber, hint: '每集解锁的优惠额度。例如：20%的折扣，显示为80% OFF' },
                form.value.package_type === 5 && [<div class="flex flex-col">{requiredLabel('悬浮层支付入口')}<span class="block p-2 text-xs text-gray-400">请注意估算好集数，悬浮层购买和支付面板不会同时展示</span></div>, 'ad_id', {
                  type: 'custom',
                  render: (r: any) => {
                    return (
                      <div class="flex items-center pl-2">
                        <span>从第几集开始展示</span>
                        <input placeholder="正整数" class="input input-bordered ml-2 flex h-8 items-center gap-1" type="number" value={form.value.show_floating_box_episode} onInput={(e: Event) => {
                          form.value.show_floating_box_episode = Number((e.target as HTMLInputElement).value) || 0
                        }} />
                      </div>
                    )
                  } },
                ],
                form.value.package_type === 5 && <h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">展示控制</h1>,
                form.value.package_type === 5 && {
                  label: requiredLabel('是否同时在【支付面板】展示'),
                  path: 'show_in_payment_panel',
                  input: {
                    type: 'radio',
                    options: [
                      { value: 1, label: '是' },
                      { value: 0, label: '否' },
                    ],
                  },
                },
                form.value.package_type === 5 && {
                  label: requiredLabel('是否在【锁定蒙层前】弹出挽留'),
                  path: 'show_in_unlock',
                  input: {
                    type: 'radio',
                    options: [
                      { value: 1, label: '是' },
                      { value: 0, label: '否' },
                    ],
                  },
                },

                form.value.package_type === 5 && { label: requiredLabel('商品描述'), path: 'package_description', input: { type: 'text', placeholder: '下发到客户端' } },
                form.value.package_type === 5 && <h1 class="border-l-solid border-l-primary border-l-2 pl-[10px] font-medium">商品信息配置</h1>,
                form.value.package_type === 5 && (
                  <x-bot-tip class="mt-1 text-[12px] text-gray-500">配置时，请注意估算单集金币价格 =（单集均价*解锁集数*礼包折扣）；<br />
                    一个行对应一个充值档位，不可为空；至少要配一行；数值，前开后包，前面必须小于后面；新建会自动带入前一行的最小值（作为最大值）。</x-bot-tip>
                ),
                form.value.package_type === 5 && ['', 'episode_products', {
                  type: 'custom',
                  render: (r: any) => {
                    return (
                      <div class="">
                        <div class="mb-2 flex justify-between">
                          <div class="w-[50%]">折扣后，总价范围</div>
                          <div class="w-[50%]">档位（充值商品）</div>
                        </div>
                        {(r.value || [])
                          .map((i: any, idx: number) => (
                            <div class="mb-3 flex justify-between">
                              <div class="flex w-[50%] items-center">
                                <input placeholder="最小值" class="input input-bordered mr-2 flex h-8 w-20 items-center gap-1 px-1 text-sm" type="number" value={i.price_gt} onInput={(e: Event) => {
                                  const data = Number((e.target as HTMLInputElement).value || '')
                                  set(form.value, `episode_products[${idx}].price_gt`, data)
                                  if (form.value.episode_products!.length > idx + 1) {
                                    set(form.value, `episode_products[${idx + 1}].price_eq_or_lt`, data)
                                  }
                                }} />
                                <span>---</span>
                                <input placeholder="最大值" class="input input-bordered ml-2 flex h-8 w-20 items-center gap-1 px-1 text-sm" type="number" disabled={idx > 0 ? true : false} value={i.price_eq_or_lt} onInput={(e: Event) => {
                                  const data = Number((e.target as HTMLInputElement).value || '')
                                  set(form.value, `episode_products[${idx}].price_eq_or_lt`, data)
                                }} />
                              </div>
                              <div class="flex w-[50%] items-center">
                                <x-level-box class={mc('border-1 border-primary border-solid pl-[10px]')}>
                                  <x-span-box class="flex items-center justify-between">
                                    <x-span>
                                      {form.value.episode_products![idx].default_products!.length > 0 && `${form.value.episode_products[idx].default_products[0]?.id} / $${form.value.episode_products[idx].default_products[0]?.price / 100} / ${form.value.episode_products[idx].default_products[0]?.delivery_details.quanity} + ${form.value.episode_products[idx].default_products[0]?.delivery_details.bonus ? form.value.episode_products[idx].default_products[0]?.delivery_details.bonus : 0}`}</x-span>
                                  </x-span-box>
                                </x-level-box>
                                <span class="ml-2 cursor-pointer text-sm text-blue-400" onClick={() => { showImportRechargeLevelDialog(1, idx) }}>{form.value.episode_products[idx].default_products.length > 0 ? '更新' : '导入'}</span>
                              </div>
                            </div>
                          ))}
                        <div class="mt-2 flex items-center">
                          <Button class="btn btn-primary btn-sm w-14" onClick={() => {
                            if (!form.value?.episode_products) {
                              form.value.episode_products = []
                            }
                            form.value.episode_products.push({
                              price_gt: undefined,
                              price_eq_or_lt: form.value.episode_products.length > 0 ? form.value.episode_products[form.value.episode_products.length - 1].price_gt : undefined,
                              default_products: [],
                            })
                          }}
                          >
                            +
                          </Button>
                          <div class={mc('ml-2 cursor-pointer text-sm text-blue-400', form.value.episode_products!.length > 1 ? '' : 'hidden')} onClick={() => {
                            form.value.episode_products?.pop()
                          }}>删除最后一行</div>
                        </div>
                        <div class="mt-2 text-[12px] text-red-500">*数值，前开后包，请输入折扣后的总价。</div>

                      </div>
                    )
                  } },
                ],

              ] as any}
            />
          </div>
          <div class="flex justify-end gap-x-2 px-[20px] pt-4">
            <Button disabled={btnLoading.value} class="btn btn-primary btn-sm" onClick={() => {
              // eslint-disable-next-line @typescript-eslint/no-floating-promises
              onSave()
            }}
            >
              {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="size-5" /> : null}
              确定
            </Button>
          </div>
        </>
      ),
    })
  }

  const checkedRechargeItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const precheckRechargeItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const checkedMoreRechargeItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const precheckMoreRechargeItem = ref<M.NSStrategyGroup.LevelOption[]>([])
  const showImportRechargeLevelDialog = (importType: number, package_type_index: number) => {
    if (package_type_index > -1 && form.value.episode_products![package_type_index].default_products) {
      checkedRechargeItem.value = form.value.episode_products![package_type_index].default_products
    }
    if (checkedRechargeItem.value.length > 0) {
      precheckRechargeItem.value = [...checkedRechargeItem.value]
    } else {
      precheckRechargeItem.value = []
    }
    if (checkedMoreRechargeItem.value.length > 0) {
      precheckMoreRechargeItem.value = [...checkedMoreRechargeItem.value]
    } else {
      precheckMoreRechargeItem.value = []
    }
    // checkedRechargeItem.value = cloneDeep(precheckRechargeItem.value)
    const hide = openDialog({
      title: '导入充值档位方案',
      mainClass: 'pb-0 px-5',
      body: () => (
        <x-import-recharge-level class="relative">
          <RechargeLevelPage
            hasNav={false}
            hasActions={false}
            hasCheckItem
            hasPriority={false}
            appIdSelectDisabled
            platform={form.value.app_id === 33 || form.value.app_id === 3 ? 1 : 2}
            checkedItem={importType == 1 ? precheckRechargeItem.value : precheckMoreRechargeItem.value}
            onAdd={item => {
              if (importType == 1) {
                if (package_type_index > -1) {
                  precheckRechargeItem.value = [{ ...item, product_id: item?.id }]
                } else {
                  if (precheckRechargeItem.value.length < 1) {
                    precheckRechargeItem.value?.push({ ...item, product_id: item?.id?.toString() } as M.NSStrategyGroup.LevelOption)
                  } else {
                    showFailToast('最多选择一个充值档位')
                  }
                }
              } else {
                if (precheckMoreRechargeItem.value.length < 3) {
                  precheckMoreRechargeItem.value?.push({ ...item, product_id: item?.id?.toString() } as M.NSStrategyGroup.LevelOption)
                } else {
                  showFailToast('最多选择三个充值档位')
                }
              }
            }}
            onRemove={item => {
              if (importType == 1) {
                precheckRechargeItem.value = precheckRechargeItem.value?.filter(i => i.id !== item.id)
              } else {
                precheckMoreRechargeItem.value = precheckMoreRechargeItem.value?.filter(i => i.id !== item.id)
              }
            }}
          />
          <footer class="sticky bottom-0 left-0 flex w-full justify-end gap-x-2 border-t border-gray-200 bg-white pt-4">
            <Button class="btn btn-ghost btn-sm" onClick={() => hide()}>取消</Button>
            <Button class="btn btn-primary btn-sm" onClick={() => {
              if (importType == 1) {
                if (precheckRechargeItem.value.length > 1) {
                  showFailToast('最多选择一个充值档位')
                  return
                }
                form.value.one_product = precheckRechargeItem.value
                checkedRechargeItem.value = cloneDeep(precheckRechargeItem.value)
                if (package_type_index > -1) {
                  form.value.episode_products[package_type_index].default_products = cloneDeep(precheckRechargeItem.value)
                  form.value.episode_products[package_type_index].product_id = precheckRechargeItem.value[0].product_id
                }
              }
              if (importType == 2) {
                if (precheckMoreRechargeItem.value.length > 3) {
                  showFailToast('最多选择三个充值档位')
                  return
                }
                form.value.more_products = precheckMoreRechargeItem.value
                checkedMoreRechargeItem.value = cloneDeep(precheckMoreRechargeItem.value)
              }
              hide()
            }}
            >确定
            </Button>
          </footer>
        </x-import-recharge-level>

      ),
      customClass: '!w-800px',
      beforeClose: () => {
        precheckRechargeItem.value = []
        precheckMoreRechargeItem.value = []
      },
    })
  }

  const columns: TableColumnOld<M.MemberLevel>[] = [
    [
      '',
      row => {
        const id = row.id as number
        return (
          <Checkbox
            label=""
            disabled={!!!id}
            modelValue={props.checkedItem.map(i => i.id).includes(id)}
            onUpdate:modelValue={(value: unknown) => {
              if (value) {
                if (!props.checkedItem.map(i => i.id).includes(id)) emit('add', row)
              } else {
                const rowIndex = props.checkedItem.findIndex(i => i.id === id)
                if (rowIndex !== -1) {
                  emit('remove', row)
                }
              }
            }}
          />
        )
      },
      { class: mc('w-[60px]', props.hasCheckItem ? '' : 'hidden') },
    ],
    ['道具ID', 'id', { class: 'w-[70px]' }],
    ['道具说明', 'description', { class: 'w-[200px]' }],
    ['类型', row => {
      return pageTypeList.find(item => item.value === row.package_type)?.label
    }, { class: 'w-[160px]' }],
    ['解锁集数', row => {
      return row.package_type == 1 ? row.unlock_num : '全剧'
    }, { class: 'w-[100px]' }],
    ['折扣', row => {
      return row.discount_rate + '%'
    }, { class: 'w-[100px]' }],
    ['默认充值档位（档位ID/价格/金币+赠送）', row => {
      return row.package_type == 1 ? row.default_product && row.default_product.id + ' / $' + (row.default_product.price / 100).toFixed(2) + ' / ' + row.default_product.delivery_details.quanity + ' + ' + (row.default_product.delivery_details.bonus || 0) : row.product_items.length && row.product_items[0].id + ' / $' + (row.product_items[0].price / 100).toFixed(2) + ' / ' + row.product_items[0].delivery_details.quanity + ' + ' + (row.product_items[0].delivery_details.bonus || 0)
    }, { class: 'w-[230px]' }],
    ['更多充值档位（数）', row => {
      return row.package_type == 1 ? row.product_ids?.length : row.episode_products!.length
    }, { class: 'w-[160px]' }],
    ['应用名称', row => {
      return appList.value.find(item => item.value === row.app_id)?.label
    }, { class: 'w-[160px]' }],

    [
      '修改时间',
      row => row.update_time,
      { class: 'w-[180px]' },
    ],
    ['修改人', 'updated_operator_name', { class: 'w-[160px]' }],

    [
      <span class="px-3">操作</span>,
      row => {
        return (
          <div class="flex flex-nowrap">
            <Button class="btn btn-link btn-primary btn-sm" onClick={() => {
              const editRow = cloneDeep(row)
              if (editRow.package_type == 1) {
                checkedRechargeItem.value = editRow.default_product ? [editRow.default_product] : []
                checkedMoreRechargeItem.value = editRow.product_items ? [...editRow.product_items] : []
              } else {
                editRow.episode_products?.map((i, index) => {
                  i.default_products = [editRow.product_items![index]]
                })
                checkedRechargeItem.value = editRow.product_items![0] ? [editRow.product_items![0]] : []
              }
              form.value = editRow
              console.log(form.value)
              showCreateDialog()
              validateAll()
            }}
            >
              编辑
            </Button>

          </div>
        )
      },
      { class: mc('w-[100px]') },
    ],
  ]

  const getList = async () => {
    try {
      tableLoading.value = true
      const res = await apiGetMemberList({
        ...queryForm.value,
        app_id: queryForm.value.app_id,
      })
      list.value = res.data?.list as unknown as M.MemberLevel[] || []
      total.value = res.data?.total || 0
    } catch (e) {
      const error = e as AxiosError
      showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    } finally {
      tableLoading.value = false
    }
  }

  const getAppList = async () => {
    try {
      const res = await apiGetAppOptions({
        app_name: '',
      })
      if (!res.data) return
      appList.value = res.data.list?.filter(i => i.app_name.includes('Drama') && !i.app_name.includes('H5')).map(row => {
        return {
          value: Number(row.id),
          label: row.app_name,
          platform: platformList.find(item => item.value === row.platform)?.label || '',
        }
      }) || []
      await nextTick()
      // queryForm.value.app_id = appList.value[0].value
      queryForm.value.app_id = appList.value.filter(i => (i.platform === 'iOS' && props.platform === 1) || (i.platform !== 'iOS' && props.platform === 2))[0]?.value
      // set(searchForm.value, 'store', props.platform === 1 ? 'Apple Store' : 'Google Play')
    } catch (e) {
      const error = e as AxiosError
      showFailToast(get(error, 'response.data.message') || error.message || '服务忙碌，稍后再试')
    }
  }

  onMounted(async () => {
    await getAppList()
    await getList()
  })

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: props.hasNav
          ? () => (
              <ul>
                <li>充值道具管理</li>
              </ul>
            )
          : null,
        form: () => (
          <QueryForm class="flex w-full flex-row"
            onSubmit={onQuery}
            onReset={onReset}
            data={queryForm.value}
            onChange={(path, value) => {
              set(queryForm.value, path, value)
            }}
            items={[
              { label: () => (
                <div>应用：</div>
              ), path: 'app_id', input: { type: 'select', options: appList.value, autoInsertEmptyOption: false,
                disabled: props.appIdSelectDisabled,
              }, transform: transformNumber },
              { label: '道具ID', path: 'id', input: { type: 'number' }, transform: transformNumber },

            ]}
          />
        ),
        tableActions: () => (
          props.hasActions
            ? (
                <x-table-actions class="flex items-center gap-4">
                  <span class="mr-auto">道具列表</span>
                  <Button class="btn btn-primary btn-sm" onClick={() => {
                    form.value = cloneDeep(defaultData)
                    form.value.app_id = queryForm.value.app_id
                    checkedRechargeItem.value = []
                    checkedMoreRechargeItem.value = []
                    showCreateDialog()
                  }}
                  >新建解锁道具
                  </Button>
                </x-table-actions>
              )
            : null
        ),
        table: () => (
          <Table
            list={list.value || []}
            class="tm-table-fix-last-column"
            columns={columns}
            loading={tableLoading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={queryForm.value.page} v-model:size={queryForm.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default rechargeItemPage
