declare namespace M {

  interface IResourcePublishQueryParams {
    title?: string
    id?: number
    partner_name?: string
    create_start_time?: number // 录入开始时间
    create_end_time?: number // 录入结束时间
    publish_status?: -1 | 0 | 1 | 2 | 3 // -1 全部 0 待发布 1 发布中 2 发布失败 3 发布完成
    check_status?: -1 | 0 | 1 | 2 | 3 | 4 // -1 未保存 1 待审核 2 审核不通过 3 审核通过 4 字幕校验通过
    series_key_list?: string
    business_principal?: string
    netdisk_status?: 0 | 1 | 2 | 3 | 4 | 5 | 6
    claim_names?: string | string[]
    task_status: 0 | 1 | 2
    audit_status_list: number[]
    entry_user?: string
    recycle_status_list?: number[]
    release_round?: 1 | 2
    has_actor_info?: 0 | 1 | 2 // 有无演员信息 0 全部 1 有 2 无
    series_key_or_title_list: string | string[]
    resource_id_or_title_list: string | string[]
    has_en_cover?: 0 | 1 | 2 // 0 所有 1 存在 2 不存在
    shelf_status?: 0 | 1 | 2 // 0 全部 1 已上架 2 未上架
  }

  interface IResourcePublishListItem {
    id: number // 资源id
    title: string
    partner_name: string
    check_status: -1 | 0 | 1 | 2 | 3 | 4 // -1 未保存 1 待审核 2 审核不通过 3 审核通过 4 字幕校验通过
    shelf_status: 1 | 2 // 1 已上架 2 未上架
    created: number // 创建时间
    updated: number // 更新时间
    check_reason?: string
    description: string
    auth_start_time: number
    auth_end_time: number
    count: number
    unlocked_episodes: number
    netdisk_url: string
    release_round: number
    resource_type: number
    language_code: string
    free_status: number
    business_principal: string
    preview_status: 1 | 2
    preview_time?: number
    free_start_time?: number
    deploy_time?: number
    online_time?: number
    claim_name: string
    audit_status?: 0 | 1 | 2 | 99 | number
    audit_reason?: string
    audit_to_status: 0 | 1 | 2 | 99 | number
    audit_to_reason: string
    entry_user?: string
    recycle_status?: 4 | 5 | 1
    root_labels: string
    first_labels: string
    second_labels: string
    actor_info_list?: IActor[]
    reject_image_list?: string[]
    external_content_rating?: string
    content_rating?: string
    auth_price?: number
    title2?: string
    title3?: string
  }

  interface IResourceAuditProcessItem {
    // 1 资源验收 2 多语言信息 3 多语言字幕 4 终审发布 5 资源录入
    page_type: 1 | 2 | 3 | 4 | 5 | number
    // 0 待审核 1 审核拒绝 2 审核通过
    status: 0 | 1 | 2 | number
    reject_from_page?: 1 | 2 | 3 | 4 | 5 | number
    reject_reason: string
    auditor: string
    updated: number
    reject_image_list?: string[]
  }

  type IActor = {
    name?: string
    link?: string
  }

  type INewActor = {
    actor_id?: number
    name: string
    home_page?: string
    gender?: -1 | 0 | 1 | 2 | number
  }

  type IRatingDetail = {
    id: string
    category: string
    criteria: string
    score: number
    desc: string
    weight: number
  }

  interface IResourcePublishDetail {
    id?: number
    title?: string
    partner_name?: string
    resource_type_v2?: 1 | 2 | 3 | 4 | 5 // 短剧类型v2 1 翻译剧 2 本土首发 3 本土对投 4 本土二轮采买 5 本土自制
    auth_start_time?: number
    auth_end_time?: number
    free_start_time?: number
    auth_language?: 1 | 2 | 3 // 授权语言 1 简体中文以外的所有 2 简体中文、繁体中文以外的所有 3 其他
    auth_area?: 1 | 2 | 3 | 4 // 授权区域 1 中国大陆以外所有 2 中国大陆及港澳台以外的所有 3 单个区域 4 全球
    sharing_model?: 1 | 2 | 3 | 4 // 分成模式 1 无 2 流水奖励 3 利润分成-50% 4 利润分层-其他
    auth_price?: number // 授权价格
    put_money?: number // 投放金额
    revenue_flow?: number// 收入流水
    estimate_sharing?: number // 预估分层
    business_principal?: string
    online_time?: number
    list?: M.IResourceAssetInfo[]
    count?: number
    unlocked_episodes?: number
    preview_time?: number
    description?: string
    netdisk_url?: string
    content_rating?: 'S' | 'A' | 'B' | 'C' | string // 内容评级 S A B C
    external_content_rating?: 'S' | 'A' | 'B' | 'C' | string // 内容评级 S A B C
    content_classify?: string
    release_round?: 1 | 2 // 发行轮次 1 首发 2二轮
    resource_type?: 1 | 2 // 资源类型 1 本土 2 翻译
    check_status?: -1 | 0 | 1 | 2 | 3 | 4 // -1 未保存 1 待审核 2 审核不通过 3 审核通过 4 字幕校验通过
    // TODO request 和 response 修改
    auth_platform?: number[] | string // 授权平台 1 DramaWave 2 Moboshort 3 Freereel 4 Dramareel 5 Linky
    language_code?: string[] | string
    subtitle_language_code?: string[] | string
    upload_subtitle_language_code?: string[] | string
    check_reason?: string
    vertical_cover?: string
    vertical_cover2?: string
    free_status?: 1 | 2
    deploy_time?: number
    preview_status?: 1 | 2
    business_principal?: string
    supplement_cover?: string
    horizontal_cover?: string
    serialize_status?: number
    psd_cover?: string
    audit_process?: IResourceAuditProcessItem[]
    audit_all_subtitle_process?: boolean
    entry_user?: string
    actor_info_list?: IActor[]
    actors?: INewActor[]
    poster_preview_time?: number
    recycle_status?: 1 | 4 | 5 | number
    root_label_ids?: string | number[]
    first_label_ids?: string | number[]
    second_label_ids?: string | number[]
    pure_cover?: string
    contractor?: string
    marital_status?: string | number[] // 婚姻状态 1 未婚 2 已婚 3 离异 4 单亲
    fertility_status?: string | number[] // 生育状态 1 丁克 2 未育 3 已育
    age?: string | number[] // 年龄 1 12-17岁 2 18-24岁 3 25-34岁 4 35-44岁 5 44岁+
    content_rating?: 'S' | 'A' | 'B' | 'C' | string // 内容评级 S A B C
    vip_type?: number
    vip_everyday_update_sn?: number
    term_status?: 0 | 1 | 2 | 3 | 4 | 5 | 6 // 术语状态 0：未提取 1：提取中 2：提取完成 3：提取失败 4：翻译中 5：翻译完成 6：翻译失败
    character_name_status?: 0 | 1 | 2 | 3 | 4 | 5 | 6 // 人名状态 0：未提取 1：提取中 2：提取完成 3：提取失败 4：翻译中 5：翻译完成 6：翻译失败
    rating_detail?: {
      score: string
      detail: IRatingDetail[]
    }
    remark?: string
    publish_status_source?: 0 | 1 | 2 | 3 // 原剧集发布状态 0 待发布 1 发布中 2 发布失败 3 发布完成
    publish_status_compound?: 0 | 1 | 2 | 3 // 合成剧集发布 0 待发布 1 发布中 2 发布失败 3 发布完成
    publish_status_preview?: 0 | 1 | number
    deploy_advance?: 0 | 1 | 2 | number
    term_extract_status?: 0 | 1 | 2 // 0 未提取 1 提取中 2 提取完成
    term_trans_status?: 0 | 1 | 2 // 0 未提取 1 提取中 2 翻译完成

    sentences?: number
    annotations?: number
    unlocked_other_days?: number
  }

  interface IPassForm {
    id?: number
    content_rating?: 'S' | 'A' | 'B' | 'C' | string // 内容评级 S A B C
    title?: string
    description?: string
    content_classify?: string
    root_label_ids?: string | string[]
    first_label_ids?: string | string[]
    second_label_ids?: string | string[]
    rating_detail?: {
      score?: string
      detail: IRatingDetail[]
    }
  }

  interface IResourceStatus {
    full: {
      finish_count: number
      total_count: number
      failed_count: number
      tasking_count: number
    }
    preview: {
      finish_count: number
      total_count: number
      failed_count: number
      tasking_count: number
    }
  }

  interface IResourceCheckItem {
    id: number // 资源id
    title: string
    vertical_cover: string
    check_status: 0 | -1 | 1 | 2 | 3 | 4
    origin_episode_progress: IResourceStatus
    pure_episode_progress: IResourceStatus
    no_audio_episode_progress: IResourceStatus
    subtitle_language_progress: IResourceStatus
    business_principal: string
    subtitle_up_language_code: string
    check_reason?: string
    preview_status?: 1 | 2
    netdisk_status?: 1 | 2 | 3 | 4 | 5 | 6 // 1:检测未通过 2:检测完成 3:已转存 4 已开启同步 5 同步完成 6同步失败
    netdisk_err_msg?: string
    release_round: number
    claim_name: string
    audit_status?: 0 | 1 | 2 | 99 | number
    audit_reason?: string
    audit_to_status: 0 | 1 | 2 | 99 | number
    audit_to_reason: string
    entry_user?: string
    partner_name?: string
    recycle_status: 4 | 5 | 1
    reject_image_list?: string[]
  }

  interface ICheckSubtitle {
    id: number // 资源id
    title: string
    progress: string
  }

  interface ISubtitleTranslate {
    language_code: string
    trans_process: {
      finish_count: number
      total_count: number
      failed_count: number
      tasking_count: number
    }
    check_process: string
  }

  type Progress = {
    language_code: string
    status: number // 1 已生成
  }

  interface IResourcePublishItem {
    id: number // 资源id
    title: string
    publish_status_source: 0 | 1 | 2 | 3 // 源剧集发布状态 0 待发布 1 发布中 2 发布失败 3 发布完成
    publish_status_compound: 0 | 1 | 2 | 3 // 合成剧集发布状态 0 待发布 1 发布中 2 发布失败 3 发布完成
    audio_language_code?: string
    resource_type: 1 | 2
    publish_data: {
      language_code: string
      publish_status: 0 | 1 | 2 | 3 // 0 待发布 1 发布中 2 发布失败 3 发布完成
    }[]
    online_time?: number
    preview_status?: 1 | 2
    series_resource_id_list?: number[] | string
    auth_start_time?: number
    release_round: number
    claim_name: string
    audit_status?: 0 | 1 | 2 | 99 | number
    audit_reason?: string
    audit_to_status: 0 | 1 | 2 | 99 | number
    audit_to_reason: string
    partner_name: string
    cover?: string
    multi_subtitle_audit_progress: Progress[]
  }

  interface IResourceAssetInfo {
    origin_path: string
    pure_path: string
    serial_number: number
    subtitle_path?: string
    origin_compress?: 1 | 0
    pure_compress?: 1 | 0
    en_subtitle_path: string
    pure_origin_path: string
    remark?: string
    sentences: string
    annotations: string
  }

  interface IResourceSubtitleError {
    serial_number: number
    language_code: string
    err_count: number // 翻译错误个数
  }

  interface IResourceSubtitleDetailError {
    err_type?: number
    line: number
    err_content: string
    subtitleIndex?: number
    canReplace?: boolean
    shorten_content_syllable_count?: number
    shorten_content: string
  }

  interface IResourceSubtitleFix {
    line: number
    err_content: string
  }

  /**
   * @description 资源任务类型 任务类型，1：资源验收 2：多语言信息校验 3：多语言字幕校验 4：终审发布
   *
   *  */
  type ITaskType = 1 | 2 | 3 | 4 | number

  interface ISubtitleCheck {
    id: number
    type: 1 // 1 字幕点击
    language_code: string
    serial_number: number
  }

  interface IAuditParams {
    id: number
    page_type: 1 | 2 | 3 | 4 | 5 // 1 资源录入 2 资源验收 3 多语言信息 4 多语言字幕 5 终审发布
    reject_to_page?: 1 | 2 | 3 | 4 | 5 | number // 1 资源录入 2 资源验收 3 多语言信息 4 多语言字幕 5 终审发布
    ope_type: 1 | 2 // 1 驳回 2 审核通过
    reject_reason?: string
    language_codes?: string[] // 多语言字幕的审核通过需要传，审核拒绝传en；其他传空
    reject_image_list?: string[] // 驳回图片列表
  }

  interface IRejectForm {
    audit_failed_reason: string
    reject_image_list?: string[]
  }

  type UploadUserFileWithPath = UploadUserFile & { path: string }

  type FileUploaderInstance = {
    uploadHandler: (options: {
      file: File
      onProgress: (evt: { percent: number }) => void
      onSuccess: (response: unknown) => void
      onError: (error: Error) => void
    }) => void
    handleStart: (file: File, newBlob: Blob) => void
  }

  type ImportErrorItem = {
    id: number
    title: string
    partner_name: string
    deal_type?: number
  }

  type IInjectSubtitleParams = {
    series_resource_id?: number // 资源id
    lang_list?: string[] // 压制语种
    force?: boolean // 是否强制压制
    audio_type?: 0 | 1 | 2 | number
    serial_numbers?: number[] // 集号，默认为空；如果为空，压制所有
  }

  interface ICascaderLabel {
    label: string
    value: number
    children?: {
      label: string
      value: number
      [record: string]: string | number
    }[]
  }

  type ITerminology = {
    name?: string
    type?: 1 | 2 | number // 术语类型 1: 人名 2 术语
    character_id?: number
    term_id?: number // 术语id
    term?: string
    official_name?: string
    category?: string
    delete?: 0 | 1 // 删除为1 修改为0
    update?: boolean
    tar_lang?: string
    translation?: string
    isNew?: boolean
    src_type?: 1 | 0
    isFixed?: boolean

    gender?: 0 | 1 | 2 // 性别 1-男 2-女 0-未知
    age?: string // 年龄段
    voice_labels?: string | string[]
    role_labels?: string | string[]
    order?: number
    new_character_sync_term?: 0 | 1
  }

  interface IDistributeTask {
    resource_ids: number[] // 资源id列表
    claim_name: string // 组长分配给哪个组员的姓名
    lang_code?: string // 语言，多语言字幕校验时必填，其他场景传空字符串，20250411新增
  }

  interface ITermStatus {
    language_code: string
    status: 0 | 1 | 2 | 3 | 4 | number // 状态 0 未处理 1 处理中 2 处理完毕 3 处理失败 4 任务超时

  }
}
