/* eslint-disable @typescript-eslint/no-explicit-any */
import { showFailToast } from '@skynet/ui'
import { ref } from 'vue'
import { apiGetMenu, apiGetRoles } from './menu-api'

const menus = ref<M.IMenuItem[]>([])
const leftSideMenu = ref<M.IMenuItem[]>([])
const roles = ref<(M.ISimpleRole[])>([])

const setRole = (roleId: string) => {
  localStorage.setItem('role', roleId)
}

const getRole = () => {
  return localStorage.getItem('role')
}

const setUserRoles = (roles: string) => {
  localStorage.setItem('roles', roles)
}

const getUserRoles = () => {
  return localStorage.getItem('roles')?.split(',').map(n => +n) || []
}

const sortMenu = (arr: M.IMenuItem[]) => {
  arr.sort((a, b) => {
    return (a.serial_number as number) - (b.serial_number as number)
  })

  arr.forEach(row => {
    if (row.children && row.children.length > 0)
      row.children = sortMenu(row.children)
  })
  arr.forEach(row => {
    row.current_menu = 0
  })
  return arr
}

export const getRoles = async () => {
  try {
    const res = await apiGetRoles()
    roles.value = res.data?.list || []
  } catch (error: any) {
    showFailToast(error.response.data.message || '菜单获取失败')
  }
}

const getMenuInfo = async () => {
  try {
    const res = await apiGetMenu()
    const _menu = res.data?.menu || []
    setRole('' + res.data?.role || '1')
    setUserRoles((res.data?.open_roles || []).join(',') || '')
    leftSideMenu.value = sortMenu(_menu)
    menus.value = sortMenu([
      {
        parent_id: -1,
        id: 0,
        path: '',
        title: '根目录',
        children: _menu,
      },
    ])
  } catch (error: any) {
    showFailToast(error.response.data.message || '菜单获取失败')
  }
}

export const useMenu = () => {
  return {
    getMenuInfo,
    menus,
    getRoles,
    getRole,
    roles,
    leftSideMenu,
    setUserRoles,
    getUserRoles
  }
}
