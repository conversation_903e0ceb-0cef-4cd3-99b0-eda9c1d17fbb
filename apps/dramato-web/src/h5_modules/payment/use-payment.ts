import { computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import { getProductList as _getProductList, getPaypalOrder, paypalPaymentConfirm, getAliwallexOrder, apiGetAliwallexOrderStatus, apiGetAliwallexCardOrder } from './payment-api'
import { AxiosError } from 'axios'
import { isEmpty } from 'lodash-es'
import { h5Track } from 'src/lib/h5-track'
import { getUser } from 'src/h5_modules/common/user'
import { StoreDataInfo } from '../store/store'
import { isIos, isTikTok, isFacebook } from 'src/lib/ua'
import { apiProductList } from '../store/store-api'
import { PaymentMethod } from '../store/store'
import { useSeriesItem } from '../series/use-series-item'

export const usePayment = () => {
  const router = useRouter()
  const route = useRoute()
  const uid = ref()
  const payProductList = ref<M.Payment.PaymentResponse>()
  const aliwallexPayInfo = ref<M.Payment.AliwallexPaymentResponse>()

  const objectToParams = (obj: Record<string, string>) => {
    return Object.entries(obj).map(([key, value]) => `${key}=${value}`).join('&')
  }

  const getReturnUrl = (page: string, from: string, extraParams: Record<string, string>) => {
    const url = new URL(location.href)
    const series_id = url.searchParams.get('series_id') || route.params.series_id as string
    const { currentEpisodeId } = useSeriesItem(series_id)
    const video_id = url.searchParams.get('video_id') || currentEpisodeId.value || route.params.episode_id as string
    return `${location.origin}/${page}?${objectToParams({
      series_id,
      video_id,
      from,
      ...extraParams,
    })}`
    // return `${location.origin}/${page}?series_id=${series_id}&video_id=${video_id}&product_id=${product_id}&from=${form}&pay_channel=${payChannel}&pay_sub_channel=${paySubChannel}&video_type=${video_type}`
  }

  const getPaymentList = async () => {
    const res = await _getProductList({ uid: uid.value || '' })
    if (res.code === 200) {
      payProductList.value = res.data
    }
    if (res.code === 401) {
      void router.push('/login')
    }
  }

  // 暂时不使用了，统一走airwallex的sdk
  const airwallexPay = async (id: number, from: string, payChannel: string, paySubChannel: string) => {
    // try {
    //   // if (!uid.value) {
    //   //   return
    //   // }
    //   const returnUrl = getReturnUrl('payment-landing', from || '', id, payChannel, paySubChannel) // `${location.origin}/payment-landing?from=${from || ''}&product_id=${id}`
    //   const res = await apiGetAliwallexCardOrder({ product_id: id, return_url: returnUrl })
    //   // TODO, 订阅跳转
    //   if (res.code === 200) {
    //     aliwallexPayInfo.value = res.data
    //   }
    // } catch (error: unknown) {
    //   // if ((error as AxiosError)?.response?.data && !isEmpty((error as AxiosError)?.response?.data)) {
    //   //   document.open()
    //   //   document.write((error as AxiosError).response?.data as string)
    //   //   document.close()
    //   // } else {

    //   // }
    //   showToast('An error occurred')
    // }
  }

  const paypalPay = async (id: number, from: string, payChannel: string, paySubChannel: string, videoType: string) => {
    try {
      const returnUrl = getReturnUrl('payment-landing', from || '', {
        product_id: id.toString(),
        pay_channel: payChannel,
        pay_sub_channel: paySubChannel,
        video_type: videoType,
        currency: selectedProduct.value?.currency || '',
      }) // `${location.origin}/payment-success?from=${from || ''}&product_id=${id}`
      const res = await getPaypalOrder({ product_id: id, return_url: returnUrl, cancel_url: location.origin + location.pathname })

      if (res.code === 200) {
        if (!res.data?.redirect_url) return
        location.href = res.data?.redirect_url
      }
    } catch (error: unknown) {
      console.log(error, '-------跳转支持错误--------------')
      if (error instanceof AxiosError) {
        showToast(error.response?.data.message)
      } else {
        showToast('An error occurred')
      }
    }
  }

  const paypalPayConfirm = async (data: { order_id: string, payer_id: string }) => {
    const res = await paypalPaymentConfirm(data)
    if (res.code === 200) {
      return true
    }
    return false
  }

  const getAliwallexPayInfo = async (id: number, args: {
    from: string
    payChannel: string
    paySubChannel: string
    series_key?: string
    episode_key?: string
    video_type?: string
    currency?: string
  }) => {
    // 因为在三方支付按钮组件中重新usePayment，所以需要重新解构，所有参数都需要从props传递
    const { from, payChannel, paySubChannel, series_key, episode_key, video_type, currency } = args
    const returnUrl = getReturnUrl('payment-landing', from || '', {
      product_id: id.toString(),
      pay_channel: payChannel,
      pay_sub_channel: paySubChannel,
      video_type: video_type || '',
      currency: currency || '',
    }) // `${location.origin}/payment-success?from=${from || ''}&product_id=${id}`
    const res = await getAliwallexOrder({ product_id: id, return_url: returnUrl, series_key, episode_key })
    if (res.code === 200) {
      aliwallexPayInfo.value = res.data
      if (aliwallexPayInfo.value) {
        aliwallexPayInfo.value.success_url = returnUrl + `&order_id=${aliwallexPayInfo.value.order_id}`
        aliwallexPayInfo.value.fail_url = location.href
      }
    }
  }

  const getAliwallexOrderStatus = async (id: string) => {
    const res = await apiGetAliwallexOrderStatus({ order_id: id })
    if (res.code === 200) {
      return res.data
    }
    return false
  }

  // apple google 用的
  const thirdType = ref('')
  const productId = ref(0)
  const showPop = ref(!!thirdType.value)
  const selectedPayChannel = ref('')
  const payChannel = ref('')
  const paySubChannel = ref('')
  const payLoading = ref(false)
  const videoType = ref('')

  const handlePay = async ({
    from = '/',
    series_id,
    video_id,
    video_type,
  }: {
    from: string
    series_id: string
    video_id: string
    video_type: string
  }) => {
    // 保存视频类型，打点使用
    videoType.value = video_type
    switch (selectedPayChannel.value) {
      case 'AirWallex':
        thirdType.value = 'AirWallex'
        showPop.value = true
        break
      case 'Paypal':
        payLoading.value = true
        await paypalPay(productId.value, from, payChannel.value, paySubChannel.value, video_type)
        payLoading.value = false
        break
      case 'Apple Store':
      case 'Google Play':
        thirdType.value = selectedPayChannel.value
        showPop.value = true
        break
    }
  }

  // product 相关
  const productData = ref<StoreDataInfo>({} as StoreDataInfo)

  // - 安卓
  // - 展示顺序：Google Pay、Paypal、银行卡，支持调整顺序
  //   - 默认选中：Google Pay
  // - iOS
  //   - 展示顺序：Apple Pay、Paypal、银行卡、Google Play，支持调整顺序
  //   - 默认选中：Apple Pay
  const orderedProductList = computed(() => {
    if (!productData.value.channel_list) return []
    const isIosDevice = isIos()
    const orderedList = [...productData.value.channel_list].sort((a, b) => {
      if (isIosDevice) {
      // iOS 排序:  Bank > Paypal > Apple Pay > Google Pay
        if (a.pay_channel === 'AirWallex') return -1
        if (b.pay_channel === 'AirWallex') return 1
        if (a.pay_channel === 'Paypal') return -1
        if (b.pay_channel === 'Paypal') return 1
        if (a.pay_channel === 'Apple Store') return -1
        if (b.pay_channel === 'Apple Store') return 1
        return 0
      } else {
      // Android 排序: Bank > Paypal > Google Pay
        if (a.pay_channel === 'AirWallex') return -1
        if (b.pay_channel === 'AirWallex') return 1
        if (a.pay_channel === 'Paypal') return -1
        if (b.pay_channel === 'Paypal') return 1
        if (a.pay_channel === 'Google Play') return -1
        if (b.pay_channel === 'Google Play') return 1
        return 0
      }
    }).filter(item => {
      // return [item.pay_channel === 'Apple Store' && !isIos() && '!hidden',
      //   isTikTok() && item.pay_channel === 'Google Play' && '!hidden',
      //   isRecurringSelected.value && item.pay_channel === 'Paypal' && '!hidden',
      // ]
      if (!isRecurringSelected.value && item.pay_channel === 'Paypal') return false // 2025-05-22 暂时隐藏Paypal
      // TODO: facebook和web目前行为一致，所以暂时不做区分，后续再做区分
      if (item.pay_channel === 'Apple Store' && !isIos()) return false
      if (isTikTok() && item.pay_channel === 'Google Play') return false
      // if (isRecurringSelected.value && item.pay_channel === 'Paypal') return false
      return true
    })

    return orderedList
  })

  const selectedProduct = computed(() => {
    if (!productData.value.recharge_list) return
    return productData.value.recharge_list.find(item => item.product_id === productId.value)
      || productData.value.membership.find(item => item.product_id === productId.value)
  })

  const isRecurringSelected = computed(() => {
    return productData.value.membership.some(item => item.product_id === productId.value)
  })

  const getProductList = async () => {
    if (productData.value.recharge_list) return
    const res = await apiProductList({ scene: 'purchase' })
    if (res && res.data) {
      productData.value = res.data
      if (!productId.value) {
        productId.value = productData.value.recharge_list[0].product_id
      }
      if (!selectedPayChannel.value) {
        setDefaultPayChannel()
      }
      // h5Track('pay_unlock', 'channel', 'show', {
      //   series_id: route.params.series_id as string,
      //   video_id: route.params.episode_id as string,
      //   pay_channel: payChannel.value,
      //   pay_sub_channel: paySubChannel.value,
      // })
    }
  }

  const setDefaultPayChannel = () => {
    // 2025-05-07 默认使用AirWallex
    const firstPayChannel = productData.value?.channel_list.find(item => item.pay_channel === 'AirWallex')
    // if (isIos()) {
    //   firstPayChannel = productData.value?.channel_list.find(item => item.pay_channel === 'AirWallex')
    // } else if (isTikTok()) {
    //   firstPayChannel = productData.value?.channel_list.find(item => item.pay_channel === 'Paypal')
    // } else {
    //   firstPayChannel = productData.value?.channel_list.find(item => item.pay_channel === 'Google Play')
    // }
    if (firstPayChannel) {
      selectedPayChannel.value = firstPayChannel.pay_channel
      payChannel.value = firstPayChannel.payment_channel
      paySubChannel.value = firstPayChannel.sub_payment_channel
    }
  }

  const getPayMethodClass = (item: PaymentMethod) => {
    // TODO: facebook和web目前行为一致，所以暂时不做区分，后续再做区分
    return []
  }

  return {
    uid,
    payProductList,
    aliwallexPayInfo,
    productId,
    thirdType,
    showPop,
    selectedPayChannel,
    payChannel,
    paySubChannel,
    payLoading,
    productData,
    isRecurringSelected,
    handlePay,
    getPaymentList,
    airwallexPay,
    paypalPay,
    paypalPayConfirm,
    getAliwallexPayInfo,
    getAliwallexOrderStatus,
    orderedProductList,
    selectedProduct,
    getProductList,
    setDefaultPayChannel,
    getPayMethodClass,
  }
}
