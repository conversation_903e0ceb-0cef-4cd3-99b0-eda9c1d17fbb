declare namespace TopUp {
  interface ExchangeProduct {
    product_id: number
    type: 'exchange' | 'recharge' // 商品类型：recharge
    title: string // 商品标题
    currency: string // 货币
    has_discount: 0 | 1 // 是否折扣商品1.是 0.否 // 是折扣商品要展示折扣描述
    expire_time: number // 限时到期时间戳 >0,展示到角标文案未知，显示倒计时
    slogan: string // 角标文案,
    discount_desc: string // 折扣描述
    description: string // 权益说明
    tips: string // 提醒
    price: number // 商品价格:金币数目
    discount_price: number // 折扣价格:金币数目
    delivery_details: { // 发货详情 充值
      quanity: string // 购买数量
      bonus: string // 赠送数量
    }
    r_info: string
    props: [ // 属性列表 highlight.高亮 default.默认
      'highlight',
      'default',
    ]
  }

  interface RechargeProduct {
    product_id: number
    type: 'recharge' // 商品类型：recharge
    title: string // 商品标题
    currency: string // 货币
    has_discount: 0 | 1 // 是否折扣商品1.是 0.否 // 是折扣商品要展示折扣描述
    expire_time: number // 限时到期时间戳 >0,展示到角标文案未知，显示倒计时
    slogan: string // 角标文案,
    discount_desc: string // 折扣描述
    description: string // 权益说明
    tips: string // 提醒
    price: number // 商品价格:单位分
    formatted_price: string // 商品价格:单位分
    discount_price: number // 折扣价格:单位分
    delivery_details: { // 发货详情 充值
      quanity: string // 购买数量
      bonus: string // 赠送数量
    }
    props: [ // 属性列表 highlight.高亮 default.默认
      'highlight',
      'default',
    ]
    pay_channel: string// 支付渠道：appleiap,googleplay，创建订单传此参数
    sku_id: string
  }
}
