import { ref } from 'vue'
import { useLocale } from 'src/lib/use-locale'
import { WelfareSleepDetails } from './sleep-check-in.d'
import { apiGetWelfareSleepDetails } from './sleep-check-in-api'
import { apiGetPrivilege } from '../meal-check-in/meal-check-in-api'

export const useSleepCheckInStore = () => {
  const sleepDetails = ref<WelfareSleepDetails>()
  const privilege = ref<number>(0)
  const getSleepDetails = async () => {
    const res = await apiGetWelfareSleepDetails()
    if (!res.data) return
    sleepDetails.value = res.data
  }

  const getPrivilege = async () => {
    const res = await apiGetPrivilege()
    if (!res.data) return
    privilege.value = res.data.popup
  }

  return {
    sleepDetails,
    getSleepDetails,
    getPrivilege,
    privilege,
  }
}
