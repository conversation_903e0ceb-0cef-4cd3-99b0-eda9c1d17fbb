import { createComponent, mc } from '@skynet/shared'
import { Button, SvgIcon } from '@skynet/ui'
import { ref, onBeforeUnmount, watch } from 'vue'
import { M3u8Player } from './m3u8-player'

type Dimension = [number, number, number, number]

type ClipOptions = {
  props: {
    path: string
    title?: string
    defaultValue?: Dimension[]
  }
  emits: {
    change(dimensions: Dimension[]): void
  }
}

export const Clip1 = createComponent<ClipOptions>({
  props: {
    path: '',
    title: '',
    defaultValue: [[0, 0, 0, 0]],
  },
  emits: {
    change: () => {},
  },
}, (props, { emit }) => {
  const targetElement = ref<HTMLDivElement | null>(null)
  const transparentCanvas = ref<HTMLCanvasElement | null>(null)
  const dimensions = ref<Dimension[]>([])
  const showCanvas = ref(false)

  let isDrawing = false
  let startX = 0, startY = 0, currentX = 0, currentY = 0
  let ctx: CanvasRenderingContext2D | null = null

  const resizeCanvas = () => {
    if (transparentCanvas.value && targetElement.value) {
      const canvas = transparentCanvas.value
      canvas.width = targetElement.value.clientWidth
      canvas.height = targetElement.value.clientHeight
    }
  }

  const handleMouseDown = (e: MouseEvent) => {
    if (!transparentCanvas.value) return
    isDrawing = true
    const rect = transparentCanvas.value.getBoundingClientRect()
    startX = e.clientX - rect.left
    startY = e.clientY - rect.top
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDrawing || !transparentCanvas.value || !ctx) return
    const rect = transparentCanvas.value.getBoundingClientRect()

    currentX = Math.min(Math.max(e.clientX - rect.left, 0), rect.width)
    currentY = Math.min(Math.max(e.clientY - rect.top, 0), rect.height)

    const width = currentX - startX
    const height = currentY - startY

    requestAnimationFrame(() => {
      ctx!.clearRect(0, 0, transparentCanvas.value!.width, transparentCanvas.value!.height)
      dimensions.value.forEach(([x, y, w, h]) => {
        ctx!.strokeRect(x * rect.width, y * rect.height, w * rect.width, h * rect.height)
      })
      ctx!.strokeStyle = 'blue'
      ctx!.lineWidth = 2
      ctx!.strokeRect(startX, startY, width, height)
    })
  }

  const fix2 = (str: number) => +(Math.abs(str).toFixed(2))

  const handleMouseUp = (e: MouseEvent) => {
    if (!transparentCanvas.value || !isDrawing) return
    isDrawing = false

    const rect = transparentCanvas.value.getBoundingClientRect()

    currentX = Math.min(Math.max(e.clientX - rect.left, 0), rect.width)
    currentY = Math.min(Math.max(e.clientY - rect.top, 0), rect.height)

    const topLeftX = Math.min(startX, currentX)
    const topLeftY = Math.min(startY, currentY)
    const width = Math.abs(currentX - startX)
    const height = Math.abs(currentY - startY)

    const canvasWidth = rect.width
    const canvasHeight = rect.height

    const newDimension: Dimension = [
      fix2(topLeftX / canvasWidth),
      fix2(topLeftY / canvasHeight),
      fix2(width / canvasWidth),
      fix2(height / canvasHeight),
    ]

    const minSize = 5
    if (width >= minSize && height >= minSize) {
      dimensions.value.push(newDimension)
      emit('change', dimensions.value)
    } else {
      console.log('框选太小，操作取消')
    }

    ctx!.clearRect(0, 0, transparentCanvas.value.width, transparentCanvas.value.height)
    dimensions.value.forEach(([x, y, w, h]) => {
      ctx!.strokeRect(x * canvasWidth, y * canvasHeight, w * canvasWidth, h * canvasHeight)
    })
  }

  watch(() => transparentCanvas.value, () => {
    if (transparentCanvas.value) {
      ctx = transparentCanvas.value.getContext('2d')
      resizeCanvas()
      transparentCanvas.value.addEventListener('mousedown', handleMouseDown)
      window.addEventListener('mousemove', handleMouseMove)
      window.addEventListener('mouseup', handleMouseUp)
      window.addEventListener('resize', resizeCanvas)
    }
  })

  onBeforeUnmount(() => {
    if (transparentCanvas.value) {
      transparentCanvas.value.removeEventListener('mousedown', handleMouseDown)
    }
    window.removeEventListener('mousemove', handleMouseMove)
    window.removeEventListener('mouseup', handleMouseUp)
    window.removeEventListener('resize', resizeCanvas)
  })

  watch(() => showCanvas.value, newValue => {
    if (transparentCanvas.value) {
      dimensions.value = props.defaultValue
      const _width = transparentCanvas.value.width
      const _height = transparentCanvas.value.height
      ctx!.clearRect(0, 0, transparentCanvas.value.width, transparentCanvas.value.height)
      dimensions.value.forEach(([x, y, w, h]) => {
        ctx!.strokeStyle = 'blue'
        ctx!.lineWidth = 2
        ctx!.strokeRect(_width * x, _height * y, _width * w, _height * h)
      })
      emit('change', dimensions.value)
    }
  })

  return () => (
    <>
      <div ref={targetElement} class="relative inline-block">
        <M3u8Player id="clip1-player" class="w-[350px]" url={props.path} onPlayerReady={_ => {
          resizeCanvas()
        }} />
        <Button class="btn btn-primary btn-xs absolute right-2 top-2 z-up" onClick={() => {
          showCanvas.value = !showCanvas.value
        }}
        >
          {props.title || '截取坐标'}
        </Button>
        <canvas ref={transparentCanvas} class={mc('absolute top-0 left-0 w-full h-full', showCanvas.value ? 'block' : 'hidden')} />
      </div>
      <div class="w-[350px] pt-2 text-[var(--text-2)]">
        {dimensions.value.map((dim, index) => (
          <div class="flex items-center justify-between" key={index}>
            <span>[{dim.map(d => (d)).join(', ')}]</span>
            <SvgIcon class="size-4 cursor-pointer text-[var(--text-1)]" name="ic_del" onClick={() => {
              dimensions.value.splice(index, 1)
              if (transparentCanvas.value) {
                const rect = transparentCanvas.value.getBoundingClientRect()
                const canvasWidth = rect.width
                const canvasHeight = rect.height
                ctx!.clearRect(0, 0, transparentCanvas.value.width, transparentCanvas.value.height)
                dimensions.value.forEach(([x, y, w, h]) => {
                  ctx!.strokeRect(x * canvasWidth, y * canvasHeight, w * canvasWidth, h * canvasHeight)
                })
              }
              emit('change', dimensions.value)
            }}
            />
          </div>
        ))}
      </div>
    </>
  )
})
