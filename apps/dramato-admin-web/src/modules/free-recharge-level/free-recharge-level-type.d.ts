declare namespace M {
  interface FreeRechargeLevelSearchParams {
    page_size: number // 每页数据
    next: string // 下一页数据偏移量 使用返回的next
    app_id: string // 查询的APP ID
    title?: string // 档位名称
    store?: string // 上架的商店
    status?: number // 上架状态 //状态：0.所有 1-启用，2-禁用
  }

  interface FreeRechargeLevel {
    id?: number
    app_id?: number
    app_name?: string
    product_type?: string
    title?: string
    slogan?: string
    tips?: string
    currency?: string
    price?: number
    discount_price?: number
    platform?: string
    store?: string
    status?: number
    props?: string[]
    delivery_details?: {
      quanity?: number
      bonus?: number
      period?: string
    }
    apple_store?: {
      apple_id?: string
      product_id?: string
      group_identifier?: string
    }
    google_play?: {
      google_play_id?: string
    }
    priority?: number
    first_recharge?: number
    create_user?: string
    update_user?: string
    created?: number
    updated?: number
    has_discount?: number // 是否打折1.是 0.否
    time_limit?: number // 限时：0不限时间 >0限时,单位小时
    discount_desc?: string
    description?: string
  }
}
