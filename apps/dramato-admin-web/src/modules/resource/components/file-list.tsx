import { createComponent } from '@skynet/shared'
import { apiGetFileByDir } from '../resource-api'
import { Button, SvgIcon } from '@skynet/ui'
import { computed, ref, watch } from 'vue'
type FileListOptions = {
  props: {
    dir: string
    chooseFileExts?: string[]
  }
  emits: {
    dirChange: (dir: string) => void
    save: (path: string) => void
    close: () => void
  }
}
export const FileList = createComponent<FileListOptions>({
  props: {
    dir: '/',
    chooseFileExts: [],
  },
  emits: {
    dirChange: () => {},
    save: () => {},
    close: () => {},
  },
}, (props, { emit }) => {
  const loading = ref(false)
  const fileList = ref<string[]>([])
  watch(() => props.dir, async () => {
    loading.value = true
    const res = await apiGetFileByDir({
      dir: props.dir,
    })
    fileList.value = res.data?.files || []
    loading.value = false
  }, {
    immediate: true,
  })
  const breadcrumbParts = computed(() => {
    return props.dir.split('/').filter(Boolean)
  })

  const getFileName = (filePath: string) => {
    return filePath.split('/').pop()
  }

  const isDirectory = (filePath: string) => {
    // 判断是否是目录：没有扩展名即认为是目录
    const fileName = getFileName(filePath) || ''
    return !fileName.includes('.')
  }

  const navigateTo = (index: number) => {
    if (index === -1) {
      emit('dirChange', '/')
      return
    }
    const path = '/' + breadcrumbParts.value.slice(0, index + 1).join('/')
    emit('dirChange', path)
  }

  const intoDirByName = (path: string) => {
    if (isDirectory(path)) {
      emit('dirChange', path)
    } else if (props.chooseFileExts.length > 0 && !isDirectory(path) && props.chooseFileExts.some(fileExt => path.endsWith(fileExt))) {
      emit('save', path)
    }
  }
  const close = () => {
    emit('close')
  }
  const save = () => {
    emit('save', props.dir)
  }

  return () => (
    <>
      {
        loading.value
          ? (
              <div class="w-full h-[300px] flex items-center justify-center">
                <SvgIcon class="animate-spin" name="ic_loading" />
              </div>
            )
          : (
              <>
                <div class="breadcrumbs text-sm text-[var(--text-1)]">
                  <ul>
                    <li>
                      <a onClick={() => navigateTo(-1)}>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          class="h-4 w-4 stroke-current"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                          />
                        </svg>
                        全部
                      </a>
                    </li>
                    {
                      breadcrumbParts.value.map((title, index) => {
                        return (
                          <li>
                            <a onClick={() => navigateTo(index)}>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                class="h-4 w-4 stroke-current"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                                />
                              </svg>
                              {title}
                            </a>
                          </li>
                        )
                      })
                    }
                  </ul>
                </div>
                <ul class="flex flex-col">
                  {
                    fileList.value.length > 0
                      ? fileList.value.map(fileName => {
                        return (
                          <li class="p-2 text-xs text-[var(--text-2)]">
                            <a class="link" onClick={() => {
                              intoDirByName(fileName)
                            }}
                            >{getFileName(fileName)}
                            </a>
                          </li>
                        )
                      })
                      : (
                          <div class="h-full w-full flex flex-col justify-center items-center">
                            <SvgIcon class="w-[180px] h-[180px]" name="ic_empty" />
                            <div class="text-[var(--text-3)]">暂无数据</div>
                          </div>
                        )
                  }
                </ul>
                {
                  props.chooseFileExts.length === 0
                    ? (
                        <x-dialog-footer class="w-full mt-4 flex justify-end gap-x-[10px]">
                          <Button class="btn btn-default btn-sm" onClick={close}>取消</Button>
                          <Button class="btn btn-primary btn-sm" onClick={save}>确定</Button>
                        </x-dialog-footer>
                      )
                    : null
                }
              </>
            )
      }

    </>
  )
})
