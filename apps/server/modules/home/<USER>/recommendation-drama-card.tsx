import { createComponent, mc } from '@skynet/shared'
import { computed, ref } from 'vue'
// import { DefaultCover } from './three-column-drama-card'
import DefaultCover from 'src/h5_modules/images/cover-def.webp'
import { SvgIcon } from '@skynet/ui'
import { useRoute, useRouter } from 'vue-router'
import { ICRedRight } from 'src/h5_modules/images/images'

type RecommendationDramaCardOptions = {
  props: {
    drama: M.Drama
  }
}
export const RecommendationDramaCard = createComponent<RecommendationDramaCardOptions>({
  props: {
    drama: {},
  },
}, props => {
  const cover = ref<string>(props.drama.cover || '')
  const loadingCover = ref<boolean>(false)

  /** Free＞Following＞Hot＞New＞Dubbed */
  const tag = computed(() => {
    if (!props.drama.tag || props.drama.tag.length <= 0) {
      return ''
    }

    if (props.drama.tag.includes('Free')) {
      return 'Free'
    }

    if (props.drama.tag.includes('Following')) {
      return 'Following'
    }

    if (props.drama.tag.includes('Hot')) {
      return 'Hot'
    }

    if (props.drama.tag.includes('Dubbed')) {
      return 'Dubbed'
    }

    return ''
  })

  const router = useRouter()
  const gotoPaly = () => {
    void router.push('/series/' + props.drama.key)
  }

  return () => (
    <x-drama-card class="block h-auto overflow-hidden rounded-lg bg-[#1D1D1E]" onClick={gotoPaly}>
      <div class="min-h-56.75 h-56.75 relative block w-full overflow-hidden rounded-t-lg">
        <img
          class={mc('size-full object-cover rounded-t-lg', loadingCover.value ? 'opacity-0' : '')}
          src={props.drama.cover || DefaultCover}
          onLoad={() => loadingCover.value = false}
          onError={() => cover.value = DefaultCover}
        />
        {
          !!tag.value && (
            <x-drama-top-tag class="px-1.25 py-.5 leading-3.25 text-3 rounded-bl-1 rounded-tr-1 absolute right-0 top-0 block text-xs text-white"
              style={{
                backgroundImage: tag.value === 'Free' ? `linear-gradient(270deg, #04b587 0.05%, #46d988 100%)` : 'linear-gradient(90deg, #F47040 0%, #F52067 100%)',
              }}
            >
              {tag.value}
            </x-drama-top-tag>
          )
        }
        {(!props.drama.style || props.drama.style === 1) && props.drama.content_tags && props.drama.content_tags.length > 0 && (
          <x-drama-bottom-tag-list class="p-.75 absolute bottom-0 left-0 flex w-full gap-x-1 overflow-hidden">
            {
              props.drama.content_tags.slice(0, 1).map(item => <x-drama-bottom-tag class="rounded-1 text-3 py-.5 leading-3.5 max-w-full truncate bg-[#42444675] px-1 text-[--white]" key={item}>{item}</x-drama-bottom-tag>)
            }
          </x-drama-bottom-tag-list>
        )}
      </div>
      <x-drama-desc-wrap class="flex flex-col gap-y-1 p-2">
        <x-drama-title class="max-h-8.4 text-3.5 leading-4.2 line-clamp-2 w-full text-[#fdfbfc]">{props.drama.title}</x-drama-title>
        {
          !(!props.drama.style || props.drama.style === 1) && props.drama.content_tags && props.drama.content_tags.length > 0 && (
            <x-drama-bottom-tag-list class=" flex w-full gap-x-1 overflow-hidden">
              {
                props.drama.content_tags.slice(0, 1).map(item => (
                  <x-drama-bottom-tag class={
                    mc(
                      'max-w-full flex flex-row items-center overflow-hidden rounded-1 text-3 leading-3.5',
                      props.drama.style === 2 && 'bg-[#411F27] text-[#FC2763]  px-1 py-.5',
                      props.drama.style === 3 && 'bg-[#443429] text-[#E29D44]  px-1 py-.5',
                      props.drama.style === 4 && 'text-[#797b7d]',
                    )
                  } key={item}
                  >
                    <x-drama-bottom-tag-desc class="flex-1 truncate text-xs">{item}</x-drama-bottom-tag-desc>
                    {props.drama.style === 2 && <img src={ICRedRight} class={mc('size-3')} alt="" />}
                  </x-drama-bottom-tag>
                ))
              }
            </x-drama-bottom-tag-list>
          )
        }
      </x-drama-desc-wrap>

    </x-drama-card>
  )
})

export default RecommendationDramaCard
