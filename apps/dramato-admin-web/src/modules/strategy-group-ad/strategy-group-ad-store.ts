import { useValidator } from '@skynet/shared'
import { CreateForm } from '@skynet/ui'
import { ref } from 'vue'
import { z } from 'zod'
const Form = CreateForm<M.StrategyGroup>()
const defaultFormData = () => {
  return {
    name: '',
    id: '',
    user_type: 2,
    user_platform: 0,
    scene: ['app_open', 'drama_free', 'drama_lock', 'reward'],
    reward_ads: {
      list: [],
      ad_list_new: [{
        ad_type: '',
        ad_platform: [],
        ad_rep_open: false,
        replace_ad: [],
      }],
      interstitial_config: {
        show_guide: false,
      },
    },
    ad_list: [],
    show_time_gap: 30,
    task_config: undefined,
    drama_list: undefined,
    iap_strategys: undefined,
    send_pro_user: 1,
    iap_strategy_ids: undefined,
    count_limit_type: 1,
    app_open_config: {
      strategy: {
        cooling_time: undefined, // CD时间，单位-秒
        watch_ad_count: undefined, // 频次上限，单位-次/天
      },
      ad_list: [],
      ad_list_new: [{
        ad_type: '',
        ad_platform: [],
        ad_rep_open: false,
        replace_ad: [],
      }],
    },
    drama_free_config: { // 面板前——集间广告控制参数
      strategy: {
        start: undefined, // 广告开始展示剧集
        skip: undefined, // 间隔剧集
        watch_time: undefined, // 间隔时间,单位-秒
        ad_force_time: undefined, // 原生广告强制看时长,单位-秒
        preload_ad_count: undefined, // 预加载广告数量
        show_reward_ad_native_ad_count: undefined, // 看第几次广告弹出激励视频弹窗
        skip_native_ad_time_ms: undefined, // 看激励视频免广告时长 单位-秒
        ad_switch_interval: undefined, // 连续几次不看激励视频免广告关闭弹窗
        ad_type_start: 'interstitial', // 起始广告
        ad_gap: [// 广告类型间gap
          {
            ad_type: 'interstitial', // rewarded-激励视频,native-原生广告,interstitial-插屏广告
            ad_count: 0, // 数量
          },
          {
            ad_type: 'native',
            ad_count: 0, // 数量
          },
          {
            ad_type: 'rewarded',
            ad_count: 0, // 数量
          },
        ],
        reward_type: 'none', // 激励视频类型,none-无激励视频，no_ads-免广激励视频,unlock-解锁激励视频
        unlock_nums: undefined, // 解锁激励视频需要看的广告次数
      },
      ad_list: [],
      ad_list_new: [{
        ad_type: '',
        ad_platform: [],
        ad_rep_open: false,
        replace_ad: [],
      }],
    },
    reward_config: {
      ad_list: [],
      ad_list_new: [{
        ad_type: '',
        ad_platform: [],
        ad_rep_open: false,
        replace_ad: [],
      }],
      strategy: {
        cd_time: undefined,
        task: [{
          sort_no: 1,
          coins: 1,
        }],
      },
    },
  } as M.StrategyGroup
}
const formData = ref<M.StrategyGroup>(defaultFormData())
const activeUserType = ref<'latest_days' | 'start_and_end'>('latest_days')

const resetFormData = () => {
  formData.value = defaultFormData()
}

const resetUserFormData = () => {
  const { name, user_type, user_platform, platform, user_config } = defaultFormData()
  formData.value = { ...formData.value, name, user_type, user_platform, user_config, platform }
}

const resetProductFormData = (isIAA?: boolean) => {
  if (isIAA) {
    formData.value = {
      ...formData.value,
      drama_list: [],
      ad_id: undefined,
      unlock_nodes: [],
      ad_info: undefined,
    }
  } else {
    formData.value.product_config = defaultFormData().product_config
  }
}

const resetSolutionFormData = () => {
  formData.value = {
    ...formData.value,
    ad_id: undefined,
    reward_ads: {
      list: [],
    },
    ad_info: undefined,
  }
}

const resetTaskFormData = (isIAA?: boolean) => {
  if (isIAA) {
    formData.value.task_config = undefined
  } else {
    formData.value = {
      ...formData.value,
      scene: ['app_open', 'drama_free', 'drama_lock', 'reward'],
      reward_ads: {
        list: [],
        ad_list_new: [{
          ad_type: '',
          ad_platform: [],
          ad_rep_open: false,
          replace_ad: [],
        }],
      },
      app_open_config: {
        strategy: {
          cooling_time: undefined, // CD时间，单位-秒
          watch_ad_count: undefined, // 频次上限，单位-次/天
        },
        ad_list: [],
        ad_list_new: [{
          ad_type: '',
          ad_platform: [],
          ad_rep_open: false,
          replace_ad: [],
        }],
      },
      drama_free_config: { // 面板前——集间广告控制参数
        strategy: {
          start: undefined, // 广告开始展示剧集
          skip: undefined, // 间隔剧集
          watch_time: undefined, // 间隔时间,单位-秒
          ad_force_time: undefined, // 原生广告强制看时长,单位-秒
          preload_ad_count: undefined, // 预加载广告数量
          show_reward_ad_native_ad_count: undefined, // 看第几次广告弹出激励视频弹窗
          skip_native_ad_time_ms: undefined, // 看激励视频免广告时长 单位-秒
          ad_switch_interval: undefined, // 连续几次不看激励视频免广告关闭弹窗
          ad_type_start: 'interstitial', // 起始广告
          ad_gap: [// 广告类型间gap
            {
              ad_type: 'interstitial', // rewarded-激励视频,native-原生广告,interstitial-插屏广告
              ad_count: 0, // 数量
            },
            {
              ad_type: 'native',
              ad_count: 0, // 数量
            },
            {
              ad_type: 'rewarded',
              ad_count: 0, // 数量
            },
          ],
        },
        ad_list: [],
        ad_list_new: [{
          ad_type: '',
          ad_platform: [],
          ad_rep_open: false,
          replace_ad: [],
        }],
      },
      reward_config: {
        strategy: {
          cd_time: undefined,
          task: [{
            sort_no: 1,
            coins: 1,
          }],
        },
        ad_list: [],
        ad_list_new: [{
          ad_type: '',
          ad_platform: [],
          ad_rep_open: false,
          replace_ad: [],
        }],
      },
    }
  }
}

const stepOneRules = z.object({
  name: z.string().min(1, '必填'),
  show_time_gap: z.number().min(0, '必填'),
  task_config: z.object({
    priority: z.optional(z.number().min(0)),
    duration: z.number().min(-1),
  }),
})

const { error: stepOneError, validateAll: _validateStepOne } = useValidator(formData, stepOneRules)

const validateStepOne = () => {
  const exclude = [].filter(Boolean).flat() as string[]
  const valid = _validateStepOne({ exclude })

  stepStatusList.value[0] = valid ? 'valid' : 'invalid'
  return valid
}

const stepTwoRules = z.object({
})

const { error: stepTwoError, validateAll: _validateStepTwo } = useValidator(formData, stepTwoRules)

const validateStepTwo = () => {
  const valid = _validateStepTwo()
  stepStatusList.value[1] = valid ? 'valid' : 'invalid'
  return valid
}

const stepRulesForSolution = z.object({
  drama_free_config: z.object({
    strategy: z.object({
      start: z.number().min(0, '必填'),
      skip: z.number().min(0, '必填'),
      watch_time: z.number().min(0, '必填'),
    }),
    ad_list: z.array(z.object({})).min(1),
  }),
  reward_ads: z.object({
    list: z.array(z.object({})).min(1),
  }),
  app_open_config: z.object({
    ad_list: z.array(z.object({})).min(1),
  }),
  reward_config: z.object({
    ad_list: z.array(z.object({})).min(1),
  }),
  // out_flow_config.ad_list_new 必须还有一项
  out_flow_config: z.object({
    ad_list_new: z.array(z.object({
      ad_type: z.string().min(1, '请填写广告类型'),
      ad_platforms: z.array(z.string().min(0, '请填写聚合平台')).min(1, '请填写聚合平台'),
      replace_ad: z.array(z.object({})),
    })).min(1, '请填写广告类型和聚合平台'),
    strategy: z.object({
      start: z.number({ required_error: '请填写广告开始展示剧集' }).min(1, '广告开始展示剧集必须大于0'),
      skip: z.number({ required_error: '请填写间隔剧集' }).min(1, '间隔剧集必须大于0'),
      watch_time: z.number({ required_error: '请填写间隔时间' }).min(1, '间隔时间必须大于0'),
    }),
  }),
})

const { error: stepErrorForSolution, validateAll: _validateStepSolution, ErrorSpan: ErrorSpanForSolution } = useValidator(formData, stepRulesForSolution)

const validateStepTwoForIap = () => {
  const exclude = [
    !formData.value.scene?.includes('drama_free') && ['drama_free_config'],
    !formData.value.scene?.includes('app_open') && ['app_open_config'],
    !formData.value.scene?.includes('reward') && ['reward_config'],
    !formData.value.scene?.includes('drama_lock') && ['reward_ads'],
    !formData.value.scene?.includes('quit_player') && ['quit_player_config'],
  ].filter(Boolean).flat() as string[]
  const valid = _validateStepSolution({ exclude })
  stepStatusList.value[1] = valid ? 'valid' : 'invalid'
  return valid
}

const validateStepTwoForIapNew = () => {
  if (!formData.value.scene?.includes('quit_player') && !formData.value.scene?.includes('drama_free')
    && !formData.value.scene?.includes('app_open') && !formData.value.scene?.includes('reward')
    && !formData.value.scene?.includes('drama_lock') && !formData.value.scene?.includes('out_flow')) {
    stepErrorForSolution.value = {
      '': [{ message: '请选择至少一个广告位置' }],
    }
    return false
  }
  const exclude = [
    !formData.value.scene?.includes('drama_free') && ['drama_free_config'],
    !formData.value.scene?.includes('app_open') && ['app_open_config'],
    !formData.value.scene?.includes('reward') && ['reward_config'],
    !formData.value.scene?.includes('drama_lock') && ['reward_ads'],
    'drama_free_config.ad_list',
    'app_open_config.ad_list',
    'reward_config.ad_list',
    'reward_ads.list',
  ].filter(Boolean).flat() as string[]
  const valid = _validateStepSolution({ exclude })
  stepStatusList.value[1] = valid ? 'valid' : 'invalid'
  return valid
}

const stepThreeRules = z.object({
  // task_config: z.object({
  //   priority: z.optional(z.number().min(0)),
  //   duration: z.number().min(-1),
  //   auto_up: z.number().min(-1).optional(),
  // }),
})

const { error: stepThreeError, validateAll: _validateStepThree } = useValidator(formData, stepThreeRules)

const validateStepThree = () => {
  const valid = _validateStepThree()
  stepStatusList.value[2] = valid ? 'valid' : 'invalid'
  return valid
}

const validateAllSteps = () => {
  return validateStepOne() && validateStepTwo() && validateStepThree()
}

const validateAllStepsForIap = () => {
  return validateStepOne() && validateStepTwoForIap() && validateStepThree()
}

const validateAllStepsForIapNew = () => {
  return validateStepOne() && validateStepTwoForIapNew() && validateStepThree()
}

const stepStatusList = ref<Array<'invalid' | 'valid'>>(['invalid', 'invalid', 'invalid'])

const userProfileList = ref<Api.StrategyGroupUserProfile.Item[]>([])
const fetchingUserProfileList = ref(false)

const platformMap: Record<number, string> = {
  0: 'All',
  1: 'IOS',
  2: 'Android',
}

export const useStrategyGroupAdStore = () => {
  return {
    platformMap,
    formData,
    resetFormData,
    Form,
    stepStatusList,
    stepOneError,
    stepTwoError,
    stepErrorForSolution,
    ErrorSpanForSolution,
    validateStepTwoForIap,
    stepThreeError,
    validateStepOne,
    validateStepTwo,
    validateStepThree,
    validateAllSteps,
    validateAllStepsForIap,
    validateAllStepsForIapNew,
    activeUserType,
    resetProductFormData,
    resetTaskFormData,
    resetUserFormData,
    resetSolutionFormData,
    userProfileList,
    fetchingUserProfileList,
  }
}
