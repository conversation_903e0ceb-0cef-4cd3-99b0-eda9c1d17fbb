export const id = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: '<PERSON><PERSON><PERSON> Populer',
    },
  },
  myList: {
    watchHistory: `R<PERSON><PERSON>at Tonton`,
    myList: '<PERSON><PERSON><PERSON>',
    seeAll: '<PERSON><PERSON>',
    noData: 'Tidak Ada Has<PERSON>',
  },
  // 分享页
  share: {
    watchNow: 'Tonton Sekarang',
    episodeDialog: {
      title: 'Bergabunglah dengan saya dan dapatkan koin ekstra!',
      description: 'Saya ketagihan drama ini!',
      tips: 'Rekomendasi',
      button: 'Tonton drama lengkapnya',
      limit24: 'Waktu terbatas: 24h',
    },
    toast: {
      takeOffShelf: 'Tidak Ada Konten yang Tersedia',
    },
  },
  'customer-service-center': {
    title: 'Pusat Layanan Pelanggan',
    description: 'Deskripsi',
    'description-placeholder': 'Berikan rincian untuk membantu kami memahami masalahnya',
    'email-placeholder': 'Masukkan email Anda untuk menerima pembaruan umpan balik',
    'submit-feedback': '<PERSON><PERSON>',
    'enter-feedback': 'Umpan balik masalah lainnya',
    'upload-pictures': 'Unggah Gambar',
    feedback: 'Umpan Balik',
    'problem-type': 'Jenis Masalah',
    'problem-type-placeholder': 'Silakan Pilih',
    'payment-issue-placeholder': 'Harap berikan ID dan bukti pembelian pesanan Anda:Google: https://pay.google.com/ (Nomor pesanan dimulai dengan GPA...) iOS: App Store - Apple ID - Riwayat Pembelian (ID Pesanan)',
    'content-issue-placeholder': 'Harap berikan judul drama, nomor episode, dan bahasa subtitle yang hilang',
    'ads-issue-placeholder': 'Harap berikan model perangkat, negara, dan pengaturan bahasa Anda',
    'technical-issue-placeholder': 'Harap berikan model perangkat, negara, dan pengaturan bahasa Anda',
    'other-issue-placeholder': 'Harap berikan detail lebih lanjut, termasuk model perangkat dan negara Anda',
    'i-want-to-give-feedback': 'Saya ingin memberikan umpan balik',
    'are-you-satisfied-with-this-reply': 'Apakah kamu puas dengan balasan ini?',
    unsatisfied: 'Tidak puas',
    satisfied: 'Puas',
    myFeedback: 'Umpan Balik Saya',
    'reply-to-feedback': 'Balas umpan balik',
    'description-min-length': 'Masukkan deskripsi masalah (minimal 6 karakter).',
    'problem-type-not-choose': 'Silakan pilih jenis umpan balik.',
    'description-empty': 'Kolom tidak boleh kosong.',
  },
  terminate: {
    question: 'Bagaimana cara mengelola langganan saya?',
    desc: 'Klik tombol di bawah ini untuk masuk ke halaman manajemen kontrak. Kamu dapat mengelola langgananmu dengan mengklik tombol kontrak:',
    title: 'Manajemen Kontrak',
    expiration: 'Waktu Kedaluwarsa',
    manageDesc: 'Hubungan Kontraktual',
  },
  toast: {
    'upload-failed': 'Gagal Mengunggah, Silakan Unggah Lagi',
    'submit-success': 'Berhasil dikirim',
    'email-incorrect': 'Format email salah',
  },
  not_found: {
    title: 'Halaman yang Anda cari tidak ada',
  },
  regulations: {
    howUseData: 'Bagaimana Kami Menggunakan Data Anda',
    content: `Jika Anda memilih "Izinkan" pada permintaan izin Transparansi Pelacakan Aplikasi, kami akan menggunakan informasi dan data Anda untuk mengoptimalkan layanan kami dan memberikan pengalaman terbaik kepada Anda, jika tidak, kami hanya akan mengumpulkan data dan tidak akan melakukan aktivitas pelacakan apa pun.

Jika Anda setuju untuk mengizinkan DramaWave dan mitra serta pemasok kami menggunakan data dari perangkat Anda untuk menganalisis bagaimana Anda menggunakan aplikasi, mempersonalisasi rekomendasi konten, dan menyediakan lebih banyak layanan terkait informasi, silakan klik "Setuju dan Lanjutkan" untuk melanjutkan.`,
    acceptAll: 'Terima Semua',
    dataSettings: 'Pengaturan Data',
    next: 'Berikutnya',
    cancel: 'Batal',
    aboutYourPrivacy: 'Tentang Privasi Anda',
    personalDataName: 'Data yang Dipersonalisasi',
    personalDataDescription: 'Kami menggunakan informasi ini untuk mengingat pilihan Anda dan memberikan rekomendasi iklan dan konten yang dipersonalisasi, pemberitahuan, dan pengingat, dll.',
    advertisementDataName: 'Data Iklan',
    advertisementDataDescription: 'Cookie, pengidentifikasi perangkat Anda, atau informasi lain dapat disimpan atau diakses di perangkat Anda untuk tujuan iklan online. Data pengguna juga dapat dibagikan dengan mitra iklan kami.',
    behaviorsDataName: 'Data Perilaku',
    behaviorsDataDescription: 'Data terkait analitik, seperti cookie atau pengidentifikasi perangkat, dapat disimpan untuk mengukur dan menganalisis penggunaan, perilaku, dan kinerja iklan.',
    privacyAndTerms: 'Kebijakan Privasi dan Ketentuan Layanan',
    referToPrivacy: 'Silakan merujuk ke Kebijakan Privasi',
    referToTerms: 'Silakan merujuk ke Ketentuan Layanan',
    agreeAndSave: 'Setuju & Simpan',
  },
  coins: {
    earnings: {
      title: 'Penghasilan Saya',
      total: 'Total Berlian',
      exchange: 'Pertukaran',
      earningsHistory: 'Riwayat Penghasilan',
      exchangeHistory: 'Riwayat Pertukaran',
      noRecords: 'Tidak ada catatan',
      last50earnings: 'Hanya 50 penghasilan terakhir yang ditampilkan',
      last50exchanges: 'Hanya 50 catatan penukaran terakhir yang ditampilkan',
      benefits: 'Manfaat',
      gotIt: 'Mengerti',
    },
    exchange: {
      title: 'Zona Penukaran',
      notOpen: 'Belum dibuka, mohon nantikan',
      receiveBtn: 'Terima dengan Senang Hati',
      earnCoins: 'Dapatkan Berlian',
      redeemRequiredXCoins: 'Penukaran membutuhkan {X} berlian',
      exchangeSuccess: 'Berhasil Ditukar',
      cardId: 'ID KARTU',
      exchangeFailed: 'Penukaran Gagal',
      transferFailedInsufficientStock: 'Habis. Silakan datang lebih awal besok.',
      gotItBtn: 'Mengerti',
      transferFailedRiskControl: 'Akun tidak biasa, sementara tidak dapat menukar.',
      tryAgainBtn: 'Coba Lagi Nanti',
      transferConfirm: 'Konfirmasi Transfer',
      paypalAccountEmail: 'Email Akun PayPal',
      confirmPaypalAccountEmail: 'Konfirmasi Email Akun PayPal',
      transferX: 'Transfer {X}',
      transferXConfirm: 'Konfirmasi Transfer {X}',
      transferToYourAccountX: 'Transfer ke akun Anda {X}',
      confirmBtn: 'Konfirmasi',
      redeemFailed: 'Penukaran gagal',
      redeemFailedContent: 'Terjadi kesalahan. Silakan coba lagi nanti.',
      transferSuccessTitle: 'Kami sedang mengirim uang!',
      transferSuccessContent: 'Uang Anda akan tiba dalam waktu 5 menit hingga 72 jam.',
      copySuccess: 'Salin Berhasil',
      enterEmailError: 'Masukkan alamat email yang valid',
      enterEmailValidError: 'Kesalahan input, harap masukkan kembali',
      emailNotMatchError: 'Alamat email tidak sesuai, harap masukkan kembali',
    },
  },
  meal_check_in: {
    sleeping_link: 'Dapatkan Berlian Saat Tidur',
    rules_title: 'Aturan Aktivitas',
    benefits_title: 'Benefits',
    rules_1: '1. Setiap hari, ada empat waktu untuk Anda mencatat makanan Anda: Sarapan: 07:00-10:00; Makan Siang: 11:00-14:00; Makan Malam: 17:00-20:00; Camilan Malam: 21:00-24:00.',
    rules_2: '2. Jika Anda melewatkan waktu pencatatan yang sesuai, Anda bisa menonton video untuk mengklaim hadiah.',
    rules_3: '3. Hadiah akan direset setiap hari pada tengah malam, jadi jangan lupa untuk mengklaim hadiah Anda.',
  },
  sleep_check_in: {
    earn_link: 'Catat makanan untuk mendapatkan',
    benefits_title: 'Benefits',
    rules_1:
      '1. Saat ini, dari pukul 7 malam hingga 12 malam setiap malam, anda boleh mengaktifkan mod \'Tidur untuk Menjana\'.',
    rules_2:
      '2. Setelah mengaktifkan, pada keesokan harinya, selepas tidur selama 8 jam, anda boleh menuntut ganjaran tidur antara pukul 8 pagi hingga 12 tengah hari.',
    rules_3:
      '3. Jika anda terlepas masa untuk menuntut ganjaran, anda boleh menonton video antara pukul 12 tengah hari hingga 7 malam pada keesokan harinya untuk menuntut ganjaran.',
  },
  bind_email: {
    title: 'Masukkan Alamat Email Anda',
    list_1: 'Hubungkan emailmu untuk mendapatkan Koin Hadiah eksklusif',
    list_2: 'Dapatkan notifikasi tentang event mendatang dan penawaran spesial',
    list_3: 'Jadilah yang pertama tahu saat episode baru dirilis',
    email_placeholder: 'Silakan masukkan alamat emailmu',
    verify: 'Verifikasi',
    email_error: 'Harap masukkan alamat email yang valid',
    verify_progress: 'Email sedang diverifikasi',
    verify_success: 'Klaim berhasil!',
    re_enter: 'Masukkan kembali',
    email_repeat: 'Email ini sudah berpartisipasi dalam acara!',
  },
  infinityScroll: {
    end: 'Selesai',
    loading: 'Memuat...',
  },
}
