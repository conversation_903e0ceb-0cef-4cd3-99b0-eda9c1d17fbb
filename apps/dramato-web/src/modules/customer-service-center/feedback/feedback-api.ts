import { httpClient, h5HttpClient, encryptedHttpClientV2 } from 'src/lib/http-client'
import { jsBridge } from 'src/lib/jsbridge'

const client = jsBridge?.inDramawaveApp ? httpClient : h5HttpClient
export const apiSubmitFeedback = (data: {
  description: string
  image_list: string[] // 图片临时路径列表
  email: string
  series_id: string
  episode_id: string
  work_order_id?: number // 工单id
  op_type?: number // 操作类型，1：创建反馈工单（默认类型），2：回复客服消息
}) => {
  // const client = jsBridge?.inDramawaveApp ? httpClient : h5HttpClient
  return client.post<ApiResponse>('drama-api/customer_service_center/feedback', {
    ...data,
    op_type: data.work_order_id ? 2 : 1,
  })
}

export const apiGetProblemTypes = () => {
  // const client = jsBridge?.inDramawaveApp ? httpClient : h5HttpClient
  return client.get<ApiResponse<{ list: M.ProblemType[] }>>('dm-api/customer_service_center/problem_type', {})
}

export const MyFeedbackApi = {
  /** my feedback入口是否有红点展示  */
  hasRedDot: () => {
    return client.get<ApiResponse<{ fd_has_red_dot: boolean }>>('drama-api/customer_service_center/init', {})
  },
  clickRedDot: (d: {
    type: number// 1：问题列表页面my feedback入口点击，2：my feedback列表项点击
    work_order_id?: number
  }) => {
    return client.post<ApiResponse>('drama-api/customer_service_center/feedback/red_dot/click', d)
  },
  getFeedbackList: (next: string) => {
    return client.post<ApiResponse<{
      list: M.MyFeedback.List.Feedback[]
      page_info: PageInfo
    }>>('drama-api/customer_service_center/feedback/list', { next })
  },
  getFeedbackDetail: (work_order_id: number) => {
    return client.post<ApiResponse<M.MyFeedback.Details.Details>>('drama-api/customer_service_center/feedback/detail', { work_order_id })
  },
  commentFeedback: (d: {
    work_order_id?: number
    satisfied_status?: number // 用户评价是否满意，0：未评价，1：满意，10：不满意
  }) => {
    return client.post<ApiResponse>('drama-api/customer_service_center/feedback/comment', d)
  },
}

export const apiGetSubscribeStatus = () => {
  return client.get<ApiResponse<{
    uid: number
    expire_time: number // 时间戳(秒)
    status: number
  }>>('/h5-api/wallet/subscribe/query', {})
}

export const apiSubscribe = (status: number) => {
  return client.post<ApiResponse>('/h5-api/wallet/subscribe/cancel', { status })
}
