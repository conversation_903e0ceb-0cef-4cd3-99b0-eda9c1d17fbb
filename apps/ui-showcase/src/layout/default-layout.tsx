import { createComponent } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { LeftAside } from 'src/modules/left-aside/left-aside'
import { TopBar } from 'src/modules/top-bar/top-bar'
import { Component } from 'vue'
import { RouterView, useRoute } from 'vue-router'
type AdminLayoutOptions = {
  props: {}
}
type RouterViewSlot = {
  Component: Component
}
export const DefaultLayout = createComponent<AdminLayoutOptions>({
  name: 'TopLeftMainRightLayout',
  props: {},
}, props => {
  const route = useRoute()
  if (route.meta.title) {
    document.title = route.meta.title
  }
  return () => (
    <MergeClass baseClass="block bg-fill-2 min-h-screen relative">
      <header class="block bg-fill-4 h-top-bar sticky top-0 shadow z-top-bar">
        <RouterView name="top">
          {{
            default: ({ Component }: RouterViewSlot) => Component ? Component : <TopBar />,
          }}
        </RouterView>
      </header>
      <div class="flex gap-x-4 mx-auto">
        <aside class="shrink-0 grow-0 relative h-[calc(100vh_-_var(--top-bar-height))] overflow-auto">
          <RouterView name="left">
            {{
              default: ({ Component }: RouterViewSlot) => Component ? Component : <LeftAside />,
            }}
          </RouterView>
        </aside>
        <main class="shrink-1 grow pad:min-h-[calc(100vh-var(--top-bar-height))] w-full overflow-auto">
          <RouterView />
        </main>
        <aside class="shrink-0 grow-0">
          <RouterView name="right">
            {{
              default: ({ Component }: RouterViewSlot) => Component ? Component : null,
            }}
          </RouterView>
        </aside>
      </div>
    </MergeClass>
  )
})
