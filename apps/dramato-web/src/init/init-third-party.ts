import { loadScript } from '@skynet/shared'

let initGoogleAdsPromise: Promise<void> | null = null
// <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4028685783554495" crossorigin="anonymous"></script>
export function initGoogleAds() {
  if (initGoogleAdsPromise) {
    return initGoogleAdsPromise
  }
  initGoogleAdsPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      loadScript('https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4028685783554495', {
        crossorigin: 'anonymous',
        onLoad: () => resolve(),
        onError: () => reject(),
      })
    }, 0)
  })
}

let initFacebookSdkPromise: Promise<void> | null = null
// <script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v1.0&appId=****************"> </script>
export function initFacebookSdk() {
  if (initFacebookSdkPromise) {
    return initFacebookSdkPromise
  }
  initFacebookSdkPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      loadScript('https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v2.2&appId=****************', {
        crossorigin: 'anonymous',
        onLoad: () => resolve(),
        onError: () => reject(),
      })
    })
  })
}
let initGoogleSdkPromise: Promise<void> | null = null
// <script src="https://accounts.google.com/gsi/client" async defer></script>
export function initGoogleSdk() {
  if (initGoogleSdkPromise) {
    return initGoogleSdkPromise
  }
  initGoogleSdkPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      loadScript('https://accounts.google.com/gsi/client', {
        onLoad: () => resolve(),
        onError: () => reject(),
      })
    })
  })
}

let initFaceBookYLSdkPromise: Promise<void> | null = null
export function initFaceBookYLSdk() {
  if (initFaceBookYLSdkPromise) {
    return initFaceBookYLSdkPromise
  }
  initFaceBookYLSdkPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      loadScript('https://business.yingliangads.com/track_v3.js?pid=135&u1=81&u2=&u3=&u4=&pt=fb&id=***************', {
        onLoad: () => resolve(),
        onError: () => reject(),
      })
    })
  })
  return initFaceBookYLSdkPromise
}
