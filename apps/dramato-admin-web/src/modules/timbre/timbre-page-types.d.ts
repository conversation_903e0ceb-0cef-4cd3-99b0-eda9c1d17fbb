declare namespace Api {
  namespace Timbre {
    interface ListReqParams {
      title?: string // 剧名
      language?: string
      gender?: -1 | 0 | 1 | 2 | number // 0 未知 1 男 2 女
      page_index?: number // 当前页索引
      page_size?: number // 每页大小
      page?: number
      desc?: string
    }

    interface TimbreListResp {
      timbre_list: TimbreItem[] // 剧列表
      total: number // 总数
    }

    interface TimbreItem {
      timbre_id?: number
      title?: string
      desc?: string
      sample_path?: string
      source?: string
    }
  }
}
