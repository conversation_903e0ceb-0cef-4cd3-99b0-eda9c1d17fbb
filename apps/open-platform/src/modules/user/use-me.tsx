import { ref, watch } from 'vue'
import { userApi } from './user-api'
import { storageKeys } from '../common/constants'
import { tryParseJson } from '@skynet/shared'
import { ElMessage } from 'element-plus'
import { keepError } from 'src/lib/http-client'

// 从 localStorage 中获取 me
const me = ref<M.User>(tryParseJson(localStorage.getItem(storageKeys.me), {
  uid: '', // 用户ID
  phone: '', // 手机号
  avatar: '', // 头像
  nickname: '', // 昵称
  company: '', // 公司名称
  usci: '', // 统一社会信用代码
  licence_pic: '', // 营业执照图片
  auth_status: 0, // 认证状态 0:全部 1: 未认证 2: 已认证 3：认证失
  email: '',
}))
const fetchMe = async () => {
  me.value = await userApi.fetchMe()
}
// 当 me 变化时，存入 localStorage
watch(() => me.value, () => {
  localStorage.setItem(storageKeys.me, JSON.stringify(me.value))
})

const updateMe = async (data: Partial<M.User>) => {
  await userApi.updateMe(data)
    .catch(keepError(error => ElMessage.error(error.message || '提交失败')))
  ElMessage.success('提交成功')
  await fetchMe()
}
export const useMe = () => {
  return {
    me,
    fetchMe,
    updateMe,
  }
}
