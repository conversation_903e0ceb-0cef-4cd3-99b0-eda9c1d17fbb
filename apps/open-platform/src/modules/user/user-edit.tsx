import { defineComponent, onBeforeMount, ref } from 'vue'
import { ElButton, ElInput, ElForm, ElFormItem, ElImage } from 'element-plus'
import { SvgIcon } from '@skynet/ui'
import { Uploader } from '../common/uploader/uploader'
import { useMe } from './use-me'
import { useI18n } from 'vue-i18n'

export default defineComponent({
  name: 'UserPage',
  setup() {
    const { me, fetchMe } = useMe()
    const { t } = useI18n()
    onBeforeMount(() => {
      void fetchMe()
    })

    return () => (
      <div class="p-6">
        <div class="mb-8 flex items-center justify-between">
          <h1 class="text-[22px] font-medium">个人信息</h1>
          <ElButton type="primary">{t('modify')}</ElButton>
        </div>

        <div class="max-w-2xl rounded-lg bg-white p-8">
          <ElForm label-position="left" label-width="80">
            <ElFormItem label={t('avatar')}>
              <div class="flex items-center gap-4">
                {me.value?.avatar
                  ? (
                      <ElImage
                        src={me.value.avatar}
                        class="size-16 rounded-full"
                      >
                        {{
                          error: () => <SvgIcon name="ic_avatar" class="size-16" />,
                          placeholder: () => <SvgIcon name="ic_avatar" class="size-16" />,
                        }}
                      </ElImage>
                    )
                  : (
                      <SvgIcon name="ic_avatar" class="size-16" />
                    )}
                <Uploader
                  accept="png,jpg,jpeg"
                  maxsize={1024 * 1024 * 2}
                  isImage={false}
                  showFileList={false}
                >
                  <ElButton>更换头像</ElButton>
                </Uploader>
              </div>
            </ElFormItem>

            <ElFormItem label={t('nickname')}>
              <ElInput v-model={me.value.nickname} disabled />
            </ElFormItem>

            <ElFormItem label={t('contacts')}>
              <div class="flex items-center gap-2">
                <ElInput v-model={me.value.phone} disabled class="flex-1" />
              </div>
            </ElFormItem>

            <ElFormItem label={t('authentication')}>
              <div class="flex items-center gap-2">
                <span class="text-gray-700">{me.value.company}</span>
                {me.value?.auth_status && (
                  <div class="flex items-center text-sm text-green-500">
                    <SvgIcon name="ic-check-circle" class="mr-1 size-4" />
                    {t('certified')}
                  </div>
                )}
              </div>
            </ElFormItem>
          </ElForm>
        </div>
      </div>
    )
  },
})
