/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import Swiper from '../swiper/swiper.vue'
// import ThreeColumnRecommend from './component/three-column-recommend'
import RecommendationDramaStream from './component/recommendation-drama-stream'
import { useHomePageStore } from './use-home-page-store'
import { computed, onActivated, onMounted, ref, shallowRef, watch } from 'vue'
import { HomePageApi } from './home-page-api'
import InfinityScroll from '../common/infinity-scroll/infinity-scroll'
import { h5Track } from '~/lib/h5-track'
import { useScrollPositionStore } from './use-scroll-position-store'
import { storeToRefs } from 'pinia'
import { argbToRgba } from '~/lib/color'
import { TransitionPresets, useTransition } from '@vueuse/core'

type HomePageOptions = {
  props: {}
}

export const HomePage = createComponent<HomePageOptions>({
  name: 'HomePage',
  props: {},
}, props => {
  const {
    getHomeModuleList,
    getDramaFeeds,
  } = useHomePageStore()

  const {
    homeModuleList,
    hasMoreDramaFeeds,
    dramaFeedsNext,
    loadingDramaFeeds,
    needInitPage,
    needChangeBgColor,
    skipFetch,
  } = storeToRefs(useHomePageStore())

  const homePageRef = ref()
  const scrollPositionStore = useScrollPositionStore()
  const currentBannerIndex = ref(0)

  onMounted(() => {
    if (!needInitPage.value || skipFetch.value || (!homeModuleList.value || homeModuleList.value.length <= 0)) {
      return
    }
    h5Track('h5_performance_home', 'before', 'fetch', {
      load_time: Date.now() - window.startReportTime,
    })
    void HomePageApi.getTabList().then(async rs => {
      if (rs.data?.list && rs.data.list.length > 0) {
        await getHomeModuleList({ tab_key: rs.data.list[0].tab_key, position_index: rs.data.list[0].position_index, first: '' })
        h5Track('h5_performance_home', 'after', 'fetch', {
          load_time: Date.now() - window.startReportTime,
        })
      }
    })
  })

  onActivated(() => {
    // 恢复滚动位置，nexttick 和 requestAnimationFrame 都不能解决, 只能用 setTimeout 了
    setTimeout(() => {
      if (homePageRef.value) {
        document.documentElement.scrollTop = scrollPositionStore.scrollTop
      }
    }, 20)
    h5Track('home', 'page', 'show')
  })

  const bannerList = computed(() => {
    return homeModuleList.value.find(item => item.type === 'banner')
  })
  // const currentBgColor = ref([0,0,0,1])
  const currentBgColor = shallowRef(argbToRgba(bannerList.value?.items?.[currentBannerIndex.value]?.bg_color || ''))

  watch(() => currentBannerIndex.value, () => {
    currentBgColor.value = argbToRgba(bannerList.value?.items?.[currentBannerIndex.value]?.bg_color || '')
  })

  const output = useTransition(currentBgColor, {
    duration: 1000,
    transition: TransitionPresets.easeInOutCubic,
  })

  const colorFull = computed(() => {
    const [r, g, b, a] = output.value
    return `rgba(${r}, ${g}, ${b}, 1)`
  })

  const alphaColor = computed(() => {
    const [r, g, b, a] = output.value
    return `rgba(${r}, ${g}, ${b}, 0)`
  })

  const renderSkeleton = () => (
    <div class="h-full">
      <div class="!h-50 skeleton-animation !w-full rounded-lg" />
      <x-three-column-recommend class="mt-4 block">
        <x-three-column-recommend-content class="gap-1.625 grid grid-cols-3 grid-rows-3">
          {
            [1, 2, 3, 4, 5, 6, 7, 8, 9].map(i => (
              <x-three-column-drama-card class="block overflow-hidden">
                <div class="!h-35 skeleton-animation !w-full rounded-lg" />
                <div class="text-3.25 skeleton-animation mt-1.5 line-clamp-2 h-8 w-full rounded-lg pb-1 leading-4 text-[#cccacb]" />
              </x-three-column-drama-card>
            ))
          }
        </x-three-column-recommend-content>
      </x-three-column-recommend>
    </div>
  )

  return () => (
    <x-home-page
      ref={homePageRef}
      class="pt-13 flex h-full flex-col overflow-auto px-3 pb-3 transition-all duration-700 "
      style={!needChangeBgColor.value ? `background: linear-gradient(180deg, ${colorFull.value} 0%, ${alphaColor.value} 100%) no-repeat; background-size: 100% 250px;` : ''}
      onScroll={(e: any) => {
        if (e.target.scrollTop > 0) {
          needChangeBgColor.value = true
        } else {
          needChangeBgColor.value = false
        }
        scrollPositionStore.setScrollTop(e.target.scrollTop)
      }}>
      {
        needInitPage.value ? (
          renderSkeleton()
        ) : (
          <InfinityScroll
            hasMore={hasMoreDramaFeeds.value}
            next={dramaFeedsNext.value}
            loading={loadingDramaFeeds.value}
            onLoad={getDramaFeeds}
          >
            <Swiper items={bannerList.value?.items || []} onChange={(index: number) => currentBannerIndex.value = index} />
            {/**  三列推荐：封面、标题、卖点标签、内容标签 */}
            {/* <ThreeColumnRecommend /> */}
            {/**  推荐流：剧卡片 */}
            <RecommendationDramaStream />

          </InfinityScroll>
        )
      }
    </x-home-page>
  )
})

export default HomePage
