import { httpClient } from 'src/lib/http-client'

const formatLabelContent = (list: Api.Label.Item[]) => {
  list.map(row => {
    row.lang_content?.map(langContent => {
      if (langContent.language_code === 'cn') row.meaning = langContent.meaning
      row[`${langContent.language_code}_content`] = langContent.content
    })
  })
  return list
}

export const apiGetLabelList = (data: Api.Label.Param) =>
  httpClient.post<Api.Label.Response.List>('/label/list', data, {
    transformResponseData: {
      'data.list': [formatLabelContent],
    },
  })

export const apiCreateLabel = (data: Api.Label.Item) =>
  httpClient.post<ApiResponse<null>>('/label/create_or_update', data)

export const apiDelLabel = (data: { label_id: number, status: number }) =>
  httpClient.post<ApiResponse<null>>('/label/edit_status', data)
