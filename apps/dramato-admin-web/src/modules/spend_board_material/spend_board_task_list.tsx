/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { CreateTableOld, CreateForm, TableColumnOld, Pager, transformTimestamp, transformInteger } from '@skynet/ui'
import { ref } from 'vue'
import { set } from 'lodash-es'
import { apiGetTaskList } from './spend_board_material-api'
import dayjs from 'dayjs'

type InfoModifyPageOptions = {
  props: {}
}
export const SpendBoardTaskList = createComponent<InfoModifyPageOptions>({
  props: {},
}, props => {
  const loading = ref(false)
  const list = ref<M.SpendBoardMaterial.TaskItem[]>([])
  const defaultParams: M.SpendBoardMaterial.TaskRequest = {
    start_time: 0, // 默认为0
    end_time: 0, // 默认为0
    series_resource_id: '', // 资源id或者title
    team: 0, // 1 昆仑 2 盈亮
    page_index: 0,
    page_size: 0,
  }
  const form = ref<M.SpendBoardMaterial.TaskRequest>(defaultParams)
  const total = ref(0)
  const pageInfo = ref({
    page_index: 1,
    page_size: 20,
  })
  const Table = CreateTableOld<M.SpendBoardMaterial.TaskItem>()
  const QueryForm = CreateForm<M.SpendBoardMaterial.TaskRequest>()

  const getList = async () => {
    const params = {
      ...form.value,
      ...pageInfo.value,
      start_time: form.value.start_time || 0,
      end_time: form.value.end_time || 0,
    }
    loading.value = true
    list.value = []
    try {
      const res = await apiGetTaskList(params)
      list.value = res.data?.tasklist || []
      total.value = res.data?.totle || 0
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }

  const columns: TableColumnOld<M.SpendBoardMaterial.TaskItem>[] = [
    ['剧单日期', row => {
      return dayjs(row.deploy_task_time * 1000).format('YYYY-MM-DD')
    }, { class: 'w-[190px] !bg-transparent ' }],
    ['资源ID', 'series_resource_id', { class: 'w-[80px]' }],
    ['资源名称', 'title', { class: 'w-[190px]' }],
    ['语种', 'lang'],
    ['配音类型', row => ['无配音', '配音剧', 'ai配音剧'][row.audio_type]],
    ['制定投放团队', row => {
      return (
        <div class="text-center">
          <span>{row.team_id === 1 ? '昆仑' : '盈量'}</span>
        </div>
      )
    }],
    ['任务备注', 'comment', { class: 'w-[190px]' }],
    ['领取状态', row => {
      return (
        <div class="text-center">
          <span>{row.task_get_status === 1 ? '已领取' : '未领取'}</span>
        </div>
      )
    }],
  ]

  const onQuery = () => {
    pageInfo.value.page_index = 1
    void getList()
  }
  const onReset = () => {
    form.value = defaultParams
    onQuery()
  }

  const onPageChange = (n: number) => {
    pageInfo.value.page_index = n
    void getList()
  }
  const onPageSizeChange = (n: number) => {
    pageInfo.value.page_size = n
    void getList()
  }

  onQuery()

  return () => (
    <div class="space-y-4">
      <NavFormTablePager>{{
        nav: () => (
          <ul>
            <li>任务管理中心</li>
          </ul>
        ),
        form: () => (
          <QueryForm
            class="w-full"
            onSubmit={onQuery}
            onReset={onReset}
            data={form.value}
            onChange={(path, value) => {
              set(form.value, path, value)
            }}
            items={[
              [
                ['任务时间-开始：', 'start_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
                ['任务时间-结束：', 'end_time', { placeholder: '选择时间', rawFormat: 'YYYY-MM-DDTHH:mm:ss+08:00', displayFormat: 'YYYY-MM-DD HH:mm', type: 'datetime' }, { transform: transformTimestamp }],
              ],
              ['资源名称/资源ID：', 'series_resource_id', { type: 'text', placeholder: '请输入资源名称' }],
              ['任务团队', 'team', { type: 'select', options: [{
                value: 1,
                label: '昆仑',
              }, {
                value: 2,
                label: '盈量',
              }], autoInsertEmptyOption: true }, { transform: transformInteger }],
            ]}
          />
        ),
        table: () => (
          <Table
            class="tm-table-fix-first-column tm-table-fix-last-column"
            list={list.value || []}
            columns={columns}
            loading={loading.value}
          />
        ),
        pager: () => (
          <Pager class="justify-end" v-model:page={pageInfo.value.page_index} v-model:size={pageInfo.value.page_size} total={total.value} onUpdate:page={onPageChange} onUpdate:size={onPageSizeChange} />
        ),
      }}
      </NavFormTablePager>
    </div>
  )
})

export default SpendBoardTaskList
