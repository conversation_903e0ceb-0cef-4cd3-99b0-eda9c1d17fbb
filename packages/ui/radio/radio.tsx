import { createComponent, createStringId, fn, JsonSimpleValue, mc, required } from '@skynet/shared'
export * from './radio-group.tsx'

type RadioOptions = {
  props: {
    checked: boolean
    disabled?: boolean
    label: string
    name: string
    class?: string
    value: JsonSimpleValue
  }
  emits: {
    'update:checked': (value: boolean) => void
  }
}

export const Radio = createComponent<RadioOptions>({
  inheritAttrs: false,
  props: {
    checked: false,
    disabled: false,
    label: required,
    name: required,
    value: required,
    class: '',
  },
  emits: {
    'update:checked': fn,
  },
}, (props, { emit }) => {
  const id = createStringId('radio')
  return () => {
    return (
      <div class="flex items-center gap-2 h-8">
        <input
          class={mc('radio radio-sm', props.class)}
          type="radio" id={id} name={props.name} disabled={props.disabled} checked={props.checked}
          onChange={e => {
            const target = e.target as HTMLInputElement
            emit('update:checked', target.checked)
          }}
        />
        <label for={id}>
          {props.label}
        </label>
      </div>
    )
  }
})
