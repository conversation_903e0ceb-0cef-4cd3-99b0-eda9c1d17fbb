/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button, DateTime } from '@skynet/ui'
import set from 'lodash-es/set'
import { NavFormTablePager } from 'src/layouts/nav-form-table-pager'
import { useJDRank } from './use-rank'
import { requiredLabel } from 'src/lib/required-label'
import { useAppAndLangOptions } from '../options/use-app-options'
import { useEpisodeTags } from '../episode-tags/use-episode-tags'
import {  watch } from 'vue'
import { lang } from 'src/lib/constant'

type JDRankOptions = {
  props: {}
}
export const JDRank = createComponent<JDRankOptions>({
  props: {},
}, props => {
  const {
    Form,
    params,
    Table,
    list,
    loading,
    search,
    onCreateBtnClick,
    onEditBtnClick,
  } = useJDRank()

  const { getTags } = useEpisodeTags()

  const { appOptions } = useAppAndLangOptions(() => '', {
    onSuccess: () => {
      params.value.language_version = appOptions.value[0].language[0]
      void search()
    },
    isFree: true,
  })

  watch(() => params.value.language_version, () => {
    if (!params.value.language_version) return
    void getTags(params.value.language_version || '')
  }, { deep: true, immediate: true })

  return () => (
    <NavFormTablePager>
      {{
        nav: () => (
          <ul>
            <li class="flex items-center gap-x-2">
              剧单配置页
            </li>
          </ul>
        ),
        form: () => (
          <Form
            onChange={(path, value) => {
              set(params.value, path, value)
            }}
            resetText=""
            onSubmit={() => {
              void search()
            }}
            data={params.value}
            items={[
              { label: requiredLabel('语言'),
                path: 'language_version',
                input: {
                  type: 'select',
                  autoInsertEmptyOption: false,
                  options: appOptions.value && appOptions.value[0]
                    ? appOptions.value[0].language.map((item: string) => ({
                      label: lang.find(i => i.value === item)?.label || '- 空 -',
                      value: item,
                    }))
                    : [],
                },
              },
            ]}
          />
        ),
        tableActions: () => (
          <div class="w-full flex flex-row justify-between items-start">
            剧单配置页
            <x-button-wrap class="flex justify-between items-center">
              <Button class="btn-primary btn btn-sm" onClick={onCreateBtnClick}>新增剧单</Button>
            </x-button-wrap>
          </div>
        ),
        table: () => (
          <Table loading={loading.value} list={list.value} columns={[
            ['优先级', 'priority', { class: 'w-[100px]' }],
            ['剧单标题', 'title', { class: 'w-[120px]' }],
            ['剧单ID', 'id', { class: 'w-[100px]' }],
            ['生效时间', row => (
              <x-time class="flex items-center gap-x-2">
                <DateTime value={row?.start ? row?.start * 1000 : 0} />-
                <DateTime value={row?.end ? row?.end * 1000 : 0} />
              </x-time>
            ), { class: 'w-[300px]' }],
            ['创建时间', row => <DateTime value={row?.created ? row?.created * 1000 : 0} />, { class: 'w-[150px]' }],
            ['创建人', row => row?.create_user_name, { class: 'w-[100px]' }],
            ['更新时间', row => <DateTime value={row?.updated ? row?.updated * 1000 : 0} />, { class: 'w-[150px]' }],
            ['修改人', row => row?.update_user_name, { class: 'w-[100px]' }],
            ['上架状态', row => (
              Date.now() < (row?.start || 0) * 1000 || Date.now() > (row?.end || 0) * 1000  ? '未上架' : '已上架'
            ), { class: 'w-[80px]' }],
            [<span class="px-3">操作</span>, row => (
              <div class="flex gap-x-2">
                <Button class="btn-outline btn btn-xs" onClick={() => onEditBtnClick(row)}>编辑</Button>
              </div>
            ), {
              class: 'w-[60px]',
            },
            ],
          ]} class="tm-table-fix-last-column"
          />
        ),
      }}
    </NavFormTablePager>
  )
})

export default JDRank
