// import { h5HttpClient } from 'src/lib/http-client'
import { encryptedHttpClient } from 'src/lib/encrypted-http-client'
import { WalletDataInfo } from './profile'

export const apiHistoryList = () => {
  return encryptedHttpClient.get<ApiResponse<Api.MyList.Response>>(`h5-api/drama/view_history`)
}

export const apiWalletMy = () => {
  return encryptedHttpClient.get<ApiResponse<WalletDataInfo>>(`h5-api/wallet/my`)
}
