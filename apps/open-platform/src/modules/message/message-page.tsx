import { createComponent } from '@skynet/shared'
import dayjs from 'dayjs'
import { ref, watch } from 'vue'
import { Empty } from '../common/empty'
import { useMessage } from './use-message'
import { useI18n } from 'vue-i18n'
import { i18n } from 'src/locales'

export const MessagePage = createComponent(null, () => {
  const hasMore = ref(true)
  const { t } = useI18n()
  const { getMessageList, messageList, setMessageReadStatus, messageLoading, messageRequestParams, messageTotal } = useMessage()

  const loadMore = async () => {
    console.log('加载更多', hasMore.value)
    if (messageLoading.value || !hasMore.value) {
      console.log('跳过加载：', { loading: messageLoading.value, hasMore: hasMore.value })
      return
    }

    try {
      messageLoading.value = true
      hasMore.value = false // 暂时先不加载更多，先加载完当前页的数据
      await getMessageList()

      // 提前计算是否还有更多数据
      const currentTotal = messageRequestParams.value.page_index * messageRequestParams.value.page_size
      if (currentTotal >= messageTotal.value) {
        hasMore.value = false
        console.log('没有更多数据', { currentTotal, messageTotal: messageTotal.value })
      } else {
        hasMore.value = true
        messageRequestParams.value.page_index++
      }
    } catch (error) {
      console.error('加载消息失败:', error)
    } finally {
      messageLoading.value = false
    }
  }

  watch(() => messageList.value, async () => {
    const ids = messageList.value.filter(msg => msg.read_status === 0).map(msg => msg.id)
    if (ids.length) {
      const res = await setMessageReadStatus(ids)
      if (res) {
        messageList.value = messageList.value.map(msg => {
          if (ids.includes(msg.id)) {
            msg.read_status = 1
          }
          return msg
        })
      }
    }
  })

  return () => (
    <div class="p-6">
      <h2 class="mb-8 text-[22px] font-medium leading-7">{t('messages')}</h2>
      <div
        v-infinite-scroll={loadMore}
        infinite-scroll-disabled={messageLoading.value || !hasMore.value}
        infinite-scroll-distance={10}
        class="message-list mt-4 max-h-[calc(100vh-200px)] overflow-auto scrollbar-none"
      >
        {messageList.value.length === 0 && !messageLoading.value && (
          <Empty desc="暂无消息" />
        )}

        {!!messageList.value.length && messageList.value.map(message => (
          <div key={message.id} class="message-item border-b-[0.5px] border-b-[#ECE9EC] pb-3 pt-5 hover:bg-gray-50">
            <div class="flex items-start justify-between">
              <h3 class="text-base font-medium text-[#0B080B]">{i18n.global.locale.value == 'en-US' ? message.title_en : message.title}</h3>

            </div>
            <p class="mt-2 text-[#434546]">{i18n.global.locale.value == 'en-US' ? (message.content_en || message.content) : message.content}</p>
            <p class="mt-4 text-right"><span class="text-sm text-[#CCCACB]">{dayjs(message.created * 1000).format('YYYY-MM-DD')}</span></p>
          </div>
        ))}

        {messageLoading.value && (
          <div class="py-4 text-center text-gray-400">加载中...</div>
        )}

        {!messageLoading.value && !hasMore.value && messageList.value.length > 0 && (
          <div class="py-4 text-center text-gray-400">没有更多消息了</div>
        )}
      </div>
    </div>
  )
})

export default MessagePage
