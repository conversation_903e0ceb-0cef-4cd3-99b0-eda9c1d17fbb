import { ref, watch } from 'vue'
import { User, apiPostSmsLogin, apiExternalLogin, apiSSOLogin } from './login-api'
import { showFailToast, showSuccessToast } from '@skynet/ui'
import { tryParseJson } from '@skynet/shared'
import router from 'src/router'
import { keepError } from 'src/lib/http-client'
import { isAxiosError } from 'axios'

export const useLoginStore = () => {
  return {
    userInfo,
    setUserInfo,
    requestLogin,
    logout,
    isValidUser,
    requestSSOLogin,
    requestExternalLogin,
  }
}

const userInfo = ref<User | null | undefined>(tryParseJson<User | null>(localStorage.getItem('userInfo'), null))
const isValidUser = ref(true)
watch(() => userInfo.value, value => {
  localStorage.setItem('userInfo', JSON.stringify(value))
})

const setUserInfo = (data: User) => {
  localStorage.setItem('userInfo', JSON.stringify(data))
  userInfo.value = data
}
const logout = () => {
  userInfo.value = null
  localStorage.removeItem('k_sso_token')
  void router.push('/login')
}

const requestLogin = async (username: string, password: string) => {
  const res = await apiPostSmsLogin({ username, password }).catch(error => {
    if (isAxiosError(error)) {
      showFailToast(error?.response?.data?.message || '登录失败')
    }
    return null
  })
  if (!res) return false
  if (res.code === 200) {
    showSuccessToast('登录成功')
    userInfo.value = res.data
    localStorage.setItem('k_sso_token', res.data?.token || '')
    return true
  }
  return false
}

const requestExternalLogin = async (username: string, password: string, mfa: string) => {
  const res = await apiExternalLogin({ username, password, mfa }).catch(error => {
    showFailToast(error?.response?.data?.message || '登录失败')
    return null
  })
  if (!res) return false
  if (res.code === 200) {
    userInfo.value = res.data
    if (!res.data?.auth_url) {
      showSuccessToast('登录成功')
      localStorage.setItem('k_sso_token', res.data?.token || '')
      return true
    }
    return true
  }
  return false
}

const requestSSOLogin = async (username: string, password: string) => {
  const res = await apiSSOLogin({ username, password }).catch(error => {
    showFailToast(error?.response?.data?.message || '登录失败')
    return null
  })
  if (!res) return false
  if (res.code === 200) {
    localStorage.setItem('k_sso_openid', res.data?.open_id || '')
    localStorage.setItem('k_sso_status', res.data?.status || '')
    return true
  }
  return false
}
