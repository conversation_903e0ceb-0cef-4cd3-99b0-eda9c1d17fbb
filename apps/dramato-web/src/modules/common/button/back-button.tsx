import { createComponent } from '@skynet/shared'
import { MergeClass } from '@skynet/ui'
import { jsBridge } from 'src/lib/jsbridge'
import { back } from './images/images'
import { useRouter } from 'vue-router'
type BackButtonOptions = {
  props: {
    onClick?: null | (() => void)
  }
}
export const BackButton = createComponent<BackButtonOptions>({
  props: {
    onClick: null,
  },
}, props => {
  const router = useRouter()
  const goBack = () => jsBridge.inDramawaveApp ? jsBridge('back') : router.back()
  const onClick = () => {
    props.onClick ? props.onClick() : void goBack()
  }
  return () => (
    <MergeClass baseClass="px-3 py-2.5 drop-shadow-lg" tag="x-back-button" onClick={onClick}>
      <img src={back} class="size-6 object-cover drop-shadow-lg" />
    </MergeClass>
  )
})
