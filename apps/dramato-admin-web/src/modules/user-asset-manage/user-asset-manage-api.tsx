import { httpClient } from 'src/lib/http-client'

export const apiGetUserAssetQuery = (data: M.UserAssetManage.Params) =>
  httpClient.get<ApiResponse<M.UserAssetManage.Response>>('/customer_service_center/asset/query', data)
export const apiGetUserAssetTransfer = (data: M.UserAssetManage.UserAssetTransfer.Params) =>
  httpClient.post<ApiResponse<M.UserAssetManage.UserAssetTransfer.Response>>('/customer_service_center/asset/transfer', data)
