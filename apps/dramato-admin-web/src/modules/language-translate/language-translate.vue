<template>
  <div class="p-4">
    <div class="flex justify-between items-center pb-4 border-b border-gray-300">
      <h1>项目</h1>
      <div>
        <ElButton type="primary" @click="addNewProject">新建项目</ElButton>
      </div>
    </div>
    <div class="mt-4">
      <div class="flex flex-wrap gap-4">
        <div class="w-[300px] border border-gray-300 rounded-md p-4 bg-white" v-for="(item, index) in projectList" :key="index">
          <div class="flex justify-end">
            <ElDropdown>
              <ElButton type="primary" size="small">更多</ElButton>
              <template #dropdown>
                <ElDropdownMenu>
                  <ElDropdownItem @click="editProject(item)">编辑</ElDropdownItem>
                  <ElDropdownItem @click="deleteProject(item)">删除</ElDropdownItem>
                </ElDropdownMenu>
              </template>
            </ElDropdown>
          </div>
          <div class="text-center text-lg font-bold mt-4">{{ item.name }}-{{ platformList.find(pitem => pitem.value === item.platform)?.label }}</div>
          <div class="mt-4 flex justify-between items-center">
            <div class="text-sm">待办任务{{ item.task_unfinished }}条</div>
            <div>
              <ElButton type="primary" link size="small" @click="goToDetail(item)">查看详情</ElButton>
            </div>
          </div>
          <div class="mt-2 text-sm">
            支持语言：{{ item.language.join(',') }}
          </div>
          <div class="mt-2 text-sm">
            未翻译任务占比： {{ item.keys_total ? parseInt((item.keys_unfinished / item.keys_total) * 100) : 0 }}%
          </div>
        </div>
      </div>
    </div>
    <ElDialog v-model="createProjectDialogVisible" :title="createProjectDialogForm.id ? '编辑项目' : '添加项目'" width="500px">
      <div>
        <ElForm :model="createProjectDialogForm" ref="createProjectDialogFormRef" :rules="createProjectDialogFormRules" label-width="100px">
          <ElFormItem label="名称" prop="name">
            <ElInput v-model="createProjectDialogForm.name" placeholder="请输入项目名称" /><span class="text-xs text-gray-500">为您的项目命名</span>
          </ElFormItem>
          <ElFormItem label="平台" prop="platform">
            <ElSelect v-model="createProjectDialogForm.platform" :disabled="createProjectDialogForm.id" placeholder="请选择平台">
              <ElOption v-for="item in platformList" :value="item.value" :label="item.label" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="负责人" prop="operator_id">
            <ElSelect filterable v-model="createProjectDialogForm.operator_id" placeholder="请选择负责人">
              <ElOption v-for="item in adminUserList" :value="item.id" :label="item.name" />
            </ElSelect>
            <span class="text-xs text-gray-500">后台账号</span>
          </ElFormItem>

        </ElForm>
      </div>
      <div class="mt-4 flex justify-end">
        <ElButton @click="createProjectDialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="createProject">{{ createProjectDialogForm.id ? '保存' : '添加' }}</ElButton>
      </div>
    </ElDialog>
  </div>
</template>
<script setup lang="ts">
  import { onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElButton, ElDialog, ElInput, ElForm, ElFormItem, ElSelect, ElOption, ElLoading, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem, ElIcon, ElMessageBox } from 'element-plus'
  import { apiCreateProject, apiGetList, apiDeleteProject, apiGetAdminUserList } from './language-translate-api'

  const initProject = {
    name: '',
    id: 0,
    task_unfinished: 0,
    language: [],
    keys_unfinished: 50,
    keys_total: 50,

  }
  const router = useRouter()
  const adminUserList = ref([])
  const platformList = ref([
    { value: 1, label: 'IOS' },
    { value: 2, label: '安卓' },
    { value: 3, label: 'H5' },
    { value: 4, label: '服务端' },
    { value: 5, label: '前端' },
  ])
  const createProjectDialogFormRef = ref()
  const projectList = ref([])
  const createProjectDialogVisible = ref(false)
  const createProjectDialogForm = ref({
    id: 0,
    name: '',
    platform: '',
    operator_id: '',
  })
  const createProjectDialogFormRules = ref({
    name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }]
  })

  const getProjectList = () => {
    const loading = ElLoading.service({ 
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    apiGetList().then((res) => {
      console.log(res)
      projectList.value = res.data.items
    }).catch((err) => {
      ElMessage.error(err?.response?.data?.err_msg || '请求失败')
    }).finally(() => {
      loading.close()
    })
  }

  const addNewProject = () => {
    createProjectDialogForm.value = {
      id: 0,
      name: '',
      platform: '',
      operator_id: '',
    }
    createProjectDialogVisible.value = true
  }

  const createProject = () => {
    createProjectDialogFormRef.value.validate((valid: boolean) => {
      if (valid) {
        console.log('submit!')
        const loadingInstance = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        apiCreateProject({
          id: createProjectDialogForm.value.id,
          name: createProjectDialogForm.value.name,
          platform: createProjectDialogForm.value.platform,
          operator_id: createProjectDialogForm.value.operator_id || 0,
        }).then((res) => {
          console.log(res)
          if (res.code === 200) {
            ElMessage.success('创建成功')
            createProjectDialogVisible.value = false
            getProjectList()
          } else {
            ElMessage.error(res.err_msg)
          }
        }).catch((error) => {
          console.log(error?.response?.data?.err_msg || '创建失败')
          ElMessage.error(error?.response?.data?.err_msg || '创建失败')
        }).finally(() => {
          loadingInstance.close()
        })
      }
    })
  }

  const getAdminUserList = () => {
    apiGetAdminUserList().then((res) => {
      adminUserList.value = res.data.items
    })
  }

  const deleteProject = (item: any) => {
    console.log(item)
    ElMessageBox.confirm('确定要删除吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      console.log('delete')
      const loading = ElLoading.service({
        lock: true,
        text: '删除中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      apiDeleteProject({
        id: item.id,
      }).then((res) => {
        console.log(res)
        if (res.code === 200) {
          ElMessage.success('删除成功')
          getProjectList()
        } else {
          ElMessage.error(res.err_msg)
        }
      }).catch((error) => {
        console.log(error?.response?.data?.err_msg || '删除失败')
        ElMessage.error(error?.response?.data?.err_msg || '删除失败')
      }).finally(() => {
        loading.close()
      })
    })
  }

  const editProject = (item: any) => {
    createProjectDialogForm.value = {
      id: item.id,
      name: item.name,
      platform: item.platform,
      operator_id: item.operator_id,
    }
    createProjectDialogVisible.value = true
  }

  const goToDetail = (item: any) => {
    router.push(`/language-translate-task/${item.id}`)
  }

  onMounted(() => {
    getProjectList()
    getAdminUserList()
  })


</script>