import { h } from 'vue'
import { getUser, setUser } from 'src/h5_modules/common/user'
import { T as TL } from 'src/h5_modules/i18n/login.i18n'
import { LoginResponse, User } from 'src/h5_modules/login/login'
import AnonymousDialog from 'src/h5_modules/payment/anonymous-dialog.vue'
import { get_k_device_hash_from_cookie } from 'src/lib/device-id.ts'
import { encryptedHttpClient } from 'src/lib/encrypted-http-client'
import { h5Track } from 'src/lib/h5-track'
import { closeDialog, showDialog, showFailToast } from 'vant'
import { useRoute, useRouter } from 'vue-router'

const excludePathList = ['/activity/payment', '/activity/recharge', '/activity/recharge-success']

// 匿名登录
const loginAnonymous = async () => {
  const router = useRouter()
  const params = getUser()
  if (params.auth_key || params.auth_secret) return
  const res = await encryptedHttpClient.post<ApiResponse<User>>('/h5-api/anonymous/login', {
    device_id: get_k_device_hash_from_cookie(),
  }).catch(() => null)

  if (!res || !res.data) {
    showFailToast('No response data received from the server')
    h5Track('h5', 'anonymous_login', 'failed')
  }
  // user.value = res.data;
  setUser(res?.data, true)
  if (excludePathList.some(path => router.currentRoute.value.path.includes(path))) return // 活动页未登录不会重定向
  // if (!isFacebook()) { window.location.href = '/' }
}
// 退出登录
const loginOut = async () => {
  try {
    const res = await encryptedHttpClient.post<ApiResponse<User>>('/h5-api/user/logout', {
      device_id: get_k_device_hash_from_cookie(),
    })
    if (!res || !res.data) {
      showFailToast('No response data received from the server')
    }
    setUser(res.data)
    window.location.href = '/'
  } catch (err) {
    showFailToast('退出登录失败，请重试。')
  }
}
// 注销登录
const loginOff = async () => {
  try {
    const res = await encryptedHttpClient.post<ApiResponse<User>>('/h5-api/user/logoff', {
      device_id: get_k_device_hash_from_cookie(),
    })
    if (!res || !res.data) {
      showFailToast('No response data received from the server')
    }
    setUser(res.data)
  } catch (err) {
    showFailToast('注销登录失败，请重试。')
  }
}

// 第三方登录
const login = async (type: 'facebook' | 'google', token: string, path?: string, cb?: Function) => {
  try {
    const route = useRoute()
    const isFacebook = type === 'facebook'
    const params: LoginResponse = {
      type,
      ...(isFacebook
        ? { is_limit: false, infos: JSON.stringify({ token }) }
        : { infos: JSON.stringify({ token, token_type: 'access_token' }) }),
    }
    const response = await encryptedHttpClient.post<ApiResponse<User>>('/h5-api/user/login', params)

    if (response && response.code !== 200) showFailToast('Login failed')

    if (response && response.data && response.data.success) {
      // user.value = response.data
      // 判断当前登录用户是否与设备绑定的用户是否一致
      const localUser = getUser()
      if (localUser.user_id !== response.data.user_id) {
        const tempUrl = new URL(location.origin + path)
        const video_id = tempUrl.searchParams.get('video_id')
        const video_type = tempUrl.searchParams.get('video_type')
        const series_id = tempUrl.searchParams.get('series_id')
        const pop_scene = series_id ? 'video' : 'store'
        // path: /series/O84ijTeypf?return_to=/&series_id=O84ijTeypf&video_id=123&video_type=1

        h5Track('hint', 'pop', 'show', {
          event: 'hint_pop_show',
          from: path,
          pop_type: 2, // 三方账号被占用
          pop_scene,
          video_id,
          video_type,
          series_id,
          session_id: `${new Date().getTime()}${(response.data.user_id || '')}`,
        })
        void showDialog({
          message: () => h(AnonymousDialog, {
            title: TL.login.attention.ref.value,
            message: TL.login.alreadyLinked.ref.value,
            confirmText: TL.login.signOut.ref.value,
            cancelText: TL.login.maybeLater.ref.value,
            onClose: () => {
              // router.push(decodeHelper(from?.toString() || ''))
              h5Track('hint', 'pop_close', 'click', {
                event: 'hint_pop_close_click',
                from: path,
                pop_type: 2, // 三方账号被占用
                pop_scene,
                video_id,
                video_type,
                series_id,
                session_id: `${new Date().getTime()}${(response.data?.user_id || '')}`,
              })
              setUser(response.data!)
              if (cb) { cb() }
              if (path) {
                window.location.href = path
              }
              closeDialog()
            },
            onConfirm: () => {
              h5Track('hint', 'pop_sign_out', 'click', {
                event: 'hint_pop_sign_out_click',
                from: path,
                pop_type: 2, // 三方账号被占用
                pop_scene,
                video_id,
                video_type,
                series_id,
                session_id: `${new Date().getTime()}${(response.data?.user_id || '')}`,
              })
              if (cb) { cb() }
              if (path) {
                window.location.href = path
              }
              closeDialog()
              // void router.push({ path: '/login', query: { from: route.fullPath } })
            },
          }),
          showConfirmButton: false,
          className: 'p-0',
        })
      } else {
        setUser(response.data)
        if (cb) { cb() }
        if (path) {
          window.location.href = path
        }
      }
      // setUser(response.data)
      // if (cb) { cb() }
      // if (path) {
      //   window.location.href = path
      // }
    }
  } catch (error) {
    showFailToast(`${type} login error: ${error}`)
  }
}

export const userStore = () => {
  return {
    loginAnonymous,
    login,
    loginOut,
    loginOff,
  }
}
