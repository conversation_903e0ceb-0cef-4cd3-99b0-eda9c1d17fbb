import { createComponent } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { ref, watch } from 'vue'
import { DataSettingsItem } from './data-settings-item'
import './custom-markdown.css'
import { useLocale } from 'src/lib/use-locale'
import { jsBridge } from 'src/lib/jsbridge'
import { useRouter } from 'vue-router'
type DataSettingsPageOptions = {
  props: {}
}
export const DataSettingsPage = createComponent<DataSettingsPageOptions>({
  props: {},
}, props => {
  const router = useRouter()
  const language = router.currentRoute.value.query.language as string || 'en'
  const from = router.currentRoute.value.query.from as string
  const isFromRegulations = from === 'regulations'
  const { t } = useLocale(language)
  const defaultDataSettings = {
    personalised: true,
    advertisement: true,
    behavioural: true,
  }
  const consentStr = window.localStorage.getItem('consent')
  const consentFromStorage = consentStr ? JSON.parse(consentStr) as Consent.Consent : null
  if (consentFromStorage) {
    defaultDataSettings.personalised = consentFromStorage.allow_ad_personalization_signals
    defaultDataSettings.advertisement = consentFromStorage.allow_ad_storage !== consentFromStorage.allow_ad_user_data ? true : consentFromStorage.allow_ad_storage
    defaultDataSettings.behavioural = consentFromStorage.allow_analytics_storage
  }
  const dataSettings = ref(defaultDataSettings)
  watch(() => dataSettings.value, () => {
    console.log('set')
    const consent = {
      allow_ad_personalization_signals: dataSettings.value.personalised,
      allow_ad_storage: dataSettings.value.advertisement,
      allow_ad_user_data: dataSettings.value.advertisement,
      allow_analytics_storage: dataSettings.value.behavioural,
    }
    window.localStorage.setItem('consent', JSON.stringify(consent))
    if (!isFromRegulations) {
      void jsBridge('google_analytics_consent_status', consent)
    }
  }, {
    immediate: true,
    deep: true,
  })

  return () => (
    <div class="size-full bg-[var(--black)] text-[var(--white)]">
      <div class="border-b-solid border-b border-b-[var(--grey-10)] px-3 py-5 text-center text-base leading-tight text-[var(--white)]">{t('regulations.aboutYourPrivacy')}</div>
      {[
        {
          name: t('regulations.personalDataName'),
          desc: t('regulations.personalDataDescription'),
          key: 'personalised',
        },
        {
          name: t('regulations.advertisementDataName'),
          desc: t('regulations.advertisementDataDescription'),
          key: 'advertisement',
        },
        {
          name: t('regulations.behaviorsDataName'),
          desc: t('regulations.behaviorsDataDescription'),
          key: 'behavioural',
        },
      ].map(item => (
        <DataSettingsItem
          name={item.name}
          desc={item.desc}
          modelValue={dataSettings.value[item.key as keyof typeof dataSettings.value]}
          onUpdate:modelValue={v => dataSettings.value[item.key as keyof typeof dataSettings.value] = v}
        />
      ))}
      <x-bottom class="z-1000 pc:w-[calc(var(--phone-page-max-width))] pad:w-[calc(var(--phone-page-max-width))] share-bottom-8 fixed bottom-0 flex w-full flex-col items-center justify-center gap-2 bg-[var(--black)] p-3 text-base">
        <Button class="flex h-11 w-full items-center justify-center rounded-lg border-none bg-[var(--brand-6)] text-[var(--white)]" onClick={() => {
          if (isFromRegulations) {
            void router.push({ path: 'rules', query: router.currentRoute.value.query })
          } else {
            if (jsBridge.inDramawaveApp) {
              void jsBridge('back')
            }
          }
        }}
        >{ isFromRegulations ? t('regulations.next') : t('regulations.cancel')}
        </Button>
      </x-bottom>
    </div>
  )
})

export default DataSettingsPage
