/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateForm, CreateTableOld, openDialog, showAlert } from '@skynet/ui'
import { ref } from 'vue'
import { omit } from 'lodash-es'

export const useEpisodeTheatres = () => {
  return {
    Form, 
  }
}

const Form = CreateForm<M.EpisodeTheatres.List.Request>()

const voidTab = {
  id: 0,
  name: '', // 名称
  status: 2, // 状态 1 上架 2 下架
  place_status: 2,
}
