export const zh_cn = {
  // 通用
  common: {
  },
  home: {
    feeds: {
      title: '热门',
    },
  },
  myList: {
    watchHistory: `观看历史`,
    myList: '收藏',
    seeAll: '查看全部',
    noData: '没有相关结果',
  },
  // 分享页
  share: {
    watchNow: '立即观看',
    episodeDialog: {
      title: '加入我，领取额外金币！',
      description: '我已经被这部剧迷住了！',
      tips: '推荐',
      button: '完整观看剧集',
      limit24: '限时：24小时',
    },
    toast: {
      takeOffShelf: '暂无内容',
    },
  },
  'customer-service-center': {
    title: '客服中心',
    description: '描述',
    'description-placeholder': '提供详细信息以帮助我们理解问题',
    'email-placeholder': '输入您的邮箱以接收反馈更新',
    'submit-feedback': '提交',
    'enter-feedback': '其他问题反馈',
    'upload-pictures': '上传图片',
    feedback: '反馈',
    'problem-type': '问题类型',
    'problem-type-placeholder': '请选择',
    'payment-issue-placeholder': '请提供您的ID和订单收据：Google: https://pay.google.com/ (订单号以GPA...开头) iOS: App Store > 苹果ID > 购买历史',
    'content-issue-placeholder': '请提供剧集名称、剧集编号和缺失的字幕语言',
    'ads-issue-placeholder': '请提供您的设备型号、国家和语言设置',
    'technical-issue-placeholder': '请提供您的设备型号、国家和语言设置',
    'other-issue-placeholder': '请提供更多详细信息，包括您的设备型号和国家',
    'i-want-to-give-feedback': '我要反馈',
    'are-you-satisfied-with-this-reply': '您对这次回复满意吗？',
    unsatisfied: '不满意',
    satisfied: '满意',
    myFeedback: '我的反馈',
    'reply-to-feedback': '回复反馈',
    'description-min-length': '请输入6个字符以上的问题描述',
    'problem-type-not-choose': '请选择反馈类型',
    'description-empty': '输入内容不能为空',
  },
  terminate: {
    question: '如何管理我的订阅？',
    desc: '请点击下方按钮进入「合约管理」页面。您可通过点击相关合约之管理选项（例如开关按钮）以更新订阅状态。',
    title: '合约管理',
    expiration: '到期日',
    manageDesc: '订阅合约状态',
  },
  regulations: {
    howUseData: '我们如何使用您的数据',
    content: `如果您在应用追踪透明度权限请求中选择“允许”，我们将使用您的信息和数据来优化我们的服务并为您提供最佳体验，否则我们只会收集数据，不进行任何追踪活动。 如果您同意允许DramaWave及我们的合作伙伴和供应商使用您的设备数据分析您如何使用应用程序、个性化内容推荐并提供更多相关信息服务，请点击“同意并继续”继续。`,
    acceptAll: '全部接受',
    dataSettings: '资料设置',
    next: '下一步',
    cancel: '取消',
    aboutYourPrivacy: '关于您的隐私',
    personalDataName: '个性化数据',
    personalDataDescription: '我们使用这些信息来记住您的选择，并提供个性化广告和内容推荐、通知和提醒等。',
    advertisementDataName: '广告数据',
    advertisementDataDescription: '可能会在您的设备上存储或访问Cookies、设备标识符或其他信息，用于在线广告。用户数据也可能会与我们的广告合作伙伴共享。',
    behaviorsDataName: '行为数据',
    behaviorsDataDescription: '与分析相关的数据，例如Cookies或设备标识符，可能会被存储，用于衡量和分析使用情况、行为以及广告表现。',
    privacyAndTerms: '隐私政策和服务条款',
    referToPrivacy: '请参阅隐私政策',
    referToTerms: '请参阅服务条款',
    agreeAndSave: '同意并保存',
  },
  coins: {
    earnings: {
      title: '我的收益',
      total: '总钻石',
      exchange: '兑换',
      earningsHistory: '收益历史',
      exchangeHistory: '兑换历史',
      noRecords: '无记录',
      last50earnings: '仅显示最近 50 笔收益',
      last50exchanges: '仅显示最近 50 笔兑换记录',
      benefits: '福利',
      gotIt: '明白了',
    },
    exchange: {
      title: '兑换区',
      notOpen: '尚未开放，请保持关注',
      receiveBtn: '快乐收下',
      earnCoins: '赚取钻石',
      redeemRequiredXCoins: '兑换需要{X}钻石',
      exchangeSuccess: '兑换成功',
      cardId: '卡ID',
      exchangeFailed: '兑换失败',
      transferFailedInsufficientStock: '库存不足，请明天早点来',
      gotItBtn: '知道了',
      transferFailedRiskControl: '账号异常，暂时无法兑换',
      tryAgainBtn: '请稍后再试',
      transferConfirm: '确认转账',
      paypalAccountEmail: 'PayPal账户邮箱',
      confirmPaypalAccountEmail: '确认PayPal账户邮箱',
      transferX: '转账 {X}',
      transferXConfirm: '确认转账 {X}',
      transferToYourAccountX: '向你的账户转账 {X}',
      confirmBtn: '确认',
      redeemFailed: '提现失败',
      redeemFailedContent: '知道了',
      transferSuccessTitle: '我们正在汇款！',
      transferSuccessContent: '您的钱将在 5 分钟至 72 小时到账。',
      copySuccess: '复制成功',
      enterEmailError: '请输入有效的电子邮件地址',
      enterEmailValidError: '输入错误，请重新输入',
      emailNotMatchError: '电子邮件地址不匹配，请重新输入',
    },
  },
  meal_check_in: {
    sleeping_link: '睡觉也能赚钻石',
    rules_title: '活动规则',
    benefits_title: '福利',
    rules_1: '1. 每天有四个时间点您可以来打卡自己的餐食，分别是：早餐：07:00-10:00；午餐：11:00-14:00；晚餐：17:00-20:00；夜宵：21:00-24:00。',
    rules_2: '2. 如果您错过了对应的打卡时间，可以通过看一则视频来补领奖励。',
    rules_3: '3. 奖励每天午夜12点刷新，别忘了领取您的奖励。',
  },
  sleep_check_in: {
    earn_link: '记录餐食以赚取',
    rules_title: '活动规则',
    benefits_title: '福利',
    rules_1: '1. 每晚从19点到24点，可以开启"睡觉赚钱"模式。',
    rules_2: '2. 开启后，第二天，睡满8小时后，8点到12点可以领取睡觉奖励。',
    rules_3: '3. 如果您错过了领取奖励的时间，可以在第二天12点到19点观看一则视频来补领奖励。',
  },
  bind_email: {
    title: '输入您的电子邮件地址',
    list_1: '绑定电子邮件地址可获得专属奖励金币',
    list_2: '获取即将举行的活动和特别优惠通知',
    list_3: '率先了解新剧集',
    email_placeholder: '请输入您的电子邮件地址',
    verify: '验证',
    email_error: '请输入有效的电子邮件地址',
    verify_progress: '邮箱验证中',
    verify_success: '领取成功！',
    re_enter: '重新输入',
    email_repeat: '此邮箱已参与过活动！',
  },
  toast: {
    'upload-failed': '上传失败，请重新上传',
    'submit-success': '提交成功',
    'email-incorrect': '邮箱格式不正确',
  },
  not_found: {
    title: '您访问的页面不存在',
  },
  infinityScroll: {
    end: '无',
    loading: '加载中...',
  },
}
