declare namespace M {
  namespace Payment {
    interface CountryInfo {
      country_code: string
      country_name: string
      is_default: boolean
    }

    interface DeliveryDetails {
      quanity: number
      bonus: number
      period: string
      quanity_str: string
    }

    interface Product {
      product_id: number
      product_type: string
      title: string
      currency: string
      currency_symbol: string
      price: number
      sku_id: string
      delivery_details: DeliveryDetails
    }

    interface PayChannel {
      pay_channel: string
      unit_price_coins: string
      icon: string
      product_list: Product[]
      unit_price_amount: string
      unit_price_coins: string
      payment_channel: string
      sub_payment_channel: string
    }

    interface PaymentResponse {
      country_list: CountryInfo[]
      pay_channel_list: PayChannel[]
    }

    interface PaypalPaymentParams {
      product_id: number
      return_url: string
      cancel_url: string
    }
    interface PaypalPaymentResponse {
      redirect_url: string
    }

    interface PaypalPaymentConfirmParams {
      order_id: string
      payer_id: string
    }

    interface AliwallexPaymentParams {
      product_id: number
      return_url: string
      series_key?: string
      episode_key?: string
    }

    interface AliwallexPaymentResponse {
      merchant_name: string // 商户名称
      auto_capture: boolean // 自动 capture
      env: 'demo' | 'dev' | 'staging' | 'prod' // 环境
      currency: string // 货币代码
      amount: string // 金额
      country_code: string // 国家代码
      order_id: string // 订单ID，用于查询订单状态
      intent_id: string
      client_secret: string
      customer_id: string
      mode: string // recurring 为订阅
      success_url?: string
      fail_url?: string
    }

    interface AliwallexPaymentStatusResponse {
      success: boolean
    }
  }
}
