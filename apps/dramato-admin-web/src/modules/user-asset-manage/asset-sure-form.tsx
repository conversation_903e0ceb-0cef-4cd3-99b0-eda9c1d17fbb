/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent, mc } from '@skynet/shared'
import { Button, Icon, showFailToast, DateTime } from '@skynet/ui'
import { useUserAssetManage } from './use-user-asset-manage'
import { set } from 'lodash-es'
import { useRouter } from 'vue-router'
import { apiGetUserAssetTransfer } from './user-asset-manage-api'
import { ref } from 'vue'
type AssetSureFormOptions = {
  props: {
  }
  emits: {
    step: (stepNum: number) => void
  }
}
export const AssetSureForm = createComponent<AssetSureFormOptions>({
  props: {},
  emits: {
    step: () => {},
  },
}, (props, { emit }) => {
  const router = useRouter()
  const {
    FormOne,
    userAssetForm,
    assetMsgList,
    selectedAssetMsg,
    transferResult,
  } = useUserAssetManage()

  const btnLoading = ref(false)
  const nextStep = async () => {
    if (selectedAssetMsg.value.transfer_asset_type_list!.length > 0) {
      try {
        btnLoading.value = true
        set(selectedAssetMsg.value, 'old_user_id', userAssetForm.value.old_user_asset?.user_id)
        set(selectedAssetMsg.value, 'new_user_id', userAssetForm.value.new_user_asset?.user_id)
        const res = await apiGetUserAssetTransfer({
          ...selectedAssetMsg.value,
        })
        if (res.code === 200) {
          emit('step', 2)
          transferResult.value = res.data || {}
        }
      } catch (error: any) {
        console.log(error)

        showFailToast(error.response.data.err_msg || error.response.data.message || '操作失败')
      } finally {
        btnLoading.value = false
      }
    } else {
      showFailToast('请回到上一步，选择要转移的资产哦～')
    }
  }
  const prevStep = () => {
    void router.push('/user-asset-manage')
  }

  return () => (
    <>
      <div>
        <FormOne
          class="mb-3 grid flex-1 grid-cols-1 gap-y-1"
          data={userAssetForm.value}
          hasAction={false}
          onChange={(path, value) => {
            set(userAssetForm.value || {}, path, value)
          }}
          items={[
            [
              '',
              '',
              {
                type: 'custom',
                render: r => {
                  return (
                    <x-push-desc-list class="flex gap-10">
                      <div class=" p-5 w-[500px] bg-gray-100 rounded-lg">
                        <h2 class="font-medium mb-2">老账号：{userAssetForm.value?.old_user_asset?.user_id}</h2>
                        <FormOne
                          class="grid grid-cols-4"
                          hasAction={false}
                          data={userAssetForm.value}
                          items={[
                            ['国家/地区代码：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value?.old_user_asset?.country_code}</div>) } }, { class: 'flex flex-row col-span-4' }],
                            ['账号状态：', '', { type: 'custom', render: r => { return (<div>最后使用时间为 {userAssetForm.value.old_user_asset?.last_active_dt}</div>) } }, { class: 'flex flex-row  col-span-4' }],
                            ['端：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.old_user_asset?.platform == 1 ? 'ios' : userAssetForm.value.old_user_asset?.platform == 2 ? 'android' : '不知'}</div>) } }, { class: 'flex flex-row col-span-4' }],
                          ]}
                        />
                      </div>
                      <div class=" p-5 w-[500px] bg-gray-100 rounded-lg">
                        <h2 class="font-medium mb-2">新账号：{userAssetForm.value.new_user_asset?.user_id}</h2>
                        <FormOne
                          class="grid grid-cols-4"
                          hasAction={false}
                          data={userAssetForm.value}
                          items={[
                            ['国家/地区代码：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value?.new_user_asset?.country_code}</div>) } }, { class: 'flex flex-row col-span-4' }],
                            ['账号状态：', '', { type: 'custom', render: r => { return (<div>最后使用时间为 {userAssetForm.value.new_user_asset?.last_active_dt}</div>) } }, { class: 'flex flex-row  col-span-4' }],
                            ['端：', '', { type: 'custom', render: r => { return (<div>{userAssetForm.value.new_user_asset?.platform == 1 ? 'ios' : userAssetForm.value.new_user_asset?.platform == 2 ? 'android' : '不知'}</div>) } }, { class: 'flex flex-row col-span-4' }],
                          ]}
                        />
                      </div>
                    </x-push-desc-list>

                  )
                },
              },
              {
                class: 'p-4',
              },
            ],
            [
              '确认要转移的资产',
              '',
              {
                type: 'custom',
                render: r => {
                  console.log('r.value', r.value)
                  return (
                    <x-push-desc-list>
                      <div class="block text-gray-400 text-xs">只转移已勾选的部分</div>
                      <div class="p-5 w-[1040px] min-h-[240px] bg-gray-100 rounded-lg">
                        <div class="gap-3 mt-3 flex flex-col">
                          {
                            (assetMsgList.value).map((item, index) => (
                              <label class="cursor-pointer m-2 flex items-center">
                                <input type="checkbox"
                                  disabled="true"
                                  value={item.name}
                                  checked={selectedAssetMsg.value.transfer_asset_type_list?.includes(item.value)}
                                  class="tm-radio mr-1 cursor-pointer checkbox checkbox-primary checkbox-sm" />
                                <span class="inline-block w-40">{item.name}</span>
                                <span class={mc(item.value == 'Subscription' && '!inline-block', 'hidden')}>{userAssetForm.value.old_user_asset?.subscription_status || '--'} (到期时间: <DateTime value={userAssetForm.value.old_user_asset?.subscription_expire_time ? userAssetForm.value.old_user_asset?.subscription_expire_time * 1000 : '--'} />)</span>
                                <span class={mc(item.value == 'Coins' && '!inline-block', 'hidden')}>{userAssetForm.value.old_user_asset?.coins} coins</span>
                                <span class={mc(item.value == 'Bonus&Rewards' && '!inline-block', 'hidden')}>{userAssetForm.value.old_user_asset?.bonus} bonus + {userAssetForm.value.old_user_asset?.rewards} rewards</span>
                                <span class={mc(item.value == 'Coupon' && '!inline-block', 'hidden')}>{userAssetForm.value.old_user_asset?.coupon}</span>
                                <span class={mc(item.value == 'UnlockedEpisode' && '!inline-block', 'hidden')}>{userAssetForm.value.old_user_asset?.unlocked_episode}</span>
                              </label>
                            ),
                            )
                          }
                        </div>
                      </div>
                    </x-push-desc-list>

                  )
                },
              },
              {
                class: 'p-4',
              },
            ],
          ]}
        />
      </div>
      <div class="flex justify-end gap-x-2 w-[1040px]">
        <div class="flex justify-start gap-x-2">
          <Button class="btn btn-sm" onClick={() => {
            prevStep()
          }}
          >上一步
          </Button>
          <Button class="btn btn-primary btn-sm w-[140px] flex flex-row items-center" disabled={btnLoading.value} onClick={() => {
            void nextStep()
          }}
          >
            {btnLoading.value ? <Icon name="line-md:loading-twotone-loop" class="w-5 h-5" /> : null}
            <span>确认资产转移</span>
          </Button>
        </div>
      </div>

    </>

  )
})
