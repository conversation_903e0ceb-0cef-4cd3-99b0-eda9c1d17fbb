import { createComponent, fn, mapToOptions, useValidator } from '@skynet/shared'
import { Button, CreateForm, showAlert, transformInteger } from '@skynet/ui'
import type { Fn } from '@vueuse/core'
import { set } from 'lodash-es'
import { httpClient, keepError } from 'src/lib/http-client'
import { requiredLabel } from 'src/lib/required-label'
import { onMounted, ref } from 'vue'
import { z } from 'zod'
import { prizeTypeMap } from './const'
import { usePrizeList } from './use-prize-list'
type PrizeFormOptions = {
  props: {
    prizeId?: M.Prize['id'] | null
  }
  emits: {
    beforeSave: Fn
    afterSave: Fn
  }
}
export const PrizeForm = createComponent<PrizeFormOptions>({
  props: {
    prizeId: null,
  },
  emits: {
    beforeSave: fn,
    afterSave: fn,
  },
}, (props, { emit }) => {
  const { generateDefaultPrize, findPrize } = usePrizeList()
  const Form = CreateForm<Partial<M.Prize>>()
  const formData = ref<Partial<M.Prize>>(generateDefaultPrize())
  const rules = z.object({
    prize_name: z.string().min(1, '奖品名称不能为空'),
    prize_image: z.string().min(1, '奖品图片不能为空'),
    price: z.number().min(0.01, '奖品价格不能小于0.01'),
    prize_level: z.number().int().min(1, '奖品等级不能小于1'),
    prize_num: z.number().int().min(1, '奖品数量不能小于1'),
    round_number: z.number().int().min(0, '轮次不能小于0'),
    weight: z.number().int().min(0, '权重不能小于0'),
    inventory: z.number().int().min(1, '每日库存不能小于1'),
    available_inventory: z.number().int().min(0, '当日可用库存不能小于0'),
    warning_threshold: z.number().int().min(0, '库存预警值不能小于0'),
    status: z.number().int().min(0, '上架状态不能为空'),
    supply_status: z.number().int().min(0, '供应状态不能为空'),
    is_infinite: z.number().int().min(0, '无限供应不能为空'),
    prize_type: z.number().int().min(1, '奖品类型不能为空'),
  })
  const { validateAll, error } = useValidator(formData, rules)
  onMounted(() => {
    if (!props.prizeId) return
    const local = findPrize(props.prizeId)
    if (local) {
      formData.value = local
    }
  })

  return () => (
    <x-prize-create class="block">
      <Form data={formData.value}
        error={error.value}
        onChange={(path, value) => set(formData.value, path, value)}
        class="flex flex-col"
        onSubmit={() => onSubmit().then(() => emit('afterSave'))}
        actions={() => (
          <Button class="btn btn-primary btn-sm" type="submit">
            {props.prizeId ? '更新' : '创建'}
          </Button>
        )}
        items={[
          [
            [requiredLabel('奖品名称'), 'prize_name', { type: 'text' }],
            ['奖品价格', 'price', { type: 'money', maxlength: 10 }],
          ],
          ['奖品图片', 'prize_image', { type: 'image' }],
          [
            ['上架状态', 'status', { type: 'select', autoInsertEmptyOption: false,
              options: [{ label: '已上架', value: 1 }, { label: '未上架', value: 0 }] }, { transform: transformInteger }],
            ['供应状态', 'supply_status', { type: 'select', autoInsertEmptyOption: false,
              options: [{ label: '供应中', value: 1 }, { label: '未开始供应', value: 0 }] }, { transform: transformInteger }],
            ['无限供应', 'is_infinite', { type: 'select', autoInsertEmptyOption: false,
              options: [{ label: '是', value: 1 }, { label: '否', value: 0 }] }, { transform: transformInteger }],
            ['奖品类型', 'prize_type', {
              type: 'select', autoInsertEmptyOption: false,
              options: mapToOptions(prizeTypeMap),
            }, { transform: transformInteger }],
          ],
          [
            ['奖品等级', 'prize_level', { type: 'number' }, { transform: transformInteger }],
            ['奖品数量', 'prize_num', { type: 'number' }, { transform: transformInteger }],
          ],
          [
            ['轮次', 'round_number', { type: 'number' }, { transform: transformInteger, hint: <span>0表示不限轮次</span> }],
            ['权重', 'weight', { type: 'number' }, { transform: transformInteger }],
          ],
          formData.value.is_infinite === 0 && [
            ['每日库存', 'inventory', { type: 'number' }, { transform: transformInteger }],
            ['当日可用库存', 'available_inventory', { type: 'number' }, { transform: transformInteger }],
            ['库存预警值', 'warning_threshold', { type: 'number' }, { transform: transformInteger }],
          ],
        ]} />
    </x-prize-create>
  )
  async function onSubmit() {
    const exclude = formData.value.is_infinite === 1 ? ['inventory', 'available_inventory', 'warning_threshold'] : []
    if (!validateAll({ exclude })) {
      return Promise.reject()
    }
    emit('beforeSave')
    await httpClient.post<ApiResponse<M.Prize>>('/draw/prize/save', { ...formData.value, id: props.prizeId })
      .catch(keepError(() => showAlert('提交失败，请稍后再试', 'error')))
    showAlert('提交成功', 'success')
  }
})

export default PrizeForm
