/* eslint-disable @typescript-eslint/no-explicit-any */
import { createComponent } from '@skynet/shared'
import { Button } from '@skynet/ui'
import { set } from 'lodash-es'
import { requiredLabel } from 'src/lib/required-label'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { apiGetFreeAds } from './strategy-group-ad-api'
import { useStrategyGroupAdStore } from './strategy-group-ad-store'

export const StrategyGroupIapCreateSolutionPage = createComponent(null, props => {
  const { formData, Form, stepErrorForSolution, validateStepTwoForIap, resetSolutionFormData } = useStrategyGroupAdStore()

  const router = useRouter()
  const route = useRoute()
  const isViewMode = computed(() => route.query.mode === 'view')

  const onSubmit = () => {
    if (!validateStepTwoForIap()) return console.log('stepErrorForSolution', stepErrorForSolution.value)

    void router.push({ path: './plan', query: { ...route.query, id: formData.value.id } })
  }

  const rewardAds = ref<Api.OriginAD.Item[]>([])
  const originAds = ref<Api.OriginAD.Item[]>([])
  const homeFeedNativeInterstitialAds = ref<Api.OriginAD.Item[]>([])
  const homeFeedRewardAds = ref<Api.OriginAD.Item[]>([])
  const welfareDetailRewardAds = ref<Api.OriginAD.Item[]>([])
  const unlockRewardDetailRewardAds = ref<Api.OriginAD.Item[]>([])
  onMounted(() => {
    void Promise.all([
      apiGetFreeAds('theatrical_detail_reward', formData.value.platform || ''),
      apiGetFreeAds('theatrical_detail_native', formData.value.platform || ''),
      apiGetFreeAds('home_feed_native_interstitial', formData.value.platform || ''),
      apiGetFreeAds('home_feed_reward', formData.value.platform || ''),
      apiGetFreeAds('welfare_detail_reward', formData.value.platform || ''),
      apiGetFreeAds('unlock_reward', formData.value.platform || ''),
    ])
      .then(([rewardAdsData, originAdsData, homeFeedNativeInterstitialAdsData, homeFeedRewardAdsData, welfareDetailRewardAdsData, unlockRewardDetailRewardAdsData]) => {
        rewardAds.value = rewardAdsData.data?.list || []
        originAds.value = originAdsData.data?.list || []
        homeFeedNativeInterstitialAds.value = homeFeedNativeInterstitialAdsData.data?.list || []
        homeFeedRewardAds.value = homeFeedRewardAdsData.data?.list || []
        welfareDetailRewardAds.value = welfareDetailRewardAdsData.data?.list || []
        unlockRewardDetailRewardAds.value = unlockRewardDetailRewardAdsData.data?.list || []
      })
  })

  return () => (
    <x-strategy-group-iap-create-solution-page class="flex flex-col gap-4 p-8">
      <Form
        class="flex flex-col gap-4"
        data={formData.value}
        error={stepErrorForSolution.value}
        onChange={(path, value) => {
          set(formData.value, path, value)
          if (!formData.value.ad_config) {
            return
          }
          if (path === 'scene_type') {
            if (!formData.value.scene_type?.includes('theatrical_detail_reward')) {
              delete formData.value.ad_config.theatrical_detail_reward
            }
            if (!formData.value.scene_type?.includes('theatrical_detail_native')) {
              delete formData.value.ad_config.theatrical_detail_native
            }
            if (!formData.value.scene_type?.includes('home_feed_native_interstitial')) {
              delete formData.value.ad_config.home_feed_native_interstitial
            }
            if (!formData.value.scene_type?.includes('home_feed_reward')) {
              delete formData.value.ad_config.home_feed_reward
            }
            if (!formData.value.scene_type?.includes('welfare_detail_reward')) {
              delete formData.value.ad_config.welfare_detail_reward
            }
            if (!formData.value.scene_type?.includes('unlock_reward')) {
              delete formData.value.ad_config.unlock_reward
            }
          }
        }}
        onReset={() => {
          resetSolutionFormData()
        }}
        onSubmit={onSubmit}
        items={[
          [requiredLabel('广告类型'), 'scene_type', {
            type: 'multi-select',
            options: [
              { label: '内流激励', value: 'theatrical_detail_reward', disabled: isViewMode.value },
              { label: '内流原生+插屏', value: 'theatrical_detail_native', disabled: isViewMode.value },
              { label: '外流原生+插屏', value: 'home_feed_native_interstitial', disabled: isViewMode.value },
              { label: '外流激励', value: 'home_feed_reward', disabled: isViewMode.value },
              { label: '福利页激励', value: 'welfare_detail_reward', disabled: isViewMode.value },
              { label: '解锁激励', value: 'unlock_reward', disabled: isViewMode.value },
            ],
          }],
          !!formData.value.scene_type?.includes('theatrical_detail_reward') && [
            requiredLabel('内流-激励广告方案'),
            'ad_config.theatrical_detail_reward',
            {
              type: 'multi-select',
              options: (rewardAds.value || []).map(item => {
                return {
                  label: item.name || '',
                  value: item.id || 0,
                }
              }) || [],
            },
          ],
          !!formData.value.scene_type?.includes('theatrical_detail_native') && [
            requiredLabel('内流-原生/插屏广告方案'),
            'ad_config.theatrical_detail_native',
            {
              type: 'multi-select',
              options: (originAds.value || []).map(item => {
                return {
                  label: item.name || '',
                  value: item.id || 0,
                }
              }) || [],
            },
          ],
          !!formData.value.scene_type?.includes('home_feed_native_interstitial') && [
            requiredLabel('外流-原生/插屏广告方案'),
            'ad_config.home_feed_native_interstitial',
            {
              type: 'multi-select',
              options: (homeFeedNativeInterstitialAds.value || []).map(item => {
                return {
                  label: item.name || '',
                  value: item.id || 0,
                }
              }) || [],
            },
          ],
          !!formData.value.scene_type?.includes('home_feed_reward') && [
            requiredLabel('外流-激励广告方案'),
            'ad_config.home_feed_reward',
            {
              type: 'multi-select',
              options: (homeFeedRewardAds.value || []).map(item => {
                return {
                  label: item.name || '',
                  value: item.id || 0,
                }
              }) || [],
            },
          ],
          !!formData.value.scene_type?.includes('welfare_detail_reward') && [
            requiredLabel('福利页激励广告方案'),
            'ad_config.welfare_detail_reward',
            {
              type: 'multi-select',
              options: (welfareDetailRewardAds.value || []).map(item => {
                return {
                  label: item.name || '',
                  value: item.id || 0,
                }
              }) || [],
            },
          ],
          !!formData.value.scene_type?.includes('unlock_reward') && [
            requiredLabel('解锁-激励广告方案'),
            'ad_config.unlock_reward',
            {
              type: 'multi-select',
              options: (unlockRewardDetailRewardAds.value || []).map(item => {
                return {
                  label: item.name || '',
                  value: item.id || 0,
                }
              }) || [],
            },
          ],
        ]}
        actions={() => (
          <div class="flex justify-between items-center gap-x-4">
            <Button class="btn btn-sm" type="button" onClick={() => {
              void router.push({ path: `/free-ad-strategy-group/iap/create/target`, query: { ...route.query } })
            }}
            >上一步
            </Button>
            {isViewMode.value ? null : <Button class="btn btn-sm mr-auto" type="reset">重置</Button>}
            {isViewMode.value ? null : <Button class="btn btn-primary btn-sm" type="submit" onClick={onSubmit}>下一步</Button>}
          </div>
        )}
      />

    </x-strategy-group-iap-create-solution-page>
  )
})
export default StrategyGroupIapCreateSolutionPage
