import { isFacebook, isIos } from './ua'
import { useClipboard } from '@vueuse/core'
import { inApp, jsBridge } from 'src/lib/jsbridge'

let timer: string | number | undefined

/**
 * 检测应用是否安装
 * 唤端成功后，打开app并跳转到对应的详情页
 * 如果唤端失败，2s后执行fallback回调函数
 * @param urlScheme 应用 URL Scheme
 * @param fallback 应用未安装时的回调函数
 */
export const goToApp = (urlScheme: string) => {
  const googlePlayLink = 'https://play.google.com/store/apps/details?id=com.freereels.app'
  const iosAppStoreLink = `https://apps.apple.com/us/app/dramareels-dramas-and-series/id6738081517`
  const { copy } = useClipboard()
  copy(urlScheme)
    // facebook直接打开google play
  if (isFacebook()) {
    window.location.href = googlePlayLink
  } else {
    window.location.href = urlScheme
    timer = window.setTimeout(() => {
      if (isIos()) {
        window.location.href = iosAppStoreLink
      }else{
        window.location.href = googlePlayLink
      }
    }, 3000)

    // 监听页面的 visibilitychange 事件
    // 监听页面的可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)
  }
}



export const goToAppNoStore = (urlScheme: string) => {
  console.log('进入函数', urlScheme)
  const googlePlayLink = 'https://play.google.com/store/apps/details?id=com.freereels.app'
  const { copy } = useClipboard()
  copy(urlScheme)
  console.log('复制完成')
  if (isIos()) {
    console.log('是IOS')
    // iOS使用Universal Link
    if(inApp){
      jsBridge('router', { url: urlScheme })
    }else{
      window.location.href = urlScheme
    }
  } else {
    console.log('不是IOS')
    // facebook直接打开google play
    if (isFacebook()) {
      console.log('是fackbook', googlePlayLink)
      window.location.href = googlePlayLink
    } else {
      console.log('不是fackbook', urlScheme)
      window.location.href = urlScheme
      timer = window.setTimeout(() => {
        console.log('timeout完成', googlePlayLink)
        window.location.href = googlePlayLink
      }, 3000)

      // 监听页面的 visibilitychange 事件
      // 监听页面的可见性变化
      document.addEventListener('visibilitychange', handleVisibilityChange)
    }
  }
}

const handleVisibilityChange = () => {
  clearTimeout(timer) // 如果页面变为可见，清除定时器
}
