import { Button, CreateForm, DateTime, openDialog, Pager, transformInteger, transformTimestamp } from '@skynet/ui'
import { CreateTable } from '@skynet/ui/table/create-table'
import { set } from 'lodash-es'
import { httpClient } from 'src/lib/http-client'
import { usePaged } from 'src/lib/use-paged'
import { deliveryStatusMap, prizeTypeMap, trackingStatusMap } from './const'
import TrackingForm from './tracking-form'

const { items: prizeRecordList, loading: fetchingPrizeRecordList, fetch: fetchPrizeRecordList, total, page, pageSize,
  query: tableQuery,
} = usePaged<M.PrizeRecord>({
  initialPage: 1,
  initialPageSize: 20,
  fetch: ({ page, page_size, ...query }) =>
    httpClient.post<ApiPagedResponse<M.PrizeRecord>>('/draw/prize/record', { offset: (page - 1) * page_size, size: page_size, ...query }),
})

type Query = {
  start_time?: number
  end_time?: number
  user_id?: M.PrizeRecord['user_id']
  prize_type?: string
}
const Table = CreateTable<M.PrizeRecord>()
const TableForm = CreateForm<Query>()
export function usePrizeRecordList() {
  return {
    fetchPrizeRecordList,
    renderTable,
    renderTableSearch,
    renderPager,
    findRecord,
  }
}

function renderTable() {
  return (
    <Table
      class="w-full [&_x-cell.odd]:bg-gray-100"
      list={prizeRecordList.value}
      loading={fetchingPrizeRecordList.value}
      columns={[
        ['ID', 'id'],
        ['用户ID', 'user_id'],
        ['奖品名称', 'prize_name'],
        ['奖品类型', record => prizeTypeMap[record.prize_type] || record.prize_type],
        ['奖品数量', 'prize_num'],
        ['奖品等级', 'prize_level'],
        ['中奖时间', record => <DateTime value={record.won_at * 1000} />],
        ['发货状态', record => deliveryStatusMap[record.delivery_status] || record.delivery_status],
        ['物流信息', record => record.tracking_info ? (
          <x-tracking>
            <div>状态: {trackingStatusMap[record.tracking_info.tracking_status] || record.tracking_info.tracking_status}</div>
            <div>单号: {record.tracking_info.tracking_no}</div>
            <div>公司: {record.tracking_info.tracking_company}</div>
            <div>链接: <a href={record.tracking_info.tracking_url} target="_blank" rel="noopener noreferrer">查看</a></div>
          </x-tracking>
        ) : <span class="text-gray-400">虚拟物品</span>, { width: 'auto' }],
        ['操作', record => (
          record.prize_type === 3
          && <Button class="btn btn-primary btn-xs" onClick={() => onEditTracking(record.id)}>更新物流信息</Button>
        ), { class: 'sticky right-0 bg-white shadow-lg' }],
      ]} />
  )
}
function renderTableSearch() {
  return (
    <TableForm
      onChange={(path, value) => set(tableQuery.value, path, value)}
      onReset={() => {
        tableQuery.value = generateDefaultTableQuery()
        void fetchPrizeRecordList({ page: 1 })
      }}
      submitText="搜索"
      onSubmit={() => fetchPrizeRecordList()} data={tableQuery.value} items={[
        ['用户ID', 'user_id', { type: 'text' }],
        ['奖品类型', 'prize_type', { type: 'select', options: Object.entries(prizeTypeMap).map(([value, label]) => ({ value, label })) },
          { transform: transformInteger }],
        ['中奖开始时间', 'start_time', { type: 'datetime' }, { transform: transformTimestamp }],
        ['中奖结束时间', 'end_time', { type: 'datetime' }, { transform: transformTimestamp }],
      ]}
    />
  )
}

function onEditTracking(recordId: M.PrizeRecord['id']) {
  const close = openDialog({
    title: '更新物流信息',
    body: () => (
      <TrackingForm
        recordId={recordId}
        onBeforeSave={() => {}}
        onAfterSave={() => { close(); void fetchPrizeRecordList() }}
      />
    ),
    onClickOutside: () => { close() },
  })
}

function renderPager() {
  if (total.value <= 0) return null
  return (
    <Pager class="justify-end" page={page.value} size={pageSize.value} total={total.value}
      onUpdate:page={page => fetchPrizeRecordList({ page })}
      onUpdate:size={page_size => fetchPrizeRecordList({ page_size })}
    />
  )
}

function findRecord(recordId?: M.PrizeRecord['id']) {
  if (!recordId) return null
  return prizeRecordList.value.find(record => record.id === recordId)
}

function generateDefaultTableQuery(): Query {
  return {
    prize_name: undefined,
    prize_level: undefined,
  }
}
