packages:
  - "packages/*"
  - "apps/*"

catalog:
  "@tiptap/core": "2.6.6"
  "@tiptap/extension-document": "2.6.6"
  "@tiptap/extension-mention": "2.6.6"
  "@tiptap/extension-paragraph": "2.6.6"
  "@tiptap/extension-placeholder": "2.6.6"
  "@tiptap/extension-text": "2.6.6"
  "@tiptap/pm": "2.6.6"
  "@tiptap/starter-kit": "2.6.6"
  "@tiptap/suggestion": "2.6.6"
  "@tiptap/vue-3": "2.6.6"
  "@types/lodash-es": "4.17.12"
  "@types/node": "20.14.8"
  "@types/markdown-it": "14.1.2"
  "@unocss/postcss": "0.62.3"
  "@unocss/preset-wind": "0.62.3"
  "@vitejs/plugin-vue-jsx": "4.0.1"
  "@vitejs/plugin-vue": "5.0.5"
  "@vueuse/core": "13.3.0"
  "@vueuse/integrations": "13.3.0"
  "ali-oss": "6.21.0"
  "axios": "1.7.9"
  "@types/ali-oss": "6.16.11"
  "autoprefixer": "10.4.19"
  "callapp-lib": "3.5.3"
  "choices.js": "11.0.0"
  "class-variance-authority": "0.7.0"
  "crypto-js": "4.2.0"
  "core-js": "3.38.1"
  "daisyui": "4.12.10"
  "date-fns": "3.6.0"
  "dayjs": "1.11.12"
  "echarts": "5.5.1"
  "eslint": "9.5.0"
  "eslint-plugin-vue": "9.32.0"
  "eslint-plugin-tailwindcss": "3.18.0"
  "github-markdown-css": "5.6.1"
  "globals": "15.8.0"
  "html2canvas": "1.4.1"
  "js-cookie": "3.0.5"
  "@types/js-cookie": "3.0.6"
  "js-md5": "0.8.3"
  "@tailwindcss/vite": "4.1.6"
  "lodash-es": "4.17.21"
  "lottie-web": "5.12.2"
  "monaco-editor": "0.52.2"
  "pinia": "2.1.7"
  "postcss-import": "16.1.0"
  "postcss-nested": "6.2.0"
  "tailwind-scrollbar": "3.1.0"
  "postcss": "8.4.41"
  "shaka-player": "4.11.7"
  "remeda": "2.3.0"
  "@types/sortablejs": "1.15.8"
  "tailwind-merge": "2.3.0"
  "@iconify/vue": "4.1.2"
  "tailwindcss": "3.4.10"
  "@tweenjs/tween.js": "25.0.0"
  "typescript": "5.5.4"
  "@unscatty/unocss-preset-daisy": "1.0.0"
  "unocss": "0.62.3"
  "vconsole": "3.15.1"
  "vh-check": "2.0.5"
  "vite-bundle-analyzer": "0.10.3"
  "vite-plugin-markdown": "2.2.0"
  "vite-plugin-svg-icons": "2.0.1"
  "vite": "5.4.3"
  "vue-multiselect": "3.0.0"
  "vue-router": "4.4.0"
  "vue-tsc": "2.0.22"
  "vue": "3.5.3"
  "zod": "3.23.8"
  "zx": "8.1.4"
  "markmap-lib": "0.17.0"
  "markmap-view": "0.17.0"
  "markdown-it": "14.1.0"
  "eslint-plugin-format": "1.0.1"
