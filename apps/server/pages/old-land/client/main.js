const eventReported = {
  pageView: false,
  viewContent: false,
  startTrial: false,
}
let buffer = []
let reportNowTimer = null
const environment = window.location.hostname.indexOf('test') >= 0 ? 'development' : 'prod'
const backendUrl = 'https://trace.mydramawave.com/client_track'
const backendUrlTest = 'https://trace-test.mydramawave.com/client_track'
const query = window.land.query
addReport({
  event: 'ad_page_show',
  event_info: JSON.stringify(getReportData())
})
window.addEventListener('load', function () {
  addReport({
    event: 'ad_page_view',
    event_info: JSON.stringify(getReportData())
  })
  reportNow()
})
document.addEventListener('DOMContentLoaded', () => {
  fetchSeries(query.content_id).then(null, () => {
    addReport({
      event: 'ad_page_load_series_failed',
      event_info: JSON.stringify(getReportData())
    })
    reportNow()
  })
  fetchClientInfo().then(ip => {
    window.land.ip = ip
  })
  const button = document.getElementById('app');
  button.addEventListener('click', function () {
    try {
      if (query.clipboard !== 'no') {
        writeToClipboard()
      }
      addReport({
        event: 'ad_page_click',
        event_info: JSON.stringify(getReportData({ button: 'continue' }))
      })
      reportNow(true)
    } catch (err) { }
    goToApp()
  });
  const minorImages = document.querySelector('#minorImages');
  setTimeout(() => {
    minorImages.style.display = 'grid';
    const images = minorImages.querySelectorAll('img');
    images.forEach(img => {
      img.src = img.dataset.src;
    });
  }, 1000)
  window.onbeforeunload = function () {
    reportNow()
  }
});

/************************************************************
   * Helpers
   ************************************************************/

function writeToClipboard() {
  const qs = createQueryString({
    content_id: query.content_id,
    fbclid: query.fbclid,
    external_id: undefined,
    ip: window.land.ip,
    ua: window.navigator.userAgent,
    media_source: query.media_source,
    pid: 'wave_ads',
    af_sub1: getCookie('_fbp'),
    af_sub2: getCookie('_fbc'),
    af_sub3: window.land.ip,
    af_sub4: window.navigator.userAgent,
    af_sub5: window.land.pixel_id,
    c: query.c,
    af_c_id: query.af_c_id,
    af_ad: query.af_ad,
    af_ad_id: query.af_ad_id,
    af_adset_id: query.af_adset_id,
    af_channel: query.af_channel,
    af_siteid: '1916',
    af_sub_siteid: '0_0_0',
  })
  try {
    ClipboardJS.copy([
      3,
      'https://mydramawave.com?redirect=/detail?id=' + query.content_id,
      qs,
    ].join(' '))
  } catch (e) {
    console.error('Error copying to clipboard:', e)
  }
}

function goToApp() {
  const links = generateLinks()
  window.location.href = links.oneLink
}
// https://api-test.mydramawave.com/dm-api/h5/series/info?series_id=QJHm97fn1X
function fetchSeries(seriesId) {
  const el = document.getElementById('_data_series_info')
  const string = el ? el.innerText : ''
  const data = string ? JSON.parse(string) : null
  if (data) {
    return Promise.resolve(data)
  }
  const baseUrl = environment === 'development' ? 'https://api-test.mydramawave.com' : 'https://api.mydramawave.com'
  const temp_t = Date.now()
  return fetch(baseUrl + '/dm-api/drama/share/series_info?series_id=' + seriesId)
    .then(res => {
      addReport({
        event: 'ad_page_load_series_time',
        event_info: JSON.stringify(getReportData({ load_time: Date.now() - temp_t })),
        load_time: Date.now() - temp_t
      })
      reportNow()
      return res.json()
    })
    .then(res => res.data.series_info)
}
function fetchClientInfo() {
  const el = document.getElementById('_data_ip')
  const string = el ? el.innerText : ''
  const data = string ? JSON.parse(string) : null
  if (data) {
    return Promise.resolve(data)
  }
  return fetch('https://trace.mydramawave.com/yl/c-info', { method: 'POST' }).then(r => r.json())
    .then(j => j.code === 200 ? j.data : Promise.reject())
}

function addReport(data) {
  if (eventReported[data.event]) return
  eventReported[data.event] = true
  buffer.push({
    channel: environment === 'development' ? 'devtest' : 'prod',
    environment,
    href: window.location.href,
    page_title: document.title,
    referrer: document.referrer,
    screen_height: window.screen.height.toString(),
    screen_width: window.screen.width.toString(),
    user_agent: window.navigator.userAgent,
    time: Date.now().toString(),
    client_height: window.innerHeight.toString(),
    client_width: window.innerWidth.toString(),
    os_name: getOSInfo().name,
    os_version: getOSInfo().version,
    user_source: 'web',
    ...data,
  })
}

function reportNow(force) {
  if (reportNowTimer) {
    window.clearTimeout(reportNowTimer);
  }
  const report = () => {
    reportNowTimer = null
    const isDev = environment === 'development'
    if (buffer.length === 0) return
    const dataToSend = [...buffer]
    buffer = []
    const blob = new Blob([JSON.stringify(dataToSend)], { type: 'application/json' })
    navigator.sendBeacon(isDev ? backendUrlTest : backendUrl, blob)
  }
  if (force) {
    report()
  } else {
    reportNowTimer = window.setTimeout(report, 300)
  }
}

function getReportData(more) {
  more = more || {}
  return {
    pixel: window.land.pixel_id,
    content_id: query.content_id,
    fbc: getCookie('_fbc'),
    fbp: getCookie('_fbp'),
    campaign_id: query.af_c_id,
    adset_id: query.af_adset_id,
    ad_id: query.af_ad_id,
    ip: window.land.ip,
    ua: window.navigator.userAgent,
    os_version: getOSInfo().version,
    model: getDeviceType(),
    timestamp: Date.now(),
    ...more,
  }
}
/**
   * 通过对象生成查询参数字符串，所有 value 自动转义
   * @param queries 要设置的查询参数对象
   */
function createQueryString(queries) {
  const p = new URLSearchParams()
  Object.entries(queries).forEach(([key, value]) => {
    value && p.set(key, value.toString())
  })
  return p.toString()
}

function generateLinks() {
  // dramawave://dramawave.app?redirect=/datail?id=MhC8rlRsV&timestamp=1678901234567&campaign id=120216175334570464&
  // adset id=120218590781410617&ad id=120217087272680464&fbp=fb.1.1596403881668.1116446470&fbc=fb.1.1554763741205.AbCdEfGhIiKlMnOpOrStUvWxYz&channel=facebook
  const os = getOSInfo()
  // "redirect=/detail?id=7zlsnH39p2&pixel_id=2246766269032711&dpsource=W2A&timestamp=1746605757791&campaign_id={{campaign.id}}&adset_id={{adset.id}}&ad_id={{ad.id}}&fbp=fb.1.1742562023858.660926066320256771&fbc=fb.1.1745307988714.IwY2xjawJ0KKNleHRuA2FlbQIxMAABHlW2iefxUSOoS8B7ejBnfQM4ymWSMltKUxo19OYooyrLp2KsoeOJeUeDmG3t_aem_6m6oesRAjYiL27vggektZQ&channel=Facebook&af_site_id=1916&af_sub_site_id=0_0_0&af_sub5=0"
  const appQs = createQueryString({
    id: query.content_id,
    pixel_id: window.land.pixel_id,
    timestamp: Date.now(),
    campaign_id: query.af_c_id,
    adset_id: query.adset_id,
    ad_id: query.ad_id,
    fbp: getCookie('_fbp'),
    fbc: getCookie('_fbc'),
    af_site_id: '1916',
    af_sub_site_id: '0_0_0',
    dpsource: 'W2A',
    af_sub5: window.land.pixel_id,
  })
  const deepLink = 'dramawave://dramawave.app?redirect=' + encodeURIComponent('/detail?' + appQs)
  const qs = createQueryString({
    is_retargeting: query.is_retargeting || true,
    af_reengagement_window: '23h',
    af_inactivity_window: '7d',
    af_click_lookback: '7d',
    af_force_deeplink: true,
    pid: 'wave_ads',
    c: query.c,
    af_c_id: query.af_c_id,
    af_adset: query.af_adset,
    af_adset_id: query.af_adset_id,
    af_ad: query.af_ad,
    af_ad_id: query.af_ad_id,
    af_channel: query.af_channel,
    af_siteid: '1916',
    af_sub_siteid: '0_0_0',
    af_ip: window.land.ip,
    af_ua: window.navigator.userAgent,
    af_os_version: os.version,
    af_model: getDeviceType(),
    af_sub1: getCookie('_fbp'),
    af_sub2: getCookie('_fbc'),
    af_sub3: window.land.ip,
    af_sub4: window.navigator.userAgent,
    af_sub5: window.land.pixel_id,
    af_dp: deepLink,
    deep_link_value: deepLink,
  })
  const oneLink = 'https://dramawave.onelink.me/gbfM?' + qs
  const universalLink = 'https://dramawave.onelink.me/gbfM?' + qs
  const googlePlayLink = 'market://details?id=com.dramawave.app'
  return { deepLink, universalLink, googlePlayLink, oneLink }
}

function getOSInfo() {
  const ua = navigator.userAgent;
  let osName = 'unknown';
  let osVersion = 'unknown';

  // Windows
  if (ua.indexOf('Windows NT 10.0') !== -1) {
    osName = 'Windows';
    osVersion = '10';
  } else if (ua.indexOf('Windows NT 6.3') !== -1) {
    osName = 'Windows';
    osVersion = '8.1';
  } else if (ua.indexOf('Windows NT 6.2') !== -1) {
    osName = 'Windows';
    osVersion = '8';
  } else if (ua.indexOf('Windows NT 6.1') !== -1) {
    osName = 'Windows';
    osVersion = '7';
  } else if (ua.indexOf('Windows NT 6.0') !== -1) {
    osName = 'Windows';
    osVersion = 'Vista';
  } else if (ua.indexOf('Windows NT 5.1') !== -1 || ua.indexOf('Windows XP') !== -1) {
    osName = 'Windows';
    osVersion = 'XP';
  }
  // Android
  else if (ua.indexOf('Android') !== -1) {
    osName = 'Android';
    const match = ua.match(/Android (\\d+(\\.\\d+)*)/);
    if (match && match[1]) {
      osVersion = match[1];
    }
  }
  // iOS
  else if (ua.indexOf('iPhone OS') !== -1 || ua.indexOf('iPad; CPU OS') !== -1) {
    osName = 'iOS';
    const match = ua.match(/(iPhone OS|CPU OS) (\\d+(_\\d+)*)/);
    if (match && match[2]) {
      osVersion = match[2].replace(/_/g, '.');
    }
  }
  // Linux
  else if (ua.indexOf('Linux') !== -1) {
    osName = 'Linux';
    // Linux 版本号较难统一获取
  }
  // Mac OS
  else if (ua.indexOf('Mac OS X') !== -1) {
    osName = 'Mac OS X';
    // 尝试提取版本号，例如 "Mac OS X 10_15_7"
    const match = ua.match(/Mac OS X (\\d+(_\\d+)*)/);
    if (match && match[1]) {
      osVersion = match[1].replace(/_/g, '.');
    }
  }

  return { name: osName, version: osVersion };
}
function getDeviceType() {
  const ua = navigator.userAgent;
  if (/iphone/i.test(ua)) {
    return 'iphone';
  } else if (/ipad/i.test(ua)) {
    return 'ipad';
  } else if (/ipod/i.test(ua)) {
    return 'ipod';
  } else if (/android/i.test(ua)) {
    // 对于安卓，可以尝试提取一些信息，但通常不是具体的市面型号
    // 例如，ua 可能包含 "SM-A525F Build/RP1A.200720.012"
    // 你可以尝试从中解析，但很复杂且不可靠
    return 'android';
  } else if (/windows phone/i.test(ua)) {
    return 'windowsphone';
  } else if (/macintosh|mac os x/i.test(ua) && navigator.maxTouchPoints > 0) {
    // 区分 macOS 和 iPadOS (iPad Pro 新版 ua 可能更像 macOS)
    // 如果是触屏 Mac，可能需要更复杂的逻辑。一般桌面 Mac maxTouchPoints 是 0。
    // 如果 iPadOS 的 userAgent 设置为 "Desktop site" 模式，它会伪装成 macOS。
    // 因此，检测 iPad 时需要更小心。
    return 'mac'; // 或保持为 Mac，取决于你的需求
  }
  return 'unknown';
}
function getCookie(name) {
  const value = '; ' + document.cookie;
  const parts = value.split('; ' + name + '=');
  if (parts.length === 2) return parts.pop().split(';').shift();
}
