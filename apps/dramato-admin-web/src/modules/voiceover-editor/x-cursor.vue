<template>
  <v-group :config="{ draggable: true, dragBoundFunc: boundFunc }" @dragmove="handleDragMove">
    <v-line :config="{
      points: [position, 0, position, height],
      stroke: '#3B82F6',
      strokeWidth: 2
    }" />
    <v-rect :config="{
      x: position - 8,
      y: 0,
      width: 16,
      height: 16,
      fill: '#3B82F6',
      cornerRadius: 2
    }" />
  </v-group>
</template>

<script lang="tsx" setup>
import { Vector2d } from 'konva/lib/types';

const props = defineProps<{
  position: number;
  height: number;
}>();

const emit = defineEmits<{
  (e: 'update:position', position: number): void;
}>();

// 限制光标只能在水平方向移动
const boundFunc = (pos: Vector2d) => {
  return {
    x: pos.x,
    y: 0
  };
};

// 处理拖动事件
const handleDragMove = (e: any) => {
  const newPos = e.target.x();
  emit('update:position', newPos);
};
</script>
