import { createComponent, getQueries } from '@skynet/shared'
import { coins, defaultAvatar, inviteAvatar, share } from './images/images'
import { T } from './activity-payment.i18n'
import { useActivityPayment } from './use-activity-payment'
import { SvgIcon } from '@skynet/ui'
import { computed, onMounted } from 'vue'
import CommonButton from './common-button'
import { showToast } from 'src/modules/common/toast/toast'
import { replaceHelper } from './replace-helper'
import { useClipboard } from '@vueuse/core'
import { apiReceiveBonus, apiRechargeShare } from './activity-payment-api'
import { h5Track } from 'src/lib/h5-track'
import { useRouter } from 'vue-router'

type CommonShareOptions = {
  props: {
    title?: string
    showTips?: boolean
    showButton?: boolean
    buttonText?: string
    selfSuccess?: boolean
    friendSuccess?: boolean
    inviteUserAvatar?: string
    buttonDisabled?: boolean
  }
}
export const CommonShare = createComponent<CommonShareOptions>({
  props: {
    title: '',
    showTips: false,
    showButton: false,
    buttonText: '',
    selfSuccess: false,
    friendSuccess: false,
    inviteUserAvatar: '',
    buttonDisabled: false,
  },
}, props => {
  const router = useRouter()
  const { user, bonus, status } = useActivityPayment()

  const { uid, currency, from } = getQueries({
    uid: '',
    currency: '',
    from: '',
  })

  const activityRuleList = computed(() => {
    return T.paymentPage.activityRules.ref.value.split('\n')
  })

  onMounted(() => {
    if (props.friendSuccess) {
      h5Track('H5_webrecharge', 'earn_rewards', 'show', {
        user_id: uid,
        from,
      })
    }
  })

  const handleShare = async () => {
    if (props.friendSuccess) {
      const res = await apiReceiveBonus(Number(uid), bonus.value)
      h5Track('H5_webrecharge', 'earn_rewards', 'click', {
        user_id: uid,
        rewards: bonus.value,
        currency,
      })
      if (res.code === 200) {
        showToast(
          <div class="min-w-30 flex flex-col items-center justify-center gap-y-3 rounded-xl bg-white px-5 py-4">
            <img src={coins} class="size-10 object-cover" />
            <p class="flex items-center text-base font-bold text-[#434546]">+{res.data?.bonus}</p>
          </div>,
          'success', // type
          { class: 'rounded-xl !p-0 !bg-white' }, // 可选的其他属性
        )
        status.value = 3
      }
    } else {
      const { copy, copied } = useClipboard()
      const urlParams = new URLSearchParams()
      urlParams.set('share_id', uid ?? '')
      urlParams.set('language', router.currentRoute.value.query.language as string ?? 'en')
      urlParams.set('from', from ?? '')
      await copy(location.origin + '/activity/payment?' + urlParams.toString())
      if (copied.value) {
        showToast(
          <p class="text-center text-sm text-[#434546]">{T.paymentPage.copied()}</p>,
          'success', // type
          { class: 'rounded-xl !px-4 !py-3 !bg-white' }, // 可选的其他属性
        )
      }
      if (!uid) return
      const res = await apiRechargeShare(Number(uid))
      if (res.code === 200) {
        bonus.value = res.data?.bonus ?? 0
        h5Track('H5_webrecharge', 'share', 'click', {
          share_user_id: uid,
          rewards: bonus.value,
        })
      }
    }
  }

  return () => (
    <x-common-share class="relative mt-5 block rounded-[20px] bg-[#F71650] pt-4 shadow-[0px_-1px_0px_0px_rgba(255,255,255,0.20)]">
      <x-shadow class="absolute inset-0 z-up size-full rounded-[20px] shadow-[0px_4px_8px_0px_rgba(207,0,37,0.50)]" />
      <x-share-title class="relative z-up-up flex items-start gap-x-1 px-3 text-lg font-normal capitalize leading-snug text-white">
        <img src={share} class="mt-1 block !size-[20px]" />
        <p innerHTML={props.title} />
      </x-share-title>
      <x-share-content class="relative z-up-up mt-3 block rounded-[20px] bg-white px-3 pb-3 pt-6 text-xs text-[#434546]">
        {props.showTips && <x-tips class="-mt-3 mb-6 block rounded-xl bg-[#FFF5E1] p-3 text-xs" innerHTML={replaceHelper(T.paymentPage.shareTips(), '#F71650', true)} />}
        <x-avators class="px-42px flex w-full items-center justify-around">
          <x-avator-self class="block shrink-0 grow-0">
            <img src={user.value?.user_avatar || defaultAvatar} class="size-16 rounded-full" />
          </x-avator-self>
          <SvgIcon name="ic-arrow" class="!size-5 shrink-0" />
          <x-avator-other class="relative block shrink-0 grow-0">
            <img src={props.inviteUserAvatar || inviteAvatar} class={props.inviteUserAvatar ? 'size-16 rounded-full' : 'size-16'} />
            {props.friendSuccess && <SvgIcon name="ic-check-circle-fill" class="absolute bottom-0 right-0 size-5" />}
          </x-avator-other>
        </x-avators>
        {!props.selfSuccess && !props.friendSuccess && (
          <x-rules class="mt-6 flex flex-col gap-y-2 rounded-xl bg-[#FDFBFC] p-3">
            <p>{activityRuleList.value?.[0]}</p>
            <p>{activityRuleList.value?.[1]}</p>
            <p>{activityRuleList.value?.[2]}</p>
            <p>{activityRuleList.value?.[3]}</p>
          </x-rules>
        )}
        <x-wrapper class="relative">
          {props.showButton && <CommonButton disabled={props.buttonDisabled || status.value === 3} text={props.buttonText} class="!mb-0 !mt-7" onClick={handleShare} />}
          {props.showButton && !props.buttonDisabled && status.value !== 3 && props.friendSuccess && <x-bonus class="right-25px absolute top-1 z-up-up inline-flex items-center justify-center gap-2.5 rounded-t-[20px] rounded-bl-sm rounded-br-[20px] bg-[#FEE54C] px-1.5 py-[3px] text-[10px] font-bold leading-3 text-[#FF2559]" innerHTML={replaceHelper(T.paymentPage.xCoins(), '#FF2559', true).replace(/xxx/i, bonus.value.toString())} />}
        </x-wrapper>
      </x-share-content>
    </x-common-share>
  )
})

export default CommonShare
