import { httpClient } from 'src/lib/http-client'

export const apiGetUserAssetOps = (data: M.UserAssetRecord.Params) =>
  httpClient.get<ApiResponse<M.UserAssetRecord.Response>>('/customer_service_center/asset/ops', data)
export const apiGetUserOpsTransfer = (data: { id: number }) =>
  httpClient.get<ApiResponse<M.UserAssetRecord.OpsTransferDetail>>('/customer_service_center/asset/ops/transfer', data)
export const apiGetUserOpsCompensation = (data: { id: number }) =>
  httpClient.get<ApiResponse<M.UserAssetRecord.OpsCompensationDetail>>('/customer_service_center/asset/ops/compensation', data)
