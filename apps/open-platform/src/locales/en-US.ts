export default {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
  },
  openCollaborationPlatform: 'Open Collaboration Platform',
  passwordLogin: 'Password',
  verificationCodeLogin: 'Verification Code',
  enterPhoneNumber: 'Please enter your phone number',
  enterPassword: 'Please enter your password',
  login: 'Log in',
  registerNow: 'Register now',
  enterVerificationCode: 'Please enter the verification code',
  sendVerificationCode: 'Send',
  workbench: 'Workbench',
  welcomeMessage: 'Welcome to the DramaWave',
  goodMorning: 'Good morning',
  goodAfternoon: 'Good afternoon.',
  goodEvening: 'Good evening',
  scriptSubmission: 'Scripts',
  uploadScript: 'Upload script',
  publishDrama: 'Publish drama',
  releaseNow: 'Release',
  workManagement: 'My Drama',
  shortPlayScript: 'Drama scripts',
  shortPlay: 'Dramas',
  myShortPlayScript: 'My scripts',
  uploadNewScript: 'Upload a script',
  titleOfThePlay: 'Title',
  enterTitle: 'Please enter the title',
  label: 'Tag',
  selectTags: 'Please select tags',
  estimatedEpisodes: 'Episodes',
  enterEstimatedEpisodes: 'Please enter an estimated number of episodes',
  introduction: 'Introduction',
  enterScriptSummary: 'Please enter a script summary of 50 to 500 words',
  cancel: 'Cancel',
  save: 'Save',
  view: 'View',
  modify: 'Modify',
  submitForReview: 'Submit',
  scriptPreparation: 'Preparation',
  preliminaryReview: 'Preliminary review',
  scriptSigning: 'Signing',
  secondRoundReview: '2nd-round review',
  secondPayment: 'Second payment',
  finalReview: 'Final review',
  finished: 'Finished',
  projectApproval: 'Set up',
  reviewFeedback: 'Review feedback',
  myShortPlay: 'My dramas',
  all: 'All',
  draft: 'Draft',
  pendingReview: 'Pending review',
  reviewNotPassed: 'Review does not pass',
  releasing: 'Releasing',
  published: 'Published',
  timeNearToFar: 'Time from near to far',
  timeFarToNear: 'Time from far to near',
  upload: 'Upload',
  videoType: 'Video type',
  enterShortPlaySummary: 'Please enter a brief introduction of 50 to 500 words or less',
  videoWithSubtitles: 'Video with subtitles',
  videoWithoutSubtitles: 'Video without subtitles',
  totalEpisodes: 'Total',
  unlockEpisodes: 'Unlock',
  vocalLanguage: 'Vocal',
  selectVocalLanguage: 'Please select the vocal language',
  subtitleLanguage: 'Subtitle',
  selectSubtitleLanguage: 'Please select the subtitle language',
  authorizationTime: 'Authorization',
  selectAuthorizationStartTime: 'Please select the authorization start time',
  to: 'To',
  selectAuthorizationEndTime: 'Select authorization end time',
  nextStep: 'Next step',
  releaseNewDramaBasicInfo: 'Release New Drama - Basic Information',
  releaseNewDramaUploadVideoSubtitles: 'Release New Drama - Upload Video & Subtitles',
  clickUploadOrDragVideo: 'Click to upload or drag the video here',
  videoNamingRecommendation: 'It is recommended to name the files as 1, 2, 3.../ep01, EP02 for easy automatic recognition of the number of episodes and sorting; the video supports MP4/MKV/AVI/MOV format, and supports three resolutions: 1080x1920, 1216x2160, and 2160x3840',
  uploadVideo: 'Upload',
  clickUploadOrDragSubtitle: 'Click to upload or drag the subtitle file here',
  subtitleNamingRecommendation: 'It is recommended to name the file with 1, 2, 3.../ep01, EP02 to facilitate automatic recognition of the number of episodes and sorting; subtitle files support srt format',
  dramaSubmitForReview: 'Submit',
  dataCenter: 'Data center',
  profitAnalysis: 'Profit analysis',
  underConstruction: 'Under construction... stay tuned~',

  verificationCodeSentToPhone: 'The verification code has been sent to the mobile phone, please pay attention to check',
  retryInNSeconds: 'Retry in {n} seconds', // TODO
  failedToGetVerificationCode: 'Failed to get verification code',
  alreadyHaveAnAccount: 'Already have an account?',
  logInNow: 'Log in now',
  pleaseSetPassword: 'Please set a password of 8 to 20 digits',
  registrationRepresentsAgreement: 'Registration represents agreement',
  userAgreement: 'User Agreement',
  privacyPolicy: 'Privacy Policy',
  completeCertificationBeforeSubmission: 'Please complete the certification before submitting the script/short play. Thank you for your understanding!',
  tip: 'Tip',
  toCertify: 'To certify',
  nothingCreatedYet: 'Nothing created yet',
  toCreate: 'To create',
  pleaseCheckFormContent: 'Please check the form content',
  viewTheScript: 'View the script',
  allStates: 'All states',
  episodes: 'Episodes',
  toUpload: 'To upload',
  previous: 'Previous',
  titleCannotBeEmpty: 'Title cannot be empty',
  titleCannotExceed15Characters: 'The title cannot exceed 15 characters',
  profileCannotBeEmpty: 'Profile cannot be empty',
  introductionShouldBeBetween50And500Words: 'The introduction should be between 50-500 words',
  videoTypeCannotBeEmpty: 'Video type cannot be empty',
  totalNumberOfEpisodesCannotBeEmpty: 'Total number of episodes cannot be empty',
  unlockEpisodesCannotBeEmpty: 'Unlock episodes cannot be empty',
  vocalLanguageCannotBeEmpty: 'Vocal language cannot be empty',
  subtitleLanguageCannotBeEmpty: 'Subtitle language cannot be empty',
  authorizationStartTimeCannotBeEmpty: 'Authorization start time cannot be empty',
  authorizationEndTimeCannotBeEmpty: 'The authorization end time cannot be empty',
  deleteSuccessfully: 'Delete successfully',
  arraignmentSuccess: 'Success',
  uploading: 'Uploading',
  uploadSuccess: 'Success',
  uploadFailed: 'Upload failed',
  operationSuccessful: 'Success',
  operationFailed: 'Failed',
  pleaseEnterTheName: 'Please enter the name',
  titleCanOnlyBeUpTo50Words: 'The title can be up to 50 words',
  pleaseSelectAWorkTag: 'Please select a work tag',
  pleaseEnterAnEstimatedNumberOfEpisodes: 'Please enter an estimated number of episodes',
  upTo1000Episodes: 'Up to 1000 episodes',
  scriptSummaryCanOnlyBeUpTo500Words: 'The script summary can be up to 500 words',
  pleaseUploadTheScript: 'Please upload the script',
  worksLabel: 'tag',

  loginBy: 'Login by',
  profile: 'Profile',
  logOut: 'Log out',
  messages: 'Messages',
  avatar: 'Avatar',
  nickname: 'Nickname',
  contacts: 'Contacts',
  modifyInformation: 'Modify',
  authentication: 'Authentication',
  uncertified: 'Uncertified',
  certified: 'Certified',
  type: 'Type',
  enterprise: 'Enterprise',
  personal: 'Personal',
  company: 'Company',
  pleaseEnterCompanyName: 'Please enter the company name',
  ein: 'EIN',
  pleaseEnterEinCode: 'Please enter the EIN code',
  businessLicensePhoto: 'Business license',
  name: 'Name',
  pleaseEnterYourName: 'Please enter your name',
  reasonForApplication: 'Reason for application',
  pleaseEnterRepresentativeWorksAndRecommenders: 'Please enter information such as representative works and recommenders',
  uploadVideos: 'Upload video',
  uploadSubtitles: 'Upload subtitles',
  numberOfEpisodes: 'Number of episodes',
  video: 'Video',
  subtitleFile: 'Subtitle file',
  operation: 'Operation',
  delete: 'Delete',
  submit: 'Submit',
  theScriptPassedTheFirstTrial: 'The script passed the first trial',
  congratulationsYourScriptHasPassedTheFirstTrial: 'Congratulations, your script has passed the first trial',
  theScript2ndRoundModerationPassed: 'The script "2nd-round Moderation" passed',
  congratulationsYourScript2DRoundModerationHasPassed: 'Congratulations, your script "2D-round Moderation" has passed',
  theScript3rdRoundModerationPassed: 'The script "3Rd-Round Moderation" passed',
  congratulationsYourScript3RdRoundModerationHasPassed: 'Congratulations, your script "3Rd-Round Moderation" has passed',
  theScriptDidNotPassTheFirstTrial: 'The script did not pass the first trial',
  sorryYourScriptHasNotPassedTheInitialReviewStage: 'I\'m sorry, your script has not passed the initial review stage~ If you have any questions, you <NAME_EMAIL>',
  theScript2ndRoundModerationDidNotPass: 'The script "2nd-round Moderation" did not pass',
  sorryYourScriptHasNotYetPassedThe2ndRoundModerationStage: 'Sorry, your script has not yet passed the 2nd-round Moderation stage~ If you have any questions, <NAME_EMAIL>',
  theScript3rdRoundModerationDidNotPass: 'The script "3Rd-Round Moderation" did not pass',
  sorryYourScriptHasNotYetPassedThe3RdRoundModerationStage: 'Sorry, your script has not yet passed the 3Rd-Round Moderation stage~ If you have any questions, <NAME_EMAIL>',
  accountReviewResults: 'Account review results',
  Audience: 'Audience',
  Type: 'Type',
  Plot: 'Plot',
  Role: 'Role',
}
