declare namespace M {
  namespace UserCompensationGrant {
    interface Params {
      user_id?: number
      compensation_type?: string // Compensation, Watch AD
      compensation_amount?: number
      compensation_reason?: string
      remark?: string
    }

    interface Response {
      success: number
      out_account_info: {
        user_id: number
        country_code: string
        last_active_dt: number
        platform: string
      }
      in_account_info: {
        user_id: number
        country_code: string
        last_active_dt: number
        platform: string
      }
      transfered_asset: {
        subscription: 1
        subscription_expire_time: number
        coins: number
        bonus: number
        rewards: number
        coupon: number
        unlocked_episode: number
      }
    }
  }
}
