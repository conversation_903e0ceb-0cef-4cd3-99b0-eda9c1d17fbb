{"name": "free-web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host --mode development --port 5175 --no-clearScreen", "build": "vite build --mode production", "build:test": "vite build --mode staging", "build:prod": "vite build --mode production", "preview": "vite preview --host --base=./", "deploy:test": "bin/deploy_from_local.sh test", "deploy:prod": "bin/deploy_from_local.sh prod", "jenkins:test": "bin/jenkins.sh test", "jenkins:prod": "bin/jenkins.sh prod", "//webp": "pnpm run webp /Users/<USER>/Downloads/1 会将1目录下的图片转换为webp格式（非递归）", "webp": "bin/webp.mjs", "convert": "node bin/convert-md.mjs"}, "dependencies": {"@better-scroll/core": "^2.5.1", "@skynet/client-track": "workspace:*", "@skynet/preset": "workspace:*", "@skynet/shared": "workspace:*", "@skynet/ui": "workspace:*", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "axios": "catalog:", "crypto-js": "^4.2.0", "dayjs": "catalog:", "github-markdown-css": "5.6.1", "imagemin": "^9.0.0", "imagemin-webp": "^8.0.0", "js-cookie": "catalog:", "js-md5": "catalog:", "lottie-web": "^5.12.2", "md-editor-v3": "^5.0.1", "vite-bundle-analyzer": "catalog:", "vue": "catalog:", "vue-i18n": "^10.0.1", "vue-router": "catalog:"}, "devDependencies": {"@skynet/vite-plugin-svg-icons": "workspace:*", "@types/ali-oss": "catalog:", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "catalog:", "@unocss/postcss": "0.61.3", "@unocss/preset-wind": "0.61.3", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "markdown-it": "catalog:", "postcss": "catalog:", "postcss-import": "catalog:", "postcss-nested": "catalog:", "typescript": "catalog:", "unocss": "catalog:", "vconsole": "catalog:", "vite": "catalog:", "vite-plugin-markdown": "2.2.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "catalog:"}, "browserslist": ["defaults"]}