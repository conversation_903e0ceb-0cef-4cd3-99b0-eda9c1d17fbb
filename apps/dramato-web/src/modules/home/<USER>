import { createComponent } from '@skynet/shared'
import { AppName, appName } from 'src/lib/constants'
import { dramawave, commonBg, dramabuzz, series, dramawaveLogo, dramabuzzLogo } from './image/image'
import { ref } from 'vue'
import { Button, Input } from '@skynet/ui'

export const AccountDeletionPage = createComponent(null, () => {
  const account = ref<string>()
  const visible = ref<boolean>(false)
  const error = ref<boolean>(false)

  return () => (
    <div class="size-screen relative select-none overflow-hidden px-[6.5%] pt-[5.55%]">
      {
        error.value
          ? (
              <div class="top-25 absolute left-1/2 z-up flex h-10 -translate-x-1/2 items-center justify-start gap-1 rounded-lg bg-[#434546] px-4 py-3 shadow">
                <div class="font-['SF Pro'] shrink grow basis-0 text-sm font-normal leading-[16.80px] text-[#fdfbfc]">Please enter the correct account ID !</div>
              </div>
            )
          : null
      }
      {visible.value
        ? (
            <>
              <div class="absolute left-0 top-0 z-up size-full bg-[#0b080b] opacity-50" />
              <div class="-translate-1/2 absolute left-1/2 top-1/2 z-up-up flex h-[150px] w-[400px] flex-col items-center justify-start gap-8 rounded-lg bg-[#2e2f30] p-5">
                <p class=" font-['SF Pro'] text-sm font-normal leading-[16.80px] text-[#cccacb]">Your deletion request has been received and your account will be deleted in 14 days !</p>
                <Button onClick={() => {
                  visible.value = false
                  account.value = ''
                }} class="font-['PingFang SC'] inline-flex h-11 w-full cursor-pointer items-center justify-center gap-2.5 rounded-lg border-none bg-[#fc2763] text-base font-medium leading-relaxed text-[#fdfbfc]/90"
                >I know
                </Button>
              </div>
            </>
          )
        : null}
      <img src={commonBg} class="absolute left-0 top-0 size-full object-cover" />
      <img src={series} class="absolute right-[7.8125%] top-0 h-screen w-[40.52%] object-cover" />
      <div class="relative z-up flex size-full flex-col items-start">
        <div class="flex w-full items-center">
          <img src={appName === 'dramawave' ? dramawaveLogo : dramabuzzLogo} class="h-auto w-[3.9%] object-cover" />
          {
            appName === 'dramawave'
              ? <img src={dramawave} class="ml-3 h-auto w-[20.05%] object-cover" />
              : <img src={dramabuzz} class="ml-8 h-auto w-[11.32%] object-cover" />
          }
        </div>
        <div class="text-7 font-['PingFang HK'] absolute left-0 top-[40%] inline-flex h-[35.56%] w-[52ex] -translate-y-1/2 flex-col gap-10 font-normal leading-8 text-white">
          <div class="font-['PingFang HK'] text-3xl font-semibold leading-9">Account Deletion</div>
          <div class="flex items-center">
            Account : <Input inputClass="!bg-transparent text-[#797b7d] text-sm font-normal w-[80%] leading-[16.80px] p-3 rounded border border-solid border-[#434546] outline-none focus:outline-none appearance-none box-border focus:border-solid" class="ml-4 inline-block flex-1" v-model={account.value} placeholder="e-mail address" />
          </div>
          <div>
            <p class="break-words">
              If you delete your account, you'll can't get your data back(Including any property such as coins, bonus, etc).
            </p>
            <p class="mt-2 break-words">Sure you accept all the deletion risk and agree to delete your account?</p>
          </div>
          <Button
            onClick={() => {
              if (account.value && account.value.includes('@')) {
                error.value = false
                visible.value = true
              } else {
                error.value = true
                window.setTimeout(() => {
                  error.value = false
                }, 3000)
              }
            }}
            class="font-['PingFang SC'] inline-flex h-11 w-[263px] shrink-0 cursor-pointer items-center justify-center gap-2.5 rounded-lg border-none bg-[#fc2763] text-[15px] font-medium leading-relaxed text-[#fdfbfc]"
          >Confirm
          </Button>
        </div>
      </div>
      {
        appName === 'dramabuzz'
          ? (
              <div class="absolute bottom-5 text-left text-base font-normal leading-[27px] text-white/80">
                <p>Contact Us：{appName}<EMAIL></p>
                <p>© {AppName}, All Rights Reserved Stardust Online Pte Ltd.</p>
              </div>
            )
          : null
      }
    </div>
  )
})

export default AccountDeletionPage
