/* eslint-disable @typescript-eslint/no-explicit-any */
import { get_k_sso_token } from 'src/lib/device-id'
import { httpClient } from 'src/lib/http-client'

export const apiGetActivityList = (data: M.RiskUsersSearchOption) =>
  httpClient.post<ApiResponse<M.RiskUsersItemResponse>>('/customer_service_center/risk/query', data)

export const apiSaveAudit = (data: M.AuditItem) =>
  httpClient.post<ApiResponse>('/customer_service_center/risk/release', data)
